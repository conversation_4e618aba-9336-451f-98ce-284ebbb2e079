{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?0fab", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?a4c1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?bdc4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?2f1d", "uni-app:///pagesExt/tuozhanyuan/tuozhancrm.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?a93d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhancrm.vue?6838"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "datalistlevel", "yihuo", "xsyj", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "today_sunshi", "total_sunshi", "total_jiediancount", "loading", "isload", "menuindex", "pre_url", "hiddenmodalput", "userinfo", "datalistxing", "datalistxing2", "datalistxing3", "jiuxing_status", "l<PERSON><PERSON><PERSON><PERSON>", "xsyj_shi", "lunshu", "yongjin", "count", "count1", "count2", "count3", "count4", "comwithdraw", "zong_y<PERSON><PERSON><PERSON>", "today_yunshouyi", "yunkucun_status", "canwithdraw", "money", "count0", "countdqr", "commission2money", "showfenhong", "showMendianOrder", "has<PERSON><PERSON>", "hasareafenhong", "has<PERSON><PERSON>fenhong", "showYeji", "fxjiesuantime", "teamyeji_show", "teamnum_show", "gongxianfenhong_show", "set", "xunizhanhaocount", "xunizhanhao", "levelArr", "yihuo_deduct_gongxianzhi", "yihuo_gongxianzhi_rate", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "setTimeout", "cancel", "tomoney", "toexchange", "tomonenyconfirm", "console", "executeTransfer", "done"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkJnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;IAEA;EACA;;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAE;UACAC;QACA;QACAH;QACAA;QACA,iCACA;UACAC;UACAG;YACAH;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACAA;QACAA;QAEAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAN;IACA;IACAO;MACAC;MACA;MACA;MACA;QACAR;QACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QAEAA;UACA;UACAD;QACA;UACA;UACA;QACA;MACA;QACA;QACAA;MACA;IACA;IAEAU;MACA;MACAC;MACAV;MACAA;QAAAzB;MAAA;QACAyB;QACA;UACAA;QACA;UACAD;UACAC;UACAG;YACAJ;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxVA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/tuozhancrm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/tuozhancrm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tuozhancrm.vue?vue&type=template&id=38c91d74&\"\nvar renderjs\nimport script from \"./tuozhancrm.vue?vue&type=script&lang=js&\"\nexport * from \"./tuozhancrm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tuozhancrm.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/tuozhancrm.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhancrm.vue?vue&type=template&id=38c91d74&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload && _vm.set && _vm.set.parent_show == 1 ? _vm.t(\"上级经理\") : null\n  var m3 =\n    _vm.isload && _vm.set && _vm.set.parent_show == 1 ? _vm.t(\"当前身份\") : null\n  var m4 = _vm.isload ? _vm.t(\"拓展费\") : null\n  var m5 = _vm.isload ? _vm.t(\"余额\") : null\n  var m6 = _vm.isload ? _vm.t(\"拓展费\") : null\n  var m7 = _vm.isload && _vm.userinfo.cityname != 0 ? _vm.t(\"代理数据\") : null\n  var m8 = _vm.isload ? _vm.t(\"拓展费\") : null\n  var m9 = _vm.isload ? _vm.t(\"余额\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhancrm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhancrm.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"banner\" :style=\"{background:'linear-gradient(180deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0) 100%)'}\">\r\n\t\t\t<image :src=\"userinfo.headimg\" background-size=\"cover\"/>\r\n\t\t\t<view class=\"info\" v-if=\"set && set.parent_show == 1\">\r\n\t\t\t\t <text class=\"nickname\">{{userinfo.nickname}}（拓展ID：{{userinfo.id}})</text>\r\n\t\t\t\t<!-- <text class=\"nickname\"  v-if=\"jiuxing_status == 1\">{{t('我的星级')}}：{{userinfo.xingji}}星</text> -->\r\n\t\t\t\t <text>{{t('上级经理')}}：{{userinfo.pid > 0 ? userinfo.pnickname : '无'}}</text>\r\n\t\t\t\t  <text>{{t('当前身份')}}：{{userinfo.shenfen}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\" v-else style=\"line-height: 120rpx;padding-top: 0;\">\r\n\t\t\t\t <text class=\"nickname\">{{userinfo.nickname}}</text>\r\n\t\t\t\t <!--  -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"contentdata\">\r\n\t\t\t<view class=\"order\">\r\n\t\t\t\t\r\n\t\t\t\r\n\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t<text class=\"f1\">我的{{t('拓展费')}}</text>\r\n\t\t\t\t\t<!-- <view class=\"f2\" @tap=\"goto\" data-url=\"withdrawtuozhan\" v-if=\"comwithdraw==1\"><text>立即提现</text><image src=\"/static/img/arrowright.png\"></image></view> -->\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"tomoney\">\r\n\t\t\t\t\t\t<text>转到{{t('余额')}}账户</text>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.tuozhanfei}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">{{comwithdraw==1?'可提现':'剩余'}}{{t('拓展费')}}</text>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{count3}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">已提现{{t('拓展费')}}</text>\r\n\t\t\t\t\t </view> -->\r\n\t\t\t\t\t <view class=\"item\">\r\n\t\t\t\t\t \t<text class=\"t1\">￥{{yihuo.tixiantuozhan}}</text>\r\n\t\t\t\t\t \t<text class=\"t3\">已提现拓展费</text>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.tuozhanfei_zls}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">在路上</text>\r\n\t\t\t\t\t </view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\t<!-- <view class=\"order\" v-if=\"hasteamfenhong && (teamnum_show==1 || teamyeji_show==1)\">\r\n\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t<text class=\"f1\">{{t('我的团队')}}</text>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"tuozhanteam\"><text>查看详情</text><image src=\"/static/img/arrowright.png\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t <view class=\"item\" v-if=\"teamnum_show==1\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{userinfo.teamnum}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">市场总用户数</text>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t\t <view class=\"item\">\r\n\t\t\t\t\t \t\t<block v-if=\"teamyeji_show==1\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.teamyeji}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">市场总业绩</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t\t <view class=\"item\">\r\n\t\t\t\t\t\t\t<block v-if=\"gongxianfenhong_show==1\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.gongxianfenhong}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\">预计{{userinfo.gongxianfenhong_txt || '股东贡献量分红'}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\r\n\t\t\t\r\n\t\t\r\n\t\t\t<view class=\"order\" v-if=\"userinfo.cityname != 0\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t\t<text class=\"f1\">{{t('代理数据')}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">{{userinfo.cityname}}</text>\r\n\t\t\t\t\t<text class=\"t3\">代理城市</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{userinfo.businesscount}}</text>\r\n\t\t\t\t\t\t<text class=\"t3\">代理城市商户数量</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.cityliushui}}</text>\r\n\t\t\t\t\t\t<text class=\"t3\">代理城市流水</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t\r\n\t\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"mylist\">\r\n\t\t\t\t\t<view class=\"f2\">商家列表</view>\r\n\t\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t -->\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"shangjipull\" >\r\n\t\t\t\t\t<view class=\"f2\">录入商机</view>\r\n\t\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"shangjilist\" >\r\n\t\t\t\t\t<view class=\"f2\">商机列表</view>\r\n\t\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"shagjigenjinjilu\" >\r\n\t\t\t\t\t<view class=\"f2\">跟进记录</view>\r\n\t\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:20rpx\"></view>\r\n\t\t\r\n\t\t<uni-popup id=\"dialogInput\" ref=\"dialogInput\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" :title=\"t('拓展费') + '转' + t('余额')\" value=\"\" placeholder=\"请输入转入金额\" @confirm=\"tomonenyconfirm\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tdatalistlevel:{},\r\n\t\t\tyihuo:{},\r\n\t\t\txsyj:{},\r\n\t\t\tpiaodianstatus:0,\r\n\t\t\tpiaodian:{},\r\n\t\t\ttoday_sunshi:0,\r\n\t\t\ttotal_sunshi:0,\r\n\t\t\ttotal_jiediancount:0,\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n      hiddenmodalput: true,\r\n      userinfo: [],\r\n\t  datalistxing:[],\r\n\t  datalistxing2:[],\r\n\t  datalistxing3:[],\r\n\t  jiuxing_status:0,\r\n\t  lunshustatus:0,\r\n\t  xsyj_shi:0,\r\n\t  lunshu:0,\r\n\t  yongjin:0,\r\n      count: 0,\r\n      count1: 0,\r\n      count2: 0,\r\n      count3: 0,\r\n      count4: 0,\r\n      comwithdraw: 0,\r\n\t  zong_yunshouyi:0,\r\n\t  today_yunshouyi:0,\r\n\t  yunkucun_status:0,\r\n      canwithdraw: true,\r\n      money: 0,\r\n      count0: \"\",\r\n      countdqr: \"\",\r\n      commission2money: \"\",\r\n\t\t\tshowfenhong:false,\r\n\t\t\tshowMendianOrder:false,\r\n\t\t\thasfenhong:false,\r\n\t\t\thasareafenhong:false,\r\n\t\t\thasteamfenhong:false,\r\n\t\t\tshowYeji:false,\r\n\t\t\tfxjiesuantime:0,\r\n\t\t\tteamyeji_show:0,\r\n\t\t\tteamnum_show:0,\r\n\t\t\tgongxianfenhong_show:0,\r\n\t\t\tset:{},\r\n\t\t\txunizhanhaocount:0,\r\n\t\t\txunizhanhao:{},\r\n\t\t\tlevelArr:{},\r\n\r\n\t\t\t// 贡献值扣除配置\r\n\t\t\tyihuo_deduct_gongxianzhi: 0,    // 是否扣除贡献值\r\n\t\t\tyihuo_gongxianzhi_rate: 100,    // 贡献值扣除比例\r\n\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tvar that = this;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAgent/commissionSurvey', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '我的' + that.t('拓展费')\r\n\t\t\t\t});\r\n\t\t\t\tthat.yihuo = res.yihuo;\r\n\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\tif(that.userinfo.shenfen=='')\r\n\t\t\t\t{\r\n\t\t\t\t\tapp.success('请联系管理员开通!');\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t  app.goto('/pages/my/usercenter');\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t}\r\n\t\t\t\tthat.datalistxing =res.xingjiArr;\r\n\t\t\t\tthat.datalistxing2 =res.xingjiArr2;\r\n\t\t\t\tthat.datalistxing3 =res.xingjiArr3;\r\n\t\t\t\tthat.datalistlevel = res.datalistlevel;\r\n\t\t\t\tthat.xsyj = res.xsyj;\r\n\t\t\t\tthat.piaodianstatus = res.piaodianstatus;\r\n\t\t\t\tthat.piaodian = res.piaodian;\r\n\t\t\t\tthat.yongjin = res.yongjin;\r\n\t\t\t\tthat.xunizhanhao = res.xunizhanhao;\r\n\t\t\t\tthat.xunizhanhaocount = res.xunizhanhaocount;\r\n\t\t\t\tthat.jiuxing_status = res.jiuxing_status\r\n\t\t\t\tthat.lunshustatus = res.lunshustatus;\r\n\t\t\t\tthat.xsyj_shi = res.xsyj_shi;\r\n\t\t\t\tthat.lunshu = res.lunshu;\r\n\t\t\t\tthat.today_sunshi = res.today_sunshi;\r\n\t\t\t\tthat.total_sunshi = res.total_sunshi;\r\n\t\t\t\tthat.total_jiediancount = res.total_jiediancount\r\n\t\t\t\tthat.set = res.set;\r\n\t\t\t\tthat.count = res.count;\r\n\t\t\t\tthat.count1 = res.count1;\r\n\t\t\t\tthat.count2 = res.count2;\r\n\t\t\t\tthat.count3 = res.count3;\r\n\t\t\t\tthat.count0 = res.count0;\r\n\t\t\t\tthat.today_yunshouyi = res.today_yunshouyi;\r\n\t\t\t\tthat.zong_yunshouyi = res.zong_yunshouyi;\r\n\t\t\t\tthat.levelArr  = res.levelArr;\r\n\t\t\t\tthat.countdqr = res.countdqr;\r\n\t\t\t\tthat.comwithdraw = res.comwithdraw;\r\n\t\t\t\tthat.yunkucun_status = res.yunkucun_status;\r\n\t\t\t\tthat.commission2money = res.commission2money;\r\n\t\t\t\tthat.showfenhong = res.showfenhong;\r\n\t\t\t\tthat.showMendianOrder = res.showMendianOrder;\r\n\t\t\t\tthat.hasfenhong = res.hasfenhong;\r\n\t\t\t\tthat.hasareafenhong = res.hasareafenhong;\r\n\t\t\t\tthat.hasteamfenhong = res.hasteamfenhong;\r\n\t\t\t\tthat.showYeji = res.hasYeji;\r\n\t\t\t\tthat.fxjiesuantime = res.fxjiesuantime;\r\n\t\t\t\tthat.teamyeji_show = res.teamyeji_show;\r\n\t\t\t\tthat.teamnum_show = res.teamnum_show;\r\n\t\t\t\tthat.gongxianfenhong_show = res.gongxianfenhong_show;\r\n\r\n\t\t\t\t// 获取贡献值扣除配置\r\n\t\t\t\tthat.yihuo_deduct_gongxianzhi = res.yihuo_deduct_gongxianzhi || 0;\r\n\t\t\t\tthat.yihuo_gongxianzhi_rate = res.yihuo_gongxianzhi_rate || 100;\r\n\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    cancel: function () {\r\n      this.hiddenmodalput = true;\r\n    },\r\n    tomoney: function () {\r\n      this.$refs.dialogInput.open()\r\n    },\r\n\ttoexchange:function () {\r\n\t\tapp.goto('/pages/money/zhuanzhang');\r\n    },\r\n    tomonenyconfirm: function (done, val) {\r\n\t\t\tconsole.log(val)\r\n      var that = this;\r\n      var money = val;\r\n      if (money == '' || parseFloat(money) <= 0) {\r\n        app.alert('请输入转入金额');\r\n        return;\r\n      }\r\n      if (parseFloat(money) > this.userinfo.tuozhanfei) {\r\n        app.alert('可转入' + that.t('拓展费') + '不足');\r\n        return;\r\n      }\r\n\r\n      // 检查是否需要扣除贡献值并给出提示\r\n      if (that.yihuo_deduct_gongxianzhi == 1) {\r\n        var gongxianzhi_rate = that.yihuo_gongxianzhi_rate || 100;\r\n        var deduct_gongxianzhi = parseFloat(money) * gongxianzhi_rate / 100;\r\n        var confirm_msg = '转入' + money + '元到' + that.t('余额') + '将扣除' + deduct_gongxianzhi.toFixed(2) + that.t('贡献值') + '，是否继续？';\r\n\r\n        app.confirm(confirm_msg, function() {\r\n          // 用户确认后执行转账\r\n          that.executeTransfer(done, money);\r\n        }, function() {\r\n          // 用户取消，不执行任何操作\r\n          return;\r\n        });\r\n      } else {\r\n        // 不需要扣除贡献值，直接执行转账\r\n        that.executeTransfer(done, money);\r\n      }\r\n    },\r\n\r\n    executeTransfer: function(done, money) {\r\n      var that = this;\r\n\t\t\tdone();\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post('ApiYihuo/tuozhanfeiyue', {money: money}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.error(data.msg);\r\n        } else {\r\n          that.hiddenmodalput = true;\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.banner{ display:flex;width:100%;height:560rpx;padding:40rpx 32rpx;color:#fff;position:relative}\r\n.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}\r\n.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}\r\n.banner .info .nickname{font-size:32rpx;font-weight:bold;padding-bottom:12rpx}\r\n.banner .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}\r\n.banner .set image{width:50rpx;height:50rpx;border-radius:0}\r\n\r\n.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-380rpx;position:relative;margin-bottom:20rpx}\r\n\r\n.order{width:100%;background:#fff;padding:0 20rpx;margin-top:20rpx;border-radius:16rpx}\r\n.order .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}\r\n.order .head .f1{flex:auto;color:#333}\r\n.order .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\r\n.order .head .f2 image{ width:30rpx;height:30rpx;}\r\n.order .head .t3{ width: 40rpx; height: 40rpx;}\r\n.order .content{ display:flex;width:100%;padding:10rpx 0;align-items:center;font-size:24rpx}\r\n.order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\r\n.order .content .item image{ width:50rpx;height:50rpx}\r\n.order .content .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}\r\n.order .content .item .t3{ padding-top:3px;color:#666}\r\n.order .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}\r\n\r\n.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}\r\n.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}\r\n.list .item:last-child{border-bottom:0;}\r\n.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}\r\n.list .f1 image{ width:40rpx;height:40rpx;}\r\n.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\r\n.list .f2{color:#222}\r\n.list .f3{ color: #FC5648;text-align:right;flex:1;}\r\n.list .f4{ width: 24rpx; height: 24rpx;} \r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhancrm.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhancrm.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103467\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}