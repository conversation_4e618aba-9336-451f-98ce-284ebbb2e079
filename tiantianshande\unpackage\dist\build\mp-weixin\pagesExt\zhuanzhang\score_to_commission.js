require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/zhuanzhang/score_to_commission"],{"136f":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,textset:{},canrecharge:0,userinfo:{},config:{status:0,rate:100,beishu:0,min:0,max:0,need_paypwd:!1,fee_rate:0},shuoming:"",score:"",paypwd:"",calculatedCommission:0,calculatedFee:0,caninput:1}},onLoad:function(e){this.opt=n.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;n.loading=!0,n.get("ApiMoney/scoreToCommission",{},(function(o){if(n.loading=!1,0==o.status)return n.error(o.msg||"功能未开启"),void setTimeout((function(){e.navigateBack()}),1500);t.isload=!0,t.textset=n.globalData.textset,e.setNavigationBarTitle({title:"积分转佣金"}),t.config={status:o.status,rate:o.rate||100,beishu:o.beishu||0,min:o.min||0,max:o.max||0,need_paypwd:o.need_paypwd||!1,fee_rate:o.fee_rate||0},t.userinfo=o.userinfo||{},t.shuoming=o.shuoming||"",t.caninput=o.caninput||1,t.config.need_paypwd&&!o.has_paypwd&&n.error("请先设置支付密码"),t.loaded()}))},scoreinput:function(e){var t=e.detail.value;parseFloat(t)<0?n.error("必须大于0"):(this.score=t,this.calculateCommission())},paypwdinput:function(e){var t=e.detail.value;this.paypwd=t.trim()},calculateCommission:function(){var e=parseInt(this.score)||0;if(e<=0)return this.calculatedCommission=0,void(this.calculatedFee=0);var t=e/this.config.rate,n=t*this.config.fee_rate;this.calculatedCommission=t-n,this.calculatedFee=n},convert:function(t){var o=this,a=o.score,i=o.paypwd;if(!a)return n.error("请输入转换积分数量");var r=parseInt(a);if(r<o.config.min)return n.error("转换积分不能少于"+o.config.min);if(o.config.max>0&&r>o.config.max)return n.error("转换积分不能超过"+o.config.max);if(o.config.beishu>0&&r%o.config.beishu!==0)return n.error("转换积分必须是"+o.config.beishu+"的倍数");if(r>parseFloat(o.userinfo.score||0))return n.error("积分余额不足");if(o.config.need_paypwd&&!i)return n.error("请输入支付密码");var c="确认转换".concat(r,"积分为").concat(o.calculatedCommission.toFixed(6),"佣金吗？").concat(o.calculatedFee>0?"\n手续费："+o.calculatedFee.toFixed(6)+"佣金":"");e.showModal({title:"确认转换",content:c,success:function(e){e.confirm&&o.doConvert(r,i)}})},doConvert:function(e,t){var o=this;o.loading=!0;var a={score:e};o.config.need_paypwd&&(a.paypwd=t),n.post("ApiMoney/scoreToCommission",a,(function(e){o.loading=!1,0!=e.status?(n.error("转换成功"),o.score="",o.paypwd="",o.calculatedCommission=0,o.calculatedFee=0,o.getdata()):n.error(e.msg)}))}}};t.default=o}).call(this,n("df3c")["default"])},"2bb3":function(e,t,n){"use strict";n.r(t);var o=n("3365"),a=n("c658");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("5150b");var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=c.exports},3365:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={parse:function(){return Promise.all([n.e("common/vendor"),n.e("components/parse/parse")]).then(n.bind(null,"1f1a"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload?e.t("color1"):null),o=e.isload&&e.config.fee_rate>0?(100*e.config.fee_rate).toFixed(2):null,a=e.isload&&e.score>0?e.calculatedCommission.toFixed(6):null,i=e.isload&&e.score>0&&e.calculatedFee>0?e.calculatedFee.toFixed(6):null,r=e.isload?e.t("color1"):null;e.$mp.data=Object.assign({},{$root:{m0:n,g0:o,g1:a,g2:i,m1:r}})},i=[]},"5150b":function(e,t,n){"use strict";var o=n("9b8b"),a=n.n(o);a.a},"9b8b":function(e,t,n){},c658:function(e,t,n){"use strict";n.r(t);var o=n("136f"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=a.a},ff31:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("06e9");o(n("3240"));var a=o(n("2bb3"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ff31","common/runtime","common/vendor"]]]);