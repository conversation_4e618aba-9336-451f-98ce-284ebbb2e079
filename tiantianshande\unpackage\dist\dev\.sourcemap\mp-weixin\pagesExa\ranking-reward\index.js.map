{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?ccaa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?d9b1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?8523", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?cbe9", "uni-app:///pagesExa/ranking-reward/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?b745", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/index.vue?4403"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "rulesList", "statsData", "issued_reward", "pending_reward", "total_reward", "rules_with_ranking", "recent_records", "loading", "onLoad", "onPullDownRefresh", "uni", "methods", "initData", "callback", "getStatsData", "console", "app", "that", "title", "icon", "getRulesList", "goToRulePreview", "url", "goToRecords", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4E9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAC;MAEA;MACA;MAEAC;QACAC;QAEA;UACAF;UACAE;QACA;UACAF;UACAL;YACAQ;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAL;MAEA;MACA;MAEAC;QACAC;QAEA;UACAF;UACAE;QACA;UACAF;UACAL;YACAQ;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAE;MACAN;MACAL;QACAY;MACA;IACA;IAEA;IACAC;MACAR;MACAL;QACAY;MACA;IACA;IAEA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,42CAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/ranking-reward/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/ranking-reward/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0105d9d0&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/ranking-reward/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0105d9d0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.rulesList.length\n  var g1 =\n    _vm.statsData.recent_records && _vm.statsData.recent_records.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 头部统计 -->\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"stats-title\">累计获得奖励</view>\r\n\t\t\t\t<view class=\"stats-value\">{{statsData.total_reward || '0.00'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-flex\">\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<view class=\"stats-subtitle\">已发放奖励</view>\r\n\t\t\t\t\t<view class=\"stats-subvalue\">{{statsData.issued_reward || '0.00'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<view class=\"stats-subtitle\">待发放奖励</view>\r\n\t\t\t\t\t<view class=\"stats-subvalue\">{{statsData.pending_reward || '0.00'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 奖励规则列表 -->\r\n\t\t<view class=\"rules-container\">\r\n\t\t\t<view class=\"section-title\">奖励规则</view>\r\n\t\t\t<view class=\"no-data\" v-if=\"rulesList.length === 0\">\r\n\t\t\t\t<text>暂无可用奖励规则</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rule-card\" v-for=\"(item, index) in rulesList\" :key=\"index\" @click=\"goToRulePreview(item.id)\">\r\n\t\t\t\t<view class=\"rule-top\">\r\n\t\t\t\t\t<view class=\"rule-name\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"rule-tag\">{{item.reward_mode_name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-info\">\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"rule-label\">排名类型：</text>\r\n\t\t\t\t\t\t<text class=\"rule-value\">{{item.rank_type_name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"rule-label\">奖励比例：</text>\r\n\t\t\t\t\t\t<text class=\"rule-value\">{{item.total_reward_rate}}%</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"rule-label\">奖励排名：</text>\r\n\t\t\t\t\t\t<text class=\"rule-value\">前{{item.reward_top_num}}名</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-button\">查看详情</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 最近奖励记录 -->\r\n\t\t<view class=\"records-container\">\r\n\t\t\t<view class=\"section-title-row\">\r\n\t\t\t\t<view class=\"section-title\">最近奖励记录</view>\r\n\t\t\t\t<view class=\"view-more\" @click=\"goToRecords\">查看更多</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"no-data\" v-if=\"statsData.recent_records && statsData.recent_records.length === 0\">\r\n\t\t\t\t<text>暂无奖励记录</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"record-card\" v-for=\"(item, index) in statsData.recent_records\" :key=\"index\">\r\n\t\t\t\t<view class=\"record-top\">\r\n\t\t\t\t\t<view class=\"record-title\">{{item.rule_name}}</view>\r\n\t\t\t\t\t<view class=\"record-status\" :class=\"{'status-success': item.status === 1, 'status-pending': item.status === 0, 'status-rejected': item.status === 2}\">\r\n\t\t\t\t\t\t{{item.status_text}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"record-info\">\r\n\t\t\t\t\t<view class=\"record-period\">{{item.period_text}}</view>\r\n\t\t\t\t\t<view class=\"record-rank\">排名：第{{item.rank}}名</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"record-amount\">+{{item.reward_amount}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\trulesList: [],\r\n\t\t\t\tstatsData: {\r\n\t\t\t\t\tissued_reward: '0.00',\r\n\t\t\t\t\tpending_reward: '0.00',\r\n\t\t\t\t\ttotal_reward: '0.00',\r\n\t\t\t\t\trules_with_ranking: 0,\r\n\t\t\t\t\trecent_records: []\r\n\t\t\t\t},\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initData();\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.initData(() => {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitData(callback) {\r\n\t\t\t\t// 获取统计数据\r\n\t\t\t\tthis.getStatsData();\r\n\t\t\t\t// 获取规则列表\r\n\t\t\t\tthis.getRulesList();\r\n\t\t\t\t\r\n\t\t\t\tif (callback && typeof callback === 'function') {\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取统计数据\r\n\t\t\tgetStatsData() {\r\n\t\t\t\tconst timestamp = new Date().getTime();\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/index][getStatsData_001] 开始获取排名奖励统计数据`);\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getRankingStats', {}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/index][getStatsData_002] 获取排名奖励统计数据成功`);\r\n\t\t\t\t\t\tthat.statsData = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/index][getStatsData_003] 获取排名奖励统计数据失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取统计数据失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取规则列表\r\n\t\t\tgetRulesList() {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/index][getRulesList_001] 开始获取排名奖励规则列表`);\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getRankingRules', {}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/index][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);\r\n\t\t\t\t\t\tthat.rulesList = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/index][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取规则列表失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到规则预览页\r\n\t\t\tgoToRulePreview(ruleId) {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/index][goToRulePreview_001] 跳转到规则预览页，规则ID：${ruleId}`);\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pagesExa/ranking-reward/preview?rule_id=${ruleId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到记录页\r\n\t\t\tgoToRecords() {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/index][goToRecords_001] 跳转到奖励记录页`);\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pagesExa/ranking-reward/records'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间，用于日志\r\n\t\t\tformatTime() {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\tconst seconds = date.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\tconst milliseconds = date.getMilliseconds().toString().padStart(3, '0');\r\n\t\t\t\t\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n// 统计卡片\r\n.stats-container {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-card {\r\n\ttext-align: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.stats-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-value {\r\n\tfont-size: 48rpx;\r\n\tcolor: #FF6600;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.stats-flex {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\tborder-top: 1rpx solid #EEEEEE;\r\n\tpadding-top: 20rpx;\r\n}\r\n\r\n.stats-item {\r\n\ttext-align: center;\r\n}\r\n\r\n.stats-subtitle {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.stats-subvalue {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n// 规则列表\r\n.rules-container {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-title-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.view-more {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.rule-card {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.rule-top {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.rule-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.rule-tag {\r\n\tfont-size: 24rpx;\r\n\tcolor: #FFFFFF;\r\n\tbackground-color: #FF6600;\r\n\tpadding: 4rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.rule-info {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.rule-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.rule-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\twidth: 180rpx;\r\n}\r\n\r\n.rule-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.rule-button {\r\n\ttext-align: center;\r\n\tcolor: #FF6600;\r\n\tfont-size: 28rpx;\r\n\tborder: 1rpx solid #FF6600;\r\n\tborder-radius: 30rpx;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n// 记录列表\r\n.record-card {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n\tposition: relative;\r\n}\r\n\r\n.record-top {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.record-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.record-status {\r\n\tfont-size: 24rpx;\r\n\tpadding: 4rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.status-pending {\r\n\tcolor: #FF9900;\r\n\tbackground-color: rgba(255, 153, 0, 0.1);\r\n}\r\n\r\n.status-success {\r\n\tcolor: #00CC66;\r\n\tbackground-color: rgba(0, 204, 102, 0.1);\r\n}\r\n\r\n.status-rejected {\r\n\tcolor: #FF3333;\r\n\tbackground-color: rgba(255, 51, 51, 0.1);\r\n}\r\n\r\n.record-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.record-amount {\r\n\tposition: absolute;\r\n\tright: 24rpx;\r\n\tbottom: 24rpx;\r\n\tfont-size: 36rpx;\r\n\tcolor: #FF6600;\r\n\tfont-weight: bold;\r\n}\r\n\r\n// 无数据提示\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 50rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115084518\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}