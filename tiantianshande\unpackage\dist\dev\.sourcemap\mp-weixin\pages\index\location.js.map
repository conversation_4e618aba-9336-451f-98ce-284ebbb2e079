{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?e78e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?4611", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?d2e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?4fc4", "uni-app:///pages/index/location.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?b6df", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/location.vue?e4f9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pre_url", "keyword", "<PERSON><PERSON><PERSON><PERSON>", "locationInfo", "searchResults", "loading", "page", "latitude", "longitude", "hasMore", "isLocating", "searchHistory", "onLoad", "methods", "t", "console", "getCurrentLocation", "app", "uni", "title", "icon", "handleSearch", "clearTimeout", "searchLocation", "loadMore", "clearSearch", "loadSearchHistory", "addToHistory", "name", "address", "clearHistory", "content", "success", "useHistoryItem", "useCurrentLocation", "display_name", "selectLocation", "item"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiIjxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACA;MACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACAC;QACA;QACA;;QAEA;QACAA;UACAV;UACAC;QACA;UACA;YACA;YACA;UACA;UACA;UACA;QACA;MACA;QACAO;QACA;QACA;QACAG;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACAN;QACAhB;QACAM;QACAC;QACAF;MACA;QACA;UACA;YACA;UACA;YACA;UACA;UACA;QACA;;QACA;MACA;IACA;IAEA;IACAkB;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;MACA;QACAX;MACA;IACA;IAEA;IACAY;MACA;MACA;QAAA;MAAA;;MAEA;MACA;QACAC;QACArB;QACAC;QACAqB;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACAX;IACA;IAEA;IACAY;MAAA;MACAZ;QACAC;QACAY;QACAC;UACA;YACA;YACAd;UACA;QACA;MACA;IACA;IAEA;IACAe;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAhB;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACAF,+DACA;QACAX;QACAC;QACA2B;MAAA,GACA;MAEAjB;IACA;IAEA;IACAkB;MACA;MACA;;MAEA;MACAlB,+DACAmB;QACA9B;QACAC;QACA2B;MAAA,GACA;MAEAjB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/location.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/location.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./location.vue?vue&type=template&id=a157a7ea&\"\nvar renderjs\nimport script from \"./location.vue?vue&type=script&lang=js&\"\nexport * from \"./location.vue?vue&type=script&lang=js&\"\nimport style0 from \"./location.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/location.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location.vue?vue&type=template&id=a157a7ea&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  var m2 = _vm.t(\"color1\")\n  var m3 = _vm.t(\"color1rgb\")\n  var g0 = !_vm.keyword && !_vm.searchResults.length\n  var g1 = _vm.searchResults.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"location-page\">\r\n\t\t<!-- 顶部搜索区域 -->\r\n\t\t<view class=\"search-section\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<view class=\"search-input-box\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/search.png'\" mode=\"aspectFit\" class=\"search-icon\"/>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tv-model=\"keyword\" \r\n\t\t\t\t\t\tplaceholder=\"搜索地点、小区、写字楼\" \r\n\t\t\t\t\t\t@input=\"handleSearch\"\r\n\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<view class=\"clear-btn\" v-if=\"keyword\" @tap=\"clearSearch\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" mode=\"aspectFit\" class=\"clear-icon\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 获取当前位置按钮 -->\r\n\t\t\t<view class=\"get-location-btn\" @tap=\"getCurrentLocation\" :class=\"{'loading': isLocating}\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t<image \r\n\t\t\t\t\t:src=\"pre_url+'/static/img/location.png'\" \r\n\t\t\t\t\tmode=\"aspectFit\" \r\n\t\t\t\t\tclass=\"btn-icon\"\r\n\t\t\t\t\t:class=\"{'rotating': isLocating}\"\r\n\t\t\t\t/>\r\n\t\t\t\t<text>{{isLocating ? '正在获取位置...' : '重新定位'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 当前位置卡片 -->\r\n\t\t<view class=\"current-location\" @tap=\"useCurrentLocation\">\r\n\t\t\t<view class=\"location-left\">\r\n\t\t\t\t<view class=\"location-icon-wrap\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/location.png'\" mode=\"aspectFit\" class=\"location-icon\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"location-info\">\r\n\t\t\t\t\t<text class=\"title\">当前位置</text>\r\n\t\t\t\t\t<text class=\"address\">{{currentAddress || '定位中...'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"use-btn\" :class=\"{'disabled': !locationInfo}\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t<text>{{locationInfo ? '使用' : '定位中'}}</text>\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-right.png'\" mode=\"aspectFit\" class=\"arrow-icon\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 搜索历史 -->\r\n\t\t<view class=\"history-section\" v-if=\"!keyword && !searchResults.length\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"title\">搜索历史</text>\r\n\t\t\t\t<view class=\"clear-history\" @tap=\"clearHistory\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/delete.png'\" mode=\"aspectFit\" class=\"delete-icon\"/>\r\n\t\t\t\t\t<text>清除</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"history-list\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"history-item\" \r\n\t\t\t\t\tv-for=\"(item, index) in searchHistory\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@tap=\"useHistoryItem(item)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/time.png'\" mode=\"aspectFit\" class=\"time-icon\"/>\r\n\t\t\t\t\t<text class=\"history-text\">{{item.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 搜索结果列表 -->\r\n\t\t<scroll-view \r\n\t\t\tscroll-y \r\n\t\t\tclass=\"location-list\"\r\n\t\t\t@scrolltolower=\"loadMore\"\r\n\t\t\tv-if=\"searchResults.length > 0\"\r\n\t\t>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"location-item\" \r\n\t\t\t\tv-for=\"(item, index) in searchResults\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@tap=\"selectLocation(item)\"\r\n\t\t\t\thover-class=\"item-hover\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"location-detail\">\r\n\t\t\t\t\t<view class=\"name-wrap\">\r\n\t\t\t\t\t\t<text class=\"name\">{{item.name}}</text>\r\n\t\t\t\t\t\t<text class=\"tag\" v-if=\"item.type\">{{item.type}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"address\">{{item.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right-area\">\r\n\t\t\t\t\t<text class=\"distance\">{{item.distance}}</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-right.png'\" mode=\"aspectFit\" class=\"arrow-icon\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 加载更多提示 -->\r\n\t\t\t<view class=\"load-more\" v-if=\"hasMore\">\r\n\t\t\t\t<view class=\"loading-dot\" v-if=\"loading\">\r\n\t\t\t\t\t<view class=\"dot\"></view>\r\n\t\t\t\t\t<view class=\"dot\"></view>\r\n\t\t\t\t\t<view class=\"dot\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text>{{loading ? '加载中...' : '上拉加载更多'}}</text>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 无搜索结果 -->\r\n\t\t<view class=\"no-result\" v-else-if=\"keyword && !loading\">\r\n\t\t\t<image :src=\"pre_url+'/static/img/no-result.png'\" mode=\"aspectFit\" class=\"no-result-icon\"/>\r\n\t\t\t<text class=\"main-text\">未找到相关地址</text>\r\n\t\t\t<text class=\"sub-text\">换个关键词试试</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 加载中 -->\r\n\t\t<view class=\"loading-overlay\" v-if=\"loading\">\r\n\t\t\t<view class=\"loading-content\">\r\n\t\t\t\t<view class=\"loading-spinner\">\r\n\t\t\t\t\t<view class=\"spinner-item\" v-for=\"i in 12\" :key=\"i\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text>加载中...</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\tkeyword: '',\r\n\t\t\tcurrentAddress: '',\r\n\t\t\tlocationInfo: null,\r\n\t\t\tsearchResults: [],\r\n\t\t\tloading: false,\r\n\t\t\tpage: 1,\r\n\t\t\tlatitude: '',\r\n\t\t\tlongitude: '',\r\n\t\t\thasMore: true,\r\n\t\t\tisLocating: false,\r\n\t\t\tsearchHistory: []  // 添加搜索历史数组\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.data) {\r\n\t\t\tconst data = JSON.parse(decodeURIComponent(options.data));\r\n\t\t\tthis.latitude = data.latitude;\r\n\t\t\tthis.longitude = data.longitude;\r\n\t\t\tthis.currentAddress = data.current_address;\r\n\t\t\tthis.locationInfo = data.location_info;\r\n\t\t}\r\n\t\t// 如果没有位置信息，获取当前位置\r\n\t\tif (!this.latitude || !this.longitude) {\r\n\t\t\tthis.getCurrentLocation();\r\n\t\t}\r\n\t\t\r\n\t\t// 加载搜索历史\r\n\t\tthis.loadSearchHistory();\r\n\t},\r\n\tmethods: {\r\n\t\t// 处理颜色\r\n\t\tt(name) {\r\n\t\t\ttry {\r\n\t\t\t\tconst colors = app.globalData.colors || {};\r\n\t\t\t\treturn colors[name] || '';\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取颜色错误:', e);\r\n\t\t\t\treturn '';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取当前位置\r\n\t\tgetCurrentLocation() {\r\n\t\t\tthis.isLocating = true;\r\n\t\t\tthis.loading = true;\r\n\t\t\tapp.getLocation((res) => {\r\n\t\t\t\tthis.latitude = res.latitude;\r\n\t\t\t\tthis.longitude = res.longitude;\r\n\t\t\t\t\r\n\t\t\t\t// 获取详细地址信息\r\n\t\t\t\tapp.get('apiIndex/getLocation', {\r\n\t\t\t\t\tlatitude: res.latitude,\r\n\t\t\t\t\tlongitude: res.longitude\r\n\t\t\t\t}, (result) => {\r\n\t\t\t\t\tif(result.status === 1) {\r\n\t\t\t\t\t\tthis.currentAddress = result.data.district + result.data.street;\r\n\t\t\t\t\t\tthis.locationInfo = result.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tthis.isLocating = false;\r\n\t\t\t\t});\r\n\t\t\t}, (err) => {\r\n\t\t\t\tconsole.log('获取位置失败：', err);\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t\tthis.isLocating = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取位置失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理搜索输入\r\n\t\thandleSearch() {\r\n\t\t\tif (this.searchTimer) {\r\n\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t}\r\n\t\t\tthis.searchTimer = setTimeout(() => {\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.searchResults = [];\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.searchLocation();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t// 搜索位置\r\n\t\tsearchLocation() {\r\n\t\t\tif (!this.keyword || this.loading || !this.hasMore) return;\r\n\t\t\t\r\n\t\t\tthis.loading = true;\r\n\t\t\tapp.get('apiIndex/searchLocation', {\r\n\t\t\t\tkeyword: this.keyword,\r\n\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\tlongitude: this.longitude,\r\n\t\t\t\tpage: this.page\r\n\t\t\t}, (res) => {\r\n\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\tif (this.page === 1) {\r\n\t\t\t\t\t\tthis.searchResults = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.searchResults = [...this.searchResults, ...res.data];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.hasMore = res.data.length === 10; // 假设每页10条数据\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载更多\r\n\t\tloadMore() {\r\n\t\t\tif (this.hasMore && !this.loading) {\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.searchLocation();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 清除搜索\r\n\t\tclearSearch() {\r\n\t\t\tthis.keyword = '';\r\n\t\t\tthis.searchResults = [];\r\n\t\t},\r\n\t\t\r\n\t\t// 加载搜索历史\r\n\t\tloadSearchHistory() {\r\n\t\t\ttry {\r\n\t\t\t\tconst history = uni.getStorageSync('location_search_history');\r\n\t\t\t\tif (history) {\r\n\t\t\t\t\tthis.searchHistory = JSON.parse(history);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('加载搜索历史失败', e);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 添加到搜索历史\r\n\t\taddToHistory(item) {\r\n\t\t\t// 如果存在相同项，先移除\r\n\t\t\tthis.searchHistory = this.searchHistory.filter(his => his.name !== item.name);\r\n\t\t\t\r\n\t\t\t// 添加到最前面\r\n\t\t\tthis.searchHistory.unshift({\r\n\t\t\t\tname: item.name,\r\n\t\t\t\tlatitude: item.latitude,\r\n\t\t\t\tlongitude: item.longitude,\r\n\t\t\t\taddress: item.address\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 最多保存10条记录\r\n\t\t\tif (this.searchHistory.length > 10) {\r\n\t\t\t\tthis.searchHistory = this.searchHistory.slice(0, 10);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 保存到本地\r\n\t\t\tuni.setStorageSync('location_search_history', JSON.stringify(this.searchHistory));\r\n\t\t},\r\n\t\t\r\n\t\t// 清除历史记录\r\n\t\tclearHistory() {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定要清除所有搜索历史吗？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.searchHistory = [];\r\n\t\t\t\t\t\tuni.removeStorageSync('location_search_history');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 使用历史记录项\r\n\t\tuseHistoryItem(item) {\r\n\t\t\t// 将历史记录项作为选择项使用\r\n\t\t\tthis.selectLocation(item);\r\n\t\t},\r\n\t\t\r\n\t\t// 使用当前位置\r\n\t\tuseCurrentLocation() {\r\n\t\t\tif (!this.locationInfo) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '位置信息不完整',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 触发选择位置事件\r\n\t\t\tuni.$emit('location_selected', {\r\n\t\t\t\t...this.locationInfo,\r\n\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\tlongitude: this.longitude,\r\n\t\t\t\tdisplay_name: this.currentAddress\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\t\r\n\t\t// 选择位置\r\n\t\tselectLocation(item) {\r\n\t\t\t// 添加到历史记录\r\n\t\t\tthis.addToHistory(item);\r\n\t\t\t\r\n\t\t\t// 触发选择位置事件\r\n\t\t\tuni.$emit('location_selected', {\r\n\t\t\t\t...item,\r\n\t\t\t\tlatitude: item.latitude,\r\n\t\t\t\tlongitude: item.longitude,\r\n\t\t\t\tdisplay_name: item.name\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.navigateBack();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\npage{background:#ffffff}\r\n.container{width:100%;}\r\n\r\n.location-page{\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f9fc;\r\n\tpadding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.search-section{\r\n\tbackground-color: #fff;\r\n\tpadding: 24rpx 30rpx;\r\n\tposition: sticky;\r\n\ttop: 0;\r\n\tz-index: 100;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0,0,0,0.03);\r\n}\r\n\r\n.search-box{\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.search-input-box{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground-color: #f8f9fc;\r\n\tborder-radius: 40rpx;\r\n\tpadding: 24rpx 32rpx;\r\n\ttransition: all 0.3s ease;\r\n\tborder: 2rpx solid transparent;\r\n}\r\n\r\n.search-input-box:active{\r\n\tbackground-color: #f0f2f7;\r\n\tborder-color: rgba(0,122,255,0.1);\r\n}\r\n\r\n.search-icon{\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n\tmargin-right: 20rpx;\r\n\topacity: 0.5;\r\n}\r\n\r\n.search-input{\r\n\tflex: 1;\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 400;\r\n}\r\n\r\n.search-input::placeholder{\r\n\tcolor: #999;\r\n\tfont-weight: 300;\r\n}\r\n\r\n.clear-btn{\r\n\tpadding: 12rpx;\r\n\tmargin-right: -12rpx;\r\n}\r\n\r\n.clear-icon{\r\n\twidth: 28rpx;\r\n\theight: 28rpx;\r\n\topacity: 0.3;\r\n\ttransition: opacity 0.3s;\r\n}\r\n\r\n.clear-icon:active{\r\n\topacity: 0.5;\r\n}\r\n\r\n.get-location-btn{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 24rpx;\r\n\tbackground: linear-gradient(to right, #f0f2f7, #f8f9fc);\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #ffffff;\r\n\ttransition: all 0.3s ease;\r\n\tborder: 2rpx solid transparent;\r\n}\r\n\r\n.get-location-btn:active{\r\n\ttransform: scale(0.98);\r\n\tborder-color: rgba(0,122,255,0.1);\r\n}\r\n\r\n.get-location-btn.loading{\r\n\topacity: 0.7;\r\n\tbackground: #f0f2f7;\r\n}\r\n\r\n.btn-icon{\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n\tmargin-right: 12rpx;\r\n\topacity: 0.6;\r\n}\r\n\r\n.btn-icon.rotating{\r\n\tanimation: rotate 1.2s linear infinite;\r\n}\r\n\r\n.current-location{\r\n\tbackground-color: #fff;\r\n\tmargin: 24rpx 24rpx 0;\r\n\tpadding: 36rpx 30rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tborder-radius: 28rpx;\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);\r\n\ttransition: all 0.3s ease;\r\n\tborder: 2rpx solid transparent;\r\n}\r\n\r\n.current-location:active{\r\n\ttransform: scale(0.985);\r\n\tborder-color: rgba(0,122,255,0.1);\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0,0,0,0.02);\r\n}\r\n\r\n.location-left{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.location-icon-wrap{\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #f0f2f7, #f8f9fc);\r\n\tborder-radius: 24rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 24rpx;\r\n}\r\n\r\n.location-icon{\r\n\twidth: 44rpx;\r\n\theight: 44rpx;\r\n\topacity: 0.6;\r\n}\r\n\r\n.location-info{\r\n\tflex: 1;\r\n}\r\n\r\n.location-info .title{\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 12rpx;\r\n\tdisplay: block;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.location-info .address{\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.4;\r\n}\r\n\r\n.use-btn{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 36rpx;\r\n\tbackground: linear-gradient(135deg, #007AFF, #0056b3);\r\n\tborder-radius: 40rpx;\r\n\tbox-shadow: 0 6rpx 16rpx rgba(0,122,255,0.15);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.use-btn:active{\r\n\ttransform: scale(0.95);\r\n\tbox-shadow: 0 3rpx 8rpx rgba(0,122,255,0.1);\r\n}\r\n\r\n.use-btn text{\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.use-btn .arrow-icon{\r\n\twidth: 28rpx;\r\n\theight: 28rpx;\r\n\tmargin-left: 8rpx;\r\n\topacity: 0.9;\r\n}\r\n\r\n.use-btn.disabled{\r\n\tbackground: linear-gradient(135deg, #ccc, #999);\r\n\tbox-shadow: none;\r\n\topacity: 0.8;\r\n}\r\n\r\n.history-section{\r\n\tbackground-color: #fff;\r\n\tmargin: 24rpx 24rpx 0;\r\n\tpadding: 30rpx;\r\n\tborder-radius: 28rpx;\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);\r\n}\r\n\r\n.section-header{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.section-header .title{\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.clear-history{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 12rpx;\r\n}\r\n\r\n.delete-icon{\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\topacity: 0.4;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.clear-history text{\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.history-list{\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin: 0 -10rpx;\r\n}\r\n\r\n.history-item{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 16rpx 24rpx;\r\n\tbackground: #f8f9fc;\r\n\tborder-radius: 32rpx;\r\n\tmargin: 10rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.history-item:active{\r\n\ttransform: scale(0.95);\r\n\tbackground: #f0f2f7;\r\n}\r\n\r\n.time-icon{\r\n\twidth: 28rpx;\r\n\theight: 28rpx;\r\n\topacity: 0.4;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.history-text{\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.location-list{\r\n\tbackground-color: #fff;\r\n\tmargin: 24rpx 24rpx 0;\r\n\tmax-height: calc(100vh - 320rpx);\r\n\tborder-radius: 28rpx;\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);\r\n\toverflow: hidden;\r\n}\r\n\r\n.location-item{\r\n\tpadding: 36rpx 30rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tborder-bottom: 1rpx solid rgba(0,0,0,0.03);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.location-item:last-child{\r\n\tborder-bottom: none;\r\n}\r\n\r\n.location-item.item-hover{\r\n\tbackground-color: #f8f9fc;\r\n}\r\n\r\n.location-detail{\r\n\tflex: 1;\r\n\tpadding-right: 24rpx;\r\n}\r\n\r\n.name-wrap{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.name{\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.tag{\r\n\tfont-size: 22rpx;\r\n\tcolor: #007AFF;\r\n\tbackground: rgba(0,122,255,0.1);\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.address{\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.4;\r\n}\r\n\r\n.right-area{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.distance{\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 16rpx;\r\n}\r\n\r\n.arrow-icon{\r\n\twidth: 28rpx;\r\n\theight: 28rpx;\r\n\topacity: 0.3;\r\n\ttransition: opacity 0.3s;\r\n}\r\n\r\n.location-item:active .arrow-icon{\r\n\topacity: 0.5;\r\n}\r\n\r\n.load-more{\r\n\ttext-align: center;\r\n\tpadding: 30rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 26rpx;\r\n\tbackground: linear-gradient(to bottom, transparent, rgba(248,249,252,0.8));\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.loading-dot{\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.dot{\r\n\twidth: 12rpx;\r\n\theight: 12rpx;\r\n\tbackground: #999;\r\n\tborder-radius: 50%;\r\n\tmargin: 0 4rpx;\r\n\topacity: 0.6;\r\n\tanimation: dot-jump 1.4s infinite ease-in-out;\r\n}\r\n\r\n.dot:nth-child(2){\r\n\tanimation-delay: 0.2s;\r\n}\r\n\r\n.dot:nth-child(3){\r\n\tanimation-delay: 0.4s;\r\n}\r\n\r\n.no-result{\r\n\tpadding: 160rpx 0;\r\n\ttext-align: center;\r\n\tcolor: #999;\r\n}\r\n\r\n.no-result-icon{\r\n\twidth: 280rpx;\r\n\theight: 280rpx;\r\n\tmargin-bottom: 40rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.main-text{\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.sub-text{\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\topacity: 0.8;\r\n}\r\n\r\n.loading-overlay{\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(255,255,255,0.95);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 999;\r\n\tbackdrop-filter: blur(8px);\r\n\t-webkit-backdrop-filter: blur(8px);\r\n}\r\n\r\n.loading-content{\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.loading-spinner{\r\n\tposition: relative;\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.spinner-item{\r\n\tposition: absolute;\r\n\twidth: 8rpx;\r\n\theight: 24rpx;\r\n\tbackground: var(--color1);\r\n\tborder-radius: 4rpx;\r\n\tanimation: spinner-fade 1.2s linear infinite;\r\n}\r\n\r\n@keyframes dot-jump{\r\n\t0%, 80%, 100%{\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\t40%{\r\n\t\ttransform: translateY(-8rpx);\r\n\t}\r\n}\r\n\r\n@keyframes spinner-fade{\r\n\t0%{\r\n\t\topacity: 1;\r\n\t}\r\n\t100%{\r\n\t\topacity: 0.15;\r\n\t}\r\n}\r\n\r\n@keyframes rotate{\r\n\tfrom{\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\tto{\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n::-webkit-scrollbar{\r\n\twidth: 0;\r\n\theight: 0;\r\n\tbackground: transparent;\r\n}\r\n\r\n/* 添加spinner-item的12个状态 */\r\n.spinner-item:nth-child(1){ transform: rotate(30deg); animation-delay: 0s; }\r\n.spinner-item:nth-child(2){ transform: rotate(60deg); animation-delay: 0.1s; }\r\n.spinner-item:nth-child(3){ transform: rotate(90deg); animation-delay: 0.2s; }\r\n.spinner-item:nth-child(4){ transform: rotate(120deg); animation-delay: 0.3s; }\r\n.spinner-item:nth-child(5){ transform: rotate(150deg); animation-delay: 0.4s; }\r\n.spinner-item:nth-child(6){ transform: rotate(180deg); animation-delay: 0.5s; }\r\n.spinner-item:nth-child(7){ transform: rotate(210deg); animation-delay: 0.6s; }\r\n.spinner-item:nth-child(8){ transform: rotate(240deg); animation-delay: 0.7s; }\r\n.spinner-item:nth-child(9){ transform: rotate(270deg); animation-delay: 0.8s; }\r\n.spinner-item:nth-child(10){ transform: rotate(300deg); animation-delay: 0.9s; }\r\n.spinner-item:nth-child(11){ transform: rotate(330deg); animation-delay: 1.0s; }\r\n.spinner-item:nth-child(12){ transform: rotate(360deg); animation-delay: 1.1s; }\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115021699\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}