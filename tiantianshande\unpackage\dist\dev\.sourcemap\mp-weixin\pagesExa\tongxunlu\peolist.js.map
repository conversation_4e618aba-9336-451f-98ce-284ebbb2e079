{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?8df8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?344b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?fa63", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?f089", "uni-app:///pagesExa/tongxunlu/peolist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?4ac9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/peolist.vue?01e1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "keyword", "datalist", "type", "nodata", "curTopIndex", "index", "curCid", "nomore", "pagenum", "regionList", "regionNames", "subRegionList", "subRegionNames", "selectedRegion", "selectedSubRegion", "clist", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "cid", "bid", "id", "name", "children", "onRegionChange", "onSubRegionChange", "getAll<PERSON><PERSON><PERSON>n", "allChildren", "getdatalist", "field", "order", "longitude", "latitude", "item", "searchChange", "searchConfirm", "call", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2DhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACAC;MAEAC;QAAAC;QAAAC;MAAA;QACAH;QAEA;UACAA;YAAA;cACAI;cACAC;cACAC;YACA;UAAA;UACAN;YAAA;UAAA;;UAEAA;UAEA;YACAA;YACA;YACAA;YACAA;cAAA;YAAA;UACA;;UAEAA;QACA;QAEAA;MACA;IACA;IAEAO;MACA;MACA;;MAEA;MACA;MACA;QAAA;MAAA;;MAEA;IACA;IAGAC;MACA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACAH;QACA;UACA;UACAI;QACA;UACA;UACAA;YAAAN;YAAAC;UAAA;QACA;MACA;MAEA;IACA;IAGAM;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAX;MACAA;MACAA;MACA;MACA;MAEAC;QAAAd;QAAAR;QAAAiC;QAAAC;QAAAX;QAAAC;QAAAtB;QAAAiC;QAAAC;MAAA;QACAf;QACA;;QAEA;QACA1B;UACA;YACA0C;UACA;QACA;;QAEA;UACAhB;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEAiB;MACA;IACA;;IAEAC;MACA;IACA;;IAEAC;MACAlD;QACAmD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tongxunlu/peolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tongxunlu/peolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peolist.vue?vue&type=template&id=c5e42e10&\"\nvar renderjs\nimport script from \"./peolist.vue?vue&type=script&lang=js&\"\nexport * from \"./peolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tongxunlu/peolist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=template&id=c5e42e10&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"header-image\">\r\n      <image src=\"https://kuaifengimg.azheteng.cn/upload/1/20240825/cd61ae6d329b86ca6717f0c25a197005_thumb.png\" mode=\"widthFix\" class=\"header-img\"></image>\r\n    </view>\r\n    <block v-if=\"isload\">\r\n         <!-- 选择所在区域文字 -->\r\n          <view class=\"select-region-text\">\r\n            选择所在区域\r\n          </view>\r\n          \r\n          <!-- 区县和小区筛选器 -->\r\n          <view class=\"region-select\">\r\n            <picker mode=\"selector\" :range=\"regionNames\" @change=\"onRegionChange\" class=\"picker-region\">\r\n              <view class=\"picker-view\">{{ selectedRegion.name || '请选择区县' }}</view>\r\n            </picker>\r\n            <picker mode=\"selector\" :range=\"subRegionNames\" @change=\"onSubRegionChange\" :disabled=\"!selectedRegion.id\" class=\"picker-subregion\">\r\n              <view class=\"picker-view\">{{ selectedSubRegion.name || '请选择小区' }}</view>\r\n            </picker>\r\n          </view>\r\n\r\n      <!-- 搜索框 -->\r\n     <!-- <view class=\"topsearch flex-y-center\">\r\n        <view class=\"f1 flex-y-center\">\r\n          <image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n          <input :value=\"keyword\" placeholder=\"输入姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n        </view>\r\n      </view> -->\r\n\r\n      <!-- 表头 -->\r\n      <view class=\"table-header\">\r\n        <view class=\"header-cell\">小区名称</view>\r\n        <view class=\"header-cell\">服务人员</view>\r\n        <view class=\"header-cell\">电话</view>\r\n      </view>\r\n\r\n     <view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"table-row\" :data-id=\"item.id\">\r\n         <view class=\"table-cell community-name\">\r\n             {{ item.community_name }}\r\n         </view>\r\n         <view class=\"table-cell service-staff\">\r\n             {{ item.realname }}\r\n         </view>\r\n         <view class=\"table-cell service-tel\">\r\n             <image src=\"/static/img/tongxunlutle.png\" mode=\"aspectFit\" class=\"call-icon\" @click=\"call(item.tel)\" />\r\n         </view>\r\n     </view>\r\n\r\n\r\n      <nodata v-if=\"nodata\"></nodata>\r\n      <nomore v-if=\"nomore\"></nomore>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      menuindex: -1,\r\n      keyword: '', // 搜索关键词\r\n      datalist: [], // 用于存放人员数据\r\n      type: \"\",\r\n      nodata: false,\r\n      curTopIndex: -1,\r\n      index: 0,\r\n      curCid: 0, // 当前选择的小区ID\r\n      nomore: false,\r\n      pagenum: 1,\r\n      regionList: [], // 存放区县列表对象\r\n      regionNames: [], // 存放区县名称的字符串数组，供 picker 使用\r\n      subRegionList: [], // 存放小区列表对象\r\n      subRegionNames: [], // 存放小区名称的字符串数组，供 picker 使用\r\n      selectedRegion: {}, // 用户选择的区县\r\n      selectedSubRegion: {}, // 用户选择的小区\r\n      clist: [], // 存放API返回的数据，确保其定义并初始化\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.type = this.opt.type || '';\r\n    this.getdata();\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdatalist(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      var nowcid = that.opt.cid;\r\n      var bid = that.opt.bid || 0;\r\n      if (!nowcid) nowcid = '';\r\n      that.loading = true;\r\n      \r\n      app.get('Apitongxunlu/peocategory', { cid: nowcid, bid: bid }, function (res) {\r\n        that.loading = false;\r\n\r\n        if (res.data && res.data.length > 0) {\r\n          that.regionList = res.data.map(item => ({\r\n            id: item.id, \r\n            name: item.name,\r\n            children: item.children || []\r\n          }));\r\n          that.regionNames = res.data.map(item => item.name); // 提取区县名称供 picker 使用\r\n\r\n          that.clist = res.data;\r\n\r\n          if (that.regionList.length > 0) {\r\n            that.selectedRegion = that.regionList[0];\r\n            // 递归获取所有子层级的小区列表\r\n            that.subRegionList = that.getAllChildren(that.regionList[0].children);\r\n            that.subRegionNames = that.subRegionList.map(item => item.name); // 提取小区名称供 picker 使用\r\n          }\r\n          \r\n          that.getdatalist();\r\n        }\r\n        \r\n        that.loaded();\r\n      });\r\n    },\r\n\r\n onRegionChange: function (e) {\r\n  var selectedRegionIndex = e.detail.value;\r\n  this.selectedRegion = this.regionList[selectedRegionIndex];\r\n\r\n  // 更新 subRegionList 和 subRegionNames 只关注 children 的 children\r\n  this.subRegionList = this.getAllChildren(this.selectedRegion.children);\r\n  this.subRegionNames = this.subRegionList.map(item => item.name); // 更新小区名称供 picker 使用\r\n\r\n  this.selectedSubRegion = {}; \r\n},\r\n\r\n\r\n    onSubRegionChange: function (e) {\r\n      var selectedSubRegionIndex = e.detail.value;\r\n      this.selectedSubRegion = this.subRegionList[selectedSubRegionIndex];\r\n      this.curCid = this.selectedSubRegion.id;\r\n\r\n      // 调用接口获取选定小区的人员数据\r\n      this.getdatalist();\r\n    },\r\n\r\n   // 递归获取所有子层级的小区\r\n   getAllChildren: function (children) {\r\n     let allChildren = [];\r\n   \r\n     // 遍历当前层级的 children\r\n     children.forEach(child => {\r\n       if (child.children && child.children.length > 0) {\r\n         // 如果 child 有子级，则递归获取子级的小区\r\n         allChildren = allChildren.concat(this.getAllChildren(child.children));\r\n       } else {\r\n         // 如果没有子级，则添加到结果中\r\n         allChildren.push({ id: child.id, name: child.name });\r\n       }\r\n     });\r\n   \r\n     return allChildren;\r\n   },\r\n\r\n    \r\n    getdatalist: function (loadmore) {\r\n      if (!loadmore) {\r\n        this.pagenum = 1;\r\n        this.datalist = [];\r\n      }\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var cid = that.curCid; // 使用选择的小区ID\r\n      var bid = that.opt.bid ? that.opt.bid : '';\r\n      var order = that.order;\r\n      var keyword = that.keyword; // 关键字\r\n      var field = that.field;\r\n      that.loading = true;\r\n      that.nodata = false;\r\n      that.nomore = false;\r\n      var latitude = that.latitude;\r\n      var longitude = that.longitude;\r\n      \r\n      app.post('Apitongxunlu/selectpeople', { pagenum: pagenum, keyword: keyword, field: field, order: order, cid: cid, bid: bid, type: 'list', longitude: longitude, latitude: latitude }, function (res) { \r\n        that.loading = false;\r\n        var data = res.data;\r\n\r\n        // 遍历每个数据项并提取服务站信息\r\n        data.forEach(item => {\r\n          if (item.second_level_name) {\r\n            item.service_area = item.second_level_name; // 使用 second_level_name 作为服务站信息\r\n          }\r\n        });\r\n\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        } else {\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    searchChange: function (e) {\r\n      this.keyword = e.detail.value; // 更新搜索关键字\r\n    },\r\n\r\n    searchConfirm: function () {\r\n      this.getdatalist(false); // 当用户确认搜索时调用，重新加载数据\r\n    },\r\n\r\n    call: function(tel) {\r\n      wx.makePhoneCall({\r\n        phoneNumber: tel\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n\r\n<style>\r\n.topsearch{width:94%;margin:16rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\r\n.search-history {padding: 24rpx 34rpx;}\r\n.search-history .search-history-title {color: #666;}\r\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\r\n.search-history-list {padding: 24rpx 0 0 0;}\r\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\r\n\r\n\r\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}\r\n.order-tab2 .on .after{display:block}\r\n\r\n\r\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx 40rpx; justify-content: space-between;}\r\n.content .f1{display:flex;align-items:center}\r\n.content .f1 image{ width: 140rpx; height: 140rpx; border-radius: 10rpx;}\r\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:10rpx;}\r\n.content .f1 .t2{color:#999999;font-size:28rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content .f1 .t3{ margin-left:10rpx;display: block; height: 40rpx;line-height: 40rpx;}\r\n.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}\r\n.content .f3{height:96rpx;display:flex;align-items:center}\r\n.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content .radio .radio-img{width:100%;height:100%}\r\n.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\r\n\r\n\r\n.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3 .t5{ margin-left: 20rpx;}\r\n.text3 .t5 text{ color:#7A83EC}\r\n.text3 .t4 text{ color:#7A83EC}\r\n.yuyue{ background: #7A83EC; height: 40rpx; line-height: 40rpx; padding: 0 10rpx; color:#fff; border-radius:28rpx; width: 80rpx; font-size: 20rpx; text-align: center; margin-top: 20rpx;}\r\n.text1{ margin-left: 10rpx;}\r\n.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:20rpx;}\r\n/* 容器样式 */\r\n.container {\r\n  padding: 16rpx;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n/* 筛选器容器 */\r\n.region-select {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 每个筛选器的样式 */\r\n.picker-region,\r\n.picker-subregion {\r\n  flex: 1;\r\n  margin-right: 10rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 8rpx;\r\n  border: 1rpx solid #dcdcdc;\r\n  overflow: hidden;\r\n}\r\n\r\n/* picker 内部的文本样式 */\r\n.picker-view {\r\n  padding: 20rpx;\r\n  color: #333333;\r\n  font-size: 28rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 禁用状态下的样式 */\r\n.picker-subregion:disabled .picker-view {\r\n  background-color: #f0f0f0;\r\n  color: #999999;\r\n}\r\n\r\n/* 当 picker 被点击时的效果 */\r\n.picker-view:active {\r\n  background-color: #eeeeee;\r\n}\r\n.container {\r\n  padding: 16rpx;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n/* 顶部图片的容器样式 */\r\n.header-image {\r\n  width: 100%;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 图片的样式 */\r\n.header-img {\r\n  width: 100%;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 筛选器容器 */\r\n.region-select {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 每个筛选器的样式 */\r\n.picker-region,\r\n.picker-subregion {\r\n  flex: 1;\r\n  margin-right: 10rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 8rpx;\r\n  border: 1rpx solid #dcdcdc;\r\n  overflow: hidden;\r\n}\r\n\r\n/* picker 内部的文本样式 */\r\n.picker-view {\r\n  padding: 20rpx;\r\n  color: #333333;\r\n  font-size: 28rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 禁用状态下的样式 */\r\n.picker-subregion:disabled .picker-view {\r\n  background-color: #f0f0f0;\r\n  color: #999999;\r\n}\r\n\r\n/* 当 picker 被点击时的效果 */\r\n.picker-view:active {\r\n  background-color: #eeeeee;\r\n}\r\n\r\n\r\n/* 拨打电话图片样式 */\r\n.call-btn-img {\r\n  width: 50rpx !important;\r\n  height: 50rpx !important;\r\n  display: block; /* 确保图片是块级元素 */\r\n  margin: auto; /* 居中对齐 */\r\n}\r\n\r\n/* 通用容器样式 */\r\n.container {\r\n  padding: 16rpx;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n/* 顶部图片的容器样式 */\r\n.header-image {\r\n  width: 100%;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 图片的样式 */\r\n.header-img {\r\n  width: 100%;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 表头样式 */\r\n.table-header {\r\n  display: flex;\r\n  background-color: #f0f0f0;\r\n  padding: 10rpx 20rpx;\r\n  font-weight: bold;\r\n  border-bottom: 1rpx solid #dcdcdc;\r\n}\r\n\r\n.header-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 行样式 */\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10rpx 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 单元格样式 */\r\n.table-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n/* 服务人员图标 */\r\n.call-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-left: 10rpx;\r\n  vertical-align: middle;\r\n}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115054764\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}