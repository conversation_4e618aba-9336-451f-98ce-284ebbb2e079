{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?c64a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?d33f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?0ff4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?bb0f", "uni-app:///pagesExt/cityagent/merchant_detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?b181", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant_detail.vue?d756"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "merchant_id", "merchant", "statistics", "today_orders", "today_amount", "month_orders", "month_amount", "total_orders", "total_amount", "recent_orders", "onLoad", "app", "onPullDownRefresh", "methods", "getdata", "that", "uni", "title", "callMerchant", "content", "success", "phoneNumber", "fail", "viewLocation", "longitude", "latitude", "name", "address", "scale", "goto", "url", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAowB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqIxxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;IAEA;MACAC;MACA;IACA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MAEAJ;QACAX;MACA;QACAe;QACAC;QAEA;UACAL;UACA;QACA;;QAEA;QACAK;UACAC;QACA;;QAEA;QACAF;QACAA;QACAA;QAEAA;MACA;IACA;IAEA;IACAG;MACA;MACA;QACAP;QACA;MACA;MAEAK;QACAC;QACAE;QACAC;UACA;YACAJ;cACAK;cACAC;gBACAX;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAY;MACA;MACA;MACA;MAEA;QACAZ;QACA;MACA;MAEAK;QACAQ;QACAC;QACAC;QACAC;QACAC;QACAN;UACAX;QACA;MACA;IACA;IAEA;IACAkB;MACA;MACA;QACA;QACAC;QACAnB;MACA;IACA;IAEA;IACAoB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAAolC,CAAgB,gkCAAG,EAAC,C;;;;;;;;;;;ACAxmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/merchant_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/merchant_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./merchant_detail.vue?vue&type=template&id=667260cf&\"\nvar renderjs\nimport script from \"./merchant_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./merchant_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./merchant_detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/merchant_detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant_detail.vue?vue&type=template&id=667260cf&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.recent_orders.length : null\n  var g1 = _vm.isload ? _vm.recent_orders.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- 商户基本信息 -->\r\n\t\t<view class=\"merchant-info-card\">\r\n\t\t\t<view class=\"merchant-header\">\r\n\t\t\t\t<view class=\"merchant-avatar\">\r\n\t\t\t\t\t<image :src=\"merchant.logo || '/static/img/default_merchant.png'\" class=\"avatar-img\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"merchant-info\">\r\n\t\t\t\t\t<view class=\"merchant-name\">{{merchant.name}}</view>\r\n\t\t\t\t\t<view class=\"merchant-category\">{{merchant.category_name || '未分类'}}</view>\r\n\t\t\t\t\t<view class=\"merchant-status\">\r\n\t\t\t\t\t\t<text class=\"status-badge\" :style=\"{background: merchant.status_color}\">\r\n\t\t\t\t\t\t\t{{merchant.status_text}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"merchant-details\">\r\n\t\t\t\t<view class=\"detail-item\" v-if=\"merchant.address\">\r\n\t\t\t\t\t<text class=\"detail-label\">地址：</text>\r\n\t\t\t\t\t<text class=\"detail-value\">{{merchant.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail-item\" v-if=\"merchant.phone\">\r\n\t\t\t\t\t<text class=\"detail-label\">电话：</text>\r\n\t\t\t\t\t<text class=\"detail-value\">{{merchant.phone}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail-item\" v-if=\"merchant.description\">\r\n\t\t\t\t\t<text class=\"detail-label\">简介：</text>\r\n\t\t\t\t\t<text class=\"detail-value\">{{merchant.description}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t<text class=\"detail-label\">注册时间：</text>\r\n\t\t\t\t\t<text class=\"detail-value\">{{merchant.createtime_format}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t<text class=\"detail-label\">账户余额：</text>\r\n\t\t\t\t\t<text class=\"detail-value highlight\">¥{{merchant.money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 操作按钮 -->\r\n\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t<view class=\"action-btn primary\" @tap=\"callMerchant\" v-if=\"merchant.phone\">\r\n\t\t\t\t\t<text class=\"iconfont icondianhua\"></text>\r\n\t\t\t\t\t<text>拨打电话</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn\" @tap=\"viewLocation\" v-if=\"merchant.longitude && merchant.latitude\">\r\n\t\t\t\t\t<text class=\"iconfont iconweizhi\"></text>\r\n\t\t\t\t\t<text>查看位置</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 统计数据 -->\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<view class=\"stats-title\">经营数据</view>\r\n\t\t\t<view class=\"stats-grid\">\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{statistics.today_orders}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">今日订单</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">¥{{statistics.today_amount}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">今日交易额</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{statistics.month_orders}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">本月订单</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">¥{{statistics.month_amount}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">本月交易额</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">{{statistics.total_orders}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">总订单数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t\t<text class=\"stat-number\">¥{{statistics.total_amount}}</text>\r\n\t\t\t\t\t<text class=\"stat-label\">总交易额</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 最近订单 -->\r\n\t\t<view class=\"recent-orders\" v-if=\"recent_orders.length > 0\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">最近订单</text>\r\n\t\t\t\t<text class=\"section-more\" @tap=\"goto\" :data-url=\"'merchant_orders?merchant_id=' + merchant.id\">查看更多</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-list\">\r\n\t\t\t\t<view class=\"order-item\" v-for=\"(order, index) in recent_orders\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"order-header\">\r\n\t\t\t\t\t\t<view class=\"order-user\">\r\n\t\t\t\t\t\t\t<image :src=\"order.headimg || '/static/img/default_avatar.png'\" class=\"user-avatar\"></image>\r\n\t\t\t\t\t\t\t<text class=\"user-name\">{{order.nickname || '用户'}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"order-status\">\r\n\t\t\t\t\t\t\t<text class=\"status-text\">{{order.status_text}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"order-content\">\r\n\t\t\t\t\t\t<view class=\"order-info\">\r\n\t\t\t\t\t\t\t<text class=\"order-sn\">订单号：{{order.ordersn}}</text>\r\n\t\t\t\t\t\t\t<text class=\"order-time\">{{order.createtime_format}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"order-amount\">\r\n\t\t\t\t\t\t\t<text class=\"amount-text\">¥{{order.totalprice}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 空状态 -->\r\n\t\t<view class=\"empty-orders\" v-if=\"recent_orders.length === 0\">\r\n\t\t\t<text class=\"empty-text\">暂无订单记录</text>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!-- 加载状态 -->\r\n\t<loading v-if=\"loading\"></loading>\r\n\t\r\n\t<!-- 底部导航 -->\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\r\n\t<!-- 消息提示 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tmerchant_id: 0,\r\n\t\t\tmerchant: {},\r\n\t\t\tstatistics: {\r\n\t\t\t\ttoday_orders: 0,\r\n\t\t\t\ttoday_amount: '0.00',\r\n\t\t\t\tmonth_orders: 0,\r\n\t\t\t\tmonth_amount: '0.00',\r\n\t\t\t\ttotal_orders: 0,\r\n\t\t\t\ttotal_amount: '0.00'\r\n\t\t\t},\r\n\t\t\trecent_orders: []\r\n\t\t};\r\n\t},\r\n\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.merchant_id = opt.merchant_id || 0;\r\n\t\t\r\n\t\tif (!this.merchant_id) {\r\n\t\t\tapp.error('参数错误');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 获取商户详情\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.get('ApiCityAgent/getMerchantDetail', {\r\n\t\t\t\tmerchant_id: that.merchant_id\r\n\t\t\t}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置导航标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.merchant.name || '商户详情'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 更新数据\r\n\t\t\t\tthat.merchant = res.merchant;\r\n\t\t\t\tthat.statistics = res.statistics;\r\n\t\t\t\tthat.recent_orders = res.recent_orders || [];\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 拨打电话\r\n\t\tcallMerchant: function () {\r\n\t\t\tvar phone = this.merchant.phone;\r\n\t\t\tif (!phone) {\r\n\t\t\t\tapp.error('该商户未设置联系电话');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '拨打电话',\r\n\t\t\t\tcontent: '确认拨打电话：' + phone + '？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\t\t\tapp.error('拨打电话失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查看位置\r\n\t\tviewLocation: function () {\r\n\t\t\tvar longitude = parseFloat(this.merchant.longitude);\r\n\t\t\tvar latitude = parseFloat(this.merchant.latitude);\r\n\t\t\tvar name = this.merchant.name;\r\n\t\t\t\r\n\t\t\tif (!longitude || !latitude) {\r\n\t\t\t\tapp.error('该商户未设置位置信息');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tlongitude: longitude,\r\n\t\t\t\tlatitude: latitude,\r\n\t\t\t\tname: name,\r\n\t\t\t\taddress: this.merchant.address,\r\n\t\t\t\tscale: 18,\r\n\t\t\t\tfail: function() {\r\n\t\t\t\t\tapp.error('打开地图失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 页面跳转\r\n\t\tgoto: function (e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tif (url) {\r\n\t\t\t\t// 替换模板变量\r\n\t\t\t\turl = url.replace('{{merchant.id}}', this.merchant.id);\r\n\t\t\t\tapp.goto('/pagesExt/cityagent/' + url);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 数据加载完成\r\n\t\tloaded: function () {\r\n\t\t\tthis.isload = true;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n/* 商户信息卡片 */\r\n.merchant-info-card {\r\n\tbackground: white;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.merchant-header {\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.merchant-avatar {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n\tmargin-right: 30rpx;\r\n}\r\n\r\n.avatar-img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.merchant-info {\r\n\tflex: 1;\r\n}\r\n\r\n.merchant-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.merchant-category {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.merchant-status {\r\n\t\r\n}\r\n\r\n.status-badge {\r\n\tfont-size: 24rpx;\r\n\tcolor: white;\r\n\tpadding: 10rpx 20rpx;\r\n\tborder-radius: 25rpx;\r\n}\r\n\r\n.merchant-details {\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tpadding-top: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.detail-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.detail-item:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.detail-label {\r\n\twidth: 140rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.detail-value {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tword-break: break-all;\r\n}\r\n\r\n.detail-value.highlight {\r\n\tcolor: #4CAF50;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tpadding-top: 30rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 25rpx 0;\r\n\tmargin-right: 20rpx;\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 15rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.action-btn:last-child {\r\n\tmargin-right: 0;\r\n}\r\n\r\n.action-btn.primary {\r\n\tbackground: #4CAF50;\r\n\tcolor: white;\r\n}\r\n\r\n.action-btn .iconfont {\r\n\tfont-size: 36rpx;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.action-btn.primary .iconfont {\r\n\tcolor: white;\r\n}\r\n\r\n/* 统计数据 */\r\n.stats-container {\r\n\tbackground: white;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.stats-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n}\r\n\r\n.stat-card {\r\n\twidth: 33.33%;\r\n\ttext-align: center;\r\n\tpadding: 25rpx 0;\r\n\tborder-right: 1rpx solid #f0f0f0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.stat-card:nth-child(3n) {\r\n\tborder-right: none;\r\n}\r\n\r\n.stat-card:nth-child(n+4) {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.stat-number {\r\n\tdisplay: block;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stat-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 最近订单 */\r\n.recent-orders {\r\n\tbackground: white;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.section-more {\r\n\tfont-size: 26rpx;\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n.order-list {\r\n\t\r\n}\r\n\r\n.order-item {\r\n\tpadding: 25rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.order-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.order-user {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.user-avatar {\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n\tborder-radius: 50%;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.user-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.order-status {\r\n\t\r\n}\r\n\r\n.status-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #4CAF50;\r\n\tpadding: 5rpx 15rpx;\r\n\tbackground: rgba(76, 175, 80, 0.1);\r\n\tborder-radius: 15rpx;\r\n}\r\n\r\n.order-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-end;\r\n}\r\n\r\n.order-info {\r\n\tflex: 1;\r\n}\r\n\r\n.order-sn {\r\n\tdisplay: block;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.order-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.order-amount {\r\n\t\r\n}\r\n\r\n.amount-text {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-orders {\r\n\tbackground: white;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 80rpx 30rpx;\r\n\ttext-align: center;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant_detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant_detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103624\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}