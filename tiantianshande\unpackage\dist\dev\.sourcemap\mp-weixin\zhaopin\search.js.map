{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?413d", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?1a87", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?98b3", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?f1a9", "uni-app:///zhaopin/search.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?0081", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/search.vue?6096"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchParams", "keyword", "category_id", "type_id", "education", "experience", "salary_min", "salary_max", "province", "city", "district", "benefits", "sort_field", "sort_type", "page", "limit", "showFilterPanel", "currentSort", "sortType", "jobList", "statistics", "isLoading", "noMore", "isRefreshing", "categoryList", "jobTypes", "educationList", "experienceList", "salaryRanges", "min", "max", "text", "benefitsList", "computed", "topSalaryRange", "topSalaryPercentage", "topExperience", "topExperiencePercentage", "onLoad", "methods", "getInitialData", "app", "that", "searchJobs", "handleSearch", "handleSort", "toggleFilterPanel", "selectCategory", "selectJobType", "selectEducation", "selectExperience", "selectSalary", "handleRegionChange", "toggleBenefit", "resetFilters", "confirmFilters", "loadMore", "onRefresh", "goToDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuQ/wB;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC,eACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;QACA;UACAC;UACAA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MAEA;QACA;QACA;QACA;MACA;MAEAF;QACAC;QACAA;QAEA;UACA;YACAA;UACA;YACAA;UACA;UAEAA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAA5C;QAAAC;QAAAC;MACA;MACA;MACA;IACA;IAEA;IACA2C;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA,oDACA;QACApD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MAAA,EACA;IACA;IAEA;IACA4C;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACAjB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzfA;AAAA;AAAA;AAAA;AAA07C,CAAgB,q4CAAG,EAAC,C;;;;;;;;;;;ACA98C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=fadcd368&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=fadcd368&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fadcd368\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/search.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=template&id=fadcd368&scoped=true&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var l0 = _vm.showFilterPanel\n    ? _vm.__map(_vm.categoryList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 =\n          _vm.searchParams.category_id === item.id ? _vm.t(\"color1rgb\") : null\n        var m2 =\n          _vm.searchParams.category_id === item.id ? _vm.t(\"color1\") : null\n        var m3 =\n          _vm.searchParams.category_id === item.id ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var l1 = _vm.showFilterPanel\n    ? _vm.__map(_vm.jobTypes, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m4 =\n          _vm.searchParams.type_id === item.id ? _vm.t(\"color1rgb\") : null\n        var m5 = _vm.searchParams.type_id === item.id ? _vm.t(\"color1\") : null\n        var m6 = _vm.searchParams.type_id === item.id ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n        }\n      })\n    : null\n  var l2 = _vm.showFilterPanel\n    ? _vm.__map(_vm.educationList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m7 = _vm.searchParams.education === item ? _vm.t(\"color1rgb\") : null\n        var m8 = _vm.searchParams.education === item ? _vm.t(\"color1\") : null\n        var m9 = _vm.searchParams.education === item ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n        }\n      })\n    : null\n  var l3 = _vm.showFilterPanel\n    ? _vm.__map(_vm.experienceList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m10 =\n          _vm.searchParams.experience === item ? _vm.t(\"color1rgb\") : null\n        var m11 = _vm.searchParams.experience === item ? _vm.t(\"color1\") : null\n        var m12 = _vm.searchParams.experience === item ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n        }\n      })\n    : null\n  var l4 = _vm.showFilterPanel\n    ? _vm.__map(_vm.salaryRanges, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m13 =\n          _vm.searchParams.salary_min === item.min &&\n          _vm.searchParams.salary_max === item.max\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m14 =\n          _vm.searchParams.salary_min === item.min &&\n          _vm.searchParams.salary_max === item.max\n            ? _vm.t(\"color1\")\n            : null\n        var m15 =\n          _vm.searchParams.salary_min === item.min &&\n          _vm.searchParams.salary_max === item.max\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n        }\n      })\n    : null\n  var l5 = _vm.showFilterPanel\n    ? _vm.__map(_vm.benefitsList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.searchParams.benefits.includes(item)\n        var g1 = _vm.searchParams.benefits.includes(item)\n        var m16 = g1 ? _vm.t(\"color1rgb\") : null\n        var m17 = g1 ? _vm.t(\"color1\") : null\n        var m18 = g1 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n          m16: m16,\n          m17: m17,\n          m18: m18,\n        }\n      })\n    : null\n  var m19 = _vm.showFilterPanel ? _vm.t(\"color1\") : null\n  var m20 = _vm.t(\"color1\")\n  var l6 = _vm.__map(_vm.jobList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g2 = item.work_time_start.substring(0, 5)\n    var g3 = item.work_time_end.substring(0, 5)\n    var g4 = item.update_time.substring(0, 10)\n    return {\n      $orig: $orig,\n      g2: g2,\n      g3: g3,\n      g4: g4,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n        m19: m19,\n        m20: m20,\n        l6: l6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"search-page\">\r\n    <!-- 搜索头部 -->\r\n    <view class=\"search-header\">\r\n      <view class=\"search-bar\">\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"searchParams.keyword\" \r\n          placeholder=\"搜索职位名称、公司名称\" \r\n          class=\"search-input\"\r\n          @confirm=\"handleSearch\"\r\n        />\r\n        <view class=\"search-btn\" @tap=\"handleSearch\" :style=\"'background:'+t('color1')\">\r\n          <text class=\"search-text\">搜索</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 筛选条件 -->\r\n      <scroll-view scroll-x class=\"filter-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"filter-tags\">\r\n          <view \r\n            class=\"filter-tag\" \r\n            :class=\"{ active: showFilterPanel }\"\r\n            @tap=\"toggleFilterPanel\"\r\n          >\r\n            筛选\r\n            <text class=\"iconfont icon-filter\"></text>\r\n          </view>\r\n          <view \r\n            class=\"filter-tag\" \r\n            :class=\"{ active: currentSort === 'time' }\"\r\n            @tap=\"handleSort('time')\"\r\n          >\r\n            最新\r\n          </view>\r\n          <view \r\n            class=\"filter-tag\" \r\n            :class=\"{ active: currentSort === 'salary' }\"\r\n            @tap=\"handleSort('salary')\"\r\n          >\r\n            薪资\r\n            <text class=\"sort-icon\" :class=\"sortType === 'desc' ? 'desc' : ''\"></text>\r\n          </view>\r\n          <view \r\n            class=\"filter-tag\" \r\n            :class=\"{ active: currentSort === 'views' }\"\r\n            @tap=\"handleSort('views')\"\r\n          >\r\n            热门\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 筛选面板 -->\r\n    <view class=\"filter-panel\" v-if=\"showFilterPanel\">\r\n      <!-- 职位分类 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">职位分类</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in categoryList\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.category_id === item.id }\"\r\n            @tap=\"selectCategory(item)\"\r\n            :style=\"searchParams.category_id === item.id ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item.name }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 职位类型 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">职位类型</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in jobTypes\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.type_id === item.id }\"\r\n            @tap=\"selectJobType(item)\"\r\n            :style=\"searchParams.type_id === item.id ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item.name }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 学历要求 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">学历要求</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in educationList\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.education === item }\"\r\n            @tap=\"selectEducation(item)\"\r\n            :style=\"searchParams.education === item ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 工作经验 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">工作经验</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in experienceList\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.experience === item }\"\r\n            @tap=\"selectExperience(item)\"\r\n            :style=\"searchParams.experience === item ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 薪资范围 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">薪资范围</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in salaryRanges\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.salary_min === item.min && searchParams.salary_max === item.max }\"\r\n            @tap=\"selectSalary(item)\"\r\n            :style=\"searchParams.salary_min === item.min && searchParams.salary_max === item.max ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item.text }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 地区选择 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">地区选择</view>\r\n        <view class=\"area-picker\">\r\n          <picker \r\n            mode=\"region\" \r\n            @change=\"handleRegionChange\" \r\n            :value=\"[searchParams.province, searchParams.city, searchParams.district]\"\r\n          >\r\n            <view class=\"picker-item\">\r\n              {{ searchParams.province || '选择地区' }}\r\n              {{ searchParams.city ? ' - ' + searchParams.city : '' }}\r\n              {{ searchParams.district ? ' - ' + searchParams.district : '' }}\r\n            </view>\r\n          </picker>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 福利标签 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">福利标签</view>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(item, index) in benefitsList\" \r\n            :key=\"index\"\r\n            class=\"tag-item\"\r\n            :class=\"{ active: searchParams.benefits.includes(item) }\"\r\n            @tap=\"toggleBenefit(item)\"\r\n            :style=\"searchParams.benefits.includes(item) ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''\"\r\n          >\r\n            {{ item }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 筛选按钮 -->\r\n      <view class=\"filter-buttons\">\r\n        <button class=\"reset-btn\" @tap=\"resetFilters\">重置</button>\r\n        <button \r\n          class=\"confirm-btn\" \r\n          @tap=\"confirmFilters\"\r\n          :style=\"'background:'+t('color1')\"\r\n        >确定</button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 搜索结果统计 -->\r\n    <view class=\"search-stats\" v-if=\"statistics\">\r\n      <text class=\"stats-text\">共找到 {{ statistics.total }} 个职位</text>\r\n    </view>\r\n\r\n    <!-- 职位列表 -->\r\n    <scroll-view \r\n      scroll-y \r\n      class=\"job-list\"\r\n      @scrolltolower=\"loadMore\"\r\n      refresher-enabled\r\n      :refresher-triggered=\"isRefreshing\"\r\n      @refresherrefresh=\"onRefresh\"\r\n    >\r\n      <view \r\n        v-for=\"(item, index) in jobList\" \r\n        :key=\"index\"\r\n        class=\"job-item\"\r\n        @tap=\"goToDetail(item.id)\"\r\n      >\r\n        <view class=\"job-header\">\r\n          <view class=\"job-title\">{{ item.title }}</view>\r\n          <view class=\"job-salary\" :style=\"'color:'+t('color1')\">{{ item.salary }}元/月</view>\r\n        </view>\r\n        \r\n        <view class=\"job-info\">\r\n          <text class=\"info-item\">\r\n            <text class=\"iconfont icon-location\"></text>\r\n            {{ item.work_address }}\r\n          </text>\r\n          <text class=\"info-item\">\r\n            <text class=\"iconfont icon-education\"></text>\r\n            {{ item.education }}\r\n          </text>\r\n          <text class=\"info-item\">\r\n            <text class=\"iconfont icon-experience\"></text>\r\n            {{ item.experience }}\r\n          </text>\r\n        </view>\r\n        \r\n        <view class=\"company-info\">\r\n          <image class=\"company-logo\" :src=\"item.company_logo\" mode=\"aspectFill\"></image>\r\n          <view class=\"company-detail\">\r\n            <view class=\"company-name\">{{ item.company_name }}</view>\r\n            <view class=\"company-tags\">\r\n              <text class=\"tag\" v-if=\"item.scale\">{{ ['','20人以下','20-99人','100-499人','500-999人','1000-9999人','10000人以上'][item.scale] }}</text>\r\n              <text class=\"tag\" v-if=\"item.nature\">{{ ['','民营企业','外资企业','合资企业','国企'][item.nature] }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"work-info\">\r\n          <text class=\"work-tag\">{{ item.work_mode }}</text>\r\n          <text class=\"work-tag\">{{ item.work_time_type }}</text>\r\n          <text class=\"work-tag\">{{ item.work_time_start.substring(0,5) }}-{{ item.work_time_end.substring(0,5) }}</text>\r\n          <text class=\"work-tag\" v-if=\"item.work_intensity\">{{ item.work_intensity }}</text>\r\n        </view>\r\n\r\n        <view class=\"update-time\">{{ item.update_time.substring(0,10) }} 更新</view>\r\n      </view>\r\n      \r\n      <!-- 加载状态 -->\r\n      <view class=\"loading-status\">\r\n        <view v-if=\"isLoading\" class=\"loading\">加载中...</view>\r\n        <view v-else-if=\"noMore\" class=\"no-more\">没有更多了</view>\r\n      </view>\r\n    </scroll-view>\r\n    <nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      searchParams: {\r\n        keyword: '',\r\n        category_id: '',\r\n        type_id: '',\r\n        education: '',\r\n        experience: '',\r\n        salary_min: '',\r\n        salary_max: '',\r\n        province: '',\r\n        city: '',\r\n        district: '',\r\n        benefits: [],\r\n        sort_field: 'time',\r\n        sort_type: 'desc',\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      showFilterPanel: false,\r\n      currentSort: 'time',\r\n      sortType: 'desc',\r\n      jobList: [],\r\n      statistics: null,\r\n      isLoading: false,\r\n      noMore: false,\r\n      isRefreshing: false,\r\n      \r\n      // 筛选选项\r\n      categoryList: [], // 职位分类列表\r\n      jobTypes: [], // 职位类型列表\r\n      educationList: ['不限', '高中', '专科', '本科', '硕士', '博士'],\r\n      experienceList: ['不限', '应届生', '1-3年', '3-5年', '5-10年', '10年以上'],\r\n      salaryRanges: [\r\n        { min: '', max: '', text: '不限' },\r\n        { min: 0, max: 5000, text: '5K以下' },\r\n        { min: 5000, max: 10000, text: '5-10K' },\r\n        { min: 10000, max: 15000, text: '10-15K' },\r\n        { min: 15000, max: 20000, text: '15-20K' },\r\n        { min: 20000, max: 30000, text: '20-30K' },\r\n        { min: 30000, max: 50000, text: '30-50K' },\r\n        { min: 50000, max: '', text: '50K以上' }\r\n      ],\r\n      benefitsList: ['五险一金', '年终奖', '加班补助', '餐补', '交通补助', '住房补贴', '节日福利', '团建活动']\r\n    }\r\n  },\r\n  computed: {\r\n    topSalaryRange() {\r\n      if (!this.statistics?.salary_distribution) return '';\r\n      const distributions = Object.entries(this.statistics.salary_distribution);\r\n      return distributions.sort((a, b) => b[1] - a[1])[0][0];\r\n    },\r\n    topSalaryPercentage() {\r\n      if (!this.statistics?.salary_distribution) return 0;\r\n      const distributions = Object.values(this.statistics.salary_distribution);\r\n      const total = distributions.reduce((sum, val) => sum + val, 0);\r\n      const max = Math.max(...distributions);\r\n      return Math.round((max / total) * 100);\r\n    },\r\n    topExperience() {\r\n      if (!this.statistics?.experience_distribution) return '';\r\n      const distributions = Object.entries(this.statistics.experience_distribution);\r\n      return distributions.sort((a, b) => b[1] - a[1])[0][0];\r\n    },\r\n    topExperiencePercentage() {\r\n      if (!this.statistics?.experience_distribution) return 0;\r\n      const distributions = Object.values(this.statistics.experience_distribution);\r\n      const total = distributions.reduce((sum, val) => sum + val, 0);\r\n      const max = Math.max(...distributions);\r\n      return Math.round((max / total) * 100);\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 从URL参数中获取关键词\r\n    if (options.keyword) {\r\n      this.searchParams.keyword = decodeURIComponent(options.keyword);\r\n    }\r\n    this.getInitialData();\r\n    this.searchJobs();\r\n  },\r\n  methods: {\r\n    // 获取初始数据\r\n    getInitialData() {\r\n      var that = this;\r\n      app.get('ApiZhaopin/getSearchInitData', {}, function(res) {\r\n        if (res.status === 1) {\r\n          that.categoryList = res.data.categories;\r\n          that.jobTypes = res.data.job_types;\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 搜索职位\r\n    searchJobs(isLoadMore = false) {\r\n      if (this.isLoading) return;\r\n      \r\n      var that = this;\r\n      this.isLoading = true;\r\n      \r\n      if (!isLoadMore) {\r\n        this.searchParams.page = 1;\r\n        this.jobList = [];\r\n        this.noMore = false;\r\n      }\r\n      \r\n      app.post('ApiZhaopin/searchPositions', this.searchParams, function(res) {\r\n        that.isLoading = false;\r\n        that.isRefreshing = false;\r\n        \r\n        if (res.status === 1) {\r\n          if (isLoadMore) {\r\n            that.jobList = [...that.jobList, ...res.data.list];\r\n          } else {\r\n            that.jobList = res.data.list;\r\n          }\r\n          \r\n          that.statistics = res.data.statistics;\r\n          that.noMore = res.data.list.length < that.searchParams.limit;\r\n        } else {\r\n          app.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 处理搜索\r\n    handleSearch() {\r\n      this.showFilterPanel = false;\r\n      this.searchJobs();\r\n    },\r\n    \r\n    // 处理排序\r\n    handleSort(type) {\r\n      if (this.currentSort === type) {\r\n        this.sortType = this.sortType === 'desc' ? 'asc' : 'desc';\r\n      } else {\r\n        this.currentSort = type;\r\n        this.sortType = 'desc';\r\n      }\r\n      \r\n      this.searchParams.sort_field = type;\r\n      this.searchParams.sort_type = this.sortType;\r\n      this.searchJobs();\r\n    },\r\n    \r\n    // 切换筛选面板\r\n    toggleFilterPanel() {\r\n      this.showFilterPanel = !this.showFilterPanel;\r\n    },\r\n    \r\n    // 选择分类\r\n    selectCategory(category) {\r\n      this.searchParams.category_id = this.searchParams.category_id === category.id ? '' : category.id;\r\n    },\r\n    \r\n    // 选择职位类型\r\n    selectJobType(type) {\r\n      this.searchParams.type_id = this.searchParams.type_id === type.id ? '' : type.id;\r\n    },\r\n    \r\n    // 选择学历\r\n    selectEducation(education) {\r\n      this.searchParams.education = this.searchParams.education === education ? '' : education;\r\n    },\r\n    \r\n    // 选择经验\r\n    selectExperience(experience) {\r\n      this.searchParams.experience = this.searchParams.experience === experience ? '' : experience;\r\n    },\r\n    \r\n    // 选择薪资范围\r\n    selectSalary(range) {\r\n      if (this.searchParams.salary_min === range.min && this.searchParams.salary_max === range.max) {\r\n        this.searchParams.salary_min = '';\r\n        this.searchParams.salary_max = '';\r\n      } else {\r\n        this.searchParams.salary_min = range.min;\r\n        this.searchParams.salary_max = range.max;\r\n      }\r\n    },\r\n    \r\n    // 处理地区选择\r\n    handleRegionChange(e) {\r\n      const [province, city, district] = e.detail.value;\r\n      this.searchParams.province = province;\r\n      this.searchParams.city = city;\r\n      this.searchParams.district = district;\r\n    },\r\n    \r\n    // 切换福利标签\r\n    toggleBenefit(benefit) {\r\n      const index = this.searchParams.benefits.indexOf(benefit);\r\n      if (index > -1) {\r\n        this.searchParams.benefits.splice(index, 1);\r\n      } else {\r\n        this.searchParams.benefits.push(benefit);\r\n      }\r\n    },\r\n    \r\n    // 重置筛选\r\n    resetFilters() {\r\n      this.searchParams = {\r\n        ...this.searchParams,\r\n        category_id: '',\r\n        type_id: '',\r\n        education: '',\r\n        experience: '',\r\n        salary_min: '',\r\n        salary_max: '',\r\n        province: '',\r\n        city: '',\r\n        district: '',\r\n        benefits: []\r\n      };\r\n    },\r\n    \r\n    // 确认筛选\r\n    confirmFilters() {\r\n      this.showFilterPanel = false;\r\n      this.searchJobs();\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.isLoading || this.noMore) return;\r\n      this.searchParams.page++;\r\n      this.searchJobs(true);\r\n    },\r\n    \r\n    // 下拉刷新\r\n    onRefresh() {\r\n      this.isRefreshing = true;\r\n      this.searchJobs();\r\n    },\r\n    \r\n    // 跳转到详情页\r\n    goToDetail(id) {\r\n      app.goto('/zhaopin/partdetails?id=' + id);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.search-page {\r\n  min-height: 100vh;\r\n  background-color: #f7f8fa;\r\n  padding-top: 240rpx;\r\n  \r\n  .search-header {\r\n    position: fixed;\r\n    top: var(--window-top, 0);\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 1000;\r\n    background: linear-gradient(to bottom, #ffffff, rgba(255,255,255,0.98));\r\n    padding: 24rpx 30rpx;\r\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n    backdrop-filter: blur(10px);\r\n    \r\n    .search-bar {\r\n      display: flex;\r\n      align-items: center;\r\n      background-color: #ffffff;\r\n      border-radius: 36rpx;\r\n      padding: 0 20rpx;\r\n      margin-bottom: 24rpx;\r\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n      border: 2rpx solid #f0f0f0;\r\n      \r\n      .search-input {\r\n        flex: 1;\r\n        height: 72rpx;\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        letter-spacing: 0.5px;\r\n        \r\n        &::placeholder {\r\n          color: #999;\r\n          font-weight: 300;\r\n        }\r\n      }\r\n      \r\n      .search-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n      }\r\n      \r\n      .search-btn:active {\r\n        opacity: 0.8;\r\n        transform: scale(0.95);\r\n        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n      }\r\n      \r\n      .search-text {\r\n        color: #FFFFFF;\r\n        font-size: 28rpx;\r\n        font-weight: 500;\r\n        line-height: 1;\r\n      }\r\n    }\r\n    \r\n    .filter-scroll {\r\n      white-space: nowrap;\r\n      \r\n      .filter-tags {\r\n        display: inline-flex;\r\n        padding: 12rpx 0;\r\n        \r\n        .filter-tag {\r\n          display: inline-flex;\r\n          align-items: center;\r\n          height: 72rpx;\r\n          padding: 0 28rpx;\r\n          margin-right: 20rpx;\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          background-color: #ffffff;\r\n          border-radius: 36rpx;\r\n          transition: all 0.3s ease;\r\n          font-weight: 500;\r\n          letter-spacing: 0.5px;\r\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);\r\n          \r\n          &:active {\r\n            transform: scale(0.95);\r\n            opacity: 0.8;\r\n          }\r\n          \r\n          &.active {\r\n            color: #1890ff;\r\n            background-color: rgba(24, 144, 255, 0.1);\r\n            font-weight: 600;\r\n            transform: translateY(-2rpx);\r\n            box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.15);\r\n          }\r\n          \r\n          .sort-icon {\r\n            width: 0;\r\n            height: 0;\r\n            margin-left: 10rpx;\r\n            border-left: 8rpx solid transparent;\r\n            border-right: 8rpx solid transparent;\r\n            border-bottom: 10rpx solid #999;\r\n            transition: transform 0.3s ease;\r\n            \r\n            &.desc {\r\n              transform: rotate(180deg);\r\n            }\r\n          }\r\n          \r\n          .icon-filter {\r\n            font-size: 28rpx;\r\n            margin-left: 6rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .filter-panel {\r\n    position: fixed;\r\n    top: 240rpx;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 999;\r\n    background-color: #ffffff;\r\n    padding: 40rpx 30rpx calc(env(safe-area-inset-bottom) + 120rpx);\r\n    overflow-y: auto;\r\n    animation: slideIn 0.3s ease;\r\n    \r\n    .filter-section {\r\n      margin-bottom: 40rpx;\r\n      \r\n      &:first-child {\r\n        padding-top: 20rpx;\r\n      }\r\n      \r\n      .section-title {\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        font-weight: 600;\r\n        margin-bottom: 28rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        &::before {\r\n          content: '';\r\n          width: 6rpx;\r\n          height: 32rpx;\r\n          background: linear-gradient(to bottom, #1890ff, #36b4ff);\r\n          border-radius: 3rpx;\r\n          margin-right: 12rpx;\r\n        }\r\n      }\r\n      \r\n      .tag-list {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 -8rpx;\r\n        \r\n        .tag-item {\r\n          height: 72rpx;\r\n          padding: 0 28rpx;\r\n          margin: 8rpx;\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          background-color: #f7f8fa;\r\n          border: 2rpx solid transparent;\r\n          border-radius: 36rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          transition: all 0.25s ease;\r\n          letter-spacing: 0.5px;\r\n          \r\n          &:active {\r\n            transform: scale(0.95);\r\n            opacity: 0.8;\r\n          }\r\n          \r\n          &.active {\r\n            color: #1890ff;\r\n            background-color: rgba(24, 144, 255, 0.1);\r\n            border-color: rgba(24, 144, 255, 0.3);\r\n            font-weight: 500;\r\n            transform: translateY(-2rpx);\r\n            box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.15);\r\n          }\r\n        }\r\n      }\r\n      \r\n      .area-picker {\r\n        .picker-item {\r\n          height: 88rpx;\r\n          line-height: 88rpx;\r\n          padding: 0 30rpx;\r\n          background-color: #f7f8fa;\r\n          border-radius: 16rpx;\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          position: relative;\r\n          \r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            right: 30rpx;\r\n            top: 50%;\r\n            width: 12rpx;\r\n            height: 12rpx;\r\n            border-right: 2rpx solid #999;\r\n            border-bottom: 2rpx solid #999;\r\n            transform: translateY(-50%) rotate(45deg);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .filter-buttons {\r\n      position: fixed;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));\r\n      background-color: #ffffff;\r\n      box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n      display: flex;\r\n      gap: 20rpx;\r\n      z-index: 100;\r\n      \r\n      button {\r\n        flex: 1;\r\n        height: 92rpx;\r\n        line-height: 92rpx;\r\n        font-size: 32rpx;\r\n        border-radius: 46rpx;\r\n        transition: all 0.3s ease;\r\n        letter-spacing: 1px;\r\n        \r\n        &:active {\r\n          transform: scale(0.98);\r\n          opacity: 0.9;\r\n        }\r\n        \r\n        &.reset-btn {\r\n          background-color: #f7f8fa;\r\n          color: #666;\r\n          border: 2rpx solid #e5e5e5;\r\n          \r\n          &:active {\r\n            background-color: #f0f0f0;\r\n          }\r\n        }\r\n        \r\n        &.confirm-btn {\r\n          background: linear-gradient(135deg, #1890ff, #36b4ff);\r\n          color: #ffffff;\r\n          font-weight: 500;\r\n          box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.25);\r\n          \r\n          &:active {\r\n            box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .search-stats {\r\n    background-color: #ffffff;\r\n    padding: 16rpx 30rpx;\r\n    margin: 0 20rpx 20rpx;\r\n    border-radius: 12rpx;\r\n    \r\n    .stats-text {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n  \r\n  .job-list {\r\n    height: calc(100vh - 240rpx);\r\n    padding: 20rpx;\r\n    \r\n    .job-item {\r\n      background: linear-gradient(to bottom, #ffffff, #fafafa);\r\n      border-radius: 20rpx;\r\n      padding: 28rpx;\r\n      margin-bottom: 24rpx;\r\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n      position: relative;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.98);\r\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n      }\r\n      \r\n      .job-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 20rpx;\r\n        \r\n        .job-title {\r\n          font-size: 34rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n          background: linear-gradient(90deg, #333 60%, #666);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n        }\r\n        \r\n        .job-salary {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.1);\r\n        }\r\n      }\r\n      \r\n      .job-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 20rpx;\r\n        margin-bottom: 20rpx;\r\n        \r\n        .info-item {\r\n          font-size: 26rpx;\r\n          color: #666;\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 4rpx 16rpx;\r\n          background: #f8f9fa;\r\n          border-radius: 6rpx;\r\n          \r\n          .iconfont {\r\n            font-size: 26rpx;\r\n            margin-right: 6rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .company-info {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 24rpx 0;\r\n        padding-bottom: 24rpx;\r\n        border-bottom: 2rpx solid #f5f5f5;\r\n        \r\n        .company-logo {\r\n          width: 88rpx;\r\n          height: 88rpx;\r\n          border-radius: 12rpx;\r\n          margin-right: 16rpx;\r\n          border: 2rpx solid #f0f0f0;\r\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n        }\r\n        \r\n        .company-detail {\r\n          flex: 1;\r\n          \r\n          .company-name {\r\n            font-size: 30rpx;\r\n            color: #333;\r\n            margin-bottom: 8rpx;\r\n            font-weight: 500;\r\n          }\r\n          \r\n          .company-tags {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 8rpx;\r\n            \r\n            .tag {\r\n              font-size: 24rpx;\r\n              color: #666;\r\n              background: #f8f9fa;\r\n              padding: 4rpx 16rpx;\r\n              border-radius: 6rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .work-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 12rpx;\r\n        margin-bottom: 24rpx;\r\n        \r\n        .work-tag {\r\n          font-size: 24rpx;\r\n          color: #1890ff;\r\n          background: rgba(24, 144, 255, 0.06);\r\n          padding: 8rpx 20rpx;\r\n          border-radius: 8rpx;\r\n        }\r\n      }\r\n      \r\n      .update-time {\r\n        position: absolute;\r\n        right: 28rpx;\r\n        bottom: 28rpx;\r\n        font-size: 22rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .loading-status {\r\n      padding: 40rpx 30rpx;\r\n      text-align: center;\r\n      color: #999;\r\n      font-size: 26rpx;\r\n      letter-spacing: 1px;\r\n      \r\n      .loading {\r\n        &::before {\r\n          content: '';\r\n          display: inline-block;\r\n          width: 28rpx;\r\n          height: 28rpx;\r\n          border: 3rpx solid #f0f0f0;\r\n          border-top-color: #666;\r\n          border-radius: 50%;\r\n          margin-right: 8rpx;\r\n          vertical-align: middle;\r\n          animation: loading 0.6s linear infinite;\r\n        }\r\n      }\r\n      \r\n      .no-more {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        \r\n        &::before,\r\n        &::after {\r\n          content: '';\r\n          flex: 1;\r\n          height: 1rpx;\r\n          background: linear-gradient(to right, transparent, #eee, transparent);\r\n          margin: 0 20rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes loading {\r\n  from { transform: rotate(0); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateY(100%);\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n</style>\r\n</rewritten_file>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=style&index=0&id=fadcd368&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./search.vue?vue&type=style&index=0&id=fadcd368&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115060202\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}