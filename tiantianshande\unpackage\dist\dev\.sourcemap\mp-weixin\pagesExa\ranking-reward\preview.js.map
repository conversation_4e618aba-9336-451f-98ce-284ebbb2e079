{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?a263", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?173f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?6017", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?a638", "uni-app:///pagesExa/ranking-reward/preview.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?07ac", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/preview.vue?7473"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "ruleId", "currentPage", "pageSize", "totalPages", "total", "ruleInfo", "id", "name", "reward_mode", "total_reward_rate", "rank_type", "rank_type_text", "rankingList", "myRankInfo", "hasRankInfo", "total_consume", "pool_amount", "selectedDate", "<PERSON><PERSON><PERSON><PERSON>", "selectedPeriodText", "loading", "onLoad", "console", "uni", "title", "icon", "setTimeout", "onPullDownRefresh", "methods", "initData", "callback", "getRulePreview", "app", "rule_id", "period", "pagenum", "pagesize", "that", "getMyRankInfo", "periodChange", "year", "month", "prevPage", "nextPage", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuHhxB;AAAA,eACA;EACAC;IACA;IACA;IACA;IAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;MACAC;MAEAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IACAC;IAEA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;MACAJ;IACA;EACA;EACAK;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;IACA;IAEA;IACAC;MACAT;MAEA;MACA;MAEAU;QACAC;QACAC;QACAC;QACAC;MACA;QACAC;QAEA;UACAf;;UAEA;UACAe;;UAEA;UACAA;;UAEA;UACAA;UACAA;;UAEA;UACAA;UACAA;QACA;UACAf;UACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAa;MACAhB;MAEA;MAEAU;QACAC;QACAC;MACA;QACA;UACAZ;UAEA;YACAe;YACAA;UACA;YACAA;UACA;QACA;UACAf;UACAe;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;MAEA;QAAA;QAAAC;QAAAC;MAEA;MACA;MACA;MACA;MAEAnB;;MAEA;MACA;IACA;IAEA;IACAoB;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjTA;AAAA;AAAA;AAAA;AAAm6C,CAAgB,82CAAG,EAAC,C;;;;;;;;;;;ACAv7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/ranking-reward/preview.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/ranking-reward/preview.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./preview.vue?vue&type=template&id=78e0e846&\"\nvar renderjs\nimport script from \"./preview.vue?vue&type=script&lang=js&\"\nexport * from \"./preview.vue?vue&type=script&lang=js&\"\nimport style0 from \"./preview.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/ranking-reward/preview.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./preview.vue?vue&type=template&id=78e0e846&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.rankingList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./preview.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./preview.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 规则信息 -->\r\n\t\t<view class=\"rule-header\">\r\n\t\t\t<view class=\"rule-name\">{{ruleInfo.name}}</view>\r\n\t\t\t<view class=\"rule-desc\">\r\n\t\t\t\t<text class=\"desc-label\">奖励模式：</text>\r\n\t\t\t\t<text v-if=\"ruleInfo.reward_mode === 1\">根据自身消费</text>\r\n\t\t\t\t<text v-else-if=\"ruleInfo.reward_mode === 2\">根据排名</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rule-desc\">\r\n\t\t\t\t<text class=\"desc-label\">排名类型：</text>\r\n\t\t\t\t<text>{{ruleInfo.rank_type_text}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rule-desc\">\r\n\t\t\t\t<text class=\"desc-label\">奖励比例：</text>\r\n\t\t\t\t<text>{{ruleInfo.total_reward_rate}}%</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 我的排名信息 -->\r\n\t\t<view class=\"my-rank-info\" v-if=\"hasRankInfo\">\r\n\t\t\t<view class=\"rank-title\">我的排名</view>\r\n\t\t\t<view class=\"rank-content\">\r\n\t\t\t\t<view class=\"rank-num-box\">\r\n\t\t\t\t\t<text class=\"rank-num\">{{myRankInfo.rank || '--'}}</text>\r\n\t\t\t\t\t<text class=\"rank-label\">当前排名</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rank-divider\"></view>\r\n\t\t\t\t<view class=\"rank-num-box\">\r\n\t\t\t\t\t<text class=\"rank-num\">{{myRankInfo.count_num || 0}}</text>\r\n\t\t\t\t\t<text class=\"rank-label\">{{ruleInfo.rank_type === 1 ? '下级人数' : '特定等级人数'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rank-divider\"></view>\r\n\t\t\t\t<view class=\"rank-num-box\">\r\n\t\t\t\t\t<text class=\"rank-num reward-color\">{{myRankInfo.reward_amount || '0.00'}}</text>\r\n\t\t\t\t\t<text class=\"rank-label\">预计奖励</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分红池信息 -->\r\n\t\t<view class=\"pool-info\">\r\n\t\t\t<view class=\"pool-flex\">\r\n\t\t\t<!-- \t<view class=\"pool-item\">\r\n\t\t\t\t\t<view class=\"pool-label\">本月消费总额</view>\r\n\t\t\t\t\t<view class=\"pool-value\">¥{{total_consume || '0.00'}}</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"pool-divider\"></view>\r\n\t\t\t\t<view class=\"pool-item\">\r\n\t\t\t\t\t<view class=\"pool-label\">分红池总额</view>\r\n\t\t\t\t\t<view class=\"pool-value reward-color\">¥{{pool_amount || '0.00'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 周期选择器 -->\r\n\t\t<view class=\"period-selector\">\r\n\t\t\t<picker mode=\"date\" fields=\"month\" :value=\"selectedDate\" @change=\"periodChange\">\r\n\t\t\t\t<view class=\"picker-box\">\r\n\t\t\t\t\t<text>{{selectedPeriodText}}</text>\r\n\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\r\n\t\t\t\t</view>\r\n\t\t\t</picker>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 排名列表 -->\r\n\t\t<view class=\"rank-list\">\r\n\t\t\t<view class=\"list-header\">\r\n\t\t\t\t<text class=\"header-item rank-col\">排名</text>\r\n\t\t\t\t<text class=\"header-item member-col\">会员信息</text>\r\n\t\t\t\t<text class=\"header-item count-col\">{{ruleInfo.rank_type === 1 ? '下级人数' : '特定等级人数'}}</text>\r\n\t\t\t<!-- \t<text class=\"header-item rate-col\">奖励比例</text> -->\r\n\t\t\t\t<text class=\"header-item amount-col\">奖励金额</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"no-data\" v-if=\"rankingList.length === 0\">\r\n\t\t\t\t<text>暂无排名数据</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view \r\n\t\t\t\tclass=\"list-item\" \r\n\t\t\t\tv-for=\"(item, index) in rankingList\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t:class=\"{'my-rank': item.is_me}\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"item-col rank-col\">\r\n\t\t\t\t\t<view :class=\"'rank-tag ' + (index < 3 ? 'rank-'+(index+1) : '')\">{{item.rank}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-col member-col\">\r\n\t\t\t\t\t<view class=\"member-info\">\r\n\t\t\t\t\t\t<image class=\"member-avatar\" :src=\"item.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<text class=\"member-name\">{{item.member_info}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-col count-col\">{{item.count_num}}</view>\r\n\t\t\t\t<!-- <view class=\"item-col rate-col\">{{item.reward_rate}}%</view> -->\r\n\t\t\t\t<view class=\"item-col amount-col reward-color\">{{item.reward_amount}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分页器 -->\r\n\t\t<view class=\"pagination\" v-if=\"totalPages > 1\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"page-btn prev-btn\" \r\n\t\t\t\t:class=\"{'disabled': currentPage <= 1}\"\r\n\t\t\t\t@click=\"prevPage\"\r\n\t\t\t>上一页</view>\r\n\t\t\t<view class=\"page-info\">{{currentPage}}/{{totalPages}}</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"page-btn next-btn\" \r\n\t\t\t\t:class=\"{'disabled': currentPage >= totalPages}\"\r\n\t\t\t\t@click=\"nextPage\"\r\n\t\t\t>下一页</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\tconst now = new Date();\r\n\t\t\tconst year = now.getFullYear();\r\n\t\t\tconst month = (now.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\truleId: null,\r\n\t\t\t\tcurrentPage: 1,\r\n\t\t\t\tpageSize: 20,\r\n\t\t\t\ttotalPages: 1,\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\t\r\n\t\t\t\truleInfo: {\r\n\t\t\t\t\tid: 0,\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\treward_mode: 2,\r\n\t\t\t\t\ttotal_reward_rate: 0,\r\n\t\t\t\t\trank_type: 1,\r\n\t\t\t\t\trank_type_text: ''\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\trankingList: [],\r\n\t\t\t\tmyRankInfo: {},\r\n\t\t\t\thasRankInfo: false,\r\n\t\t\t\t\r\n\t\t\t\ttotal_consume: '0.00',\r\n\t\t\t\tpool_amount: '0.00',\r\n\t\t\t\t\r\n\t\t\t\tselectedDate: `${year}-${month}`,\r\n\t\t\t\tselectedPeriod: `${year}${month}`,\r\n\t\t\t\tselectedPeriodText: `${year}年${month}月`,\r\n\t\t\t\t\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/preview][onLoad_001] 页面加载，参数：`, options);\r\n\t\t\t\r\n\t\t\tif (options.rule_id) {\r\n\t\t\t\tthis.ruleId = parseInt(options.rule_id);\r\n\t\t\t\tthis.initData();\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '参数错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.initData(() => {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitData(callback) {\r\n\t\t\t\t// 获取规则预览信息\r\n\t\t\t\tthis.getRulePreview();\r\n\t\t\t\t// 获取我的排名信息\r\n\t\t\t\tthis.getMyRankInfo();\r\n\t\t\t\t\r\n\t\t\t\tif (callback && typeof callback === 'function') {\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取规则预览信息\r\n\t\t\tgetRulePreview() {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/preview][getRulePreview_001] 开始获取规则预览，规则ID：${this.ruleId}，周期：${this.selectedPeriod}`);\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/previewRanking', {\r\n\t\t\t\t\trule_id: this.ruleId,\r\n\t\t\t\t\tperiod: this.selectedPeriod,\r\n\t\t\t\t\tpagenum: this.currentPage,\r\n\t\t\t\t\tpagesize: this.pageSize\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/preview][getRulePreview_002] 获取规则预览成功`);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 规则信息\r\n\t\t\t\t\t\tthat.ruleInfo = res.data.rule_info;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 排名列表\r\n\t\t\t\t\t\tthat.rankingList = res.data.ranking_list;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 分页信息\r\n\t\t\t\t\t\tthat.total = res.data.total;\r\n\t\t\t\t\t\tthat.totalPages = Math.ceil(that.total / that.pageSize);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 消费金额和分红池金额\r\n\t\t\t\t\t\tthat.total_consume = res.data.total_consume;\r\n\t\t\t\t\t\tthat.pool_amount = res.data.pool_amount;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/preview][getRulePreview_003] 获取规则预览失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取规则预览失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取我的排名信息\r\n\t\t\tgetMyRankInfo() {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/preview][getMyRankInfo_001] 开始获取我的排名信息，规则ID：${this.ruleId}，周期：${this.selectedPeriod}`);\r\n\t\t\t\t\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getMyRankInfo', {\r\n\t\t\t\t\trule_id: this.ruleId,\r\n\t\t\t\t\tperiod: this.selectedPeriod\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/preview][getMyRankInfo_002] 获取我的排名信息成功`);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (res.data && res.data.rank) {\r\n\t\t\t\t\t\t\tthat.myRankInfo = res.data;\r\n\t\t\t\t\t\t\tthat.hasRankInfo = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.hasRankInfo = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/preview][getMyRankInfo_003] 获取我的排名信息失败：${res.msg}`);\r\n\t\t\t\t\t\tthat.hasRankInfo = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 周期选择器变更\r\n\t\t\tperiodChange(e) {\r\n\t\t\t\tconst val = e.detail.value;\r\n\t\t\t\tif (!val) return;\r\n\t\t\t\t\r\n\t\t\t\tconst [year, month] = val.split('-');\r\n\t\t\t\t\r\n\t\t\t\tthis.selectedDate = val;\r\n\t\t\t\tthis.selectedPeriod = `${year}${month}`;\r\n\t\t\t\tthis.selectedPeriodText = `${year}年${month}月`;\r\n\t\t\t\tthis.currentPage = 1;\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/preview][periodChange_001] 周期变更：${this.selectedPeriod}`);\r\n\t\t\t\t\r\n\t\t\t\t// 重新加载数据\r\n\t\t\t\tthis.initData();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上一页\r\n\t\t\tprevPage() {\r\n\t\t\t\tif (this.currentPage <= 1) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.currentPage--;\r\n\t\t\t\tthis.getRulePreview();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 下一页\r\n\t\t\tnextPage() {\r\n\t\t\t\tif (this.currentPage >= this.totalPages) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.currentPage++;\r\n\t\t\t\tthis.getRulePreview();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间，用于日志\r\n\t\t\tformatTime() {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\tconst seconds = date.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\tconst milliseconds = date.getMilliseconds().toString().padStart(3, '0');\r\n\t\t\t\t\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tpadding: 30rpx 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.reward-color {\r\n\tcolor: #FF6600 !important;\r\n}\r\n\r\n// 规则信息\r\n.rule-header {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.rule-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.rule-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n\tdisplay: flex;\r\n}\r\n\r\n.desc-label {\r\n\twidth: 160rpx;\r\n}\r\n\r\n// 我的排名信息\r\n.my-rank-info {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.rank-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.rank-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\talign-items: center;\r\n}\r\n\r\n.rank-num-box {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.rank-num {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.rank-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.rank-divider {\r\n\twidth: 2rpx;\r\n\theight: 80rpx;\r\n\tbackground-color: #EEEEEE;\r\n}\r\n\r\n// 分红池信息\r\n.pool-info {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.pool-flex {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\talign-items: center;\r\n}\r\n\r\n.pool-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.pool-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.pool-value {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.pool-divider {\r\n\twidth: 2rpx;\r\n\theight: 60rpx;\r\n\tbackground-color: #EEEEEE;\r\n}\r\n\r\n// 周期选择器\r\n.period-selector {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.picker-box {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.picker-arrow {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n// 排名列表\r\n.rank-list {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n\toverflow: hidden;\r\n}\r\n\r\n.list-header {\r\n\tdisplay: flex;\r\n\tbackground-color: #F7F7F7;\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.header-item {\r\n\ttext-align: center;\r\n}\r\n\r\n.list-item {\r\n\tdisplay: flex;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.list-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.my-rank {\r\n\tbackground-color: rgba(255, 102, 0, 0.05);\r\n}\r\n\r\n.item-col {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.rank-col {\r\n\twidth: 15%;\r\n}\r\n\r\n.member-col {\r\n\twidth: 35%;\r\n}\r\n\r\n.count-col {\r\n\twidth: 15%;\r\n}\r\n\r\n.rate-col {\r\n\twidth: 15%;\r\n}\r\n\r\n.amount-col {\r\n\twidth: 20%;\r\n}\r\n\r\n.rank-tag {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #F0F0F0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.rank-1 {\r\n\tbackground-color: #FFD700;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.rank-2 {\r\n\tbackground-color: #C0C0C0;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.rank-3 {\r\n\tbackground-color: #CD7F32;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.member-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-start;\r\n\tpadding: 0 10rpx;\r\n}\r\n\r\n.member-avatar {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 30rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.member-name {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tmax-width: 180rpx;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n// 分页器\r\n.pagination {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tmargin-top: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.page-btn {\r\n\tbackground-color: #FFFFFF;\r\n\tborder: 1rpx solid #DDDDDD;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 15rpx 30rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.disabled {\r\n\tcolor: #CCCCCC;\r\n\tbackground-color: #F5F5F5;\r\n}\r\n\r\n.page-info {\r\n\tmargin: 0 30rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n// 无数据提示\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 50rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./preview.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./preview.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115083999\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}