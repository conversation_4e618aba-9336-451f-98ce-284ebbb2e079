{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?a36c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?dce0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?2e78", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?4dc9", "uni-app:///pagesExb/message/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?7867", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/detail.vue?7d15"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "detail", "datalist", "pagenum", "id", "pagecontent", "title", "sharepic", "nodata", "nomore", "iszan", "plcount", "is_read", "read_time", "onLoad", "console", "onPullDownRefresh", "onShareAppMessage", "desc", "pic", "callback", "that", "onShareTimeline", "imageUrl", "query", "onReachBottom", "methods", "sharecallback", "app", "getdata", "uni", "getReadScore", "<PERSON><PERSON><PERSON><PERSON>", "zan", "pzan", "shareMessage", "provider", "scene", "type", "href", "summary", "success", "fail", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoE/wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACAD;IACA;EACA;EACAE;IACA;IACA;MAAAX;MAAAY;MAAAC;MAAAC;QACAC;MACA;IAAA;EACA;EACAC;IACA;IACA;MAAAhB;MAAAY;MAAAC;MACAC;QACAC;MACA;IAAA;IACA;IACA;MACAf;MACAiB;MACAC;IACA;EACA;EACAC;IACAV;IACA;MACA;MACA;IACA;EACA;EACAW;IACAC;MACAC;QACA;UACAA;QACA;MACA;IACA;IACAC;MACAd;MACA;MACA;MACAM;MACAO;QAAAxB;MAAA;QACAW;QACAM;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAS;YACAxB;UACA;UACA;UACAe;UACA;UACAA;UACAA;UAEAA;UACAA;UACAA;UACAA;YAAAf;YAAAY;YAAAC;UAAA;QACA;UACA;UACA;YACAS;UACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACAH;QAAAxB;MAAA;QACA;UACAwB;QACA;MACA;IACA;IACAI;MACA;MACA;MACAX;MACAA;MACAA;MACAO;QAAAzB;QAAAC;MAAA;QACAiB;QACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAY;MACA;MACA;MACAL;QAAAxB;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QACAiB;QACAA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACAN;QAAAxB;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QAEAF;QACAA;QACAmB;MACA;IACA;IACAc;MACA;MACAL;QACAM;QACAC;QACAC;QACAC;QACAjC;QACAkC;QACAjB;QACAkB;UACApB;QACA;QACAqB;UACA3B;QACA;MACA;IACA;IACA4B;MACA;MACA;MACAb;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/message/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/message/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=47cda49f&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/message/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=47cda49f&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.detail.canpl == 1\n      ? _vm.__map(_vm.datalist, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = item.replylist.length\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<text class=\"title\" v-if=\"detail.showname==1\">{{detail.name}}</text>\r\n\t\t\t\t<view class=\"msginfo\" v-if=\"detail.showsendtime==1 || detail.showauthor==1 || detail.showreadcount==1\">\r\n\t\t\t\t\t<text class=\"t1\" v-if=\"detail.showsendtime==1\">{{detail.createtime}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.showauthor==1\">{{detail.author}}</text>\r\n\t\t\t\t\t<text class=\"t3\" v-if=\"detail.showreadcount==1\">阅读：{{detail.readcount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"padding:8rpx 0\">\r\n\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<!--评论-->\r\n\t\t\t<block v-if=\"detail.canpl==1\">\r\n\t\t\t<view class=\"plbox\">\r\n\t\t\t\t<view class=\"plbox_title\"><text class=\"t1\">评论</text><text>({{plcount}})</text></view>\r\n\t\t\t\t<view class=\"plbox_content\">\r\n\t\t\t\t\t<block v-for=\"(item, idx) in datalist\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"item1 flex\">\r\n\t\t\t\t\t\t<view class=\"f1 flex0\"><image :src=\"item.headimg\"></image></view>\r\n\t\t\t\t\t\t<view class=\"f2 flex-col\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2 plcontent\"><parse :content=\"item.content\" /></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.replylist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"relist\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(hfitem, index) in item.replylist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"item2\">\r\n\t\t\t\t\t\t\t\t\t<view>{{hfitem.nickname}}：</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2 plcontent\"><parse :content=\"hfitem.content\" /></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class=\"t3 flex\">\r\n\t\t\t\t\t\t\t\t<text>{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"><text v-if=\"detail.canplrp==1\" class=\"phuifu\" style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"'pinglun?type=1&id=' + detail.id + '&hfid=' + item.id\">回复</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center pzan\" @tap=\"pzan\" :data-id=\"item.id\" :data-index=\"idx\"><image :src=\"'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'\"></image>{{item.zan}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height:160rpx\"></view>\r\n\t\t\t<view class=\"pinglun notabbarbot\">\r\n\t\t\t\t<view class=\"pinput\" @tap=\"goto\" :data-url=\"'pinglun?type=0&id=' + detail.id\">发表评论</view>\r\n\t\t\t\t<view class=\"zan flex-y-center\" @tap=\"zan\" :data-id=\"detail.id\">\r\n\t\t\t\t\t<image :src=\"'/static/img/zan-' + (iszan?'2':'1') + '.png'\"/><text style=\"padding-left:2px\">{{detail.zan}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share flex-y-center\" @tap=\"shareMessage\">\r\n\t\t\t\t\t<image src=\"/static/img/share.png\"/><text style=\"padding-left:2px\">分享</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n            opt:{},\r\n            loading:true,\r\n            isload: false,\r\n            menuindex:-1,\r\n            pre_url:app.globalData.pre_url,\r\n\r\n            detail:[],\r\n            datalist: [],\r\n            pagenum: 1,\r\n            id: 0,\r\n            pagecontent: \"\",\r\n            title: \"\",\r\n            sharepic: \"\",\r\n            nodata:false,\r\n            nomore:false,\r\n            iszan: \"\",\r\n\t\t\tplcount:0,\r\n\t\t\tis_read: false,\r\n\t\t\tread_time: ''\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tconsole.log('2025-01-03 23:20:01,001-INFO-[detail][onLoad_001] 页面加载开始，id=' + opt.id);\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.id = opt.id || 0;\r\n\t\tif (this.id) {\r\n\t\t\tthis.getdata();\r\n\t\t}\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tconsole.log('2025-01-03 23:20:01,002-INFO-[detail][onPullDownRefresh_001] 下拉刷新开始');\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\tvar that = this;\r\n\t\treturn this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,callback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar that = this;\r\n\t\tvar sharewxdata = this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,\r\n\t\t\tcallback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  onReachBottom: function () {\r\n    console.log('2025-01-03 23:20:01,003-INFO-[detail][onReachBottom_001] 触底加载更多评论');\r\n    if (!this.nodata && !this.nomore && this.detail.canpl==1) {\r\n      this.pagenum = this.pagenum + 1\r\n      this.getpllist();\r\n    }\r\n  },\r\n  methods: {\r\n\t  sharecallback: function() {\r\n\t  \tapp.post(\"ApiMessageNotify/giveScorenum\",{}, function(res) {\r\n\t  \t\tif(res.code == 1) {\r\n\t  \t\t\tapp.success(res.msg);\r\n\t  \t\t}\r\n\t  \t});\t\r\n\t  },\r\n\t\tgetdata:function(){\r\n\t\t\tconsole.log('2025-01-03 23:20:01,004-INFO-[detail][getdata_001] 开始获取消息详情，id=' + this.id);\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiMessageNotify/detail', {id: id}, function (res) {\r\n\t\t\t\tconsole.log('2025-01-03 23:20:01,005-INFO-[detail][getdata_002] API响应：' + JSON.stringify(res));\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res && res.code == 1){\r\n\t\t\t\t\tthat.detail = res.data || {};\r\n\t\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\t\tthat.plcount = res.plcount;\r\n\t\t\t\t\tthat.iszan = res.iszan;\r\n\t\t\t\t\tthat.title = res.detail.name;\r\n\t\t\t\t\tthat.sharepic = res.detail.pic;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.detail.name\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 自动获取阅读积分\r\n\t\t\t\t\tthat.getReadScore();\r\n\t\t\t\t\t// 更新已读状态信息\r\n\t\t\t\t\tthat.is_read = res.data.is_read || false;\r\n\t\t\t\t\tthat.read_time = res.data.read_time_text || '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\t\tthat.datalist = [];\r\n\t\t\t\t\tthat.getpllist();\r\n\t\t\t\t\tthat.loaded({title:res.detail.name,desc:res.detail.subname,pic:res.detail.pic});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 只在有错误消息时才显示alert\r\n\t\t\t\t\tif (res && res.msg) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.alert('获取消息详情失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetReadScore: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post(\"ApiMessageNotify/giveScore\", {id: that.opt.id}, function(res) {\r\n\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    getpllist: function () {\r\n        var that = this;\r\n        var pagenum = that.pagenum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n        app.post('ApiMessageNotify/getpllist', {pagenum: pagenum,id: that.detail.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n            var data = res.data;\r\n            if (data.length == 0) {\r\n                if(pagenum == 1){\r\n                    that.nodata = true;\r\n                }else{\r\n                    that.nomore = true;\r\n                }\r\n            }\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n        });\r\n    },\r\n    zan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.post(\"ApiMessageNotify/zan\", {id: id}, function (res) {\r\n        if (res.type == 0) {\r\n          //取消点赞\r\n          var iszan = 0;\r\n        } else {\r\n          var iszan = 1;\r\n        }\r\n        that.iszan = iszan;\r\n        that.detail.zan = res.zancount;\r\n      });\r\n    },\r\n    pzan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var index = e.currentTarget.dataset.index;\r\n      var datalist = that.datalist;\r\n      app.post(\"ApiMessageNotify/pzan\", {id: id}, function (res) {\r\n        if (res.type == 0) {\r\n          //取消点赞\r\n          var iszan = 0;\r\n        } else {\r\n          var iszan = 1;\r\n        }\r\n\r\n        datalist[index].iszan = iszan;\r\n        datalist[index].zan = res.zancount;\r\n        that.datalist = datalist;\r\n      });\r\n    },\r\n    shareMessage: function() {\r\n    \tvar that = this;\r\n    \tuni.share({\r\n    \t\tprovider: \"weixin\",\r\n    \t\tscene: \"WXSceneSession\",\r\n    \t\ttype: 0,\r\n    \t\thref: \"\",\r\n    \t\ttitle: that.detail.name,\r\n    \t\tsummary: that.detail.subname,\r\n    \t\timageUrl: that.detail.pic,\r\n    \t\tsuccess: function (res) {\r\n    \t\t\tthat.sharecallback();\r\n    \t\t},\r\n    \t\tfail: function (err) {\r\n    \t\t\tconsole.log(\"分享失败：\" + JSON.stringify(err));\r\n    \t\t}\r\n    \t});\r\n    },\r\n    goto: function(e) {\r\n    \tvar that = this;\r\n    \tvar url = e.currentTarget.dataset.url;\r\n    \tuni.navigateTo({\r\n    \t\turl: url\r\n    \t});\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.header{ background-color: #fff;padding: 10rpx 20rpx 0 20rpx;position: relative;display:flex;flex-direction:column;}\r\n.header .title{width:100%;font-size: 36rpx;color:#333;line-height: 1.4;margin:10rpx 0;margin-top:20rpx;font-weight:bold}\r\n.header .msginfo{width:100%;font-size:28rpx;color: #8c8c8c;font-style: normal;overflow: hidden;display:flex;margin:10rpx 0;}\r\n.header .msginfo .t1{padding-right:8rpx}\r\n.header .msginfo .t2{color:#777;padding-right:8rpx}\r\n.header .msginfo .t3{text-align:right;flex:1;}\r\n.header .subname{width:100%;font-size:28rpx;color: #888;border:1px dotted #ddd;border-radius:10rpx;margin:10rpx 0;padding:10rpx}\r\n\r\n.pinglun{ width:96%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:10;border-top:1px solid #f7f7f7;padding:0 2%;box-sizing:content-box}\r\n.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}\r\n.pinglun .zan{padding:0 12rpx;line-height:100rpx}\r\n.pinglun .zan image{width:48rpx;height:48rpx}\r\n.pinglun .zan span{height:40rpx;line-height:50rpx;font-size:32rpx}\r\n.pinglun .share{padding:0 12rpx;line-height:100rpx}\r\n.pinglun .share image{width:48rpx;height:48rpx}\r\n\r\n.plbox{width:100%;padding:40rpx 20rpx;background:#fff;margin-top:10px}\r\n.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx}\r\n.plbox_title .t1{color:#000;font-weight:bold}\r\n.plbox_content .plcontent{vertical-align: middle;color:#111}\r\n.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}\r\n.plbox_content .item1{width:100%;margin-bottom:20rpx}\r\n.plbox_content .item1 .f1{width:80rpx;}\r\n.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}\r\n.plbox_content .item1 .f2{flex:1}\r\n.plbox_content .item1 .f2 .t1{}\r\n.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}\r\n.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}\r\n.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:2px}\r\n.plbox_content .item1 .f2 .phuifu{margin-left:6px;color:#507DAF}\r\n.plbox_content .relist{width:100%;background:#f5f5f5;padding:4rpx 20rpx;margin-bottom:20rpx}\r\n.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}\r\n\r\n.copyright{display:none}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024237\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}