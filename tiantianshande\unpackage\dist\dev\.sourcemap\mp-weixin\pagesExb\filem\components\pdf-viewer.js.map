{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?1a1a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?7628", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?7765", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?ce84", "uni-app:///pagesExb/filem/components/pdf-viewer.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?d557", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/components/pdf-viewer.vue?90cb"], "names": ["pdfjsLib", "console", "name", "props", "url", "type", "required", "data", "loading", "error", "pdfDoc", "currentPage", "totalPages", "canvasWidth", "canvasHeight", "scale", "canvasContext", "useIframe", "mounted", "query", "fields", "node", "size", "exec", "methods", "loadPdf", "withCredentials", "loadingTask", "then", "catch", "renderPage", "viewport", "page", "prevPage", "nextPage"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBnxB;AACA;AACA;EACAA;AACA;EACAC;EACAD;AACA;AAAA,eAEA;EACAE;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAjB;;IASA;IACA;MACA;MACA;MACAA;MACA;IACA;IAEA;MACA;MACA;QACAD;MACA;;MAEA;MACA;MACAmB,4BACAC;QAAAC;QAAAC;MAAA,GACAC;QACA;UACA;UACA;UACA;QACA;;QAEA;;QAaA;UACA;UACA;QACA;UACAtB;UACA;UACA;UACA;QACA;;QAGA;QACA;MACA;IACA;MACAA;MACA;MACA;IACA;EACA;EACAuB;IACA;IACAC;MAAA;MACA;MACA;MAEAxB;MAEA;QACA;QACA;UACAG;UACAsB;QACA;QAEA;QAEA;UACAC,oBACAC;YACA3B;YACA;YACA;YACA;;YAEA;YACA;UACA,GACA4B;YACA;YACA;YACA5B;;YAEA;UAIA;QACA;UACA;QACA;MACA;QACAA;QACA;QACA;;QAEA;MAIA;IACA;IAEA;IA+BA;IACA6B;MAAA;MACA;MAEA;MAEA;QACA,gCACAF;UACA;UACA;YAAAb;UAAA;UACA;UACA;;UAEA;;UASA;UACA;YACAC;YACAe;UACA;UAEAC,mCACAJ;YACA;YACA;;YAEA;;YAEA;cACA;YACA;UAEA,GACAC;YACA;YACA;YACA5B;UACA;QACA,GACA4B;UACA;UACA;UACA5B;QACA;MACA;QACA;QACA;QACAA;MACA;IACA;IAEA;IACAgC;MACA;MAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/filem/components/pdf-viewer.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./pdf-viewer.vue?vue&type=template&id=2d5b34f2&\"\nvar renderjs\nimport script from \"./pdf-viewer.vue?vue&type=script&lang=js&\"\nexport * from \"./pdf-viewer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pdf-viewer.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/filem/components/pdf-viewer.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer.vue?vue&type=template&id=2d5b34f2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"pdf-container\">\r\n    <view v-if=\"loading\" class=\"loading\">\r\n      <text>PDF文件加载中...</text>\r\n    </view>\r\n    <view v-else-if=\"error\" class=\"error\">\r\n      <text>{{ error }}</text>\r\n    </view>\r\n    <view v-else class=\"pdf-viewer\">\r\n      <view class=\"pdf-controls\">\r\n        <view class=\"page-info\">{{ currentPage }} / {{ totalPages }}</view>\r\n        <view class=\"pdf-buttons\">\r\n          <button class=\"btn\" :disabled=\"currentPage <= 1\" @click=\"prevPage\">上一页</button>\r\n          <button class=\"btn\" :disabled=\"currentPage >= totalPages\" @click=\"nextPage\">下一页</button>\r\n        </view>\r\n      </view>\r\n      <scroll-view class=\"pdf-content\" scroll-y>\r\n        <canvas class=\"pdf-canvas\" canvas-id=\"pdf-canvas\" :style=\"{ width: canvasWidth + 'px', height: canvasHeight + 'px' }\"></canvas>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n// 导入PDF.js库，使用try-catch处理导入失败的情况\r\nlet pdfjsLib;\r\ntry {\r\n  pdfjsLib = require('../pdfjs/pdf.min.js');\r\n} catch (error) {\r\n  console.error('PDF.js导入失败:', error);\r\n  pdfjsLib = null;\r\n}\r\n\r\nexport default {\r\n  name: 'PdfViewer',\r\n  props: {\r\n    url: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      error: null,\r\n      pdfDoc: null,\r\n      currentPage: 1,\r\n      totalPages: 0,\r\n      canvasWidth: 0,\r\n      canvasHeight: 0,\r\n      scale: 1.0,\r\n      canvasContext: null,\r\n      useIframe: false  // 是否使用iframe备选方案\r\n    };\r\n  },\r\n  mounted() {\r\n    console.log('PDF预览组件加载，URL:', this.url);\r\n    \r\n    // #ifdef H5\r\n    // 在H5环境下，优先使用iframe显示PDF\r\n    this.useIframe = true;\r\n    this.tryIframeRender();\r\n    return;\r\n    // #endif\r\n    \r\n    // 如果PDF.js库不可用，显示错误信息\r\n    if (!pdfjsLib) {\r\n      this.loading = false;\r\n      this.error = '未能加载PDF.js库，无法渲染PDF';\r\n      console.error('PDF.js库不可用');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      // 设置PDF.js工作线程路径\r\n      if (pdfjsLib.GlobalWorkerOptions) {\r\n        pdfjsLib.GlobalWorkerOptions.workerSrc = '../pdfjs/pdf.worker.min.js';\r\n      }\r\n      \r\n      // 初始化Canvas上下文\r\n      const query = uni.createSelectorQuery().in(this);\r\n      query.select('.pdf-canvas')\r\n        .fields({ node: true, size: true })\r\n        .exec((res) => {\r\n          if (!res || !res[0]) {\r\n            this.error = 'Canvas元素未找到';\r\n            this.loading = false;\r\n            return;\r\n          }\r\n          \r\n          // 仅在支持的平台上进行Canvas操作\r\n          // #ifdef APP-PLUS || H5\r\n          try {\r\n            this.canvasContext = res[0].node.getContext('2d');\r\n          } catch (error) {\r\n            console.error('获取Canvas上下文失败:', error);\r\n            this.error = '无法初始化PDF渲染环境';\r\n            this.loading = false;\r\n            return;\r\n          }\r\n          // #endif\r\n          \r\n          // #ifdef MP-WEIXIN\r\n          try {\r\n            const canvas = uni.createCanvasContext('pdf-canvas', this);\r\n            this.canvasContext = canvas;\r\n          } catch (error) {\r\n            console.error('创建Canvas上下文失败:', error);\r\n            this.error = '无法初始化PDF渲染环境';\r\n            this.loading = false;\r\n            return;\r\n          }\r\n          // #endif\r\n          \r\n          // 加载PDF文档\r\n          this.loadPdf();\r\n        });\r\n    } catch (error) {\r\n      console.error('PDF预览组件初始化失败:', error);\r\n      this.loading = false;\r\n      this.error = `PDF预览初始化失败: ${error.message}`;\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载PDF文档\r\n    loadPdf() {\r\n      this.loading = true;\r\n      this.error = null;\r\n      \r\n      console.log('开始加载PDF文件:', this.url);\r\n      \r\n      try {\r\n        // 创建加载任务选项，增加对CORS的支持\r\n        const loadingOptions = {\r\n          url: this.url,\r\n          withCredentials: true\r\n        };\r\n        \r\n        const loadingTask = pdfjsLib.getDocument(loadingOptions);\r\n        \r\n        if (loadingTask && loadingTask.promise) {\r\n          loadingTask.promise\r\n            .then(pdfDoc => {\r\n              console.log('PDF加载成功，总页数:', pdfDoc.numPages);\r\n              this.pdfDoc = pdfDoc;\r\n              this.totalPages = pdfDoc.numPages;\r\n              this.loading = false;\r\n              \r\n              // 渲染第一页\r\n              this.renderPage(this.currentPage);\r\n            })\r\n            .catch(error => {\r\n              this.loading = false;\r\n              this.error = `无法加载PDF文件: ${error.message}`;\r\n              console.error('PDF加载错误:', error);\r\n              \r\n              // 尝试退回到iframe渲染方式（仅在H5环境）\r\n              // #ifdef H5\r\n              this.tryIframeRender();\r\n              // #endif\r\n            });\r\n        } else {\r\n          throw new Error('PDF加载任务创建失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载PDF文件失败:', error);\r\n        this.loading = false;\r\n        this.error = `PDF加载失败: ${error.message}`;\r\n        \r\n        // 尝试退回到iframe渲染方式（仅在H5环境）\r\n        // #ifdef H5\r\n        this.tryIframeRender();\r\n        // #endif\r\n      }\r\n    },\r\n    \r\n    // H5环境下的备选方案：使用iframe直接显示PDF\r\n    // #ifdef H5\r\n    tryIframeRender() {\r\n      console.log('使用iframe渲染PDF:', this.url);\r\n      this.useIframe = true;\r\n      this.loading = false;\r\n      this.error = null;\r\n      \r\n      this.$nextTick(() => {\r\n        const container = document.querySelector('.pdf-container');\r\n        if (container) {\r\n          // 清空原有内容\r\n          while (container.firstChild) {\r\n            container.removeChild(container.firstChild);\r\n          }\r\n          \r\n          // 创建iframe元素\r\n          const iframe = document.createElement('iframe');\r\n          iframe.src = this.url;\r\n          iframe.style.width = '100%';\r\n          iframe.style.height = '800px';\r\n          iframe.style.border = 'none';\r\n          container.appendChild(iframe);\r\n        } else {\r\n          console.error('未找到PDF容器元素');\r\n          this.error = '未找到PDF容器元素';\r\n        }\r\n      });\r\n    },\r\n    // #endif\r\n    \r\n    // 渲染指定页面\r\n    renderPage(pageNumber) {\r\n      if (this.useIframe) return;\r\n      \r\n      this.loading = true;\r\n      \r\n      try {\r\n        this.pdfDoc.getPage(pageNumber)\r\n          .then(page => {\r\n            // 调整Canvas大小以适应PDF页面\r\n            const viewport = page.getViewport({ scale: this.scale });\r\n            this.canvasWidth = viewport.width;\r\n            this.canvasHeight = viewport.height;\r\n            \r\n            // 设置Canvas大小\r\n            // #ifdef APP-PLUS || H5\r\n            if (this.canvasContext && this.canvasContext.canvas) {\r\n              const canvas = this.canvasContext.canvas;\r\n              canvas.width = viewport.width;\r\n              canvas.height = viewport.height;\r\n            }\r\n            // #endif\r\n            \r\n            // 渲染PDF页面\r\n            const renderContext = {\r\n              canvasContext: this.canvasContext,\r\n              viewport: viewport\r\n            };\r\n            \r\n            page.render(renderContext).promise\r\n              .then(() => {\r\n                this.loading = false;\r\n                this.currentPage = pageNumber;\r\n                \r\n                // 小程序平台需要特殊处理\r\n                // #ifdef MP-WEIXIN\r\n                if (this.canvasContext && this.canvasContext.draw) {\r\n                  this.canvasContext.draw();\r\n                }\r\n                // #endif\r\n              })\r\n              .catch(error => {\r\n                this.loading = false;\r\n                this.error = `无法渲染PDF页面: ${error.message}`;\r\n                console.error('PDF渲染错误:', error);\r\n              });\r\n          })\r\n          .catch(error => {\r\n            this.loading = false;\r\n            this.error = `无法获取PDF页面: ${error.message}`;\r\n            console.error('获取PDF页面错误:', error);\r\n          });\r\n      } catch (error) {\r\n        this.loading = false;\r\n        this.error = `渲染PDF页面失败: ${error.message}`;\r\n        console.error('渲染PDF页面异常:', error);\r\n      }\r\n    },\r\n    \r\n    // 上一页\r\n    prevPage() {\r\n      if (this.useIframe) return;\r\n      \r\n      if (this.currentPage > 1) {\r\n        this.renderPage(this.currentPage - 1);\r\n      }\r\n    },\r\n    \r\n    // 下一页\r\n    nextPage() {\r\n      if (this.useIframe) return;\r\n      \r\n      if (this.currentPage < this.totalPages) {\r\n        this.renderPage(this.currentPage + 1);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.pdf-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.loading, .error {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.pdf-viewer {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.pdf-controls {\r\n  height: 80rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20rpx;\r\n  background-color: #f5f5f5;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.page-info {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n.pdf-buttons {\r\n  display: flex;\r\n}\r\n\r\n.btn {\r\n  font-size: 24rpx;\r\n  padding: 4rpx 20rpx;\r\n  margin: 0 10rpx;\r\n  background-color: #007AFF;\r\n  color: white;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.btn[disabled] {\r\n  background-color: #cccccc;\r\n  color: #999999;\r\n}\r\n\r\n.pdf-content {\r\n  flex: 1;\r\n  width: 100%;\r\n  background-color: #f9f9f9;\r\n  overflow: scroll;\r\n}\r\n\r\n.pdf-canvas {\r\n  margin: 20rpx auto;\r\n  background-color: white;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115033937\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}