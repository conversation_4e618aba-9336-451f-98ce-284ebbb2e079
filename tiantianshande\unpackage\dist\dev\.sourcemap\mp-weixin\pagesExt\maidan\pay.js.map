{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?7d60", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?37ea", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?b958", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?45c9", "uni-app:///pagesExt/maidan/pay.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?56d7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/pay.vue?4557"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pre_url", "opt", "loading", "isload", "menuindex", "bid", "ymid", "hiddenmodalput", "wxpayst", "alipay", "paypwd", "moneypay", "mdlist", "name", "userinfo", "couponList", "couponrid", "coupontype", "usescore", "money", "disprice", "dkmoney", "couponmoney", "paymoney", "mdkey", "couponvisible", "couponkey", "logo", "usemoney", "moneydkmoney", "<PERSON><PERSON><PERSON><PERSON>", "balancePayEnabled", "KeyboardKeys", "keyHidden", "selectmdDialogShow", "pay_notice", "onLoad", "app", "uni", "console", "onPullDownRefresh", "onShow", "that", "setTimeout", "methods", "handleHiddenKey", "handleShowKey", "handle<PERSON>ey", "getdata", "latitude", "loaded", "modalinput", "selectmd", "itemlist", "itemList", "success", "selectmdRadioChange", "hideSelectmdDialog", "scoredk", "moneydk", "inputMoney", "cancel", "calculatePrice", "原始金额", "会员折扣", "优惠券抵扣", "积分抵扣", "余额抵扣", "实付金额", "微信支付金额", "chooseCoupon", "topay", "mdid", "showCouponList", "handleClickMask", "GetDistance", "Math", "s", "compare"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqM5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;;IAEA;IACA;MACA;MACAC;MACAC;IACA;IACA;IAAA,KACA;MACA;MACAD;MACAC;MACAC;IACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAF;IACA;IACA;IACA;MACA;MACAA;MACA;QACAG;QACA;QACAJ;QACA;QACAK;UACAD;QACA;MACA;IACA;EACA;EACAE;IAEAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA,IACA5B,QACA,KADAA;MAEA;MACA;QACA;UACA;UACAuB;QACA;MACA;QACA;QACA;QACAH;QACA;UACAG;QACA;MACA;MACA;IACA;IAGAM;MACA;MACAN;;MAEA;MACA;QACAH;QACA;QACA;UACAA;UACAG;QACA;MACA;MAEAL;QACAhC;MACA;QACAqC;QACA;UACAL;YACAA;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;QAEAK;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACAA;QAEA;UACAL;YACA;YACA;YACA;YACA;YAEA;cACAzB,iEACAqC;YACA;YAEArC;YACA2B;YACAG;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAX;MACA;QACAJ;UACAgB;UACAC;YACA;cACAb;YACA;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACArB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAsB;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA;QACA3C;MACA;QACAA;MACA;MAEA;MACA;QACAC;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA,wEACAA;QACAA;MACA;;MAEA;MACA;MACA;QACA;QACA;UACAQ;QACA;MACA;;MAEA;MACA;MAEAa;MACAA;MACAA;MACAA;;MAEAH;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAhD;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAiD;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACAlC;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;MACA;MACAC;MACAC;;MAEA;;MAEAA;MAMAF;QACAhC;QACAC;QACAa;QACAH;QACAE;QACAU;QAAA;QACA4C;QACA;MACA;QACA;UACAnC;UACA;QACA;QACAA;MACA;IACA;IACAoC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA,uGACAC;MACAC;MACAA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnlBA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/maidan/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/maidan/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=57260629&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/maidan/pay.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=template&id=57260629&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.userinfo.discount > 0 && _vm.userinfo.discount < 10\n      ? _vm.t(\"会员\")\n      : null\n  var m1 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var g0 = _vm.isload ? _vm.couponList.length : null\n  var m2 =\n    _vm.isload && g0 > 0 && !(_vm.couponrid != 0) ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && !(g0 > 0) ? _vm.t(\"优惠券\") : null\n  var m4 =\n    _vm.isload && _vm.userinfo.scoredkmaxpercent > 0 ? _vm.t(\"积分\") : null\n  var m5 =\n    _vm.isload && _vm.userinfo.scoredkmaxpercent > 0 ? _vm.t(\"积分\") : null\n  var m6 =\n    _vm.isload && _vm.userinfo.money > 0 && _vm.balancePayEnabled\n      ? _vm.t(\"余额\")\n      : null\n  var m7 =\n    _vm.isload && _vm.userinfo.money > 0 && _vm.balancePayEnabled\n      ? _vm.t(\"余额\")\n      : null\n  var m8 =\n    _vm.isload && _vm.usemoney && _vm.moneydkmoney > 0 && _vm.actualpaymoney > 0\n      ? _vm.t(\"余额\")\n      : null\n  var m9 = _vm.isload && _vm.keyHidden ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var m11 =\n    _vm.isload && !_vm.keyHidden ? _vm.money && parseFloat(_vm.money) > 0 : null\n  var l0 =\n    _vm.isload && _vm.selectmdDialogShow\n      ? _vm.__map(_vm.mdlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m12 = index == _vm.mdkey ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m12: m12,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.money && parseFloat(_vm.money) > 0 ? _vm.topay() : \"\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<block v-if=\"isload\">\n\t\t\t<view class=\"container\">\n\t\t\t\t<view class=\"header\">\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<image class=\"header_icon\" :src=\"logo\"></image>\n\t\t\t\t\t\t<view class=\"flex1\">\n\t\t\t\t\t\t\t<view class=\"header_name\">{{name}}</view>\n\t\t\t\t\t\t\t<view class=\"header_shop\">\n\t\t\t\t\t\t\t\t<text>选择门店:</text><text @tap=\"selectmd\">{{mdlist[mdkey].name}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"page\">\n\t\t\t\t\t<view class=\"page_module flex-y-center\" @click=\"handleShowKey\">\n\t\t\t\t\t\t<text class=\"page_tag\">￥</text>\n\t\t\t\t\t\t<!-- <input class=\"page_price flex1\" type=\"digit\" @input=\"inputMoney\" placeholder=\"请输入金额\"></input> -->\n\t\t\t\t\t\t<view class=\"page_price flex-y-center\">\n\t\t\t\t\t\t\t<text v-if=\"keyHidden&&!money\" class=\"page_notice\">请输入金额</text>\n\t\t\t\t\t\t\t<text v-if=\"money\">{{ money }}</text>\n\t\t\t\t\t\t\t<view v-if=\"!keyHidden\" class=\"page_cursor\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"notice-box\" v-if=\"pay_notice\">\n\t\t\t\t\t\t<!-- <view class=\"notice-title\">付款须知</view> -->\n\t\t\t\t\t\t<rich-text class=\"notice-content\" :nodes=\"pay_notice\"></rich-text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"info-box\">\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.discount>0 && userinfo.discount<10\">\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" style=\"color: #e94745;\">-￥{{disprice}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex-y-center\">\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-if=\"couponList.length>0\" @tap=\"showCouponList\" style=\"color:#e94745\">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<image class=\"f3\" src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.scoredkmaxpercent > 0\">\n\t\t\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.dkmoney*1}}</text> 元</view>\n\t\t\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">\n\t\t\t\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 添加余额支付选项 -->\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.money > 0 && balancePayEnabled\">\n\t\t\t\t\t\t\t\t<checkbox-group @change=\"moneydk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t\t<view>可用{{t('余额')}} <text style=\"color:#e94745\">{{userinfo.money*1}}</text> 元</view>\n\t\t\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"moneydkmoney > 0\">\n\t\t\t\t\t\t\t\t\t\t\t本次可抵扣 {{moneydkmoney}} 元\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('余额')}}支付\n\t\t\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex flex-bt\">\n\t\t\t\t\t\t\t\t<text class=\"t1\">实付金额:</text>\n\t\t\t\t\t\t\t\t<text class=\"t2\">￥{{paymoney}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 显示组合支付详情 -->\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex flex-bt\" v-if=\"usemoney && moneydkmoney > 0 && actualpaymoney > 0\">\n\t\t\t\t\t\t\t\t<text class=\"t1\" style=\"font-size:24rpx;color:#666;\">支付详情:</text>\n\t\t\t\t\t\t\t\t<text class=\"t2\" style=\"font-size:24rpx;color:#666;\">{{t('余额')}}￥{{moneydkmoney}} + 微信￥{{actualpaymoney}}</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view v-if=\"keyHidden\" class=\"op\">\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去支付</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\n\t\t\t\t<!-- <view class=\"info-box\">\n\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t<text class=\"t1\">商户名称:</text>\n\t\t\t\t\t\t<text class=\"t2\">{{name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\" v-if=\"mdlist.length>0\">\n\t\t\t\t\t\t<text class=\"t1\">选择门店:</text>\n\t\t\t\t\t\t<text class=\"t2\" @tap=\"selectmd\">{{mdlist[mdkey].name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t<text class=\"t1\">支付金额:</text>\n\t\t\t\t\t\t<view class=\"t2 flex-y-center\" style=\"justify-content:flex-end\"><input type=\"digit\" @input=\"inputMoney\" value=\"\" placeholder=\"请输入金额\"></input> 元</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t<!-- <view class=\"info-box\">\n\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.discount>0 && userinfo.discount<10\">\n\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>\n\t\t\t\t\t\t<text class=\"f2\" style=\"color: #e94745;\">-￥{{disprice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dkdiv-item flex\">\n\t\t\t\t\t\t<text class=\"f1\">{{t('优惠券')}}</text>\n\t\t\t\t\t\t<text class=\"f2\" @tap=\"showCouponList\"\n\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>\n\t\t\t\t\t\t<image class=\"f3\" src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dkdiv-item flex\">\n\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t\t\t\t<text class=\"f1\">{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.dkmoney*1}}</text> 元</text>\n\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t<!-- <view class=\"info-box\">\n\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t<text class=\"t1\">实付金额:</text>\n\t\t\t\t\t\t<text class=\"t2\">￥{{paymoney}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t<!-- <view class=\"op\">\n\t\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去支付</view>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponrid\"\n\t\t\t\t\t\t\t@chooseCoupon=\"chooseCoupon\"></couponlist>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"!keyHidden\" class=\"keyboard_page\">\n\t\t\t\t<view @click=\"handleHiddenKey\" class=\"keyboard_none\"></view>\n\t\t\t\t<view class=\"keyboard_key hind_box\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t<image @click=\"handleHiddenKey\" class=\"key-down\" :src=\"pre_url+'/static/img/pack_up.png'\" mode=\"\"></image>\n\t\t\t\t\t<view class=\"key-box\">\n\t\t\t\t\t\t<view class=\"number-box clearfix\">\n\t\t\t\t\t\t\t<view v-for=\"(item,index) in KeyboardKeys\" :key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"index === 9 ? 'key key-zero' : 'key'\" hover-class=\"number-box-hover\"\n\t\t\t\t\t\t\t\t@click=\"handleKey(item)\">{{item}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"btn-box\">\n\t\t\t\t\t\t\t<!-- TODO: 需要替换成删除icon -->\n\t\t\t\t\t\t\t<view class=\"key\" hover-class=\"number-box-hover\" data-key=\"X\" @click=\"handleKey('X')\">×\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t:class=\"money && parseFloat(money) > 0 ? 'key pay_btn' : 'key pay_btn pay-btn-display'\"\n\t\t\t\t\t\t\t\thover-class=\"pay-btn-hover\" \n\t\t\t\t\t\t\t\t@tap=\"money && parseFloat(money) > 0 ? topay() : ''\"\n\t\t\t\t\t\t\t>付款</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"selectmdDialogShow\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelectmdDialog\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择门店</text>\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelectmdDialog\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in mdlist\" :key=\"index\" @tap=\"selectmdRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\n\t\t\t\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-right:10rpx\">{{item.juli ? ' 距离:' + item.juli + '千米' : ''}}</view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"index==mdkey ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</block>\n\t\t<loading v-if=\"loading\"></loading>\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpre_url: app.globalData.pre_url,\n\t\t\t\topt: {},\n\t\t\t\tloading: false,\n\t\t\t\tisload: false,\n\t\t\t\tmenuindex: -1,\n\n\t\t\t\tbid: 0,\n                ymid: 0,\n\t\t\t\thiddenmodalput: true,\n\t\t\t\twxpayst: '',\n\t\t\t\talipay: '',\n\t\t\t\tpaypwd: '',\n\t\t\t\tmoneypay: '',\n\t\t\t\tmdlist: \"\",\n\t\t\t\tname: \"\",\n\t\t\t\tuserinfo: \"\",\n\t\t\t\tcouponList: [],\n\t\t\t\tcouponrid: 0,\n\t\t\t\tcoupontype: 1,\n\t\t\t\tusescore: 0,\n\t\t\t\tmoney: '',\n\t\t\t\tdisprice: 0,\n\t\t\t\tdkmoney: 0,\n\t\t\t\tcouponmoney: 0,\n\t\t\t\tpaymoney: 0,\n\t\t\t\tmdkey: 0,\n\t\t\t\tcouponvisible: false,\n\t\t\t\tcouponkey: 0,\n\t\t\t\tlogo:\"\",\n\t\t\t\t// 添加余额支付相关变量\n\t\t\t\tusemoney: 0, // 是否使用余额支付\n\t\t\t\tmoneydkmoney: 0, // 余额可抵扣金额\n\t\t\t\tactualpaymoney: 0, // 实际需要微信支付的金额\n\t\t\t\tbalancePayEnabled: 0, // 余额支付功能开关\n\n\t\t\t\tKeyboardKeys: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.'],\n\t\t\t\tkeyHidden: false,\n\t\t\t\tselectmdDialogShow: false,\n\t\t\t\tpay_notice: '',\n\t\t\t};\n\t\t},\n\n\t\tonLoad: function(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.bid = this.opt.bid || 0;\n\t\t\t\n\t\t\t// 处理ymid参数（优先级最高）\n\t\t\tif(this.opt.ymid){\n\t\t\t\tthis.ymid = this.opt.ymid;\n\t\t\t\tapp.globalData.pid = this.opt.ymid;\n\t\t\t\tuni.setStorageSync('pid', this.opt.ymid);\n\t\t\t}\n\t\t\t// 处理pid参数（通常来自scene参数解析）\n\t\t\telse if(this.opt.pid){\n\t\t\t\tthis.ymid = this.opt.pid;\n\t\t\t\tapp.globalData.pid = this.opt.pid;\n\t\t\t\tuni.setStorageSync('pid', this.opt.pid);\n\t\t\t\tconsole.log('商家买单页面：识别到推广用户ID', this.opt.pid);\n\t\t\t}\n\t\t\t\n\t\t\tthis.getdata();\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.getdata();\n\t\t},\n\t\tonShow: function() {\n\t\t\t// 检查是否支付完成后返回\n\t\t\tconsole.log('页面显示，检查bid'); // 添加日志\n\t\t\tvar that = this;\n\t\t\t// 如果当前没有bid但存储中有，则恢复它\n\t\t\tif (!that.bid || that.bid == 0) {\n\t\t\t\tvar savedBid = uni.getStorageSync('maidan_last_bid');\n\t\t\t\tconsole.log('获取存储的bid:', savedBid); // 添加日志\n\t\t\t\tif (savedBid) {\n\t\t\t\t\tthat.bid = savedBid;\n\t\t\t\t\t// 清除存储，避免影响后续操作\n\t\t\t\t\tuni.removeStorageSync('maidan_last_bid');\n\t\t\t\t\t// 刷新页面数据\n\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 200);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\n\t\t\thandleHiddenKey() {\n\t\t\t\tthis.keyHidden = true;\n\t\t\t},\n\t\t\t// 显示键盘\n\t\t\thandleShowKey() {\n\t\t\t\tthis.keyHidden = false;\n\t\t\t},\n\t\t\t// 键盘输入\n\t\t\thandleKey(key) {\n\t\t\t\tconst that = this\n\t\t\t\tconst {\n\t\t\t\t\tmoney\n\t\t\t\t} = this\n\t\t\t\t// 删除金额\n\t\t\t\tif (key === 'X') {\n\t\t\t\t\tif (money !== '') {\n\t\t\t\t\t\tconst payMoney = money.slice(0, money.length - 1)\n\t\t\t\t\t\tthat.money = payMoney\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 添加金额\n\t\t\t\t\tconst payMoney = money + key\n\t\t\t\t\tconsole.log(/^(\\d+\\.?\\d{0,2})$/.test(payMoney), payMoney, 'payMoney')\n\t\t\t\t\tif (/^(\\d+\\.?\\d{0,2})$/.test(payMoney)) {\n\t\t\t\t\t\tthat.money = payMoney\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.calculatePrice();\n\t\t\t},\n\n\n\t\t\tgetdata: function() {\n\t\t\t\tvar that = this; //获取产品信息\n\t\t\t\tthat.loading = true;\n\t\t\t\t\n\t\t\t\t// 检查bid是否为零\n\t\t\t\tif (!that.bid || that.bid == 0) {\n\t\t\t\t\tconsole.log('警告: bid参数为空或为0');\n\t\t\t\t\tvar savedBid = uni.getStorageSync('maidan_last_bid');\n\t\t\t\t\tif (savedBid) {\n\t\t\t\t\t\tconsole.log('从本地存储恢复bid:', savedBid);\n\t\t\t\t\t\tthat.bid = savedBid;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tapp.get('ApiMaidan/maidan', {\n\t\t\t\t\tbid: that.bid\n\t\t\t\t}, function(res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.alert(res.msg, function() {\n\t\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 安全地获取数据，防止undefined错误\n\t\t\t\t\tvar userinfo = res.userinfo || {};\n\t\t\t\t\tvar couponList = res.couponList || [];\n\t\t\t\t\tvar mdlist = res.mdlist || [];\n\t\t\t\t\t\n\t\t\t\t\tthat.wxpayst = res.wxpayst || '';\n\t\t\t\t\tthat.alipay = res.alipay || '';\n\t\t\t\t\tthat.couponList = couponList;\n\t\t\t\t\tthat.mdlist = mdlist;\n\t\t\t\t\tthat.moneypay = res.moneypay || '';\n\t\t\t\t\tthat.name = res.name || '';\n\t\t\t\t\tthat.userinfo = userinfo;\n\t\t\t\t\tthat.logo = res.logo || '';\n\t\t\t\t\tthat.pay_notice = res.pay_notice || '';\n\t\t\t\t\tthat.balancePayEnabled = res.balance_pay_enabled || 0; // 获取余额支付开关状态\n\t\t\t\t\t\n\t\t\t\t\t// 确保页面显示\n\t\t\t\t\tthat.loaded();\n\n\t\t\t\t\tif (mdlist && mdlist.length > 0) {\n\t\t\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\t\t\tvar speed = res.speed;\n\t\t\t\t\t\t\tvar accuracy = res.accuracy;\n\n\t\t\t\t\t\t\tfor (var i in mdlist) {\n\t\t\t\t\t\t\t\tmdlist[i].juli = that.GetDistance(latitude, longitude, mdlist[i]\n\t\t\t\t\t\t\t\t\t.latitude, mdlist[i].longitude);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tmdlist = mdlist.sort(that.compare('juli'));\n\t\t\t\t\t\t\tconsole.log(mdlist);\n\t\t\t\t\t\t\tthat.mdlist = mdlist;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tloaded: function() {\n\t\t\t\t// 设置页面已加载\n\t\t\t\tthis.isload = true;\n\t\t\t},\n\t\t\tmodalinput: function() {\n\t\t\t\tthis.$refs.dialogInput.open()\n\t\t\t},\n\t\t\t//选择门店\n\t\t\tselectmd: function(e) {\n\t\t\t\tvar that = this;\n\t\t\t\tvar itemlist = [];\n\t\t\t\tvar mdlist = this.mdlist;\n\t\t\t\tfor (var i = 0; i < mdlist.length; i++) {\n\t\t\t\t\titemlist.push(mdlist[i].name + (mdlist[i].juli ? ' 距离:' + mdlist[i].juli + '千米' : ''));\n\t\t\t\t}\n\t\t\t\tif (itemlist.length > 6) {\n\t\t\t\t\tthat.selectmdDialogShow = true;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showActionSheet({\n\t\t\t\t\t\titemList: itemlist,\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.tapIndex >= 0) {\n\t\t\t\t\t\t\t\tthat.mdkey = res.tapIndex;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tselectmdRadioChange: function (e) {\n\t\t\t\tthis.mdkey = e.currentTarget.dataset.index;\n\t\t\t\tthis.selectmdDialogShow = false;\n\t\t\t},\n\t\t\thideSelectmdDialog: function () {\n\t\t\t\tthis.selectmdDialogShow = false\n\t\t\t},\n\t\t\t//积分抵扣\n\t\t\tscoredk: function(e) {\n\t\t\t\tvar usescore = e.detail.value[0];\n\t\t\t\tif (!usescore) usescore = 0;\n\t\t\t\tthis.usescore = usescore;\n\t\t\t\tthis.calculatePrice();\n\t\t\t},\n\t\t\t//余额抵扣\n\t\t\tmoneydk: function(e) {\n\t\t\t\tvar usemoney = e.detail.value[0];\n\t\t\t\tif (!usemoney) usemoney = 0;\n\t\t\t\tthis.usemoney = usemoney;\n\t\t\t\tthis.calculatePrice();\n\t\t\t},\n\t\t\tinputMoney: function(e) {\n\t\t\t\tconsole.log(e);\n\t\t\t\tvar money = e.detail.value;\n\t\t\t\tif (!money) money = 0;\n\t\t\t\tvar money = parseFloat(money);\n\t\t\t\tif (money <= 0) money = 0;\n\t\t\t\tthis.money = money;\n\t\t\t\tthis.calculatePrice();\n\t\t\t},\n\t\t\tcancel: function() {\n\t\t\t\tthis.hiddenmodalput = true;\n\t\t\t},\n\t\t\t//计算价格\n\t\t\tcalculatePrice: function() {\n\t\t\t\tvar that = this;\n\n\t\t\t\tvar money = 0;\n\t\t\t\tif (that.money == '') {\n\t\t\t\t\tmoney = 0;\n\t\t\t\t} else {\n\t\t\t\t\tmoney = parseFloat(that.money) || 0;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar disprice = 0;\n\t\t\t\tif (that.userinfo && that.userinfo.discount > 0 && that.userinfo.discount < 10) {\n\t\t\t\t\tdisprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100; //-会员折扣\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar couponmoney = parseFloat(that.couponmoney) || 0; //-优惠券抵扣 \n\t\t\t\tvar dkmoney = 0;\n\t\t\t\tif (that.usescore && that.userinfo) {\n\t\t\t\t\tdkmoney = parseFloat(that.userinfo.dkmoney) || 0; //-积分抵扣\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar scoredkmaxpercent = that.userinfo ? parseFloat(that.userinfo.scoredkmaxpercent) || 0 : 0; //最大抵扣比例\n\t\t\t\tif (dkmoney > 0 && scoredkmaxpercent >= 0 && scoredkmaxpercent < 100 &&\n\t\t\t\t\tdkmoney > (money - disprice - couponmoney) * scoredkmaxpercent * 0.01) {\n\t\t\t\t\tdkmoney = (money - disprice - couponmoney) * scoredkmaxpercent * 0.01;\n\t\t\t\t}\n\n\t\t\t\t// 计算余额抵扣\n\t\t\t\tvar moneydkmoney = 0;\n\t\t\t\tif (that.usemoney && that.userinfo && that.userinfo.money > 0) {\n\t\t\t\t\tvar remainingMoney = money - disprice - couponmoney - dkmoney; // 剩余需要支付的金额\n\t\t\t\t\tif (remainingMoney > 0) {\n\t\t\t\t\t\tmoneydkmoney = Math.min(parseFloat(that.userinfo.money), remainingMoney); // 取用户余额和剩余金额的最小值\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar paymoney = money - disprice - couponmoney - dkmoney - moneydkmoney; // 商品金额 - 会员折扣 - 优惠券抵扣 - 积分抵扣 - 余额抵扣\n\t\t\t\tif (paymoney < 0) paymoney = 0;\n\t\t\t\t\n\t\t\t\tthat.paymoney = paymoney.toFixed(2);\n\t\t\t\tthat.disprice = disprice.toFixed(2);\n\t\t\t\tthat.moneydkmoney = moneydkmoney.toFixed(2);\n\t\t\t\tthat.actualpaymoney = paymoney.toFixed(2); // 实际需要微信支付的金额\n\t\t\t\t\n\t\t\t\tconsole.log('计算价格完成:', {\n\t\t\t\t\t原始金额: money,\n\t\t\t\t\t会员折扣: disprice,\n\t\t\t\t\t优惠券抵扣: couponmoney,\n\t\t\t\t\t积分抵扣: dkmoney,\n\t\t\t\t\t余额抵扣: moneydkmoney,\n\t\t\t\t\t实付金额: paymoney,\n\t\t\t\t\t微信支付金额: that.actualpaymoney\n\t\t\t\t});\n\t\t\t},\n\t\t\tchooseCoupon: function(e) {\n\t\t\t\tvar couponrid = e.rid;\n\t\t\t\tvar couponkey = e.key;\n\n\t\t\t\tif (couponrid == this.couponrid) {\n\t\t\t\t\tthis.couponkey = 0;\n\t\t\t\t\tthis.couponrid = 0;\n\t\t\t\t\tthis.coupontype = 1;\n\t\t\t\t\tthis.couponmoney = 0;\n\t\t\t\t\tthis.couponvisible = false;\n\t\t\t\t} else {\n\t\t\t\t\tvar couponList = this.couponList;\n\t\t\t\t\tvar couponmoney = couponList[couponkey]['money'];\n\t\t\t\t\tvar coupontype = couponList[couponkey]['type'];\n\t\t\t\t\tif (coupontype == 4) {\n\t\t\t\t\t\tcouponmoney = this.freightprice;\n\t\t\t\t\t}\n\t\t\t\t\tthis.couponkey = couponkey;\n\t\t\t\t\tthis.couponrid = couponrid;\n\t\t\t\t\tthis.coupontype = coupontype;\n\t\t\t\t\tthis.couponmoney = couponmoney;\n\t\t\t\t\tthis.couponvisible = false;\n\t\t\t\t}\n\t\t\t\tthis.calculatePrice();\n\t\t\t},\n\t\t\ttopay: function(e) {\n\t\t\t\tvar that = this;\n\t\t\t\tvar money = that.money;\n\t\t\t\tvar couponrid = that.couponrid;\n\t\t\t\tvar usescore = that.usescore;\n\t\t\t\tvar usemoney = that.usemoney; // 添加余额支付参数\n\n\t\t\t\t// 验证金额是否有效\n\t\t\t\tif (!money || money === '') {\n\t\t\t\t\tapp.error('请输入支付金额');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (that.mdlist.length > 0) {\n\t\t\t\t\tvar mdid = that.mdlist[that.mdkey].id;\n\t\t\t\t} else {\n\t\t\t\t\tvar mdid = 0;\n\t\t\t\t}\n\t\t\t\t// 在跳转到支付页面前，先保存bid到本地存储\n\t\t\t\tuni.setStorageSync('maidan_last_bid', that.bid);\n\t\t\t\tconsole.log('支付前保存bid:', that.bid); // 添加日志\n\n\t\t\t\t// 特殊处理小程序环境 - 仅记录不传参\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tconsole.log('当前平台: 微信小程序');\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\tconsole.log('当前平台: H5');\n\t\t\t\t// #endif\n\n\t\t\t\tapp.post('ApiMaidan/maidan', {\n\t\t\t\t\tbid: that.bid,\n\t\t\t\t\tymid: that.ymid,\n\t\t\t\t\tmoney: money,\n\t\t\t\t\tcouponrid: couponrid,\n\t\t\t\t\tusescore: usescore,\n\t\t\t\t\tusemoney: usemoney, // 添加余额支付参数\n\t\t\t\t\tmdid: mdid\n\t\t\t\t\t// 暂时移除platform参数，避免后端API不兼容\n\t\t\t\t}, function(res) {\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid+'&tourl=/pagesExt/maidan/pay?bid='+that.bid);\n\t\t\t\t});\n\t\t\t},\n\t\t\tshowCouponList: function() {\n\t\t\t\tthis.couponvisible = true;\n\t\t\t},\n\t\t\thandleClickMask: function() {\n\t\t\t\tthis.couponvisible = false;\n\t\t\t},\n\t\t\tGetDistance: function(lat1, lng1, lat2, lng2) {\n\t\t\t\tvar radLat1 = lat1 * Math.PI / 180.0;\n\t\t\t\tvar radLat2 = lat2 * Math.PI / 180.0;\n\t\t\t\tvar a = radLat1 - radLat2;\n\t\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n\t\t\t\tvar s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *\n\t\t\t\t\tMath.pow(Math.sin(b / 2), 2)));\n\t\t\t\ts = s * 6378.137; // EARTH_RADIUS;\n\t\t\t\ts = Math.round(s * 100) / 100;\n\t\t\t\treturn s;\n\t\t\t},\n\t\t\tcompare: function(property) {\n\t\t\t\treturn function(a, b) {\n\t\t\t\t\tvar value1 = a[property];\n\t\t\t\t\tvar value2 = b[property];\n\t\t\t\t\treturn value1 - value2;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style>\n\tpage {\n\t\tbackground: #f0f0f0;\n\t}\n\n\t.container {\n\t\tposition: fixed;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t\tz-index: 5;\n\t}\n\n\t.header {\n\t\tposition: relative;\n\t\tpadding: 30rpx;\n\t}\n\n\t.header_text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.header_name {\n\t\tfont-size: 36rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.header_icon {\n\t\tposition: relative;\n\t\theight: 85rpx;\n\t\twidth: 85rpx;\n\t\tmargin-right: 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground: #37b053;\n\t}\n\n\t.header_shop {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.page {\n\t\tposition: relative;\n\t\tpadding: 20rpx 50rpx 20rpx 50rpx;\n\t\tborder-radius: 30rpx 30rpx 0 0;\n\t\tbackground: #fff;\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\theight: calc(100% - 185rpx);\n\t}\n\n\t.page_title {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t}\n\n\t.page_module {\n\t\tposition: relative;\n\t\theight: 125rpx;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.page_notice {\n\t\tcolor: #999;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: normal;\n\t}\n\n\t.page_tag {\n\t\tfont-size: 58rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.page_price {\n\t\tmargin-left: 20rpx;\n\t\tfont-size: 54rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.page_cursor {\n\t\twidth: 4rpx;\n\t\theight: 70rpx;\n\t\tbackground: #1AAD19;\n\t\tborder-radius: 6rpx;\n\t\tanimation: twinkling 1.5s infinite;\n\t}\n\n\t@keyframes twinkling {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t}\n\n\t\t90% {\n\t\t\topacity: .8;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.info-box {\n\t\tposition: relative;\n\t\tbackground: #fff;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-bottom: 1px #f3f3f3 solid;\n\t}\n\n\t.info-item:last-child {\n\t\tborder: none\n\t}\n\n\t.info-item .t1 {\n\t\twidth: 200rpx;\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tcolor: #000;\n\t}\n\n\t.info-item .t2 {\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tcolor: #000;\n\t\ttext-align: right;\n\t\tflex: 1;\n\t\tfont-size: 28rpx\n\t}\n\n\t.info-item .t2 input {\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tborder: 1px solid #f5f5f5;\n\t\tpadding: 0 5px;\n\t\twidth: 240rpx;\n\t\tfont-size: 30rpx;\n\t\tmargin-right: 10rpx\n\t}\n\n\t.dkdiv {\n\t\tmargin-top: 20rpx\n\t}\n\n\t.dkdiv-item {\n\t\twidth: 100%;\n\t\tpadding: 30rpx 0;\n\t\tbackground: #fff;\n\t\tborder-bottom: 1px #ededed solid;\n\t}\n\n\t.dkdiv-item:last-child {\n\t\tborder: none;\n\t}\n\n\t.dkdiv-item .f1 {}\n\n\t.dkdiv-item .f2 {\n\t\ttext-align: right;\n\t\tflex: 1\n\t}\n\n\t.dkdiv-item .f3 {\n\t\twidth: 30rpx;\n\t\theight: 30rpx;\n\t}\n\n\t.fpay-btn {\n\t\twidth: 90%;\n\t\tmargin: 0 5%;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tmargin-top: 40rpx;\n\t\tfloat: left;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #1aac19;\n\t\tborder: none;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.fpay-btn2 {\n\t\twidth: 90%;\n\t\tmargin: 0 5%;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tmargin-top: 20rpx;\n\t\tfloat: left;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #e2cc05;\n\t\tborder: none;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.mendian {\n\t\twidth: 90%;\n\t\tline-height: 60rpx;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 30rpx 5%;\n\t\theight: 800rpx;\n\t\toverflow-y: scroll;\n\t\tborder: none;\n\t\tborder-radius: 5px;\n\t\t-webkit-animation-duration: .5s;\n\t\tanimation-duration: .5s;\n\t}\n\n\t.mendian label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\t\tpadding: 20rpx 0;\n\t\tcolor: #333\n\t}\n\n\t.mendian input {\n\t\tmargin-right: 10rpx\n\t}\n\n\t.submit {\n\t\ttext-align: center\n\t}\n\n\t.mendian button {\n\t\tpadding: 20rpx 60rpx;\n\t\tborder-radius: 40rpx;\n\t\tborder: 0;\n\t\tmargin-top: 20rpx;\n\t\tcolor: #fff;\n\t\tbackground: #31C88E\n\t}\n\n\t.i-as {\n\t\tposition: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: #f7f7f8;\n\t\ttransform: translate3d(0, 100%, 0);\n\t\ttransform-origin: center;\n\t\ttransition: all .2s ease-in-out;\n\t\tz-index: 900;\n\t\tvisibility: hidden\n\t}\n\n\t.i-as-show {\n\t\ttransform: translate3d(0, 0, 0);\n\t\tvisibility: visible\n\t}\n\n\t.i-as-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, .7);\n\t\tz-index: 900;\n\t\ttransition: all .2s ease-in-out;\n\t\topacity: 0;\n\t\tvisibility: hidden\n\t}\n\n\t.i-as-mask-show {\n\t\topacity: 1;\n\t\tvisibility: visible\n\t}\n\n\t.i-as-header {\n\t\tbackground: #fff;\n\t\ttext-align: center;\n\t\tposition: relative;\n\t\tfont-size: 30rpx;\n\t\tcolor: #555;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx\n\t}\n\n\t.i-as-header::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 200%;\n\t\theight: 200%;\n\t\ttransform: scale(.5);\n\t\ttransform-origin: 0 0;\n\t\tpointer-events: none;\n\t\tbox-sizing: border-box;\n\t\tborder: 0 solid #e9eaec;\n\t\tborder-bottom-width: 1px\n\t}\n\n\t.i-as-cancel {\n\t\tmargin-top: 20rpx\n\t}\n\n\t.i-as-cancel button {\n\t\tborder: 0\n\t}\n\n\t.i-as-cancel button::after {\n\t\tborder: 0;\n\t}\n\n\t.i-as-content {\n\t\theight: 700rpx;\n\t\twidth: 710rpx;\n\t\tmargin: 20rpx;\n\t}\n\n\n\t.op {\n\t\twidth: 96%;\n\t\tmargin: 20rpx 2%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 40rpx\n\t}\n\n\t.op .btn {\n\t\tflex: 1;\n\t\theight: 100rpx;\n\t\tline-height: 100rpx;\n\t\tbackground: #07C160;\n\t\twidth: 90%;\n\t\tmargin: 0 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\t.op .btn .img {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-right: 20rpx\n\t}\n\n\n\t.keyboard_page {\n\t\tposition: fixed;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 999;\n\t}\n\n\t.keyboard_none {\n\t\tposition: absolute;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\ttop: 0;\n\t\tleft: 0;\n\t}\n\n\t.keyboard_key {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\theight: 0;\n\t\tz-index: 10;\n\t\tbackground: #f7f7f7;\n\t\tz-index: 9999999999;\n\t\ttransition: height 0.3s;\n\t\tpadding: 20rpx 0 0 0;\n\t}\n\n\t.hind_box {\n\t\theight: 515rpx;\n\t}\n\n\t.key-box {\n\t\tdisplay: flex;\n\t\tpadding-left: 16rpx;\n\t\tpadding-bottom: 16rpx;\n\t\tpadding-bottom: calc(16rpx + constant(safe-area-inset-bottom));\n\t\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n\t}\n\t\n\t.key-down{\n\t\theight: 50rpx;\n\t\twidth: 50rpx;\n\t\tdisplay: block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.number-box {\n\t\tflex: 3;\n\t}\n\n\t.number-box .key {\n\t\tfloat: left;\n\t\tmargin: 16rpx 16rpx 0 0;\n\t\twidth: calc(100% / 3 - 16rpx);\n\t\theight: 90rpx;\n\t\tborder-radius: 10rpx;\n\t\tline-height: 90rpx;\n\t\ttext-align: center;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tbackground-color: #fff;\n\t}\n\n\t.number-box .key.key-zero {\n\t\twidth: calc((100% / 3) * 2 - 16rpx);\n\t}\n\n\t.keyboard .number-box-hover {\n\t\t/* 临时定义颜色 */\n\t\tbackground-color: #e1e1e1 !important;\n\t}\n\n\t.btn-box {\n\t\tflex: 1;\n\t}\n\n\t.btn-box .key {\n\t\tmargin: 16rpx 16rpx 0 0;\n\t\theight: 90rpx;\n\t\tborder-radius: 10rpx;\n\t\tline-height: 90rpx;\n\t\ttext-align: center;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tbackground-color: #fff;\n\t}\n\n\t.btn-box .pay_btn {\n\t\theight: 298rpx;\n\t\tline-height: 298rpx;\n\t\tfont-weight: normal;\n\t\tbackground-color: #1AAD19;\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.btn-box .pay_btn.pay-btn-display {\n\t\tbackground-color: #9ED99D !important;\n\t}\n\n\t.btn-box .pay_btn.pay-btn-hover {\n\t\tbackground-color: #179B16;\n\t}\n\t.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\n\n\t.notice-box {\n\t\tpadding: 30rpx 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.notice-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.notice-content {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\t}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102927\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}