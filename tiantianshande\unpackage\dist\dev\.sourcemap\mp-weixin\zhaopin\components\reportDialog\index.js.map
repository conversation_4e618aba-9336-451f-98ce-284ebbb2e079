{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?a9ff", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?9f8d", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?37a4", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?7587", "uni-app:///zhaopin/components/reportDialog/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?3b97", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/reportDialog/index.vue?fae1"], "names": ["components", "payDialog", "data", "tips", "applyId", "sendData", "payVisible", "successVisible", "contactWay", "contactNo", "reportVisibleClone", "famousList", "requireList", "partJobVoClone", "jobFeeVO", "feeRushPrice", "rushStatus", "serviceQrcode", "serviceTips", "created", "props", "visible", "type", "default", "partJobId", "reportVisible", "partJobVo", "famousJobList", "hasLatitude", "recommendTips", "<PERSON><PERSON><PERSON>", "shareUserId", "source", "isPartDetails", "fromRecommend", "agreementVo", "isDirect", "reportDialogText", "beforeMount", "methods", "initData", "cancelPayHandle", "successPayHandle", "queryMobile", "<PERSON><PERSON><PERSON>", "saveAgreeMent", "applyValidate", "console", "uni", "title", "icon", "that", "app", "position_id", "setTimeout", "goApplyJob", "normalApply", "closeReportDialog", "copyContact", "signUserContacted", "t", "getServiceQrcode", "closeSuccessDialog", "watch", "handler", "immediate", "newVal", "deep"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmJ9wB;AAAA,gBAEA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;UACAC;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QAAA;MAAA;IACA;IACAI;MACAL;MACAC;QAAA;MAAA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;QAAA;MAAA;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;EACAC;IACAC;MACA;MACA;QACA;MACA;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;MACAC;MACA;MACA;MACA;QACAC;UACAC;UACAC;QACA;QACAC;QACA;MACA;MACA;MACA;QACAH;UACAC;UACAC;QACA;QACA;MACA;MACAH;MACAK;QACAC;MACA;QACAN;QAEA;UACA;UACAA;;UAEA;UACAI;;UAEA;UACAG;YACAH;YACA;YACAA;YACAA;;YAEA;YACAA;;YAEA;YACAH;cACAC;cACAC;YACA;UACA;QACA;UACA;UACAC;UAEAG;YACA;cACAN;gBACAC;gBACAC;cACA;YACA;cACAF;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAK;IACAC;IACAC;MACAV;MACA;MACA;MACA;MACA;IACA;IACAW;IACAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACAT;QACAL;QACA;UACAI;UACAA;QACA;MACA;IACA;IACAW;MACAf;MACA;MACA;IACA;EACA;EACAgB;IACAtC;MACAuC;QACAjB;QACA;MACA;MACAkB;IACA;IACA5C;MACA2C;QACAjB;QACA;QACA;UACA;QACA;MACA;MACAkB;IACA;IACAtC;MACAqC;QACAjB;QACA;UACA;QACA;MACA;MACAkB;IACA;IACAvC;MACAsC;QACA;UACA,sDACAE;YACApD;cACAC;cACAC;YACA;UAAA,EACA;UACA;YACA;UACA;QACA;MACA;MACAiD;MACAE;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC1ZA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/components/reportDialog/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=669d0344&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/components/reportDialog/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=669d0344&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.visible &&\n    !(\n      _vm.partJobVo.template.templateId === 16 ||\n      _vm.partJobVo.template.templateId === 11\n    )\n      ? _vm.famousList.length\n      : null\n  var l0 =\n    _vm.visible &&\n    !(\n      _vm.partJobVo.template.templateId === 16 ||\n      _vm.partJobVo.template.templateId === 11\n    ) &&\n    g0 > 0\n      ? _vm.__map(_vm.famousList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = _vm.famousList.length\n          var g2 = _vm.famousList.length\n          return {\n            $orig: $orig,\n            g1: g1,\n            g2: g2,\n          }\n        })\n      : null\n  var m0 = _vm.visible && !_vm.tips ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <block v-if=\"visible\">\r\n            <view :class=\"'qts-mask ' + (reportVisibleClone ? 'show' : 'hidden')\">\r\n                <view @tap=\"closeReportDialog\" class=\"qts-mask-bg ptp_exposure\" :data-ptpid=\"isPartDetails ? '28a6-1ebc-b0f5-dae7' : 'dhg1-fh2g-h3bf-fh1b'\"></view>\r\n                <view :class=\"'qts-mask-main ' + (isPartDetails ? '' : 'pb-32') + ' ' + (reportVisibleClone ? 'popup' : '')\">\r\n                    <view\r\n                        @tap=\"closeReportDialog\"\r\n                        class=\"qts-close-wrap ptp_exposure\"\r\n                        :data-ptpid=\"isPartDetails ? '5983-1650-a367-446c' : 'fj2y-cn2b-27dh-lvoh'\"\r\n                        v-if=\"partJobVo.template.templateId !== 6\"\r\n                    >\r\n                        <image class=\"qts-mask-close\" src=\"https://qiniu-image.qtshe.com/20190812_maskClose.png\"></image>\r\n                    </view>\r\n                    <block v-if=\"partJobVo.template.templateId === 16 || partJobVo.template.templateId === 11\">\r\n                        <view class=\"qts-mask-title\">温馨提示</view>\r\n                        <view class=\"qts-mask-text\">{{ reportDialogText }}</view>\r\n                    </block>\r\n                    <block v-else>\r\n                        <view class=\"qts-mask-title\">确认工作信息</view>\r\n                        <view class=\"qts-mask-notice\" v-if=\"partJobVo.jobLineType === 2 && !hasLatitude\">你未开启定位，请注意确认工作地点</view>\r\n                        <view>\r\n                            <!-- 添加工资信息 -->\r\n                            <view class=\"qts-mask-item\">工资待遇</view>\r\n                            <view class=\"qts-mask-salary\">{{ partJobVo.salary }}</view>\r\n                            \r\n                            <block\r\n                                v-if=\"\r\n                                    partJobVo.needHealth ||\r\n                                    partJobVo.diploma !== 0 ||\r\n                                    partJobVo.sexRequire.key !== '0' ||\r\n                                    (partJobVo.needHeight && partJobVo.heightRequire !== '不限')\r\n                                \"\r\n                            >\r\n                                <!-- <view class=\"qts-mask-item\">工作要求</view>\r\n                                <view class=\"qts-mask-tips\">\r\n                                    <block v-if=\"requireList.length\">\r\n                                        <view class=\"qts-tips-item\" v-for=\"(item, index) in requireList\" :key=\"item\">{{ item }}</view>\r\n                                    </block>\r\n                                </view> -->\r\n                            </block>\r\n                            \r\n                            <!-- 添加任职要求 -->\r\n                            <block v-if=\"partJobVo.requirement\">\r\n                                <view class=\"qts-mask-item\">任职要求</view>\r\n                                <view class=\"qts-mask-tips\">\r\n                                    <rich-text :nodes=\"partJobVo.requirement\"></rich-text>\r\n                                </view>\r\n                            </block>\r\n                            \r\n                            <view class=\"qts-mask-item\">工作地点</view>\r\n                            <view class=\"qts-mask-address\">\r\n                                <view class=\"qts-mask-address-detail\">{{ partJobVo.addressDetail }}</view>\r\n                                <view v-if=\"partJobVo.jobLineType === 2 && partJobVo.distance\">{{ partJobVo.distance.distance }}</view>\r\n                                <view v-if=\"partJobVo.jobLineType === 2 && !partJobVo.distance\">{{ partJobVo.publishTown.townName }}</view>\r\n                            </view>\r\n                            <view class=\"qts-mask-item\">工作时间</view>\r\n                            <view class=\"qts-mask-time\">{{ partJobVo.jobDateDesc }} {{ partJobVo.jobTime }}</view>\r\n                            <block v-if=\"famousList.length > 0\">\r\n                                <view class=\"qts-mask-text\">{{ recommendTips }}</view>\r\n                                <view class=\"qts-famous-recommend\">\r\n                                    <view\r\n                                        :class=\"'qts-recommend-item ' + (index === famousList.length - 1 ? 'qts-no-border' : '')\"\r\n                                        v-for=\"(item, index) in famousList\"\r\n                                        :key=\"index\"\r\n                                    >\r\n                                        <image\r\n                                            @tap=\"selectJob\"\r\n                                            class=\"qts-recommend-icon\"\r\n                                            :data-index=\"index\"\r\n                                            :data-ptpid=\"isPartDetails ? '4bb9-10fb-9cd9-76b8' : '27fg-v2hc-29fj-ol2n'\"\r\n                                            src=\"https://qiniu-image.qtshe.com/20190812_checkActive.png\"\r\n                                            v-if=\"item.status\"\r\n                                        ></image>\r\n\r\n                                        <image\r\n                                            @tap=\"selectJob\"\r\n                                            class=\"qts-recommend-icon\"\r\n                                            :data-index=\"index\"\r\n                                            :data-ptpid=\"isPartDetails ? '4bb9-10fb-9cd9-76b8' : '27fg-v2hc-29fj-ol2n'\"\r\n                                            src=\"https://qiniu-image.qtshe.com/20190812_check.png\"\r\n                                            v-else\r\n                                        ></image>\r\n\r\n                                        <view\r\n                                            @tap=\"selectJob\"\r\n                                            :class=\"'qts-recommend-main ' + (index === famousList.length - 1 ? 'qts-no-border' : '') + ' ptp_exposure'\"\r\n                                            :data-index=\"index\"\r\n                                            :data-ptpid=\"isPartDetails ? '4bb9-10fb-9cd9-76b8' : '27fg-v2hc-29fj-ol2n'\"\r\n                                        >\r\n                                            <view class=\"qts-recommend-content\">\r\n                                                <view class=\"qts-recommend-title\">{{ item.title }}</view>\r\n                                                <view class=\"qts-recommend-distance\">{{ item.addressDetail }} {{ item.distance }}</view>\r\n                                            </view>\r\n                                            <view class=\"qts-recommend-salary\">{{ item.salary }}</view>\r\n                                        </view>\r\n                                    </view>\r\n                                </view>\r\n                            </block>\r\n                        </view>\r\n                    </block>\r\n                    <view class=\"qts-apply-content\" v-if=\"tips\">{{ tips }}</view>\r\n                    <view class=\"qts-mask-button-disable\" v-if=\"tips\">{{ partJobVo.is_apply ? '已报名' : '立即报名' }}</view>\r\n                    <view @tap=\"applyValidate\" class=\"qts-mask-button ptp_exposure\" :style=\"{background: t('color1')}\" :data-ptpid=\"isPartDetails ? 'bdbf-1c9e-ba25-8d88' : 'f827-fjb1-fh3b-vj3b'\" v-else>\r\n                        {{ partJobVo.is_apply ? '已报名' : '立即报名' }}\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            <view disableScroll class=\"qts-mask-bg\" v-if=\"successVisible\">\r\n                <view class=\"qts-success-box\">\r\n                    <image class=\"qts-success-pic\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210107_icon.png\"></image>\r\n                    <image\r\n                        @tap=\"closeSuccessDialog\"\r\n                        class=\"qts-success-close ptp_exposure\"\r\n                        :data-ptpid=\"isPartDetails ? '8fdh-zkfb-wyu1-vol1' : '28fh-28f6-fj2b-vm3h'\"\r\n                        mode=\"scaleToFill\"\r\n                        src=\"https://qiniu-image.qtshe.com/20210107_close.png\"\r\n                    ></image>\r\n                    <view class=\"qts-success-title\">{{ (partJobVoClone.jobFeeVO && partJobVoClone.jobFeeVO.rushStatus === 1) ? '抢购成功' : '报名成功' }}</view>\r\n                    <view class=\"qts-success-text\">\r\n                        {{ (partJobVoClone.jobFeeVO && partJobVoClone.jobFeeVO.rushStatus === 1) ? '名额锁定成功，快快联系老师听课吧！' : '客服会马上联系您，请保持电话畅通。' }}\r\n                    </view>\r\n                    \r\n                    <!-- 客服二维码显示 -->\r\n                    <view class=\"qts-service-qrcode\" v-if=\"serviceQrcode\">\r\n                        <image class=\"qts-qrcode-image\" :src=\"serviceQrcode\" mode=\"aspectFit\"></image>\r\n                        <view class=\"qts-qrcode-tips\">{{ serviceTips }}</view>\r\n                    </view>\r\n                    \r\n                    <view @tap=\"copyContact\" class=\"qts-success-button ptp_exposure\" :data-ptpid=\"isPartDetails ? 'fu1n-zlje-27fh-zbf1' : '38fh-20fj-fl2j-73gf'\" v-if=\"contactNo\">\r\n                        复制商家联系方式\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            <pay-dialog\r\n                @cancel=\"cancelPayHandle\"\r\n                @success=\"successPayHandle\"\r\n                :isPartDetails=\"isPartDetails\"\r\n                :money=\"(partJobVo.jobFeeVO && partJobVo.jobFeeVO.feeRushPrice) || 0\"\r\n                :show=\"payVisible\"\r\n            ></pay-dialog>\r\n        </block>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport payDialog from '../../components/payDialog/index';\r\nvar app = getApp();\r\n\r\nexport default {\r\n    components: {\r\n        payDialog\r\n    },\r\n    data() {\r\n        return {\r\n            tips: '',\r\n            applyId: '',\r\n            sendData: {},\r\n            payVisible: false,\r\n            successVisible: false,\r\n            contactWay: '',\r\n            contactNo: '',\r\n            reportVisibleClone: false,\r\n            famousList: [],\r\n            requireList: [],\r\n            partJobVoClone: {\r\n                jobFeeVO: {\r\n                    feeRushPrice: 0,\r\n                    rushStatus: 0\r\n                }\r\n            },\r\n            serviceQrcode: '',\r\n            serviceTips: '扫码添加客服微信，获取更多工作机会'\r\n        };\r\n    },\r\n    created() {\r\n        this.initData();\r\n    },\r\n    props: {\r\n        visible: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        partJobId: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        reportVisible: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        partJobVo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        famousJobList: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        hasLatitude: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        recommendTips: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        authorizedKey: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        shareUserId: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        source: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        isPartDetails: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        fromRecommend: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        agreementVo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        isDirect: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        reportDialogText: {\r\n            type: String,\r\n            default: '报名后需要参与课程学习，技能提升后才会有就业机会哦～'\r\n        }\r\n    },\r\n    beforeMount: function () {},\r\n    methods: {\r\n        initData() {\r\n            this.famousList = this.famousJobList || [];\r\n            if (this.partJobVo && this.partJobVo.requireList) {\r\n                this.requireList = this.partJobVo.requireList;\r\n            }\r\n        },\r\n        cancelPayHandle: function () {},\r\n        successPayHandle: function () {},\r\n        queryMobile: function () {},\r\n        selectJob: function (e) {},\r\n        saveAgreeMent: function () {},\r\n        applyValidate: function () {\r\n            console.log('点击确认报名');\r\n            // 判断是否已报名\r\n            var that = this;\r\n            if (that.partJobVo.is_apply) {\r\n                uni.showToast({\r\n                    title: '您已报名过该职位',\r\n                    icon: 'none'\r\n                });\r\n                that.closeReportDialog();\r\n                return;\r\n            }\r\n            // 调用报名接口\r\n            if (!that.partJobId) {\r\n                uni.showToast({\r\n                    title: '职位ID不能为空',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n            console.log('开始报名，职位ID:', that.partJobId);\r\n            app.post('/apiZhaopin/apply', {\r\n                position_id: that.partJobId\r\n            }, function(res) {\r\n                console.log('报名接口返回:', res);\r\n                \r\n                if(res.status == 1) {\r\n                    // 报名成功 - 不要关闭弹窗，显示成功界面\r\n                    console.log('报名成功，显示成功弹窗');\r\n                    \r\n                    // 先关闭报名弹窗\r\n                    that.reportVisibleClone = false;\r\n                    \r\n                    // 延迟显示成功弹窗\r\n                    setTimeout(() => {\r\n                        that.successVisible = true;\r\n                        // 更新职位状态\r\n                        that.partJobVoClone.hasApply = true;\r\n                        that.partJobVo.is_apply = 1;  // 更新报名状态\r\n                        \r\n                        // 获取客服二维码信息\r\n                        that.getServiceQrcode();\r\n                        \r\n                        // 显示成功提示\r\n                        uni.showToast({\r\n                            title: '报名成功，客服会马上联系您，请保持电话畅通',\r\n                            icon: 'success'\r\n                        });\r\n                    }, 300);\r\n                } else {\r\n                    // 报名失败 - 关闭弹窗并显示错误信息\r\n                    that.closeReportDialog();\r\n                    \r\n                    setTimeout(() => {\r\n                        if(res.msg) {\r\n                            uni.showToast({\r\n                                title: res.msg,\r\n                                icon: 'none'\r\n                            });\r\n                        } else {\r\n                            uni.showToast({\r\n                                title: '报名失败',\r\n                                icon: 'none'\r\n                            });\r\n                        }\r\n                    }, 300);\r\n                }\r\n            });\r\n        },\r\n        goApplyJob: function (t, a, s, o) {},\r\n        normalApply: function () {},\r\n        closeReportDialog: function () {\r\n            console.log('关闭报名弹窗');\r\n            this.reportVisibleClone = false;\r\n            this.successVisible = false;\r\n            // 通知父组件关闭弹窗\r\n            this.$emit('close');\r\n        },\r\n        copyContact: function () {},\r\n        signUserContacted: function () {},\r\n        t(text) {\r\n            if(text=='color1'){\r\n                return getApp().globalData.initdata.color1;\r\n            }else if(text=='color2'){\r\n                return getApp().globalData.initdata.color2;\r\n            }else if(text=='color1rgb'){\r\n                var color1rgb = getApp().globalData.initdata.color1rgb;\r\n                return color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n            }else if(text=='color2rgb'){\r\n                var color2rgb = getApp().globalData.initdata.color2rgb;\r\n                return color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n            }else{\r\n                return getApp().globalData.initdata.textset[text] || text;\r\n            }\r\n        },\r\n        getServiceQrcode: function () {\r\n            var that = this;\r\n            app.get('/apiZhaopin/getServiceQrcode', {}, function(res) {\r\n                console.log('获取客服二维码信息:', res);\r\n                if(res.status == 1 && res.data) {\r\n                    that.serviceQrcode = res.data.service_qrcode || '';\r\n                    that.serviceTips = res.data.service_tips || '扫码添加客服微信，获取更多工作机会';\r\n                }\r\n            });\r\n        },\r\n        closeSuccessDialog: function () {\r\n            console.log('关闭成功弹窗');\r\n            this.successVisible = false;\r\n            this.closeReportDialog();\r\n        }\r\n    },\r\n    watch: {\r\n        reportVisible: {\r\n            handler: function (newVal, oldVal) {\r\n                console.log('reportDialog - visible变化:', newVal);\r\n                this.reportVisibleClone = newVal;\r\n            },\r\n            immediate: true\r\n        },\r\n        visible: {\r\n            handler: function (newVal) {\r\n                console.log('reportDialog - visible变化:', newVal);\r\n                this.reportVisibleClone = newVal;\r\n                if (newVal) {\r\n                    this.initData();\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        famousJobList: {\r\n            handler: function (newVal) {\r\n                console.log('reportDialog - famousJobList变化:', newVal);\r\n                if (Array.isArray(newVal)) {\r\n                    this.famousList = newVal;\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        partJobVo: {\r\n            handler: function (newVal) {\r\n                if (newVal) {\r\n                    this.partJobVoClone = {\r\n                        ...newVal,\r\n                        jobFeeVO: newVal.jobFeeVO || {\r\n                            feeRushPrice: 0,\r\n                            rushStatus: 0\r\n                        }\r\n                    };\r\n                    if (newVal.requireList) {\r\n                        this.requireList = newVal.requireList;\r\n                    }\r\n                }\r\n            },\r\n            immediate: true,\r\n            deep: true\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style>\r\n\t@import './index.scss';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066349\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}