{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?3802", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?4693", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?90a9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?546f", "uni-app:///pagesExb/daxuepage/components/line.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?b2ed", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/line.vue?1fe4"], "names": ["props", "dax<PERSON><PERSON>", "data", "loading", "oldData", "array", "arrayYear", "arrayGroup", "arrayBatch", "arrayType", "arraySubject", "index", "college", "areaName", "year", "group", "speciality", "batch", "classify", "type", "upgradation", "subject", "created", "console", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "areaChange", "yearChange", "groupChange", "batchChange", "typeChange", "subjectChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6M7wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAEA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAD;MACAE;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;QACAC;QACAC;QACAC;MACA;MACAC;QAAA;QACAH;QACAC;QACAC;QACAE;MACA;MACAC;QAAA;QACAL;QACAC;QACAK;MACA;MACAC;QAAA;QACAP;QACAC;QACAO;MACA;IAEA;EACA;EACAC;IACA;IACA;MACAC;MACA;MACA;IACA;IACA;EACA;EACAC,8BAEA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACA5B;MACA;QACA2B;QACAA;QACAA;MACA;IACA;IACA;IACAE;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;MAEA;QAAA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;MAEA;QAAA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;MAEA;QAAA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;MAEA;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5YA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/daxuepage/components/line.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./line.vue?vue&type=template&id=5bb42a62&\"\nvar renderjs\nimport script from \"./line.vue?vue&type=script&lang=js&\"\nexport * from \"./line.vue?vue&type=script&lang=js&\"\nimport style0 from \"./line.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/daxuepage/components/line.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./line.vue?vue&type=template&id=5bb42a62&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./line.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./line.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block>\r\n\t\t\t<view class=\"view-show\" style=\"padding: 30rpx;padding-bottom: 200rpx;\">\r\n\t\t\t\t<!-- 高考分数线 -->\r\n\t\t\t\t<view style=\"margin-bottom: 30rpx;\">\r\n\r\n\t\t\t\t\t<view style=\"display: flex;align-items: center;margin-bottom: 40rpx;\">\r\n\t\t\t\t\t\t<view style=\"height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;\"></view>\r\n\t\t\t\t\t\t<span style=\"font-weight: bold;font-size: 36rpx;\">高考分数线：</span>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"display: flex;margin-bottom: 20rpx;\">\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"areaChange($event,'1')\" :value=\"index\" :range=\"array\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{college.areaName}} </view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"yearChange($event,'1')\" :value=\"index\" :range=\"arrayYear\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{college.year}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"groupChange($event,'1')\" :value=\"index\" :range=\"arrayGroup\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{college.group}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 表格 -->\r\n\t\t\t\t\t<view class=\"table-box\">\r\n\t\t\t\t\t\t<!-- 表头 -->\r\n\t\t\t\t\t\t<view class=\"table-header\">\r\n\t\t\t\t\t\t\t<view>录取批次</view>\r\n\t\t\t\t\t\t\t<view>最低分/位次</view>\r\n\t\t\t\t\t\t\t<view>省控线</view>\r\n\t\t\t\t\t\t\t<view>招生类型</view>\r\n\t\t\t\t\t\t\t<view>选科要求</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"table-content\" v-for=\"item in data['高考分数线']\">\r\n\t\t\t\t\t\t\t<view>{{item.batch}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.min_score}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.control_line}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.admission_type}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.requirement}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 专业分数线 -->\r\n\t\t\t\t<view style=\"margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t<view style=\"display: flex;align-items: center;margin-bottom: 40rpx;\">\r\n\t\t\t\t\t\t<view style=\"height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;\"></view>\r\n\t\t\t\t\t\t<span style=\"font-weight: bold;font-size: 36rpx;\">专业分数线：</span>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"display: flex;margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"areaChange($event,'2')\" :value=\"index\" :range=\"array\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{speciality.areaName}} </view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"yearChange($event,'2')\" :value=\"index\" :range=\"arrayYear\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{speciality.year}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"groupChange($event,'2')\" :value=\"index\" :range=\"arrayGroup\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{speciality.group}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"batchChange($event,'2')\" :value=\"index\" :range=\"arrayBatch\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{speciality.batch}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 表格 -->\r\n\t\t\t\t\t<view class=\"table-box\">\r\n\t\t\t\t\t\t<!-- 表头 -->\r\n\t\t\t\t\t\t<view class=\"table-header\">\r\n\t\t\t\t\t\t\t<view>专业名称</view>\r\n\t\t\t\t\t\t\t<view>选科要求</view>\r\n\t\t\t\t\t\t\t<view>最低分/位次</view>\r\n\t\t\t\t\t\t\t<view>录取批次</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"table-content\" v-for=\"item in data['专业分数线']\">\r\n\t\t\t\t\t\t\t<view>{{item.zhuanye_name}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.requirement}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.min_score}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.batch}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 分类单招分数线 -->\r\n\t\t\t\t<view style=\"margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t<view style=\"display: flex;align-items: center;margin-bottom: 40rpx;\">\r\n\t\t\t\t\t\t<view style=\"height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;\"></view>\r\n\t\t\t\t\t\t<span style=\"font-weight: bold;font-size: 36rpx;\">分类单招分数线：</span>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"display: flex;margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"areaChange($event,'3')\" :value=\"index\" :range=\"array\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{classify.areaName}} </view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"yearChange($event,'3')\" :value=\"index\" :range=\"arrayYear\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{classify.year}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"typeChange($event,'3')\" :value=\"index\" :range=\"arrayType\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{classify.type}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 表格 -->\r\n\t\t\t\t\t<view class=\"table-box\">\r\n\t\t\t\t\t\t<!-- 表头 -->\r\n\t\t\t\t\t\t<view class=\"table-header\">\r\n\t\t\t\t\t\t\t<view>录取批次</view>\r\n\t\t\t\t\t\t\t<view>最低分/位次</view>\r\n\t\t\t\t\t\t\t<view>省控线</view>\r\n\t\t\t\t\t\t\t<view>招生类型</view>\r\n\t\t\t\t\t\t\t<view>选科要求</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"table-content\" v-for=\"item in data['分类单招分数线']\">\r\n\t\t\t\t\t\t\t<view>{{item.batch}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.min_score}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.control_line}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.admission_type}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.requirement}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!-- 专升本分数线 -->\r\n\t\t\t\t<view style=\"margin-bottom: 20rpx;\">\r\n\t\t\t\t\r\n\t\t\t\t\t<view style=\"display: flex;align-items: center;margin-bottom: 40rpx;\">\r\n\t\t\t\t\t\t<view style=\"height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;\"></view>\r\n\t\t\t\t\t\t<span style=\"font-weight: bold;font-size: 36rpx;\">专升本分数线：</span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t<view style=\"display: flex;margin-bottom: 20rpx;\">\r\n\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"areaChange($event,'4')\" :value=\"index\" :range=\"array\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{upgradation.areaName}} </view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"yearChange($event,'4')\" :value=\"index\" :range=\"arrayYear\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{upgradation.year}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"tag-item\" style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t<picker @change=\"subjectChange($event,'4')\" :value=\"index\" :range=\"arraySubject\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-input\">{{upgradation.subject}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 表格 -->\r\n\t\t\t\t\t<view class=\"table-box\">\r\n\t\t\t\t\t\t<!-- 表头 -->\r\n\t\t\t\t\t\t<view class=\"table-header\">\r\n\t\t\t\t\t\t\t<view>录取批次</view>\r\n\t\t\t\t\t\t\t<view>最低分/位次</view>\r\n\t\t\t\t\t\t\t<view>省控线</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"table-content\" v-for=\"item in data['专升本分数线']\">\r\n\t\t\t\t\t\t\t<view>{{item.batch}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.min_score}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.control_line}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\timport area from '../common/area.js'\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tdaxueid: ''\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\tdata: [],\r\n\t\t\t\toldData: [],\r\n\t\t\t\tarray: JSON.parse(JSON.stringify(area.provinces)), // 地区\r\n\t\t\t\tarrayYear: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],\r\n\t\t\t\tarrayGroup: ['物理', '历史'],\r\n\t\t\t\tarrayBatch: ['本科提前批', '本科一批', '本科二批', '专科一批', '专科二批'],\r\n\t\t\t\tarrayType: ['普高', '中职'],\r\n\t\t\t\tarraySubject:['理科','文科'],\r\n\t\t\t\tindex: 0,\r\n\t\t\t\tcollege: { // 高考分数线筛选条件\r\n\t\t\t\t\tareaName: '全部',\r\n\t\t\t\t\tyear: '2023',\r\n\t\t\t\t\tgroup: '历史'\r\n\t\t\t\t},\r\n\t\t\t\tspeciality: { // 专业分数线筛选条件\r\n\t\t\t\t\tareaName: '全部',\r\n\t\t\t\t\tyear: '2023',\r\n\t\t\t\t\tgroup: '历史',\r\n\t\t\t\t\tbatch: '本科一批'\r\n\t\t\t\t},\r\n\t\t\t\tclassify: { // 分类单招分数线筛选条件\r\n\t\t\t\t\tareaName: '全部',\r\n\t\t\t\t\tyear: '2023',\r\n\t\t\t\t\ttype: '普高'\r\n\t\t\t\t},\r\n\t\t\t\tupgradation: { // 专升本分数线筛选条件\r\n\t\t\t\t\tareaName: '全部',\r\n\t\t\t\t\tyear: '2023',\r\n\t\t\t\t\tsubject: '理科'\r\n\t\t\t\t}\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getdata();\r\n\t\t\tthis.array = this.array.map(item => {\r\n\t\t\t\tconsole.log('item', item)\r\n\t\t\t\t// 替换掉字符串中的“市”和“省”\r\n\t\t\t\treturn item.name.replace(/市|省/g, '');\r\n\t\t\t});\r\n\t\t\tthis.array.unshift('全部')\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiDaxue/fenshuxianlist', {\r\n\t\t\t\t\tdaxueid: that.daxueid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.data = res.list;\r\n\t\t\t\t\tthat.oldData = JSON.parse(JSON.stringify(res.list))\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 地区筛选\r\n\t\t\tareaChange: function(e, type) {\r\n\t\t\t\tconst index = e.detail.value\r\n\t\t\t\tif (type == '1') { // 高考分数线\r\n\t\t\t\t\tthis.college.areaName = this.array[index]\r\n\t\t\t\t\tif (this.college.areaName == '全部') {\r\n\t\t\t\t\t\tthis.data['高考分数线'] = this.oldData['高考分数线']\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.province == this.college.areaName\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (type == '2') { // 专业分数线\r\n\t\t\t\t\tthis.speciality.areaName = this.array[index]\r\n\t\t\t\t\tif (this.speciality.areaName == '全部') {\r\n\t\t\t\t\t\tthis.data['专业分数线'] = this.oldData['专业分数线']\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.province == this.speciality.areaName\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (type == '3') { // 分类单招分数线\r\n\t\t\t\t\tthis.classify.areaName = this.array[index]\r\n\t\t\t\t\tif (this.classify.areaName == '全部') {\r\n\t\t\t\t\t\tthis.data['分类单招分数线'] = this.oldData['分类单招分数线']\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.province == this.classify.areaName\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (type == '4') { // 专升本分数线\r\n\t\t\t\t\tthis.upgradation.areaName = this.array[index]\r\n\t\t\t\t\tif (this.upgradation.areaName == '全部') {\r\n\t\t\t\t\t\tthis.data['专升本分数线'] = this.oldData['专升本分数线']\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.province == this.upgradation.areaName\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 年份筛选\r\n\t\t\tyearChange: function(e,type) {\r\n\t\t\t\tconst year = this.arrayYear[e.detail.value]\r\n\t\t\t\tif (type == '1') { // 高考分数线\r\n\t\t\t\t\tthis.college.year = year\r\n\t\t\t\t\tthis.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.year == this.college.year\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (type == '2') { // 专业分数线\r\n\t\t\t\t\tthis.speciality.year = year\r\n\t\t\t\t\tthis.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.year == this.speciality.year\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (type == '3') { // 分类单招分数线\r\n\t\t\t\t\tthis.classify.year = year\r\n\t\t\t\t\tthis.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.year == this.classify.year\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (type == '4') { // 专升本分数线\r\n\t\t\t\t\tthis.upgradation.year = year\r\n\t\t\t\t\tthis.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.year == this.upgradation.year\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 组筛选\r\n\t\t\tgroupChange: function(e,type) {\r\n\t\t\t\tconst group = this.arrayGroup[e.detail.value]\r\n\t\t\t\tif (type == '1') { // 高考分数线\r\n\t\t\t\t\tthis.college.group = group\r\n\t\t\t\t\tthis.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.subject_choice == this.college.group\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (type == '2') { // 专业分数线\r\n\t\t\t\t\tthis.speciality.group = group\r\n\t\t\t\t\tthis.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.subject_choice == this.speciality.group\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 批次 筛选\r\n\t\t\tbatchChange: function(e,type) {\r\n\t\t\t\tconst batch = this.arrayBatch[e.detail.value]\r\n\t\t\t\tif (type == '2') { // 专业分数线\r\n\t\t\t\t\tthis.speciality.batch = batch\r\n\t\t\t\t\tthis.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.batch == this.speciality.batch\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 普高', '中职'筛选\r\n\t\t\ttypeChange: function(e,type) {\r\n\t\t\t\tconst type1 = this.arrayType[e.detail.value]\r\n\t\t\t\tif (type == '3') { // 分类单招分数线\r\n\t\t\t\t\tthis.classify.type = type1\r\n\t\t\t\t\tthis.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.admission_type == this.classify.type\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 科目，理科，文科\r\n\t\t\tsubjectChange:function(e,type){\r\n\t\t\t\tconst type1 = this.arraySubject[e.detail.value]\r\n\t\t\t\tif (type == '4') { // 专升本分数线\r\n\t\t\t\t\tthis.upgradation.subject = type1\r\n\t\t\t\t\tthis.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {\r\n\t\t\t\t\t\treturn item.admission_type == this.speciality.type\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.tag-item {\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 2rpx 8rpx;\r\n\t\tborder: 2rpx solid #d7d7d7;\r\n\t\tcolor: #686868;\r\n\t}\r\n\r\n\t.table-box {\r\n\t\tborder-radius: 26rpx;\r\n\t\tbox-shadow: 1rpx 1rpx 10rpx 0 rgba(0, 0, 0, 0.2);\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\r\n\t.table-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-around;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #565656;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tborder-bottom: 2rpx solid #d7d7d7;\r\n\t}\r\n\r\n\t.table-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-around;\r\n\t\tcolor: #565656;\r\n\t\tpadding-top: 20rpx;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tborder-bottom: 2rpx solid #d7d7d7;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./line.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./line.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115033908\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}