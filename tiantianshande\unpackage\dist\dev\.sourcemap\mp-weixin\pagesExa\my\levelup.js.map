{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?5c9f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?7463", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?646e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?ef10", "uni-app:///pagesExa/my/levelup.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?503b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/levelup.vue?cd6e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "levelid", "loading", "isload", "menuindex", "pre_url", "editorFormdata", "regiondata", "items", "provincedata", "citydata", "test", "sysset", "userinfo", "aglevelList", "aglevelCount", "applytj_reach", "errmsg", "userlevel", "selectedLevel", "explain", "applytj_info", "autouptj_info", "areafenhong_province", "areafenhong_city", "areafenhong_area", "largeareaindex", "largearea", "changeState", "levelupcode", "bgset", "type", "ycode", "nolevel", "showxieyi3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "width", "height", "points", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "text", "value", "citys", "children", "onPullDownRefresh", "getdata", "id", "cid", "title", "setTimeout", "cannotapply", "bindBanknameChange", "formSubmit", "formdata", "changelevel", "agleveldata", "editorChooseImage", "console", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "onchange", "onchange1", "onchange2", "onchange3", "largeareaBindPickerChange", "applycode", "inputycode", "getlevel", "getSign", "showxieyiFun3", "handleCancel", "handleReset", "self", "tempPoint", "touchstart", "X", "Y", "touchmove", "touchend", "draw", "handleConfirm", "icon", "duration", "canvasId", "ctx", "filePath", "name", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyOhxB;AACA;AACA;AACA;AACA;AACA;AAAA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACA3C;QACA6C;QACAC;UAAA;QAAA;QACAC;UACAC;UACA;UACA;YACAtC;cAAAuC;cAAAC;YAAA;UACA;UACAF;UACA;UACA;YACA;YACA;cACAG;gBAAAF;gBAAAC;cAAA;YACA;YACAvC;cAAAsC;cAAAC;cAAAE;YAAA;UACA;UACAJ;QACA;MACA;IACA;IAEA;;IAEA;IACAA;IACA;IACA;IACA;IACA;IACA;IAEAJ;MACAG;QACAC;QACAA;MACA;IACA;;IAEA;IACA;EACA;EACAK;IACA;EACA;AAAA,6EACA;EACA;AACA,oEACA;EACAC;IACA;IACAN;IACAN;MAAAa;MAAAC;MAAAtD;IAAA;MACA8C;MACAJ;QACAa;MACA;MACA;QACAT;MACA;QACAA;QACAU;UACAhB;QACA;MACA;QACAM;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACA;YACA;cACAJ;gBACAa;cACA;YACA;YACAT;UACA;QACA;QACA;MACA;MACAA;IACA;EACA;EACAW;IACAjB;EACA;EACAkB;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACA;MACA;QACAnB;QAAA;MACA;MACA;QACAoB;MACA;IACA;IACA;MACApB;MAAA;IACA;IACA;MACAA;MAAA;IACA;IACA;MACAA;MAAA;IACA;IACA;MACAA;MAAA;IACA;IACA;MACAA;MACA;IACA;;IAEA;IACA;MACAA;MACA;IACA;IAEAoB;IACAA;IACAA;IACA;MACAA;IACA;IACA;MACAA;MACA;QACA;QACAA;QACAA;MACA;QACA;QACA;QACA;UACA;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;;IAEA;IACA;MACAA;IACA;IAEApB;IACAA;MACAA;MACA;QACAA;QACA;MACA;MACAA;MACAgB;QACAhB;MACA;IACA;EACA;EACAqB;IACA;IACA;IACA;IACA;IAEA;MACA;QACAC;QACA;MACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAvB;MACAnC;MACA2D;MACAlB;MACAA;IACA;EACA;EACAmB;IACA;IACA;IACA;IACA;IACA;IACA5D;IACA2D;IACA;IACA;EACA;EACAE;IACA;IACAF;IACA;EACA;EACAG;IACA;IACAH;IACA;EACA;EACAI;IACA;IACAJ;IACA;IACA;EACA;EACAK;IACA;IACA;IACA;IACA;EACA;EACAC;IACAN;IACA;EACA;EACAO;IACA;IACA;IAEA;IACA;IACA;IAEA;MACA;QACA;UACA1D;QACA;MACA;IACA;IACAiC;EACA;EACA0B;IACA;IACA;IACA1B;EACA;EACA2B;IACA;IAEA;IACA;MACAjC;MACA;IACA;IAEAM;IACAN;MAAAa;MAAAC;MAAAtD;MAAA+B;IAAA;MACAe;MACA;QACAA;MACA;QACAA;QACAU;UACAhB;QACA;MACA;QACAM;QACAA;QACA;UACAA;UACA;UACAA;UACAA;QACA;UACAA;UACA;YACA;cACA;YACA;UACA;UACAA;QACA;MACA;MACAA;IACA;EACA;EACA;EACA4B;IACA;IACAlC;MACA;QACAM;MACA;IACA;EACA;EAEA6B;IACA;EACA;EAEAC;IACA;EACA;EAEA;EACAC;IACA;IACAC;IACAA;IACAC;EACA;EAEA;EACAC;IACA;IACA;IACA;MACAC;MACAC;IACA;IAEA;IACA;;IAEA;IACA;EACA;EAEA;EACAC;IACA;IACA;IACA;MACAF;MACAC;IACA;IACA;IACA;IACA;MACA;IACA;;IACAH;EACA;EAEA;EACAK;IACA;EACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EAEA;EACAC;IACA;;IAEA;MACA5C;QACAa;QACAgC;QACAC;MACA;MACA;IACA;;IAEA;IACA9C;MACA+C;MACA5C;QACA;QAEA;QACA6C;QACAA;QACAA;QACAA;QAEAlC;UACA;UACAd;YACA+C;YACA5C;cACA;;cAEA;cACAH;gBACAD;gBACAkD;gBACAC;gBACA/C;kBACA;kBACA;oBACA;oBACAiC;oBACAtC;oBACAsC;kBACA;oBACAtC;kBACA;gBACA;gBACAqD;kBACArD;gBACA;cACA;YACA;YACAqD;cACA7B;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnvBA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/levelup.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/levelup.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./levelup.vue?vue&type=template&id=ed59aeba&\"\nvar renderjs\nimport script from \"./levelup.vue?vue&type=script&lang=js&\"\nexport * from \"./levelup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./levelup.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/levelup.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./levelup.vue?vue&type=template&id=ed59aeba&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./levelup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./levelup.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" :style=\"bgset && bgset.bgcolor?'background-color:'+bgset.bgcolor:''\">\r\n\t<block v-if=\"errmsg=='' && isload\">\r\n\t\t<image :src=\"bgset && bgset.bgimg?bgset.bgimg:pre_url + '/static/img/lv-upbanner.png'\" class=\"banner\" mode=\"widthFix\"></image>\r\n\t\t\r\n\t\t<view class=\"contentbox\">\r\n\t\t\t<view class=\"title\">欢迎加入{{sysset.name}}</view>\r\n\t\t\t<view class=\"title\">您的当前{{bgset && bgset.level_name?bgset.level_name:'等级'}}：<text style=\"font-weight:bold\">{{userlevel.name}}</text></view>\r\n\t\t</view>\r\n\t\t<form @submit=\"formSubmit\">\r\n        <view v-if=\"type =='ycode'\">\r\n            <view class=\"contentbox\" >\r\n            \t<view class=\"form-item1\">\r\n                     <view style=\"width: 100%;overflow: hidden;margin: 10rpx 0;\">\r\n                        <input @input=\"inputycode\"  placeholder=\"输入验证码\" style=\"width: 300rpx;float:left;line-height: 60rpx;border: 2rpx solid #eee;height: 60rpx;padding-left: 20rpx;\"/>\r\n                        <view @tap=\"getlevel\" style=\"width: 140rpx;float:right;line-height: 60rpx;border: 2rpx solid #eee;height: 60rpx;background-color: red;text-align: center;color: #fff;\">\r\n                            确认\r\n                        </view>\r\n                     </view>\r\n            \t</view>\r\n            </view>\r\n            <view class=\"contentbox\" v-if=\"nolevel\">\r\n            \t<view class=\"noup\">\r\n                    <text class=\"fa fa-check\"></text> \r\n                    <text>等级不存在或不符合此级别升级条件</text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\t\t<view class=\"contentbox\" v-else>\r\n\t\t\t<view class=\"form-item1\" v-if=\"aglevelCount>0\">\r\n\t\t\t\t <view class=\"panel\">请选择升级{{bgset && bgset.level_name?bgset.level_name:'等级'}}：</view>\r\n\t\t\t\t <radio-group @change=\"changelevel\" name=\"levelid\">\r\n                     <block v-for=\"(item, idx) in aglevelList\" :key=\"idx\">\r\n                         <label class=\"radio-item\">\r\n                                <view class=\"flex1\"><text style=\"font-weight:bold\">{{item.name}}</text></view>\r\n                                <!-- <text>{{item.apply_paymoney>0 ? '加盟费'+item.apply_paymoney+'元':''}}</text> -->\r\n                                <radio :value=\"item.id+''\" v-if=\"item.id!=userlevel.id\"></radio>\r\n                                <text class=\"curlevel_tag\" v-if=\"item.id==userlevel.id\">当前等级</text>\r\n                         </label>\r\n                         <view v-if=\"item.apply_code\" style=\"width: 100%;overflow: hidden;margin: 10rpx 0;\">\r\n                            <input @input=\"applycode\" :data-index=\"idx\" :data-id=\"item.id\" placeholder=\"输入验证码\" style=\"width: 300rpx;float:left;line-height: 60rpx;border: 2rpx solid #eee;height: 60rpx;padding-left: 20rpx;\"/>\r\n                         </view>\r\n                     </block>\r\n\t\t\t\t </radio-group>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"noup\">\r\n                <text class=\"fa fa-check\"></text> \r\n                <block v-if=\"levelid>0\">\r\n                    <text v-if=\"levelid ==userinfo.levelid\">您已达到此级别</text>\r\n                    <text v-else>暂不符合此级别升级条件</text>\r\n                </block>\r\n                <block v-else>\r\n                    <text>您已达到最高可升级级别</text>\r\n                </block>\r\n            </view>\r\n\t\t</view>\r\n\t\t<view class=\"contentbox\" v-if=\"!nolevel && selectedLevel.can_apply==1 && selectedLevel.id!=userinfo.levelid\">\r\n\t\t\t<view class=\"applytj\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<text>{{selectedLevel.name}}</text>\r\n\t\t\t\t\t<text class=\"t2\">申请条件：</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\" v-if=\"selectedLevel.applytj!=''\">{{selectedLevel.applytj}}</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"selectedLevel.apply_paymoney>0\">{{selectedLevel.apply_paytxt}}：￥{{selectedLevel.apply_paymoney}}</view>\r\n\t\t\t\t\t<view class=\"t3\" v-if=\"selectedLevel.applytj_reach==0\">您暂未达到申请条件，请继续努力！</view>\r\n\t\t\t\t\t<view class=\"t4\" v-if=\"selectedLevel.applytj_reach==1\"><text v-if=\"selectedLevel.applytj!=''\">您已达到申请条件，</text>请填写申请资料</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"applydata\" v-if=\"selectedLevel.applytj_reach==1\">\r\n\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in selectedLevel.apply_formdata\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t<textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t<radio-group class=\"flex\" :name=\"'form'+idx\" style=\"flex-wrap:wrap\">\r\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"flex\" style=\"flex-wrap:wrap\">\r\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t\t\t<uni-data-picker :localdata=\"items\" popup-title=\"请选择省市区\" @change=\"onchange\" styleData=\"width:100%\"></uni-data-picker>\r\n\t\t\t\t\t\t<!-- <picker class=\"picker\" mode=\"region\" :name=\"'form'+idx\" value=\"\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view> \r\n\t\t\t\t\t\t\t<view v-else>请选择省市区</view>\r\n\t\t\t\t\t\t</picker> -->\r\n\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"regiondata\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\" :data-idx=\"idx\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"selectedLevel.areafenhong == 1\">\r\n\t\t\t\t\t<view class=\"label\">代理区域<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<uni-data-picker :localdata=\"provincedata\" popup-title=\"请选择代理区域\" :placeholder=\"areafenhong_province || '请选择代理区域'\" @change=\"onchange1\" styleData=\"width:100%\"></uni-data-picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"selectedLevel.areafenhong == 2\">\r\n\t\t\t\t\t<view class=\"label\">代理区域<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<uni-data-picker :localdata=\"citydata\" popup-title=\"请选择代理区域\" :placeholder=\"areafenhong_city ? areafenhong_province + '/' + areafenhong_city : '请选择代理区域'\" @change=\"onchange2\" styleData=\"width:100%\"></uni-data-picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"selectedLevel.areafenhong == 3\">\r\n\t\t\t\t\t<view class=\"label\">代理区域<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<uni-data-picker :localdata=\"items\" popup-title=\"请选择代理区域\" :placeholder=\"areafenhong_area ? areafenhong_province + '/' + areafenhong_city + '/' + areafenhong_area : '请选择代理区域'\" @change=\"onchange3\" styleData=\"width:100%\"></uni-data-picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"selectedLevel.areafenhong == 10\">\r\n\t\t\t\t\t<view class=\"label\">代理区域<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" value=\"\" :range=\"largearea\" @change=\"largeareaBindPickerChange\">\r\n\t\t\t\t\t\t<view v-if=\"largeareaindex!=-1\">{{largearea[largeareaindex]}}</view>\r\n\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 添加签名区域 -->\r\n\t\t\t\t<view class=\"form-item\" v-if=\"selectedLevel.need_signature == 1\">\r\n\t\t\t\t\t<view class=\"label\">协议签名<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"qianmingurl\">\r\n\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"qianmingurl\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sign-btn\" @tap=\"showxieyiFun3\">\r\n\t\t\t\t\t\t\t{{qianmingurl ? '重新签名' : '点击签名'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 签约协议内容和说明 -->\r\n\t\t\t\t<view class=\"form-item\" v-if=\"content && selectedLevel.need_signature == 1\">\r\n\t\t\t\t\t<view class=\"label\">协议内容</view>\r\n\t\t\t\t\t<view class=\"agreement-content\">\r\n\t\t\t\t\t\t<parse :content=\"content\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"form-btn\" form-type=\"submit\" v-if=\"selectedLevel.applytj_reach==1\">申请成为{{selectedLevel.name}}</button>\r\n\t\t</view>\r\n\t\t</form>\r\n\r\n\t\t<!-- 签约弹窗 -->\r\n\t\t<view v-if=\"showxieyi3\" class=\"xieyibox\">\r\n\t\t\t<view class=\"container3\">\r\n\t\t\t\t<view class=\"sigh-btns\">\r\n\t\t\t\t\t<button class=\"btn\" @tap=\"handleCancel\" style=\"background:#F56C6C;padding:10px;color:#fff;\">取消</button>\r\n\t\t\t\t\t<button class=\"btn\" @tap=\"handleReset\" style=\"background:#F56C6C;padding:10px;color:#fff;\">重写</button>\r\n\t\t\t\t\t<button class=\"btn\" @tap=\"handleConfirm\" style=\"background:#F56C6C;padding:10px;color:#fff;\">确认</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sign-box\">\r\n\t\t\t\t\t<canvas class=\"mycanvas\" :style=\"{width:width +'px',height:height +'px'}\" canvas-id=\"mycanvas\"\r\n\t\t\t\t\t\t@touchstart=\"touchstart\" @touchmove=\"touchmove\" @touchend=\"touchend\"></canvas>\r\n\t\t\t\t\t<canvas canvas-id=\"camCacnvs\" :style=\"{width:height +'px',height:width +'px'}\"\r\n\t\t\t\t\t\tclass=\"canvsborder\"></canvas>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"contentbox\" v-if=\"!nolevel && selectedLevel.can_up==1 && selectedLevel.up_condition_show == 1 && selectedLevel.id!=userinfo.levelid\">\r\n\t\t\t<view class=\"uplvtj\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<text>{{selectedLevel.name}}</text>\r\n\t\t\t\t\t<text class=\"t2\">升级条件：</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"changeState\" class=\"f2\">\r\n\t\t\t\t\t<parse :content=\"selectedLevel.autouptj\" />\r\n\t\t\t\t\t<view class=\"t3\">您达到升级条件后将自动升级为{{selectedLevel.name}}，请继续努力！</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"contentbox\">\r\n\t\t\t<view class=\"explain\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<text>{{selectedLevel.name}}</text>\r\n\t\t\t\t\t<text class=\"t2\">{{bgset && bgset.level_name?bgset.level_name:'等级'}}特权：</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<parse :content=\"userlevel.explain\" v-if=\"userlevel.id==selectedLevel.id\"/>\r\n\t\t\t\t\t<block v-for=\"(item,index) in aglevelList\">\r\n\t\t\t\t\t<parse :content=\"item.explain\" v-if=\"item.id==selectedLevel.id\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<block v-if=\"errmsg!='' && isload\">\r\n\t<view class=\"zan-box\">\r\n\t\t\t<image src=\"/static/img/zan.png\" class=\"zan-img\"></image>\r\n\t\t\t<view class=\"zan-text\">{{errmsg}}</view>\r\n\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar x = 20;\r\nvar y = 20;\r\nvar tempPoint = []; // 用来存放当前画纸上的轨迹点\r\nlet canvasw;\r\nlet canvash;\r\nexport default {\r\n  data() {\r\n    return {\r\n        opt:{},\r\n        levelid:0,\r\n        loading:false,\r\n        isload: false,\r\n        menuindex:-1,\r\n\r\n        pre_url:app.globalData.pre_url,\r\n        editorFormdata:[],\r\n        regiondata:'',\r\n        items: [],\r\n        provincedata:[],\r\n        citydata:[],\r\n        test:'test',\r\n\r\n        sysset: [],\r\n        userinfo: [],\r\n        aglevelList: [],\r\n        aglevelCount: 0,\r\n        applytj_reach: 0,\r\n        errmsg: '',\r\n        userlevel: \"\",\r\n        selectedLevel: \"\",\r\n        explain: \"\",\r\n        applytj_info: \"\",\r\n        autouptj_info: \"\",\r\n        areafenhong_province:'',\r\n        areafenhong_city:'',\r\n        areafenhong_area:'',\r\n        largeareaindex:-1,\r\n        largearea:[],\r\n        changeState: true,\r\n        levelupcode:false,\r\n        bgset:'',\r\n        type:0,\r\n        ycode:'',\r\n        nolevel:false,\r\n        // 签约相关属性\r\n        showxieyi3: false,\r\n        qianmingurl: '',\r\n        content: '',\r\n        width: 0,\r\n        height: 0,\r\n        points: [] // 路径点集合\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n        if(this.opt && this.opt.levelid){\r\n            this.levelid = this.opt.levelid;\r\n        }\r\n        if(this.opt && this.opt.type){\r\n            this.type = this.opt.type;\r\n        }\r\n\t\tvar that = this;\r\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\tif(customs.data.includes('plug_zhiming')) {\r\n\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\r\n\t\t\t}\r\n\t\t\tuni.request({\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: {},\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\tvar provincedata = [];\r\n\t\t\t\t\tfor(var i in res2.data){\r\n\t\t\t\t\t\tprovincedata.push({text:res2.data[i].text,value:res2.data[i].value})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.provincedata = provincedata;\r\n\t\t\t\t\tvar citydata = [];\r\n\t\t\t\t\tfor(var i in res2.data){\r\n\t\t\t\t\t\tvar citys = [];\r\n\t\t\t\t\t\tfor(var j in res2.data[i].children){\r\n\t\t\t\t\t\t\tcitys.push({text:res2.data[i].children[j].text,value:res2.data[i].children[j].value});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tcitydata.push({text:res2.data[i].text,value:res2.data[i].value,children:citys});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.citydata = citydata;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t\t\r\n\t\tthis.getdata();\r\n        \r\n        // 初始化签约画布\r\n        that = this;\r\n        this.ctx = uni.createCanvasContext('mycanvas', this); // 创建绘图对象\r\n        // 设置画笔样式\r\n        this.ctx.lineWidth = 4;\r\n        this.ctx.lineCap = 'round';\r\n        this.ctx.lineJoin = 'round';\r\n\r\n        uni.getSystemInfo({\r\n            success: function(res) {\r\n                that.width = res.windowWidth * 0.8;\r\n                that.height = res.windowHeight * 0.85;\r\n            }\r\n        });\r\n        \r\n        // 获取签约内容\r\n        this.getSign();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata(true);\r\n\t},\r\n  onPullDownRefresh: function () {\r\n    this.getdata(true);\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiMy/levelup', {id:that.opt.id,cid:that.opt.cid,levelid:that.levelid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.t('会员') + '升级'\r\n\t\t\t\t});\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tthat.errmsg = res.msg;\r\n\t\t\t\t} else if (res.status == 2) {\r\n\t\t\t\t\tthat.errmsg = res.msg;\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goto('index');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\t\tthat.bankname = res.userinfo.bankname;\r\n\t\t\t\t\tthat.userlevel = res.userlevel;\r\n\t\t\t\t\tthat.selectedLevel = res.userlevel;\r\n\t\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\t\tthat.aglevelList = res.aglevelList;\r\n\t\t\t\t\tthat.aglevelCount = res.aglevelList.length;\r\n\t\t\t\t\tthat.explain = res.userlevel.explain;\r\n                    if(res.levelupcode){\r\n                        that.levelupcode = res.levelupcode;\r\n                        if(res.bgset){\r\n                            if(res.bgset.title){\r\n                                uni.setNavigationBarTitle({\r\n                                \ttitle: res.bgset.title\r\n                                });\r\n                            }\r\n                            that.bgset = res.bgset;\r\n                        }\r\n                    }\r\n\t\t\t\t\tif(res.largearea) that.largearea = res.largearea;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    cannotapply: function () {\r\n      app.alert('不满足申请条件');\r\n    },\r\n    bindBanknameChange: function (e) {\r\n      this.bankname = this.banklist[e.detail.value];\r\n    },\r\n    formSubmit: function (e) {\r\n        var that = this;\r\n        var apply_formdata = this.selectedLevel.apply_formdata;\r\n        var formdata = e.detail.value;\r\n        for (var i = 0; i < apply_formdata.length;i++){\r\n            //console.log(formdata['form' + i]);\r\n            if (apply_formdata[i].val3 == 1 && (formdata['form' + i] === '' || formdata['form' + i] === undefined || formdata['form' + i].length==0)){\r\n                    app.alert(apply_formdata[i].val1+' 必填');return;\r\n            }\r\n            if (apply_formdata[i].key == 'selector') {\r\n                    formdata['form' + i] = apply_formdata[i].val2[formdata['form' + i]]\r\n            }\r\n        }\r\n        if(this.selectedLevel.areafenhong==1 && this.areafenhong_province==''){\r\n            app.alert('请选择代理区域');return;\r\n        }\r\n        if(this.selectedLevel.areafenhong==2 && this.areafenhong_city==''){\r\n            app.alert('请选择代理区域');return;\r\n        }\r\n        if(this.selectedLevel.areafenhong==3 && this.areafenhong_area==''){\r\n            app.alert('请选择代理区域');return;\r\n        }\r\n        if(this.selectedLevel.areafenhong==10 && this.largeareaindex==-1){\r\n            app.alert('请选择代理区域');return;\r\n        }\r\n        if (formdata.levelid == '') {\r\n            app.alert('请选择等级');\r\n            return;\r\n        }\r\n        \r\n        // 验证签名\r\n        if(this.selectedLevel.need_signature == 1 && (!this.qianmingurl || this.qianmingurl == '')){\r\n            app.alert('请先完成签名');\r\n            return;\r\n        }\r\n        \r\n        formdata.areafenhong_province = this.areafenhong_province;\r\n        formdata.areafenhong_city = this.areafenhong_city;\r\n        formdata.areafenhong_area = this.areafenhong_area;\r\n        if(this.selectedLevel.areafenhong==10){\r\n            formdata.areafenhong_largearea = this.largearea[this.largeareaindex];\r\n        }\r\n        if(that.levelupcode){\r\n            formdata.code = '';\r\n            if(that.type == 'ycode'){\r\n                var aglevelList = that.aglevelList;\r\n                formdata.levelid = aglevelList[0]['id'];\r\n                formdata.code    = that.ycode;\r\n            }else{\r\n                var aglevelList = that.aglevelList;\r\n                var len         = aglevelList.length;\r\n                if(len>0){\r\n                    for(var i=0;i<len;i++){\r\n                        if(aglevelList[i]['id'] == formdata.levelid){\r\n                            formdata.code = aglevelList[i]['applycode'];\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 添加签名链接到提交数据\r\n        if(this.selectedLevel.need_signature == 1) {\r\n            formdata.qianmingurl = this.qianmingurl;\r\n        }\r\n        \r\n        app.showLoading('提交中');\r\n        app.post('ApiMy/levelup', formdata, function (res) {\r\n            app.showLoading(false);\r\n            if (res.status == 0) {\r\n              app.alert(res.msg);\r\n              return;\r\n            }\r\n            app.success(res.msg);\r\n            setTimeout(function () {\r\n              app.goto(res.url);\r\n            }, 1000);\r\n        });\r\n    },\r\n    changelevel: function (e) {\r\n\t\tthis.changeState = false;\r\n        var levelid = e.detail.value;\r\n        var aglevelList = this.aglevelList;\r\n        var agleveldata;\r\n\r\n        for (var i in aglevelList) {\r\n            if (aglevelList[i].id == levelid) {\r\n                agleveldata = aglevelList[i];\r\n                break;\r\n            }\r\n        }\r\n        var applytj = [];\r\n        var applytj_reach = 0;\r\n        var member = this.userinfo;\r\n\r\n        // var applytj_info = applytj.join(' 或 ');\r\n        var autouptj = [];\r\n\r\n        // var autouptj_info = autouptj.join(' 或 ');\r\n        // this.applytj_info = applytj_info;\r\n        // this.applytj_reach = applytj_reach;\r\n        // this.autouptj_info = autouptj_info;\r\n        this.selectedLevel = agleveldata;\r\n        this.explain = agleveldata.explain;\r\n        this.editorFormdata = [];\r\n        this.changeState = true;\r\n        this.test = Math.random();\r\n    },\r\n    editorChooseImage: function (e) {\r\n        var that = this;\r\n        var idx = e.currentTarget.dataset.idx;\r\n        var tplindex = e.currentTarget.dataset.tplindex;\r\n        var editorFormdata = this.editorFormdata;\r\n        if(!editorFormdata) editorFormdata = [];\r\n        app.chooseImage(function(data){\r\n            editorFormdata[idx] = data[0];\r\n            console.log(editorFormdata)\r\n            that.editorFormdata = editorFormdata\r\n            that.test = Math.random();\r\n        })\r\n    },\r\n    editorBindPickerChange:function(e){\r\n        var idx = e.currentTarget.dataset.idx;\r\n        var tplindex = e.currentTarget.dataset.tplindex;\r\n        var val = e.detail.value;\r\n        var editorFormdata = this.editorFormdata;\r\n        if(!editorFormdata) editorFormdata = [];\r\n        editorFormdata[idx] = val;\r\n        console.log(editorFormdata)\r\n        this.editorFormdata = editorFormdata\r\n        this.test = Math.random();\r\n    },\r\n    onchange(e) {\r\n        const value = e.detail.value\r\n        console.log(value[0].text + ',' + value[1].text + ',' + value[2].text)\r\n        this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text\r\n    },\r\n    onchange1(e) {\r\n        const value = e.detail.value\r\n        console.log(value[0].text)\r\n        this.areafenhong_province = value[0].text;\r\n    },\r\n    onchange2(e) {\r\n        const value = e.detail.value\r\n        console.log(value[0].text + ',' + value[1].text)\r\n        this.areafenhong_province = value[0].text;\r\n        this.areafenhong_city = value[1].text\r\n    },\r\n    onchange3(e) {\r\n        const value = e.detail.value\r\n        this.areafenhong_province = value[0].text;\r\n        this.areafenhong_city = value[1].text;\r\n        this.areafenhong_area = value[2].text;\r\n    },\r\n    largeareaBindPickerChange:function(e){\r\n        console.log(e.detail.value);\r\n        this.largeareaindex = e.detail.value\r\n    },\r\n    applycode:function(e){\r\n        var that = this;\r\n        var aglevelList = that.aglevelList;\r\n\r\n        var index = e.currentTarget.dataset.index;\r\n        var val   = e.detail.value;\r\n        var len   = aglevelList.length;\r\n        \r\n        if(len>0){\r\n            for(var i=0;i<len;i++){\r\n                if(i == index){\r\n                    aglevelList[i]['applycode'] = val;\r\n                }\r\n            }\r\n        }\r\n        that.aglevelList=aglevelList;\r\n    },\r\n    inputycode:function(e){\r\n        var that = this;\r\n        var ycode  = e.detail.value;\r\n        that.ycode = ycode;\r\n    },\r\n    getlevel: function () {\r\n    \tvar that = this;\r\n\r\n        var ycode = that.ycode;\r\n        if(!ycode){\r\n            app.alert('请填写验证码');\r\n            return;\r\n        }\r\n\r\n    \tthat.loading = true;\r\n    \tapp.get('ApiMy/levelup', {id:0,cid:0,levelid:0,ycode:that.ycode}, function (res) {\r\n    \t\tthat.loading = false;\r\n    \t\tif (res.status == 0) {\r\n    \t\t\tthat.errmsg = res.msg;\r\n    \t\t} else if (res.status == 2) {\r\n    \t\t\tthat.errmsg = res.msg;\r\n    \t\t\tsetTimeout(function () {\r\n    \t\t\t\tapp.goto('index');\r\n    \t\t\t}, 1000);\r\n    \t\t} else {\r\n    \t\t\tthat.aglevelList  = res.aglevelList;\r\n    \t\t\tthat.aglevelCount = res.aglevelList.length;\r\n                if(res.aglevelList.length<=0){\r\n                    that.nolevel = true;\r\n                    var userlevel      = that.userlevel;\r\n                    that.selectedLevel = that.userlevel;\r\n                    that.explain       = userlevel.explain;\r\n                }else{\r\n                    that.nolevel = false;\r\n                    var data = {\r\n                        'detail':{\r\n                            'value':res.aglevelList[0]['id']\r\n                        }\r\n                    }\r\n                    that.changelevel(data);\r\n                }\r\n    \t\t}\r\n    \t\tthat.loaded();\r\n    \t});\r\n    },\r\n    // 签约相关方法\r\n    getSign: function(){\r\n        let that = this;\r\n        app.get('ApiMy/getRenzhengItems', {}, function(res) {\r\n            if(res.content){\r\n                that.content = res.content;\r\n            }\r\n        });\r\n    },\r\n    \r\n    showxieyiFun3: function() {\r\n        this.showxieyi3 = true;\r\n    },\r\n    \r\n    handleCancel: function() {\r\n        this.showxieyi3 = false;\r\n    },\r\n    \r\n    // 清空画布\r\n    handleReset: function() {\r\n        var self = this;\r\n        self.ctx.clearRect(0, 0, self.width, self.height);\r\n        self.ctx.draw(true);\r\n        tempPoint = [];\r\n    },\r\n    \r\n    // 触摸开始，获取到起点\r\n    touchstart: function(e) {\r\n        let startX = e.changedTouches[0].x;\r\n        let startY = e.changedTouches[0].y;\r\n        let startPoint = {\r\n            X: startX,\r\n            Y: startY\r\n        };\r\n        \r\n        this.points = [];\r\n        this.points.push(startPoint);\r\n        \r\n        // 每次触摸开始，开启新的路径\r\n        this.ctx.beginPath();\r\n    },\r\n    \r\n    // 触摸移动，获取到路径点\r\n    touchmove: function(e) {\r\n        let moveX = e.changedTouches[0].x;\r\n        let moveY = e.changedTouches[0].y;\r\n        let movePoint = {\r\n            X: moveX,\r\n            Y: moveY\r\n        };\r\n        this.points.push(movePoint); // 存点\r\n        let len = this.points.length;\r\n        if (len >= 2) {\r\n            this.draw(); // 绘制路径\r\n        }\r\n        tempPoint.push(movePoint);\r\n    },\r\n    \r\n    // 触摸结束，将未绘制的点清空防止对后续路径产生干扰\r\n    touchend: function() {\r\n        this.points = [];\r\n    },\r\n    \r\n    // 绘制笔迹\r\n    draw: function() {\r\n        let point1 = this.points[0];\r\n        let point2 = this.points[1];\r\n        this.points.shift();\r\n        this.ctx.moveTo(point1.X, point1.Y);\r\n        this.ctx.lineTo(point2.X, point2.Y);\r\n        this.ctx.stroke();\r\n        this.ctx.draw(true);\r\n    },\r\n    \r\n    // 将签名笔迹上传到服务器，并将返回来的地址存到本地\r\n    handleConfirm: function() {\r\n        var self = this; // 保存当前的this引用\r\n        \r\n        if (tempPoint.length == 0) {\r\n            uni.showToast({\r\n                title: '请先签名',\r\n                icon: 'none',\r\n                duration: 2000\r\n            });\r\n            return;\r\n        }\r\n        \r\n        // 将签名转换为临时文件路径\r\n        uni.canvasToTempFilePath({\r\n            canvasId: 'mycanvas',\r\n            success: function(res) {\r\n                let tempPath = res.tempFilePath;\r\n                \r\n                const ctx = uni.createCanvasContext('camCacnvs', self);\r\n                ctx.translate(0, self.width);\r\n                ctx.rotate((-90 * Math.PI) / 180);\r\n                ctx.drawImage(tempPath, 0, 0, self.width, self.height);\r\n                ctx.draw();\r\n                \r\n                setTimeout(() => {\r\n                    // 保存签名图片到本地\r\n                    uni.canvasToTempFilePath({\r\n                        canvasId: 'camCacnvs',\r\n                        success: function(res) {\r\n                            let path = res.tempFilePath;\r\n                            \r\n                            // 上传签名图片到服务器\r\n                            uni.uploadFile({\r\n                                url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n                                filePath: path,\r\n                                name: 'file',\r\n                                success: function(res) {\r\n                                    var data = JSON.parse(res.data);\r\n                                    if (data.status == 1) {\r\n                                        // 保存签名URL到本地变量\r\n                                        self.qianmingurl = data.url;\r\n                                        app.alert('签名成功');\r\n                                        self.showxieyi3 = false;\r\n                                    } else {\r\n                                        app.alert(data.msg);\r\n                                    }\r\n                                },\r\n                                fail: function(res) {\r\n                                    app.alert(res.errMsg);\r\n                                }\r\n                            });\r\n                        },\r\n                        fail: err => {\r\n                            console.log('fail', err);\r\n                        }\r\n                    }, self);\r\n                }, 200);\r\n            }\r\n        });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage{background:#F2D8B2;width: 100%;height: 100%;}\r\n.container{width: 100%;height:auto;min-height: 100%;padding-bottom:10rpx;}\r\n.banner{ width:100%;background:#fff;height:400rpx;display:table}\r\n\r\n.contentbox{width:94%;margin: 0 3%;padding:20rpx 40rpx;border-radius:20rpx;background:#fff;color:#B17D2D;margin-bottom:30rpx;display:flex;flex-direction:column;margin-bottom:10px}\r\n.title{height:50rpx;line-height:50rpx}\r\n\r\n.user-level {margin-left:10rpx;display:flex;}\r\n.user-level image {width: 44rpx;height: 44rpx;margin-right: 10rpx;margin-left: -4rpx;}\r\n.level-name {height: 36rpx;border-radius: 18rpx;font-size: 24rpx;color: #fff;background-color: #5c5652;padding: 0 16rpx 0 0;display:flex;align-items: flex-end;}\r\n.level-name .name{display:flex;align-items:center;height:100%}\r\n\r\n.noup{ width:100%;text-align:center;font-size:32rpx;color:green}\r\n\r\n.form-item1{width: 100%;display:flex;flex-direction:column;color:#333}\r\n.form-item1 .panel{width: 100%;font-size:32rpx;color:#B17D2D;}\r\n.form-item1 radio-group{width: 100%;background:#fff;padding-left:10rpx;}\r\n.form-item1 .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;padding:12rpx 0;}\r\n.form-item1 .radio-item:last-child{border:0}\r\n.radio-item .user-level{flex:1}\r\n.form-item1 radio{ transform: scale(0.8);}\r\n\r\n.applytj{width:100%;}\r\n.applytj .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}\r\n.applytj .f1 .t2{padding-left:10rpx}\r\n.applytj .f2{padding:20rpx;background-color:#fff;color:#f56060}\r\n.applytj .f2 .t2{padding-top:10rpx;color:#88e}\r\n.applytj .f2 .t3{padding-top:10rpx}\r\n.applytj .f2 .t4{padding-top:10rpx;color:green;font-size:30rpx}\r\n.uplvtj{width:100%;margin-top:20rpx;}\r\n.uplvtj .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}\r\n.uplvtj .f1 .t2{padding-left:10rpx}\r\n.uplvtj .f2{padding:20rpx;background-color:#fff;color:#f56060}\r\n.uplvtj .f2 .t3{padding-top:10rpx;color:green}\r\n\r\n.explain{ width:100%;margin:20rpx 0;}\r\n.explain .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}\r\n.explain .f1 .t2{padding-left:10rpx}\r\n.explain .f2{padding:20rpx;background-color:#fff;color:#999999}\r\n\r\n\r\n.applydata{width: 100%;background: #fff;padding: 0 20rpx;color:#333}\r\n\r\n.form-btn{width:100%;height: 88rpx;line-height: 88rpx;border-radius:8rpx;background: #FC4343;color: #fff;margin-top: 40rpx;margin-bottom: 20rpx;}\r\n\r\n.applydata .radio{transform:scale(.7);}\r\n.applydata .checkbox{transform:scale(.7);}\r\n.form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;align-items: center;}\r\n.form-item:last-child{border:0}\r\n.form-item .label{height:70rpx;line-height: 70rpx;width:160rpx;margin-right: 10px;flex-shrink:0}\r\n.form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;}\r\n.form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .layui-form-switch{}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n.apply_code{float: right;width: 100rpx;line-height: 60rpx;border-radius: 60rpx;text-align: center;color: #fff;}\r\n.curlevel_tag{color: #b4b4b4;font-size: 24rpx;}\r\n\r\n/* 签约相关样式 */\r\n.xieyibox {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 99;\r\n    background: rgba(0, 0, 0, 0.7)\r\n}\r\n\r\n.container3 {\r\n    display: flex;\r\n    flex-direction: row;\r\n}\r\n\r\n.sign-box {\r\n    width: 80%;\r\n    height: 90%;\r\n    margin: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    text-align: center;\r\n}\r\n\r\n.sign-view {\r\n    height: 100%;\r\n}\r\n\r\n.sigh-btns {\r\n    height: 100%;\r\n    margin: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n}\r\n\r\n.btn {\r\n    margin: auto;\r\n    padding: 8rpx;\r\n    transform: rotate(90deg);\r\n    border: grey 1rpx solid;\r\n}\r\n\r\n.mycanvas {\r\n    margin: auto 0rpx;\r\n    background-color: #ececec;\r\n}\r\n\r\n.canvsborder {\r\n    border: 1rpx solid #333;\r\n    position: fixed;\r\n    top: 0;\r\n    left: 10000rpx;\r\n}\r\n\r\n.sign-btn {\r\n    margin-top: 20rpx;\r\n    width: 100%;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    background: #F56C6C;\r\n    border-radius: 40rpx;\r\n    color: #fff;\r\n    font-size: 30rpx;\r\n    text-align: center;\r\n}\r\n\r\n.agreement-content {\r\n    padding: 20rpx;\r\n    border: 1px solid #eee;\r\n    border-radius: 10rpx;\r\n    max-height: 300rpx;\r\n    overflow-y: auto;\r\n    font-size: 28rpx;\r\n    margin-top: 10rpx;\r\n    background-color: #f9f9f9;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./levelup.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./levelup.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115060956\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}