{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?85f2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?5cf5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?cd25", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?388d", "uni-app:///pagesExb/coupon/coupondetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?3341", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/coupondetail.vue?b0d7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "textset", "record", "coupon", "shareTitle", "sharePic", "shareDesc", "shareLink", "mid", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "desc", "link", "onShareTimeline", "console", "imageUrl", "query", "methods", "getdata", "that", "app", "rid", "id", "uni", "getcoupon", "datalist", "setTimeout", "receiveCoupon", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9YA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkMrxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAJ;MAAAC;MAAAC;MAAAC;IAAA;IACA;IACAE;IACAA;IACA;MACAL;MACAM;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACAA;QACAI;UACAd;QACA;QACA;UACAW;UAAA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;UAAAV;UAAAC;UAAAC;UAAAC;QAAA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;MAEA;QACAJ;UAAAE;QAAA;UACA;YACAF;UACA;YACAA;UACA;QACA;QACA;MACA;MACA;MACA;QACAA;UACAA;UACAA;YAAAE;UAAA;YACAF;YACA;cACAA;YACA;cACAA;cACAK;cACAN;YACA;UACA;QACA;MACA;QACAC;QACAA;UAAAE;QAAA;UACAF;UACA;YACAA;UACA;YACAA;YACAM;cACAN;YACA;UACA;QACA;MACA;IACA;IAEAO;MACA;MACA;MACA;MACA;MACAP;MACAA;QAAAE;QAAAD;MAAA;QACAD;QACA;UACAA;QACA;UACAA;UACAD;QACA;MACA;IACA;IAEAS;MACAR;MACA;IACA;IACAS;MACA;MACAV;MACAI;QACAO;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YAEAV;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClWA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/coupon/coupondetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/coupon/coupondetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupondetail.vue?vue&type=template&id=e5e4612c&\"\nvar renderjs\nimport script from \"./coupondetail.vue?vue&type=script&lang=js&\"\nexport * from \"./coupondetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupondetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/coupon/coupondetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupondetail.vue?vue&type=template&id=e5e4612c&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload && _vm.record.id && _vm.record.type == 1 ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload &&\n    _vm.record.id &&\n    !(_vm.record.type == 1) &&\n    _vm.record.type == 10\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.record.id &&\n    !(_vm.record.type == 1) &&\n    !(_vm.record.type == 10) &&\n    _vm.record.type == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.record.id &&\n    !(_vm.record.type == 1) &&\n    !(_vm.record.type == 10) &&\n    !(_vm.record.type == 2) &&\n    _vm.record.type == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.record.id &&\n    !(_vm.record.type == 1) &&\n    !(_vm.record.type == 10) &&\n    !(_vm.record.type == 2) &&\n    !(_vm.record.type == 3) &&\n    _vm.record.type == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload &&\n    _vm.record.id &&\n    !(_vm.record.type == 1) &&\n    !(_vm.record.type == 10) &&\n    !(_vm.record.type == 2) &&\n    !(_vm.record.type == 3) &&\n    !(_vm.record.type == 4) &&\n    _vm.record.type == 5\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.record.id &&\n    _vm.record.type == 3 &&\n    _vm.record.used_count > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && !_vm.record.id && _vm.coupon.type == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && !_vm.record.id && _vm.coupon.type == 10\n      ? _vm.t(\"color1\")\n      : null\n  var m11 =\n    _vm.isload &&\n    !_vm.record.id &&\n    !(_vm.coupon.type == 10) &&\n    _vm.coupon.type == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    !_vm.record.id &&\n    !(_vm.coupon.type == 10) &&\n    !(_vm.coupon.type == 2) &&\n    _vm.coupon.type == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !_vm.record.id &&\n    !(_vm.coupon.type == 10) &&\n    !(_vm.coupon.type == 2) &&\n    !(_vm.coupon.type == 3) &&\n    _vm.coupon.type == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !_vm.record.id &&\n    !(_vm.coupon.type == 10) &&\n    !(_vm.coupon.type == 2) &&\n    !(_vm.coupon.type == 3) &&\n    !(_vm.coupon.type == 4) &&\n    _vm.coupon.type == 5\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload && !_vm.record.id && _vm.coupon.score > 0 ? _vm.t(\"积分\") : null\n  var m16 =\n    _vm.isload && !_vm.record.id && _vm.coupon.score > 0 ? _vm.t(\"积分\") : null\n  var m17 = _vm.isload && !_vm.record.id ? _vm.t(\"color1\") : null\n  var m18 = _vm.isload && !_vm.record.id ? _vm.t(\"color1rgb\") : null\n  var m19 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    (_vm.coupon.type == 1 || _vm.coupon.type == 10) &&\n    _vm.record.status == 0\n      ? _vm.inArray(_vm.coupon.fwtype, [0, 1, 2])\n      : null\n  var m20 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    (_vm.coupon.type == 1 || _vm.coupon.type == 10) &&\n    _vm.record.status == 0 &&\n    m19\n      ? _vm.t(\"color1\")\n      : null\n  var m21 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    (_vm.coupon.type == 1 || _vm.coupon.type == 10) &&\n    _vm.record.status == 0 &&\n    m19\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m22 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    (_vm.coupon.type == 1 || _vm.coupon.type == 10) &&\n    _vm.record.status == 0 &&\n    _vm.coupon.fwtype == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m23 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    (_vm.coupon.type == 1 || _vm.coupon.type == 10) &&\n    _vm.record.status == 0 &&\n    _vm.coupon.fwtype == 4\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m24 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    _vm.coupon.type == 3 &&\n    _vm.record.status == 0 &&\n    _vm.record.yuyue_proid > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m25 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.coupon.isgive != 2 &&\n    _vm.record.id &&\n    _vm.coupon.type == 3 &&\n    _vm.record.status == 0 &&\n    _vm.record.yuyue_proid > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m26 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2)\n      ? _vm.getplatform()\n      : null\n  var m27 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    m26 == \"app\"\n      ? _vm.t(\"color2\")\n      : null\n  var m28 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    m26 == \"app\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m29 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m30 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    m29 == \"mp\"\n      ? _vm.t(\"color2\")\n      : null\n  var m31 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    m29 == \"mp\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m32 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    !(m29 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m33 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    !(m29 == \"mp\") &&\n    m32 == \"h5\"\n      ? _vm.t(\"color2\")\n      : null\n  var m34 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    !(m29 == \"mp\") &&\n    m32 == \"h5\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m35 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    !(m29 == \"mp\") &&\n    !(m32 == \"h5\")\n      ? _vm.t(\"color2\")\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.mid == _vm.record.mid &&\n    _vm.record.id &&\n    _vm.record.status == 0 &&\n    !_vm.record.from_mid &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    !(m26 == \"app\") &&\n    !(m29 == \"mp\") &&\n    !(m32 == \"h5\")\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m37 =\n    _vm.isload &&\n    !(_vm.mid == _vm.record.mid) &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    _vm.opt.pid == _vm.record.mid &&\n    _vm.opt.pid > 0\n      ? _vm.t(\"color2\")\n      : null\n  var m38 =\n    _vm.isload &&\n    !(_vm.mid == _vm.record.mid) &&\n    (_vm.coupon.isgive == 1 || _vm.coupon.isgive == 2) &&\n    _vm.opt.pid == _vm.record.mid &&\n    _vm.opt.pid > 0\n      ? _vm.t(\"color2rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupondetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupondetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"couponbg\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"></view>\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<block v-if=\"record.id\">\r\n\t\t\t\t<view class=\"topitem\">\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"record.type==1\"><text style=\"font-size:32rpx\">￥</text><text class=\"t1\">{{record.money}}</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"record.type==10\"><text class=\"t1\">{{record.discount/10}}</text><text style=\"font-size:32rpx\">折</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"record.type==2\">礼品券</view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"record.type==3\"><text class=\"t1\">{{record.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"record.type==4\">抵运费</view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"record.type==5\">餐饮券</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{record.couponname}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"record.type==1 || record.type==4 || record.type==5\">\r\n\t\t\t\t\t\t\t<text v-if=\"record.minprice>0\">满{{record.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"record.type==2\">礼品券</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"record.type==3\">计次券</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"coupon.bid!=0\">\r\n\t\t\t\t\t<text class=\"t1\">适用商家</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.bname}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">类型</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==1\">代金券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2\">（可赠送）</text></text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==10\">折扣券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2\">（可赠送）</text></text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==2\">礼品券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2 \">（可赠送）</text></text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==3\">计次券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2\">（可赠送）</text></text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==4\">运费抵扣券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2\">（可赠送）</text></text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"record.type==5\">餐饮券<text v-if=\"coupon.isgive == 1 || coupon.isgive == 2\">（可赠送）</text></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"record.type==3\">\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<text class=\"t1\">共计次数</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{record.limit_count}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<text class=\"t1\">已使用次数</text>\r\n\t\t\t\t\t\t<view class=\"t2 flex\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{record.used_count}}</view>\r\n\t\t\t\t\t\t\t<view :style=\"{color:t('color1')}\" v-if=\"record.used_count > 0\" @tap=\"goto\" :data-url=\"'/pagesExt/coupon/record?crid='+record.id\">查看使用记录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"record.limit_perday>0\">\r\n\t\t\t\t\t\t<text class=\"t1\">每天限制次数</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{record.limit_perday}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">领取时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{record.createtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"record.status==1\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">使用时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{record.usetime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\">有效期</text>\r\n\t\t\t\t\t<text class=\"t2\">{{record.starttime}} 至 {{record.endtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\">使用说明</text>\r\n\t\t\t\t\t<view class=\"t2\">{{coupon.usetips}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-col\" v-if=\"record.status==0 && record.hexiaoqr && coupon.isgive!=2\">\r\n\t\t\t\t\t<text class=\"t1\">核销码</text>\r\n\t\t\t\t\t<view class=\"flex-x-center\">\r\n\t\t\t\t\t\t<image :src=\"record.hexiaoqr\" style=\"width:500rpx;height:500rpx\" @tap=\"previewImage\" :data-url=\"record.hexiaoqr\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"flex-x-center\">到店使用时请出示核销码进行核销</text>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<view class=\"topitem\">\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"coupon.type==1\"><text style=\"font-size:32rpx\">￥</text><text class=\"t1\">{{coupon.money}}</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"coupon.type==10\"><text class=\"t1\">{{coupon.discount/10}}</text><text style=\"font-size:32rpx\">折</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"coupon.type==2\">礼品券</view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"coupon.type==3\"><text class=\"t1\">{{coupon.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"coupon.type==4\">抵运费</view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"coupon.type==5\">餐饮券</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{coupon.name}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"coupon.type==1 || coupon.type==4 || coupon.type==5\">\r\n\t\t\t\t\t\t\t<text v-if=\"coupon.minprice>0\">满{{coupon.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"coupon.type==2\">礼品券</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"coupon.type==3\">计次券</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"item\" v-if=\"coupon.bid!=0\">\r\n\t\t\t\t\t<text class=\"t1\">适用商家</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.bname}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">类型</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==1\">代金券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==10\">折扣券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==2\">礼品券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==3\">计次券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==4\">运费抵扣券</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"coupon.type==5\">餐饮券</text>\r\n\t\t\t\t</view>\r\n                <view class=\"item\" v-if=\"coupon.house_status\">\r\n                \t<text class=\"t1\">领取限制</text>\r\n                \t<text class=\"t2\">一户仅限一次</text>\r\n                </view>\r\n\t\t\t\t<block v-if=\"coupon.type==3\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">共计次数</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.limit_count}}次</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"coupon.limit_perday>0\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">每天限制使用</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.limit_perday}}次</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"coupon.price>0\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">所需金额</text>\r\n\t\t\t\t\t<text class=\"t2\">￥{{coupon.price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"coupon.score>0\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">所需{{t('积分')}}</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.score}}{{t('积分')}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">活动时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.starttime}} ~ {{coupon.endtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">有效期</text>\r\n\t\t\t\t\t<block v-if=\"coupon.yxqtype==1\">\r\n\t\t\t\t\t<text class=\"t2\">{{coupon.yxqtime}}</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"coupon.yxqtype==2\">\r\n\t\t\t\t\t<text class=\"t2\">领取后{{coupon.yxqdate}}天内有效</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"coupon.yxqtype==3\">\r\n\t\t\t\t\t<text class=\"t2\">领取后{{coupon.yxqdate}}天内有效（次日0点生效）</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">使用说明</text>\r\n\t\t\t\t\t<view class=\"t2\">{{coupon.usetips}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-if=\"!record.id\" class=\"btn-add\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"getcoupon\" :data-id=\"coupon.id\" :data-price=\"coupon.price\" :data-score=\"coupon.score\">{{coupon.price>0?'立即购买':(coupon.score>0?'立即兑换':'立即领取')}}</view>\r\n\t\t<block v-if=\"mid == record.mid\">\r\n\t\t\t<!-- 仅转赠 -->\r\n\t\t\t<block v-if=\"coupon.isgive != 2\">\r\n\t\t\t\t<block v-if=\"record.id && (coupon.type==1 || coupon.type==10) && record.status==0\">\r\n\t\t\t\t\t<view class=\"btn-add\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"inArray(coupon.fwtype,[0,1,2])\" @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/prolist?cpid='+record.couponid+(coupon.bid?'&bid='+coupon.bid:'')\">去使用</view>\r\n\t\t\t\t\t<view class=\"btn-add\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"coupon.fwtype == 4\" @tap.stop=\"goto\" :data-url=\"'/yuyue/prolist?cpid=' + record.couponid+(coupon.bid?'&bid='+coupon.bid:'')\">去使用</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"btn-add\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"record.id && coupon.type==3 && record.status==0 && record.yuyue_proid > 0\" @tap.stop=\"goto\" :data-url=\"'/yuyue/product?id=' + record.yuyue_proid\">去预约</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"record.id && record.status==0 && !record.from_mid && (coupon.isgive == 1 || coupon.isgive == 2)\">\r\n\t\t\t\t<view class=\"btn-add\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" :data-id=\"record.id\">转赠好友</view>\r\n\t\t\t\t<view class=\"btn-add\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" :data-id=\"record.id\">转赠好友</view>\r\n\t\t\t\t<view class=\"btn-add\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" :data-id=\"record.id\">转赠好友</view>\r\n\t\t\t\t<button class=\"btn-add\" open-type=\"share\" v-else :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" :data-id=\"record.id\">转赠好友</button>\r\n\t\t\t</block>\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<view v-if=\"(coupon.isgive == 1 || coupon.isgive == 2) && opt.pid == record.mid && opt.pid > 0\" class=\"btn-add\" @tap=\"receiveCoupon\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" :data-id=\"record.id\">立即领取</view>\r\n\t\t</block>\r\n\t\t\r\n\t\t<view class='text-center' @tap=\"goto\" data-url='/pages/index/index' style=\"margin-top: 40rpx; line-height: 60rpx;\"><text>返回首页</text></view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\r\n\t\t\ttextset:{},\r\n\t\t\trecord:{},\r\n\t\t\tcoupon:{},\r\n\t\t\tshareTitle:'',\r\n\t\t\tsharePic:'',\r\n\t\t\tshareDesc:'',\r\n\t\t\tshareLink:'',\r\n\t\t\tmid:0\r\n\t\t}\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiCoupon/coupondetail', {rid: that.opt.rid,id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.t('优惠券') + '详情'\r\n\t\t\t\t});\r\n\t\t\t\tif(!res.coupon.id) {\r\n\t\t\t\t\tapp.alert(that.t('优惠券')+'不存在');return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.mid = app.globalData.mid;\r\n\t\t\t\tthat.record = res.record;\r\n\t\t\t\tthat.coupon = res.coupon;\r\n\t\t\t\tthat.shareTitle = '送你一张优惠券，点击领取';\r\n\t\t\t\tthat.shareDesc = that.coupon.name;\r\n\t\t\t\tthat.sharePic = app.globalData.initdata.logo;\r\n\t\t\t\tthat.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/coupon/coupondetail?scene=id_'+that.coupon.id+'-pid_' + app.globalData.mid+'-rid_' + that.record.id;\r\n\t\t\t\tthat.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});\r\n\t\t\t});\r\n\t\t},\r\n    getcoupon: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar datalist = that.datalist;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar score = parseInt(e.currentTarget.dataset.score);\r\n\t\t\tvar price = e.currentTarget.dataset.price;\r\n\r\n\t\t\tif (price > 0) {\r\n\t\t\t\tapp.post('ApiCoupon/buycoupon', {id: id}, function (res) {\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tvar key = e.currentTarget.dataset.key;\r\n\t\t\tif (score > 0) {\r\n\t\t\t\tapp.confirm('确定要消耗' + score + '' + that.t('积分') + '兑换吗?', function () {\r\n\t\t\t\t\tapp.showLoading('兑换中');\r\n\t\t\t\t\tapp.post('ApiCoupon/getcoupon', {id: id}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\tdatalist[key]['haveget'] = data.haveget;\r\n\t\t\t\t\t\t\tthat.datalist = datalist;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tapp.showLoading('领取中');\r\n\t\t\t\tapp.post('ApiCoupon/getcoupon', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tapp.goto('mycoupon');\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n    },\r\n\t\t\r\n\t\treceiveCoupon:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar datalist = that.datalist;\r\n\t\t\tvar rid = that.record.id;\r\n\t\t\tvar id = that.coupon.id;\r\n\t\t\tapp.showLoading('领取中');\r\n\t\t\tapp.post('ApiCoupon/receiveCoupon', {id: id,rid:rid}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n\t\t    itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t    success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = '送你一张优惠券，点击领取';\r\n\t\t\t\t\t\tsharedata.summary = that.shareDesc;\r\n\t\t\t\t\t\tsharedata.href = that.shareLink;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.sharePic;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column; padding-bottom: 30rpx;}\r\n.couponbg{width:100%;height:500rpx;}\r\n.orderinfo{ width:94%;margin:-400rpx 3% 20rpx 3%;border-radius:8px;padding:14rpx 3%;background: #FFF;color:#333;}\r\n.orderinfo .topitem{display:flex;padding:60rpx 40rpx;align-items:center;border-bottom:2px dashed #E5E5E5;position:relative}\r\n.orderinfo .topitem .f1{font-size:50rpx;font-weight:bold;}\r\n.orderinfo .topitem .f1 .t1{font-size:60rpx;}\r\n.orderinfo .topitem .f1 .t2{font-size:40rpx;}\r\n.orderinfo .topitem .f2{margin-left:40rpx}\r\n.orderinfo .topitem .f2 .t1{font-size:36rpx;color:#2B2B2B;font-weight:bold;height:50rpx;line-height:50rpx}\r\n.orderinfo .topitem .f2 .t2{font-size:24rpx;color:#999999;height:50rpx;line-height:50rpx}\r\n.orderinfo .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}\r\n.orderinfo .item .t2{color:#2B2B2B;font-size:24rpx;height:auto;line-height:40rpx;white-space:pre-wrap;}\r\n.orderinfo .item .red{color:red}\r\n\r\n.text-center { text-align: center;}\r\n.btn-add{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupondetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupondetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024297\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}