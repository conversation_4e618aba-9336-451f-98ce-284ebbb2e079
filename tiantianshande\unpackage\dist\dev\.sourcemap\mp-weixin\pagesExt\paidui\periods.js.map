{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?7b60", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?fdaf", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?2f04", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?8b97", "uni-app:///pagesExt/paidui/periods.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?d240", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/periods.vue?a294"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "nodata", "nomore", "periodsList", "userinfo", "nickname", "headimg", "user_summary", "total_periods", "completed_periods", "waiting_periods", "completion_rate", "total_distributed", "total_waiting", "pagination", "pagenum", "currentStatus", "showDetailModal", "currentPeriod", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "loaded", "uni", "t", "getPeriodsData", "that", "page", "limit", "params", "console", "app", "title", "changeStatus", "scrollTop", "duration", "showPeriodDetail", "period_id", "closeDetailModal", "viewPaiduiDetail", "viewPaidui", "formatTime", "String"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+KhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACAC;MACAA;MACAA;MAEA;QACAC;QACAC;MACA;;MAEA;MACA;QACAC;MACA;QACAA;MACA;;MAEAC;MAEAC;QACAL;QAEA;UACA;UAEA;YACA;YACAA;cACAtB;cACAC;YACA;YACAqB;cACAnB;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACAc;YAEAH;cACAS;YACA;YAEAN;YAEA;cACAA;YACA;YAEAA;UACA;YACA;cACAA;YACA;cACA;cACAA;YACA;UACA;QACA;UACAK;QACA;MACA;QACAL;QACAK;MACA;IACA;IAEA;IACAE;MACAH;MACA;MACAP;QACAW;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAV;MAEAK;QAAAM;MAAA;QACAX;QAEA;UACAA;UACAA;QACA;UACAK;QACA;MACA;QACAL;QACAK;MACA;IACA;IAEA;IACAO;MACA;MACA;IACA;IAEA;IACAC;MACAR;IACA;IAEA;IACAS;MACAT;IACA;IAEA;IACAU;MACA;MACA;MACA,kCACAC,qDACAA,gDACAA,iDACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnXA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/paidui/periods.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/paidui/periods.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./periods.vue?vue&type=template&id=1b291d17&\"\nvar renderjs\nimport script from \"./periods.vue?vue&type=script&lang=js&\"\nexport * from \"./periods.vue?vue&type=script&lang=js&\"\nimport style0 from \"./periods.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/paidui/periods.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./periods.vue?vue&type=template&id=1b291d17&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.periodsList && _vm.periodsList.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./periods.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./periods.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- 用户信息横幅 -->\r\n\t\t<view class=\"banner\" :style=\"{background:t('color1')}\">\r\n\t\t\t<image :src=\"userinfo.headimg || '/static/img/default-avatar.png'\"/>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t<text class=\"nickname\">{{userinfo.nickname || '用户'}}</text>\r\n\t\t\t\t<text class=\"subtitle\">我的分期排队</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 统计信息卡片 -->\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{user_summary.total_periods || 0}}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">总分期数</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{user_summary.completed_periods || 0}}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">已完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{user_summary.waiting_periods || 0}}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">待分红</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{user_summary.completion_rate || 0}}%</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">完成率</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">￥{{user_summary.total_distributed || 0}}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">已分红金额</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">￥{{user_summary.total_waiting || 0}}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">待分红金额</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 状态切换标签 -->\r\n\t\t<dd-tab :itemdata=\"['全部','待分红','已分红']\" :itemst=\"['all','waiting','completed']\" :st=\"currentStatus\" :isfixed=\"false\" @changetab=\"changeStatus\"></dd-tab>\r\n\t\t\r\n\t\t<!-- 分期列表 -->\r\n\t\t<view class=\"periods-content\">\r\n\t\t\t<block v-if=\"periodsList && periodsList.length > 0\">\r\n\t\t\t\t<view v-for=\"(item, index) in periodsList\" :key=\"index\" class=\"period-card\">\r\n\t\t\t\t\t<view class=\"period-header\">\r\n\t\t\t\t\t\t<view class=\"period-title\">\r\n\t\t\t\t\t\t\t<text class=\"period-id\">分期 #{{item.id}}</text>\r\n\t\t\t\t\t\t\t<text class=\"period-num\">第{{item.period_num}}期/共{{item.total_periods}}期</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"period-status\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.status == 0\">\r\n\t\t\t\t\t\t\t\t<text class=\"status-waiting\">待分红</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<text class=\"status-completed\">已分红</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"period-info\">\r\n\t\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"info-label\">排队ID:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"info-value\">{{item.paidui_id}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"info-label\">分红金额:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"info-value amount\">￥{{item.period_amount}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"info-label\">创建时间:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"info-value\">{{item.createtime_text}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info-item\" v-if=\"item.status == 1\">\r\n\t\t\t\t\t\t\t\t<text class=\"info-label\">分红时间:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"info-value\">{{item.distributed_time_text}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"period-progress\" v-if=\"item.paidui_summary\">\r\n\t\t\t\t\t\t<view class=\"progress-info\">\r\n\t\t\t\t\t\t\t<text class=\"progress-text\">排队进度: {{item.paidui_summary.distributed_periods}}/{{item.paidui_summary.total_periods}} ({{item.paidui_summary.completion_rate}}%)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{width: item.paidui_summary.completion_rate + '%'}\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"period-actions\">\r\n\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"showPeriodDetail(item)\">\r\n\t\t\t\t\t\t\t<text>查看详情</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"viewPaiduiDetail(item.paidui_id)\">\r\n\t\t\t\t\t\t\t<text>查看排队</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分期详情弹窗 -->\r\n\t\t<view v-if=\"showDetailModal\" class=\"modal-overlay\" @tap=\"closeDetailModal\">\r\n\t\t\t<view class=\"modal-content\" @tap.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">分期详情</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @tap=\"closeDetailModal\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">分期ID:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.id}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">排队ID:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.paidui_id}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">期数:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">第{{currentPeriod.period_num}}期 / 共{{currentPeriod.total_periods}}期</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">分红金额:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value amount\">￥{{currentPeriod.period_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">总金额:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">￥{{currentPeriod.total_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">状态:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.status_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">创建时间:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.createtime_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"currentPeriod.status == 1\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">分红时间:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.distributed_time_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"currentPeriod.ordernum\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">订单号:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.ordernum}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"currentPeriod.nickname\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">会员昵称:</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{currentPeriod.nickname}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!-- 数据状态组件 -->\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      nodata: false,\r\n      nomore: false,\r\n      periodsList: [],\r\n      userinfo: {\r\n        nickname: '用户',\r\n        headimg: '/static/img/default-avatar.png'\r\n      },\r\n      user_summary: {\r\n        total_periods: 0,\r\n        completed_periods: 0,\r\n        waiting_periods: 0,\r\n        completion_rate: 0,\r\n        total_distributed: 0,\r\n        total_waiting: 0\r\n      },\r\n      pagination: {},\r\n      pagenum: 1,\r\n      currentStatus: 'all',\r\n      showDetailModal: false,\r\n      currentPeriod: {}\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.getPeriodsData();\r\n  },\r\n\r\n  onPullDownRefresh: function () {\r\n    this.getPeriodsData();\r\n  },\r\n\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore && this.pagination.page < this.pagination.pages) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getPeriodsData(true);\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 标记页面加载完成\r\n    loaded: function() {\r\n      this.isload = true;\r\n      uni.stopPullDownRefresh();\r\n    },\r\n\r\n    // 获取主题颜色\r\n    t: function(colorName) {\r\n      if (colorName === 'color1') {\r\n        return '#1890ff';\r\n      }\r\n      return '#333';\r\n    },\r\n\r\n    // 获取分期数据\r\n    getPeriodsData: function (loadmore) {\r\n      if (!loadmore) {\r\n        this.pagenum = 1;\r\n        this.periodsList = [];\r\n      }\r\n      \r\n      var that = this;\r\n      that.loading = true;\r\n      that.nodata = false;\r\n      that.nomore = false;\r\n      \r\n      var params = {\r\n        page: that.pagenum,\r\n        limit: 20\r\n      };\r\n      \r\n      // 根据状态筛选\r\n      if (that.currentStatus === 'waiting') {\r\n        params.status = 0;  // 待分红\r\n      } else if (that.currentStatus === 'completed') {\r\n        params.status = 1;  // 已分红\r\n      }\r\n      \r\n      console.log('筛选参数:', params);\r\n      \r\n      app.post('ApiPaidui/myPeriods', params, function (res) {\r\n        that.loading = false;\r\n        \r\n        if (res.status == 1) {\r\n          var data = res.data || [];\r\n          \r\n          if (that.pagenum == 1) {\r\n            // 设置用户信息和统计信息\r\n            that.userinfo = res.userinfo || {\r\n              nickname: '用户',\r\n              headimg: '/static/img/default-avatar.png'\r\n            };\r\n            that.user_summary = res.user_summary || {\r\n              total_periods: 0,\r\n              completed_periods: 0,\r\n              waiting_periods: 0,\r\n              completion_rate: 0,\r\n              total_distributed: 0,\r\n              total_waiting: 0\r\n            };\r\n            that.pagination = res.pagination || {};\r\n            \r\n            uni.setNavigationBarTitle({\r\n              title: '我的分期排队'\r\n            });\r\n            \r\n            that.periodsList = data;\r\n            \r\n            if (data.length == 0) {\r\n              that.nodata = true;\r\n            }\r\n            \r\n            that.loaded();\r\n          } else {\r\n            if (data.length == 0) {\r\n              that.nomore = true;\r\n            } else {\r\n              var newData = that.periodsList.concat(data);\r\n              that.periodsList = newData;\r\n            }\r\n          }\r\n        } else {\r\n          app.error(res.msg || '获取数据失败');\r\n        }\r\n      }, function() {\r\n        that.loading = false;\r\n        app.error('网络请求失败');\r\n      });\r\n    },\r\n\r\n    // 切换状态\r\n    changeStatus: function (status) {\r\n      console.log('切换状态到:', status);\r\n      this.currentStatus = status;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getPeriodsData();\r\n    },\r\n\r\n    // 显示分期详情\r\n    showPeriodDetail: function (period) {\r\n      var that = this;\r\n      that.loading = true;\r\n      \r\n      app.post('ApiPaidui/periodDetail', {period_id: period.id}, function (res) {\r\n        that.loading = false;\r\n        \r\n        if (res.status == 1) {\r\n          that.currentPeriod = res.data || period;\r\n          that.showDetailModal = true;\r\n        } else {\r\n          app.error(res.msg || '获取详情失败');\r\n        }\r\n      }, function() {\r\n        that.loading = false;\r\n        app.error('网络请求失败');\r\n      });\r\n    },\r\n\r\n    // 关闭详情弹窗\r\n    closeDetailModal: function () {\r\n      this.showDetailModal = false;\r\n      this.currentPeriod = {};\r\n    },\r\n\r\n    // 查看排队详情\r\n    viewPaiduiDetail: function (paidui_id) {\r\n      app.goto('/pagesExt/paidui/paidui?paidui_id=' + paidui_id);\r\n    },\r\n\r\n    // 查看分期详情\r\n    viewPaidui: function (paidui_id) {\r\n      app.goto('/pagesExt/paidui/paidui?paidui_id=' + paidui_id);\r\n    },\r\n\r\n    // 格式化时间\r\n    formatTime: function (timestamp) {\r\n      if (!timestamp) return '-';\r\n      const date = new Date(timestamp * 1000);\r\n      return date.getFullYear() + '-' + \r\n             String(date.getMonth() + 1).padStart(2, '0') + '-' + \r\n             String(date.getDate()).padStart(2, '0') + ' ' + \r\n             String(date.getHours()).padStart(2, '0') + ':' + \r\n             String(date.getMinutes()).padStart(2, '0');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  width: 100%;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 横幅样式 */\r\n.banner {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 400rpx;\r\n  padding: 40rpx 32rpx;\r\n  color: #fff;\r\n  position: relative;\r\n}\r\n\r\n.banner image {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.banner .info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding-top: 10rpx;\r\n}\r\n\r\n.banner .nickname {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.banner .subtitle {\r\n  font-size: 28rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 统计信息样式 */\r\n.stats-container {\r\n  width: 100%;\r\n  padding: 0 30rpx;\r\n  margin-top: -180rpx;\r\n  position: relative;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.stats-card {\r\n  width: 100%;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.stats-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 分期列表样式 */\r\n.periods-content {\r\n  width: 100%;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.period-card {\r\n  width: 100%;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.period-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 20rpx;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.period-title {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.period-id {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.period-num {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.period-status {\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.status-waiting {\r\n  background: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.status-completed {\r\n  background: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.period-info {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.info-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.info-value {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n.info-value.amount {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.period-progress {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.progress-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.progress-bar {\r\n  height: 8rpx;\r\n  background: #f0f0f0;\r\n  border-radius: 4rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #1890ff, #40a9ff);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.period-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  line-height: 60rpx;\r\n  text-align: center;\r\n  background: #f0f0f0;\r\n  border-radius: 8rpx;\r\n  margin: 0 10rpx;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.action-btn:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.action-btn:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.action-btn:active {\r\n  background: #e0e0e0;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  width: 80%;\r\n  max-width: 600rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  max-height: 80%;\r\n  overflow-y: auto;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.modal-close {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  line-height: 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.modal-body {\r\n  padding: 30rpx;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.detail-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.detail-label {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  width: 200rpx;\r\n}\r\n\r\n.detail-value {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  flex: 1;\r\n  text-align: right;\r\n}\r\n\r\n.detail-value.amount {\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./periods.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./periods.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103054\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}