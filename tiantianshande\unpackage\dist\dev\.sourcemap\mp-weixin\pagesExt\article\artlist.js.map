{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?b015", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?1de2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?73f6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?9a89", "uni-app:///pagesExt/article/artlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?ef8b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/artlist.vue?deae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "datalist", "pagenum", "clist", "cnamelist", "cidlist", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "console", "that", "app", "bid", "cid", "uni", "title", "searchConfirm", "changetab", "scrollTop", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyMhxB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,gDACA,+CACA,oDACA,+CACA,sDACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAC;QAAAb;QAAAF;MAAA;QACAY;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACA;YACA;YACAR;YACAC;YACA;cACAD;cACAC;YACA;YACAO;YACAA;UACA;UAEAI;YACAC;UACA;UACAL;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAN;MACAA;IACA;IACAO;MACA;MACAH;QACAI;QACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7TA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/article/artlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/article/artlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./artlist.vue?vue&type=template&id=8d076cb8&\"\nvar renderjs\nimport script from \"./artlist.vue?vue&type=script&lang=js&\"\nexport * from \"./artlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./artlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/article/artlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlist.vue?vue&type=template&id=8d076cb8&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    waterfallArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/waterfall-article/waterfall-article\" */ \"@/components/waterfall-article/waterfall-article.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.clist.length > 0 && !_vm.look_type : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的文章\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<dd-tab :itemdata=\"cnamelist\" :itemst=\"cidlist\" :st=\"cid\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"clist.length>0 && !look_type\"></dd-tab>\n\t\t\n\n\t\t<view class=\"article_list\">\n\t\t\t<!--横排-->\n\t\t\t<view v-if=\"listtype=='0'\" class=\"article-itemlist\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item.id\">\n\t\t\t\t<view class=\"article-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"article-info\">\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!--双排-->\n\t\t\t<view v-if=\"listtype=='1'\" class=\"article-item2\" v-for=\"(item,index) in datalist\" :key=\"item.id\" :style=\"{marginRight:index%2==0?'2%':'0'}\" @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item.id\">\n\t\t\t\t<view class=\"article-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"article-info\">\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<waterfall-article v-if=\"listtype=='2'\" :list=\"datalist\" ref=\"waterfall\"></waterfall-article>\n\t\t\t<!--单排-->\n\t\t\t<view v-if=\"listtype=='3'\" class=\"article-item1\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item.id\">\n\t\t\t\t<view class=\"article-pic\">\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"article-info\">\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\n\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\r\n            <!-- 三排显示s -->\r\n            <view v-if=\"listtype=='4'\" class=\"article-item3\" v-for=\"(item,index) in datalist\" :key=\"item.id\" :style=\"{marginRight:(index+1)%3==0?'0':'2%'}\" @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item.id\">\r\n                <view class=\"article-info\">\r\n                \t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                </view>\r\n                <view class=\"article-pic\">\r\n                \t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.po_status && item.po_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                        {{item.po_name}} {{item.po_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pt_status && item.pt_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                        {{item.pt_name}} {{item.pt_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pth_status && item.pth_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                        {{item.pth_name}} {{item.pth_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pf_status && item.pf_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                        {{item.pf_name}} {{item.pf_content}}\r\n                    </view>\r\n                </view>\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p2\">\r\n            \t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n            \t\t</view>\r\n                    <view class=\"p2\">\r\n                    \t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n                    </view>\r\n            \t</view>\r\n            </view>\r\n            <!-- 三排显示e -->\r\n            \r\n            <!--单排三图s-->\r\n            <view v-if=\"listtype=='5'\" class=\"article-item1\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExt/article/detail?id='+item.id\">\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n            \t</view>\r\n                <view class=\"article-pic\">\r\n                    <block v-if=\"item.pic\" v-for=\"(img,index) in item.pic\">\r\n                        <image class=\"image\" :src=\"img\" style=\"width: 220rpx;height: 220rpx;margin: 8rpx;\"/>\r\n                    </block>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.po_status && item.po_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                        {{item.po_name}} {{item.po_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pt_status && item.pt_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                        {{item.pt_name}} {{item.pt_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pth_status && item.pth_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                        {{item.pth_name}} {{item.pth_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pf_status && item.pf_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                        {{item.pf_name}} {{item.pf_content}}\r\n                    </view>\r\n                </view>\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p2\">\r\n            \t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n            \t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n            \t\t</view>\r\n            \t</view>\r\n            </view>\r\n            <!--单排三图e-->\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n\t\t\tnodata:false,\n\t\t\tnomore:false,\n\t\t\tkeyword:'',\n      datalist: [],\n      pagenum: 1,\n\t\t\tclist:[],\n\t\t\tcnamelist:[],\n\t\t\tcidlist:[],\n      datalist: [],\n      cid: 0,\n\t\t\tbid: 0,\n\t\t\tlisttype:0,\r\n            set:'',\r\n            look_type:false,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.cid = this.opt.cid || 0;\t\n\t\tthis.bid = this.opt.bid || 0;\r\n        this.look_type = this.opt.look_type || false;\r\n        if(this.opt.keyword) {\r\n        \tthis.keyword = this.opt.keyword;\r\n        }\n    this.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nomore && !this.nodata) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n      var cid = that.cid;\n\t\t\tconsole.log(cid)\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiArticle/getartlist', {bid:that.bid,cid: cid,pagenum: pagenum,keyword:keyword}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.listtype = res.listtype || 0;\n\t\t\t\t\tthat.clist    = res.clist;\r\n                    that.set      = res.set;\n\t\t\t\t\tif((res.clist).length > 0){\n\t\t\t\t\t\tvar cnamelist = [];\n\t\t\t\t\t\tvar cidlist = [];\n\t\t\t\t\t\tcnamelist.push('全部');\n\t\t\t\t\t\tcidlist.push('0');\n\t\t\t\t\t\tfor(var i in that.clist){\n\t\t\t\t\t\t\tcnamelist.push(that.clist[i].name);\n\t\t\t\t\t\t\tcidlist.push(that.clist[i].id);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.cnamelist = cnamelist;\n\t\t\t\t\t\tthat.cidlist = cidlist;\n\t\t\t\t\t}\n\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: res.title\n\t\t\t\t\t});\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.getdata();\n    },\n    changetab: function (cid) {\n      this.cid = cid;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n\t\t\tif(this.listtype==2){\n\t\t\t\tthis.$refs.waterfall.refresh();\n\t\t\t}\n      this.getdata();\n    },\n  }\n};\r\n</script>\r\n<style>\r\npage{background:#f6f6f7}\n.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}\n\n.article_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}\r\n.article_list .article-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}\n.article_list .article-item1 .article-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}\n.article_list .article-item1 .article-pic .image{width: 100%;height:auto}\n.article_list .article-item1 .article-info {padding:10rpx 20rpx 20rpx 20rpx;}\n.article_list .article-item1 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.article_list .article-item1 .article-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}\n.article_list .article-item1 .article-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}\n.article_list .article-item1 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}\r\n\n.article_list .article-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\n/*.article-item2:nth-child(even){margin-right:2%}*/\n.article_list .article-item2 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\n.article_list .article-item2 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.article_list .article-item2 .article-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\n.article_list .article-item2 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.article_list .article-item2 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\n\n.article_list .article-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}\n.article_list .article-itemlist .article-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}\n.article_list .article-itemlist .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.article_list .article-itemlist .article-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}\n.article_list .article-itemlist .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}\n.article_list .article-itemlist .article-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}\r\n\r\n.article_list .article-item3 {width: 32%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\n/*.article-item3:nth-child(even){margin-right:2%}*/\n.article_list .article-item3 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\n.article_list .article-item3 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.article_list .article-item3 .article-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\n.article_list .article-item3 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.article_list .article-item3 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102762\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}