{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?2411", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?42db", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?09a7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?2bea", "uni-app:///pagesExb/message/msglist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?0f36", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/message/msglist.vue?2bda"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "list", "pagenum", "clist", "cnamelist", "cidlist", "cid", "bid", "listtype", "set", "look_type", "read_status", "total_count", "read_count", "unread_count", "current", "tablist", "name", "id", "onLoad", "console", "onPullDownRefresh", "setTimeout", "uni", "onReachBottom", "methods", "getdata", "refresh", "that", "app", "title", "searchConfirm", "changetab", "scrollTop", "duration", "getReadStatistics", "url", "method", "success", "fail", "filterByReadStatus", "markAllAsRead", "content", "searchMessages", "tabchange", "goDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyFhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,UACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAC;IACAC;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACAA;IACA;IACA;EACA;EACAC;IACAD;IACA;IACA;IACAE;MACAC;IACA;EACA;EACAC;IACAJ;IACA;MACA;MACA;IACA;EACA;EACAK;IACAC;MAAA;MACAN;QAAAO;QAAAzB;MAAA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA0B;MACAA;MACAA;MACAC;QAAAtB;QAAAD;QAAAJ;QAAAF;QAAAW;MAAA;QACAiB;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACA;YACA;YACAxB;YACAC;YACA;cACAD;cACAC;YACA;YACAuB;YACAA;UACA;UAEAL;YACAO;UACA;UACAF;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACAH;MACAA;IACA;IACAI;MACA;MACAT;QACAU;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAf;MAEAG;QACAa;QACAC;QACAC;UACAlB;UAEA;YACA;YACA;YACA;YACA;UACA;QACA;QACAmB;UACAnB;QACA;MACA;IACA;IACAoB;MACApB;MAEA;QACA;QACA;MACA;IACA;IACAqB;MAAA;MACArB;MAEAG;QACAO;QACAY;QACAJ;UACA;YACAf;cACAa;cACAC;cACAC;gBACAlB;gBAEA;kBACA;kBACA;kBACA;gBACA;kBACA;gBACA;cACA;cACAmB;gBACAnB;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAuB;MACAvB;MACA;IACA;IACAwB;MACAxB;MACA;MACA;IACA;IACAyB;MACAzB;MACAG;QACAa;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvSA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/message/msglist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/message/msglist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./msglist.vue?vue&type=template&id=48d45741&\"\nvar renderjs\nimport script from \"./msglist.vue?vue&type=script&lang=js&\"\nexport * from \"./msglist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./msglist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/message/msglist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./msglist.vue?vue&type=template&id=48d45741&\"", "var components\ntry {\n  components = {\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && _vm.list.length === 0\n  var g1 = _vm.loading && _vm.list.length === 0\n  var g2 = !_vm.nomore && _vm.list.length > 0\n  var g3 = _vm.nomore && _vm.list.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./msglist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./msglist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<!-- 顶部导航栏 -->\r\n\t<view class=\"nav-header\">\r\n\t\t<view class=\"nav-title\">消息通知</view>\r\n\t\t<view class=\"nav-actions\">\r\n\t\t\t<view class=\"unread-count\" v-if=\"unread_count > 0\">{{unread_count}}</view>\r\n\t\t\t<view class=\"action-btn\" @click=\"markAllAsRead\" v-if=\"unread_count > 0\">全部已读</view>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 搜索栏 -->\r\n\t<view class=\"search-container\">\r\n\t\t<view class=\"search-box\">\r\n\t\t\t<image src=\"/static/icon/search.png\" class=\"search-icon\"></image>\r\n\t\t\t<input type=\"text\" v-model=\"keyword\" placeholder=\"搜索消息\" @confirm=\"searchMessages\" />\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 筛选标签 -->\r\n\t<view class=\"filter-tabs\">\r\n\t\t<view class=\"tab-item\" :class=\"{active: read_status === 'all'}\" @click=\"filterByReadStatus('all')\">\r\n\t\t\t全部\r\n\t\t</view>\r\n\t\t<view class=\"tab-item\" :class=\"{active: read_status === 'unread'}\" @click=\"filterByReadStatus('unread')\">\r\n\t\t\t未读 <text v-if=\"unread_count > 0\" class=\"tab-count\">({{unread_count}})</text>\r\n\t\t</view>\r\n\t\t<view class=\"tab-item\" :class=\"{active: read_status === 'read'}\" @click=\"filterByReadStatus('read')\">\r\n\t\t\t已读 <text v-if=\"read_count > 0\" class=\"tab-count\">({{read_count}})</text>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 消息列表 - 微信聊天样式 -->\r\n\t<view class=\"message-list\">\r\n\t\t<view v-for=\"(item, index) in list\" :key=\"index\" class=\"chat-item\" @click=\"goDetail(item)\">\r\n\t\t\t<!-- 左侧头像区域 -->\r\n\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t<image :src=\"item.pic || '/static/img/default-message.png'\" class=\"message-avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<!-- 未读红点 -->\r\n\t\t\t\t<view v-if=\"item.is_read == 0\" class=\"red-dot\"></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 右侧内容区域 -->\r\n\t\t\t<view class=\"content-container\">\r\n\t\t\t\t<view class=\"content-header\">\r\n\t\t\t\t\t<view class=\"message-title\">{{item.title}}</view>\r\n\t\t\t\t\t<view class=\"message-time\">{{item.createtime_text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content-body\">\r\n\t\t\t\t\t<view class=\"message-preview\">{{item.subtitle || item.content}}</view>\r\n\t\t\t\t\t<view class=\"message-status\">\r\n\t\t\t\t\t\t<text v-if=\"item.is_read == 0\" class=\"unread-indicator\">●</text>\r\n\t\t\t\t\t\t<text class=\"read-count\">{{item.readcount}}人已读</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 无数据提示 -->\r\n\t<view v-if=\"!loading && list.length === 0\" class=\"empty-state\">\r\n\t\t<image src=\"/static/img/empty-message.png\" class=\"empty-icon\"></image>\r\n\t\t<text class=\"empty-text\">暂无消息通知</text>\r\n\t</view>\r\n\t\r\n\t<!-- 加载状态 -->\r\n\t<view v-if=\"loading && list.length === 0\" class=\"loading-state\">\r\n\t\t<text>加载中...</text>\r\n\t</view>\r\n\t\r\n\t<!-- 加载更多 -->\r\n\t<view v-if=\"!nomore && list.length > 0\" class=\"load-more\">\r\n\t\t<text>上拉加载更多</text>\r\n\t</view>\r\n\t\r\n\t<!-- 没有更多 -->\r\n\t<view v-if=\"nomore && list.length > 0\" class=\"no-more\">\r\n\t\t<text>没有更多消息了</text>\r\n\t</view>\r\n\t\r\n\t<!-- 底部导航 -->\r\n\t<dp-tabbar :current=\"0\"></dp-tabbar>\r\n\t\r\n\t<!-- 弹窗消息 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:true,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tnodata:false,\r\n\t\t\tnomore:false,\r\n\t\t\tkeyword:'',\r\n      list: [],\r\n      pagenum: 1,\r\n\t\t\tclist:[],\r\n\t\t\tcnamelist:[],\r\n\t\t\tcidlist:[],\r\n      cid: 0,\r\n\t\t\tbid: 0,\r\n\t\t\tlisttype:0,\r\n            set:'',\r\n            look_type:false,\r\n\t\t\tread_status: 'all',\r\n\t\t\ttotal_count: 0,\r\n\t\t\tread_count: 0,\r\n\t\t\tunread_count: 0,\r\n\t\t\tcurrent: 0,\r\n\t\t\ttablist: [\r\n\t\t\t\t{name: '全部', id: 0},\r\n\t\t\t\t{name: '系统通知', id: 1},\r\n\t\t\t\t{name: '活动消息', id: 2},\r\n\t\t\t\t{name: '订单消息', id: 3}\r\n\t\t\t]\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[msglist][onLoad_001] 页面加载开始');\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.cid = this.opt.cid || 0;\t\r\n\t\tthis.bid = this.opt.bid || 0;\r\n        this.look_type = this.opt.look_type || false;\r\n        if(this.opt.keyword) {\r\n        \tthis.keyword = this.opt.keyword;\r\n        }\r\n\t\tconsole.log('2025-01-03 22:55:53,566-INFO-[msglist][onLoad_002] 参数解析完成', this.opt);\r\n\t\tthis.getdata(true);\r\n\t\tthis.getReadStatistics();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tconsole.log('2025-01-03 22:55:53,567-INFO-[msglist][onPullDownRefresh_001] 下拉刷新开始');\r\n\t\tthis.getdata(true);\r\n\t\tthis.getReadStatistics();\r\n\t\tsetTimeout(() => {\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t}, 1000);\r\n  },\r\n  onReachBottom: function () {\r\n\t\tconsole.log('2025-01-03 22:55:53,568-INFO-[msglist][onReachBottom_001] 触底加载更多');\r\n    if (!this.nomore && !this.nodata) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(false);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function (refresh = false) {\r\n\t\t\tconsole.log('2025-01-03 22:55:53,569-INFO-[msglist][getdata_001] 开始获取数据', {refresh, pagenum: this.pagenum});\r\n\t\t\tif (refresh) {\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.list = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var keyword = that.keyword;\r\n      var cid = that.cid;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('ApiMessageNotify/getmsglist', {bid:that.bid,cid: cid,pagenum: pagenum,keyword:keyword, read_status: that.read_status}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.listtype = res.listtype || 0;\r\n\t\t\t\t\tthat.clist    = res.clist;\r\n                    that.set      = res.set;\r\n\t\t\t\t\tif((res.clist).length > 0){\r\n\t\t\t\t\t\tvar cnamelist = [];\r\n\t\t\t\t\t\tvar cidlist = [];\r\n\t\t\t\t\t\tcnamelist.push('全部');\r\n\t\t\t\t\t\tcidlist.push('0');\r\n\t\t\t\t\t\tfor(var i in that.clist){\r\n\t\t\t\t\t\t\tcnamelist.push(that.clist[i].name);\r\n\t\t\t\t\t\t\tcidlist.push(that.clist[i].id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.cnamelist = cnamelist;\r\n\t\t\t\t\t\tthat.cidlist = cidlist;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.title\r\n\t\t\t\t\t});\r\n          that.list = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.list;\r\n            var newdata = datalist.concat(data);\r\n            that.list = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    searchConfirm: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword\r\n      that.getdata();\r\n    },\r\n    changetab: function (cid) {\r\n      this.cid = cid;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n    getReadStatistics() {\r\n      console.log('2025-01-03 23:15:01,008-INFO-[msglist][getReadStatistics_001] 开始获取阅读统计');\r\n      \r\n      uni.request({\r\n        url: this.$config.apiurl + '/api/message_notify/getReadStatistics',\r\n        method: 'POST',\r\n        success: (res) => {\r\n          console.log('2025-01-03 23:15:01,009-INFO-[msglist][getReadStatistics_002] 统计响应：' + JSON.stringify(res.data));\r\n          \r\n          if (res.data.code === 1) {\r\n            const stats = res.data.data;\r\n            this.total_count = stats.total_count;\r\n            this.read_count = stats.read_count;\r\n            this.unread_count = stats.unread_count;\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('2025-01-03 23:15:01,010-ERROR-[msglist][getReadStatistics_003] 统计请求失败：' + JSON.stringify(err));\r\n        }\r\n      });\r\n    },\r\n    filterByReadStatus(status) {\r\n      console.log('2025-01-03 23:15:01,011-INFO-[msglist][filterByReadStatus_001] 筛选状态：' + status);\r\n      \r\n      if (this.read_status !== status) {\r\n        this.read_status = status;\r\n        this.getdata(true);\r\n      }\r\n    },\r\n    markAllAsRead() {\r\n      console.log('2025-01-03 23:15:01,012-INFO-[msglist][markAllAsRead_001] 开始批量标记已读');\r\n      \r\n      uni.showModal({\r\n        title: '确认操作',\r\n        content: '确定要将所有未读消息标记为已读吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.request({\r\n              url: this.$config.apiurl + '/api/message_notify/markAllAsRead',\r\n              method: 'POST',\r\n              success: (res) => {\r\n                console.log('2025-01-03 23:15:01,013-INFO-[msglist][markAllAsRead_002] 批量标记响应：' + JSON.stringify(res.data));\r\n                \r\n                if (res.data.code === 1) {\r\n                  this.$refs.popmsg.show('标记成功');\r\n                  this.getdata(true);\r\n                  this.getReadStatistics();\r\n                } else {\r\n                  this.$refs.popmsg.show(res.data.msg || '标记失败');\r\n                }\r\n              },\r\n              fail: (err) => {\r\n                console.log('2025-01-03 23:15:01,014-ERROR-[msglist][markAllAsRead_003] 批量标记失败：' + JSON.stringify(err));\r\n                this.$refs.popmsg.show('网络请求失败');\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    searchMessages() {\r\n      console.log('2025-01-03 23:15:01,015-INFO-[msglist][searchMessages_001] 搜索关键词：' + this.keyword);\r\n      this.getdata(true);\r\n    },\r\n    tabchange(index) {\r\n      console.log('2025-01-03 23:15:01,016-INFO-[msglist][tabchange_001] 切换分类：' + index);\r\n      this.current = index;\r\n      this.getdata(true);\r\n    },\r\n    goDetail(item) {\r\n      console.log('2025-01-03 23:15:01,017-INFO-[msglist][goDetail_001] 跳转详情，id=' + item.id);\r\n      uni.navigateTo({\r\n        url: '/pagesExb/message/detail?id=' + item.id\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {\r\n\tbackground: #f6f6f7;\r\n}\r\n\r\n/* 顶部导航栏 */\r\n.nav-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: #fff;\r\n\tborder-bottom: 1rpx solid #e5e5e5;\r\n}\r\n\r\n.nav-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.nav-actions {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.unread-count {\r\n\tbackground: #ff4757;\r\n\tcolor: #fff;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 20rpx;\r\n\tfont-size: 22rpx;\r\n\tmargin-right: 20rpx;\r\n\tmin-width: 40rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.action-btn {\r\n\tpadding: 10rpx 20rpx;\r\n\tbackground: #007aff;\r\n\tcolor: #fff;\r\n\tborder-radius: 20rpx;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n/* 搜索容器 */\r\n.search-container {\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: #fff;\r\n\tborder-bottom: 1rpx solid #e5e5e5;\r\n}\r\n\r\n.search-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: #f5f5f5;\r\n\tborder-radius: 30rpx;\r\n\tpadding: 15rpx 20rpx;\r\n}\r\n\r\n.search-icon {\r\n\twidth: 30rpx;\r\n\theight: 30rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.search-box input {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tbackground: transparent;\r\n\tborder: none;\r\n}\r\n\r\n/* 筛选标签 */\r\n.filter-tabs {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: #fff;\r\n\tborder-bottom: 1rpx solid #e5e5e5;\r\n}\r\n\r\n.tab-item {\r\n\tpadding: 12rpx 24rpx;\r\n\tmargin-right: 20rpx;\r\n\tborder-radius: 25rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tbackground: #f0f0f0;\r\n\tposition: relative;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground: #007aff;\r\n\tcolor: #fff;\r\n}\r\n\r\n.tab-count {\r\n\tfont-size: 24rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n/* 消息列表 - 微信聊天样式 */\r\n.message-list {\r\n\tbackground: #fff;\r\n}\r\n\r\n.chat-item {\r\n\tdisplay: flex;\r\n\tpadding: 25rpx 30rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tposition: relative;\r\n\tbackground: #fff;\r\n}\r\n\r\n.chat-item:active {\r\n\tbackground: #f5f5f5;\r\n}\r\n\r\n/* 左侧头像区域 */\r\n.avatar-container {\r\n\tposition: relative;\r\n\tmargin-right: 25rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.message-avatar {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground: #f0f0f0;\r\n}\r\n\r\n/* 未读红点 */\r\n.red-dot {\r\n\tposition: absolute;\r\n\ttop: -5rpx;\r\n\tright: -5rpx;\r\n\twidth: 20rpx;\r\n\theight: 20rpx;\r\n\tbackground: #ff4757;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid #fff;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-container {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n\tmin-height: 100rpx;\r\n}\r\n\r\n.content-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.message-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n\tflex: 1;\r\n\tmargin-right: 20rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.message-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.content-body {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-end;\r\n}\r\n\r\n.message-preview {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tflex: 1;\r\n\tmargin-right: 20rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tline-height: 1.4;\r\n}\r\n\r\n.message-status {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.unread-indicator {\r\n\tcolor: #ff4757;\r\n\tfont-size: 20rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.read-count {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 120rpx 60rpx;\r\n\tbackground: #fff;\r\n}\r\n\r\n.empty-icon {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 40rpx;\r\n\topacity: 0.6;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-state {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 80rpx;\r\n\tbackground: #fff;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 40rpx;\r\n\tbackground: #fff;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 没有更多 */\r\n.no-more {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 40rpx;\r\n\tbackground: #fff;\r\n\tfont-size: 26rpx;\r\n\tcolor: #ccc;\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}\r\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}\r\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}\r\n\r\n.message_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}\r\n.message_list .message-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}\r\n.message_list .message-item1 .message-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}\r\n.message_list .message-item1 .message-pic .image{width: 100%;height:auto}\r\n.message_list .message-item1 .message-info {padding:10rpx 20rpx 20rpx 20rpx;}\r\n.message_list .message-item1 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.message_list .message-item1 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.message_list .message-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\r\n.message_list .message-item2 .message-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\r\n.message_list .message-item2 .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.message_list .message-item2 .message-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\r\n.message_list .message-item2 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.message_list .message-item2 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.message_list .message-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}\r\n.message_list .message-itemlist .message-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}\r\n.message_list .message-itemlist .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.message_list .message-itemlist .message-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}\r\n.message_list .message-itemlist .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}\r\n.message_list .message-itemlist .message-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}\r\n\r\n.message_list .message-item3 {width: 32%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\r\n.message_list .message-item3 .message-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\r\n.message_list .message-item3 .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.message_list .message-item3 .message-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\r\n.message_list .message-item3 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.message_list .message-item3 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./msglist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./msglist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024363\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}