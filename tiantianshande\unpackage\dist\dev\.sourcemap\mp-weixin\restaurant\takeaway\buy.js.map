{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?8fcb", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?f92d", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?427d", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?2464", "uni-app:///restaurant/takeaway/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?7c78", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/buy.vue?5836"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "havetongcheng", "address", "usescore", "scoredk_money", "totalprice", "couponvisible", "cuxiaovisible", "bid", "nowbid", "needaddress", "linkman", "tel", "userinfo", "pstimeDialogShow", "pstimeIndex", "manjian_money", "cxid", "latitude", "longitude", "allbuydata", "alltotalprice", "cuxiaoin<PERSON>", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "prodata", "storedata", "scoredk", "inputLinkman", "inputTel", "inputfield", "<PERSON><PERSON><PERSON><PERSON>", "calculatePrice", "freight_price", "coupon_money", "allfreight_price", "changeFreight", "console", "chooseFreight", "itemlist", "uni", "itemList", "success", "choosePstime", "pstimeRadioChange", "hidePstimeDialog", "chooseCoupon", "choosestore", "topay", "buydata", "cuxiaoid", "couponrid", "freight_id", "freight_time", "storeid", "message", "field1", "field2", "field3", "field4", "field5", "frompage", "addressid", "showCouponList", "handleClickMask", "showCuxiaoList", "changecx", "id", "chooseCuxiao", "openLocation", "name", "scale"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2N5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACA;YACAC;cACA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAC;YACA;YACA;YACAD;YACAA;YACA;YACA;cACA;cACA;gBACA;kBACA;kBACA;oBACA;sBACA;wBACA;wBACAG;sBACA;oBACA;oBACAA;sBACA;oBACA;oBACA;sBACA;wBACAA;sBACA;oBACA;oBACAV;kBACA;gBACA;cACA;YACA;YACAO;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAd;MACA;IACA;IACA;IACAe;MACAP;IACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA1B;QACA;QACA;UACA2B;UACAC;QACA;QACA;QACA;;QAEAjC;QACAe;QACAA;QACAC;QACAkB;MACA;MACAZ;MAEA;QACA;MACA;QACA;MACA;MAEA;MACAN;MACA;MAEA;QACA;QACAA;QACAjB;MACA;MACA;MACA;MACA;MAEA;QACAA;QACAiB;MACA;QACAjB;QACAiB;MACA;MAEA;MACAA;MACAM;MACAA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACA;QACAb;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAR;MACAO;MACAA;IACA;IACAe;MACA;MACA;MACA;MACAD;MACAA;MACA;MACA;MAEA;QACAE;MACA;MAEAC;QACAC;QACAC;UACA;YACA1B;YACAO;YACAA;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;MACA;MACA;QACAf;QACA;MACA;MACAD;MACAA;MACAA;IACA;IACAqB;MACA;MACA;MACA;MACAP;MACA;MACA;MACA;MACA;MACA;MACA;MACArB;MACAA;MACAO;MACAA;IACA;IACAsB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA9B;QACAA;QACAA;QACAA;QACA;QACA;MACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;MACA;MACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MACA;MACA/B;MACA;IACA;IACA;IACAgC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAxB;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;QAEAyB;UACA7C;UACAqB;UACAyB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACApC;MACAA;QAAAqC;QAAAZ;QAAAa;QAAAvD;QAAAC;QAAAT;MAAA;QACAyB;QACA;UACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACAuC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA7B;MACAd;MACA;QACAA;QACA;MACA;MACAC;QAAA2C;MAAA;QACA5C;MACA;IACA;IACA6C;MACA;MACA;MACA;MACA;MACA;QACApD;QACAA;QACAA;MACA;QACA;QACAqB;QACA;UACA;UACArB;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;UACA;UACA;UACA;UACA;UACAA;QACA;QACAA;QACAA;QACAA;MACA;MACA;MACA;MACA;IACA;IACAqD;MACA;MACA;MACA;MACA;MACA;MACA;MACAhC;MACA;MACA;MACA;MACAG;QACA1B;QACAC;QACAuD;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrqBA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/takeaway/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/takeaway/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=6c485640&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/takeaway/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=6c485640&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l4 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var l0 = _vm.__map(buydata.prodata, function (item, index2) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = parseFloat(\n            parseFloat(item.guige.sell_price) + parseFloat(item.jldata.jlprice)\n          ).toFixed(2)\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n        var l1 = _vm.__map(buydata.freightList, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = buydata.freightkey == idx2 ? _vm.t(\"color1\") : null\n          var m1 = buydata.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n        var g1 =\n          buydata.freightList[buydata.freightkey].minpriceset == 1 &&\n          buydata.freightList[buydata.freightkey].minprice > 0 &&\n          buydata.freightList[buydata.freightkey].minprice >\n            buydata.product_price\n            ? (\n                buydata.freightList[buydata.freightkey].minprice -\n                buydata.product_price\n              ).toFixed(2)\n            : null\n        var l2 =\n          buydata.freightList[buydata.freightkey].pstype == 1\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m2 =\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m2: m2,\n                  }\n                }\n              )\n            : null\n        var l3 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m3 =\n                    (idx < 5 || _vm.storeshowall == true) &&\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m3: m3,\n                  }\n                }\n              )\n            : null\n        var g2 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var m4 = buydata.leveldk_money > 0 ? _vm.t(\"会员\") : null\n        var m5 = _vm.t(\"优惠券\")\n        var m6 = buydata.couponCount > 0 ? _vm.t(\"color1\") : null\n        var m7 = !(buydata.couponCount > 0) ? _vm.t(\"优惠券\") : null\n        var m8 = buydata.cuxiaoCount > 0 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          l0: l0,\n          l1: l1,\n          g1: g1,\n          l2: l2,\n          l3: l3,\n          g2: g2,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 =\n    _vm.isload &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m10 =\n    _vm.isload &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m11 = _vm.isload ? _vm.t(\"color1\") : null\n  var m12 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m13 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l5 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.allbuydata[_vm.nowbid].freightList[\n            _vm.allbuydata[_vm.nowbid].freightkey\n          ].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m14 =\n              _vm.allbuydata[_vm.nowbid].freight_time == item.value\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m14: m14,\n            }\n          }\n        )\n      : null\n  var m15 =\n    _vm.isload && _vm.cuxiaovisible && _vm.cxid == 0 ? _vm.t(\"color1\") : null\n  var l6 =\n    _vm.isload && _vm.cuxiaovisible\n      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m16 = _vm.cxid == item.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m16: m16,\n          }\n        })\n      : null\n  var m17 = _vm.isload && _vm.cuxiaovisible ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l4: l4,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        l5: l5,\n        m15: m15,\n        l6: l6,\n        m17: m17,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\n\t\t\t<view class=\"linkitem\">\n\t\t\t\t<text class=\"f1\">联 系 人：</text>\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t</view>\n\t\t\t<view class=\"linkitem\">\n\t\t\t\t<text class=\"f1\">联系电话：</text>\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\n\t\t\t<view class=\"f1\"><image class=\"img\" src=\"/static/img/address.png\"/></view>\n\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\n\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\n\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\n\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"></image>\n\t\t</view>\n\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\n\t\t\t<view class=\"btitle\"><image class=\"img\" src=\"/static/img/ico-shop.png\"/>{{buydata.business.name}}</view>\n\t\t\t<view class=\"bcontent\">\n\t\t\t\t<view class=\"product\">\n\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\" class=\"item flex\">\n\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + item.product.id\"><image :src=\"item.product.pic\"></image></view>\n\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}{{item.jldata.jltitle}}</view>\n\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-weight:bold;\">￥{{parseFloat(parseFloat(item.guige.sell_price)+parseFloat(item.jldata.jlprice)).toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{item.num}}</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"freight\">\n\t\t\t\t\t<view class=\"f1\">配送方式</view>\n\t\t\t\t\t<view class=\"freight-ul\">\n\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\n\t\t\t\t\t\t <block v-for=\"(item, idx2) in buydata.freightList\" :key=\"idx2\">\n\t\t\t\t\t\t <view class=\"freight-li\" :style=\"buydata.freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeFreight\" :data-bid=\"buydata.bid\" :data-index=\"idx2\">{{item.name}}</view>\n\t\t\t\t\t\t </block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].minpriceset==1 && buydata.freightList[buydata.freightkey].minprice > 0 && buydata.freightList[buydata.freightkey].minprice > buydata.product_price\">满{{buydata.freightList[buydata.freightkey].minprice}}元起送，还差{{(buydata.freightList[buydata.freightkey].minprice - buydata.product_price).toFixed(2)}}元</view>\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].isoutjuli==1\">超出配送范围</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"price\" v-if=\"buydata.freightList[buydata.freightkey].pstimeset==1\">\n\t\t\t\t\t<view class=\"f1\">{{buydata.freightList[buydata.freightkey].pstype==1?'取货':'配送'}}时间</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\" :data-bid=\"buydata.bid\">{{buydata.pstimetext==''?'请选择时间':buydata.pstimetext}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==1\">\n\t\t\t\t\t<view class=\"panel\">\n\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"openLocation\" :data-bid=\"buydata.bid\" :data-freightkey=\"buydata.freightkey\" :data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text class=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\n\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\n\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==5\">\r\n\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t<view class=\"f1\">配送门店</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-bid=\"buydata.bid\"\r\n\t\t\t\t\t\t\t:data-freightkey=\"buydata.freightkey\"\r\n\t\t\t\t\t\t\t:data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text\r\n\t\t\t\t\t\t\t\tclass=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t<view class=\"radio\"\r\n\t\t\t\t\t\t\t\t:style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\r\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<text class=\"f1\">商品金额</text>\n\t\t\t\t\t<text class=\"f2\">¥{{buydata.product_price}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.leveldk_money>0\">\n\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\n\t\t\t\t\t<text class=\"f2\">-¥{{buydata.leveldk_money}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.manjian_money>0\">\n\t\t\t\t\t<text class=\"f1\">满减活动</text>\n\t\t\t\t\t<text class=\"f2\">-¥{{buydata.manjian_money}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<text class=\"f1\">{{buydata.freightList[buydata.freightkey].freight_price_txt || '运费'}}</text>\n\t\t\t\t\t<text class=\"f2\">+¥{{buydata.freightList[buydata.freightkey].freight_price}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.pack_fee>0\">\n\t\t\t\t\t<text class=\"f1\">打包费</text>\n\t\t\t\t\t<text class=\"f2\">+¥{{buydata.pack_fee}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\n\t\t\t\t\t<view v-if=\"buydata.couponCount > 0\" class=\"f2\" @tap=\"showCouponList\" :data-bid=\"buydata.bid\"><text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{buydata.couponrid!=0?buydata.couponList[buydata.couponkey].couponname:buydata.couponCount+'张可用'}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.cuxiaoCount > 0\">\n\t\t\t\t\t<view class=\"f1\">促销活动</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showCuxiaoList\" :data-bid=\"buydata.bid\"><text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{buydata.cuxiaoname?buydata.cuxiaoname:buydata.cuxiaoCount+'个可用'}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t</view>\n\t\t\t\t<block v-for=\"(item,idx) in buydata.freightList[buydata.freightkey].field_list\">\n\t\t\t\t<view class=\"remark\" v-if=\"item.isshow==1\">\n\t\t\t\t\t<text class=\"f1\">{{item.name}}</text>\n\t\t\t\t\t<input type=\"text\" class=\"flex1\" :placeholder=\"item.tips || '请输入'+item.name\" @input=\"inputfield\" :data-field=\"idx\" :data-bid=\"buydata.bid\" placeholder-style=\"color:#cdcdcd;font-size:28rpx\"></input>\n\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"scoredk\" v-if=\"userinfo.score2money>0 && (userinfo.scoremaxtype==0 || (userinfo.scoremaxtype==1 && userinfo.scoredkmaxmoney>0))\">\n\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoremaxtype==0 && userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtype==1\">最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t</view>\n\t\t\t</checkbox-group>\n\t\t</view>\n\t\t<view style=\"width: 100%; height:110rpx;\"></view>\n\t\t<view class=\"footer flex\">\n\t\t\t<view class=\"text1 flex1\">总计：\n\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{alltotalprice}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"op\" @tap=\"topay\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</view>\n\t\t</view>\n\n\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<couponlist :couponlist=\"allbuydata[bid].couponList\" :choosecoupon=\"true\" :selectedrid=\"allbuydata[bid].couponrid\" :bid=\"bid\" @chooseCoupon=\"chooseCoupon\"></couponlist>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送'}}时间</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstimeArr\" :key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"allbuydata[nowbid].freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"cuxiaovisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"cuxiao-desc\">\n\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changecx\" data-id=\"0\">\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">不使用促销</text></view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid==0 ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-for=\"(item, index) in allbuydata[bid].cuxiaolist\" :key=\"index\" class=\"cuxiao-item\" @tap=\"changecx\" :data-id=\"item.id\">\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text> <text style=\"color:#333;padding-left:20rpx\">{{item.name}}</text></view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid==item.id ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view id=\"cxproinfo\" v-if=\"cuxiaoinfo.product\" style=\"padding:0 40rpx\">\n\t\t\t\t\t\t<view class=\"product\">\n\t\t\t\t\t\t\t<view class=\"item flex\" style=\"background:#f5f5f5\">\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + cuxiaoinfo.product.id\"><image :src=\"cuxiaoinfo.product.pic\"></image></view>\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cuxiaoinfo.product.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{cuxiaoinfo.guige.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{cuxiaoinfo.guige.sell_price}}</text><text style=\"padding-left:20rpx\"> × 1</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\n\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\n\t\t\t\t\t\t<view style=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\" :style=\"{background:t('color1')}\" @tap=\"chooseCuxiao\">确 定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\thavetongcheng:0,\n      address: [],\n      usescore: 0,\n      scoredk_money: 0,\n      totalprice: '0.00',\n      couponvisible: false,\n      cuxiaovisible: false,\n      bid: 0,\n      nowbid: 0,\n      needaddress: 1,\n      linkman: '',\n      tel: '',\n\t\t\tuserinfo:{},\n      pstimeDialogShow: false,\n      pstimeIndex: -1,\n      manjian_money: 0,\n      cxid: 0,\n      latitude: \"\",\n      longitude: \"\",\n      allbuydata: \"\",\n      alltotalprice: \"\",\n      cuxiaoinfo: false\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiRestaurantTakeaway/buy', {prodata: that.opt.prodata}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tif (res.msg) {\n\t\t\t\t\t\tapp.alert(res.msg, function () {\n\t\t\t\t\t\t\tif (res.url) {\n\t\t\t\t\t\t\t\tapp.goto(res.url);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (res.url) {\n\t\t\t\t\t\tapp.goto(res.url);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.alert('您没有权限购买该商品');\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.havetongcheng = res.havetongcheng;\n\t\t\t\tthat.address = res.address;\n\t\t\t\tthat.linkman = res.linkman;\n\t\t\t\tthat.tel = res.tel;\n\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\tthat.allbuydata = res.allbuydata;\n\t\t\t\tthat.needLocation = res.needLocation;\n\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\n\t\t\t\tthat.calculatePrice();\n\t\t\t\tthat.loaded();\n\n\t\t\t\tif (res.needLocation == 1) {\n\t\t\t\t\tapp.getLocation(function (res) {\n\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\t\tthat.latitude = latitude;\n\t\t\t\t\t\tthat.longitude = longitude;\n\t\t\t\t\t\tvar allbuydata = that.allbuydata;\n\t\t\t\t\t\tfor (var i in allbuydata) {\n\t\t\t\t\t\t\tvar freightList = allbuydata[i].freightList;\n\t\t\t\t\t\t\tfor (var j in freightList) {\n\t\t\t\t\t\t\t\tif (freightList[j].pstype == 1 || freightList[j].pstype == 5) {\n\t\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\n\t\t\t\t\t\t\t\t\tif (storedata) {\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\n\t\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tstoredata.sort(function (a, b) {\n\t\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tallbuydata[i].freightList[j].storedata = storedata;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.allbuydata = allbuydata;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    //积分抵扣\n    scoredk: function (e) {\n      var usescore = e.detail.value[0];\n      if (!usescore) usescore = 0;\n      this.usescore = usescore;\n      this.calculatePrice();\n    },\n    inputLinkman: function (e) {\n      this.linkman = e.detail.value;\n    },\n    inputTel: function (e) {\n      this.tel = e.detail.value;\n    },\n    inputfield: function (e) {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = e.currentTarget.dataset.bid;\n\t\t\tvar field = e.currentTarget.dataset.field;\n      allbuydata[bid][field] = e.detail.value;\n      this.allbuydata = allbuydata;\n    },\n    //选择收货地址\n    chooseAddress: function () {\n      app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\n    },\n    //计算价格\n    calculatePrice: function () {\n      var that = this;\n      var address = that.address;\n      var allbuydata = that.allbuydata;\n      var alltotalprice = 0;\n      var allfreight_price = 0;\n      var needaddress = 0;\n\n      for (var k in allbuydata) {\n        var product_price = parseFloat(allbuydata[k].product_price);\n        var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣\n        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动\n        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 \n        var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动  \n        var pack_fee = parseFloat(allbuydata[k].pack_fee); //+打包费\n        //算运费\n        var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];\n        var freight_price = freightdata['freight_price'];\n        if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\n          needaddress = 1;\n        }\n        if (allbuydata[k].coupontype == 4) {\n          freight_price = 0;\n          coupon_money = 0;\n        }\n        var totalprice = product_price + pack_fee - leveldk_money - manjian_money - coupon_money + cuxiao_money;\n        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\n\n        totalprice = totalprice + freight_price;\n        allbuydata[k].freight_price = freight_price.toFixed(2);\n        allbuydata[k].totalprice = totalprice.toFixed(2);\n        alltotalprice += totalprice;\n        allfreight_price += freight_price;\n      }\n      that.needaddress = needaddress;\n\n      if (that.usescore) {\n        var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣\n      } else {\n        var scoredk_money = 0;\n      }\n\n      var oldalltotalprice = alltotalprice;\n      alltotalprice = alltotalprice - scoredk_money;\n      if (alltotalprice < 0) alltotalprice = 0;\n\n      if (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {\n        //积分不抵扣运费\n        alltotalprice = allfreight_price;\n        scoredk_money = oldalltotalprice - allfreight_price;\n      }\n      var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\n      var scoremaxtype = parseInt(that.userinfo.scoremaxtype);\n      var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);\n\n      if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {\n        scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;\n        alltotalprice = oldalltotalprice - scoredk_money;\n      } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {\n        scoredk_money = scoredkmaxmoney;\n        alltotalprice = oldalltotalprice - scoredk_money;\n      }\n\n      if (alltotalprice < 0) alltotalprice = 0;\n      alltotalprice = alltotalprice.toFixed(2);\n      that.alltotalprice = alltotalprice;\n      that.allbuydata = allbuydata;\n    },\n    changeFreight: function (e) {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = e.currentTarget.dataset.bid;\n      var index = e.currentTarget.dataset.index;\n      var freightList = allbuydata[bid].freightList;\r\n\t\t\tconsole.log(allbuydata);\r\n\t\t\tconsole.log(freightList[index]);\r\n\t\t\tif(freightList[index].pstype==1 && freightList[index].storedata.length < 1) {\r\n\t\t\t\tapp.error('无可自提门店');return;\r\n\t\t\t}\r\n\t\t\tif(freightList[index].pstype==5 && freightList[index].storedata.length < 1) {\r\n\t\t\t\tapp.error('无可配送门店');return;\r\n\t\t\t}\n\t\t\tallbuydata[bid].freightkey = index;\n\t\t\tthat.allbuydata = allbuydata;\n\t\t\tthat.calculatePrice();\n    },\n    chooseFreight: function (e) {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = e.currentTarget.dataset.bid;\n      console.log(bid);\n      console.log(allbuydata);\n      var freightList = allbuydata[bid].freightList;\n      var itemlist = [];\n\n      for (var i = 0; i < freightList.length; i++) {\n        itemlist.push(freightList[i].name);\n      }\n\n      uni.showActionSheet({\n        itemList: itemlist,\n        success: function (res) {\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tallbuydata[bid].freightkey = res.tapIndex;\n\t\t\t\t\t\tthat.allbuydata = allbuydata;\n\t\t\t\t\t\tthat.calculatePrice();\n\t\t\t\t\t}\n        }\n      });\n    },\n    choosePstime: function (e) {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = e.currentTarget.dataset.bid;\n      var freightkey = allbuydata[bid].freightkey;\n      var freightList = allbuydata[bid].freightList;\n      var freight = freightList[freightkey];\n      var pstimeArr = freightList[freightkey].pstimeArr;\n      var itemlist = [];\n      for (var i = 0; i < pstimeArr.length; i++) {\n        itemlist.push(pstimeArr[i].title);\n      }\n      if (itemlist.length == 0) {\n        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\n        return;\n      }\n      that.nowbid = bid;\n      that.pstimeDialogShow = true;\n      that.pstimeIndex = -1;\n    },\n    pstimeRadioChange: function (e) {\n      var that = this;\n\t\t\tvar allbuydata = that.allbuydata;\n      var pstimeIndex = e.currentTarget.dataset.index;\n\t\t\tconsole.log(pstimeIndex)\n\t\t\tvar nowbid = that.nowbid;\n      var freightkey = allbuydata[nowbid].freightkey;\n      var freightList = allbuydata[nowbid].freightList;\n      var freight = freightList[freightkey];\n      var pstimeArr = freightList[freightkey].pstimeArr;\n\t\t\tvar choosepstime = pstimeArr[pstimeIndex];\n      allbuydata[nowbid].pstimetext = choosepstime.title;\n      allbuydata[nowbid].freight_time = choosepstime.value;\n      that.allbuydata = allbuydata\n      that.pstimeDialogShow = false;\n    },\n    hidePstimeDialog: function () {\n      this.pstimeDialogShow = false;\n    },\n\t\tchooseCoupon:function(e){\n      var allbuydata = this.allbuydata;\n\t\t\tvar bid = e.bid;\n\t\t\tvar couponrid = e.rid;\n      var couponkey = e.key;\n\t\t\tif (couponrid == allbuydata[bid].couponrid) {\n        allbuydata[bid].couponkey = 0;\n        allbuydata[bid].couponrid = 0;\n        allbuydata[bid].coupontype = 1;\n        allbuydata[bid].coupon_money = 0;\n        this.allbuydata = allbuydata;\n        this.couponvisible = false;\n      } else {\n        var couponList = allbuydata[bid].couponList;\n        var coupon_money = couponList[couponkey]['money'];\n        var coupontype = couponList[couponkey]['type'];\n        allbuydata[bid].couponkey = couponkey;\n        allbuydata[bid].couponrid = couponrid;\n        allbuydata[bid].coupontype = coupontype;\n        allbuydata[bid].coupon_money = coupon_money;\n        this.allbuydata = allbuydata;\n        this.couponvisible = false;\n      }\n      this.calculatePrice();\n\t\t},\n    choosestore: function (e) {\n      var bid = e.currentTarget.dataset.bid;\n\t\t\tvar storekey = e.currentTarget.dataset.index;\n\t\t\tvar allbuydata = this.allbuydata;\n      var buydata = allbuydata[bid];\n\t\t\tvar freightkey = buydata.freightkey\n\t\t\tallbuydata[bid].freightList[freightkey].storekey = storekey\n      this.allbuydata = allbuydata;\n    },\n    //提交并支付\n    topay: function () {\n      var that = this;\n      var needaddress = that.needaddress;\n      var addressid = this.address.id;\n      var linkman = this.linkman;\n      var tel = this.tel;\n      var usescore = this.usescore;\n      var frompage = that.opt.frompage ? that.opt.frompage : '';\n      var allbuydata = that.allbuydata;\n      if (needaddress == 0) addressid = 0;\n\n      if (needaddress == 1 && addressid == undefined) {\n        app.error('请选择收货地址');\n        return;\n      }\n      var buydata = [];\n      for (var i in allbuydata) {\n\t\t\t\tvar freightkey = allbuydata[i].freightkey;\n        if (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {\n          app.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');\n          return;\n        }\r\n\t\t\t\tif (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 5) {\r\n\t\t\t\t\tvar storekey = allbuydata[i].freightList[freightkey].storekey;\n\t\t\t\t\tvar storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;\n\t\t\t\t}else{\n\t\t\t\t\tvar storeid = 0;\n\t\t\t\t}\n\n        buydata.push({\n          bid: allbuydata[i].bid,\n          prodata: allbuydata[i].prodatastr,\n          cuxiaoid: allbuydata[i].cuxiaoid,\n          couponrid: allbuydata[i].couponrid,\n          freight_id: allbuydata[i].freightList[freightkey].id,\n          freight_time: allbuydata[i].freight_time,\n          storeid: storeid,\n          message: allbuydata[i].message,\n          field1: allbuydata[i].field1,\n          field2: allbuydata[i].field2,\n          field3: allbuydata[i].field3,\n          field4: allbuydata[i].field4,\n          field5: allbuydata[i].field5\n        });\n      }\n\t\t\tapp.showLoading('提交中');\n      app.post('ApiRestaurantTakeaway/createOrder', {frompage: frompage,buydata: buydata,addressid: addressid,linkman: linkman,tel: tel,usescore: usescore}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 0) {\n          //that.showsuccess(res.data.msg);\n          app.error(res.msg);\n          return;\n        }\n        app.goto('/pages/pay/pay?id=' + res.payorderid, 'redirect');\n      });\n    },\n    showCouponList: function (e) {\n      this.couponvisible = true;\n      this.bid = e.currentTarget.dataset.bid;\n    },\n    handleClickMask: function () {\n      this.couponvisible = false;\n      this.cuxiaovisible = false;\n    },\n    showCuxiaoList: function (e) {\n      this.cuxiaovisible = true;\n      this.bid = e.currentTarget.dataset.bid;\n    },\n    changecx: function (e) {\n      var that = this;\n      var cxid = e.currentTarget.dataset.id;\n      console.log(cxid);\n      that.cxid = cxid;\n      if (cxid == 0) {\n        that.cuxiaoinfo = false;\n        return;\n      }\n      app.post(\"ApiRestaurantTakeaway/getcuxiaoinfo\", {id: cxid}, function (res) {\n        that.cuxiaoinfo = res;\n      });\n    },\n    chooseCuxiao: function () {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = that.bid;\n      var cxid = that.cxid;\n      if (cxid == 0) {\n        allbuydata[bid].cuxiaoid = '';\n        allbuydata[bid].cuxiao_money = 0;\n        allbuydata[bid].cuxiaoname = '不使用促销';\n      } else {\n        var cxtype = that.cuxiaoinfo.info.type;\n\t\t\t\tconsole.log(cxtype);\n        if (cxtype == 1 || cxtype == 6) {\n          //满额立减 满件立减\n          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;\n        } else if (cxtype == 2) {\n          //满额赠送\n          allbuydata[bid].cuxiao_money = 0;\n        } else if (cxtype == 3) {\n          //加价换购\n          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];\n        } else if (cxtype == 4 || cxtype == 5) {\n\t\t\t\t\tvar product_price = parseFloat(allbuydata[bid].product_price);\n\t\t\t\t\tvar leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣\n\t\t\t\t\tvar manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动\n          //满额打折 满件打折\n          allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;\n        }\n        allbuydata[bid].cuxiaoid = cxid;\n        allbuydata[bid].cuxiaotype = cxtype;\n        allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];\n      }\n      this.allbuydata = allbuydata;\n      this.cuxiaovisible = false;\n      this.calculatePrice();\n    },\n\t\topenLocation:function(e){\n\t\t\tvar allbuydata = this.allbuydata\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\n\t\t\tvar frightinfo = allbuydata[bid].freightList[freightkey]\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\n\t\t\tconsole.log(storeinfo)\n\t\t\tvar latitude = parseFloat(storeinfo.latitude);\n\t\t\tvar longitude = parseFloat(storeinfo.longitude);\n\t\t\tvar address = storeinfo.name;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t}\n  }\n}\r\n</script>\r\n<style>\r\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\r\n.address-add .f1{margin-right:20rpx}\r\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\r\n.address-add .f2{ color: #666; }\r\n.address-add .f3{ width: 26rpx; height: 26rpx;}\r\n\r\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\r\n.linkitem .f1{width:160rpx;color:#111111}\r\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\r\n\r\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\r\n\r\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\r\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\r\n\r\n.bcontent{width:100%;padding:0 20rpx}\r\n\r\n.product{width:100%;border-bottom:1px solid #f4f4f4} \r\n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\r\n.product .item:last-child{border:none}\r\n.product .info{padding-left:20rpx;}\r\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .info .f2{color: #999999; font-size:24rpx}\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\r\n.product image{ width:140rpx;height:140rpx}\r\n\r\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\r\n.freight .f1{color:#333;margin-bottom:10rpx}\r\n.freight .f2{color: #111111;text-align:right;flex:1}\r\n.freight .f3{width: 24rpx;height:28rpx;}\r\n.freighttips{color:red;font-size:24rpx;}\r\n\n.freight-ul{width:100%;display:flex;}\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\n\n\r\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\r\n.price .f1{color:#333}\r\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.price .f3{width: 24rpx;height:24rpx;}\r\n\r\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\r\n.scoredk .f1{color:#333333}\r\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\r\n\r\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\r\n.remark .f1{color:#333;width:200rpx}\r\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\r\n\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding:0 20rpx;display:flex;align-items:center;z-index:8}\r\n.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1  text{color: #e94745;font-size: 32rpx;}\r\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\r\n\r\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\r\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\n.storeitem .panel .f1{color:#333}\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\n.storeitem .radio-item:last-child{border:0}\n.storeitem .radio-item .f1{color:#666;flex:1}\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\n.storeitem .radio .radio-img{width:100%;height:100%}\r\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n\r\n.cuxiao-desc{width:100%}\r\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\r\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066757\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}