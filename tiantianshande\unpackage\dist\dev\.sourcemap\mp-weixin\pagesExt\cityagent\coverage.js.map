{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?7209", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?9888", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?2a0c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?6467", "uni-app:///pagesExt/cityagent/coverage.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?27c3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/coverage.vue?3aea"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "provinces", "districts", "expandedProvinceIndex", "coverage_stats", "province_count", "city_count", "district_count", "orders_count", "performance_data", "onLoad", "onPullDownRefresh", "methods", "getColor", "console", "loadCoverageData", "that", "app", "uni", "title", "toggle<PERSON><PERSON><PERSON><PERSON>", "loadCityData", "province_id", "viewCityDetail", "viewDistrictDetail", "applyExpansion", "viewApplications", "contactAdmin", "phoneNumber", "success", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgJjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MAEAC;QACAD;QACAE;QAEA;UACAF;UACAA;UACAA;UACAA;;UAEA;UACAE;YACAC;UACA;QACA;UACAF;QACA;MACA;IACA;IAEA;IACAG;MACA;QACA;QACA;MACA;QACA;QACA;UACA;QACA;QAEA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEAJ;QAAAK;MAAA;QACA;UACAN;QACA;MACA;IACA;IAEA;IACAO;MACAN;IACA;IAEA;IACAO;MACAP;IACA;IAEA;IACAQ;MACAR;IACA;IAEA;IACAS;MACAT;IACA;IAEA;IACAU;MACAT;QACAU;QACAC;UACAf;QACA;QACAgB;UACAb;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnRA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/coverage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/coverage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coverage.vue?vue&type=template&id=1becbfd1&\"\nvar renderjs\nimport script from \"./coverage.vue?vue&type=script&lang=js&\"\nexport * from \"./coverage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coverage.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/coverage.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coverage.vue?vue&type=template&id=1becbfd1&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.provinces.length\n  var g1 = _vm.districts.length\n  var m0 = _vm.getColor(\"color1\") || \"#4CAF50\"\n  var g2 = _vm.provinces.length === 0 && _vm.districts.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coverage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coverage.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<view class=\"header\">\r\n\t\t<text class=\"title\">区域管理</text>\r\n\t\t<text class=\"subtitle\">管理您的代理覆盖区域</text>\r\n\t</view>\r\n\t\r\n\t<view class=\"content\">\r\n\t\t<!-- 区域统计 -->\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">{{coverage_stats.province_count}}</text>\r\n\t\t\t\t<text class=\"stats-label\">覆盖省份</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">{{coverage_stats.city_count}}</text>\r\n\t\t\t\t<text class=\"stats-label\">覆盖城市</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">{{coverage_stats.district_count}}</text>\r\n\t\t\t\t<text class=\"stats-label\">覆盖区县</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">{{coverage_stats.orders_count}}</text>\r\n\t\t\t\t<text class=\"stats-label\">区域订单</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 省份列表 -->\r\n\t\t<view class=\"region-container\" v-if=\"provinces.length > 0\">\r\n\t\t\t<view class=\"region-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-province.png\" class=\"region-icon\"></image>\r\n\t\t\t\t<text class=\"region-title\">覆盖省份</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"province-list\">\r\n\t\t\t\t<view class=\"province-item\" \r\n\t\t\t\t\tv-for=\"(province, index) in provinces\" \r\n\t\t\t\t\t:key=\"province.id\"\r\n\t\t\t\t\t@tap=\"toggleProvince(index)\">\r\n\t\t\t\t\t<view class=\"province-info\">\r\n\t\t\t\t\t\t<text class=\"province-name\">{{province.name}}</text>\r\n\t\t\t\t\t\t<text class=\"province-stats\">{{province.city_count}}个城市 | {{province.orders}}笔订单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image :src=\"province.expanded ? '/static/img/arrow-up.png' : '/static/img/arrow-down.png'\" \r\n\t\t\t\t\t\tclass=\"arrow-icon\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 城市列表 -->\r\n\t\t\t\t<view class=\"city-container\" v-if=\"provinces[expandedProvinceIndex] && provinces[expandedProvinceIndex].expanded\">\r\n\t\t\t\t\t<view class=\"city-list\">\r\n\t\t\t\t\t\t<view class=\"city-item\" \r\n\t\t\t\t\t\t\tv-for=\"city in provinces[expandedProvinceIndex].cities\" \r\n\t\t\t\t\t\t\t:key=\"city.id\"\r\n\t\t\t\t\t\t\t@tap=\"viewCityDetail(city)\">\r\n\t\t\t\t\t\t\t<view class=\"city-info\">\r\n\t\t\t\t\t\t\t\t<text class=\"city-name\">{{city.name}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"city-stats\">{{city.district_count}}个区县 | {{city.orders}}笔订单</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"city-revenue\">\r\n\t\t\t\t\t\t\t\t<text class=\"revenue-amount\">￥{{city.revenue}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"revenue-label\">收益</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 直辖区县列表 -->\r\n\t\t<view class=\"region-container\" v-if=\"districts.length > 0\">\r\n\t\t\t<view class=\"region-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-district.png\" class=\"region-icon\"></image>\r\n\t\t\t\t<text class=\"region-title\">直辖区县</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"district-list\">\r\n\t\t\t\t<view class=\"district-item\" \r\n\t\t\t\t\tv-for=\"district in districts\" \r\n\t\t\t\t\t:key=\"district.id\"\r\n\t\t\t\t\t@tap=\"viewDistrictDetail(district)\">\r\n\t\t\t\t\t<view class=\"district-info\">\r\n\t\t\t\t\t\t<text class=\"district-name\">{{district.name}}</text>\r\n\t\t\t\t\t\t<text class=\"district-stats\">{{district.streets}}个街道 | {{district.orders}}笔订单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"district-revenue\">\r\n\t\t\t\t\t\t<text class=\"revenue-amount\">￥{{district.revenue}}</text>\r\n\t\t\t\t\t\t<text class=\"revenue-label\">收益</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 申请扩展区域 -->\r\n\t\t<!-- <view class=\"action-container\">\r\n\t\t\t<button class=\"action-btn primary\" @tap=\"applyExpansion\">\r\n\t\t\t\t<image src=\"/static/img/icon-expand.png\" class=\"btn-icon\"></image>\r\n\t\t\t\t<text>申请扩展区域</text>\r\n\t\t\t</button>\r\n\t\t\t\r\n\t\t\t<button class=\"action-btn secondary\" @tap=\"viewApplications\">\r\n\t\t\t\t<image src=\"/static/img/icon-application.png\" class=\"btn-icon\"></image>\r\n\t\t\t\t<text>查看申请记录</text>\r\n\t\t\t</button>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- 区域业绩分析 -->\r\n\t\t<view class=\"analysis-container\">\r\n\t\t\t<view class=\"analysis-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-analysis.png\" class=\"analysis-icon\"></image>\r\n\t\t\t\t<text class=\"analysis-title\">区域业绩分析</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"analysis-chart\">\r\n\t\t\t\t<view class=\"chart-item\" v-for=\"item in performance_data\" :key=\"item.name\">\r\n\t\t\t\t\t<view class=\"chart-bar\">\r\n\t\t\t\t\t\t<view class=\"bar-fill\" :style=\"{width: item.percentage + '%', background: getColor('color1') || '#4CAF50'}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"chart-info\">\r\n\t\t\t\t\t\t<text class=\"chart-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t<text class=\"chart-value\">￥{{item.value}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 空状态 -->\r\n\t\t<view class=\"empty-state\" v-if=\"provinces.length === 0 && districts.length === 0\">\r\n\t\t\t<image src=\"/static/img/empty-coverage.png\" class=\"empty-icon\"></image>\r\n\t\t\t<text class=\"empty-title\">暂无覆盖区域</text>\r\n\t\t\t<text class=\"empty-desc\">您还没有分配到任何代理区域</text>\r\n\t\t\t<button class=\"empty-btn\" @tap=\"contactAdmin\">联系管理员</button>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 加载状态 -->\r\n\t<loading v-if=\"loading\"></loading>\r\n\t\r\n\t<!-- 消息提示 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tprovinces: [],\r\n\t\t\tdistricts: [],\r\n\t\t\texpandedProvinceIndex: -1,\r\n\t\t\tcoverage_stats: {\r\n\t\t\t\tprovince_count: 0,\r\n\t\t\t\tcity_count: 0,\r\n\t\t\t\tdistrict_count: 0,\r\n\t\t\t\torders_count: 0\r\n\t\t\t},\r\n\t\t\tperformance_data: []\r\n\t\t};\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tthis.loadCoverageData();\r\n\t},\r\n\t\r\n\tonPullDownRefresh() {\r\n\t\tthis.loadCoverageData();\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t// 安全获取颜色值\r\n\t\tgetColor: function(colorKey) {\r\n\t\t\ttry {\r\n\t\t\t\tif (typeof this.t === 'function') {\r\n\t\t\t\t\treturn this.t(colorKey);\r\n\t\t\t\t}\r\n\t\t\t\treturn null;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('获取颜色失败:', e);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 加载覆盖区域数据\r\n\t\tloadCoverageData() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.get('ApiCityAgent/getCoverageData', {}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\tthat.provinces = res.provinces || [];\r\n\t\t\t\t\tthat.districts = res.districts || [];\r\n\t\t\t\t\tthat.coverage_stats = res.coverage_stats || {};\r\n\t\t\t\t\tthat.performance_data = res.performance_data || [];\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 设置导航标题\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '区域管理'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换省份展开状态\r\n\t\ttoggleProvince(index) {\r\n\t\t\tif (this.expandedProvinceIndex === index) {\r\n\t\t\t\tthis.expandedProvinceIndex = -1;\r\n\t\t\t\tthis.provinces[index].expanded = false;\r\n\t\t\t} else {\r\n\t\t\t\t// 关闭之前展开的省份\r\n\t\t\t\tif (this.expandedProvinceIndex >= 0) {\r\n\t\t\t\t\tthis.provinces[this.expandedProvinceIndex].expanded = false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.expandedProvinceIndex = index;\r\n\t\t\t\tthis.provinces[index].expanded = true;\r\n\t\t\t\t\r\n\t\t\t\t// 如果还没有加载城市数据，则加载\r\n\t\t\t\tif (!this.provinces[index].cities) {\r\n\t\t\t\t\tthis.loadCityData(this.provinces[index].id, index);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 加载城市数据\r\n\t\tloadCityData(provinceId, index) {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tapp.get('ApiCityAgent/getCityData', {province_id: provinceId}, function(res) {\r\n\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\tthat.$set(that.provinces[index], 'cities', res.cities);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查看城市详情\r\n\t\tviewCityDetail(city) {\r\n\t\t\tapp.goto('/pagesExt/cityagent/city-detail?city_id=' + city.id);\r\n\t\t},\r\n\t\t\r\n\t\t// 查看区县详情\r\n\t\tviewDistrictDetail(district) {\r\n\t\t\tapp.goto('/pagesExt/cityagent/district-detail?district_id=' + district.id);\r\n\t\t},\r\n\t\t\r\n\t\t// 申请扩展区域\r\n\t\tapplyExpansion() {\r\n\t\t\tapp.goto('/pagesExt/cityagent/apply-expansion');\r\n\t\t},\r\n\t\t\r\n\t\t// 查看申请记录\r\n\t\tviewApplications() {\r\n\t\t\tapp.goto('/pagesExt/cityagent/expansion-applications');\r\n\t\t},\r\n\t\t\r\n\t\t// 联系管理员\r\n\t\tcontactAdmin() {\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: '************',\r\n\t\t\t\tsuccess: function () {\r\n\t\t\t\t\tconsole.log('拨号成功');\r\n\t\t\t\t},\r\n\t\t\t\tfail: function () {\r\n\t\t\t\t\tapp.error('拨号失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tpadding: 40rpx 30rpx;\r\n\tcolor: white;\r\n\ttext-align: center;\r\n}\r\n\r\n.title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.subtitle {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.9;\r\n}\r\n\r\n.content {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-container {\r\n\tdisplay: flex;\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.stats-number {\r\n\tdisplay: block;\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 区域容器样式 */\r\n.region-container {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.region-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.region-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.region-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 省份列表样式 */\r\n.province-list {\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.province-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 25rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.province-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.province-info {\r\n\tflex: 1;\r\n}\r\n\r\n.province-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.province-stats {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 24rpx;\r\n\theight: 24rpx;\r\n}\r\n\r\n/* 城市容器样式 */\r\n.city-container {\r\n\tbackground: #f8f8f8;\r\n\tmargin: 0 -30rpx;\r\n\tpadding: 20rpx 30rpx;\r\n}\r\n\r\n.city-list {\r\n\tbackground: white;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.city-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.city-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.city-info {\r\n\tflex: 1;\r\n}\r\n\r\n.city-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.city-stats {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.city-revenue {\r\n\ttext-align: right;\r\n}\r\n\r\n.revenue-amount {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n\tdisplay: block;\r\n\tmargin-bottom: 4rpx;\r\n}\r\n\r\n.revenue-label {\r\n\tfont-size: 20rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 区县列表样式 */\r\n.district-list {\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.district-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 25rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.district-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.district-info {\r\n\tflex: 1;\r\n}\r\n\r\n.district-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.district-stats {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.district-revenue {\r\n\ttext-align: right;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-container {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tborder: none;\r\n}\r\n\r\n.action-btn.primary {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n}\r\n\r\n.action-btn.secondary {\r\n\tbackground: white;\r\n\tcolor: #666;\r\n\tborder: 2rpx solid #e0e0e0;\r\n}\r\n\r\n.btn-icon {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n/* 业绩分析样式 */\r\n.analysis-container {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.analysis-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.analysis-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.analysis-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.analysis-chart {\r\n\t\r\n}\r\n\r\n.chart-item {\r\n\tmargin-bottom: 25rpx;\r\n}\r\n\r\n.chart-bar {\r\n\theight: 8rpx;\r\n\tbackground: #f0f0f0;\r\n\tborder-radius: 4rpx;\r\n\tmargin-bottom: 10rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.bar-fill {\r\n\theight: 100%;\r\n\tborder-radius: 4rpx;\r\n\ttransition: width 0.3s ease;\r\n}\r\n\r\n.chart-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.chart-name {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.chart-value {\r\n\tfont-size: 26rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n\ttext-align: center;\r\n\tpadding: 80rpx 40rpx;\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.empty-icon {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.empty-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.empty-desc {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.empty-btn {\r\n\twidth: 300rpx;\r\n\theight: 60rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\tfont-size: 28rpx;\r\n\tborder-radius: 30rpx;\r\n\tborder: none;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coverage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coverage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102985\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}