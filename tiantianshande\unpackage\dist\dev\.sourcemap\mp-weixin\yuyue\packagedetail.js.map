{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?4059", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?697f", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?4e1d", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?0fb1", "uni-app:///yuyue/packagedetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?fccb", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagedetail.vue?8eac"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "packageId", "packageDetail", "loading", "onLoad", "app", "uni", "title", "methods", "getDetail", "that", "id", "console", "packageData", "businessData", "item", "business", "isfavorite", "<PERSON><PERSON><PERSON><PERSON>", "gotoBusiness"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuEtxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACAC;QACAA;MACA;MACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAL;QAAAM;MAAA;QACAC;QACAF;QACA;QACA;UACA;YACA;YACA;YACA;YACAE;YACAA;;YAEA;YACA;cACAA;cACAC;YACA;YACA;cACAD;cACAE;YACA;YACA;YACA;cACAD;gBACA;kBACAD;kBACAG;gBACA;cACA;YACA;;YAEA;YACA;cAAA;cACAH;cACA;gBACAC;cACA;gBACAD;gBACAC;cACA;YACA;cACAD;cACAC;YACA;;YAEA;YACA,kDACAA;cACAG;cACAT;cAAA;cACAU;YAAA,EACA;;YACAL;YACAF;;YAEA;YACA;cACAE;cACAP;cACAK;YACA;UAEA;YACAE;YACAP;YACAK;UACA;QACA;UACAE;UACAP;YACAA;UACA;QACA;MACA;QACAK;QACAL;UACAA;QACA;MACA;IACA;IACAa;MACAb;IACA;IACAc;MACA;QACAd;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packagedetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packagedetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packagedetail.vue?vue&type=template&id=07278117&\"\nvar renderjs\nimport script from \"./packagedetail.vue?vue&type=script&lang=js&\"\nexport * from \"./packagedetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packagedetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packagedetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagedetail.vue?vue&type=template&id=07278117&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.loading && _vm.packageDetail.id ? _vm.t(\"color1\") : null\n  var g0 =\n    !_vm.loading && _vm.packageDetail.id\n      ? _vm.packageDetail.items && _vm.packageDetail.items.length > 0\n      : null\n  var m1 = !_vm.loading && _vm.packageDetail.id ? _vm.t(\"color1\") : null\n  var m2 = !_vm.loading && _vm.packageDetail.id ? _vm.t(\"color1\") : null\n  var m3 = !_vm.loading && _vm.packageDetail.id ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagedetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagedetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\" v-if=\"!loading && packageDetail.id\">\r\n\t\t<!-- 套餐主图 -->\r\n\t\t<image class=\"package-banner\" :src=\"packageDetail.pic\" mode=\"widthFix\"></image>\r\n\r\n\t\t<!-- 基本信息 -->\r\n\t\t<view class=\"section info-section\">\r\n\t\t\t<view class=\"package-name\">{{ packageDetail.name }}</view>\r\n\t\t\t<view class=\"package-tags\">\r\n\t\t\t\t<text class=\"tag\" v-if=\"packageDetail.valid_days > 0\">有效期 {{ packageDetail.valid_days }} 天</text>\r\n\t\t\t\t<text class=\"tag\" v-else>永久有效</text>\r\n\t\t\t\t<!-- 可以添加其他标签 -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"package-price\" :style=\"{color: t('color1')}\">￥<text class=\"price-value\">{{ packageDetail.sell_price }}</text></view>\r\n\t\t\t<!-- 可以在这里添加原价、已售数量等信息 -->\r\n\t\t</view>\r\n\r\n\t\t<!-- 包含项目 -->\r\n\t\t<view class=\"section items-section\" v-if=\"packageDetail.items && packageDetail.items.length > 0\">\r\n\t\t\t<view class=\"section-title\">包含服务项目</view>\r\n\t\t\t<view class=\"service-list\">\r\n\t\t\t\t<view class=\"service-item\" v-for=\"(item, index) in packageDetail.items\" :key=\"index\">\r\n\t\t\t\t\t<image class=\"service-pic\" :src=\"item.product_pic || '/static/img/goods-default.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"service-info\">\r\n\t\t\t\t\t\t<view class=\"service-name\">{{ item.product_name }}</view>\r\n\t\t\t\t\t\t<view class=\"service-times\">x {{ item.num }} 次</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 可以添加服务价格或跳转到服务详情 -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商家信息 -->\r\n\t\t<view class=\"section business-section\" v-if=\"packageDetail.business\">\r\n\t\t\t<view class=\"section-title\">适用商家</view>\r\n\t\t\t<view class=\"business-info\" @tap=\"gotoBusiness(packageDetail.bid)\">\r\n\t\t\t\t<image class=\"business-logo\" :src=\"packageDetail.business.logo\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"business-details\">\r\n\t\t\t\t\t<view class=\"business-name\">{{ packageDetail.business.name }}</view>\r\n\t\t\t\t\t<view class=\"business-address\">{{ packageDetail.business.address }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"arrow\">&gt;</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 套餐详情 -->\r\n\t\t<view class=\"section detail-section\">\r\n\t\t\t<view class=\"section-title\">套餐详情</view>\r\n\t\t\t<rich-text class=\"rich-text-content\" :nodes=\"packageDetail.content || '暂无详情'\"></rich-text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部购买栏 -->\r\n\t\t<view class=\"bottom-bar\">\r\n\t\t\t<view class=\"bottom-price\">\r\n\t\t\t\t<text>合计：</text>\r\n\t\t\t\t<text class=\"price-symbol\" :style=\"{color: t('color1')}\">￥</text>\r\n\t\t\t\t<text class=\"price-value-bottom\" :style=\"{color: t('color1')}\">{{ packageDetail.sell_price }}</text>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"buy-button\" :style=\"{background: t('color1')}\" @tap=\"gotoBuy\">立即购买</button>\r\n\t\t</view>\r\n\r\n\t</view>\r\n\t<view class=\"loading-container\" v-else-if=\"loading\">\r\n\t\t<text>加载中...</text>\r\n\t</view>\r\n\t<view class=\"empty-container\" v-else>\r\n\t\t<text>未找到套餐信息</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tpackageId: null,\r\n\t\t\tpackageDetail: {},\r\n\t\t\tloading: true\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.id) {\r\n\t\t\tthis.packageId = options.id;\r\n\t\t\tthis.getDetail();\r\n\t\t} else {\r\n\t\t\tapp.error('缺少套餐ID', function() {\r\n\t\t\t\tapp.goback();\r\n\t\t\t});\r\n\t\t\tthis.loading = false;\r\n\t\t}\r\n\t\tuni.setNavigationBarTitle({\r\n\t\t\ttitle: '套餐详情'\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\tgetDetail() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiYuyuePackage/getDetail', { id: that.packageId }, function(res) {\r\n\t\t\t\tconsole.log('ApiYuyuePackage/getDetail 返回:', JSON.stringify(res)); // 完整日志\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t// 调整判断条件，直接检查 res.status 和 res.package\r\n\t\t\t\tif (res.status == 1 && res.package) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 直接从 res 中提取 package 和 business\r\n\t\t\t\t\t\tlet packageData = JSON.parse(JSON.stringify(res.package)); // 深拷贝\r\n\t\t\t\t\t\tlet businessData = res.business ? JSON.parse(JSON.stringify(res.business)) : null;\r\n\t\t\t\t\t\tconsole.log('提取的 packageData:', JSON.stringify(packageData));\r\n\t\t\t\t\t\tconsole.log('提取的 businessData:', JSON.stringify(businessData));\r\n\r\n\t\t\t\t\t\t// 清理图片URL (保持不变)\r\n\t\t\t\t\t\tif (packageData.pic && packageData.pic.startsWith('https://localhost')) {\r\n\t\t\t\t\t\t\tconsole.log('清理套餐图片前缀');\r\n\t\t\t\t\t\t\tpackageData.pic = packageData.pic.substring('https://localhost'.length);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (businessData && businessData.logo && businessData.logo.startsWith('https://localhost')) {\r\n\t\t\t\t\t\t\tconsole.log('清理商家Logo前缀');\r\n\t\t\t\t\t\t\tbusinessData.logo = businessData.logo.substring('https://localhost'.length);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 清理 items 中的图片URL\r\n\t\t\t\t\t\tif(packageData.items && Array.isArray(packageData.items)) {\r\n\t\t\t\t\t\t\tpackageData.items.forEach(item => {\r\n\t\t\t\t\t\t\t\tif(item.product_pic && item.product_pic.startsWith('https://localhost')){\r\n\t\t\t\t\t\t\t\t\tconsole.log('清理服务项图片前缀:', item.product_pic);\r\n\t\t\t\t\t\t\t\t\titem.product_pic = item.product_pic.substring('https://localhost'.length);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 处理富文本中的图片样式 (保持不变)\r\n\t\t\t\t\t\tif (packageData.content && packageData.content !== '[]') { // 检查是否是无效的 '[]'\r\n\t\t\t\t\t\t\tconsole.log('格式化富文本内容');\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tpackageData.content = app.formatRichText(packageData.content);\r\n\t\t\t\t\t\t\t} catch (formatError) {\r\n\t\t\t\t\t\t\t\tconsole.error('formatRichText 出错:', formatError);\r\n\t\t\t\t\t\t\t\tpackageData.content = '内容加载失败'; // 出错时给默认提示\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (packageData.content === '[]'){\r\n\t\t\t\t\t\t\tconsole.log('富文本内容为 [], 置空处理');\r\n\t\t\t\t\t\t\tpackageData.content = ''; // 置为空字符串\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 构建最终对象\r\n\t\t\t\t\t\tconst finalDetail = {\r\n\t\t\t\t\t\t\t...packageData,\r\n\t\t\t\t\t\t\tbusiness: businessData,\r\n\t\t\t\t\t\t\ttitle: res.title, // 从 res 直接获取\r\n\t\t\t\t\t\t\tisfavorite: res.isfavorite // 从 res 直接获取\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tconsole.log('最终赋值给 packageDetail 的对象:', JSON.stringify(finalDetail));\r\n\t\t\t\t\t\tthat.packageDetail = finalDetail;\r\n\r\n\t\t\t\t\t\t// 再次检查 packageDetail.id 是否存在\r\n\t\t\t\t\t\tif (!that.packageDetail || !that.packageDetail.id) {\r\n\t\t\t\t\t\t\tconsole.error('赋值后 packageDetail 或其 id 无效:', that.packageDetail);\r\n\t\t\t\t\t\t\tapp.error('获取套餐详情失败(数据处理异常)');\r\n\t\t\t\t\t\t\tthat.packageDetail = {}; // 确保渲染失败状态\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tconsole.error('处理套餐详情数据时出错:', e);\r\n\t\t\t\t\t\tapp.error('加载套餐信息异常');\r\n\t\t\t\t\t\tthat.packageDetail = {}; // 清空数据，确保显示失败状态\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('获取套餐详情失败，status 或 package 结构不符:', res);\r\n\t\t\t\t\tapp.error(res.msg || '获取套餐详情失败', function() {\r\n\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}, function() {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tapp.error('请求失败', function() {\r\n\t\t\t\t\tapp.goback();\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\tgotoBuy() {\r\n\t\t\tapp.goto('/yuyue/packagebuy?package_id=' + this.packageId);\r\n\t\t},\r\n\t\tgotoBusiness(bid){\r\n\t\t\tif(bid){\r\n\t\t\t\tapp.goto('/pages/business/index?id='+bid);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding-bottom: 120rpx; /* 为底部购买栏留出空间 */\r\n\tbackground-color: #f5f5f5;\r\n}\r\n\r\n.package-banner {\r\n\twidth: 100%;\r\n\tdisplay: block; /* 消除图片底部空隙 */\r\n\tvertical-align: bottom;\r\n}\r\n\r\n.section {\r\n\tbackground-color: #fff;\r\n\tmargin: 20rpx;\r\n\tpadding: 25rpx;\r\n\tborder-radius: 16rpx;\r\n}\r\n\r\n.info-section .package-name {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.info-section .package-tags {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.info-section .tag {\r\n\tbackground-color: #fff5f5;\r\n\tcolor: #E64340;\r\n\tfont-size: 22rpx;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 4rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.info-section .package-price {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.info-section .price-value {\r\n\tfont-size: 44rpx;\r\n\tmargin-left: 4rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 25rpx;\r\n\tpadding-left: 16rpx;\r\n\tborder-left: 6rpx solid; /* 使用主题色 */\r\n}\r\n\r\n/* 包含项目 */\r\n.items-section {\r\n\t/* section通用样式已包含padding */\r\n}\r\n\r\n.service-list {\r\n\t/* 列表容器 */\r\n}\r\n\r\n.service-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n.service-item:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.service-pic {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tborder-radius: 8rpx;\r\n\tmargin-right: 20rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.service-info {\r\n\tflex: 1;\r\n\toverflow: hidden;\r\n}\r\n\r\n.service-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 8rpx;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n.service-times {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 商家信息 */\r\n.business-info {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 15rpx 0;\r\n}\r\n\r\n.business-logo {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border-radius: 50%;\r\n    margin-right: 20rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.business-details {\r\n    flex: 1;\r\n    overflow: hidden;\r\n}\r\n\r\n.business-name {\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    font-weight: bold;\r\n    margin-bottom: 5rpx;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n.business-address {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n.arrow{\r\n\tcolor: #bbb;\r\n\tfont-size: 28rpx;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n/* 套餐详情 */\r\n.detail-section {\r\n\t/* section通用样式已包含padding */\r\n}\r\n\r\n.rich-text-content {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tline-height: 1.6;\r\n}\r\n.rich-text-content image, .rich-text-content img {\r\n\tmax-width: 100% !important;\r\n\theight: auto !important;\r\n\tdisplay: block;\r\n\tmargin: 10rpx 0;\r\n}\r\n\r\n/* 底部购买栏 */\r\n.bottom-bar {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\twidth: 100%;\r\n\theight: 100rpx; /* 根据内容调整 */\r\n\tbackground-color: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 0 20rpx 0 30rpx;\r\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tbox-sizing: border-box; /* 包含padding */\r\n\tpadding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */\r\n\theight: calc(100rpx + env(safe-area-inset-bottom));\r\n}\r\n\r\n.bottom-price {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.bottom-price .price-symbol {\r\n\tfont-size: 28rpx;\r\n\tmargin-left: 8rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.bottom-price .price-value-bottom {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.buy-button {\r\n\theight: 72rpx;\r\n\tline-height: 72rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #fff;\r\n\tpadding: 0 40rpx;\r\n\tborder-radius: 36rpx;\r\n\ttext-align: center;\r\n\tborder: none;\r\n\toutline: none;\r\n\tmargin: 0;\r\n}\r\nbutton::after {\r\n\tborder: none;\r\n}\r\n\r\n\r\n/* 加载和空状态 */\r\n.loading-container, .empty-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tmin-height: 60vh;\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagedetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagedetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041063\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}