{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?8cae", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?9196", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?f937", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?cd18", "uni-app:///pagesExt/coupons/mycoupon.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?165e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/mycoupon.vue?ee0b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "givecheckbox", "checkednum", "test", "shareTitle", "sharePic", "shareDesc", "shareLink", "onLoad", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "console", "title", "pic", "desc", "link", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "bid", "uni", "changetab", "scrollTop", "duration", "changeradio", "ids", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyDjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;IACAA;IACA;MAAAC;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAJ;MAAAC;MAAAC;MAAAC;IAAA;IACA;IACAJ;IACAA;IACA;MACAC;MACAK;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAA3B;QAAAE;QAAA0B;MAAA;QACAF;QACAG;UACAZ;QACA;QACA;QAEA;UACAS;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACAD;QACAE;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAhC;QACAyB;MACA;QACAzB;QACAyB;MACA;MACAA;MACAA;MACAV;MACA;MACA;QACA;UACAkB;QACA;MACA;MACAA;MACAR;MACAA;MACAA;MACA;QACAA;MACA;QACAA;MACA;MACAV;MACAU;QAAAT;QAAAC;QAAAC;QAAAC;MAAA;IACA;IAEAe;MACAR;MACA;IACA;IACAS;MACA;MACAV;MACAG;QACAQ;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YAEAX;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClOA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/coupons/mycoupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/coupons/mycoupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mycoupon.vue?vue&type=template&id=b91f21f0&\"\nvar renderjs\nimport script from \"./mycoupon.vue?vue&type=script&lang=js&\"\nexport * from \"./mycoupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mycoupon.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/coupons/mycoupon.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=template&id=b91f21f0&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.type == 0 ? _vm.t(\"color1\") : null\n        var m1 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m2 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m3 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m4 = _vm.t(\"color1\")\n        var m5 =\n          item.isgive == 1 || item.isgive == 2 ? _vm.t(\"color2rgb\") : null\n        var m6 = item.isgive == 1 || item.isgive == 2 ? _vm.t(\"color2\") : null\n        var m7 = _vm.t(\"color1\")\n        var m8 = _vm.t(\"color1rgb\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 = _vm.isload && _vm.checkednum > 0 ? _vm.getplatform() : null\n  var m10 =\n    _vm.isload && _vm.checkednum > 0 && m9 == \"app\" ? _vm.t(\"color2\") : null\n  var m11 =\n    _vm.isload && _vm.checkednum > 0 && m9 == \"app\" ? _vm.t(\"color2rgb\") : null\n  var m12 =\n    _vm.isload && _vm.checkednum > 0 && !(m9 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m13 =\n    _vm.isload && _vm.checkednum > 0 && !(m9 == \"app\") && m12 == \"mp\"\n      ? _vm.t(\"color2\")\n      : null\n  var m14 =\n    _vm.isload && _vm.checkednum > 0 && !(m9 == \"app\") && m12 == \"mp\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m15 =\n    _vm.isload && _vm.checkednum > 0 && !(m9 == \"app\") && !(m12 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m9 == \"app\") &&\n    !(m12 == \"mp\") &&\n    m15 == \"h5\"\n      ? _vm.t(\"color2\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m9 == \"app\") &&\n    !(m12 == \"mp\") &&\n    m15 == \"h5\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m18 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m9 == \"app\") &&\n    !(m12 == \"mp\") &&\n    !(m15 == \"h5\")\n      ? _vm.t(\"color2\")\n      : null\n  var m19 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m9 == \"app\") &&\n    !(m12 == \"mp\") &&\n    !(m15 == \"h5\")\n      ? _vm.t(\"color2rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['未使用','已使用','已过期']\" :itemst=\"['0','1','2']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\n\n\t\t<view class=\"coupon-list\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"coupon\" @tap.stop=\"goto\" :data-url=\"'coupondetail?rid=' + item.id\" :style=\" (item.isgive == 1 || item.isgive == 2)?'padding-left:40rpx':''\">\n\t\t\t\t<view class=\"pt_left\">\n\t\t\t\t\t<view class=\"pt_left-content\">\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==0\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==1\">商品券</view>\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==2\"><text class=\"t1\">{{item.score}}</text><text class=\"t2\">积分</text></view>\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==3\">礼品卡</view>\n\t\t\t\t\t\t<view class=\"f2\" :style=\"{color:t('color1')}\" >\n\t\t\t\t\t\t\t<text >兑换码：{{item.code}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"pt_right\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\n\t\t\t\t\t\t<!-- <view class=\"t4\" style=\"color:red;\" >x {{item.count}} 次</view> -->\n\t\t\t\t\t\t<text class=\"t2\" v-if=\" (item.isgive == 1 || item.isgive == 2)\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">可赠送</text>\n\t\t\t\t\t\t<view class=\"t3\" style=\"margin-top:10rpx\">有效期至 {{item.endtime}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f1_top\">\n\t\t\t\t\t\t<block>\n\t\t\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"goto\" :data-url=\"'coupondetail?rid=' + item.id\">详情</button>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"f1_right\">\r\n\t\t\t\t\t\t<block>\r\n\t\t\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"goto2\" :data-url=\"'coupondetail?rid=' + item.id\">去兑换</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view> -->\n\t\t\t\t\t<image class=\"sygq\" v-if=\"st==1\" :src=\"pre_url+'/static/img/ysy.png'\"></image>\n\t\t\t\t\t<image class=\"sygq\" v-if=\"st==2\" :src=\"pre_url+'/static/img/ygq.png'\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t<view class=\"giveopbox\" v-if=\"checkednum > 0\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot3'\">\n\t\t\t<view class=\"btn-give\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\n\t\t\t<view class=\"btn-give\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\n\t\t\t<view class=\"btn-give\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\n\t\t\t<button class=\"btn-give\" open-type=\"share\" v-else :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" >转赠好友({{checkednum}}张)</button>\n\t\t</view>\n\t\t<view style=\"display:none\">{{test}}</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\n      st: 0,\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n\t\t\tnodata:false,\n\t\t\tgivecheckbox:false,\n\t\t\tcheckednum:0,\n\t\t\ttest:'',\n\t\t\tshareTitle:'',\n\t\t\tsharePic:'',\n\t\t\tshareDesc:'',\n\t\t\tshareLink:'',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || 0;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonShareAppMessage:function(){\n\t\tconsole.log('this.shareLink');\n\t\tconsole.log(this.shareLink);\n\t\treturn this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\n\t},\n\tonShareTimeline:function(){\n\t\tvar sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\tconsole.log(sharewxdata)\n\t\tconsole.log(query)\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n      var bid = that.opt && (that.opt.bid || that.opt.bid === '0') ? that.opt.bid : '';\n\t\t\tthat.loading = true;\n\t\t\tthat.nomore = false;\n\t\t\tthat.nodata = false;\n        app.post('ApiLipin2/mycoupon', {st: st,pagenum: pagenum,bid: bid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '我的' + that.t('礼品卡')\n\t\t\t\t});\n        var data = res.data;\n\t\t\n        if (pagenum == 1) {\n\t\t\t\t\tthat.checkednum = 0;\n\t\t\t\t\tthat.pics = res.pics;\n\t\t\t\t\tthat.clist = res.clist;\n\t\t\t\t\tthat.givecheckbox = res.givecheckbox;\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    changeradio: function (e) {\n      var that = this;\n      var index = e.currentTarget.dataset.index;\n\t\t\tvar datalist = that.datalist;\n      var checked = datalist[index].checked;\n\t\t\tif(checked){\n\t\t\t\tdatalist[index].checked = false;\n\t\t\t\tthat.checkednum--;\n\t\t\t}else{\n\t\t\t\tdatalist[index].checked = true;\n\t\t\t\tthat.checkednum++;\n\t\t\t}\n\t\t\tthat.datalist = datalist;\n\t\t\tthat.test = Math.random();\n\t\t\tconsole.log(that.checkednum);\n\t\t\tvar ids = [];\n\t\t\tfor(var i in datalist){\n\t\t\t\tif(datalist[i].checked){\n\t\t\t\t\tids.push(datalist[i].id);\n\t\t\t\t}\n\t\t\t}\n\t\t\tids = ids.join(',');\n\t\t\tthat.shareTitle = '送你'+that.checkednum+'张'+that.t('礼品卡');\n\t\t\tthat.shareDesc = '点击前往查看领取';\n\t\t\tthat.sharePic = app.globalData.initdata.logo;\n\t\t\tif(app.globalData.platform == 'h5' || app.globalData.platform == 'mp' || app.globalData.platform == 'app'){\n\t\t\t\tthat.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/coupons/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;\n\t\t\t}else{\n\t\t\t\tthat.shareLink = '/pagesExt/coupons/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;\n\t\t\t}\n\t\t\tconsole.log(that.shareLink);\n\t\t\tthat.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});\n    },\r\n\t\n\t\tsharemp:function(){\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshareapp:function(){\n\t\t\tvar that = this;\n\t\t\tthat.sharetypevisible = false;\n\t\t\tuni.showActionSheet({\n\t\t    itemList: ['发送给微信好友', '分享到微信朋友圈'],\n\t\t    success: function (res){\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\tsharedata.title = that.shareTitle;\n\t\t\t\t\t\tsharedata.summary = that.shareDesc;\n\t\t\t\t\t\tsharedata.href = that.shareLink;\n\t\t\t\t\t\tsharedata.imageUrl = that.sharePic;\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t}\n\t\t    }\n\t\t  });\n\t\t},\n  }\n};\n</script>\n<style>\n\n.coupon-list{width:100%;padding:20rpx}\n.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden;align-items:center;position:relative;background: #fff;}\n.coupon .pt_left{background: #fff;min-height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\n.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}\n.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}\n.coupon .pt_left .t0{padding-right:0;}\n.coupon .pt_left .t1{font-size:60rpx;}\n.coupon .pt_left .t2{padding-left:10rpx;}\n.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}\n.coupon .pt_right{background: #fff;width:70%;display:flex;min-height:200rpx;text-align: left;padding:20rpx 20rpx;position:relative}\n.coupon .pt_right .f1{\n\tflex-grow: 1;flex-shrink: 1;\n\tposition: relative;\n}\r\n.coupon .pt_right .f1_right{\r\n\tflex-grow: 1;flex-shrink: 1;\r\n\tposition: relative;\r\n\ttop:5px;\r\n\tleft:20px;\r\n}\r\n.coupon .pt_right .f1_top{\n\tflex-grow: 1;flex-shrink: 1;\r\n\tposition: relative;\r\n\ttop:-20rpx;\r\n\tleft:35rpx;\n}\n.coupon .pt_right .f1 .f1_action {\n\tposition: absolute;\n\tright: 20rpx;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\twidth: 100rpx;\n\theight: 40rpx;\n\tborder-radius: 25rpx;\n\tbackground: linear-gradient(130deg, #F21818 0%, #FC9191 100%);\n\tcolor: #ffffff;\n\tfont-size: 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}\n.coupon .pt_right .f1 .t2{height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx; margin-right: 16rpx;}\n.coupon .pt_right .f1 .t2:last-child {margin-right: 0;}\n.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\n.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\n.coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-28rpx;border-radius:28rpx;width:160rpx;height:56rpx;line-height:56rpx;color:#fff}\n.coupon .pt_right .sygq{position:absolute;right:30rpx;top:50%;margin-top:-50rpx;width:100rpx;height:100rpx;}\n\n.coupon .pt_left.bg3{background:#ffffff;color:#b9b9b9!important}\n.coupon .pt_right.bg3 .t1{color:#b9b9b9!important}\n.coupon .pt_right.bg3 .t3{color:#b9b9b9!important}\n.coupon .pt_right.bg3 .t4{color:#999999!important}\n\n.coupon .radiobox{position:absolute;left:0;padding:20rpx}\n.coupon .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;}\n.coupon .radio .radio-img{width:100%;height:100%}\n.giveopbox{position:fixed;bottom:0;left:0;width:100%;}\n.btn-give{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098363\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}