{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?6eaa", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?69f5", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?6ae6", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?824e", "uni-app:///yuyue/selectworker.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?1de8", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/selectworker.vue?f18d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "yydate", "orderInfo", "product", "pic", "name", "sell_price", "order_no", "yy_time", "workerList", "selectedWorkerId", "onLoad", "console", "app", "methods", "getOrderInfo", "id", "that", "getWorkerList", "workerData", "selectWorker", "submitAssign", "worker_id", "success", "message"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2DrxB;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;IACA;MACA;MACA;MACA;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACAF;MAEAD;MAEAC;QACAG;MACA;QACAH;QACAD;;QAEA;QACA;UACAK;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;YACAb;YACAC;YACAC;UACA;QACA;;QAEA;QACA;UACAW;QACA;;QAEA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;;QAEA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAL;;MAEA;MACA;MAEAD;QACAI;QACAf;MACA;MAEAY;QACAG;QACAf;MACA;QACAY;QAEAD;QAEA;;QAEA;QACA;UACAO;QACA;UACAA;QACA;UACAA;QACA;QAEAF;;QAEA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAG;MACAR;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;QACAR;QACA;MACA;MAEA;QACAG;QACAM;MACA;MAEAV;MAEAC;MACAA;QACAA;QACAD;;QAEA;QACA;QACA;QAEA;UACAW;UACAC;QACA;UACAD;UACAC;QACA;QAEA;UACAX;YACAA;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzOA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/selectworker.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/selectworker.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./selectworker.vue?vue&type=template&id=6e199bac&\"\nvar renderjs\nimport script from \"./selectworker.vue?vue&type=script&lang=js&\"\nexport * from \"./selectworker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./selectworker.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/selectworker.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectworker.vue?vue&type=template&id=6e199bac&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.orderInfo && _vm.orderInfo.product ? _vm.t(\"color1\") : null\n  var g0 = _vm.workerList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.workerList, function (worker, index) {\n          var $orig = _vm.__get_orig(worker)\n          var m1 =\n            _vm.selectedWorkerId === worker.id ? _vm.t(\"color1rgb\") : null\n          var m2 = _vm.selectedWorkerId === worker.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var m3 = _vm.selectedWorkerId ? _vm.t(\"color1\") : null\n  var m4 = _vm.selectedWorkerId ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectworker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectworker.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"order-info\" v-if=\"orderInfo && orderInfo.product\">\n      <view class=\"product-info\">\n        <image :src=\"orderInfo.product.pic\" mode=\"aspectFill\"></image>\n        <view class=\"info\">\n          <text class=\"name\">{{orderInfo.product.name}}</text>\n          <text class=\"price\" :style=\"'color:'+t('color1')\">￥{{orderInfo.product.sell_price}}</text>\n        </view>\n      </view>\n      <view class=\"order-detail\">\n        <text>订单号：{{orderInfo.order_no || ''}}</text>\n        <text>预约时间：{{orderInfo.yy_time || ''}}</text>\n      </view>\n    </view>\n\n    <view class=\"worker-list-section\">\n      <view class=\"section-title\">选择服务人员</view>\n      \n      <view class=\"worker-list\" v-if=\"workerList.length > 0\">\n        <view class=\"worker-item\" \n          v-for=\"(worker, index) in workerList\" \n          :key=\"index\"\n          :class=\"{'active': selectedWorkerId === worker.id}\"\n          :style=\"selectedWorkerId === worker.id ? 'background: rgba('+t('color1rgb')+',0.1)' : ''\"\n          @tap=\"selectWorker(worker)\">\n          <image :src=\"worker.avatar || '/static/img/default-avatar.png'\" mode=\"aspectFill\" class=\"avatar\"></image>\n          <view class=\"worker-info\">\n            <text class=\"name\">{{worker.name}}</text>\n            <view class=\"stats\">\n              <text>评分：{{worker.rating || '5.0'}}分</text>\n              <text>已完成：{{worker.order_count || '0'}}单</text>\n            </view>\n          </view>\n          <view class=\"select-icon\" \n               v-if=\"selectedWorkerId === worker.id\"\n               :style=\"'background:'+t('color1')\">\n            <text class=\"iconfont iconcheck\"></text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"no-worker\" v-else>\n        <text>当前预约时间暂无可用服务人员</text>\n        <text>请返回选择其他时间段</text>\n      </view>\n    </view>\n\n    <view class=\"footer\">\n      <button class=\"submit-btn\" \n              :disabled=\"!selectedWorkerId\" \n              :class=\"{'shadow-box': selectedWorkerId}\"\n              :style=\"selectedWorkerId ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)' : 'background:#ccc'\" \n              @tap=\"submitAssign\">确认选择</button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      orderId: '',\n      yydate: '',\n      orderInfo: {\n        product: {\n          pic: '',\n          name: '',\n          sell_price: 0\n        },\n        order_no: '',\n        yy_time: ''\n      },\n      workerList: [],\n      selectedWorkerId: ''\n    }\n  },\n  \n  onLoad(options) {\n    var app = getApp();\n    console.log('选择服务人员页面加载，参数:', options);\n    if(options && options.id) {\n      this.orderId = options.id;\n      this.yydate = options.yydate || '';\n      this.getOrderInfo();\n    } else {\n      app.error('参数错误');\n    }\n  },\n  \n  methods: {\n    // 获取订单信息\n    getOrderInfo() {\n      var app = getApp();\n      var that = this;\n      app.showLoading('加载订单信息');\n      \n      console.log('获取订单详情，ID:', that.orderId);\n      \n      app.get('ApiYuyue/orderDetail', {\n        id: that.orderId\n      }, function(res) {\n        app.showLoading(false);\n        console.log('获取订单信息原始返回:', res);\n        \n        // 兼容不同的返回结构\n        if (res.status == 1 && res.data) {\n          that.orderInfo = res.data;\n        } else if (res.detail) {\n          that.orderInfo = res.detail;\n        } else {\n          that.orderInfo = res;\n        }\n        \n        // 确保product存在\n        if(!that.orderInfo.product) {\n          that.orderInfo.product = {\n            pic: that.orderInfo.propic || '',\n            name: that.orderInfo.proname || '商品信息',\n            sell_price: that.orderInfo.product_price || that.orderInfo.totalprice || 0\n          };\n        }\n        \n        // 添加订单号\n        if (!that.orderInfo.order_no) {\n          that.orderInfo.order_no = that.orderInfo.ordernum || '';\n        }\n        \n        // 获取预约时间\n        if (!that.orderInfo.yy_time) {\n          if (that.orderInfo.yy_time_format) {\n            that.orderInfo.yy_time = that.orderInfo.yy_time_format;\n          } else if (that.orderInfo.yydate && that.orderInfo.yytime) {\n            that.orderInfo.yy_time = that.orderInfo.yydate + ' ' + that.orderInfo.yytime;\n          }\n        }\n          \n        // 获取服务人员列表\n        that.getWorkerList();\n      });\n    },\n    \n    // 获取服务人员列表\n    getWorkerList() {\n      var app = getApp();\n      var that = this;\n      app.showLoading('加载服务人员');\n      \n      // 优先使用传入的yydate，如果没有则使用订单中的日期\n      const date = that.yydate || that.orderInfo.yydate || '';\n      \n      console.log('获取服务人员参数:', {\n        id: that.orderId,\n        yydate: date\n      });\n      \n      app.get('ApiYuyue/getAvailableWorkers', {\n        id: that.orderId,\n        yydate: date\n      }, function(res) {\n        app.showLoading(false);\n        \n        console.log('获取服务人员列表返回:', res);\n        \n        let workerData = [];\n        \n        // 兼容不同的返回结构\n        if (res.status == 1 && res.data) {\n          workerData = res.data;\n        } else if (res.list) {\n          workerData = res.list;\n        } else if (Array.isArray(res)) {\n          workerData = res;\n        }\n        \n        that.workerList = workerData;\n        \n        // 如果只有一个服务人员，自动选择\n        if(that.workerList.length === 1) {\n          that.selectWorker(that.workerList[0]);\n        }\n      });\n    },\n    \n    // 选择服务人员\n    selectWorker(worker) {\n      console.log('选择服务人员:', worker);\n      this.selectedWorkerId = worker.id;\n    },\n    \n    // 提交分配\n    submitAssign() {\n      var app = getApp();\n      var that = this;\n      if(!that.selectedWorkerId) {\n        app.error('请选择服务人员');\n        return;\n      }\n      \n      const params = {\n        id: that.orderId,\n        worker_id: that.selectedWorkerId\n      };\n      \n      console.log('提交分配参数:', params);\n      \n      app.showLoading('提交选择');\n      app.post('ApiYuyue/assignWorker', params, function(res) {\n        app.showLoading(false);\n        console.log('分配服务人员返回:', res);\n        \n        // 处理不同的返回结构\n        let success = false;\n        let message = '';\n        \n        if (res.status == 1) {\n          success = true;\n          message = res.msg || '选择成功';\n        } else if (res.code == 200 || res.code == 1) {\n          success = true;\n          message = res.message || res.msg || '选择成功';\n        }\n        \n        if (success) {\n          app.alert(message, function() {\n            app.goto('/yuyue/orderlist');\n          });\n        } else {\n          app.error(res.msg || res.message || '选择失败');\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n  padding: 20rpx;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.order-info {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.product-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.product-info image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n}\n\n.info {\n  flex: 1;\n}\n\n.name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.price {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.order-detail {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.order-detail text {\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.worker-list-section {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 120rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.worker-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.worker-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1px solid #f5f5f5;\n  position: relative;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n}\n\n.worker-info {\n  flex: 1;\n}\n\n.worker-info .name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.worker-info .stats {\n  display: flex;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.worker-info .stats text {\n  margin-right: 20rpx;\n}\n\n.select-icon {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 20rpx;\n  color: #fff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.no-worker {\n  padding: 60rpx 0;\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.no-worker text {\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 20rpx;\n  background: #fff;\n  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.submit-btn {\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  color: #fff;\n  font-size: 30rpx;\n  border-radius: 40rpx;\n}\n\n/* 新增阴影效果的类 */\n.shadow-box {\n  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.2);\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectworker.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectworker.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115042604\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}