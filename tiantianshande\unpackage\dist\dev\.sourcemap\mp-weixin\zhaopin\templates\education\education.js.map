{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?3121", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?c148", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?74d7", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?1737", "uni-app:///zhaopin/templates/education/education.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?1211", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/templates/education/education.vue?7edf"], "names": ["props", "partJobVo", "type", "default", "title", "salary", "description", "requirement", "address", "province", "city", "district", "jobDesc", "company", "name", "logo", "industry", "scale", "introduction", "labelList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requireList", "work_mode", "work_intensity", "work_time_type", "payment", "education", "experience", "formatted_options", "company_show", "chosenList", "tabCurrent", "hasEyeAuth", "isShowAll", "agreementVo", "data", "isInfoShowBtn", "isComputedInfo", "isDescShowBtn", "isComputedDesc", "isReqShowBtn", "isComputedReq", "isCompanyShowBtn", "isComputedCompany", "healthVisible", "isBenefitsShowBtn", "isComputedBenefits", "created", "console", "methods", "getGenderText", "getHousingProvidedText", "jumpToCompany", "uni", "url", "formatRichText", "content", "replace", "getDescHeight", "setTimeout", "query", "getReqHeight", "getInfoHeight", "getCompanyHeight", "getBenefitsHeight", "descBtnTap", "reqBtnTap", "infoBtnTap", "companyBtnTap", "benefitsBtnTap"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC+PlxB;EACAA;IACAC;MACAC;MACAC;QAAA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACAC;YACAC;UACA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IACAC;MACA5B;MACAC;QAAA;MAAA;IACA;IACA4B;MACA7B;MACAC;IACA;IACA6B;MACA9B;MACAC;IACA;IACA8B;MACA/B;MACAC;IACA;IACA+B;MACAhC;MACAC;QAAA;MAAA;IACA;EACA;EACAgC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAC;IACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACAC,0CACAC,sBACAA,sBACAA,uBACAA,wBACAA,uBACAA,0BACAA,yBACAA,yBACAA;;QAEA;QACAD;;QAEA;QACAA,8CACAC;;QAEA;QACAD,gDACAC,8BACAA;;QAEA;QACAD,mDACAC,+BACAA,6BACAA;;QAEA;QACAD,2EACAC;;QAEA;QACAD;UACA;UACA;QACA;;QAEA;QACAA;;QAEA;QACAA;MAEA;QACAR;QACA;QACA;MACA;MACA;IACA;IACA;IACAU;MAAA;MACAC;QACA;QACAC;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACAF;QACA;QACAC;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAE;MAAA;MACAH;QACA;QACAC;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAG;MAAA;MACA;QACAJ;UACA;UACAC;YACA;cACA;cACA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAI;MAAA;MACAL;QACA;QACAC;UACA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAK;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACzgBA;AAAA;AAAA;AAAA;AAAq6C,CAAgB,g3CAAG,EAAC,C;;;;;;;;;;;ACAz7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/templates/education/education.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./education.vue?vue&type=template&id=0ad9efb7&\"\nvar renderjs\nimport script from \"./education.vue?vue&type=script&lang=js&\"\nexport * from \"./education.vue?vue&type=script&lang=js&\"\nimport style0 from \"./education.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/templates/education/education.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./education.vue?vue&type=template&id=0ad9efb7&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.partJobVo.commission_detail &&\n    _vm.partJobVo.commission_detail.calculation\n      ? _vm.t(\"color1\")\n      : null\n  var l0 = _vm.partJobVo.formatted_options\n    ? Object.values(_vm.partJobVo.formatted_options).flat()\n    : null\n  var m1 = _vm.t(\"color1\")\n  var m2 = _vm.t(\"color2\")\n  var m3 = _vm.t(\"color2\")\n  var m4 =\n    _vm.partJobVo.gender_requirement !== undefined\n      ? _vm.getGenderText(_vm.partJobVo.gender_requirement)\n      : null\n  var m5 =\n    _vm.partJobVo.housing_provided !== undefined\n      ? _vm.getHousingProvidedText(_vm.partJobVo.housing_provided)\n      : null\n  var m6 = _vm.formatRichText(_vm.partJobVo.benefits || \"暂无其他福利\")\n  var m7 = _vm.formatRichText(_vm.partJobVo.description || \"暂无描述\")\n  var m8 = _vm.partJobVo.requirement\n    ? _vm.formatRichText(_vm.partJobVo.requirement)\n    : null\n  var m9 =\n    _vm.partJobVo.company_introduction && _vm.partJobVo.company_show === 1\n      ? _vm.formatRichText(_vm.partJobVo.company_introduction)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./education.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./education.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"common-header education-header pb16\">\r\n\t\t\t<view class=\"header-top\">\r\n\t\t\t\t<view class=\"common-header-title\">{{ partJobVo.title }}</view>\r\n\t\t\t\t<view class=\"commission-info\" v-if=\"partJobVo.commission_detail && partJobVo.commission_detail.calculation\" :style=\"{background: t('color1'), opacity: 0.8}\">\r\n\t\t\t\t\t<text class=\"commission-text\" :style=\"{color: '#fff'}\">{{ partJobVo.commission_detail.calculation }}\r\n\t\t\t\t\t\t<!-- <text :style=\"{color: '#fff'}\" class=\"commission-label\">预计佣金</text> -->\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rpo-salary\">\r\n\t\t\t\t<view class=\"rpo-salary-num\">{{ partJobVo.salary }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"common-list-tag list-tag-box\">\r\n\t\t\t\t<template v-if=\"partJobVo.formatted_options\">\r\n\t\t\t\t\t<view class=\"list-tag\" v-for=\"(tag, index) in Object.values(partJobVo.formatted_options).flat()\" :key=\"'format_' + index\">\r\n\t\t\t\t\t\t<view class=\"ellipsis\">{{ tag }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.work_mode\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.work_mode }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.work_intensity\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.work_intensity }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.work_time_type\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.work_time_type }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.payment\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.payment }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.education\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.education }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-tag\" v-if=\"partJobVo.experience\">\r\n\t\t\t\t\t<view class=\"ellipsis\">{{ partJobVo.experience }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- <view class=\"anchor-new-company\" v-if=\"partJobVo.company\">\r\n\t\t\t\t<image :src=\"partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"anchor-new-info\">\r\n\t\t\t\t\t<view class=\"name ellipsis\">{{ partJobVo.company.name }}</view>\r\n\t\t\t\t\t<view class=\"desc ellipsis\">{{ partJobVo.company.industry }} | {{ partJobVo.company.scale }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n<!-- \r\n\t\t\t<view class=\"anchor-new-place\">\r\n\t\t\t\t<view class=\"place-bold\">{{ partJobVo.address }}</view>\r\n\t\t\t\t<view class=\"place-normal\">{{ partJobVo.province }} {{ partJobVo.city }} {{ partJobVo.district }}</view>\r\n\t\t\t</view> -->\r\n\r\n\t\t\t<view @tap=\"skiptoWebview\" class=\"common-safe ptp_exposure_static\" data-ptpid=\"69a4-1137-8c63-fcc7\"\r\n\t\t\t\tid=\"pid=69a4-1137-8c63-fcc7\" :style=\"{background: t('color1'), opacity: 0.8}\">\r\n\t\t\t\t<view class=\"common-safe-left\">\r\n\t\t\t\t\t<view class=\"iconfont iconsafeguard\" :style=\"{color: t('color2')}\"></view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view :style=\"{color: '#fff', fontWeight: 'bold'}\">此职位经过官方认证，放心投递+职位无忧</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"iconfont iconarrow\" :style=\"{color: t('color2')}\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t<!-- 基本信息 -->\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\">基本信息</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.work_address\">\r\n\t\t\t\t<view class=\"info-label\">工作地点</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.work_address }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.education\">\r\n\t\t\t\t<view class=\"info-label\">学历要求</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.education }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.experience\">\r\n\t\t\t\t<view class=\"info-label\">经验要求</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.experience }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.age_requirement\">\r\n\t\t\t\t<view class=\"info-label\">年龄要求</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.age_requirement }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.gender_requirement !== undefined\">\r\n\t\t\t\t<view class=\"info-label\">性别要求</view>\r\n\t\t\t\t<view class=\"info-content\">{{ getGenderText(partJobVo.gender_requirement) }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.numbers\">\r\n\t\t\t\t<view class=\"info-label\">招聘人数</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.numbers }}人</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 工作安排 -->\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\">工作安排</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.work_mode\">\r\n\t\t\t\t<view class=\"info-label\">工作形式</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.work_mode }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.payment\">\r\n\t\t\t\t<view class=\"info-label\">结算方式</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.payment }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.work_time_start || partJobVo.work_time_end\">\r\n\t\t\t\t<view class=\"info-label\">工作时间</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.work_time_start }} - {{ partJobVo.work_time_end }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.work_time_type\">\r\n\t\t\t\t<view class=\"info-label\">班次类型</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.work_time_type }}</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"info-item\" v-if=\"partJobVo.work_intensity\">\r\n\t\t\t\t<view class=\"info-label\">工作强度</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.work_intensity }}</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.rest_time\">\r\n\t\t\t\t<view class=\"info-label\">休息时间</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.rest_time }}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 福利待遇 -->\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\">福利待遇</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.meal_provided !== undefined\">\r\n\t\t\t\t<view class=\"info-label\">工作餐</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.meal_provided === 1 ? '提供' : '不提供' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.meal_provided === 0 && partJobVo.meal_allowance\">\r\n\t\t\t\t<view class=\"info-label\">餐费补贴</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.meal_allowance }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.housing_provided !== undefined\">\r\n\t\t\t\t<view class=\"info-label\">住宿</view>\r\n\t\t\t\t<view class=\"info-content\">{{ getHousingProvidedText(partJobVo.housing_provided) }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"partJobVo.housing_provided === 2 && partJobVo.housing_fee\">\r\n\t\t\t\t<view class=\"info-label\">住宿费用</view>\r\n\t\t\t\t<view class=\"info-content\">{{ partJobVo.housing_fee }}元/月</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 其他信息 -->\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\">其他福利</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view :class=\"'detail-info-box ' + (isBenefitsShowBtn ? '' : 'detail-info-show')\">\r\n\t\t\t\t<rich-text class=\"detail-info-text\" :nodes=\"formatRichText(partJobVo.benefits || '暂无其他福利')\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"benefitsBtnTap\" class=\"detail-info-btn\" v-if=\"isBenefitsShowBtn && isComputedBenefits\">\r\n\t\t\t\t查看更多\r\n\t\t\t\t<view class=\"iconfont iconarrow_down\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\">职位描述</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view :class=\"'detail-info-box ' + (isDescShowBtn ? '' : 'detail-info-show')\">\r\n\t\t\t\t<rich-text class=\"detail-info-text\" :nodes=\"formatRichText(partJobVo.description || '暂无描述')\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"descBtnTap\" class=\"detail-info-btn\" v-if=\"isDescShowBtn && isComputedDesc\">\r\n\t\t\t\t查看更多\r\n\t\t\t\t<view class=\"iconfont iconarrow_down\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 视频展示区域 -->\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\" v-if=\"partJobVo.video_info && partJobVo.video_info.url\">视频介绍</view>\r\n\t\t<view class=\"common-box video-box\" v-if=\"partJobVo.video_info && partJobVo.video_info.url\">\r\n\t\t\t<video \r\n\t\t\t\t:src=\"partJobVo.video_info.url\"\r\n\t\t\t\t:poster=\"partJobVo.video_info.poster\"\r\n\t\t\t\t:controls=\"true\"\r\n\t\t\t\t:show-center-play-btn=\"true\"\r\n\t\t\t\t:enable-progress-gesture=\"true\"\r\n\t\t\t\t:show-fullscreen-btn=\"true\"\r\n\t\t\t\t:show-play-btn=\"true\"\r\n\t\t\t\t:show-progress=\"true\"\r\n\t\t\t\t:object-fit=\"'contain'\"\r\n\t\t\t\tclass=\"job-video\"\r\n\t\t\t></video>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\" v-if=\"partJobVo.requirement\">任职要求</view>\r\n\t\t<view class=\"common-box\" v-if=\"partJobVo.requirement\">\r\n\t\t\t<view :class=\"'detail-info-box ' + (isReqShowBtn ? '' : 'detail-info-show')\">\r\n\t\t\t\t<rich-text class=\"detail-info-text\" :nodes=\"formatRichText(partJobVo.requirement)\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"reqBtnTap\" class=\"detail-info-btn\" v-if=\"isReqShowBtn && isComputedReq\">\r\n\t\t\t\t查看更多\r\n\t\t\t\t<view class=\"iconfont iconarrow_down\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"common-title\" style=\"margin-top: 48rpx;\">培训详情</view>\r\n\t\t<view class=\"common-box\">\r\n\t\t\t<view :class=\"'detail-info-box ' + (isInfoShowBtn ? '' : 'detail-info-show')\">\r\n\t\t\t\t<rich-text class=\"detail-info-text\" :nodes=\"formatRichText(partJobVo.jobDesc || '1. 岗前培训：我们将提供专业的岗前培训，包括工作流程、服务标准、操作规范等内容。\\n2. 技能培训：定期组织专业技能培训，提升员工的专业水平。\\n3. 职业发展：提供清晰的职业发展路径，有机会晋升为店长或区域经理。\\n4. 在岗指导：配备经验丰富的导师，提供一对一指导。')\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"infoBtnTap\" class=\"detail-info-btn\" v-if=\"isInfoShowBtn && isComputedInfo\">\r\n\t\t\t\t查看更多\r\n\t\t\t\t<view class=\"iconfont iconarrow_down\"></view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- <view class=\"common-title\" style=\"margin-top: 48rpx;\">入选专区</view>\r\n\t\t<view class=\"chosen-inner-item single\">\r\n\t\t\t<view class=\"single-box\">\r\n\t\t\t\t<image class=\"logo\" src=\"https://qiniu-image.qtshe.com/entrance/bd_small.png\"></image>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"title ellipsis\">高薪急招</view>\r\n\t\t\t\t\t<view class=\"desc\">入选高薪急招专区</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn orange\">进入榜单</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\" v-if=\"partJobVo.company_introduction && partJobVo.company_show === 1\">公司介绍</view>\r\n\t\t<view class=\"common-box\" v-if=\"partJobVo.company_introduction && partJobVo.company_show === 1\">\r\n\t\t\t<view :class=\"'detail-info-box ' + (isCompanyShowBtn ? '' : 'detail-info-show')\">\r\n\t\t\t\t<rich-text class=\"detail-info-text\" :nodes=\"formatRichText(partJobVo.company_introduction)\"></rich-text>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"companyBtnTap\" class=\"detail-info-btn\" v-if=\"isCompanyShowBtn && isComputedCompany\">\r\n\t\t\t\t查看更多\r\n\t\t\t\t<view class=\"iconfont iconarrow_down\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"common-title\" style=\"margin-top: 48rpx;\" v-if=\"partJobVo.company_show === 1\">{{ partJobVo.company.companyType === 2 ? '发布者' : '发布企业' }}</view>\r\n\t\t<view class=\"common-box\" v-if=\"partJobVo.company_show === 1\" :class=\"(partJobVo.company.isOfficialAccount || partJobVo.company.companyType === 2 || (partJobVo.company.companyType === 1 && hasEyeAuth) ? 'pb0' : '')\">\r\n\t\t\t<view @tap=\"$emit('clickCompany')\" class=\"common-company ptp_exposure_static\" data-ptpid=\"c4e6-18fa-96fc-d96d\" id=\"pid=c4e6-18fa-96fc-d96d\">\r\n\t\t\t\t<view class=\"common-company-box\">\r\n\t\t\t\t\t<image lazyLoad class=\"common-company-logo\" :src=\"partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'\"></image>\r\n\t\t\t\t\t<view class=\"common-company-main\">\r\n\t\t\t\t\t\t<view class=\"common-company-name ellipsis\">{{ partJobVo.company.name }}</view>\r\n\t\t\t\t\t\t<view class=\"common-company-auth ignoreT2\">\r\n\t\t\t\t\t\t\t<view class=\"iconfont iconverified company-icon-blue\"></view>\r\n\t\t\t\t\t\t\t<view>企业认证</view>\r\n\t\t\t\t\t\t\t<image class=\"common-company-auth-icon\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210106_icon.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"iconfont iconarrow\" v-if=\"hasEyeAuth\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"official-box\">\r\n\t\t\t\t<view class=\"official-item\">\r\n\t\t\t\t\t<view class=\"iconfont iconconfirm_round\"></view>\r\n\t\t\t\t\t已通过天眼查认证\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tpartJobVo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => ({\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tsalary: '',\r\n\t\t\t\t\tdescription: '',\r\n\t\t\t\t\trequirement: '',\r\n\t\t\t\t\taddress: '',\r\n\t\t\t\t\tprovince: '',\r\n\t\t\t\t\tcity: '',\r\n\t\t\t\t\tdistrict: '',\r\n\t\t\t\t\tjobDesc: '',\r\n\t\t\t\t\tcompany: {\r\n\t\t\t\t\t\tname: '',\r\n\t\t\t\t\t\tlogo: '',\r\n\t\t\t\t\t\tindustry: '',\r\n\t\t\t\t\t\tscale: '',\r\n\t\t\t\t\t\tintroduction: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\tlabelList: {\r\n\t\t\t\t\t\tdescLabels: []\r\n\t\t\t\t\t},\r\n\t\t\t\t\trequireList: [],\r\n\t\t\t\t\twork_mode: '',\r\n\t\t\t\t\twork_intensity: '',\r\n\t\t\t\t\twork_time_type: '',\r\n\t\t\t\t\tpayment: '',\r\n\t\t\t\t\teducation: '',\r\n\t\t\t\t\texperience: '',\r\n\t\t\t\t\tformatted_options: {},\r\n\t\t\t\t\tcompany_show: 0\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchosenList: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t\ttabCurrent: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\thasEyeAuth: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tisShowAll: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tagreementVo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => ({})\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisInfoShowBtn: true,\r\n\t\t\t\tisComputedInfo: false,\r\n\t\t\t\tisDescShowBtn: true,\r\n\t\t\t\tisComputedDesc: false,\r\n\t\t\t\tisReqShowBtn: true,\r\n\t\t\t\tisComputedReq: false,\r\n\t\t\t\tisCompanyShowBtn: true,\r\n\t\t\t\tisComputedCompany: false,\r\n\t\t\t\thealthVisible: false,\r\n\t\t\t\tisBenefitsShowBtn: true,\r\n\t\t\t\tisComputedBenefits: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tconsole.log('education组件初始化', this.partJobVo);\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.getInfoHeight();\r\n\t\t\t\tthis.getDescHeight();\r\n\t\t\t\tthis.getReqHeight();\r\n\t\t\t\tthis.getCompanyHeight();\r\n\t\t\t\tthis.getBenefitsHeight();\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取性别要求文本\r\n\t\t\tgetGenderText(gender) {\r\n\t\t\t\tswitch(gender) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\treturn '不限';\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\treturn '男';\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\treturn '女';\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn '不限';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取住宿类型文本\r\n\t\t\tgetHousingProvidedText(type) {\r\n\t\t\t\tif(type === 1) return '包住';\r\n\t\t\t\tif(type === 2) return '有偿住宿';\r\n\t\t\t\tif(type === 0) return '不包住';\r\n\t\t\t\treturn '不包住';\r\n\t\t\t},\r\n\t\t\t// 跳转到公司\r\n\t\t\tjumpToCompany() {\r\n\t\t\t\tif (this.hasEyeAuth && this.partJobVo.company && this.partJobVo.company.id) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: \"/pagesExa/zhaopin/company?id=\" + this.partJobVo.company.id\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 格式化富文本\r\n\t\t\tformatRichText(text) {\r\n\t\t\t\tif (!text) return '';\r\n\t\t\t\tlet content = text;\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 替换常见的HTML实体\r\n\t\t\t\t\tcontent = content.replace(/&nbsp;/g, ' ')\r\n\t\t\t\t\t\t\t\t   .replace(/&lt;/g, '<')\r\n\t\t\t\t\t\t\t\t   .replace(/&gt;/g, '>')\r\n\t\t\t\t\t\t\t\t   .replace(/&amp;/g, '&')\r\n\t\t\t\t\t\t\t\t   .replace(/&quot;/g, '\"')\r\n\t\t\t\t\t\t\t\t   .replace(/&#39;/g, \"'\")\r\n\t\t\t\t\t\t\t\t   .replace(/&middot;/g, '·')\r\n\t\t\t\t\t\t\t\t   .replace(/&ldquo;/g, '\"')\r\n\t\t\t\t\t\t\t\t   .replace(/&rdquo;/g, '\"')\r\n\t\t\t\t\t\t\t\t   .replace(/&hellip;/g, '...');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理换行\r\n\t\t\t\t\tcontent = content.replace(/\\n/g, '<br>');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理段落\r\n\t\t\t\t\tcontent = content.replace(/<p[^>]*>/g, '<p>')\r\n\t\t\t\t\t\t\t\t   .replace(/<\\/p>/g, '</p><br>');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理列表\r\n\t\t\t\t\tcontent = content.replace(/<ul[^>]*>/g, '<ul>')\r\n\t\t\t\t\t\t\t\t   .replace(/<ol[^>]*>/g, '<ol>')\r\n\t\t\t\t\t\t\t\t   .replace(/<li[^>]*>/g, '<li>');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理加粗和斜体\r\n\t\t\t\t\tcontent = content.replace(/<strong[^>]*>/g, '<b>')\r\n\t\t\t\t\t\t\t\t   .replace(/<\\/strong>/g, '</b>')\r\n\t\t\t\t\t\t\t\t   .replace(/<em[^>]*>/g, '<i>')\r\n\t\t\t\t\t\t\t\t   .replace(/<\\/em>/g, '</i>');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理链接\r\n\t\t\t\t\tcontent = content.replace(/<a[^>]*href=\"([^\"]*)\"[^>]*>/g, '<a href=\"$1\">')\r\n\t\t\t\t\t\t\t\t   .replace(/<\\/a>/g, '</a>');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 移除其他HTML标签的属性\r\n\t\t\t\t\tcontent = content.replace(/<([^>]+)>/g, (match, tag) => {\r\n\t\t\t\t\t\tconst tagName = tag.split(' ')[0];\r\n\t\t\t\t\t\treturn `<${tagName}>`;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保内容是一个有效的字符串\r\n\t\t\t\t\tcontent = String(content).trim();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 移除多余的换行\r\n\t\t\t\t\tcontent = content.replace(/(<br>){3,}/g, '<br><br>');\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('格式化富文本出错:', e);\r\n\t\t\t\t\t// 发生错误时返回纯文本\r\n\t\t\t\t\treturn text.replace(/<[^>]*>/g, '');\r\n\t\t\t\t}\r\n\t\t\t\treturn content;\r\n\t\t\t},\r\n\t\t\t// 判断职位描述是否显示更多\r\n\t\t\tgetDescHeight() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tquery.select('.common-box:nth-child(4) .detail-info-text').boundingClientRect(res1 => {\r\n\t\t\t\t\t\tif (res1 && res1.height) {\r\n\t\t\t\t\t\t\tthis.isDescShowBtn = res1.height > (44 * 4);\r\n\t\t\t\t\t\t\tthis.isComputedDesc = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isDescShowBtn = false;\r\n\t\t\t\t\t\t\tthis.isComputedDesc = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t// 判断任职要求是否显示更多\r\n\t\t\tgetReqHeight() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tquery.select('.common-box:nth-child(6) .detail-info-text').boundingClientRect(res1 => {\r\n\t\t\t\t\t\tif (res1 && res1.height) {\r\n\t\t\t\t\t\t\tthis.isReqShowBtn = res1.height > (44 * 4);\r\n\t\t\t\t\t\t\tthis.isComputedReq = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isReqShowBtn = false;\r\n\t\t\t\t\t\t\tthis.isComputedReq = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t// 判断培训详情是否显示更多\r\n\t\t\tgetInfoHeight() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tquery.select('.common-box:nth-child(8) .detail-info-text').boundingClientRect(res1 => {\r\n\t\t\t\t\t\tif (res1 && res1.height) {\r\n\t\t\t\t\t\t\tthis.isInfoShowBtn = res1.height > (44 * 4);\r\n\t\t\t\t\t\t\tthis.isComputedInfo = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isInfoShowBtn = false;\r\n\t\t\t\t\t\t\tthis.isComputedInfo = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t// 判断公司介绍是否显示更多\r\n\t\t\tgetCompanyHeight() {\r\n\t\t\t\tif (this.partJobVo.company && this.partJobVo.company.introduction) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\t\tquery.select('.common-box:nth-child(12) .detail-info-text').boundingClientRect(res1 => {\r\n\t\t\t\t\t\t\tif (res1 && res1.height) {\r\n\t\t\t\t\t\t\t\tthis.isCompanyShowBtn = res1.height > (44 * 4);\r\n\t\t\t\t\t\t\t\tthis.isComputedCompany = true;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.isCompanyShowBtn = false;\r\n\t\t\t\t\t\t\t\tthis.isComputedCompany = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).exec();\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 判断其他福利是否显示更多\r\n\t\t\tgetBenefitsHeight() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tquery.select('.common-box:nth-child(10) .detail-info-text').boundingClientRect(res1 => {\r\n\t\t\t\t\t\tif (res1 && res1.height) {\r\n\t\t\t\t\t\t\tthis.isBenefitsShowBtn = res1.height > (44 * 4);\r\n\t\t\t\t\t\t\tthis.isComputedBenefits = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isBenefitsShowBtn = false;\r\n\t\t\t\t\t\t\tthis.isComputedBenefits = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t// 点击展开职位描述\r\n\t\t\tdescBtnTap() {\r\n\t\t\t\tthis.isDescShowBtn = false;\r\n\t\t\t},\r\n\t\t\t// 点击展开任职要求\r\n\t\t\treqBtnTap() {\r\n\t\t\t\tthis.isReqShowBtn = false;\r\n\t\t\t},\r\n\t\t\t// 点击展开培训详情\r\n\t\t\tinfoBtnTap() {\r\n\t\t\t\tthis.isInfoShowBtn = false;\r\n\t\t\t},\r\n\t\t\t// 点击展开公司介绍\r\n\t\t\tcompanyBtnTap() {\r\n\t\t\t\tthis.isCompanyShowBtn = false;\r\n\t\t\t},\r\n\t\t\t// 点击展开其他福利\r\n\t\t\tbenefitsBtnTap() {\r\n\t\t\t\tthis.isBenefitsShowBtn = false;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import './education.scss';\t\r\n\t\r\n\t.header-top {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t.common-header-title {\r\n\t\tcolor: #111e38;\r\n\t\tfont-size: 44rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tfont-weight: 700;\r\n\t\tflex: 1;\r\n\t\tmargin-right: 120rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.commission-info {\r\n\t\tposition: fixed;\r\n\t\tright: 32rpx;\r\n\t\ttop: 240rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.7);\r\n\t\tbackdrop-filter: blur(20px);\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 26rpx 24rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t\tz-index: 999;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\twidth: 180rpx;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tinset: 0;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tborder: 1px solid rgba(255, 107, 0, 0.1);\r\n\t\t\tz-index: -1;\r\n\t\t}\r\n\t\t\r\n\t\t.commission-text {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: 8rpx;\r\n\t\t\t\r\n\t\t\t&::before {\r\n\t\t\t\tcontent: '预计佣金';\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tletter-spacing: 1rpx;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-8rpx);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.detail-info-box {\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\ttransition: all 0.3s;\r\n\t\t\r\n\t\t&:not(.detail-info-show) {\r\n\t\t\t.detail-info-text {\r\n\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t-webkit-line-clamp: 4;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tpadding-bottom: 40rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.detail-info-show {\r\n\t\t\t.detail-info-text {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tpadding-bottom: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.detail-info-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tline-height: 44rpx;\r\n\t\t\tcolor: #111E38;\r\n\t\t\t\r\n\t\t\t/deep/ {\r\n\t\t\t\tp {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tul {\r\n\t\t\t\t\tmargin: 16rpx 0;\r\n\t\t\t\t\tpadding-left: 40rpx;\r\n\t\t\t\t\tlist-style: disc;\r\n\t\t\t\t\t\r\n\t\t\t\t\tli {\r\n\t\t\t\t\t\tmargin: 16rpx 0;\r\n\t\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tp {\r\n\t\t\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ta {\r\n\t\t\t\t\tcolor: #3370ff;\r\n\t\t\t\t\ttext-decoration: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&:not(.detail-info-show)::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 50%, #FFFFFF 100%);\r\n\t\t\tpointer-events: none;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.detail-info-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\theight: 80rpx;\r\n\t\tcolor: #3370ff;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: -40rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t\t\r\n\t\t.iconarrow_down {\r\n\t\t\tmargin-left: 8rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.chosen-inner-item.single {\r\n\t\twidth: 686rpx;\r\n\t\theight: 132rpx;\r\n\t\tpadding: 24rpx 32rpx 24rpx 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 24rpx;\r\n\t\tmargin: 0 auto;\r\n\t\t\r\n\t\t.single-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.logo {\r\n\t\t\t\twidth: 64rpx;\r\n\t\t\t\theight: 64rpx;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #111e38;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t\tmax-width: 406rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.desc {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #808999;\r\n\t\t\t\tline-height: 32rpx;\r\n\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.btn {\r\n\t\t\twidth: 140rpx;\r\n\t\t\theight: 56rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\t\r\n\t\t\t&.orange {\r\n\t\t\t\tcolor: #ff8000;\r\n\t\t\t\tbackground: #fff2e5;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.green {\r\n\t\t\t\tcolor: #00ca88;\r\n\t\t\t\tbackground: #e5fcf4;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.common-list-tag {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 10rpx;\r\n\t\tmax-height: 180rpx;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.list-tag {\r\n\t\t\theight: 48rpx;\r\n\t\t\tpadding: 0 16rpx;\r\n\t\t\tbackground: #F5F7FA;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.ellipsis {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #485670;\r\n\t\t\t\tline-height: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 24rpx 0;\r\n\t\tborder-bottom: 1px solid #F5F7FA;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t.info-label {\r\n\t\t\twidth: 160rpx;\r\n\t\t\tcolor: #808999;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tline-height: 40rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.info-content {\r\n\t\t\tflex: 1;\r\n\t\t\tcolor: #111E38;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tline-height: 40rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.video-box {\r\n\t\tpadding: 24rpx;\r\n\t\t\r\n\t\t.job-video {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 400rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tbackground: #000;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./education.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./education.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115070711\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}