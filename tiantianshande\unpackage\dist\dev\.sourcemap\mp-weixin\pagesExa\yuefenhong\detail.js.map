{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?b524", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?c723", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?aa19", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?4ea5", "uni-app:///pagesExa/yuefenhong/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?129a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/detail.vue?3eb0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activityId", "activityDetail", "detailList", "pendingAmount", "participantCount", "currentPage", "pageSize", "totalItems", "totalPages", "loading", "nodata", "nomore", "isAdmin", "showDividendModal", "onLoad", "onPullDownRefresh", "uni", "methods", "getActivityDetail", "app", "aid", "id", "that", "getDetailList", "fenhong_id", "page", "limit", "list", "count", "getParticipantCount", "checkAdminStatus", "formatTime", "getStatusClass", "getStatusText", "changePage", "triggerDividend", "confirmDividend"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4I/wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAA;QACAC;QACAC;MACA;QACAF;QACA;UACAG;;UAEA;UACA;UACA;UACAA;;UAEA;UACAA;QACA;UACAH;QACA;MACA;IACA;IACAI;MACA;MACAD;MACAH;MACAA;QACAC;QACAI;QACAC;QACAC;MACA;QACAJ;QACAH;QACA;UACA;YAAAQ;YAAAC;UACAN;UACAA;UACAA;UACAA;UACAA;QACA;UACAH;QACA;MACA;IACA;IACAU;MACA;MACA;MACA;MACAV;QACAC;QACAC;MACA;QACA;UACAC;QACA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACAX;QACAC;MACA;QACA;UACAE;QACA;MACA;IACA;IACAS;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAjB;MACAA;QACAC;QACAC;MACA;QACAF;QACAG;QACA;UACAH;UACA;UACAG;UACAA;QACA;UACAH;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAA07C,CAAgB,q4CAAG,EAAC,C;;;;;;;;;;;ACA98C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/yuefenhong/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/yuefenhong/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=608e267a&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=608e267a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"608e267a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/yuefenhong/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=608e267a&scoped=true&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getStatusText()\n  var m1 = _vm.formatTime(_vm.activityDetail.createtime)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"yuefenhong-detail-container\">\r\n    <!-- 顶部活动信息 -->\r\n    <view class=\"activity-header\">\r\n      <view class=\"header-content\">\r\n        <view class=\"title-section\">\r\n          <text class=\"activity-title\">{{activityDetail.title}}</text>\r\n          <text class=\"activity-status\" :class=\"activityDetail.status === 1 ? 'active' : 'ended'\">\r\n            {{getStatusText()}}\r\n          </text>\r\n        </view>\r\n        <view class=\"activity-info\">\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">分红比例:</text>\r\n            <text class=\"value\">{{activityDetail.fenhong_ratio}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">奖金池比例:</text>\r\n            <text class=\"value\">{{activityDetail.pool_ratio}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"label\">创建时间:</text>\r\n            <text class=\"value\">{{formatTime(activityDetail.createtime)}}</text>\r\n          </view>\r\n        </view>\r\n      <!--  <view class=\"rules-section\">\r\n          <text class=\"section-title\">分红规则</text>\r\n          <view class=\"rules-content\">\r\n            <view class=\"rule-item\">\r\n              <text class=\"rule-label\">参与等级:</text>\r\n              <text class=\"rule-value\">{{activityDetail.level_names || '无限制'}}</text>\r\n            </view>\r\n            <view class=\"rule-item\">\r\n              <text class=\"rule-label\">业绩等级:</text>\r\n              <text class=\"rule-value\">{{activityDetail.yeji_level_names || '无限制'}}</text>\r\n            </view>\r\n            <view class=\"rule-item\" v-if=\"activityDetail.deduct_contribution === 1\">\r\n              <text class=\"rule-label\">贡献值扣除:</text>\r\n              <text class=\"rule-value\">{{activityDetail.contribution_ratio}}倍</text>\r\n            </view>\r\n          </view>\r\n        </view> -->\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 数据概览 -->\r\n    <view class=\"data-overview-section\">\r\n      <view class=\"overview-grid\">\r\n        <view class=\"overview-card\">\r\n          <text class=\"card-value\">¥{{activityDetail.pool_amount || '0.00'}}</text>\r\n          <text class=\"card-label\">当前奖金池</text>\r\n        </view>\r\n        <view class=\"overview-card\">\r\n          <text class=\"card-value\">¥{{activityDetail.total_amount || '0.00'}}</text>\r\n          <text class=\"card-label\">总分红金额</text>\r\n        </view>\r\n        <view class=\"overview-card\">\r\n          <text class=\"card-value\">¥{{pendingAmount || '0.00'}}</text>\r\n          <text class=\"card-label\">待分红金额</text>\r\n        </view>\r\n        <view class=\"overview-card\">\r\n          <text class=\"card-value\">{{participantCount || '0'}}</text>\r\n          <text class=\"card-label\">参与人数</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 分红明细列表 -->\r\n   <!-- <view class=\"members-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">分红明细列表</text>\r\n        <view class=\"header-actions\">\r\n          <button class=\"action-btn refresh-btn\" @click=\"getDetailList\">刷新</button>\r\n        </view>\r\n      </view> -->\r\n      \r\n    <!--  <view class=\"table-container\">\r\n        <view class=\"table-header\">\r\n          <view class=\"th\">会员名称</view>\r\n          <view class=\"th\">分红金额</view>\r\n          <view class=\"th\">分红时间</view>\r\n        </view>\r\n        <view class=\"table-body\">\r\n          <view class=\"tr\" v-for=\"(item, index) in detailList\" :key=\"index\">\r\n            <view class=\"td\">{{item.member_name}}</view>\r\n            <view class=\"td amount\">¥{{item.dividend_amount}}</view>\r\n            <view class=\"td\">{{formatTime(item.dividend_time)}}</view>\r\n          </view>\r\n        </view>\r\n      </view> -->\r\n      \r\n      <!-- 分页控制 -->\r\n      <!-- <view class=\"pagination\">\r\n        <view class=\"page-info\">\r\n          <text>共 {{totalItems}} 条记录，当前第 {{currentPage}}/{{totalPages}} 页</text>\r\n        </view>\r\n        <view class=\"page-controls\">\r\n          <button class=\"page-btn\" :disabled=\"currentPage <= 1\" @click=\"changePage(currentPage - 1)\">上一页</button>\r\n          <button class=\"page-btn\" :disabled=\"currentPage >= totalPages\" @click=\"changePage(currentPage + 1)\">下一页</button>\r\n        </view>\r\n      </view> -->\r\n      \r\n      <!-- 加载状态组件 -->\r\n      <nodata v-if=\"nodata\"></nodata>\r\n      <nomore v-if=\"nomore\"></nomore>\r\n      <loading v-if=\"loading\"></loading>\r\n    </view>\r\n    \r\n    <!-- 分红操作按钮 -->\r\n   <!-- <view class=\"action-section\" v-if=\"isAdmin\">\r\n      <button class=\"primary-btn\" @click=\"triggerDividend\">手动触发分红</button>\r\n    </view> -->\r\n    \r\n    <!-- 分红确认弹窗 -->\r\n    <!-- <view class=\"dividend-modal\" v-if=\"showDividendModal\">\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">确认分红</text>\r\n        </view>\r\n        <view class=\"modal-body\">\r\n          <text class=\"modal-text\">您确定要触发分红操作吗？此操作将按照当前奖金池金额和规则进行分红。</text>\r\n          <view class=\"modal-info\">\r\n            <text class=\"info-label\">当前奖金池:</text>\r\n            <text class=\"info-value\">¥{{activityDetail.pool_amount || '0.00'}}</text>\r\n          </view>\r\n          <view class=\"modal-info\">\r\n            <text class=\"info-label\">预计分红人数:</text>\r\n            <text class=\"info-value\">{{participantCount || '0'}}人</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"showDividendModal = false\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"confirmDividend\">确认分红</button>\r\n        </view>\r\n      </view>\r\n    </view> -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      activityId: '',\r\n      activityDetail: {},\r\n      detailList: [],\r\n      pendingAmount: '0.00',\r\n      participantCount: 0,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalItems: 0,\r\n      totalPages: 1,\r\n      loading: false,\r\n      nodata: false,\r\n      nomore: false,\r\n      isAdmin: false, // 是否管理员\r\n      showDividendModal: false\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    this.activityId = options.id || ''\r\n    this.getActivityDetail()\r\n    this.getDetailList()\r\n    this.checkAdminStatus()\r\n  },\r\n  onPullDownRefresh() {\r\n    this.getActivityDetail()\r\n    this.getDetailList()\r\n    uni.stopPullDownRefresh()\r\n  },\r\n  methods: {\r\n    getActivityDetail() {\r\n      const that = this\r\n      app.showLoading('加载中')\r\n      app.post('ApiYuefenhong/getDetail', {\r\n        aid: app.aid,\r\n        id: this.activityId\r\n      }, function(res) {\r\n        app.showLoading(false)\r\n        if (res.code === 1) {\r\n          that.activityDetail = res.data\r\n          \r\n          // 计算待分红金额\r\n          const totalAmount = parseFloat(that.activityDetail.total_amount || 0)\r\n          const poolAmount = parseFloat(that.activityDetail.pool_amount || 0)\r\n          that.pendingAmount = (poolAmount).toFixed(2)\r\n          \r\n          // 获取参与人数（这里可能需要额外的API调用）\r\n          that.getParticipantCount()\r\n        } else {\r\n          app.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    getDetailList() {\r\n      const that = this\r\n      that.loading = true\r\n      app.showLoading('加载中')\r\n      app.post('ApiYuefenhong/getDetailList', {\r\n        aid: app.aid,\r\n        fenhong_id: this.activityId,\r\n        page: this.currentPage,\r\n        limit: this.pageSize\r\n      }, function(res) {\r\n        that.loading = false\r\n        app.showLoading(false)\r\n        if (res.code === 1) {\r\n          const { list, count } = res.data\r\n          that.detailList = list\r\n          that.totalItems = count\r\n          that.totalPages = Math.ceil(count / that.pageSize)\r\n          that.nodata = list.length === 0\r\n          that.nomore = that.currentPage >= that.totalPages\r\n        } else {\r\n          app.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    getParticipantCount() {\r\n      // 这里可能需要额外的API调用来获取参与人数\r\n      // 示例代码，实际实现可能不同\r\n      const that = this\r\n      app.post('ApiYuefenhong/getParticipantCount', {\r\n        aid: app.aid,\r\n        id: this.activityId\r\n      }, function(res) {\r\n        if (res.code === 1) {\r\n          that.participantCount = res.data.count || 0\r\n        }\r\n      })\r\n    },\r\n    checkAdminStatus() {\r\n      // 检查当前用户是否有管理员权限\r\n      // 示例代码，实际实现可能不同\r\n      const that = this\r\n      app.post('ApiYuefenhong/checkAdminPermission', {\r\n        aid: app.aid\r\n      }, function(res) {\r\n        if (res.code === 1) {\r\n          that.isAdmin = res.data.is_admin || false\r\n        }\r\n      })\r\n    },\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      return time.substring(0, 16) // 只显示到分钟\r\n    },\r\n    getStatusClass() {\r\n      // 这里根据实际状态判断逻辑修改\r\n      return this.activityDetail.status === 1 ? 'active' : 'ended'\r\n    },\r\n    getStatusText() {\r\n      // 这里根据实际状态判断逻辑修改\r\n      return this.activityDetail.status === 1 ? '进行中' : '已结束'\r\n    },\r\n    changePage(page) {\r\n      if (page < 1 || page > this.totalPages) return\r\n      this.currentPage = page\r\n      this.getDetailList()\r\n    },\r\n    triggerDividend() {\r\n      this.showDividendModal = true\r\n    },\r\n    confirmDividend() {\r\n      const that = this\r\n      app.showLoading('处理中')\r\n      app.post('ApiYuefenhong/triggerDividend', {\r\n        aid: app.aid,\r\n        id: this.activityId\r\n      }, function(res) {\r\n        app.showLoading(false)\r\n        that.showDividendModal = false\r\n        if (res.code === 1) {\r\n          app.success(res.msg || '分红操作成功')\r\n          // 刷新数据\r\n          that.getActivityDetail()\r\n          that.getDetailList()\r\n        } else {\r\n          app.error(res.msg)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.yuefenhong-detail-container {\r\n  min-height: 100vh;\r\n  background: #f8f9fd;\r\n  padding-bottom: 120rpx;\r\n  \r\n  /deep/ .nodata, /deep/ .nomore, /deep/ .loading {\r\n    position: relative;\r\n    margin: 30rpx auto;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.activity-header {\r\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n  padding: 40rpx 30rpx;\r\n  color: #fff;\r\n  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.3);\r\n  \r\n  .header-content {\r\n    .title-section {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 30rpx;\r\n      \r\n      .activity-title {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n      }\r\n      \r\n      .activity-status {\r\n        font-size: 24rpx;\r\n        padding: 8rpx 20rpx;\r\n        border-radius: 30rpx;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        &.active {\r\n          background: #10b981;\r\n        }\r\n        \r\n        &.ended {\r\n          background: #6b7280;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .activity-info {\r\n      margin-bottom: 30rpx;\r\n      \r\n      .info-item {\r\n        display: flex;\r\n        margin-bottom: 10rpx;\r\n        \r\n        .label {\r\n          font-size: 28rpx;\r\n          opacity: 0.9;\r\n          margin-right: 20rpx;\r\n        }\r\n        \r\n        .value {\r\n          font-size: 28rpx;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .rules-section {\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 16rpx;\r\n      padding: 24rpx;\r\n      \r\n      .section-title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        margin-bottom: 20rpx;\r\n        display: block;\r\n      }\r\n      \r\n      .rules-content {\r\n        .rule-item {\r\n          display: flex;\r\n          margin-bottom: 10rpx;\r\n          \r\n          .rule-label {\r\n            font-size: 28rpx;\r\n            opacity: 0.9;\r\n            margin-right: 20rpx;\r\n            min-width: 150rpx;\r\n          }\r\n          \r\n          .rule-value {\r\n            font-size: 28rpx;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.data-overview-section {\r\n  padding: 30rpx;\r\n  \r\n  .overview-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20rpx;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .overview-card {\r\n      background: #fff;\r\n      border-radius: 16rpx;\r\n      padding: 30rpx;\r\n      text-align: center;\r\n      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n      \r\n      .card-value {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        color: #6366f1;\r\n        display: block;\r\n        margin-bottom: 10rpx;\r\n      }\r\n      \r\n      .card-label {\r\n        font-size: 28rpx;\r\n        color: #6b7280;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.members-section {\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 0 30rpx 30rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .section-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #1f2937;\r\n    }\r\n    \r\n    .header-actions {\r\n      .action-btn {\r\n        font-size: 24rpx;\r\n        padding: 10rpx 20rpx;\r\n        border-radius: 40rpx;\r\n        background: #f3f4f6;\r\n        color: #6b7280;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .table-container {\r\n    width: 100%;\r\n    overflow-x: auto;\r\n    \r\n    .table-header {\r\n      display: flex;\r\n      background: #f3f4f6;\r\n      border-radius: 8rpx 8rpx 0 0;\r\n      \r\n      .th {\r\n        flex: 1;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        font-weight: bold;\r\n        color: #1f2937;\r\n        text-align: center;\r\n      }\r\n    }\r\n    \r\n    .table-body {\r\n      .tr {\r\n        display: flex;\r\n        border-bottom: 2rpx solid #f3f4f6;\r\n        \r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n        \r\n        .td {\r\n          flex: 1;\r\n          padding: 20rpx;\r\n          font-size: 28rpx;\r\n          color: #6b7280;\r\n          text-align: center;\r\n          \r\n          &.amount {\r\n            color: #6366f1;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .pagination {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 30rpx;\r\n    flex-wrap: wrap;\r\n    \r\n    .page-info {\r\n      font-size: 24rpx;\r\n      color: #6b7280;\r\n    }\r\n    \r\n    .page-controls {\r\n      display: flex;\r\n      gap: 10rpx;\r\n      \r\n      .page-btn {\r\n        font-size: 24rpx;\r\n        padding: 10rpx 20rpx;\r\n        border-radius: 40rpx;\r\n        background: #f3f4f6;\r\n        color: #6b7280;\r\n        \r\n        &:disabled {\r\n          opacity: 0.5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.action-section {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 20rpx 30rpx;\r\n  background: #fff;\r\n  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n  z-index: 100;\r\n  \r\n  .primary-btn {\r\n    width: 100%;\r\n    height: 80rpx;\r\n    background: linear-gradient(90deg, #6366f1, #8b5cf6);\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    border-radius: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);\r\n  }\r\n}\r\n\r\n.dividend-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  \r\n  .modal-content {\r\n    width: 80%;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.15);\r\n    \r\n    .modal-header {\r\n      padding: 30rpx;\r\n      border-bottom: 2rpx solid #f3f4f6;\r\n      \r\n      .modal-title {\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n        color: #1f2937;\r\n      }\r\n    }\r\n    \r\n    .modal-body {\r\n      padding: 30rpx;\r\n      \r\n      .modal-text {\r\n        font-size: 28rpx;\r\n        color: #6b7280;\r\n        margin-bottom: 30rpx;\r\n        display: block;\r\n      }\r\n      \r\n      .modal-info {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 20rpx;\r\n        \r\n        .info-label {\r\n          font-size: 28rpx;\r\n          color: #6b7280;\r\n        }\r\n        \r\n        .info-value {\r\n          font-size: 28rpx;\r\n          font-weight: bold;\r\n          color: #1f2937;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .modal-footer {\r\n      display: flex;\r\n      border-top: 2rpx solid #f3f4f6;\r\n      \r\n      button {\r\n        flex: 1;\r\n        height: 100rpx;\r\n        font-size: 32rpx;\r\n        border: none;\r\n        background: transparent;\r\n        \r\n        &.cancel-btn {\r\n          color: #6b7280;\r\n          border-right: 2rpx solid #f3f4f6;\r\n        }\r\n        \r\n        &.confirm-btn {\r\n          color: #6366f1;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=608e267a&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=608e267a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115081895\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}