{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?20d2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?8c77", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?4065", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?d423", "uni-app:///pagesExa/shoumai/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?02fb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/index.vue?f778"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "pagenum", "my", "activeTab", "saleList", "nodata", "loading", "buyData", "buyRMB", "showModal", "showAuditModal", "selectedItem", "pre_url", "content_pic", "assetTypes", "computed", "pageTitle", "emptyStateText", "i18n", "messages", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "$t", "refreshData", "toggleFilter", "getdata", "that", "app", "st", "item", "activeTabFn", "inputData", "amount", "getAssetTypeName", "getStatusText", "getStatusClass", "buyItem", "id", "money", "setTimeout", "openModal", "upVoucher", "openAudit", "showDetail", "cancelOrder", "<PERSON><PERSON><PERSON><PERSON>", "previewImage", "urls", "current"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoP9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;;IAEA;IACAC;MACAC;IACA;IAEA;EACA;EAEAC;IACA;IACAF;EACA;EAEAG;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MACA;MAEAC;MACAC;QAAAC;QAAA/B;QAAAC;MAAA;QACA4B;QAEA;UACAA;UACA;QACA;QAEA;QACA;UACAA;QACA;;QAEA;QACA/B;UACA;UACA;YACAkC;cAAA;YAAA;UACA;YACAA;UACA;;UAEA;UACA;YACAA;UACA;QACA;QAEA;QACA;QACAH;MACA;IACA;IAEA;IACAI;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MAEA;QACA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAJ;QACA;QACA;QACA;MACA;MAEA;QACAA;QACA;QACAK;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAT;QACA;MACA;MAEA;;MAEA;MACAA;QACAA;QACAA;UACAU;UACAC;QACA;UACAX;UAEA;YACAA;YACA;UACA;YACAA;YACAD;;YAEA;YACAa;cACAZ;YACA;UACA;QACA;MACA;IACA;IAEA;IACAa;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAd;IACA;IAEA;IACAe;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAhB;IACA;IAEA;IACAiB;MACA;MAEAjB;QACAA;QACAA;UAAAU;QAAA;UACAV;UAEA;YACAA;UACA;YACAD;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAkB;MACA;MACA;MAEAlB;QACAA;QACAA;UAAAU;UAAAT;QAAA;UACAD;UAEA;YACAA;UACA;YACAA;YACAD;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAoB;MACA;MACA7B;QACA8B;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzmBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/shoumai/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/shoumai/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2e7a2866&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/shoumai/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2e7a2866&\"", "var components\ntry {\n  components = {\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.saleList.length === 0 && !_vm.loading\n  var l0 = _vm.__map(_vm.saleList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getStatusText(item)\n    var m1 = item.asset_name || _vm.getAssetTypeName(item.asset_type)\n    var g1 = (item.ratio * 100).toFixed(0)\n    var g2 = (item.commission * item.ratio).toFixed(2)\n    var m2 = _vm.activeTab === 0 && item.is_author === 0 ? _vm.$t(\"買入\") : null\n    var m3 = _vm.activeTab === 0 && item.is_author === 1 ? _vm.$t(\"取消\") : null\n    var m4 =\n      _vm.activeTab === 1 && item.is_buy === 1 && item.is_voucher === 0\n        ? _vm.$t(\"上傳憑證\")\n        : null\n    var m5 =\n      _vm.activeTab === 1 && item.is_sale === 1 && item.is_voucher === 1\n        ? _vm.$t(\"審核\")\n        : null\n    var m6 = _vm.activeTab === 1 && item.is_buy === 1 ? _vm.$t(\"取消\") : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      g1: g1,\n      g2: g2,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n    }\n  })\n  var m7 = _vm.showModal && _vm.selectedItem ? _vm.$t(\"買入資產\") : null\n  var m8 =\n    _vm.showModal && _vm.selectedItem\n      ? _vm.selectedItem.asset_name ||\n        _vm.getAssetTypeName(_vm.selectedItem.asset_type)\n      : null\n  var g3 =\n    _vm.showModal && _vm.selectedItem\n      ? (_vm.selectedItem.ratio * 100).toFixed(0)\n      : null\n  var g4 = _vm.showModal && _vm.selectedItem ? _vm.buyRMB.toFixed(2) : null\n  var m9 = _vm.showModal && _vm.selectedItem ? _vm.$t(\"取消\") : null\n  var m10 = _vm.showModal && _vm.selectedItem ? _vm.$t(\"確認買入\") : null\n  var m11 =\n    _vm.showAuditModal && _vm.selectedItem\n      ? _vm.selectedItem.asset_name ||\n        _vm.getAssetTypeName(_vm.selectedItem.asset_type)\n      : null\n  var g5 =\n    _vm.showAuditModal && _vm.selectedItem\n      ? (_vm.selectedItem.ratio * _vm.selectedItem.commission).toFixed(2)\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showModal = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showAuditModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m7: m7,\n        m8: m8,\n        g3: g3,\n        g4: g4,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"container\">\r\n        <!-- 页面标题 -->\r\n        <view class=\"page-header\">\r\n            <text class=\"page-title\">{{pageTitle}}</text>\r\n            <view class=\"header-actions\">\r\n                <view class=\"filter-btn\" @tap=\"toggleFilter\">\r\n                    <text class=\"filter-text\">{{ my === 1 ? '查看全部' : '只看我的' }}</text>\r\n                    <text class=\"filter-icon\">{{ my === 1 ? '🔍' : '👤' }}</text>\r\n                </view>\r\n                <view class=\"add-btn\" @tap=\"goto\" data-url=\"/pagesExa/shoumai/sale\">\r\n                    <text class=\"add-icon\">+</text>\r\n                    <text class=\"add-text\">發布</text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        \r\n        <!-- 分段控制器 -->\r\n        <view class=\"segment-control\">\r\n            <view class=\"scroll-container\">\r\n                <view class=\"segment\" :class=\"{active: activeTab === 0}\" @click=\"activeTabFn(0)\">售賣中</view>\r\n                <view class=\"segment\" :class=\"{active: activeTab === 1}\" @click=\"activeTabFn(1)\">交易中</view>\r\n                <view class=\"segment\" :class=\"{active: activeTab === 2}\" @click=\"activeTabFn(2)\">已完成</view>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 列表视图 -->\r\n        <view class=\"tab-content\">\r\n            <!-- 无数据提示 -->\r\n            <view class=\"empty-state\" v-if=\"saleList.length === 0 && !loading\">\r\n                <image class=\"empty-icon\" src=\"/static/img/empty.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"empty-text\">{{ emptyStateText }}</text>\r\n                <view class=\"empty-action\" v-if=\"activeTab === 0 && my === 0\">\r\n                    <text @tap=\"goto\" data-url=\"/pagesExa/shoumai/sale\">立即發布售賣</text>\r\n                </view>\r\n            </view>\r\n            \r\n            <!-- 列表项 -->\r\n            <view class=\"list-item\" v-for=\"(item, index) in saleList\" :key=\"index\">\r\n                <view class=\"list-item-header\">\r\n                    <view class=\"seller-info\">\r\n                        <text class=\"seller-badge\" v-if=\"item.is_author === 1\">我的</text>\r\n                        <text class=\"seller-name\">{{item.is_author === 1 ? '我' : item.nickname}}</text>\r\n                    </view>\r\n                    <text class=\"item-status\" :class=\"{\r\n                        'status-selling': activeTab === 0,\r\n                        'status-canceled': activeTab === 2 && item.status === 3,\r\n                        'status-success': activeTab === 2 && item.status === 1,\r\n                        'status-rejected': activeTab === 2 && item.status === 2,\r\n                        'status-completed': activeTab === 2 && item.status !== 1 && item.status !== 2 && item.status !== 3,\r\n                        'status-pending': activeTab === 1 && item.is_voucher === 0,\r\n                        'status-reviewing': activeTab === 1 && item.is_voucher === 1\r\n                    }\">{{ getStatusText(item) }}</text>\r\n                </view>\r\n\r\n                <view class=\"list-item-content\">\r\n                    <view class=\"asset-info\">\r\n                        <view class=\"asset-type\">\r\n                            <text class=\"asset-label\">{{ item.asset_name || getAssetTypeName(item.asset_type) }}</text>\r\n                        </view>\r\n                        <view class=\"asset-amount\">\r\n                            <text class=\"amount-value\">{{ item.commission }}</text>\r\n                        </view>\r\n                    </view>\r\n                    \r\n                    <view class=\"transaction-details\">\r\n                        <view class=\"detail-row\">\r\n                            <text class=\"detail-label\">折扣率:</text>\r\n                            <text class=\"detail-value\">{{ (item.ratio * 100).toFixed(0) }}%</text>\r\n                        </view>\r\n                        <view class=\"detail-row\">\r\n                            <text class=\"detail-label\">約合人民幣:</text>\r\n                            <text class=\"detail-value price\">¥{{ (item.commission * item.ratio).toFixed(2) }}</text>\r\n                        </view>\r\n                        <view class=\"detail-row\" v-if=\"activeTab === 1 && item.is_sale === 1\">\r\n                            <text class=\"detail-label\">購買人:</text>\r\n                            <text class=\"detail-value\">{{ item.buy_nickname }}</text>\r\n                        </view>\r\n                        <view class=\"detail-row\">\r\n                            <text class=\"detail-label\">發布時間:</text>\r\n                            <text class=\"detail-value time\">{{ item.createtime }}</text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"list-item-footer\">\r\n                    <!-- 售卖中状态的按钮 -->\r\n                    <template v-if=\"activeTab === 0\">\r\n                        <button class=\"action-btn buy-btn\" @click=\"openModal(item)\" v-if=\"item.is_author === 0\">\r\n                            <text class=\"btn-icon\">💰</text>\r\n                            <text class=\"btn-text\">{{$t('買入')}}</text>\r\n                        </button>\r\n                        <button class=\"action-btn cancel-btn\" @click=\"cancelOrder(item, index)\" v-if=\"item.is_author === 1\">\r\n                            <text class=\"btn-icon\">✖</text>\r\n                            <text class=\"btn-text\">{{$t('取消')}}</text>\r\n                        </button>\r\n                    </template>\r\n                    \r\n                    <!-- 交易中状态的按钮 -->\r\n                    <template v-if=\"activeTab === 1\">\r\n                        <button class=\"action-btn upload-btn\" @click=\"upVoucher(item)\" v-if=\"item.is_buy === 1 && item.is_voucher === 0\">\r\n                            <text class=\"btn-icon\">📤</text>\r\n                            <text class=\"btn-text\">{{$t('上傳憑證')}}</text>\r\n                        </button>\r\n                        <button class=\"action-btn audit-btn\" @click=\"openAudit(item)\" v-if=\"item.is_sale === 1 && item.is_voucher === 1\">\r\n                            <text class=\"btn-icon\">✓</text>\r\n                            <text class=\"btn-text\">{{$t('審核')}}</text>\r\n                        </button>\r\n                        <button class=\"action-btn cancel-btn\" @click=\"cancelOrder(item, index)\" v-if=\"item.is_buy === 1\">\r\n                            <text class=\"btn-icon\">✖</text>\r\n                            <text class=\"btn-text\">{{$t('取消')}}</text>\r\n                        </button>\r\n                    </template>\r\n                    \r\n                    <!-- 已完成状态的按钮 -->\r\n                    <template v-if=\"activeTab === 2\">\r\n                        <button class=\"action-btn detail-btn\" @click=\"showDetail(item)\">\r\n                            <text class=\"btn-text\">查看詳情</text>\r\n                        </button>\r\n                    </template>\r\n                </view>\r\n            </view>\r\n            \r\n            <!-- 加载中提示 -->\r\n            <view class=\"loading-more\" v-if=\"loading\">\r\n                <text class=\"loading-text\">加載中...</text>\r\n            </view>\r\n        </view>\r\n\r\n        <!-- 买入模态窗口 -->\r\n        <div class=\"modal\" v-if=\"showModal && selectedItem\">\r\n            <div class=\"modal-content\">\r\n                <view class=\"modal-header\">\r\n                    <text class=\"modal-title\">{{$t('買入資產')}}</text>\r\n                    <text class=\"modal-close\" @click=\"showModal = false\">✕</text>\r\n                </view>\r\n                \r\n                <view class=\"modal-body\">\r\n                    <view class=\"modal-section\">\r\n                        <view class=\"section-title\">賣家信息</view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">賣家:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.nickname }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">資產類型:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.asset_name || getAssetTypeName(selectedItem.asset_type) }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">可買數量:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.commission }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">折扣率:</text>\r\n                            <text class=\"info-value\">{{ (selectedItem.ratio * 100).toFixed(0) }}%</text>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"modal-section\">\r\n                        <view class=\"section-title\">購買信息</view>\r\n                        <view class=\"buy-input-wrapper\">\r\n                            <text class=\"input-label\">購買數量</text>\r\n                            <input \r\n                                type=\"number\" \r\n                                class=\"buy-input\" \r\n                                v-model=\"buyData\"\r\n                                @input=\"inputData\" \r\n                                placeholder=\"請輸入購買數量\"\r\n                            />\r\n                        </view>\r\n                        <view class=\"buy-result\">\r\n                            <text class=\"result-label\">應付金額:</text>\r\n                            <text class=\"result-value\">¥{{ buyRMB.toFixed(2) }}</text>\r\n                        </view>\r\n                        <view class=\"buy-note\">\r\n                            <text>* 提交後請及時上傳支付憑證，確保交易順利完成</text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"modal-footer\">\r\n                    <button class=\"modal-btn cancel\" @click=\"showModal = false\">{{$t('取消')}}</button>\r\n                    <button class=\"modal-btn confirm\" @click=\"buyItem(selectedItem)\">{{$t('確認買入')}}</button>\r\n                </view>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 审核模态窗口 -->\r\n        <div class=\"modal\" v-if=\"showAuditModal && selectedItem\">\r\n            <div class=\"modal-content\">\r\n                <view class=\"modal-header\">\r\n                    <text class=\"modal-title\">審核支付憑證</text>\r\n                    <text class=\"modal-close\" @click=\"showAuditModal = false\">✕</text>\r\n                </view>\r\n\r\n                <view class=\"modal-body\">\r\n                    <view class=\"modal-section\">\r\n                        <view class=\"section-title\">訂單信息</view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">購買人:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.buy_nickname }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">資產類型:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.asset_name || getAssetTypeName(selectedItem.asset_type) }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">交易數量:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.commission }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">應付金額:</text>\r\n                            <text class=\"info-value highlight\">¥{{ (selectedItem.ratio * selectedItem.commission).toFixed(2) }}</text>\r\n                        </view>\r\n                        <view class=\"info-row\">\r\n                            <text class=\"info-label\">發布時間:</text>\r\n                            <text class=\"info-value\">{{ selectedItem.createtime }}</text>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"modal-section\">\r\n                        <view class=\"section-title\">支付憑證</view>\r\n                        <view class=\"voucher-images\">\r\n                            <view v-for=\"(item, index) in selectedItem.buy_voucher\" :key=\"index\" class=\"voucher-image-wrapper\">\r\n                                <image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"aspectFit\" class=\"voucher-image\"></image>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"audit-note\">\r\n                            <text>請務必確認上傳支付憑證的有效性與真實性，支付金額必須與訂單應付金額一致。</text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <view class=\"modal-footer\">\r\n                    <button class=\"modal-btn reject\" @click=\"postAudit(selectedItem, 2)\">不通過</button>\r\n                    <button class=\"modal-btn approve\" @click=\"postAudit(selectedItem, 1)\">通過</button>\r\n                </view>\r\n            </div>\r\n        </div>\r\n        \r\n        <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    </view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n    data() {\r\n        return {\r\n            opt: {},\r\n            pagenum: 1,\r\n            my: 0,\r\n            activeTab: 0,\r\n            saleList: [],\r\n            nodata: false,\r\n            loading: false,\r\n            \r\n            // 购买相关数据\r\n            buyData: 0,\r\n            buyRMB: 0,\r\n            \r\n            // 弹窗相关\r\n            showModal: false,\r\n            showAuditModal: false,\r\n            selectedItem: null,\r\n            \r\n            // 资源相关\r\n            pre_url: app.globalData.pre_url,\r\n            content_pic: [],\r\n            \r\n            // 资产类型映射\r\n            assetTypes: {\r\n                'money': '余額',\r\n                'score': '積分',\r\n                'commission': '佣金',\r\n                'heiscore': '現金券',\r\n                'contribution': '貢獻值',\r\n                'score_huangjin': '黃積分'\r\n            }\r\n        }\r\n    },\r\n    \r\n    computed: {\r\n        // 页面标题\r\n        pageTitle() {\r\n            const titles = ['售賣列表', '交易進行中', '歷史交易'];\r\n            return titles[this.activeTab] || '資產售賣';\r\n        },\r\n        \r\n        // 空状态提示文字\r\n        emptyStateText() {\r\n            if (this.activeTab === 0) {\r\n                return this.my === 1 ? '您還沒有發布任何售賣' : '暫無可購買的資產';\r\n            } else if (this.activeTab === 1) {\r\n                return '暫無進行中的交易';\r\n            } else {\r\n                return '暫無已完成的交易';\r\n            }\r\n        }\r\n    },\r\n    \r\n    i18n: {\r\n        messages: {\r\n            'zh-CN': {\r\n                '售賣中': '售賣中',\r\n                '交易中': '交易中',\r\n                '已完成': '已完成', \r\n                '買入': '買入',\r\n                '買入資產': '買入資產',\r\n                '取消': '取消',\r\n                '上傳憑證': '上傳憑證',\r\n                '審核': '審核',\r\n                '確認買入': '確認買入'\r\n            }\r\n        }\r\n    },\r\n    \r\n    onLoad: function(opt) {\r\n        this.opt = app.getopts(opt);\r\n        if (this.opt.my) {\r\n            this.my = parseInt(opt.my);\r\n        }\r\n        \r\n        // 设置页面标题\r\n        uni.setNavigationBarTitle({\r\n            title: '資產售賣'\r\n        });\r\n        \r\n        this.getdata();\r\n    },\r\n    \r\n    onPullDownRefresh: function() {\r\n        this.refreshData();\r\n        uni.stopPullDownRefresh();\r\n    },\r\n    \r\n    onReachBottom: function() {\r\n        if (!this.nodata && !this.loading) {\r\n            this.pagenum = this.pagenum + 1;\r\n            this.getdata(true);\r\n        }\r\n    },\r\n    \r\n    methods: {\r\n        // 国际化辅助函数\r\n        $t(key) {\r\n            if(this.$i18n && this.$i18n.t) {\r\n                return this.$i18n.t(key);\r\n            }\r\n            return key;\r\n        },\r\n        \r\n        // 刷新数据\r\n        refreshData() {\r\n            this.pagenum = 1;\r\n            this.saleList = [];\r\n            this.nodata = false;\r\n            this.getdata();\r\n        },\r\n        \r\n        // 切换过滤状态（我的/全部）\r\n        toggleFilter() {\r\n            this.my = this.my === 1 ? 0 : 1;\r\n            this.refreshData();\r\n        },\r\n        \r\n        // 获取数据\r\n        getdata: function(loadmore) {\r\n            if (!loadmore) {\r\n                this.pagenum = 1;\r\n                this.saleList = [];\r\n                this.nodata = false;\r\n            }\r\n            \r\n            var that = this;\r\n            var pagenum = that.pagenum;\r\n            var st = that.activeTab;\r\n            \r\n            that.loading = true;\r\n            app.get('ApiShoumai/getList', { st: st, pagenum: pagenum, my: that.my }, function(res) {\r\n                that.loading = false;\r\n                \r\n                if (!res || !res.datalist) {\r\n                    that.nodata = true;\r\n                    return;\r\n                }\r\n                \r\n                var data = res.datalist;\r\n                if (data.length == 0) {\r\n                    that.nodata = true;\r\n                }\r\n                \r\n                // 处理数据\r\n                data.forEach(item => {\r\n                    // 处理支付凭证字符串转数组\r\n                    if (item.buy_voucher && typeof item.buy_voucher === 'string') {\r\n                        item.buy_voucher = item.buy_voucher.split(',').filter(url => url);\r\n                    } else if (!item.buy_voucher) {\r\n                        item.buy_voucher = [];\r\n                    }\r\n                    \r\n                    // 如果资产类型为空，默认为佣金\r\n                    if (!item.asset_type) {\r\n                        item.asset_type = 'commission';\r\n                    }\r\n                });\r\n                \r\n                var datalist = that.saleList;\r\n                var newdata = datalist.concat(data);\r\n                that.saleList = newdata;\r\n            });\r\n        },\r\n        \r\n        // 切换分段控制器\r\n        activeTabFn(index) {\r\n            if (this.activeTab !== index) {\r\n                this.activeTab = index;\r\n                this.refreshData();\r\n            }\r\n        },\r\n        \r\n        // 金额输入处理\r\n        inputData(e) {\r\n            if (!this.selectedItem) return;\r\n            \r\n            let value = e.detail ? e.detail.value : e.target.value;\r\n            let maxAmount = parseFloat(this.selectedItem.commission);\r\n            \r\n            if (value === '' || isNaN(value)) {\r\n                this.buyData = '';\r\n                this.buyRMB = 0;\r\n                return;\r\n            }\r\n            \r\n            // 转换为数字并验证\r\n            let amount = parseFloat(value);\r\n            \r\n            // 验证金额\r\n            if (amount <= 0) {\r\n                app.error('購買數量必須大於0');\r\n                this.buyData = '';\r\n                this.buyRMB = 0;\r\n                return;\r\n            }\r\n            \r\n            if (amount > maxAmount) {\r\n                app.error('超出可購買數量');\r\n                this.buyData = maxAmount;\r\n                amount = maxAmount;\r\n            } else {\r\n                this.buyData = amount;\r\n            }\r\n            \r\n            // 计算应付金额\r\n            this.buyRMB = parseFloat((amount * this.selectedItem.ratio).toFixed(2));\r\n        },\r\n        \r\n        // 获取资产类型名称\r\n        getAssetTypeName(type) {\r\n            return this.assetTypes[type] || '佣金';\r\n        },\r\n        \r\n        // 获取状态文本\r\n        getStatusText(item) {\r\n            if (this.activeTab === 0) {\r\n                return '售賣中';\r\n            } else if (this.activeTab === 2) {\r\n                if (item.status === 3) {\r\n                    return '已取消';\r\n                } else if (item.status === 1) {\r\n                    return '交易成功';\r\n                } else if (item.status === 2) {\r\n                    return '審核未通過';\r\n                } else {\r\n                    return '已完成';\r\n                }\r\n            } else {\r\n                if (item.is_buy === 1 && item.is_voucher === 0) {\r\n                    return '待上傳憑證';\r\n                } else if (item.is_sale === 1 && item.is_voucher === 1) {\r\n                    return '待審核';\r\n                } else {\r\n                    return '交易中';\r\n                }\r\n            }\r\n        },\r\n        \r\n        // 获取状态样式类\r\n        getStatusClass(item) {\r\n            if (this.activeTab === 0) {\r\n                return 'status-selling';\r\n            } else if (this.activeTab === 2) {\r\n                if (item.status === 3) {\r\n                    return 'status-canceled';\r\n                } else if (item.status === 1) {\r\n                    return 'status-success';\r\n                } else if (item.status === 2) {\r\n                    return 'status-rejected';\r\n                } else {\r\n                    return 'status-completed';\r\n                }\r\n            } else {\r\n                if (item.is_voucher === 0) {\r\n                    return 'status-pending';\r\n                } else {\r\n                    return 'status-reviewing';\r\n                }\r\n            }\r\n        },\r\n        \r\n        // 购买操作\r\n        buyItem(item) {\r\n            if (!this.buyData || parseFloat(this.buyData) <= 0) {\r\n                app.error('請輸入購買數量');\r\n                return;\r\n            }\r\n            \r\n            var that = this;\r\n            \r\n            // 二次确认\r\n            app.confirm(`確認購買${this.buyData}${item.asset_name || this.getAssetTypeName(item.asset_type)}嗎？應付金額${this.buyRMB.toFixed(2)}元`, function() {\r\n                app.showLoading('提交中');\r\n                app.post('ApiShoumai/postBuy', { \r\n                    id: item.id, \r\n                    money: that.buyData \r\n                }, function(data) {\r\n                    app.showLoading(false);\r\n                    \r\n                    if (data.status == 0) {\r\n                        app.error(data.msg);\r\n                        return;\r\n                    } else {\r\n                        app.success(data.msg);\r\n                        that.showModal = false;\r\n                        \r\n                        // 跳转到凭证上传页面\r\n                        setTimeout(function() {\r\n                            app.goto(\"/pagesExa/shoumai/voucher?id=\" + data.id);\r\n                        }, 1000);\r\n                    }\r\n                });\r\n            });\r\n        },\r\n        \r\n        // 打开购买模态框\r\n        openModal(item) {\r\n            this.selectedItem = item;\r\n            this.buyData = \"\";\r\n            this.buyRMB = 0;\r\n            this.showModal = true;\r\n        },\r\n        \r\n        // 上传凭证\r\n        upVoucher(item) {\r\n            app.goto(\"/pagesExa/shoumai/voucher?id=\" + item.id);\r\n        },\r\n        \r\n        // 打开审核模态框\r\n        openAudit(item) {\r\n            this.selectedItem = item;\r\n            this.showAuditModal = true;\r\n        },\r\n        \r\n        // 查看交易详情\r\n        showDetail(item) {\r\n            // 如果需要查看详情，可以跳转到详情页或者打开详情模态框\r\n            app.goto(\"/pagesExa/shoumai/detail?id=\" + item.id);\r\n        },\r\n        \r\n        // 取消订单\r\n        cancelOrder(item, index) {\r\n            var that = this;\r\n            \r\n            app.confirm('確定要取消該訂單嗎?', function () {\r\n                app.showLoading('处理中');\r\n                app.post('ApiShoumai/cancelOrder', {id: item.id}, function (data) {\r\n                    app.showLoading(false);\r\n                    \r\n                    if (data.status == 0) {\r\n                        app.error(data.msg);\r\n                    } else {\r\n                        that.saleList.splice(index, 1);\r\n                        app.success(data.msg);\r\n                    }\r\n                });\r\n            });\r\n        },\r\n        \r\n        // 提交审核结果\r\n        postAudit(item, st) {\r\n            var that = this;\r\n            var msg = st == 1 ? '是否確認通過審核?' : '是否確認不通過審核?';\r\n            \r\n            app.confirm(msg, function () {\r\n                app.showLoading('处理中');\r\n                app.post('ApiShoumai/postAudit', {id: item.id, st: st}, function (data) {\r\n                    app.showLoading(false);\r\n                    \r\n                    if (data.status == 0) {\r\n                        app.error(data.msg);\r\n                    } else {\r\n                        app.success(data.msg);\r\n                        that.showAuditModal = false;\r\n                        that.refreshData();\r\n                    }\r\n                });\r\n            });\r\n        },\r\n        \r\n        // 预览图片\r\n        previewImage(e) {\r\n            var url = e.currentTarget.dataset.url;\r\n            uni.previewImage({\r\n                urls: [url],\r\n                current: url\r\n            });\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 页面容器 */\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题 */\r\n.page-header {\r\n  padding: 30rpx 30rpx 10rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fff;\r\n}\r\n\r\n.page-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.filter-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #f0f2f5;\r\n  padding: 12rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.filter-text {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.filter-icon {\r\n  font-size: 26rpx;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #533CD7;\r\n  padding: 12rpx 24rpx;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.add-icon {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  margin-right: 6rpx;\r\n}\r\n\r\n.add-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n}\r\n\r\n/* 分段控制器 */\r\n.segment-control {\r\n  width: 100%;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #fff;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.scroll-container {\r\n  display: flex;\r\n  background: #f0f2f5;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.segment {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.segment.active {\r\n  background-color: #533CD7;\r\n  color: #fff;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 内容区域 */\r\n.tab-content {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 20rpx;\r\n}\r\n\r\n.empty-icon {\r\n  width: 180rpx;\r\n  height: 180rpx;\r\n  margin-bottom: 30rpx;\r\n  opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-action {\r\n  background: #533CD7;\r\n  color: #fff;\r\n  padding: 16rpx 40rpx;\r\n  border-radius: 40rpx;\r\n  font-size: 26rpx;\r\n}\r\n\r\n/* 列表项 */\r\n.list-item {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.list-item-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 16rpx;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.seller-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.seller-badge {\r\n  background: #e8f0fe;\r\n  color: #533CD7;\r\n  font-size: 22rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.seller-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.item-status {\r\n  font-size: 24rpx;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-selling {\r\n  background: #e5f7ff;\r\n  color: #0587f0;\r\n}\r\n\r\n.status-pending {\r\n  background: #fff5e5;\r\n  color: #ff9500;\r\n}\r\n\r\n.status-reviewing {\r\n  background: #e6f7f2;\r\n  color: #10b981;\r\n}\r\n\r\n.status-success {\r\n  background: #e6f7f2;\r\n  color: #10b981;\r\n}\r\n\r\n.status-canceled {\r\n  background: #f1f1f1;\r\n  color: #999;\r\n}\r\n\r\n.status-rejected {\r\n  background: #ffefef;\r\n  color: #ff4d4f;\r\n}\r\n\r\n.status-completed {\r\n  background: #e8f0fe;\r\n  color: #533CD7;\r\n}\r\n\r\n/* 列表内容 */\r\n.list-item-content {\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.asset-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.asset-type {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.asset-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  background: #f5f5f5;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.asset-amount {\r\n  flex: 1;\r\n}\r\n\r\n.amount-value {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.transaction-details {\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.detail-row {\r\n  display: flex;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.detail-label {\r\n  width: 160rpx;\r\n  font-size: 26rpx;\r\n  color: #888;\r\n}\r\n\r\n.detail-value {\r\n  flex: 1;\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n.detail-value.price {\r\n  color: #ff6b6b;\r\n  font-weight: 500;\r\n}\r\n\r\n.detail-value.time {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n\r\n/* 列表底部 */\r\n.list-item-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding-top: 16rpx;\r\n  border-top: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 64rpx;\r\n  padding: 0 24rpx;\r\n  border-radius: 32rpx;\r\n  font-size: 26rpx;\r\n  margin-left: 16rpx;\r\n  border: none;\r\n}\r\n\r\n.btn-icon {\r\n  margin-right: 8rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.buy-btn {\r\n  background: #533CD7;\r\n  color: #fff;\r\n}\r\n\r\n.cancel-btn {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n.upload-btn {\r\n  background: #0587f0;\r\n  color: #fff;\r\n}\r\n\r\n.audit-btn {\r\n  background: #10b981;\r\n  color: #fff;\r\n}\r\n\r\n.detail-btn {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n/* 加载更多 */\r\n.loading-more {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 模态窗口 */\r\n.modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  z-index: 100;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-content {\r\n  width: 90%;\r\n  max-width: 600rpx;\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\r\n  overflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n  position: relative;\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.modal-close {\r\n  position: absolute;\r\n  right: 30rpx;\r\n  top: 30rpx;\r\n  font-size: 32rpx;\r\n  color: #999;\r\n}\r\n\r\n.modal-body {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.modal-section {\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.modal-section:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.info-label {\r\n  width: 160rpx;\r\n  font-size: 26rpx;\r\n  color: #888;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n.info-value.highlight {\r\n  color: #ff6b6b;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 购买输入 */\r\n.buy-input-wrapper {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.input-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.buy-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1rpx solid #e0e0e0;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.buy-result {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 20rpx 0;\r\n}\r\n\r\n.result-label {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.result-value {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.buy-note {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  line-height: 1.5;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n/* 凭证图片 */\r\n.voucher-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.voucher-image-wrapper {\r\n  width: 180rpx;\r\n  height: 180rpx;\r\n  margin: 0 20rpx 20rpx 0;\r\n  background: #f5f5f5;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.voucher-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.audit-note {\r\n  padding: 16rpx;\r\n  background: #fff9e6;\r\n  border-radius: 8rpx;\r\n  font-size: 24rpx;\r\n  color: #f59942;\r\n  line-height: 1.5;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  display: flex;\r\n  padding: 20rpx 30rpx;\r\n  border-top: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.modal-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  border: none;\r\n}\r\n\r\n.modal-btn.cancel {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.modal-btn.confirm {\r\n  background: #533CD7;\r\n  color: #fff;\r\n}\r\n\r\n.modal-btn.reject {\r\n  background: #ff6b6b;\r\n  color: #fff;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.modal-btn.approve {\r\n  background: #10b981;\r\n  color: #fff;\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061092\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}