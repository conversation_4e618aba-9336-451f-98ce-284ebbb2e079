require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/order/orderzhangdan"],{"4b68":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return n}));var n={nomore:function(){return o.e("components/nomore/nomore").then(o.bind(null,"3892"))},nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},uniPopup:function(){return Promise.all([o.e("common/vendor"),o.e("components/uni-popup/uni-popup")]).then(o.bind(null,"ca44a"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))},uniPopupDialog:function(){return o.e("components/uni-popup-dialog/uni-popup-dialog").then(o.bind(null,"267c"))}},i=function(){var t=this.$createElement;this._self._c},a=[]},"5f87":function(t,e,o){},ab6d:function(t,e,o){"use strict";o.r(e);var n=o("e531"),i=o.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a},c809:function(t,e,o){"use strict";var n=o("5f87"),i=o.n(n);i.a},db8e:function(t,e,o){"use strict";o.r(e);var n=o("4b68"),i=o("ab6d");for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);o("c809");var s=o("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports},e531:function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:"all",datalist:[],pagenum:1,nomore:!1,nodata:!1,codtxt:"",canrefund:1,express_content:"",selectExpressShow:!1,hexiao_qr:"",keyword:"",is_more:0,cart_info:[],loop_point:0,loop_length:0,billTypes:["年度账单","月度账单"],billType:"",billTypeLabel:"请选择账单类型",years:[],months:[],selectedYear:"",selectedYearLabel:"请选择年份",selectedMonth:"",selectedMonthLabel:"请选择月份",totalAmount:null,orderCount:null,showBillSummary:!1}},onLoad:function(t){this.opt=o.getopts(t),this.opt&&this.opt.st&&(this.st=this.opt.st),this.getdata();for(var e=(new Date).getFullYear(),n=0;n<5;n++)this.years.push(e-n+"年");for(var i=1;i<=12;i++)this.months.push(i+"月")},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},onNavigationBarSearchInputConfirmed:function(t){this.searchConfirm({detail:{value:t.text}})},methods:{onBillTypeChange:function(t){var e=t.detail.value;this.billType=["annual","monthly"][e],this.billTypeLabel=this.billTypes[e],this.selectedYear="",this.selectedYearLabel="请选择年份",this.selectedMonth="",this.selectedMonthLabel="请选择月份",this.totalAmount=null,this.orderCount=null,this.showBillSummary=!1,this.getdata(!1)},onYearChange:function(t){var e=t.detail.value;this.selectedYearLabel=this.years[e],this.selectedYear=this.years[e].replace("年",""),this.showBillSummary=!0,this.getdata(!1)},onMonthChange:function(t){var e=t.detail.value;this.selectedMonthLabel=this.months[e],this.selectedMonth=(e+1).toString().padStart(2,"0"),this.showBillSummary=!0,this.getdata(!1)},add_cart:function(e){var n=this;o.post("ApiShop/addcart",{ggid:e["ggid"],num:e["num"],proid:e["id"],tid:0,glass_record_id:0},(function(e){if(1==e.status){if(n.loop_point==n.loop_length-1)return void t.redirectTo({url:"/shopPackage/shop/cart"});n.loop_point++,n.add_cart(n.cart_info[n.loop_point])}}))},more_one:function(t){for(var e=t.target.dataset,o=[],n=e["key"],i=this.datalist[n],a=i["prolist"],s=0;s<a.length;s++){var r={};r["id"]=a[s]["proid"],r["num"]=a[s]["num"],r["ggid"]=a[s]["ggid"],o.push(r)}this.loop_length=o.length,this.loop_point=0,this.cart_info=o,this.add_cart(o[0])},confirm:function(e,o){var n=this.more_data,i=o,a=this;""!=i&&0!=i||t.showModal({title:"提示",content:"请输入数量",success:function(){a.$refs.more_one.close()}});var s="/shopPackage/shop/buy?prodata="+n["id"]+","+n["ggid"]+","+i;t.redirectTo({url:s}),this.$refs.more_one.close()},close:function(){this.$refs.more_one.close()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var e=this,n=e.pagenum,i=e.st;e.nodata=!1,e.nomore=!1,e.loading=!0;var a={st:i,pagenum:n,keyword:e.keyword};"annual"===e.billType&&e.selectedYear?a.year=e.selectedYear:"monthly"===e.billType&&e.selectedYear&&e.selectedMonth&&(a.year=e.selectedYear,a.month=e.selectedMonth),o.post("ApiOrder/orderzhangdan",a,(function(t){e.loading=!1;var o=t.datalist;if(1==n)e.codtxt=t.codtxt,e.canrefund=t.canrefund,e.datalist=o,0==o.length&&(e.nodata=!0),e.loaded();else if(0==o.length)e.nomore=!0;else{var i=e.datalist,a=i.concat(o);e.datalist=a}void 0!==t.totalAmount&&(e.totalAmount=t.totalAmount),void 0!==t.orderCount&&(e.orderCount=t.orderCount)}))},changetab:function(e){this.st=e,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},toclose:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要关闭该订单吗?",(function(){o.showLoading("提交中"),o.post("ApiOrder/closeOrder",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},tuikuang:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定退框吗?",(function(){o.showLoading("退框中"),o.post("ApiOrder/delOrders",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},todel:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要删除该订单吗?",(function(){o.showLoading("删除中"),o.post("ApiOrder/delOrder",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},orderCollect:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要收货吗?",(function(){o.showLoading("提交中"),o.post("ApiOrder/orderCollect",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},logistics:function(t){var e=t.currentTarget.dataset.index,n=this.datalist[e],i=n.express_com,a=n.express_no,s=n.express_content,r=n.express_type,l=n.prolist;if(console.log(s),s){for(var d in s=JSON.parse(s),s)if(s[d].express_ogids){var c=s[d].express_ogids.split(",");console.log(c);var u=[];for(var h in l)o.inArray(l[h].id+"",c)&&u.push(l[h]);s[d].express_oglist=u}this.express_content=s,console.log(s),this.$refs.dialogSelectExpress.open()}else o.goto("/pagesExt/order/logistics?express_com="+i+"&express_no="+a+"&type="+r)},hideSelectExpressDialog:function(){this.$refs.dialogSelectExpress.close()},showhxqr:function(t){this.hexiao_qr=t.currentTarget.dataset.hexiao_qr,this.$refs.dialogHxqr.open()},closeHxqr:function(){this.$refs.dialogHxqr.close()},searchConfirm:function(t){this.keyword=t.detail.value,this.getdata(!1)}}};e.default=n}).call(this,o("df3c")["default"])},f742:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var i=n(o("db8e"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])}},[["f742","common/runtime","common/vendor"]]]);