{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?187c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?af98", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?7608", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?9fd2", "uni-app:///pagesExt/tuozhanyuan/shiyebutongji.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?359c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shiyebutongji.vue?3c73"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "field", "order", "oldcid", "<PERSON>ecid", "longitude", "latitude", "clist", "datalist", "pagenum", "keyword", "cid", "nomore", "nodata", "types", "showfilter", "showtype", "buydialogShow", "proid", "member", "member<PERSON><PERSON><PERSON>", "business", "businessxiaoliang", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "ming<PERSON><PERSON>", "getDataList", "uni", "showDrawer", "console", "closeDrawer", "change", "cateClick", "filterConfirm", "filterReset", "filterClick", "changetab", "search", "sortClick", "name", "scale", "phoneNumber", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2GtxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MACAC;QACA;QACA;QACAD;QACAA;QACAA;MACA,GACA;QACAA;MACA;IACA;IACAG;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAH;MACAA;MACAA;MACAC;QAAApB;QAAAE;QAAAV;QAAAC;QAAAG;QAAAC;QAAAI;MAAA;QACAkB;QACAI;QACA,qBACA;UACAH;YACAA;UACA;UACA;QACA;QACA;QACA;UACAD;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAK;MACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAF;MACA;IACA;IACAG;MACA;MACA;MACAT;IACA;IACAU;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAb;MACAA;MACAA;MACAA;IACA;IACAc;MACA;MACA;MACAd;MACAA;MACAA;MACAA;IACA;IACAe;MACA;MACA;MACAf;MACAA;MACAA;IACA;EAAA,0DACA;IACA;IACA;IACAA;EACA,4DACA;IACA;IACA;IACA;IACA;IACAI;MACA1B;MACAD;MACAuC;MACAC;IACA;EACA,qDACA;IACA;IACAb;MACAc;MACAC,uBACA;IACA;EACA,+DACA;IACA;MACA;IACA;IACA;IACAb;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/shiyebutongji.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/shiyebutongji.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shiyebutongji.vue?vue&type=template&id=0854e8a5&\"\nvar renderjs\nimport script from \"./shiyebutongji.vue?vue&type=script&lang=js&\"\nexport * from \"./shiyebutongji.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shiyebutongji.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/shiyebutongji.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shiyebutongji.vue?vue&type=template&id=0854e8a5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.memberxiaoliang.day.toFixed(2) : null\n  var g1 = _vm.isload ? _vm.memberxiaoliang.zuo.toFixed(2) : null\n  var g2 = _vm.isload ? _vm.memberxiaoliang.month.toFixed(2) : null\n  var g3 = _vm.isload ? _vm.memberxiaoliang.zongji.toFixed(2) : null\n  var g4 = _vm.isload ? _vm.businessxiaoliang.day.toFixed(2) : null\n  var g5 = _vm.isload ? _vm.businessxiaoliang.zuo.toFixed(2) : null\n  var g6 = _vm.isload ? _vm.businessxiaoliang.month.toFixed(2) : null\n  var g7 = _vm.isload ? _vm.businessxiaoliang.zongji.toFixed(2) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shiyebutongji.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shiyebutongji.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<!-- 用户推广统计卡片 -->\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">用户推广统计</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"stats-grid\">\r\n\t\t\t\t\t<!-- 新增数据统计 -->\r\n\t\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{member.day}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">今日新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{member.zuo}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">昨日新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{member.month}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">本月新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{member.zongji}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">总计</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 销量数据统计 -->\r\n\t\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{memberxiaoliang.day.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">今日销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{memberxiaoliang.zuo.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">昨日销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{memberxiaoliang.month.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">本月销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{memberxiaoliang.zongji.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">总销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 商户推广统计卡片 -->\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">商户推广统计</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"stats-grid\">\r\n\t\t\t\t\t<!-- 新增数据统计 -->\r\n\t\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{business.day}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">今日新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{business.zuo}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">昨日新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{business.month}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">本月新增</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value\">{{business.zongji}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">总计</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 销量数据统计 -->\r\n\t\t\t\t\t<view class=\"stats-row\">\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{businessxiaoliang.day.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">今日销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{businessxiaoliang.zuo.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">昨日销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{businessxiaoliang.month.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">本月销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t\t<text class=\"stats-value highlight\">￥{{businessxiaoliang.zongji.toFixed(2)}}</text>\r\n\t\t\t\t\t\t\t<text class=\"stats-label\">总销量</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      field: 'juli',\r\n\t\t\torder:'asc',\r\n      oldcid: \"\",\r\n      catchecid: \"\",\r\n      longitude: '',\r\n      latitude: '',\r\n\t\t\tclist:[],\r\n      datalist: [],\r\n      pagenum: 1,\r\n      keyword: '',\r\n      cid: '',\r\n      nomore: false,\r\n      nodata: false,\r\n      types: \"\",\r\n      showfilter: \"\",\r\n\t\t\tshowtype:0,\r\n\t\t\tbuydialogShow:false,\r\n\t\t\tproid:0,\r\n\t\t\tmember:{},\r\n\t\t\tmemberxiaoliang:{},\r\n\t\t\tbusiness:{},\r\n\t\t\tbusinessxiaoliang:{},\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.oldcid = this.opt.cid;\r\n\t\tthis.catchecid = this.opt.cid;\r\n\t\tthis.cid = this.opt.cid;\r\n\t\tthis.mingxiid = this.opt.mingxiid;\r\n        if(this.opt.keyword) {\r\n        \tthis.keyword = this.opt.keyword;\r\n        }\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getDataList(true);\r\n    }\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYihuo/mydata', {mingxiid:that.mingxiid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.member= res.member;\r\n\t\t\t\tthat.memberxiaoliang = res.memberxiaoliang;\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.businessxiaoliang = res.businessxiaoliang;\r\n\t\t\t\tthat.showtype = res.showtype || 0;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\tthat.getDataList();\r\n\t\t\t},\r\n\t\t\tfunction () {\r\n\t\t\t\tthat.getDataList();\r\n\t\t\t});\r\n\t\t},\r\n    getDataList: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var latitude = that.latitude;\r\n      var longitude = that.longitude;\r\n      var keyword = that.keyword;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('ApiBusiness/mylist', {pagenum: pagenum,cid: that.cid,field: that.field,order: that.order,longitude: longitude,latitude: latitude,keyword: keyword}, function (res) {\r\n        that.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t    if(res.status == 0)\r\n\t\t{\r\n\t\t\tapp.alert(res.msg, function () {\r\n\t\t\t\tapp.goto('/pages/my/usercenter', 'redirect');\r\n\t\t\t})\r\n\t\t\treturn;\r\n\t\t}\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\t\t// 打开窗口\r\n\t\tshowDrawer(e) {\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.$refs[e].open()\r\n\t\t},\r\n\t\t// 关闭窗口\r\n\t\tcloseDrawer(e) {\r\n\t\t\tthis.$refs[e].close()\r\n\t\t},\r\n\t\t// 抽屉状态发生变化触发\r\n\t\tchange(e, type) {\r\n\t\t\tconsole.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));\r\n\t\t\tthis[type] = e\r\n\t\t},\r\n    cateClick: function (e) {\r\n      var that = this;\r\n      var cid = e.currentTarget.dataset.cid;\r\n      that.catchecid = cid\r\n    },\r\n\t\tfilterConfirm(){\r\n\t\t\tthis.cid = this.catchecid;\r\n\t\t\tthis.gid = this.catchegid;\r\n\t\t\tthis.getDataList();\r\n\t\t\tthis.$refs['showRight'].close()\r\n\t\t},\r\n\t\tfilterReset(){\r\n\t\t\tthis.catchecid = this.oldcid;\r\n\t\t\tthis.catchegid = '';\r\n\t\t},\r\n    filterClick: function () {\r\n      this.showfilter = !this.showfilter\r\n    },\r\n    changetab: function (e) {\r\n      var that = this;\r\n      var cid = e.currentTarget.dataset.cid;\r\n      that.cid = cid\r\n      that.pagenum = 1;\r\n      that.datalist = [];\r\n      that.getDataList();\r\n    },\r\n    search: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword;\r\n\t\t\tthat.pagenum = 1;\r\n      that.datalist = [];\r\n      that.getDataList();\r\n    },\r\n    sortClick: function (e) {\r\n      var that = this;\r\n      var t = e.currentTarget.dataset;\r\n      that.field = t.field;\r\n      that.order = t.order;\r\n      that.getDataList();\r\n    },\r\n    filterClick: function (e) {\r\n      var that = this;\r\n      var types = e.currentTarget.dataset.types;\r\n      that.types = types;\r\n    },\r\n\t\topenLocation:function(e){\r\n\t\t\t//console.log(e)\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t\t})\t\t\r\n\t\t},\r\n\t\tphone:function(e) {\r\n\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: phone,\r\n\t\t\t\tfail: function () {\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\tconsole.log(this.buydialogShow);\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n/* 容器样式 */\r\n.stats-container {\r\n  padding: 24rpx;\r\n  background: #f5f6fa;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-card {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 24rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.card-header {\r\n  margin-bottom: 24rpx;\r\n  padding-bottom: 16rpx;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 统计网格 */\r\n.stats-grid {\r\n  width: 100%;\r\n}\r\n\r\n.stats-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 24rpx;\r\n  padding: 16rpx 0;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.stats-row:last-child {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n/* 统计项 */\r\n.stats-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 0 8rpx;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stats-value.highlight {\r\n  color: #fe2b2e;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shiyebutongji.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shiyebutongji.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115104584\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}