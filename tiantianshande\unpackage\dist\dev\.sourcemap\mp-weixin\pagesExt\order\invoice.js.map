{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?98d9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?0f8c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?37e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?0b0a", "uni-app:///pagesExt/order/invoice.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?f397", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/invoice.vue?bd78"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pics", "detail", "invoice", "invoice_type", "invoice_type_select", "name_type_select", "name_type_personal_disabled", "inputDisabled", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "type", "formSubmit", "formdata", "setTimeout", "changeOrderType", "changeNameType", "back", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoHhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACA;UACAA;UACAA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACAA;QAEAA;QACA;MACA;IACA;;IACAI;MACA;MACA;MACA;MACA;MACA;QACAH;QACA;MACA;MACA;QACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEAA;MACAA;QAAAC;QAAAG;QAAAF;MAAA;QACAF;QACAA;QACA;UACAK;YACA,oBACAL,6DAEAA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACA;QACAP;QACAA;MACA;QACAA;MACA;MACAA;IACA;IACAQ;MACA;MACA;MACAR;IACA;IACAS;MACAC,kBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/invoice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/invoice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./invoice.vue?vue&type=template&id=3e34a4a8&\"\nvar renderjs\nimport script from \"./invoice.vue?vue&type=script&lang=js&\"\nexport * from \"./invoice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./invoice.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/invoice.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=template&id=3e34a4a8&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && !_vm.inputDisabled ? _vm.inArray(1, _vm.invoice_type) : null\n  var m1 =\n    _vm.isload && !_vm.inputDisabled ? _vm.inArray(2, _vm.invoice_type) : null\n  var m2 = _vm.isload && !_vm.invoice ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.invoice && _vm.invoice.status != 1\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">可开票金额</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">发票类型</text>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<block v-if=\"inputDisabled\">\r\n\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.type == 1\">普通发票</text>\r\n\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.type == 2\">增值税专用发票</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeOrderType\" name=\"invoice_type\">\r\n\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(1,invoice_type)\">\r\n\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"invoice_type_select == 1 ? true : false\"></radio>普通发票\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(2,invoice_type)\">\r\n\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"invoice_type_select == 2 ? true : false\"></radio>增值税专用发票\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t </view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">抬头类型</text>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<block v-if=\"inputDisabled\">\r\n\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.name_type == 1\">个人</text>\r\n\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.name_type == 2\">公司</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeNameType\" name=\"name_type\">\r\n\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"name_type_select == 1 ? true : false\" :disabled=\"name_type_personal_disabled ? true : false\"></radio>个人\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"name_type_select == 2 ? true : false\"></radio>公司\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">抬头名称</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"抬头名称\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"invoice_name\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.invoice_name : ''\" ></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"name_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">公司税号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"公司税号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tax_no\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.tax_no : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">注册地址</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册地址\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"address\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.address : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">注册电话</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册电话\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tel\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.tel : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">开户银行</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"开户银行\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_name\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.bank_name : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">银行账号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"银行账号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_account\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.bank_account : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">手机号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票手机号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"mobile\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.mobile : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">邮箱</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票邮箱\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"email\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.email : ''\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"item\" v-if=\"pay_transfer_info.pay_transfer_desc\">\r\n\t\t\t\t\t<text class=\"text-min\">{{pay_transfer_info.pay_transfer_desc}}</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">开票状态</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"invoice\">{{invoice.status_label}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-else>未申请</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice && invoice.check_remark\">\r\n\t\t\t\t\t<text class=\"t1\">审核备注</text>\r\n\t\t\t\t\t<text class=\"t2\">{{invoice.check_remark}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"btn\" v-if=\" !invoice\" form-type=\"submit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t<button class=\"btn\" v-if=\" invoice && invoice.status != 1\" form-type=\"submit\" :style=\"{background:t('color1')}\">修改</button>\r\n\t\t\t<view class=\"btn-a\" @tap=\"back\">返回上一步</view>\r\n\t\t\t<view style=\"padding-top:30rpx\"></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\t\r\n\t\t\t\tpics:[],\r\n\t\t\t\tdetail:{},\r\n\t\t\t\tinvoice:{},\r\n\t\t\t\tinvoice_type:[],\r\n\t\t\t\tinvoice_type_select:1,\r\n\t\t\t\tname_type_select:1,\r\n\t\t\t\tname_type_personal_disabled:false,\r\n\t\t\t\tinputDisabled:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.pre_url = app.globalData.pre_url;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar type = that.opt.type;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiOrder/invoice', {id: that.opt.orderid,type:type}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\tthat.invoice = res.invoice;\r\n\t\t\t\t\tif(res.invoice) {\r\n\t\t\t\t\t\tthat.invoice_type_select = res.invoice.type;\r\n\t\t\t\t\t\tthat.name_type_select = res.invoice.name_type;\r\n\t\t\t\t\t\tif(that.invoice_type_select == 2) {\r\n\t\t\t\t\t\t\tthat.name_type_personal_disabled = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.name_type_personal_disabled = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(res.invoice.status == 1) {\r\n\t\t\t\t\t\t\tthat.inputDisabled = true;\r\n\t\t\t\t\t\t}\r\n \t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(app.inArray(1,res.invoice_type)){\r\n\t\t\t\t\t\t\tthat.invoice_type_select = 1;\r\n\t\t\t\t\t\t}else if(app.inArray(2,res.invoice_type)){\r\n\t\t\t\t\t\t\tthat.invoice_type_select = 2;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.invoice_type = res.invoice_type;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t//\r\n\t\t\t\t});\r\n\t\t\t},\r\n    formSubmit: function (e) {\r\n      var that = this;\r\n      var id = that.opt.orderid;\r\n\t\t\tvar type = that.opt.type;\r\n\t\t\tvar formdata = e.detail.value;\r\n\t\t\tif(formdata.invoice_name == '') {\r\n\t\t\t\tapp.error('请填写抬头名称');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {\r\n\t\t\t\t///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/\r\n\t\t\t\tapp.error('请填写公司税号');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(formdata.invoice_type == 2) {\r\n\t\t\t\tif(formdata.address == '') {\r\n\t\t\t\t\tapp.error('请填写注册地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.tel == '') {\r\n\t\t\t\t\tapp.error('请填写注册电话');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.bank_name == '') {\r\n\t\t\t\t\tapp.error('请填写开户银行');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.bank_account == '') {\r\n\t\t\t\t\tapp.error('请填写银行账号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (formdata.mobile != '') {\r\n\t\t\t\tif(!/^1[3456789]\\d{9}$/.test(formdata.mobile)){\r\n\t\t\t\t\tapp.error(\"手机号码有误，请重填\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (formdata.email != '') {\r\n\t\t\t\tif(!/^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$/.test(formdata.email)){\r\n\t\t\t\t\tapp.error(\"邮箱有误，请重填\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(formdata.mobile == '' && formdata.email == '') {\r\n\t\t\t\tapp.error(\"手机号和邮箱请填写其中一个\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post('ApiOrder/invoice', {id: id,formdata:formdata,type:type}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n        app.alert(res.msg);\r\n        if (res.status == 1) {\r\n         setTimeout(function () {\r\n\t\t\t\t\t if(type == 'shop')\r\n\t\t\t\t\t\tapp.goto('/pagesExt/order/detail?id='+that.detail.id);\r\n\t\t\t\t\t else\r\n\t\t\t\t\t\tapp.goto('/activity/'+type+'/orderdetail?id='+that.detail.id);\r\n         }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tchangeOrderType: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tif(value == 2) {\r\n\t\t\t\tthat.name_type_select = 2;\r\n\t\t\t\tthat.name_type_personal_disabled = true;\r\n\t\t\t} else {\r\n\t\t\t\tthat.name_type_personal_disabled = false;\r\n\t\t\t}\r\n\t\t\tthat.invoice_type_select = value;\r\n\t\t},\r\n\t\tchangeNameType: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tthat.name_type_select = value;\r\n\t\t},\r\n\t\tback:function(e) {\r\n\t\t\tuni.navigateBack({\r\n\t\t\t\t\r\n\t\t\t})\r\n\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n.radio radio{transform: scale(0.8);}\r\n.radio:nth-child(2) { margin-left: 30rpx;}\r\n.btn-a { text-align: center; padding: 30rpx; color: rgb(253, 74, 70);}\r\n.text-min { font-size: 24rpx; color: #999;}\r\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right; font-size: 28rpx;}\r\n.orderinfo .item .red{color:red}\r\n.orderinfo .item .grey{color:grey}\r\n\r\n.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}\r\n.form-item4 .label{ width:150rpx;}\r\n\r\n.form-content{width:94%;margin:16rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff;overflow:hidden}\r\n.form-item{ width:100%;padding: 32rpx 20rpx;}\r\n.form-item .label{ width:100%;height:60rpx;line-height:60rpx}\r\n.form-item .input-item{ width:100%;}\r\n.form-item textarea{ width:100%;height:200rpx;border: 1px #eee solid;padding: 20rpx;}\r\n.form-item input{ width:100%;border: 1px #f5f5f5 solid;padding: 10rpx;height:80rpx}\r\n.form-item .mid{ height:80rpx;line-height:80rpx;padding:0 20rpx;}\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098730\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}