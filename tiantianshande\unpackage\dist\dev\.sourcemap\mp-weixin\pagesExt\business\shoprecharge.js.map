{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?1387", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?befa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?9b0d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?46f5", "uni-app:///pagesExt/business/shoprecharge.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?da27", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/shoprecharge.vue?7913"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pre_url", "opt", "loading", "isload", "menuindex", "bid", "ymid", "hiddenmodalput", "wxpayst", "alipay", "paypwd", "moneypay", "mdlist", "name", "userinfo", "couponList", "couponrid", "coupontype", "usescore", "money", "disprice", "dkmoney", "couponmoney", "paymoney", "mdkey", "couponvisible", "couponkey", "logo", "KeyboardKeys", "keyHidden", "selectmdDialogShow", "onLoad", "app", "uni", "onPullDownRefresh", "methods", "handleHiddenKey", "handleShowKey", "handle<PERSON>ey", "that", "console", "getdata", "latitude", "modalinput", "selectmd", "itemlist", "itemList", "success", "selectmdRadioChange", "hideSelectmdDialog", "scoredk", "inputMoney", "cancel", "calculatePrice", "chooseCoupon", "topay", "charge_s", "mdid", "showCouponList", "handleClickMask", "GetDistance", "Math", "s", "compare"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2KrxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;MACA;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAEAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA,IACAnB,QACA,KADAA;MAEA;MACA;QACA;UACA;UACAoB;QACA;MACA;QACA;QACA;QACAC;QACA;UACAD;QACA;MACA;MACA;IACA;IAGAE;MACA;MACAF;MACAP;QACA3B;MACA;QACAkC;QACA;UACAP;YACAA;UACA;UACA;QACA;QACA;QACA;QACA;QACAO;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAP;YACA;YACA;YACA;YACA;YAEA;cACApB,iEACA8B;YACA;YAEA9B;YACA4B;YACAD;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAN;MACA;QACAN;UACAa;UACAC;YACA;cACAR;YACA;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACAX;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAY;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA;QACAlC;MACA;QACAA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA,wEACAE;QACAA;MACA;MAEA;MACA;MACAE;MACAgB;MACAA;IACA;IACAe;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAhC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;MACA;MACA;MACAC;MACAxB;QACA3B;QACAC;QACAa;QACAH;QACAE;QACA;QACAuC;MACA;QACA;UACAzB;UACA;QACA;QACAA;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA,uGACAC;MACAC;MACAA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACldA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/shoprecharge.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/shoprecharge.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shoprecharge.vue?vue&type=template&id=2344e4c8&\"\nvar renderjs\nimport script from \"./shoprecharge.vue?vue&type=script&lang=js&\"\nexport * from \"./shoprecharge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shoprecharge.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/shoprecharge.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shoprecharge.vue?vue&type=template&id=2344e4c8&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.userinfo.discount > 0 && _vm.userinfo.discount < 10\n      ? _vm.t(\"会员\")\n      : null\n  var m1 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var g0 = _vm.isload ? _vm.couponList.length : null\n  var m2 =\n    _vm.isload && g0 > 0 && !(_vm.couponrid != 0) ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && !(g0 > 0) ? _vm.t(\"优惠券\") : null\n  var m4 =\n    _vm.isload && _vm.userinfo.scoredkmaxpercent > 0 ? _vm.t(\"积分\") : null\n  var m5 =\n    _vm.isload && _vm.userinfo.scoredkmaxpercent > 0 ? _vm.t(\"积分\") : null\n  var m6 = _vm.isload && _vm.keyHidden ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l0 =\n    _vm.isload && _vm.selectmdDialogShow\n      ? _vm.__map(_vm.mdlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m8 = index == _vm.mdkey ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m8: m8,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shoprecharge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shoprecharge.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"container\">\r\n\t\t\t\t<view class=\"header\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"header_icon\" :src=\"logo\"></image>\r\n\t\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t\t<view class=\"header_name\">{{name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"header_shop\">\r\n\t\t\t\t\t\t\t\t<text>选择门店:</text><text @tap=\"selectmd\">{{mdlist[mdkey].name}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"page\">\r\n\t\t\t\t\t<view class=\"page_module flex-y-center\" @click=\"handleShowKey\">\r\n\t\t\t\t\t\t<text class=\"page_tag\">￥</text>\r\n\t\t\t\t\t\t<!-- <input class=\"page_price flex1\" type=\"digit\" @input=\"inputMoney\" placeholder=\"请输入金额\"></input> -->\r\n\t\t\t\t\t\t<view class=\"page_price flex-y-center\">\r\n\t\t\t\t\t\t\t<text v-if=\"keyHidden&&!money\" class=\"page_notice\">请输入金额</text>\r\n\t\t\t\t\t\t\t<text v-if=\"money\">{{ money }}</text>\r\n\t\t\t\t\t\t\t<view v-if=\"!keyHidden\" class=\"page_cursor\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"info-box\">\r\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.discount>0 && userinfo.discount<10\">\r\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>\r\n\t\t\t\t\t\t\t\t<text class=\"f2\" style=\"color: #e94745;\">-￥{{disprice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex-y-center\">\r\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-if=\"couponList.length>0\" @tap=\"showCouponList\" style=\"color:#e94745\">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t\t\t<image class=\"f3\" src=\"/static/img/arrowright.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.scoredkmaxpercent > 0\">\r\n\t\t\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.dkmoney*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">\r\n\t\t\t\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex flex-bt\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">实付金额:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">￥{{paymoney}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view v-if=\"keyHidden\" class=\"op\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去支付</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t<!-- <view class=\"info-box\">\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"t1\">商户名称:</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\" v-if=\"mdlist.length>0\">\r\n\t\t\t\t\t\t<text class=\"t1\">选择门店:</text>\r\n\t\t\t\t\t\t<text class=\"t2\" @tap=\"selectmd\">{{mdlist[mdkey].name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"t1\">支付金额:</text>\r\n\t\t\t\t\t\t<view class=\"t2 flex-y-center\" style=\"justify-content:flex-end\"><input type=\"digit\" @input=\"inputMoney\" value=\"\" placeholder=\"请输入金额\"></input> 元</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<!-- <view class=\"info-box\">\r\n\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.discount>0 && userinfo.discount<10\">\r\n\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>\r\n\t\t\t\t\t\t<text class=\"f2\" style=\"color: #e94745;\">-￥{{disprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dkdiv-item flex\">\r\n\t\t\t\t\t\t<text class=\"f1\">{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<text class=\"f2\" @tap=\"showCouponList\"\r\n\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image class=\"f3\" src=\"/static/img/arrowright.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"dkdiv-item flex\">\r\n\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.dkmoney*1}}</text> 元</text>\r\n\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<!-- <view class=\"info-box\">\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"t1\">实付金额:</text>\r\n\t\t\t\t\t\t<text class=\"t2\">￥{{paymoney}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<!-- <view class=\"op\">\r\n\t\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去支付</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponrid\"\r\n\t\t\t\t\t\t\t@chooseCoupon=\"chooseCoupon\"></couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"!keyHidden\" class=\"keyboard_page\">\r\n\t\t\t\t<view @click=\"handleHiddenKey\" class=\"keyboard_none\"></view>\r\n\t\t\t\t<view class=\"keyboard_key hind_box\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t\t<image @click=\"handleHiddenKey\" class=\"key-down\" :src=\"pre_url+'/static/img/pack_up.png'\" mode=\"\"></image>\r\n\t\t\t\t\t<view class=\"key-box\">\r\n\t\t\t\t\t\t<view class=\"number-box clearfix\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item,index) in KeyboardKeys\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t:class=\"index === 9 ? 'key key-zero' : 'key'\" hover-class=\"number-box-hover\"\r\n\t\t\t\t\t\t\t\t@click=\"handleKey(item)\">{{item}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"btn-box\">\r\n\t\t\t\t\t\t\t<!-- TODO: 需要替换成删除icon -->\r\n\t\t\t\t\t\t\t<view class=\"key\" hover-class=\"number-box-hover\" data-key=\"X\" @click=\"handleKey('X')\">×\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view :class=\"money ? 'key pay_btn' : 'key pay_btn pay-btn-display'\"\r\n\t\t\t\t\t\t\t\thover-class=\"pay-btn-hover\" @tap=\"topay\">付款</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"selectmdDialogShow\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelectmdDialog\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择门店</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelectmdDialog\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in mdlist\" :key=\"index\" @tap=\"selectmdRadioChange\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-right:10rpx\">{{item.juli ? ' 距离:' + item.juli + '千米' : ''}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"index==mdkey ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tbid: 0,\r\n                ymid: 0,\r\n\t\t\t\thiddenmodalput: true,\r\n\t\t\t\twxpayst: '',\r\n\t\t\t\talipay: '',\r\n\t\t\t\tpaypwd: '',\r\n\t\t\t\tmoneypay: '',\r\n\t\t\t\tmdlist: \"\",\r\n\t\t\t\tname: \"\",\r\n\t\t\t\tuserinfo: \"\",\r\n\t\t\t\tcouponList: [],\r\n\t\t\t\tcouponrid: 0,\r\n\t\t\t\tcoupontype: 1,\r\n\t\t\t\tusescore: 0,\r\n\t\t\t\tmoney: '',\r\n\t\t\t\tdisprice: 0,\r\n\t\t\t\tdkmoney: 0,\r\n\t\t\t\tcouponmoney: 0,\r\n\t\t\t\tpaymoney: 0,\r\n\t\t\t\tmdkey: 0,\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tcouponkey: 0,\r\n\t\t\t\tlogo:\"\",\r\n\r\n\t\t\t\tKeyboardKeys: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.'],\r\n\t\t\t\tkeyHidden: false,\r\n\t\t\t\tselectmdDialogShow: false,\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.bid = this.opt.bid || 0;\r\n            if(this.opt.ymid){\r\n                this.ymid          = this.opt.ymid;\r\n                app.globalData.pid = this.opt.ymid;\r\n                uni.setStorageSync('pid', this.opt.ymid);\r\n            }\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t\thandleHiddenKey() {\r\n\t\t\t\tthis.keyHidden = true;\r\n\t\t\t},\r\n\t\t\t// 显示键盘\r\n\t\t\thandleShowKey() {\r\n\t\t\t\tthis.keyHidden = false;\r\n\t\t\t},\r\n\t\t\t// 键盘输入\r\n\t\t\thandleKey(key) {\r\n\t\t\t\tconst that = this\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmoney\r\n\t\t\t\t} = this\r\n\t\t\t\t// 删除金额\r\n\t\t\t\tif (key === 'X') {\r\n\t\t\t\t\tif (money !== '') {\r\n\t\t\t\t\t\tconst payMoney = money.slice(0, money.length - 1)\r\n\t\t\t\t\t\tthat.money = payMoney\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 添加金额\r\n\t\t\t\t\tconst payMoney = money + key\r\n\t\t\t\t\tconsole.log(/^(\\d+\\.?\\d{0,2})$/.test(payMoney), payMoney, 'payMoney')\r\n\t\t\t\t\tif (/^(\\d+\\.?\\d{0,2})$/.test(payMoney)) {\r\n\t\t\t\t\t\tthat.money = payMoney\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\r\n\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this; //获取产品信息\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiMaidan/maidan', {\r\n\t\t\t\t\tbid: that.bid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar userinfo = res.userinfo;\r\n\t\t\t\t\tvar couponList = res.couponList;\r\n\t\t\t\t\tvar mdlist = res.mdlist;\r\n\t\t\t\t\tthat.wxpayst = res.wxpayst;\r\n\t\t\t\t\tthat.alipay = res.alipay;\r\n\t\t\t\t\tthat.couponList = res.couponList;\r\n\t\t\t\t\tthat.mdlist = res.mdlist;\r\n\t\t\t\t\tthat.moneypay = res.moneypay;\r\n\t\t\t\t\tthat.name = res.name;\r\n\t\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\t\tthat.logo = res.logo;\r\n\t\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\t\tif (mdlist.length > 0) {\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t\t\tvar speed = res.speed;\r\n\t\t\t\t\t\t\tvar accuracy = res.accuracy;\r\n\r\n\t\t\t\t\t\t\tfor (var i in mdlist) {\r\n\t\t\t\t\t\t\t\tmdlist[i].juli = that.GetDistance(latitude, longitude, mdlist[i]\r\n\t\t\t\t\t\t\t\t\t.latitude, mdlist[i].longitude);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tmdlist = mdlist.sort(that.compare('juli'));\r\n\t\t\t\t\t\t\tconsole.log(mdlist);\r\n\t\t\t\t\t\t\tthat.mdlist = mdlist;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tmodalinput: function() {\r\n\t\t\t\tthis.$refs.dialogInput.open()\r\n\t\t\t},\r\n\t\t\t//选择门店\r\n\t\t\tselectmd: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar itemlist = [];\r\n\t\t\t\tvar mdlist = this.mdlist;\r\n\t\t\t\tfor (var i = 0; i < mdlist.length; i++) {\r\n\t\t\t\t\titemlist.push(mdlist[i].name + (mdlist[i].juli ? ' 距离:' + mdlist[i].juli + '千米' : ''));\r\n\t\t\t\t}\r\n\t\t\t\tif (itemlist.length > 6) {\r\n\t\t\t\t\tthat.selectmdDialogShow = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\titemList: itemlist,\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\t\tthat.mdkey = res.tapIndex;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselectmdRadioChange: function (e) {\r\n\t\t\t\tthis.mdkey = e.currentTarget.dataset.index;\r\n\t\t\t\tthis.selectmdDialogShow = false;\r\n\t\t\t},\r\n\t\t\thideSelectmdDialog: function () {\r\n\t\t\t\tthis.selectmdDialogShow = false\r\n\t\t\t},\r\n\t\t\t//积分抵扣\r\n\t\t\tscoredk: function(e) {\r\n\t\t\t\tvar usescore = e.detail.value[0];\r\n\t\t\t\tif (!usescore) usescore = 0;\r\n\t\t\t\tthis.usescore = usescore;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tinputMoney: function(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tvar money = e.detail.value;\r\n\t\t\t\tif (!money) money = 0;\r\n\t\t\t\tvar money = parseFloat(money);\r\n\t\t\t\tif (money <= 0) money = 0;\r\n\t\t\t\tthis.money = money;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tcancel: function() {\r\n\t\t\t\tthis.hiddenmodalput = true;\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\r\n\t\t\t\tvar money = ''\r\n\t\t\t\tif (that.money == '') {\r\n\t\t\t\t\tmoney = 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tmoney = parseFloat(that.money);\r\n\t\t\t\t}\r\n\t\t\t\tif (that.userinfo.discount > 0 && that.userinfo.discount < 10) {\r\n\t\t\t\t\tvar disprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100; //-会员折扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar disprice = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar couponmoney = parseFloat(that.couponmoney); //-优惠券抵扣 \r\n\t\t\t\tif (that.usescore) {\r\n\t\t\t\t\tvar dkmoney = parseFloat(that.userinfo.dkmoney); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar dkmoney = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\t\t\t\tif (dkmoney > 0 && scoredkmaxpercent >= 0 && scoredkmaxpercent < 100 &&\r\n\t\t\t\t\tdkmoney > (money - disprice - couponmoney) * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t\tdkmoney = (money - disprice - couponmoney) * scoredkmaxpercent * 0.01;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar paymoney = money - disprice - couponmoney - dkmoney; // 商品金额 - 会员折扣 - 优惠券抵扣 - 积分抵扣\r\n\t\t\t\tif (paymoney < 0) paymoney = 0;\r\n\t\t\t\tpaymoney = paymoney.toFixed(2);\r\n\t\t\t\tthat.paymoney = paymoney;\r\n\t\t\t\tthat.disprice = disprice;\r\n\t\t\t},\r\n\t\t\tchooseCoupon: function(e) {\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t\tvar couponkey = e.key;\r\n\r\n\t\t\t\tif (couponrid == this.couponrid) {\r\n\t\t\t\t\tthis.couponkey = 0;\r\n\t\t\t\t\tthis.couponrid = 0;\r\n\t\t\t\t\tthis.coupontype = 1;\r\n\t\t\t\t\tthis.couponmoney = 0;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar couponList = this.couponList;\r\n\t\t\t\t\tvar couponmoney = couponList[couponkey]['money'];\r\n\t\t\t\t\tvar coupontype = couponList[couponkey]['type'];\r\n\t\t\t\t\tif (coupontype == 4) {\r\n\t\t\t\t\t\tcouponmoney = this.freightprice;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.couponkey = couponkey;\r\n\t\t\t\t\tthis.couponrid = couponrid;\r\n\t\t\t\t\tthis.coupontype = coupontype;\r\n\t\t\t\t\tthis.couponmoney = couponmoney;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar money = that.money;\r\n\t\t\t\tvar couponrid = that.couponrid;\r\n\t\t\t\tvar usescore = that.usescore;\r\n\r\n\t\t\t\tif (that.mdlist.length > 0) {\r\n\t\t\t\t\tvar mdid = that.mdlist[that.mdkey].id;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar mdid = 0;\r\n\t\t\t\t}\r\n\t\t\t\t//\r\n\t\t\t\tlet charge_s = [];\r\n\t\t\t\tcharge_s.push(that.bid+\"-\"+money);\r\n\t\t\t\tapp.post('ApiMoney/recharge', {\r\n\t\t\t\t\tbid: that.bid,\r\n                    ymid: that.ymid,\r\n\t\t\t\t\tmoney: money,\r\n\t\t\t\t\tcouponrid: couponrid,\r\n\t\t\t\t\tusescore: usescore,\r\n\t\t\t\t\t'charge_s':charge_s,\r\n\t\t\t\t\tmdid: mdid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowCouponList: function() {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t},\r\n\t\t\tGetDistance: function(lat1, lng1, lat2, lng2) {\r\n\t\t\t\tvar radLat1 = lat1 * Math.PI / 180.0;\r\n\t\t\t\tvar radLat2 = lat2 * Math.PI / 180.0;\r\n\t\t\t\tvar a = radLat1 - radLat2;\r\n\t\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\r\n\t\t\t\tvar s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *\r\n\t\t\t\t\tMath.pow(Math.sin(b / 2), 2)));\r\n\t\t\t\ts = s * 6378.137; // EARTH_RADIUS;\r\n\t\t\t\ts = Math.round(s * 100) / 100;\r\n\t\t\t\treturn s;\r\n\t\t\t},\r\n\t\t\tcompare: function(property) {\r\n\t\t\t\treturn function(a, b) {\r\n\t\t\t\t\tvar value1 = a[property];\r\n\t\t\t\t\tvar value2 = b[property];\r\n\t\t\t\t\treturn value1 - value2;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground: #f0f0f0;\r\n\t}\r\n\r\n\t.container {\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 5;\r\n\t}\r\n\r\n\t.header {\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.header_text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.header_name {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.header_icon {\r\n\t\tposition: relative;\r\n\t\theight: 85rpx;\r\n\t\twidth: 85rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground: #37b053;\r\n\t}\r\n\r\n\t.header_shop {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.page {\r\n\t\tposition: relative;\r\n\t\tpadding: 20rpx 50rpx 20rpx 50rpx;\r\n\t\tborder-radius: 30rpx 30rpx 0 0;\r\n\t\tbackground: #fff;\r\n\t\tbox-sizing: border-box;\r\n\t\twidth: 100%;\r\n\t\theight: calc(100% - 185rpx);\r\n\t}\r\n\r\n\t.page_title {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.page_module {\r\n\t\tposition: relative;\r\n\t\theight: 125rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.page_notice {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.page_tag {\r\n\t\tfont-size: 58rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.page_price {\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 54rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.page_cursor {\r\n\t\twidth: 4rpx;\r\n\t\theight: 70rpx;\r\n\t\tbackground: #1AAD19;\r\n\t\tborder-radius: 6rpx;\r\n\t\tanimation: twinkling 1.5s infinite;\r\n\t}\r\n\r\n\t@keyframes twinkling {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\r\n\t\t90% {\r\n\t\t\topacity: .8;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t.info-box {\r\n\t\tposition: relative;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px #f3f3f3 solid;\r\n\t}\r\n\r\n\t.info-item:last-child {\r\n\t\tborder: none\r\n\t}\r\n\r\n\t.info-item .t1 {\r\n\t\twidth: 200rpx;\r\n\t\theight: 120rpx;\r\n\t\tline-height: 120rpx;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.info-item .t2 {\r\n\t\theight: 120rpx;\r\n\t\tline-height: 120rpx;\r\n\t\tcolor: #000;\r\n\t\ttext-align: right;\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx\r\n\t}\r\n\r\n\t.info-item .t2 input {\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tborder: 1px solid #f5f5f5;\r\n\t\tpadding: 0 5px;\r\n\t\twidth: 240rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.dkdiv {\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\t.dkdiv-item {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t}\r\n\r\n\t.dkdiv-item:last-child {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.dkdiv-item .f1 {}\r\n\r\n\t.dkdiv-item .f2 {\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.dkdiv-item .f3 {\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t}\r\n\r\n\t.fpay-btn {\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 5%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\tfloat: left;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #1aac19;\r\n\t\tborder: none;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.fpay-btn2 {\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 5%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfloat: left;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #e2cc05;\r\n\t\tborder: none;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.mendian {\r\n\t\twidth: 90%;\r\n\t\tline-height: 60rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 30rpx 5%;\r\n\t\theight: 800rpx;\r\n\t\toverflow-y: scroll;\r\n\t\tborder: none;\r\n\t\tborder-radius: 5px;\r\n\t\t-webkit-animation-duration: .5s;\r\n\t\tanimation-duration: .5s;\r\n\t}\r\n\r\n\t.mendian label {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\tpadding: 20rpx 0;\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.mendian input {\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.submit {\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.mendian button {\r\n\t\tpadding: 20rpx 60rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tborder: 0;\r\n\t\tmargin-top: 20rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #31C88E\r\n\t}\r\n\r\n\t.i-as {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: #f7f7f8;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransform-origin: center;\r\n\t\ttransition: all .2s ease-in-out;\r\n\t\tz-index: 900;\r\n\t\tvisibility: hidden\r\n\t}\r\n\r\n\t.i-as-show {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t\tvisibility: visible\r\n\t}\r\n\r\n\t.i-as-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, .7);\r\n\t\tz-index: 900;\r\n\t\ttransition: all .2s ease-in-out;\r\n\t\topacity: 0;\r\n\t\tvisibility: hidden\r\n\t}\r\n\r\n\t.i-as-mask-show {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible\r\n\t}\r\n\r\n\t.i-as-header {\r\n\t\tbackground: #fff;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #555;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx\r\n\t}\r\n\r\n\t.i-as-header::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 200%;\r\n\t\theight: 200%;\r\n\t\ttransform: scale(.5);\r\n\t\ttransform-origin: 0 0;\r\n\t\tpointer-events: none;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 0 solid #e9eaec;\r\n\t\tborder-bottom-width: 1px\r\n\t}\r\n\r\n\t.i-as-cancel {\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\t.i-as-cancel button {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.i-as-cancel button::after {\r\n\t\tborder: 0;\r\n\t}\r\n\r\n\t.i-as-content {\r\n\t\theight: 700rpx;\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 20rpx;\r\n\t}\r\n\r\n\r\n\t.op {\r\n\t\twidth: 96%;\r\n\t\tmargin: 20rpx 2%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 40rpx\r\n\t}\r\n\r\n\t.op .btn {\r\n\t\tflex: 1;\r\n\t\theight: 100rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tbackground: #07C160;\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\t.op .btn .img {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t\tmargin-right: 20rpx\r\n\t}\r\n\r\n\r\n\t.keyboard_page {\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.keyboard_none {\r\n\t\tposition: absolute;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.keyboard_key {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\theight: 0;\r\n\t\tz-index: 10;\r\n\t\tbackground: #f7f7f7;\r\n\t\tz-index: 9999999999;\r\n\t\ttransition: height 0.3s;\r\n\t\tpadding: 20rpx 0 0 0;\r\n\t}\r\n\r\n\t.hind_box {\r\n\t\theight: 515rpx;\r\n\t}\r\n\r\n\t.key-box {\r\n\t\tdisplay: flex;\r\n\t\tpadding-left: 16rpx;\r\n\t\tpadding-bottom: 16rpx;\r\n\t\tpadding-bottom: calc(16rpx + constant(safe-area-inset-bottom));\r\n\t\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\r\n\t}\r\n\t\r\n\t.key-down{\r\n\t\theight: 50rpx;\r\n\t\twidth: 50rpx;\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.number-box {\r\n\t\tflex: 3;\r\n\t}\r\n\r\n\t.number-box .key {\r\n\t\tfloat: left;\r\n\t\tmargin: 16rpx 16rpx 0 0;\r\n\t\twidth: calc(100% / 3 - 16rpx);\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tline-height: 90rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.number-box .key.key-zero {\r\n\t\twidth: calc((100% / 3) * 2 - 16rpx);\r\n\t}\r\n\r\n\t.keyboard .number-box-hover {\r\n\t\t/* 临时定义颜色 */\r\n\t\tbackground-color: #e1e1e1 !important;\r\n\t}\r\n\r\n\t.btn-box {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.btn-box .key {\r\n\t\tmargin: 16rpx 16rpx 0 0;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tline-height: 90rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.btn-box .pay_btn {\r\n\t\theight: 298rpx;\r\n\t\tline-height: 298rpx;\r\n\t\tfont-weight: normal;\r\n\t\tbackground-color: #1AAD19;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.btn-box .pay_btn.pay-btn-display {\r\n\t\tbackground-color: #9ED99D !important;\r\n\t}\r\n\r\n\t.btn-box .pay_btn.pay-btn-hover {\r\n\t\tbackground-color: #179B16;\r\n\t}\r\n\t.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\r\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shoprecharge.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shoprecharge.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098464\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}