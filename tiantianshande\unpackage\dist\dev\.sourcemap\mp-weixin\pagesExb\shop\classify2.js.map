{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?0e3f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?90a1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?6f79", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?357e", "uni-app:///pagesExb/shop/classify2.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?adbd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/classify2.vue?dccd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "bottom", "pagenum", "order", "field", "clist", "curTopIndex", "curIndex", "curIndex2", "datalist", "nopro", "curCid", "proid", "buydialogShow", "prodata", "userinfo", "nomore", "nodata", "bid", "showLinkStatus", "lx_name", "lx_bid", "lx_tel", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "cid", "isget", "getdatalist", "wherefield", "uni", "scrolltolower", "changeCTab", "changeOrder", "switchRightTab", "switchTopTab", "buydialogChange", "showLinkChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzKA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqGlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAC;QAAAC;QAAAX;MAAA;QACAS;QACA;QACAA;QACAA;QACA;UACA;YACA;cACAA;cACAA;cACA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAG;gBACA;cACA;YACA;YACA;UACA;QACA;QACAH;QACAA;MACA;IACA;IACAI;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;MACAA;MACAA;MACA;MACAK;MACAA;MACAA;MACAA;MACA;QACAA;MACA;QACAA;MACA;MACAJ;QACAD;QACAM;QACA;QACA;UACA;YACAN;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAO;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAb;MACAA;MACAA;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChSA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/shop/classify2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/shop/classify2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./classify2.vue?vue&type=template&id=6b0024ca&\"\nvar renderjs\nimport script from \"./classify2.vue?vue&type=script&lang=js&\"\nexport * from \"./classify2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./classify2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/shop/classify2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./classify2.vue?vue&type=template&id=6b0024ca&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 = _vm.isload && _vm.curIndex == -1 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.clist[_vm.curTopIndex].child, function (item, index2) {\n        var $orig = _vm.__get_orig(item)\n        var m3 = _vm.curIndex == index2 ? _vm.t(\"color1\") : null\n        var m4 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var m5 =\n    _vm.isload && (!_vm.field || _vm.field == \"sort\") ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.field == \"sales\" ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.field == \"sell_price\" ? _vm.t(\"color1\") : null\n  var m8 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var g0 = _vm.isload\n    ? _vm.curTopIndex > -1 &&\n      _vm.curIndex > -1 &&\n      _vm.clist[_vm.curTopIndex].child[_vm.curIndex].child.length > 0\n    : null\n  var m10 = _vm.isload && g0 && _vm.curIndex2 == -1 ? _vm.t(\"color1\") : null\n  var m11 = _vm.isload && g0 && _vm.curIndex2 == -1 ? _vm.t(\"color1rgb\") : null\n  var l2 =\n    _vm.isload && g0\n      ? _vm.__map(\n          _vm.clist[_vm.curTopIndex].child[_vm.curIndex].child,\n          function (item, idx2) {\n            var $orig = _vm.__get_orig(item)\n            var m12 = _vm.curIndex2 == idx2 ? _vm.t(\"color1\") : null\n            var m13 = _vm.curIndex2 == idx2 ? _vm.t(\"color1rgb\") : null\n            return {\n              $orig: $orig,\n              m12: m12,\n              m13: m13,\n            }\n          }\n        )\n      : null\n  var l3 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m14 =\n          item.price_type != 1 || item.sell_price > 0 ? _vm.t(\"color1\") : null\n        var m15 =\n          item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\n            ? _vm.t(\"color1\")\n            : null\n        var m16 =\n          item.xunjia_text &&\n          item.price_type == 1 &&\n          item.sell_price <= 0 &&\n          item.xunjia_text &&\n          item.price_type == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m17 = !item.price_type ? _vm.t(\"color1rgb\") : null\n        var m18 = !item.price_type ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m14: m14,\n          m15: m15,\n          m16: m16,\n          m17: m17,\n          m18: m18,\n        }\n      })\n    : null\n  var m19 = _vm.showLinkStatus && _vm.lx_tel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        l1: l1,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        g0: g0,\n        m10: m10,\n        m11: m11,\n        l2: l2,\n        l3: l3,\n        m19: m19,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./classify2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./classify2.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/search?bid='+bid\" class=\"search-container\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"order-tab\">\r\n\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\">{{item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content-container\">\r\n\t\t\t<view class=\"nav_left\">\r\n\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == -1 ? 'active' : '')\" :style=\"{color:curIndex == -1?t('color1'):'#333'}\" @tap=\"switchRightTab\" data-index=\"-1\" :data-id=\"clist[curTopIndex].id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>全部</view>\r\n\t\t\t\t<block v-for=\"(item, index2) in clist[curTopIndex].child\" :key=\"index2\">\r\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index2 ? 'active' : '')\" :style=\"{color:curIndex == index2?t('color1'):'#333'}\" @tap=\"switchRightTab\" :data-index=\"index2\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t<view class=\"nav_right-content\">\r\n\t\t\t\t\t<view class=\"nav-pai\">\r\n\t\t\t\t\t\t<view class=\"nav-paili\" :style=\"{color:(!field||field=='sort')?t('color1'):'#323232'}\" @tap=\"changeOrder\" data-field=\"sort\" data-order=\"desc\">综合</view> \r\n\t\t\t\t\t\t<view class=\"nav-paili\" :style=\"field=='sales'?'color:'+t('color1'):''\" @tap=\"changeOrder\" data-field=\"sales\" data-order=\"desc\">销量</view> \r\n\t\t\t\t\t\t<view class=\"nav-paili\" @tap=\"changeOrder\" data-field=\"sell_price\" :data-order=\"order=='asc'?'desc':'asc'\">\r\n\t\t\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t\t\t</view>  \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"classify-ul\" v-if=\"curTopIndex>-1 && curIndex>-1 && clist[curTopIndex].child[curIndex].child.length>0\">\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t <view class=\"classify-li\" :style=\"curIndex2==-1?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeCTab\" :data-id=\"clist[curTopIndex].child[curIndex].id\" data-index=\"-1\">全部</view>\r\n\t\t\t\t\t\t <block v-for=\"(item, idx2) in clist[curTopIndex].child[curIndex].child\" :key=\"idx2\">\r\n\t\t\t\t\t\t <view class=\"classify-li\" :style=\"curIndex2==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeCTab\" :data-id=\"item.id\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/shopPackage/shop/product?id='+item.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p2\" v-if=\"item.price_type != 1 || item.sell_price > 0\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n                                    <view class=\"p2\" v-if=\"item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\" style=\"height: 50rpx;line-height: 44rpx;\">\r\n                                    \t<text class=\"t1\" :style=\"{color:t('color1'),fontSize:'30rpx'}\">询价</text>\r\n                                        <block v-if=\"item.xunjia_text && item.price_type == 1\">\r\n                                        \t<view class=\"lianxi\" :style=\"{background:t('color1')}\" @tap.stop=\"showLinkChange\" :data-lx_name=\"item.lx_name\" :data-lx_bid=\"item.lx_bid\" :data-lx_bname=\"item.lx_bname\" :data-lx_tel=\"item.lx_tel\" data-btntype=\"2\">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>\r\n                                        </block>\r\n                                    </view>\r\n                                    <view class=\"p1\" v-if=\"item.merchant_name\" style=\"color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal\"><text>{{item.merchant_name}}</text></view>\r\n                                    <view class=\"p1\" v-if=\"item.main_business\" style=\"color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;\"><text>{{item.main_business}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n                  <view v-if=\"item.sales<=0 && item.merchant_name\" style=\"height: 44rpx;\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"p4\" v-if=\"!item.price_type\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\"></buydialog>\r\n    <view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n    \t<view class=\"main\">\r\n    \t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" src=\"/static/img/close.png\"/></view>\r\n    \t\t<view class=\"content\">\r\n    \t\t\t<view class=\"title\">{{lx_name}}</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\r\n    \t\t\t\t<view class=\"f1\">店铺名称</view>\r\n    \t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">{{lx_bname}}<image src=\"/static/img/arrowright.png\" class=\"image\"/></view>\r\n    \t\t\t</view>\r\n    \t\t\t<view class=\"row\" v-if=\"lx_tel\">\r\n    \t\t\t\t<view class=\"f1\">联系电话</view>\r\n    \t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}<image src=\"/static/img/copy.png\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\"></image></view>\r\n    \t\t\t</view>\r\n    \t\t</view>\r\n    \t</view>\r\n    </view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      bottom: 0,\r\n      pagenum: 1,\r\n      order: '',\r\n      field: '',\r\n      clist: [],\r\n      curTopIndex: 0,\r\n      curIndex: -1,\r\n      curIndex2: -1,\r\n      datalist: [],\r\n      nopro: 0,\r\n      curCid: 0,\r\n\t\t\tproid:0,\r\n\t\t\tbuydialogShow: false,\r\n      prodata: [],\r\n      userinfo: [],\r\n      nomore: false,\r\n\t\t\tnodata: false,\r\n\t\t\tbid:'',\r\n            \r\n            showLinkStatus:false,\r\n            lx_name:'',\r\n            lx_bid:'',\r\n            lx_tel:''\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid ? this.opt.bid  : '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiShop/classify2', {cid: nowcid,bid:that.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.clist = data;\r\n\t\t\t\tthat.curCid = data[0]['id'];\r\n\t\t\t\tif (nowcid) {\r\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tif (data[i]['id'] == nowcid) {\r\n\t\t\t\t\t\t\tthat.curTopIndex = i;\r\n\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar downcdata = data[i]['child'];\r\n\t\t\t\t\t\tvar isget = 0;\r\n\t\t\t\t\t\tfor (var j = 0; j < downcdata.length; j++) {\r\n\t\t\t\t\t\t\tif (downcdata[j]['id'] == nowcid) {\r\n\t\t\t\t\t\t\t\tthat.curIndex = i + 1;\r\n\t\t\t\t\t\t\t\tthat.curIndex2 = j;\r\n\t\t\t\t\t\t\t\tthat.curTopIndex = i;\r\n\t\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\t\tisget = 1;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (isget) break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t});\r\n\t\t},\r\n    getdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar cid = that.curCid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar order = that.order;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tvar wherefield = {};\r\n\t\t\twherefield.pagenum = pagenum;\r\n\t\t\twherefield.field = field;\r\n\t\t\twherefield.order = order;\r\n\t\t\twherefield.bid = bid;\r\n\t\t\tif(bid > 0){\r\n\t\t\t\twherefield.cid2 = cid;\r\n\t\t\t}else{\r\n\t\t\t\twherefield.cid = cid;\r\n\t\t\t}\r\n\t\t\tapp.post('ApiShop/getprolist',wherefield, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t});\r\n\t\t},\r\n\t\tscrolltolower: function () {\r\n\t\t\tif (!this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\t//改变子分类\r\n\t\tchangeCTab: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tthis.curIndex2 = index;\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.curCid = id;\r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.datalist = [];\r\n\t\t\tthis.nomore = false;\r\n\t\t\tthis.getdatalist();\r\n\t\t},\r\n\t\t//改变排序规则\r\n\t\tchangeOrder: function (e) {\r\n\t\t\tvar t = e.currentTarget.dataset;\r\n\t\t\tthis.field = t.field; \r\n\t\t\tthis.order = t.order;\r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.datalist = []; \r\n\t\t\tthis.nomore = false;\t\r\n\t\t\tthis.getdatalist();\r\n\t\t},\r\n\t\t//事件处理函数\r\n\t\tswitchRightTab: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tthis.curIndex = index;\r\n\t\t\tthis.curIndex2 = -1;\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.curCid = id;\r\n\t\t\tthis.getdatalist();\r\n\t\t},\r\n    switchTopTab: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var index = parseInt(e.currentTarget.dataset.index);\r\n      this.curTopIndex = index;\r\n      this.curIndex = -1;\r\n      this.curIndex2 = -1;\r\n      this.prolist = [];\r\n      this.nopro = 0;\r\n      this.curCid = id;\r\n      this.getdatalist();\r\n    },\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n        showLinkChange: function (e) {\r\n            var that = this;\r\n        \tthat.showLinkStatus = !that.showLinkStatus;\r\n            that.lx_name = e.currentTarget.dataset.lx_name;\r\n            that.lx_bid = e.currentTarget.dataset.lx_bid;\r\n            that.lx_bname = e.currentTarget.dataset.lx_bname;\r\n            that.lx_tel = e.currentTarget.dataset.lx_tel;\r\n        },\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {height:100%;}\r\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\r\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;}\r\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\r\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\r\n\r\n\r\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:auto;padding:0 20rpx;font-size:30rpx;font-weight:bold;text-align: center; color:#999999; height:90rpx; line-height:90rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}\r\n.order-tab2 .on .after{display:block}\r\n\r\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\r\n\r\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\r\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\r\n.nav-pai{ width: 100%;display:flex;align-items:center;justify-content:center;}\r\n.nav-paili{flex:1; text-align:center;color:#323232; font-size:28rpx;font-weight:bold;position: relative;height:80rpx;line-height:80rpx;}\r\n.nav-paili .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.nav-paili .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n\r\n.classify-ul{width:100%;height:100rpx;padding:0 10rpx;}\r\n.classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\r\n\r\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\r\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\r\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\r\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\r\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;}\r\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\r\n.product-itemlist .product-info .p4{width:56rpx;height:56rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:32rpx;height:56rpx;line-height:56rpx}\r\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\r\n\r\n.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}\r\n.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}\r\n.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}\r\n.posterDialog .close .img{ width:32rpx;height:32rpx;}\r\n.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\r\n.posterDialog .content .img{width:540rpx;height:960rpx}\r\n.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}\r\n.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;-webkit-transform: translate(-50%,-50%);transform: translate(-50%,-50%);}\r\n.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}\r\n.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}\r\n.linkDialog .row .f1 {width: 40%; text-align: left;}\r\n.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}\r\n.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}\r\n.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}\r\n\r\n.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./classify2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./classify2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024032\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}