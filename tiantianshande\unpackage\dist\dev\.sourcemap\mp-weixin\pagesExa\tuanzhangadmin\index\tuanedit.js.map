{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?6c36", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?1526", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?1f27", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?cedd", "uni-app:///pagesExa/tuanzhangadmin/index/tuanedit.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?2533", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/tuanedit.vue?d864"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "pagecontent", "aglevellist", "levellist", "clist", "clist2", "cateArr", "cateArr2", "glist", "groupArr", "freighttypeArr", "freightindex", "freightList", "freightdata", "freightIds", "gui<PERSON><PERSON>", "pic", "pics", "cids", "cids2", "gids", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnames", "cnames2", "gnames", "clistshow", "clist2show", "glistshow", "ggname", "ggindex", "ggindex2", "oldgglist", "gglist", "catche_detailtxt", "start_time1", "start_time2", "end_time1", "end_time2", "start_hours", "end_hours", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_showset", "commission_canset", "bid", "paramList", "paramdata", "editorFormdata", "test", "ls", "checks", "showModel", "keyword", "onLoad", "methods", "getList", "app", "res", "item", "that", "arr", "searchConfirm", "selectItem", "ind", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "console", "setfield", "<PERSON><PERSON><PERSON>", "detailBiji", "subform", "id", "<PERSON><PERSON><PERSON>", "setTimeout", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "dialogDetailtxtConfirm", "detailAddpic", "detailMoveup", "detailMovedown", "detailMovedel", "changeFrieght", "newfreightIds", "ischecked", "freighttypeChange", "bindStatusChange", "bindStartTime1Change", "bindStartTime2Change", "bindEndTime1Change", "bindEndTime2Change", "bindStartHoursChange", "bindEndHoursChange", "gglistInput", "getgglist", "newlen", "h", "k", "title", "tarr", "sarr", "ks", "titles", "name", "market_price", "cost_price", "sell_price", "weight", "stock", "givescore", "lvprice_data", "addgggroupname", "delgggroupname", "uni", "itemList", "success", "newguigedata", "setgggroupname", "items", "addggname", "delggname", "newitems", "index2", "setggname", "cidsChange", "newcids", "getcnames", "cids2Change", "getcnames2", "gidsChange", "newgids", "<PERSON><PERSON><PERSON>", "changeClistDialog", "changeClist2Dialog", "changeGlistDialog", "uploadimg", "removeimg", "uploadimg2", "removeimg2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChNA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6PjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QAAAJ;MAAA;QAEA;QACAK;UAEAC;UAEAC;YAEA;cACAD;YACA;UAEA;UAEAE;QAEA;QAEAD;MAGA;IACA;IACAE;MACA;MACA;IACA;IAEAC;MAAA;MAEA;MACA;QACA;UACAC;QACA;MAEA;MAEA;QACA;MACA;QACA;MACA;MAEA;MACA;QAEA;UAEA;YACAL;UACA;QACA;QAEA;UACAA;QACA;QAEAE;MAEA;MAEA;IAEA;IAEAI;MACA;MACA;MACA;MACA;MACA;MACAjB;MACAkB;MACA;MACA;MAEA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MAEA;MACA;MAEA;MAEAJ;MAEAT;QACAc;QACArE;QACAsE;MACA;QACA;UACAf;QACA;UACAA;UACAgB;YACAhB;UACA;QACA;MACA;IAEA;IACAiB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAV;MACA;IACA;IACAW;MACA;MACAX;MACA;MACA;MACA/D;QACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;IACA;IACA2E;MACA;MACArB;QACA;QACA;QACA;UACA;UACAtC;YACA;YACA;YACA;YACA;UACA;QACA;QACA;QACAhB;UACA;UACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;cACA;YACA;YACA;cACA;YACA;UACA;UACA;UACA;UACA;QACA;QACAyD;MACA;IACA;IACAmB;MACA;MACA;MACA,eACA5E;IACA;IACA6E;MACA;MACA;MACA,oCACA7E;IACA;IACA8E;MACA;MACA;MACA9E;IACA;IACA+E;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA3D;MACA;MACAgC;IACA;IACA4B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;QAAA;QACAC;QACAC;QACA;UACAA;YACAC;YACAC;UACA;QACA;MACA;;MAEA;MACA;MACA,YACA,GACA;MACA;QACA;QACA;UACA;YACAC;UACA;QAAA;QACAC;MACA;MACAlC;MACAA;MAEA;QACA;QACA;QACA;UACAmC;UACAC;QACA;QACAD;QACAC;QACA;QACA;QACA;UACA;QACA;UACA;YACAD;YACAE;YACAC;YACAC;YACAC;YACAC;YACAC;YACA1F;YACA2F;YACAC;UACA;QACA;QACA5E;MACA;MACA;MACAgC;IACA;IACA6C;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;UACA;YACA;cAAA;cACAvD;cACAA;cACAA;cACA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACAwD;gBACA;cACA;cACAxD;cACAM;cACAN;YACA;UACA;QACA;MACA;IACA;IACAyD;MACA;MACA;MACA;QAAA;QACAtF;QACAd;UACAgF;UACAC;UACAoB;QACA;QACA;MACA;QAAA;QACArG;QACA;MACA;MACA;MACA;IACA;IACAsG;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAP;QACAC;QACAC;UACA;YACA;cAAA;cACAvD;cACAA;cACAA;cACAA;cACA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA;kBACA;kBACA;oBACA;sBACA6D;wBACAxB;wBACAC;sBACA;sBACAwB;oBACA;kBACA;kBACAzG;gBACA;gBACAmG;cACA;cACAxD;cACAM;cACAN;YACA;UACA;QACA;MACA;IACA;IACA+D;MACA;MACA;MACA;MACA;QAAA;QACA;QACA3F;QACAsF;UACArB;UACAC;QACA;QACAjF;QACA;MACA;QAAA;QACAA;QACA;MACA;MACA;MACA;IACA;IACA2G;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAzC;QACA;MACA;MACA;QACA;UACA3B;UACA;QACA;QACAoE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAtG;MACA;MACA;IACA;IACAuG;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAF;QACA;UACAzC;QACA;MACA;MACA;QACA;UACA3B;UACA;QACA;QACAoE;MACA;MACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACAxG;MACA;MACA;IACA;IACAyG;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACA9C;QACA;MACA;MACA;QACA8C;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAzG;MACA;MACA;IACA;IACA0G;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA9E;QACA;UACAtC;QACA;QACA;QACA;MACA;IACA;IACAqH;MACA;MACA;MACA;MACA;QACA;QACArH;QACAyC;MACA;QACA;QACAzC;QACAyC;MACA;IACA;IACA6E;MACA;MACA;MACAhF;QACAG;MACA;IACA;IACA8E;MACA;MACA;MACA9E;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj6BA;AAAA;AAAA;AAAA;AAAi4C,CAAgB,g1CAAG,EAAC,C;;;;;;;;;;;ACAr5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/index/tuanedit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/index/tuanedit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tuanedit.vue?vue&type=template&id=6e830708&\"\nvar renderjs\nimport script from \"./tuanedit.vue?vue&type=script&lang=js&\"\nexport * from \"./tuanedit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tuanedit.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/index/tuanedit.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuanedit.vue?vue&type=template&id=6e830708&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  var m2 = _vm.showModel ? _vm.t(\"color1\") : null\n  var m3 = _vm.showModel ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuanedit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuanedit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block>\r\n\r\n\t\t\t<form @submit=\"subform\">\r\n\r\n\t\t\t\t<view class=\"form-box\">\r\n\t\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t\t<text>商品详情</text>\r\n\t\t\t\t\t\t<view class=\"detailop\">\r\n\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailAddtxt\">+文本</view>\r\n\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailAddpic\">+图片</view>\r\n\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailBiji\">+选择笔记</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"detaildp\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailMoveup\" :data-index=\"index\">上移</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailMovedown\" :data-index=\"index\">下移</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"detailMovedel\" :data-index=\"index\">删除</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detailbox\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\"></dp-text>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\"></dp-cube>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\"></dp-picture>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='pictures'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:shopinfo=\"setData.shopinfo\"></dp-shop>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:menuindex=\"menuindex\"></dp-product>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\"></dp-collage>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\"></dp-kanjia>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\"></dp-seckill>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\"></dp-scoreshop>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\"></dp-business>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:content=\"setData.content\"></dp-richtext>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:content=\"setData.content\"></dp-form>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:content=\"setData.content\"></dp-userinfo>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<button class=\"savebtn\"\r\n\t\t\t\t\t:style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"\r\n\t\t\t\t\tform-type=\"submit\">提交</button>\r\n\t\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t\t</form>\r\n\r\n\r\n\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"position: fixed;height: 400px;background: #eee;bottom: 0;z-index: 100;left: 0;width: 100%;border-radius: 10px 10px 0 0;\"\r\n\t\t\t\tv-if=\"showModel\">\r\n\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding:10px 20px;\">\r\n\r\n\t\t\t\t\t<image style=\"width: 15px;height: 15px;\"></image>\r\n\r\n\t\t\t\t\t<view>选择笔记</view>\r\n\r\n\t\t\t\t\t<image style=\"width: 15px;height: 15px;\" src=\"../../../static/img/close.png\" @click=\"selectbiji\">\r\n\t\t\t\t\t</image>\r\n\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t   <view class=\"search-bar\">\r\n\t\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索笔记\"\r\n\t\t\t\t\t\t\t\tplaceholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"uni-list\">\r\n\t\r\n\t\t\t\t\t<view class=\"list\" style=\"padding: 10px;\">\r\n\r\n\t\t\t\t\t\t<view class=\"pbl\" v-for=\"(item, j) in ls\" :key=\"j\"  @click=\"selectItem(item.id)\">\r\n\r\n\t\t\t\t\t\t\t<view class=\"image\" style=\"position: relative;min-height: 150px;\">\r\n\t\t\t\t\t\t\t\t<image fade-show lazy-load :lazy-load-margin=\"0\" mode=\"widthFix\" :src=\"item.coverimg\">\r\n\t\t\t\t\t\t\t\t</image>\r\n\r\n\r\n\t\t\t\t\t\t\t\t<image v-if=\"item.checked\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"position: absolute;right: 5px;top: 5px;width: 20px;height:20px;z-index: 100;\"\r\n\t\t\t\t\t\t\t\t\tsrc=\"../../../static/highVoltage/checked.png\"></image>\r\n\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<rich-text class=\"p1\" :nodes=\"item.content\"></rich-text>\r\n\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tstyle=\"display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;\">\r\n\r\n\t\t\t\t\t\t\t\t<view style=\"display: flex;align-items: center;width: 60%;\">\r\n\r\n\t\t\t\t\t\t\t\t\t<img style=\"width: 20px;height: 20px;border-radius: 50px;\"\r\n\t\t\t\t\t\t\t\t\t\t:src=\"item.headimg\"></img>\r\n\r\n\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\tstyle=\"font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.nickname}}</view>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t<view style=\"display: flex;align-items: center;\">\r\n\r\n\t\t\t\t\t\t\t\t\t<image style=\"width: 12px;height: 12px;\" src=\"../../../static/restaurant/like1.png\">\r\n\t\t\t\t\t\t\t\t\t</image>\r\n\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 10px;margin-left: 5px;\">{{item.readcount}}</view>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<button class=\"savebtn\"\r\n\t\t\t\t\t:style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"\r\n\t\t\t\t\t@click=\"selectbiji\">确定</button>\r\n\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<uni-popup id=\"dialogDetailtxt\" ref=\"dialogDetailtxt\" type=\"dialog\">\r\n\t\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-title-text\">请输入文本内容</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t\t<textarea value=\"\" placeholder=\"请输入文本内容\" @input=\"catcheDetailtxt\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"dialogDetailtxtConfirm\">\r\n\t\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-popup-dialog__close\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t<span class=\"uni-popup-dialog__close-icon \"></span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisload: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tpagecontent: [],\r\n\t\t\t\taglevellist: [],\r\n\t\t\t\tlevellist: [],\r\n\t\t\t\tclist: [],\r\n\t\t\t\tclist2: [],\r\n\t\t\t\tcateArr: [],\r\n\t\t\t\tcateArr2: [],\r\n\t\t\t\tglist: [],\r\n\t\t\t\tgroupArr: [],\r\n\t\t\t\tfreighttypeArr: ['全部模板', '指定模板', '自动发货', '在线卡密'],\r\n\t\t\t\tfreightindex: 0,\r\n\t\t\t\tfreightList: [],\r\n\t\t\t\tfreightdata: [],\r\n\t\t\t\tfreightIds: [],\r\n\t\t\t\tguigedata: [],\r\n\t\t\t\tpic: [],\r\n\t\t\t\tpics: [],\r\n\t\t\t\tcids: [],\r\n\t\t\t\tcids2: [],\r\n\t\t\t\tgids: [],\r\n\t\t\t\txiaofeizhi: [],\r\n\t\t\t\tcnames: '',\r\n\t\t\t\tcnames2: '',\r\n\t\t\t\tgnames: '',\r\n\t\t\t\tclistshow: false,\r\n\t\t\t\tclist2show: false,\r\n\t\t\t\tglistshow: false,\r\n\t\t\t\tggname: '',\r\n\t\t\t\tggindex: 0,\r\n\t\t\t\tggindex2: 0,\r\n\t\t\t\toldgglist: [],\r\n\t\t\t\tgglist: [],\r\n\t\t\t\tcatche_detailtxt: '',\r\n\t\t\t\tstart_time1: '-选择日期-',\r\n\t\t\t\tstart_time2: '-选择时间-',\r\n\t\t\t\tend_time1: '-选择日期-',\r\n\t\t\t\tend_time2: '-选择时间-',\r\n\t\t\t\tstart_hours: '-开始时间-',\r\n\t\t\t\tend_hours: '-结束时间-',\r\n\t\t\t\tgettjArr: ['-1'],\r\n\t\t\t\tproduct_showset: 1,\r\n\t\t\t\tcommission_canset: 1,\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tparamList: [],\r\n\t\t\t\tparamdata: [],\r\n\t\t\t\teditorFormdata: [],\r\n\t\t\t\ttest: '',\r\n\r\n\t\t\t\tls: [],\r\n\t\t\t\tchecks: [],\r\n\t\t\t\tshowModel: false,\r\n\t\t\t\tkeyword: ''\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\t//this.getdata();\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetList() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tapp.post('Apidaihuobiji/ltlist', {keyword : that.keyword}, function(res) {\r\n\r\n\t\t\t\t\tlet arr = [];\r\n\t\t\t\t\tres.datalist.filter((item,k)=>{\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\titem.checked = false \r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.checks.filter(m=>{\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t if(item.id == m){\r\n\t\t\t\t\t\t\t\t item.checked = true  \r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tarr.push(item);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.ls = arr;\r\n\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsearchConfirm(e){\r\n\t\t\t\tthis.keyword = e.detail.value;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tselectItem(id){\r\n\t\t\t\t\r\n\t\t\t\tlet ind = -1;\r\n\t\t\t\tthis.checks.filter((item,k)=>{\r\n\t\t\t\t\tif(item == id){\r\n\t\t\t\t\t\tind = k;\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t})\r\n\r\n\t\t\t\tif(ind>-1){\r\n\t\t\t\t\tthis.checks.splice(ind, 1);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.checks.push(id);\r\n\t\t\t\t}\r\n\t\r\n\t            let arr = [];\r\n\t\t\t\tthis.ls.filter((item,k)=>{\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.checks.filter(m=>{\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t if(item.id == m){\r\n\t\t\t\t\t\t\t item.checked = true  \r\n\t\t\t\t\t\t }\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(ind>-1&&item.id == id){\r\n\t\t\t\t\t\t item.checked = false  \t\t\t\t\t \r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tarr.push(item);\r\n\t\r\n\t\t\t\t})\r\n\r\n\t\t\t\tthis.ls = arr;\r\n\r\n\t\t\t},\r\n\r\n\t\t\teditorBindPickerChange: function(e) {\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif (!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tthis.editorFormdata = editorFormdata;\r\n\t\t\t\tthis.test = Math.random();\r\n\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tthis.paramdata[field] = val;\r\n\t\t\t},\r\n\t\t\tsetfield: function(e) {\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tthis.paramdata[field] = value;\r\n\t\t\t},\r\n\r\n\t\t\tselectbiji() {\r\n\t\t\t\tthis.showModel = false;\r\n\t\t\t},\r\n\t\t\tdetailBiji() {\r\n\t\t\t\tthis.showModel = true;\r\n\t\t\t},\r\n\r\n\t\t\tsubform: function(e) {\r\n\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\r\n\t\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(that.checks.toString()) \r\n\r\n\t\t\t\tapp.post('ApituanzhangAdminIndex/adddaihuo', {\r\n\t\t\t\t\tid: id,\r\n\t\t\t\t\tinfo: formdata, \r\n\t\t\t\t\tbijiid: that.checks.toString()\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tapp.goto('index', 'redirect');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tdetailAddtxt: function() {\r\n\t\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t\t},\r\n\t\t\tdialogDetailtxtClose: function() {\r\n\t\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t\t},\r\n\t\t\tcatcheDetailtxt: function(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t\t},\r\n\t\t\tdialogDetailtxtConfirm: function(e) {\r\n\t\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\t\tconsole.log(detailtxt)\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\t\tpagecontent.push({\r\n\t\t\t\t\t\"id\": Mid,\r\n\t\t\t\t\t\"temp\": \"text\",\r\n\t\t\t\t\t\"params\": {\r\n\t\t\t\t\t\t\"content\": detailtxt,\r\n\t\t\t\t\t\t\"showcontent\": detailtxt,\r\n\t\t\t\t\t\t\"bgcolor\": \"#ffffff\",\r\n\t\t\t\t\t\t\"fontsize\": \"14\",\r\n\t\t\t\t\t\t\"lineheight\": \"20\",\r\n\t\t\t\t\t\t\"letter_spacing\": \"0\",\r\n\t\t\t\t\t\t\"bgpic\": \"\",\r\n\t\t\t\t\t\t\"align\": \"left\",\r\n\t\t\t\t\t\t\"color\": \"#000\",\r\n\t\t\t\t\t\t\"margin_x\": \"0\",\r\n\t\t\t\t\t\t\"margin_y\": \"0\",\r\n\t\t\t\t\t\t\"padding_x\": \"5\",\r\n\t\t\t\t\t\t\"padding_y\": \"5\",\r\n\t\t\t\t\t\t\"quanxian\": {\r\n\t\t\t\t\t\t\t\"all\": true\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"platform\": {\r\n\t\t\t\t\t\t\t\"all\": true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"data\": \"\",\r\n\t\t\t\t\t\"other\": \"\",\r\n\t\t\t\t\t\"content\": \"\"\r\n\t\t\t\t});\r\n\t\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t\t},\r\n\t\t\tdetailAddpic: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.chooseImage(function(urls) {\r\n\t\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tvar pics = [];\r\n\t\t\t\t\tfor (var i in urls) {\r\n\t\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\t\tpics.push({\r\n\t\t\t\t\t\t\t\"id\": picid,\r\n\t\t\t\t\t\t\t\"imgurl\": urls[i],\r\n\t\t\t\t\t\t\t\"hrefurl\": \"\",\r\n\t\t\t\t\t\t\t\"option\": \"0\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\t\tpagecontent.push({\r\n\t\t\t\t\t\t\"id\": Mid,\r\n\t\t\t\t\t\t\"temp\": \"picture\",\r\n\t\t\t\t\t\t\"params\": {\r\n\t\t\t\t\t\t\t\"bgcolor\": \"#FFFFFF\",\r\n\t\t\t\t\t\t\t\"margin_x\": \"0\",\r\n\t\t\t\t\t\t\t\"margin_y\": \"0\",\r\n\t\t\t\t\t\t\t\"padding_x\": \"0\",\r\n\t\t\t\t\t\t\t\"padding_y\": \"0\",\r\n\t\t\t\t\t\t\t\"quanxian\": {\r\n\t\t\t\t\t\t\t\t\"all\": true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"platform\": {\r\n\t\t\t\t\t\t\t\t\"all\": true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"data\": pics,\r\n\t\t\t\t\t\t\"other\": \"\",\r\n\t\t\t\t\t\t\"content\": \"\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t}, 9);\r\n\t\t\t},\r\n\t\t\tdetailMoveup: function(e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\t\tif (index > 0)\r\n\t\t\t\t\tpagecontent[index] = pagecontent.splice(index - 1, 1, pagecontent[index])[0];\r\n\t\t\t},\r\n\t\t\tdetailMovedown: function(e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\t\tif (index < pagecontent.length - 1)\r\n\t\t\t\t\tpagecontent[index] = pagecontent.splice(index + 1, 1, pagecontent[index])[0];\r\n\t\t\t},\r\n\t\t\tdetailMovedel: function(e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\t\tpagecontent.splice(index, 1);\r\n\t\t\t},\r\n\t\t\tchangeFrieght: function(e) {\r\n\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar freightIds = this.freightIds;\r\n\t\t\t\tvar newfreightIds = [];\r\n\t\t\t\tvar ischecked = false;\r\n\t\t\t\tfor (var i in freightIds) {\r\n\t\t\t\t\tif (freightIds[i] != id) {\r\n\t\t\t\t\t\tnewfreightIds.push(freightIds[i]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tischecked = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (!ischecked) newfreightIds.push(id);\r\n\t\t\t\tthis.freightIds = newfreightIds;\r\n\t\t\t},\r\n\t\t\tfreighttypeChange: function(e) {\r\n\t\t\t\tthis.freightindex = e.detail.value;\r\n\t\t\t},\r\n\t\t\tbindStatusChange: function(e) {\r\n\t\t\t\tthis.info.status = e.detail.value;\r\n\t\t\t},\r\n\t\t\tbindStartTime1Change: function(e) {\r\n\t\t\t\tthis.start_time1 = e.target.value\r\n\t\t\t},\r\n\t\t\tbindStartTime2Change: function(e) {\r\n\t\t\t\tthis.start_time2 = e.target.value\r\n\t\t\t},\r\n\t\t\tbindEndTime1Change: function(e) {\r\n\t\t\t\tthis.end_time1 = e.target.value\r\n\t\t\t},\r\n\t\t\tbindEndTime2Change: function(e) {\r\n\t\t\t\tthis.end_time2 = e.target.value\r\n\t\t\t},\r\n\t\t\tbindStartHoursChange: function(e) {\r\n\t\t\t\tthis.start_hours = e.target.value\r\n\t\t\t},\r\n\t\t\tbindEndHoursChange: function(e) {\r\n\t\t\t\tthis.end_hours = e.target.value\r\n\t\t\t},\r\n\t\t\tgglistInput: function(e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\t\tvar gglist = this.gglist;\r\n\t\t\t\tgglist[index][field] = e.detail.value;\r\n\t\t\t\tthis.gglist = gglist;\r\n\t\t\t\tconsole.log(gglist)\r\n\t\t\t},\r\n\t\t\tgetgglist: function() {\r\n\t\t\t\tvar oldgglist = this.oldgglist;\r\n\t\t\t\tvar guigedata = this.guigedata;\r\n\t\t\t\tvar gglist = [];\r\n\t\t\t\tvar len = guigedata.length;\r\n\t\t\t\tvar newlen = 1;\r\n\t\t\t\tvar h = new Array(len);\r\n\t\t\t\tfor (var i = 0; i < len; i++) {\r\n\t\t\t\t\tvar itemlen = guigedata[i].items.length;\r\n\t\t\t\t\tif (itemlen <= 0) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t};\r\n\t\t\t\t\tnewlen *= itemlen;\r\n\t\t\t\t\th[i] = new Array(itemlen);\r\n\t\t\t\t\tfor (var j = 0; j < itemlen; j++) {\r\n\t\t\t\t\t\th[i][j] = {\r\n\t\t\t\t\t\t\tk: guigedata[i].items[j].k,\r\n\t\t\t\t\t\t\ttitle: guigedata[i].items[j].title\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//排列组合算法\r\n\t\t\t\tvar arr = h; //原二维数组\r\n\t\t\t\tvar sarr = [\r\n\t\t\t\t\t[]\r\n\t\t\t\t]; //排列组合后的数组\r\n\t\t\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\t\t\tvar tarr = [];\r\n\t\t\t\t\tfor (var j = 0; j < sarr.length; j++)\r\n\t\t\t\t\t\tfor (var k = 0; k < arr[i].length; k++) {\r\n\t\t\t\t\t\t\ttarr.push(sarr[j].concat(arr[i][k]));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\tsarr = tarr;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(sarr);\r\n\t\t\t\tconsole.log(' ------ ');\r\n\r\n\t\t\t\tfor (var i = 0; i < sarr.length; i++) {\r\n\t\t\t\t\tvar ks = [];\r\n\t\t\t\t\tvar titles = [];\r\n\t\t\t\t\tfor (var j = 0; j < sarr[i].length; j++) {\r\n\t\t\t\t\t\tks.push(sarr[i][j].k);\r\n\t\t\t\t\t\ttitles.push(sarr[i][j].title);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tks = ks.join(',');\r\n\t\t\t\t\ttitles = titles.join(',');\r\n\t\t\t\t\t//console.log(ks);\r\n\t\t\t\t\t//console.log(titles);\r\n\t\t\t\t\tif (typeof(oldgglist[ks]) != 'undefined') {\r\n\t\t\t\t\t\tvar val = oldgglist[ks];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar val = {\r\n\t\t\t\t\t\t\tks: ks,\r\n\t\t\t\t\t\t\tname: titles,\r\n\t\t\t\t\t\t\tmarket_price: '',\r\n\t\t\t\t\t\t\tcost_price: '',\r\n\t\t\t\t\t\t\tsell_price: '',\r\n\t\t\t\t\t\t\tweight: '100',\r\n\t\t\t\t\t\t\tstock: '1000',\r\n\t\t\t\t\t\t\tpic: '',\r\n\t\t\t\t\t\t\tgivescore: '0',\r\n\t\t\t\t\t\t\tlvprice_data: null\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tgglist.push(val);\r\n\t\t\t\t}\r\n\t\t\t\tthis.gglist = gglist;\r\n\t\t\t\tconsole.log(gglist);\r\n\t\t\t},\r\n\t\t\taddgggroupname: function(e) {\r\n\t\t\t\tthis.ggname = '';\r\n\t\t\t\tthis.ggindex = -1;\r\n\t\t\t\tthis.$refs.dialogInput2.open();\r\n\t\t\t},\r\n\t\t\tdelgggroupname: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['修改', '删除'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\t\tthat.$refs.dialogInput2.open();\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t} else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\t\tfor (var i in guigedata) {\r\n\t\t\t\t\t\t\t\t\tif (i != ggindex) {\r\n\t\t\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\t\tconsole.log(newguigedata);\r\n\t\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetgggroupname: function(done, val) {\r\n\t\t\t\tvar guigedata = this.guigedata;\r\n\t\t\t\tvar ggindex = this.ggindex;\r\n\t\t\t\tif (ggindex == -1) { //新增规格分组\r\n\t\t\t\t\tggindex = guigedata.length;\r\n\t\t\t\t\tguigedata.push({\r\n\t\t\t\t\t\tk: ggindex,\r\n\t\t\t\t\t\ttitle: val,\r\n\t\t\t\t\t\titems: []\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t\t} else { //修改规格分组名称\r\n\t\t\t\t\tguigedata[ggindex].title = val;\r\n\t\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.dialogInput2.close();\r\n\t\t\t\tthis.getgglist();\r\n\t\t\t},\r\n\t\t\taddggname: function(e) {\r\n\t\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\t\tthis.ggname = '';\r\n\t\t\t\tthis.ggindex = ggindex;\r\n\t\t\t\tthis.ggindex2 = -1;\r\n\t\t\t\tthis.$refs.dialogInput.open();\r\n\t\t\t},\r\n\t\t\tdelggname: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\t\tvar ggindex2 = e.currentTarget.dataset.index2;\r\n\t\t\t\tvar k = e.currentTarget.dataset.k;\r\n\t\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['修改', '删除'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\t\tthat.ggindex2 = ggindex2;\r\n\t\t\t\t\t\t\t\tthat.$refs.dialogInput.open();\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t} else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\t\tfor (var i in guigedata) {\r\n\t\t\t\t\t\t\t\t\tif (i == ggindex) {\r\n\t\t\t\t\t\t\t\t\t\tvar newitems = [];\r\n\t\t\t\t\t\t\t\t\t\tvar index2 = 0;\r\n\t\t\t\t\t\t\t\t\t\tfor (var j in guigedata[i].items) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (j != ggindex2) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tnewitems.push({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tk: index2,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: guigedata[i].items[j].title\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\tindex2++;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tguigedata[i].items = newitems;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\t\tconsole.log(newguigedata)\r\n\t\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetggname: function(done, val) {\r\n\t\t\t\tvar guigedata = this.guigedata;\r\n\t\t\t\tvar ggindex = this.ggindex;\r\n\t\t\t\tvar ggindex2 = this.ggindex2;\r\n\t\t\t\tif (ggindex2 == -1) { //新增规格名称\r\n\t\t\t\t\tvar items = guigedata[ggindex].items;\r\n\t\t\t\t\tggindex2 = items.length;\r\n\t\t\t\t\titems.push({\r\n\t\t\t\t\t\tk: ggindex2,\r\n\t\t\t\t\t\ttitle: val\r\n\t\t\t\t\t});\r\n\t\t\t\t\tguigedata[ggindex].items = items;\r\n\t\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t\t} else { //修改规格名称\r\n\t\t\t\t\tguigedata[ggindex].items[ggindex2].title = val;\r\n\t\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.dialogInput.close();\r\n\t\t\t\tthis.getgglist();\r\n\t\t\t},\r\n\t\t\tcidsChange: function(e) {\r\n\t\t\t\tvar clist = this.clist;\r\n\t\t\t\tvar cids = this.cids;\r\n\t\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar newcids = [];\r\n\t\t\t\tvar ischecked = false;\r\n\t\t\t\tfor (var i in cids) {\r\n\t\t\t\t\tif (cids[i] != cid) {\r\n\t\t\t\t\t\tnewcids.push(cids[i]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tischecked = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (ischecked == false) {\r\n\t\t\t\t\tif (newcids.length >= 5) {\r\n\t\t\t\t\t\tapp.error('最多只能选择五个分类');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewcids.push(cid);\r\n\t\t\t\t}\r\n\t\t\t\tthis.cids = newcids;\r\n\t\t\t\tthis.getcnames();\r\n\t\t\t},\r\n\t\t\tgetcnames: function() {\r\n\t\t\t\tvar cateArr = this.cateArr;\r\n\t\t\t\tvar cids = this.cids;\r\n\t\t\t\tvar cnames = [];\r\n\t\t\t\tfor (var i in cids) {\r\n\t\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t\t}\r\n\t\t\t\tthis.cnames = cnames.join(',');\r\n\t\t\t},\r\n\t\t\tcids2Change: function(e) {\r\n\t\t\t\tvar clist = this.clist2;\r\n\t\t\t\tvar cids = this.cids2;\r\n\t\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar newcids = [];\r\n\t\t\t\tvar ischecked = false;\r\n\t\t\t\tfor (var i in cids) {\r\n\t\t\t\t\tif (cids[i] != cid) {\r\n\t\t\t\t\t\tnewcids.push(cids[i]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tischecked = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (ischecked == false) {\r\n\t\t\t\t\tif (newcids.length >= 5) {\r\n\t\t\t\t\t\tapp.error('最多只能选择五个分类');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewcids.push(cid);\r\n\t\t\t\t}\r\n\t\t\t\tthis.cids2 = newcids;\r\n\t\t\t\tthis.getcnames2();\r\n\t\t\t},\r\n\t\t\tgetcnames2: function() {\r\n\t\t\t\tvar cateArr = this.cateArr2;\r\n\t\t\t\tvar cids = this.cids2;\r\n\t\t\t\tvar cnames = [];\r\n\t\t\t\tfor (var i in cids) {\r\n\t\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t\t}\r\n\t\t\t\tthis.cnames2 = cnames.join(',');\r\n\t\t\t},\r\n\t\t\tgidsChange: function(e) {\r\n\t\t\t\tvar glist = this.glist;\r\n\t\t\t\tvar gids = this.gids;\r\n\t\t\t\tvar gid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar newgids = [];\r\n\t\t\t\tvar ischecked = false;\r\n\t\t\t\tfor (var i in gids) {\r\n\t\t\t\t\tif (gids[i] != gid) {\r\n\t\t\t\t\t\tnewgids.push(gids[i]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tischecked = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (ischecked == false) {\r\n\t\t\t\t\tnewgids.push(gid);\r\n\t\t\t\t}\r\n\t\t\t\tthis.gids = newgids;\r\n\t\t\t\tthis.getgnames();\r\n\t\t\t},\r\n\t\t\tgetgnames: function() {\r\n\t\t\t\tvar groupArr = this.groupArr;\r\n\t\t\t\tvar gids = this.gids;\r\n\t\t\t\tvar gnames = [];\r\n\t\t\t\tfor (var i in gids) {\r\n\t\t\t\t\tgnames.push(groupArr[gids[i]]);\r\n\t\t\t\t}\r\n\t\t\t\tthis.gnames = gnames.join(',');\r\n\t\t\t},\r\n\t\t\tchangeClistDialog: function() {\r\n\t\t\t\tthis.clistshow = !this.clistshow\r\n\t\t\t},\r\n\t\t\tchangeClist2Dialog: function() {\r\n\t\t\t\tthis.clist2show = !this.clist2show\r\n\t\t\t},\r\n\t\t\tchangeGlistDialog: function() {\r\n\t\t\t\tthis.glistshow = !this.glistshow\r\n\t\t\t},\r\n\t\t\tuploadimg: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\t\tif (!pernum) pernum = 1;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field\r\n\t\t\t\tvar pics = that[field]\r\n\t\t\t\tif (!pics) pics = [];\r\n\t\t\t\tapp.chooseImage(function(urls) {\r\n\t\t\t\t\tfor (var i = 0; i < urls.length; i++) {\r\n\t\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (field == 'pic') that.pic = pics;\r\n\t\t\t\t\tif (field == 'pics') that.pics = pics;\r\n\t\t\t\t}, pernum);\r\n\t\t\t},\r\n\t\t\tremoveimg: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tvar field = e.currentTarget.dataset.field\r\n\t\t\t\tif (field == 'pic') {\r\n\t\t\t\t\tvar pics = that.pic\r\n\t\t\t\t\tpics.splice(index, 1);\r\n\t\t\t\t\tthat.pic = pics;\r\n\t\t\t\t} else if (field == 'pics') {\r\n\t\t\t\t\tvar pics = that.pics\r\n\t\t\t\t\tpics.splice(index, 1);\r\n\t\t\t\t\tthat.pics = pics;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tuploadimg2: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tapp.chooseImage(function(urls) {\r\n\t\t\t\t\tthat.gglist[index].pic = urls[0];\r\n\t\t\t\t}, 1);\r\n\t\t\t},\r\n\t\t\tremoveimg2: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\t\tthat.gglist[index].pic = '';\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"less\">\r\n\tradio {\r\n\t\ttransform: scale(0.6);\r\n\t}\r\n\r\n\tcheckbox {\r\n\t\ttransform: scale(0.6);\r\n\t}\r\n\r\n\t.form-box {\r\n\t\tpadding: 2rpx 24rpx 0 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin: 24rpx;\r\n\t\tborder-radius: 10rpx\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tline-height: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #eee\r\n\t}\r\n\r\n\t.form-item .f1 {\r\n\t\tcolor: #222;\r\n\t\twidth: 200rpx;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.form-item .f2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.form-box .form-item:last-child {\r\n\t\tborder: none\r\n\t}\r\n\r\n\t.form-box .flex-col {\r\n\t\tpadding-bottom: 20rpx\r\n\t}\r\n\r\n\t.form-item input {\r\n\t\twidth: 100%;\r\n\t\tborder: none;\r\n\t\tcolor: #111;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: right\r\n\t}\r\n\r\n\t.form-item textarea {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 200rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.form-item .upload_pic {\r\n\t\tmargin: 50rpx 0;\r\n\t\tbackground: #F3F3F3;\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.form-item .upload_pic image {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.savebtn {\r\n\t\twidth: 90%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: bold;\r\n\t\tmargin: 0 5%;\r\n\t\tmargin-top: 60rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.ggtitle {\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #111;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1px solid #f4f4f4\r\n\t}\r\n\r\n\t.ggtitle .t1 {\r\n\t\twidth: 200rpx;\r\n\t}\r\n\r\n\t.ggcontent {\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\tcolor: #111;\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.ggcontent .t1 {\r\n\t\twidth: 200rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.ggcontent .t1 .edit {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx\r\n\t}\r\n\r\n\t.ggcontent .t2 {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.ggcontent .ggname {\r\n\t\tbackground: #f55;\r\n\t\tcolor: #fff;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.ggcontent .ggname .close {\r\n\t\tposition: absolute;\r\n\t\ttop: -14rpx;\r\n\t\tright: -14rpx;\r\n\t\tbackground: #fff;\r\n\t\theight: 28rpx;\r\n\t\twidth: 28rpx;\r\n\t\tborder-radius: 14rpx\r\n\t}\r\n\r\n\t.ggcontent .ggnameadd {\r\n\t\tbackground: #ccc;\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #fff;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.ggcontent .ggadd {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #558\r\n\t}\r\n\r\n\t.ggbox {\r\n\t\tline-height: 50rpx;\r\n\t}\r\n\r\n\r\n\t.layui-imgbox {\r\n\t\tmargin-right: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.layui-imgbox-close {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tright: -16rpx;\r\n\t\ttop: -16rpx;\r\n\t\tz-index: 5;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 50%\r\n\t}\r\n\r\n\t.layui-imgbox-close image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.layui-imgbox-img {\r\n\t\tdisplay: block;\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tpadding: 2px;\r\n\t\tborder: #d3d3d3 1px solid;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.layui-imgbox-img>image {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.layui-imgbox-repeat {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tright: 2px;\r\n\t\tbottom: 2px;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 30rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.uploadbtn {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx\r\n\t}\r\n\r\n\r\n\t.clist-item {\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\r\n\t.radio {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder: 2rpx solid #BFBFBF;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 30rpx\r\n\t}\r\n\r\n\t.radio .radio-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block\r\n\t}\r\n\r\n\t.freightitem {\r\n\t\twidth: 100%;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-left: 40rpx\r\n\t}\r\n\r\n\t.freightitem .f1 {\r\n\t\tcolor: #666;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.detailop {\r\n\t\tdisplay: flex;\r\n\t\tline-height: 60rpx\r\n\t}\r\n\r\n\t.detailop .btn {\r\n\t\tborder: 1px solid #ccc;\r\n\t\tmargin-right: 10rpx;\r\n\t\tpadding: 0 16rpx;\r\n\t\tcolor: #222;\r\n\t\tborder-radius: 10rpx\r\n\t}\r\n\r\n\t.detaildp {\r\n\t\tposition: relative;\r\n\t\tline-height: 50rpx\r\n\t}\r\n\r\n\t.detaildp .op {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-top: 10rpx\r\n\t}\r\n\r\n\t.detaildp .op .btn {\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tmargin-right: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tcolor: #fff\r\n\t}\r\n\r\n\t.detaildp .detailbox {\r\n\t\tborder: 2px dashed #00a0e9\r\n\t}\r\n\r\n\t.uni-popup-dialog {\r\n\t\twidth: 300px;\r\n\t\tborder-radius: 5px;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.uni-dialog-title {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\tpadding-top: 15px;\r\n\t\tpadding-bottom: 5px;\r\n\t}\r\n\r\n\t.uni-dialog-title-text {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.uni-dialog-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 5px 15px 15px 15px;\r\n\t}\r\n\r\n\t.uni-dialog-content-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #6e6e6e;\r\n\t}\r\n\r\n\t.uni-dialog-button-group {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tborder-top-color: #f5f5f5;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 1px;\r\n\t}\r\n\r\n\t.uni-dialog-button {\r\n\t\tdisplay: flex;\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 45px;\r\n\t}\r\n\r\n\t.uni-border-left {\r\n\t\tborder-left-color: #f0f0f0;\r\n\t\tborder-left-style: solid;\r\n\t\tborder-left-width: 1px;\r\n\t}\r\n\r\n\t.uni-dialog-button-text {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.uni-button-color {\r\n\t\tcolor: #007aff;\r\n\t}\r\n\r\n\t.uni-list-cell {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-start;\r\n\t\tpadding: 5px 10px;\r\n\t}\r\n\r\n\r\n\t.uni-list {\r\n\t\theight: 270px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\r\n\t.list {\r\n\t\tcolumn-count: 2;\r\n\t\tbox-sizing: content-box;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.pbl {\r\n\t\twidth: 100%;\r\n\t\tbreak-inside: avoid;\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 5px;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t}\r\n\r\n\t\t.image {\r\n\t\t\twidth: 100%;\r\n\t\t\tborder-radius: 5px;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t&>image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tmargin-bottom: 6rpx;\r\n\t\t\tdisplay: -webkit-box;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\toverflow: hidden;\r\n\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t-webkit-line-clamp: 2; //当属性值为3，表示超出3行隐\r\n\t\t\tpadding: 5px 10px;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.more {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tcolor: #9499aa;\r\n\t\t\tmargin-bottom: 6rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/* 多行省略 */\r\n\t.multi-line-omit {\r\n\t\tword-break: break-all; // 允许单词内自动换行，如果一个单词很长的话\r\n\t\ttext-overflow: ellipsis; // 溢出用省略号显示\r\n\t\toverflow: hidden; // 超出的文本隐藏\r\n\t\tdisplay: -webkit-box; // 作为弹性伸缩盒子模型显示\r\n\t\t-webkit-line-clamp: 2; // 显示的行\r\n\t\t-webkit-box-orient: vertical; // 设置伸缩盒子的子元素排列方式--从上到下垂直排列\r\n\t}\r\n\r\n\t/* 单行省略 */\r\n\t.one-line-omit {\r\n\t\twidth: 100%; // 宽度100%：1vw等于视口宽度的1%；1vh等于视口高度的1%\r\n\t\twhite-space: nowrap; // 溢出不换行\r\n\t\toverflow: hidden; // 超出的文本隐藏\r\n\t\ttext-overflow: ellipsis; // 溢出用省略号显示\r\n\t}\r\n\r\n\t.p1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t\tpadding: 5px 10px; \r\n\t}\r\n\t\r\n\t.search-bar {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin: 0 10px;\r\n\t}\r\n\t\r\n\t.back-icon {\r\n\t\twidth: 18px;\r\n\t\theight: 18px;\r\n\t}\r\n\t\r\n\t.search-container {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 23rpx;\r\n\t\tbackground: #5AA37B;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tflex: 1;\r\n\t/* \tmargin-left: 15px; */\r\n\t}\r\n\t\r\n\t.search-box .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\t\r\n\t.search-box .search-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #C2C2C2;\r\n\t\twidth: 100%;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuanedit.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuanedit.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115055951\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}