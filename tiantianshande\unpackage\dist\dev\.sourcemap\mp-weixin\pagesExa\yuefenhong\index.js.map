{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?7cd1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?9229", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?e569", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?a060", "uni-app:///pagesExa/yuefenhong/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?3293", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/yuefenhong/index.vue?67f6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activities", "page", "limit", "loading", "nodata", "nomore", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getActivities", "app", "aid", "that", "list", "count", "uni", "formatTime", "getStatusClass", "getStatusText", "goToDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoD9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MAEA;MACA;MAEAC;MACAA;QACAC;QACAX;QACAC;MACA;QACAW;QACAF;QAEA;UACA;YAAAG;YAAAC;UAEA;YACAF;YACAA;UACA;YACAA;UACA;UAEAA;UACAG;QACA;UACAL;QACA;MACA;IACA;IACAM;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAAy7C,CAAgB,o4CAAG,EAAC,C;;;;;;;;;;;ACA78C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/yuefenhong/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/yuefenhong/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=22d1bcd0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=22d1bcd0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"22d1bcd0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/yuefenhong/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=22d1bcd0&scoped=true&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.activities, function (activity, index) {\n    var $orig = _vm.__get_orig(activity)\n    var m0 = _vm.formatTime(activity.createtime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"yuefenhong-container\">\r\n    <!-- 页面标题 -->\r\n   <!-- <view class=\"page-title\">\r\n      <text>月分红活动</text>\r\n    </view> -->\r\n    \r\n    <!-- 活动列表区 -->\r\n    <view class=\"activities-list\">\r\n      <view class=\"activities-container\">\r\n        <view \r\n          v-for=\"(activity, index) in activities\" \r\n          :key=\"index\" \r\n          class=\"activity-card\"\r\n         \r\n        >\r\n          <view class=\"card-header\">\r\n            <text class=\"activity-title\">{{activity.title}}</text>\r\n            <!-- 状态已移除 -->\r\n          </view>\r\n          <view class=\"card-body\">\r\n            <view class=\"ratio-display\">\r\n              <text class=\"ratio-value\">{{activity.fenhong_ratio}}</text>\r\n              <text class=\"ratio-label\">分红比例</text>\r\n            </view>\r\n            <view class=\"pool-info\">\r\n              <text class=\"pool-amount\">¥{{activity.pool_amount}}</text>\r\n              <text class=\"pool-label\">奖金池金额</text>\r\n            </view>\r\n           <!-- <view class=\"participants-info\">\r\n              <text class=\"participants-count\">{{activity.member_count || 0}}</text>\r\n              <text class=\"participants-label\">参与人数</text>\r\n            </view> -->\r\n          </view>\r\n          <view class=\"card-footer\">\r\n            <text class=\"create-time\">创建时间: {{formatTime(activity.createtime)}}</text>\r\n           <!-- <view class=\"action-buttons\">\r\n              <button class=\"detail-btn\" @click.stop=\"goToDetail(activity.id)\">查看详情</button>\r\n            </view> -->\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载状态组件 -->\r\n      <nodata v-if=\"nodata\"></nodata>\r\n      <nomore v-if=\"nomore\"></nomore>\r\n      <loading v-if=\"loading\"></loading>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      activities: [],\r\n      page: 1,\r\n      limit: 10,\r\n      loading: false,\r\n      nodata: false,\r\n      nomore: false\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.getActivities()\r\n  },\r\n  onPullDownRefresh() {\r\n    this.page = 1\r\n    this.getActivities()\r\n  },\r\n  onReachBottom() {\r\n    if (!this.nomore && !this.nodata) {\r\n      this.page = this.page + 1\r\n      this.getActivities(true)\r\n    }\r\n  },\r\n  methods: {\r\n    getActivities(loadmore) {\r\n      if (!loadmore) {\r\n        this.page = 1\r\n        this.activities = []\r\n      }\r\n      \r\n      this.loading = true\r\n      const that = this\r\n      \r\n      app.showLoading('加载中')\r\n      app.post('ApiYuefenhong/getList', {\r\n        aid: app.aid,\r\n        page: this.page,\r\n        limit: this.limit\r\n      }, function(res) {\r\n        that.loading = false\r\n        app.showLoading(false)\r\n        \r\n        if (res.code === 1) {\r\n          const { list, count } = res.data\r\n          \r\n          if (that.page === 1) {\r\n            that.activities = list\r\n            that.nodata = list.length === 0\r\n          } else {\r\n            that.activities = [...that.activities, ...list]\r\n          }\r\n          \r\n          that.nomore = that.activities.length >= count\r\n          uni.stopPullDownRefresh()\r\n        } else {\r\n          app.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      return time.substring(0, 16) // 只显示到分钟\r\n    },\r\n    getStatusClass(activity) {\r\n      // 这里根据实际状态判断逻辑修改\r\n      return activity.status === 1 ? 'active' : 'ended'\r\n    },\r\n    getStatusText(activity) {\r\n      // 这里根据实际状态判断逻辑修改\r\n      return activity.status === 1 ? '进行中' : '已结束'\r\n    },\r\n    goToDetail(id) {\r\n      app.goto(`/pagesExa/yuefenhong/detail?id=${id}`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.yuefenhong-container {\r\n  min-height: 100vh;\r\n  background: #f8f9fd;\r\n  padding: 20rpx;\r\n  \r\n  /deep/ .nodata, /deep/ .nomore, /deep/ .loading {\r\n    position: relative;\r\n    margin: 30rpx auto;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n  \r\n  text {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #1f2937;\r\n    position: relative;\r\n    display: inline-block;\r\n    padding-bottom: 16rpx;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 60rpx;\r\n      height: 6rpx;\r\n      background: linear-gradient(90deg, #6366f1, #8b5cf6);\r\n      border-radius: 3rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.activities-list {\r\n  .activities-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20rpx;\r\n  }\r\n  \r\n  .activity-card {\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: translateY(-5rpx);\r\n      box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.12);\r\n    }\r\n    \r\n    .card-header {\r\n      padding: 24rpx;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      border-bottom: 2rpx solid #f3f4f6;\r\n      \r\n      .activity-title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #1f2937;\r\n        flex: 1;\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n    }\r\n    \r\n    .card-body {\r\n      padding: 30rpx 24rpx;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      \r\n      .ratio-display {\r\n        text-align: center;\r\n        \r\n        .ratio-value {\r\n          font-size: 48rpx;\r\n          font-weight: bold;\r\n          color: #6366f1;\r\n          display: block;\r\n          margin-bottom: 10rpx;\r\n        }\r\n        \r\n        .ratio-label {\r\n          font-size: 24rpx;\r\n          color: #6b7280;\r\n        }\r\n      }\r\n      \r\n      .pool-info, .participants-info {\r\n        text-align: center;\r\n        \r\n        .pool-amount, .participants-count {\r\n          font-size: 36rpx;\r\n          font-weight: bold;\r\n          color: #1f2937;\r\n          display: block;\r\n          margin-bottom: 10rpx;\r\n        }\r\n        \r\n        .pool-label, .participants-label {\r\n          font-size: 24rpx;\r\n          color: #6b7280;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .card-footer {\r\n      padding: 24rpx;\r\n      border-top: 2rpx solid #f3f4f6;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      flex-wrap: wrap;\r\n      \r\n      .create-time {\r\n        font-size: 24rpx;\r\n        color: #6b7280;\r\n      }\r\n      \r\n      .action-buttons {\r\n        .detail-btn {\r\n          font-size: 26rpx;\r\n          padding: 12rpx 30rpx;\r\n          border-radius: 40rpx;\r\n          background: #6366f1;\r\n          color: #fff;\r\n          box-shadow: 0 4rpx 8rpx rgba(99, 102, 241, 0.2);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=22d1bcd0&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=22d1bcd0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115083313\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}