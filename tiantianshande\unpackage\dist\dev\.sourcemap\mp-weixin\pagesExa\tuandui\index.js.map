{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?b485", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?faf5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?8833", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?cafa", "uni-app:///pagesExa/tuandui/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?4761", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/index.vue?5e3e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "userRewardInfo", "activityList", "selectedActivityId", "onLoad", "onPullDownRefresh", "onShow", "methods", "getdata", "that", "app", "getUserRewardInfo", "params", "algorithmText", "uni", "title", "selectActivity", "console", "gotoDetail", "switchActivity", "claimReward", "gotoRecords"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6H9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;;MAEA;MACAC;QACA;UACAD;UACA;UACA;YACAA;UACA;QACA;QACA;QACAA;MACA;IACA;IAEAE;MACA;MACA;MACA;QACAC;MACA;MAEAF;QACAD;QACA;UACAA;UACAA;UACA;UACA;UACA;YACAI;UACA;UACAC;YACAC;UACA;QACA;UACAL;UACAD;QACA;QACAK;MACA;IACA;IAEAE;MACA;MACA;MACAC;;MAEA;MACAP;IACA;IAEAQ;MACA;MACA;MACAD;;MAEA;MACAP;IACA;IAEAS;MACA;MACA;MACAF;MAEAR;MACAA;IACA;IAEAW;MACA;MACA;MAEAH;;MAEA;MACA;IACA;IAEAI;MACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuandui/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuandui/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=13c1a8b6&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuandui/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=13c1a8b6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view class=\"container\">\n\t\t\t<view class=\"header\">\n\t\t\t\t<text class=\"title\">团队业绩奖励</text>\n\t\t\t</view>\n\n\t\t\t<!-- 用户奖励信息 -->\n\t\t\t<view class=\"reward-info-box\" v-if=\"userRewardInfo && userRewardInfo.activity_info\">\n\t\t\t\t<view class=\"section-title\">我的奖励信息</view>\n\t\t\t\t<view class=\"reward-card\">\n\t\t\t\t\t<view class=\"activity-title\">{{userRewardInfo.activity_info.title}}</view>\n\t\t\t\t\t<view class=\"algorithm-info\" v-if=\"userRewardInfo.activity_info.algorithm_type\">\n\t\t\t\t\t\t<text class=\"algorithm-tag\" :class=\"userRewardInfo.activity_info.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'\">\n\t\t\t\t\t\t\t{{userRewardInfo.activity_info.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法'}}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"performance-info\">\n\t\t\t\t\t\t<view class=\"perf-type\">业绩类型：{{userRewardInfo.activity_info.performance_type == 1 ? '团队金额' : '团队数量'}}</view>\n\t\t\t\t\t\t<view class=\"reward-type\">奖励类型：{{userRewardInfo.activity_info.reward_type == 1 ? '比例奖励' : '固定金额'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"team-performance\" v-if=\"userRewardInfo.user_performance && userRewardInfo.team_structure\">\n\t\t\t\t\t\t<view class=\"perf-value\">团队业绩：{{userRewardInfo.user_performance.team_performance}}{{userRewardInfo.activity_info.performance_type == 1 ? '元' : '件'}}</view>\n\t\t\t\t\t\t<view class=\"team-count\">团队成员：{{userRewardInfo.team_structure.total_members}}人</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stage-info\" v-if=\"userRewardInfo.current_stage\">\n\t\t\t\t\t\t<view class=\"current-stage\">当前阶段：{{userRewardInfo.current_stage.achievement}} ({{userRewardInfo.current_stage.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}})</view>\n\t\t\t\t\t\t<view class=\"next-stage\" v-if=\"userRewardInfo.next_stage\">下一阶段：{{userRewardInfo.next_stage.achievement}} ({{userRewardInfo.next_stage.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}})</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"reward-summary\" v-if=\"userRewardInfo.user_performance\">\n\t\t\t\t\t\t<view class=\"theoretical-reward\">理论奖励：¥{{userRewardInfo.user_performance.theoretical_reward}}</view>\n\t\t\t\t\t\t<view class=\"actual-reward\" v-if=\"userRewardInfo.activity_info.algorithm_type === 'layered_reduction'\">实际奖励：¥{{userRewardInfo.user_performance.actual_reward}}</view>\n\t\t\t\t\t\t<view class=\"calculation-detail\" v-if=\"userRewardInfo.user_performance.calculation_detail\">\n\t\t\t\t\t\t\t{{userRewardInfo.user_performance.calculation_detail}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 奖励详情列表 -->\n\t\t\t<view class=\"reward-details-box\" v-if=\"userRewardInfo && userRewardInfo.reward_details && userRewardInfo.activity_info\">\n\t\t\t\t<view class=\"section-title\">奖励详情</view>\n\t\t\t\t<view class=\"reward-details\">\n\t\t\t\t\t<block v-for=\"(item, index) in userRewardInfo.reward_details\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t\t\t\t<view class=\"achievement-target\">目标业绩：{{item.achievement_target}}</view>\n\t\t\t\t\t\t\t\t<view class=\"reward-value\">奖励：{{item.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"detail-content\">\n\t\t\t\t\t\t\t\t<view class=\"reward-amount-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"theoretical-amount\">理论奖励：¥{{item.theoretical_reward_amount}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"actual-amount\" v-if=\"userRewardInfo.activity_info.algorithm_type === 'layered_reduction' && item.actual_reward_amount !== item.theoretical_reward_amount\">\n\t\t\t\t\t\t\t\t\t\t实际奖励：¥{{item.actual_reward_amount}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"status-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"status\" :class=\"item.is_achieved ? (item.is_paid ? 'status-paid' : (item.is_claimed ? 'status-claimed' : 'status-achieved')) : 'status-not-achieved'\">{{item.is_achieved ? (item.is_paid ? '已发放' : (item.is_claimed ? '已领取' : '已达成')) : '未达成'}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" v-if=\"item.is_achieved && !item.is_claimed && !item.is_paid\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"claim-btn\" @tap=\"claimReward\" :data-activity=\"item.activity_id\" :data-level=\"item.achievement_level\">立即领取</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"calculation\" v-if=\"item.calculation\">{{item.calculation}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 活动列表 -->\n\t\t\t<view class=\"activity-list-box\">\n\t\t\t\t<view class=\"section-title\">所有活动</view>\n\t\t\t\t<view class=\"activity-list\">\n\t\t\t\t\t<block v-for=\"(item, index) in activityList\" :key=\"item.id\">\n\t\t\t\t\t\t<view class=\"activity-item\" @tap=\"selectActivity\" :data-id=\"item.id\">\n\t\t\t\t\t\t\t<view class=\"activity-header\">\n\t\t\t\t\t\t\t\t<text class=\"activity-title\">{{item.title}}</text>\n\t\t\t\t\t\t\t\t<text class=\"activity-time\">{{item.createtime}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"activity-info\">\n\t\t\t\t\t\t\t\t<view class=\"type-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"perf-type\">{{item.performance_name || (item.performance_type == 1 ? '团队金额' : '团队数量')}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"reward-type\">{{item.reward_name || (item.reward_type == 1 ? '比例奖励' : '固定金额')}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"algorithm-type\" :class=\"item.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'\">\n\t\t\t\t\t\t\t\t\t\t{{item.algorithm_name || (item.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法')}}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"participation-info\" v-if=\"item.user_participated\">\n\t\t\t\t\t\t\t\t\t<text class=\"user-performance\">我的业绩：{{item.user_performance}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"user-reward\">我的奖励：¥{{item.user_reward}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"activity-footer\">\n\t\t\t\t\t\t\t\t<view class=\"activity-status\">\n\t\t\t\t\t\t\t\t\t<text class=\"status-text\" :class=\"item.status == 1 ? 'status-active' : 'status-ended'\">{{item.status_text || (item.status == 1 ? '进行中' : '已结束')}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"activity-actions\">\n\t\t\t\t\t\t\t\t\t<text class=\"detail-btn\" @tap.stop=\"gotoDetail\" :data-id=\"item.id\">查看详情</text>\n\t\t\t\t\t\t\t\t\t<text class=\"switch-btn\" @tap.stop=\"switchActivity\" :data-id=\"item.id\" v-if=\"selectedActivityId != item.id\">切换</text>\n\t\t\t\t\t\t\t\t\t<text class=\"current-btn\" v-if=\"selectedActivityId == item.id\">当前</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 奖励记录入口 -->\n\t\t\t<view class=\"records-entry\">\n\t\t\t\t<view class=\"entry-btn\" @tap=\"gotoRecords\">\n\t\t\t\t\t<text>查看奖励记录</text>\n\t\t\t\t\t<image src=\"/static/img/arrow-right.png\" class=\"arrow-icon\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: false,\n\t\t\tisload: false,\n\t\t\tuserRewardInfo: null,\n\t\t\tactivityList: [],\n\t\t\tselectedActivityId: 0\n\t\t};\n\t},\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.selectedActivityId = opt.activity_id || 0;\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShow: function() {\n\t\t// 页面显示时刷新数据\n\t\tif (this.isload) {\n\t\t\tthis.getUserRewardInfo();\n\t\t}\n\t},\n\tmethods: {\n\t\tgetdata: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\t// 获取活动列表\n\t\t\tapp.get('ApiTuandui/getActivityList', {}, function(res) {\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.activityList = res.data || [];\n\t\t\t\t\t// 如果没有指定活动ID且有活动列表，使用第一个活动\n\t\t\t\t\tif (!that.selectedActivityId && that.activityList.length > 0) {\n\t\t\t\t\t\tthat.selectedActivityId = that.activityList[0].id;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 获取用户奖励信息\n\t\t\t\tthat.getUserRewardInfo();\n\t\t\t});\n\t\t},\n\t\t\n\t\tgetUserRewardInfo: function() {\n\t\t\tvar that = this;\n\t\t\tvar params = {};\n\t\t\tif (that.selectedActivityId > 0) {\n\t\t\t\tparams.activity_id = that.selectedActivityId;\n\t\t\t}\n\t\t\t\n\t\t\tapp.get('ApiTuandui/getUserRewardInfo', params, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.userRewardInfo = res.data;\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t\t// 安全访问 activity_info 属性\n\t\t\t\t\tvar algorithmText = '传统模式';\n\t\t\t\t\tif (res.data && res.data.activity_info && res.data.activity_info.algorithm_type === 'layered_reduction') {\n\t\t\t\t\t\talgorithmText = '分层递减';\n\t\t\t\t\t}\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: '团队业绩奖励 - ' + algorithmText\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tapp.alert(res.msg || '获取奖励信息失败');\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t}\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\t\n\t\tselectActivity: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar activityId = e.currentTarget.dataset.id;\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[index.vue][selectActivity_001] 点击活动ID:', activityId);\n\t\t\t\n\t\t\t// 跳转到活动详情页面\n\t\t\tapp.goto('/pagesExa/tuandui/detail?id=' + activityId);\n\t\t},\n\t\t\n\t\tgotoDetail: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar activityId = e.currentTarget.dataset.id;\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[index.vue][gotoDetail_001] 查看详情ID:', activityId);\n\t\t\t\n\t\t\t// 跳转到活动详情页面\n\t\t\tapp.goto('/pagesExa/tuandui/detail?id=' + activityId);\n\t\t},\n\t\t\n\t\tswitchActivity: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar activityId = e.currentTarget.dataset.id;\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[index.vue][switchActivity_001] 切换活动ID:', activityId);\n\t\t\t\n\t\t\tthat.selectedActivityId = activityId;\n\t\t\tthat.getUserRewardInfo();\n\t\t},\n\t\t\n\t\tclaimReward: function(e) {\n\t\t\tvar activityId = e.currentTarget.dataset.activity;\n\t\t\tvar level = e.currentTarget.dataset.level;\n\t\t\t\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[index.vue][claimReward_001] 领取奖励:', activityId, level);\n\t\t\t\n\t\t\t// TODO: 实现领取奖励逻辑\n\t\t\tthis.$refs.popmsg.show('提示', '领取奖励功能待实现');\n\t\t},\n\t\t\n\t\tgotoRecords: function() {\n\t\t\tapp.goto('/pagesExa/tuandui/records');\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n.header {\n\tbackground: linear-gradient(to right, #ff8c00, #ff5722);\n\tpadding: 30rpx 20rpx;\n\ttext-align: center;\n\tborder-bottom: none;\n}\n\n.header .title {\n\tfont-size: 38rpx;\n\tcolor: #fff;\n\tfont-weight: bold;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tfont-weight: 600;\n\tpadding: 25rpx 30rpx;\n\tbackground-color: transparent;\n\tborder-bottom: none;\n\tmargin-top: 20rpx;\n}\n\n/* 用户奖励信息样式 */\n.reward-info-box {\n\tmargin: 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.reward-card {\n\tpadding: 30rpx;\n}\n\n.reward-info-box .activity-title {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 15rpx;\n\ttext-align: center;\n}\n\n.algorithm-info {\n\ttext-align: center;\n\tmargin-bottom: 20rpx;\n}\n\n.algorithm-tag {\n\tfont-size: 24rpx;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n\tfont-weight: 500;\n}\n\n.algorithm-layered {\n\tbackground-color: #e3f2fd;\n\tcolor: #1976d2;\n}\n\n.algorithm-standard {\n\tbackground-color: #f3e5f5;\n\tcolor: #7b1fa2;\n}\n\n.performance-info,\n.team-performance,\n.stage-info,\n.reward-summary {\n\tmargin-bottom: 20rpx;\n\tfont-size: 28rpx;\n}\n\n.performance-info view,\n.team-performance view,\n.stage-info view {\n\tline-height: 1.7;\n\tcolor: #555;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 8rpx 0;\n}\n\n.reward-summary {\n\tborder-top: 1rpx solid #f0f0f0;\n\tpadding-top: 15rpx;\n}\n\n.theoretical-reward,\n.actual-reward {\n\tpadding: 8rpx 0;\n\tfont-weight: bold;\n}\n\n.theoretical-reward {\n\tcolor: #ff9800;\n}\n\n.actual-reward {\n\tcolor: #ff5722;\n}\n\n.calculation-detail {\n\tfont-size: 24rpx;\n\tcolor: #888;\n\tbackground-color: #f9f9f9;\n\tpadding: 10rpx;\n\tborder-radius: 8rpx;\n\tmargin-top: 10rpx;\n}\n\n.perf-value,\n.total-reward,\n.rewarded-amount,\n.pending-reward {\n\tcolor: #ff5722;\n\tfont-weight: bold;\n}\n\n.reward-summary .rewarded-amount {\n\tcolor: #4caf50;\n}\n\n.reward-summary .pending-reward {\n\tcolor: #ff9800;\n}\n\n/* 奖励详情样式 */\n.reward-details-box {\n\tmargin: 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.reward-details-box .section-title {\n\tmargin-top: 0;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.detail-item {\n\tpadding: 25rpx 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-item:last-child {\n\tborder-bottom: none;\n}\n\n.detail-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.achievement-target {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n\n.detail-header .reward-value {\n\tfont-size: 26rpx;\n\tcolor: #ff5722;\n\tfont-weight: bold;\n}\n\n.detail-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-bottom: 10rpx;\n}\n\n.reward-amount-info {\n\tflex: 1;\n}\n\n.theoretical-amount,\n.actual-amount {\n\tfont-size: 26rpx;\n\tfont-weight: bold;\n\tline-height: 1.4;\n}\n\n.theoretical-amount {\n\tcolor: #ff9800;\n}\n\n.actual-amount {\n\tcolor: #ff5722;\n}\n\n.status-info {\n\tdisplay: flex;\n\talign-items: center;\n\tflex-shrink: 0;\n\tmargin-left: 15rpx;\n}\n\n.status {\n\tfont-size: 22rpx;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 8rpx;\n\tmargin-right: 15rpx;\n\tfont-weight: 500;\n}\n\n.status-unachieved {\n\tbackground-color: #f0f0f0;\n\tcolor: #999;\n}\n\n.status-paid {\n\tbackground-color: #e8f5e9;\n\tcolor: #4caf50;\n}\n\n.status-pending {\n\tbackground-color: #fff8e1;\n\tcolor: #ff9800;\n}\n\n.status-claimable {\n\tbackground-color: #e3f2fd;\n\tcolor: #2196f3;\n}\n\n.claim-btn {\n\tbackground-color: #ff5722;\n\tcolor: #fff;\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n}\n\n.claim-btn:active {\n\tbackground-color: #e64a19;\n}\n\n.calculation {\n\tfont-size: 24rpx;\n\tcolor: #888;\n\tmargin-top: 10rpx;\n\tbackground-color: #f9f9f9;\n\tpadding: 10rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 活动列表样式 */\n.activity-list-box {\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\tbackground-color: #fff;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.activity-list-box .section-title {\n\tmargin-top: 0;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.activity-item {\n\tpadding: 25rpx 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\ttransition: background-color 0.2s;\n}\n\n.activity-item:last-child {\n\tborder-bottom: none;\n}\n\n.activity-item:active {\n\tbackground-color: #f9f9f9;\n}\n\n.activity-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.activity-item .activity-title {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.activity-time {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.activity-info {\n\tmargin-bottom: 10rpx;\n}\n\n.type-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.perf-type,\n.reward-type,\n.algorithm-type {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tmargin-right: 15rpx;\n\tpadding: 6rpx 12rpx;\n\tbackground-color: #f0f0f0;\n\tborder-radius: 8rpx;\n}\n\n.participation-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.user-performance,\n.user-reward {\n\tfont-size: 24rpx;\n\tcolor: #555;\n\tfont-weight: 500;\n}\n\n.user-reward {\n\tcolor: #ff5722;\n}\n\n.activity-footer {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-top: 10rpx;\n}\n\n.activity-status {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n}\n\n.status-text {\n\tfont-size: 24rpx;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 8rpx;\n\tfont-weight: 500;\n}\n\n.status-active {\n\tbackground-color: #e8f5e9;\n\tcolor: #4caf50;\n}\n\n.status-ended {\n\tbackground-color: #f0f0f0;\n\tcolor: #999;\n}\n\n.activity-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.detail-btn,\n.switch-btn,\n.current-btn {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 8rpx;\n\tmargin-left: 10rpx;\n}\n\n.detail-btn {\n\tbackground-color: #e3f2fd;\n}\n\n.switch-btn {\n\tbackground-color: #fff;\n}\n\n.current-btn {\n\tbackground-color: #e8f5e9;\n}\n\n/* 奖励记录入口 */\n.records-entry {\n\tmargin: 30rpx 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.entry-btn {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\ttransition: background-color 0.2s;\n}\n\n.entry-btn:active {\n\tbackground-color: #f9f9f9;\n}\n\n.arrow-icon {\n\twidth: 28rpx;\n\theight: 28rpx;\n\topacity: 0.7;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065337\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}