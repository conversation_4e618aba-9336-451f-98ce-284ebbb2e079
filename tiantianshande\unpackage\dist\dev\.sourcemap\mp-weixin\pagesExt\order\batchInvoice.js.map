{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?864a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?52e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?479a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?7ea8", "uni-app:///pagesExt/order/batchInvoice.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?7c80", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/batchInvoice.vue?6d7d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "orderIds", "totalAmount", "invoice_type", "invoice_type_select", "name_type_select", "name_type_personal_disabled", "formData", "invoice_name", "tax_no", "address", "tel", "bank_name", "bank_account", "mobile", "email", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "ids", "formSubmit", "order_ids", "invoice_data", "name_type", "setTimeout", "changeOrderType", "changeNameType", "back", "uni", "inArray"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsFrxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACAD;QACAA;MACA;IACA;IACAG;MACA;MACA;MAEA;QACAF;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEAA;MACA;QACAG;QACAC;UACAtB;UACAK;UACAkB;UACAjB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEAM;QACAA;QACAA;QACA;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACA;QACAR;QACAA;MACA;QACAA;MACA;MACAA;IACA;IACAS;MACA;MACA;MACAT;IACA;IACAU;MACAC;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/batchInvoice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/batchInvoice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./batchInvoice.vue?vue&type=template&id=bc1f4fb0&\"\nvar renderjs\nimport script from \"./batchInvoice.vue?vue&type=script&lang=js&\"\nexport * from \"./batchInvoice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./batchInvoice.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/batchInvoice.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batchInvoice.vue?vue&type=template&id=bc1f4fb0&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.orderIds.length : null\n  var m0 = _vm.isload ? _vm.inArray(1, _vm.invoice_type) : null\n  var m1 = _vm.isload ? _vm.inArray(2, _vm.invoice_type) : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batchInvoice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batchInvoice.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">选中订单数</text>\r\n\t\t\t\t\t<text class=\"t2\">{{orderIds.length}}个</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">可开票总金额</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{totalAmount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">发票类型</text>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeOrderType\" name=\"invoice_type\">\r\n\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(1,invoice_type)\">\r\n\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"invoice_type_select == 1 ? true : false\"></radio>普通发票\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(2,invoice_type)\">\r\n\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"invoice_type_select == 2 ? true : false\"></radio>增值税专用发票\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">抬头类型</text>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeNameType\" name=\"name_type\">\r\n\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"name_type_select == 1 ? true : false\" :disabled=\"name_type_personal_disabled ? true : false\"></radio>个人\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"name_type_select == 2 ? true : false\"></radio>公司\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">抬头名称</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"抬头名称\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"invoice_name\" v-model=\"formData.invoice_name\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"name_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">公司税号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"公司税号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tax_no\" v-model=\"formData.tax_no\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">注册地址</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册地址\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"address\" v-model=\"formData.address\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">注册电话</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册电话\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tel\" v-model=\"formData.tel\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">开户银行</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"开户银行\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_name\" v-model=\"formData.bank_name\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t<text class=\"t1\">银行账号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"银行账号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_account\" v-model=\"formData.bank_account\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">手机号</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票手机号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"mobile\" v-model=\"formData.mobile\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">邮箱</text>\r\n\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票邮箱\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"email\" v-model=\"formData.email\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"btn\" form-type=\"submit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t<view class=\"btn-a\" @tap=\"back\">返回上一步</view>\r\n\t\t\t<view style=\"padding-top:30rpx\"></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\torderIds: [],\r\n\t\t\ttotalAmount: 0,\r\n\t\t\tinvoice_type:[1,2],\r\n\t\t\tinvoice_type_select:1,\r\n\t\t\tname_type_select:1,\r\n\t\t\tname_type_personal_disabled:false,\r\n\t\t\tformData: {\r\n\t\t\t\tinvoice_name: '',\r\n\t\t\t\ttax_no: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tbank_name: '',\r\n\t\t\t\tbank_account: '',\r\n\t\t\t\tmobile: '',\r\n\t\t\t\temail: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt.ids) {\r\n\t\t\tthis.orderIds = this.opt.ids.split(',');\r\n\t\t}\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiOrder/batchInvoiceInfo', {ids: that.orderIds.join(',')}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.totalAmount = res.total_amount;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tformSubmit: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\r\n\t\t\tif(formdata.invoice_name == '') {\r\n\t\t\t\tapp.error('请填写抬头名称');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {\r\n\t\t\t\tapp.error('请填写公司税号');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(formdata.invoice_type == 2) {\r\n\t\t\t\tif(formdata.address == '') {\r\n\t\t\t\t\tapp.error('请填写注册地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.tel == '') {\r\n\t\t\t\t\tapp.error('请填写注册电话');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.bank_name == '') {\r\n\t\t\t\t\tapp.error('请填写开户银行');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.bank_account == '') {\r\n\t\t\t\t\tapp.error('请填写银行账号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (formdata.mobile != '') {\r\n\t\t\t\tif(!/^1[3456789]\\d{9}$/.test(formdata.mobile)){\r\n\t\t\t\t\tapp.error(\"手机号码有误，请重填\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (formdata.email != '') {\r\n\t\t\t\tif(!/^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$/.test(formdata.email)){\r\n\t\t\t\t\tapp.error(\"邮箱有误，请重填\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(formdata.mobile == '' && formdata.email == '') {\r\n\t\t\t\tapp.error(\"手机号和邮箱请填写其中一个\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tvar postData = {\r\n\t\t\t\torder_ids: that.orderIds,\r\n\t\t\t\tinvoice_data: {\r\n\t\t\t\t\tinvoice_type: formdata.invoice_type,\r\n\t\t\t\t\tinvoice_name: formdata.invoice_name,\r\n\t\t\t\t\tname_type: formdata.name_type,\r\n\t\t\t\t\ttax_no: formdata.tax_no,\r\n\t\t\t\t\taddress: formdata.address,\r\n\t\t\t\t\ttel: formdata.tel,\r\n\t\t\t\t\tbank_name: formdata.bank_name,\r\n\t\t\t\t\tbank_account: formdata.bank_account,\r\n\t\t\t\t\tmobile: formdata.mobile,\r\n\t\t\t\t\temail: formdata.email\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tapp.post('ApiOrder/batchInvoice', postData, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goto('/pagesExt/order/orderlist');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tchangeOrderType: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tif(value == 2) {\r\n\t\t\t\tthat.name_type_select = 2;\r\n\t\t\t\tthat.name_type_personal_disabled = true;\r\n\t\t\t} else {\r\n\t\t\t\tthat.name_type_personal_disabled = false;\r\n\t\t\t}\r\n\t\t\tthat.invoice_type_select = value;\r\n\t\t},\r\n\t\tchangeNameType: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tthat.name_type_select = value;\r\n\t\t},\r\n\t\tback:function(e) {\r\n\t\t\tuni.navigateBack({});\r\n\t\t},\r\n\t\tinArray: function(value, array) {\r\n\t\t\treturn app.inArray(value, array);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.radio radio{transform: scale(0.8);}\r\n.radio:nth-child(2) { margin-left: 30rpx;}\r\n.btn-a { text-align: center; padding: 30rpx; color: rgb(253, 74, 70);}\r\n.text-min { font-size: 24rpx; color: #999;}\r\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right; font-size: 28rpx;}\r\n.orderinfo .item .red{color:red}\r\n.orderinfo .item .grey{color:grey}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batchInvoice.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./batchInvoice.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103573\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}