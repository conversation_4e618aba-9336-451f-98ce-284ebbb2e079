{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?7ccc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?25a6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?136e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?35ca", "uni-app:///pagesExt/business/clist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?cfa2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/clist.vue?3735"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "pagenum", "datalist", "clist", "curIndex", "curCid", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "scrolltolower", "getblist", "longitude", "latitude", "cid", "uni", "switchRightTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsE9wB;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,yDACA,qDACA,kDACA;EAEA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;MACAC;QACAD;QACAA;QACAA;QACAC;UACA;UACA;UACAD;UACAA;UACAA;QACA,GACA;UACAA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAH;MACAA;MACAA;MACAC;QAAAG;QAAAC;QAAAd;QAAAe;MAAA;QACAN;QACAO;QACA;QACA;UACAP;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACA;MACA;MACAR;MACAA;MACAA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/clist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/clist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./clist.vue?vue&type=template&id=0ed46834&\"\nvar renderjs\nimport script from \"./clist.vue?vue&type=script&lang=js&\"\nexport * from \"./clist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./clist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/clist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=template&id=0ed46834&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view @tap.stop=\"goto\" data-url=\"/pagesExt/business/blist\" class=\"search-container\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t<view class=\"search-text\">搜索感兴趣的商家</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content-container\">\n\t\t\t<view class=\"nav_left\">\n\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == -1 ? 'active' : '')\" @tap=\"switchRightTab\" data-index=\"-1\" data-id=\"0\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>全部</view>\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view class=\"nav_right\">\n\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.id\">\n\t\t\t\t\t\t\t<image class=\"logo\" :src=\"item.logo\"></image>\n\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\"><block v-if=\"item.tel\">电话：<text style=\"font-weight:bold\">{{item.tel}}</text></block></view>\n\t\t\t\t\t\t\t\t<view class=\"f4\">地址：<text style=\"font-weight:bold\">{{item.address}}</text></view>\n\t\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.juli\">距离：{{item.juli}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<nomore text=\"没有更多商家了\" v-if=\"nomore\"></nomore>\n\t\t\t\t\t\t<nodata text=\"暂无相关商家\" v-if=\"nodata\"></nodata>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\" loadstyle=\"left:62.5%\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n\r\n<!-- <view class=\"container\" :class=\"menuindex>-1?'tabbarbot':''\">\r\n\t<view class=\"nav_left\">\r\n    <block v-for=\"(item, index) in clist\" :key=\"index\">\r\n      <view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\">\r\n      {{item.name}}\r\n      </view>\r\n    </block>\r\n  </view>\r\n  <view class=\"nav_right\">\r\n    <scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.id\">\r\n\t\t\t\t<image :src=\"item.logo\"></image>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"f2\"><block v-if=\"item.tel\"><text class=\"fa fa-phone\"></text> {{item.tel}}</block></view>\r\n\t\t\t\t\t<view class=\"f4\"><text class=\"fa fa-map-marker\"></text> {{item.address}}</view>\r\n\t\t\t\t\t<view class=\"f3\" v-if=\"item.juli\">距离：{{item.juli}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n    </scroll-view>\r\n  </view>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view> -->\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tnodata:false,\n\t\t\tnomore:false,\n      pagenum: 1,\n      datalist: [],\n      clist: [],\n      curIndex: -1,\n      curCid: 0,\n      nomore: false,\n      longitude: \"\",\n      latitude: \"\",\n      blist: \"\"\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.curCid = this.opt.cid || 0;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.pagenum = 1;\n\t\t\tthat.prolist = [];\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiBusiness/clist', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.clist = res.clist;\n\t\t\t\tthat.loaded();\n\t\t\t\tapp.getLocation(function (res) {\n\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\tthat.longitude = longitude;\n\t\t\t\t\tthat.latitude = latitude;\n\t\t\t\t\tthat.getblist();\n\t\t\t\t},\n\t\t\t\tfunction () {\n\t\t\t\t\tthat.getblist();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n    scrolltolower: function () {\n      if (!this.nodata && !this.nomore) {\n        this.pagenum = this.pagenum + 1;\n        this.getblist();\n      }\n    },\n    getblist: function () {\n      var that = this;\n      var pagenum = that.pagenum;\n      var cid = that.curCid;\n      var longitude = that.longitude;\n      var latitude = that.latitude;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiBusiness/clist', {longitude: longitude,latitude: latitude,pagenum: pagenum,cid: cid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n        var data = res.data;\n        if (pagenum == 1) {\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    //事件处理函数\n    switchRightTab: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = parseInt(e.currentTarget.dataset.index);\n      that.curIndex = index;\n      that.curCid = id;\n      that.pagenum = 1;\n      that.blist = [];\n      this.getblist();\n    }\n  }\n};\r\n</script>\r\n<style>\npage {height:100%;}\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\n\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\n.nav_left .nav_left_items{line-height:50rpx;color:#999999;font-size:24rpx;position: relative;padding:25rpx 30rpx;}\n.nav_left .nav_left_items.active{background: #fff;color:#222222;font-size:28rpx;font-weight:bold}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\n.nav_left .nav_left_items.active .before{display:block}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\n\n.nav_right .item{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f5f5f5 solid;position:relative}\n.nav_right .item:last-child{ border-bottom: 0; }\n.nav_right .item .logo{ width: 160rpx; height: 160rpx;}\n.nav_right .item .detail{width:100%;overflow:hidden;display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.nav_right .item .detail .f1{color:#323232;font-weight:bold;font-size:28rpx;line-height:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.nav_right .item .detail .f2{margin-top:6rpx;height: 40rpx;line-height: 40rpx;color: #888;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.nav_right .item .detail .f3{margin-top:6rpx;height: 40rpx;line-height: 40rpx;color: #31C88E;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.nav_right .item .detail .f4{margin-top:6rpx;line-height: 40rpx;color: #999;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.nav_right .item .detail .f5{margin-top:6rpx;display:flex;height: 35rpx;line-height: 35rpx;font-size:24rpx;color: #ff4246;font-size: 22rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n/*\n.nomore-footer-tips{background:#ffffff}\r\n.container {position:fixed;width: 100%;max-width:640px;background-color: #fff;color: #939393;top: 0;bottom: 0;}\r\n.nav_left{overflow-y: scroll;width: 25%;height: 100%;background: #f5f5f5; box-sizing: border-box;text-align: center;position: absolute; top:var(--window-top); left: 0;}\r\n.nav_left .nav_left_items{height:100rpx;line-height:100rpx;color:#666666;padding:0;border-bottom: 1px solid #E6E6E6;font-size:28rpx;position: relative;border-right:1px solid #E6E6E6}\r\n.nav_left .nav_left_items.active{background: #fff;color:#FC4343;border-left:3px solid #FC4343}\r\n\r\n.nav_right{position: absolute;display:flex;flex-direction:column;top: var(--window-top);right: 0;flex: 1;width:75%;height: 100%;padding:0px 20rpx 20rpx 20rpx ;background: #fff;box-sizing: border-box;overflow-y: hidden;}\r\n\r\n.classify-box{ width: 100%;overflow-y: scroll; height:100%;}\r\n.nav_right_items{ width:100%;border-bottom:1px #eeeeee solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.nav_right .item{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f5f5f5 solid;position:relative}\r\n.nav_right .item:last-child{ border-bottom: 0; }\r\n.nav_right .item .logo{ width: 160rpx; height: 160rpx;}\r\n.nav_right .item .detail{width:100%;overflow:hidden;display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.nav_right .item .detail .f1{height: 40rpx;line-height: 40rpx;color: #111;}\r\n.nav_right .item .detail .f2{height: 40rpx;line-height: 40rpx;color: #888;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f3{height: 40rpx;line-height: 40rpx;color: #31C88E;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f4{height: 40rpx;line-height: 40rpx;color: #999;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f5{display:flex;height: 35rpx;line-height: 35rpx;color: #ff4246;font-size: 22rpx}\r\n*/\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098452\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}