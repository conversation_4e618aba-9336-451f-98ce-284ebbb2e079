{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?356f", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?8b39", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?4288", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?2ca8", "uni-app:///yuyue/peolist2.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?5b5b", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist2.vue?d89d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "keyword", "datalist", "type", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "cid", "bid", "console", "isget", "getdatalist", "st", "pagenum", "field", "order", "subCid", "latitude", "longitude", "scrolltolower", "switchTopTab", "changeCTab", "<PERSON><PERSON><PERSON><PERSON>", "uni", "success", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8CjxB;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,kDACA,mDACA,qDACA,kDACA,oDACA,sDACA,qDACA,oDACA,sDACA,kDACA,kDACA;EAEA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;QACAA;QACAA;QACA;QACAI;QACA;UACA;YACA;cACAJ;cACAA;cACA;YACA;YACA;YACA;YACAA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAK;gBACA;cACA;YACA;YACA;UACA;QACA;QACAL;QACA;UACAA;QACA;UACAC;YACAD;YACAA;YACAA;UACA;QACA;MACA;IAEA;IACAM;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAN;MACAA;MACAA;MACA;MACA;QACA;MACA;MACAC;QAAAM;QAAAC;QAAAhB;QAAAiB;QAAAC;QAAAR;QAAAS;QAAAR;QAAAT;QAAAkB;QAAAC;MAAA;QACAb;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MAEA;IACA;IACAc;MACA;QACA;QACA;MAEA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QAEA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAb;MACA;MACAc;QACAC;UACAf;UACAJ;UACAA;UACAA;UACAA;QACA;QACAoB;UACAhB;UACA;YACA;YACAH;cACAiB;YACA;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtPA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/peolist2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/peolist2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peolist2.vue?vue&type=template&id=c8e515ac&\"\nvar renderjs\nimport script from \"./peolist2.vue?vue&type=script&lang=js&\"\nexport * from \"./peolist2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peolist2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/peolist2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist2.vue?vue&type=template&id=c8e515ac&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  var l1 =\n    _vm.isload && _vm.curIndex != \"-1\"\n      ? _vm.__map(_vm.clist[_vm.curIndex].child, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.curIndex2 == idx2 ? _vm.t(\"color1\") : null\n          var m3 = _vm.curIndex2 == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m4 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m4: m4,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist2.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topbox\">\r\n\t\t\t<view class=\"order-tab\">\r\n\t\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t\t\t<view :class=\"'item ' + (curIndex == -1 ? 'on' : '')\" @tap=\"switchTopTab\" data-index=\"-1\" data-id=\"0\">全部<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t\t<view :class=\"'item ' + (curIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.appid\">{{item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"classify-ul\" v-if=\"curIndex!='-1'\">\r\n\t\t\t\t<view class=\"libox flex\" style=\"width:100%;\">\r\n\t\t\t\t <block v-for=\"(item, idx2) in clist[curIndex].child\" :key=\"idx2\">\r\n\t\t\t\t <view class=\"classify-li\" :style=\"curIndex2==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeCTab\" :data-id=\"item.appid\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t </block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"content flex\" :style=\"curIndex==-1?'top: 100rpx;':'top:'+toppx\" >\r\n\t\t\t<view  v-for=\"(item, index) in datalist\" :key=\"index\" class=\"f1\" @click=\"goto\" :data-url=\"'peodetail2?id='+item.id\" >\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"item.avatar\" /></view>\r\n\t\t\t\t<view class=\"text1 flex\">\t\r\n\t\t\t\t\t<text class=\"t1\">{{item.name}} </text>\r\n\t\t\t\t\t<text class=\"t3\">{{item.distanceDesc}} </text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text3 flex\">\r\n\t\t\t\t\t\t<text class=\"t3\" v-if=\"curIndex==-1\">接单量:{{item.acceptOrderTotal}}  <text class=\"statusdesc\">{{item.statusDesc}}</text></text>\r\n\t\t\t\t\t\t<text class=\"t3\" v-else>{{item.priceDesc}} </text>\r\n\t\t\t\t\t\t<view class=\"yuyue\"  @click=\"goto\" :data-url=\"'/yuyue/peodetail2?id='+item.id\" :style=\"{background:t('color1')}\">预约</view>\r\n\t\t\t\t</view>\t\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n      keyword: '',\r\n      datalist: [],\r\n      type: \"\",\r\n\t\t\tkeyword:'',\r\n\t\t\tnodata:false,\r\n\t\t\tindex:0,\r\n\t\t\tcurCid:0,\r\n\t\t\tcurIndex: -1,\r\n\t\t\tcurIndex2:0,\r\n\t\t\tcursubCid:0,\r\n\t\t\tlatitude:'',\r\n\t\t\tlongitude:'',\r\n\t\t\tclist:[],\r\n\t\t\ttoppx:'100rpx'\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.type = this.opt.type || '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdatalist();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t  if (!this.nodata && !this.nomore) {\r\n\t    this.pagenum = this.pagenum + 1;\r\n\t    this.getdatalist(true);\r\n\t  }\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tvar bid = that.opt.bid || 0;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYuyue2/peocategory', {cid: nowcid,bid:bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.clist = data;\r\n\t\t\t\tthat.curCid = data[0]['appid'];\r\n\t\t\t\t//that.cursubCid = data[0]['child'][0]['appid'];\r\n\t\t\t\tconsole.log(nowcid);\r\n\t\t\t\tif (nowcid) {\r\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tif (data[i]['id'] == nowcid) {\r\n\t\t\t\t\t\t\tthat.curIndex = i;\r\n\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar downcdata = data[i]['child'];\r\n\t\t\t\t\t\tvar isget = 0;\r\n\t\t\t\t\t\tthat.cursubCid = downcdata[0]['appid'];\r\n\t\t\t\t\t\tfor (var j = 0; j < downcdata; j++) {\r\n\t\t\t\t\t\t\tif (downcdata[j]['id'] == nowcid) {\r\n\t\t\t\t\t\t\t\tthat.curIndex = i;\r\n\t\t\t\t\t\t\t\tthat.curIndex2 = j;\r\n\t\t\t\t\t\t\t\tthat.cursubCid = nowcid;\r\n\t\t\t\t\t\t\t\tisget = 1;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (isget) break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tif(app.globalData.platform=='h5'){\r\n\t\t\t\t\tthat.getdatalist();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\tthat.getdatalist();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t},\r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;  \r\n\t\t\tvar cid = that.curCid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar order = that.order;\r\n\t\t    var keyword = that.keyword;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tvar st = 0;\r\n\t\t\tif(this.curIndex=='-1'){\r\n\t\t\t\t\tvar st='all';\r\n\t\t\t}\r\n\t\t\tapp.post('ApiYuyue2/selectpeople', {st:st,pagenum: pagenum,keyword: keyword,field: field,order: order,cid: cid,subCid:that.cursubCid,bid:bid,type:'list',latitude:that.latitude,longitude:that.longitude}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nodata = true;\r\n\t\t\t\t  }\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nomore = true;\r\n\t\t\t\t  } else {\r\n\t\t\t\t    var datalist = that.datalist;\r\n\t\t\t\t    var newdata = datalist.concat(data);\r\n\t\t\t\t    that.datalist = newdata;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\tscrolltolower: function () {\r\n\t\t\tif (!this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t \r\n\t\t\t}\r\n\t\t \r\n\t\t},\r\n\t\t//事件处理函数\r\n\t\t switchTopTab: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = e.currentTarget.dataset.id; \r\n\t\t\t\tvar index = parseInt(e.currentTarget.dataset.index); \r\n\t\t\t\tthis.curIndex = index;\r\n\t\t\t\tif(index!='-1'){\r\n\t\t\t\t\tthis.curIndex2 = 0;\r\n\t\t\t\t\tthis.nodata = false;\r\n\t\t\t\t\tthis.curCid = id\t;\r\n\t\t\t\t\t//console.log(this.clist);\r\n\t\t\t\t\tthis.cursubCid = this.clist[index]['child'][0]['appid'];\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(this.clist[index]['child'].length<5 && this.clist[index]['child'].length>=1){\r\n\t\t\t\t\t\tthis.toppx='200rpx';\r\n\t\t\t\t\t} \r\n\t\t\t\t\tif(this.clist[index]['child'].length>5 && this.clist[index]['child'].length<=10){\r\n\t\t\t\t\t\t this.toppx='280rpx';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(this.clist[index]['child'].length>10) {\r\n\t\t\t\t\t\tthis.toppx='360rpx';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.pagenum = 1; \r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t\tthis.nomore = false;\r\n\t\t\t\tthis.getdatalist();\r\n\t\t},\r\n\tchangeCTab: function (e) {\r\n\t\tvar that = this;\r\n\t\tvar id = e.currentTarget.dataset.id;\r\n\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\tthis.curIndex2 = index;\r\n\t\tthis.nodata = false;\r\n\t\tthis.cursubCid = id;\r\n\t\tthis.pagenum = 1;\r\n\t\tthis.datalist = [];\r\n\t\tthis.nomore = false;\r\n\t\tthis.getdatalist();\r\n\t},\r\n\t\r\n\t\tselectzuobiao: function () {\r\n\t\t\tconsole.log('selectzuobiao')\r\n\t\t  var that = this;\r\n\t\t  uni.chooseLocation({\r\n\t\t    success: function (res) {\r\n\t\t      console.log(res);\r\n\t\t      that.area = res.address;\r\n\t\t      that.address = res.name;\r\n\t\t      that.latitude = res.latitude;\r\n\t\t      that.longitude = res.longitude;\r\n\t\t    },\r\n\t\t    fail: function (res) {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t      if (res.errMsg == 'chooseLocation:fail auth deny') {\r\n\t\t        //$.error('获取位置失败，请在设置中开启位置信息');\r\n\t\t        app.confirm('获取位置失败，请在设置中开启位置信息', function () {\r\n\t\t          uni.openSetting({});\r\n\t\t        });\r\n\t\t      }\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n  },\r\n\t\r\n};\r\n</script>\r\n<style>\r\n.topsearch{width:94%;margin:16rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.topbox{ position: fixed; top: 0; z-index: 1000;} \r\n\r\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\r\n.search-history {padding: 24rpx 34rpx;}\r\n.search-history .search-history-title {color: #666;}\r\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\r\n.search-history-list {padding: 24rpx 0 0 0;}\r\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\r\n\r\n\r\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}\r\n.order-tab2 .on .after{display:block}\r\n\r\n\r\n.content{width:100%;margin:20rpx 0; padding:0 20rpx;justify-content: space-between;  flex-wrap: wrap; position: absolute;}\r\n.content .f1{align-items:center;width:48%;background:#fff;padding: 20rpx;border-radius:10rpx; margin-bottom: 20rpx;}\r\n.content .f1 image{ width: 310rpx; height:  310rpx; border-radius: 10rpx;}\r\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:10rpx;}\r\n.content .f1 .t2{color:#999999;font-size:24rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}\r\n.content .f3{height:96rpx;display:flex;align-items:center}\r\n.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content .radio .radio-img{width:100%;height:100%}\r\n.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\r\n\r\n.text1 .t3{ color:red}\r\n.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 20rpx;justify-content: space-between; line-height: 40rpx;}\r\n.text3 .t5{ margin-left: 20rpx;}\r\n.text3 .t5 text{ color:#7A83EC}\r\n.text3 .t4 text{ color:#7A83EC}\r\n.text3 .t3{ line-height: 60rpx;}\r\n.yuyue{ background: #7A83EC; height: 40rpx; line-height: 40rpx; padding: 0 10rpx; color:#fff; border-radius:28rpx; width: 80rpx; font-size: 20rpx; text-align: center; margin-top: 10rpx;}\r\n.text1{ margin-left: 10rpx; justify-content: space-between; margin-top: 10rpx;}\r\n.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:20rpx;}\r\n\r\n.classify-ul{width:100%;padding:10rpx; background: #fff;}\r\n.libox{flex-wrap: wrap;}\r\n.classify-li{background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;\r\nmargin:15rpx 10rpx 24rpx 0}\r\n.statusdesc{ color:#06A051; margin-left: 10rpx; }\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115043994\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}