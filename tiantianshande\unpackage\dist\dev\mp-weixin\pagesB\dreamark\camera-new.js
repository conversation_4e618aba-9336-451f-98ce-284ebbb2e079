require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesB/dreamark/camera-new"],{

/***/ 7258:
/*!***********************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"pagesB%2Fdreamark%2Fcamera-new"} ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _cameraNew = _interopRequireDefault(__webpack_require__(/*! ./pagesB/dreamark/camera-new.vue */ 7259));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_cameraNew.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 7259:
/*!****************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./camera-new.vue?vue&type=template&id=368f168d&scoped=true& */ 7260);
/* harmony import */ var _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./camera-new.vue?vue&type=script&lang=js& */ 7262);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& */ 7264);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "368f168d",
  null,
  false,
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesB/dreamark/camera-new.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 7260:
/*!***********************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=template&id=368f168d&scoped=true& ***!
  \***********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=template&id=368f168d&scoped=true& */ 7261);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 7261:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=template&id=368f168d&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m1 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m2 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m3 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m4 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m5 = _vm.currentStep === "preview" ? _vm.t("color1") : null
  var m6 = _vm.currentStep === "preview" ? _vm.t("color1") : null
  var m7 = _vm.currentStep === "preview" ? _vm.t("color1rgb") : null
  var m8 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m9 = _vm.currentStep === "processing" ? _vm.t("color1rgb") : null
  var m10 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m11 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m12 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var l0 =
    _vm.currentStep === "processing"
      ? _vm.__map(_vm.processingSteps, function (step, index) {
          var $orig = _vm.__get_orig(step)
          var m13 = _vm.t("color1")
          var m14 = index <= _vm.currentProcessStep ? _vm.t("color1") : null
          var m15 = index <= _vm.currentProcessStep ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m13: m13,
            m14: m14,
            m15: m15,
          }
        })
      : null
  var m16 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m17 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m18 = _vm.currentStep === "processing" ? _vm.t("color1rgb") : null
  var m19 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m20 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m21 = _vm.currentStep === "result" ? _vm.t("color1rgb") : null
  var m22 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m23 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m24 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m25 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m26 =
    _vm.currentStep === "result" && !_vm.predictedImageLoaded
      ? _vm.t("color1")
      : null
  var m27 =
    _vm.currentStep === "result" && _vm.futureTalkEnabled
      ? _vm.t("color1")
      : null
  var m28 =
    _vm.currentStep === "result" && _vm.futureTalkEnabled
      ? _vm.t("color1")
      : null
  var m29 =
    _vm.currentStep === "result" && _vm.futureTalkEnabled
      ? _vm.t("color1")
      : null
  var m30 =
    _vm.currentStep === "result" && _vm.futureTalkEnabled
      ? _vm.t("color1")
      : null
  var l1 =
    _vm.currentStep === "result" && _vm.futureTalkEnabled
      ? _vm.__map(5, function (i, __i2__) {
          var $orig = _vm.__get_orig(i)
          var m31 = _vm.t("color1")
          return {
            $orig: $orig,
            m31: m31,
          }
        })
      : null
  var m32 = _vm.showConfig ? _vm.t("color1") : null
  var m33 = _vm.showConfig && _vm.showConfigTip ? _vm.t("color1") : null
  var m34 = _vm.showConfig ? _vm.t("color1") : null
  var l2 = _vm.showConfig
    ? _vm.__map(_vm.genderOptions, function (option, index) {
        var $orig = _vm.__get_orig(option)
        var m35 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1") : null
        var m36 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1rgb") : null
        var m37 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1") : null
        return {
          $orig: $orig,
          m35: m35,
          m36: m36,
          m37: m37,
        }
      })
    : null
  var m38 = _vm.showConfig ? _vm.t("color1") : null
  var m39 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m40 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m41 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m42 = _vm.showConfig ? _vm.t("color1") : null
  var m43 = _vm.showConfig ? _vm.t("color1rgb") : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        m8: m8,
        m9: m9,
        m10: m10,
        m11: m11,
        m12: m12,
        l0: l0,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        m25: m25,
        m26: m26,
        m27: m27,
        m28: m28,
        m29: m29,
        m30: m30,
        l1: l1,
        m32: m32,
        m33: m33,
        m34: m34,
        l2: l2,
        m38: m38,
        m39: m39,
        m40: m40,
        m41: m41,
        m42: m42,
        m43: m43,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 7262:
/*!*****************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=script&lang=js& */ 7263);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 7263:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      currentStep: 'camera',
      // camera, preview, processing, result
      currentStatus: '准备拍照',
      showCamera: false,
      cameraReady: false,
      cameraStatusText: '正在启动摄像头...',
      cameraPosition: 'front',
      // 相机位置：front(前置) 或 back(后置)
      isSwitching: false,
      // 是否正在切换摄像头
      capturedImageUrl: '',
      predictedImageUrl: '',
      progressPercent: 0,
      currentProcessStep: 0,
      processingSteps: [{
        icon: '🔍',
        text: '面部识别'
      }, {
        icon: '🧠',
        text: 'AI分析'
      }, {
        icon: '✨',
        text: '预测生成'
      }],
      // 配置相关
      showConfig: false,
      showConfigTip: false,
      genderIndex: -1,
      genderOptions: ['请选择性别', '男', '女', '其他'],
      userProfession: '',
      // 图片加载状态
      imageLoaded: false,
      currentImageLoaded: false,
      predictedImageLoaded: false,
      // 定时器
      processingTimer: null,
      progressTimer: null,
      // 处理状态标记
      progressCompleted: false,
      // 进度条是否完成
      apiCompleted: false,
      // API请求是否完成

      // 音频相关
      pre_url: '',
      // 云资源域名前缀
      processingAudio: null,
      // 处理中音频对象

      // 与未来对话配置
      futureTalkEnabled: false,
      // 是否启用与未来对话功能
      futureTalkButtonText: '与未来对话' // 按钮显示文字
    };
  },
  onLoad: function onLoad(options) {
    var _this = this;
    console.log('=== 摄像头页面加载开始 ===');
    console.log('接收到的页面参数:', options);

    // 保存从对话页面传递过来的用户信息（URL参数方式）
    if (options.name) {
      uni.setStorageSync('user_dialogue_name', decodeURIComponent(options.name));
      console.log('从URL参数保存姓名:', decodeURIComponent(options.name));
    }
    if (options.age) {
      uni.setStorageSync('user_dialogue_age', decodeURIComponent(options.age));
      console.log('从URL参数保存年龄:', decodeURIComponent(options.age));
    }
    if (options.dream) {
      uni.setStorageSync('user_dialogue_dream', decodeURIComponent(options.dream));
      console.log('从URL参数保存梦想:', decodeURIComponent(options.dream));
    }

    // 备用方案：从临时存储读取数据（如果URL参数失败）
    var tempName = uni.getStorageSync('temp_dialogue_name');
    var tempAge = uni.getStorageSync('temp_dialogue_age');
    var tempDream = uni.getStorageSync('temp_dialogue_dream');
    if (tempName && !uni.getStorageSync('user_dialogue_name')) {
      uni.setStorageSync('user_dialogue_name', tempName);
      console.log('从临时存储恢复姓名:', tempName);
    }
    if (tempAge && !uni.getStorageSync('user_dialogue_age')) {
      uni.setStorageSync('user_dialogue_age', tempAge);
      console.log('从临时存储恢复年龄:', tempAge);
    }
    if (tempDream && !uni.getStorageSync('user_dialogue_dream')) {
      uni.setStorageSync('user_dialogue_dream', tempDream);
      console.log('从临时存储恢复梦想:', tempDream);
    }

    // 清理临时存储
    uni.removeStorageSync('temp_dialogue_name');
    uni.removeStorageSync('temp_dialogue_age');
    uni.removeStorageSync('temp_dialogue_dream');

    // 初始化云资源前缀URL
    var app = getApp();
    this.pre_url = app.globalData.pre_url || '';
    setTimeout(function () {
      _this.loadUserConfig();
      _this.initCamera();
      _this.checkDreamInspirationSettings();
    }, 500);
  },
  onUnload: function onUnload() {
    this.clearTimers();
    this.stopProcessingAudio();
  },
  methods: {
    // 加载用户配置
    loadUserConfig: function loadUserConfig() {
      console.log('=== 开始加载用户配置 ===');

      // 尝试加载已保存的配置
      try {
        var savedGender = uni.getStorageSync('user_gender');
        var savedProfession = uni.getStorageSync('user_profession');

        // 加载从对话页面传递过来的用户信息
        var dialogueName = uni.getStorageSync('user_dialogue_name');
        var dialogueAge = uni.getStorageSync('user_dialogue_age');
        var dialogueDream = uni.getStorageSync('user_dialogue_dream');
        if (savedGender) {
          var genderIndex = this.genderOptions.indexOf(savedGender);
          if (genderIndex > 0) {
            this.genderIndex = genderIndex;
          }
        }

        // 优先使用对话页面的梦想信息，如果没有则使用已保存的职业信息
        if (dialogueDream && dialogueDream.trim() !== '') {
          this.userProfession = dialogueDream;
          console.log('自动填入对话页面的梦想:', dialogueDream);
        } else if (savedProfession) {
          this.userProfession = savedProfession;
        }
        console.log('加载已保存配置:', {
          gender: savedGender,
          profession: savedProfession,
          genderIndex: this.genderIndex,
          dialogueInfo: {
            name: dialogueName,
            age: dialogueAge,
            dream: dialogueDream
          }
        });
      } catch (e) {
        console.error('加载配置失败:', e);
      }
      this.showConfigTip = true;
      this.showConfig = true;
      console.log('显示配置弹窗');
    },
    // 初始化摄像头
    initCamera: function initCamera() {
      console.log('=== 开始初始化摄像头 ===');
      this.showCamera = true;
      this.cameraReady = true;
      this.cameraStatusText = '摄像头已就绪';
      console.log('摄像头状态设置完成');
    },
    // 摄像头就绪
    onCameraReady: function onCameraReady() {
      this.cameraReady = true;
      this.cameraStatusText = '摄像头已就绪';
      this.currentStatus = '准备拍照';
      console.log('摄像头初始化完成');
    },
    // 摄像头错误
    onCameraError: function onCameraError(e) {
      console.error('摄像头错误:', e);
      this.cameraStatusText = '摄像头启动失败';
    },
    // 拍照
    capturePhoto: function capturePhoto() {
      var _this2 = this;
      console.log('=== 开始拍照 ===');
      console.log('相机状态检查 - cameraReady:', this.cameraReady);
      if (!this.cameraReady) {
        console.log('相机未就绪');
        uni.showToast({
          title: '相机未准备好',
          icon: 'none'
        });
        return;
      }

      // 添加触觉反馈
      uni.vibrateShort();

      // 显示拍摄中状态
      uni.showLoading({
        title: '正在拍照...',
        mask: true
      });

      // 创建相机上下文
      var cameraContext = uni.createCameraContext();
      cameraContext.takePhoto({
        quality: 'high',
        success: function success(res) {
          console.log('拍摄成功:', res.tempImagePath);
          uni.hideLoading();

          // 直接上传拍摄的照片
          _this2.uploadCapturedImage(res.tempImagePath);
        },
        fail: function fail(err) {
          console.error('拍摄失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '拍摄失败，请重试',
            icon: 'error'
          });
        }
      });
    },
    // 选择图片
    chooseImage: function chooseImage() {
      console.log('选择图片');
      this.uploadImage('album');
    },
    // 上传拍摄的照片
    uploadCapturedImage: function uploadCapturedImage(tempImagePath) {
      console.log('=== 上传拍摄的照片 ===');
      console.log('临时图片路径:', tempImagePath);
      var that = this;
      var app = getApp();

      // 显示上传提示
      uni.showLoading({
        title: '正在上传照片...',
        mask: true
      });

      // 直接上传拍摄的照片
      uni.uploadFile({
        url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
        filePath: tempImagePath,
        name: 'file',
        success: function success(uploadRes) {
          uni.hideLoading();
          console.log('照片上传响应:', uploadRes);
          try {
            var data = JSON.parse(uploadRes.data);
            console.log('解析后的上传结果:', data);
            if (data.status == 1) {
              // 获取上传后的图片URL
              that.capturedImageUrl = data.url;
              that.currentStep = 'preview';

              // 重置图片加载状态
              that.imageLoaded = false;
              that.currentImageLoaded = false;
              that.predictedImageLoaded = false;
              console.log('照片上传成功:', data.url);
              console.log('切换到预览模式，当前步骤:', that.currentStep);
              uni.showToast({
                title: '拍照成功',
                icon: 'success'
              });
            } else {
              console.error('上传失败:', data.msg);
              uni.showToast({
                title: data.msg || '上传失败',
                icon: 'error'
              });
            }
          } catch (e) {
            console.error('解析上传结果失败:', e);
            uni.showToast({
              title: '上传结果解析失败',
              icon: 'error'
            });
          }
        },
        fail: function fail(uploadError) {
          uni.hideLoading();
          console.error('照片上传失败:', uploadError);
          uni.showToast({
            title: '上传失败: ' + uploadError.errMsg,
            icon: 'error'
          });
        }
      });
    },
    // 统一的图片上传方法
    uploadImage: function uploadImage() {
      var sourceType = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'album';
      var that = this;
      var app = getApp();
      console.log('uploadImage 被调用，sourceType:', sourceType);

      // 根据sourceType设置不同的选择来源
      var chooseSourceType = sourceType === 'camera' ? ['camera'] : ['album', 'camera'];
      console.log('设置的sourceType数组:', chooseSourceType);

      // 显示上传提示
      uni.showLoading({
        title: sourceType === 'camera' ? '正在拍照...' : '正在选择图片...',
        mask: true
      });

      // 直接使用uni.chooseImage，支持sourceType参数
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: chooseSourceType,
        success: function success(res) {
          console.log('选择图片成功:', res);
          var tempFilePaths = res.tempFilePaths;
          if (tempFilePaths && tempFilePaths.length > 0) {
            // 上传图片到服务器
            uni.uploadFile({
              url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
              filePath: tempFilePaths[0],
              name: 'file',
              success: function success(uploadRes) {
                uni.hideLoading();
                console.log('图片上传响应:', uploadRes);
                try {
                  var data = JSON.parse(uploadRes.data);
                  console.log('解析后的上传结果:', data);
                  if (data.status == 1) {
                    // 获取上传后的图片URL
                    that.capturedImageUrl = data.url;
                    that.currentStep = 'preview';

                    // 重置图片加载状态
                    that.imageLoaded = false;
                    that.currentImageLoaded = false;
                    that.predictedImageLoaded = false;
                    console.log('图片上传成功:', data.url);
                    console.log('切换到预览模式，当前步骤:', that.currentStep);
                    uni.showToast({
                      title: sourceType === 'camera' ? '拍照成功' : '图片上传成功',
                      icon: 'success'
                    });
                  } else {
                    console.error('上传失败:', data.msg);
                    uni.showToast({
                      title: data.msg || '上传失败',
                      icon: 'error'
                    });
                  }
                } catch (e) {
                  console.error('解析上传结果失败:', e);
                  uni.showToast({
                    title: '上传结果解析失败',
                    icon: 'error'
                  });
                }
              },
              fail: function fail(uploadError) {
                uni.hideLoading();
                console.error('图片上传失败:', uploadError);
                uni.showToast({
                  title: '上传失败: ' + uploadError.errMsg,
                  icon: 'error'
                });
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '未选择图片',
              icon: 'none'
            });
          }
        },
        fail: function fail(chooseError) {
          uni.hideLoading();
          console.log('选择图片失败或取消:', chooseError);
          // 用户取消选择不显示错误提示
          if (chooseError.errMsg && !chooseError.errMsg.includes('cancel')) {
            uni.showToast({
              title: '选择图片失败',
              icon: 'error'
            });
          }
        }
      });
    },
    // 切换摄像头
    switchCamera: function switchCamera() {
      var _this3 = this;
      console.log('=== 切换摄像头 ===');
      console.log('当前摄像头位置:', this.cameraPosition);

      // 添加触觉反馈
      uni.vibrateShort();

      // 防止重复点击
      if (this.isSwitching) {
        console.log('摄像头正在切换中，忽略此次点击');
        return;
      }
      this.isSwitching = true;

      // 切换前后摄像头
      this.cameraPosition = this.cameraPosition === 'front' ? 'back' : 'front';
      console.log('切换后摄像头位置:', this.cameraPosition);

      // 显示切换提示
      uni.showToast({
        title: this.cameraPosition === 'front' ? '切换到前置摄像头' : '切换到后置摄像头',
        icon: 'none',
        duration: 1000
      });

      // 重置摄像头状态，等待重新初始化
      this.cameraReady = false;
      this.cameraStatusText = '正在切换摄像头...';

      // 延迟重置切换状态
      setTimeout(function () {
        _this3.isSwitching = false;
        _this3.cameraReady = true;
        _this3.cameraStatusText = '摄像头已就绪';
        console.log('摄像头切换状态重置');
      }, 1000);
    },
    // 重新拍照
    retakePhoto: function retakePhoto() {
      this.currentStep = 'camera';
      this.capturedImageUrl = '';
      this.predictedImageUrl = '';
      // 重置图片加载状态
      this.imageLoaded = false;
      this.currentImageLoaded = false;
      this.predictedImageLoaded = false;
    },
    // 图片加载事件处理
    onImageLoad: function onImageLoad() {
      this.imageLoaded = true;
      console.log('预览图片加载完成');
    },
    onImageError: function onImageError(e) {
      console.error('预览图片加载失败:', e);
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
    },
    onCurrentImageLoad: function onCurrentImageLoad() {
      this.currentImageLoaded = true;
      console.log('当前图片加载完成');
    },
    onCurrentImageError: function onCurrentImageError(e) {
      console.error('当前图片加载失败:', e);
      this.currentImageLoaded = false;
    },
    onPredictedImageLoad: function onPredictedImageLoad() {
      this.predictedImageLoaded = true;
      console.log('预测图片加载完成');
    },
    onPredictedImageError: function onPredictedImageError(e) {
      console.error('预测图片加载失败:', e);
      this.predictedImageLoaded = false;
      uni.showToast({
        title: '预测图片加载失败',
        icon: 'none'
      });
    },
    // 开始处理
    startProcessing: function startProcessing() {
      this.currentStep = 'processing';
      this.progressPercent = 0;
      this.currentProcessStep = 0;

      // 重置状态标记
      this.progressCompleted = false;
      this.apiCompleted = false;

      // 播放处理中音频
      this.playProcessingAudio();

      // 立即开始API请求（与进度条并行）
      this.generateFutureImage();

      // 开始进度条动画
      this.simulateAIProcessing();
    },
    // 播放处理中音频
    playProcessingAudio: function playProcessingAudio() {
      var _this4 = this;
      try {
        // 停止之前的音频
        this.stopProcessingAudio();

        // 创建音频上下文
        this.processingAudio = uni.createInnerAudioContext();

        // 设置音频源（使用云端资源）
        this.processingAudio.src = this.pre_url + '/static/MP3/耐心等待20秒.mp3';

        // 音频播放事件
        this.processingAudio.onPlay(function () {
          console.log('开始播放处理中音频');
        });

        // 音频播放完成事件
        this.processingAudio.onEnded(function () {
          console.log('处理中音频播放完成');
          _this4.processingAudio.destroy();
          _this4.processingAudio = null;
        });

        // 音频播放错误事件
        this.processingAudio.onError(function (res) {
          console.error('处理中音频播放错误:', res);
          _this4.processingAudio.destroy();
          _this4.processingAudio = null;
        });

        // 开始播放
        this.processingAudio.play();

        // 设置定时器，10-15秒后停止播放
        setTimeout(function () {
          _this4.stopProcessingAudio();
        }, 12000); // 12秒后停止
      } catch (error) {
        console.error('播放处理中音频时发生错误:', error);
      }
    },
    // 停止处理中音频
    stopProcessingAudio: function stopProcessingAudio() {
      if (this.processingAudio) {
        try {
          this.processingAudio.stop();
          this.processingAudio.destroy();
          this.processingAudio = null;
          console.log('处理中音频已停止');
        } catch (error) {
          console.error('停止处理中音频时发生错误:', error);
        }
      }
    },
    // 模拟AI处理过程
    simulateAIProcessing: function simulateAIProcessing() {
      var _this5 = this;
      // 处理步骤动画 - 调整为20秒内完成3个步骤
      var stepInterval = setInterval(function () {
        if (_this5.currentProcessStep < _this5.processingSteps.length - 1) {
          _this5.currentProcessStep++;
        }
      }, 6700); // 每6.7秒切换一个步骤（20秒÷3步≈6.7秒）

      // 进度条动画 - 调整为20秒内完成（100% ÷ 100次 = 每200ms增加1%）
      this.processingTimer = setInterval(function () {
        _this5.progressPercent += 1;
        if (_this5.progressPercent >= 100) {
          clearInterval(stepInterval);
          _this5.completeProcessing();
        }
      }, 200); // 从150ms改为200ms，让进度条在20秒内完成
    },
    // 完成处理
    completeProcessing: function completeProcessing() {
      this.clearTimers();

      // 停止处理中音频
      this.stopProcessingAudio();

      // 进度条完成，标记进度条已完成
      // API请求已经在startProcessing()中启动，这里只需要等待API结果
      this.progressCompleted = true;

      // 如果API已经返回结果，立即显示
      if (this.apiCompleted && this.predictedImageUrl) {
        this.showResult();
      }
    },
    // 显示结果
    showResult: function showResult() {
      this.currentStep = 'result';

      // 重置图片加载状态，确保图片能正常显示
      this.predictedImageLoaded = false;

      // 强制触发页面更新
      this.$forceUpdate();
      uni.showToast({
        title: '梦想图片生成完成！',
        icon: 'success'
      });
      console.log('页面切换到结果展示，预测图片URL:', this.predictedImageUrl);
    },
    // 生成梦想启蒙图片
    generateFutureImage: function generateFutureImage() {
      var _this6 = this;
      // 重置预测图片加载状态
      this.predictedImageLoaded = false;

      // 检查是否已配置性别和职业
      var userGender = uni.getStorageSync('user_gender');
      var userProfession = uni.getStorageSync('user_profession');
      console.log('检查用户配置:', {
        userGender: userGender,
        userProfession: userProfession,
        capturedImageUrl: this.capturedImageUrl
      });
      if (!userGender || !userProfession || userGender === '未设置' || userProfession === '未设置') {
        console.log('用户配置不完整，显示配置提醒');
        uni.showModal({
          title: '配置提醒',
          content: '请先完成性别和职业配置，以便生成个性化的梦想图片',
          showCancel: false,
          confirmText: '去配置',
          success: function success() {
            _this6.showConfig = true;
          }
        });
        return;
      }

      // 检查是否有图片URL
      if (!this.capturedImageUrl) {
        console.log('没有图片URL，无法调用API');
        uni.showToast({
          title: '请先上传照片',
          icon: 'none'
        });
        return;
      }
      console.log('配置检查通过，开始调用梦想启蒙API');
      // 调用真实的梦想启蒙API
      this.callAIAPI();
    },
    // 调用梦想启蒙AI接口
    callAIAPI: function callAIAPI() {
      var _this7 = this;
      console.log('=== 开始调用梦想启蒙API ===');

      // 获取用户配置信息
      var userGender = uni.getStorageSync('user_gender') || '未设置';
      var userProfession = uni.getStorageSync('user_profession') || '未设置';

      // 获取对话页面的用户信息
      var dialogueName = uni.getStorageSync('user_dialogue_name') || '';
      var dialogueAge = uni.getStorageSync('user_dialogue_age') || '';
      var dialogueDream = uni.getStorageSync('user_dialogue_dream') || '';

      // 将性别转换为数字格式（后端期望的格式）
      var genderCode = 0; // 默认为女
      if (userGender === '男') {
        genderCode = 1;
      } else if (userGender === '女') {
        genderCode = 0;
      }

      // 确定职业信息 - 优先使用配置页面的职业，其次使用对话页面的梦想
      var profession = '';
      if (userProfession && userProfession !== '未设置' && userProfession.trim() !== '') {
        profession = userProfession; // 优先使用配置页面的职业
      } else if (dialogueDream && dialogueDream.trim() !== '') {
        profession = dialogueDream; // 其次使用对话页面的梦想作为职业
      } else {
        profession = '医生'; // 默认职业
      }

      // 构建梦想内容 - 优先使用对话页面的详细信息
      var dreamContent = '';
      if (dialogueName && dialogueAge && dialogueDream) {
        // 使用对话页面的完整信息
        dreamContent = "\u6211\u53EB".concat(dialogueName, "\uFF0C\u6211\u4ECA\u5E74").concat(dialogueAge, "\u5C81\uFF0C\u6211\u7684\u68A6\u60F3\u662F").concat(dialogueDream, "\u3002\u5E0C\u671B\u5728\u672A\u6765\u80FD\u591F\u5B9E\u73B0\u81EA\u5DF1\u7684\u68A6\u60F3\uFF0C\u53D8\u5F97\u66F4\u52A0\u4F18\u79C0\u548C\u6210\u529F\u3002");
      } else {
        // 使用配置页面的基本信息
        dreamContent = "\u6211\u662F\u4E00\u4E2A".concat(userGender, "\uFF0C\u804C\u4E1A\u662F").concat(userProfession, "\uFF0C\u5E0C\u671B\u5728\u672A\u6765\u80FD\u591F\u5B9E\u73B0\u81EA\u5DF1\u7684\u68A6\u60F3\uFF0C\u53D8\u5F97\u66F4\u52A0\u4F18\u79C0\u548C\u6210\u529F\u3002");
      }

      // 准备请求参数
      var requestData = {
        gender: genderCode,
        // 使用数字格式：1=男，0=女
        dream_content: dreamContent,
        profession: profession,
        // 新增：职业信息
        image_url: this.capturedImageUrl // 通过统一上传接口获取的图片URL
      };

      console.log('性别转换:', {
        原始性别: userGender,
        转换后代码: genderCode,
        说明: genderCode === 1 ? '男' : '女'
      });
      console.log('职业信息:', {
        对话梦想: dialogueDream,
        配置职业: userProfession,
        最终职业: profession,
        职业来源: userProfession && userProfession !== '未设置' && userProfession.trim() !== '' ? '配置页面' : dialogueDream && dialogueDream.trim() !== '' ? '对话页面' : '默认值'
      });
      console.log('API请求参数:', requestData);
      console.log('即将调用接口: ApiDreamInspiration/generateImage');

      // 获取全局app对象
      var app = getApp();

      // 调用梦想启蒙API
      app.post('ApiDreamInspiration/generateImage', requestData, function (res) {
        console.log('API调用成功，返回结果:', res);
        if (res.code == 1) {
          // 生成成功，获取记录ID
          var recordId = res.data.record_id;

          // 移除弹窗提示，直接进入轮询检查
          // uni.showLoading({
          // 	title: '正在生成梦想图片...',
          // 	mask: true
          // });

          // 轮询检查生成状态
          _this7.checkGenerationStatus(recordId);
        } else {
          _this7.handleAPIError(res.msg || '生成请求失败');
        }
      }, function (error) {
        console.error('API调用失败:', error);
        _this7.handleAPIError('网络请求失败，请检查网络连接');
      });
    },
    // 检查图片生成状态
    checkGenerationStatus: function checkGenerationStatus(recordId) {
      var _this8 = this;
      var app = getApp();
      var pollCount = 0;
      var maxPolls = 60; // 最多轮询60次（约2分钟）

      var checkStatus = function checkStatus() {
        pollCount++;
        console.log("\u8F6E\u8BE2\u67E5\u8BE2\u72B6\u6001 - \u7B2C".concat(pollCount, "\u6B21\uFF0C\u8BB0\u5F55ID: ").concat(recordId));
        app.post('ApiDreamInspiration/checkGenerationStatus', {
          record_id: recordId
        }, function (res) {
          console.log('状态查询结果:', res);
          if (res.code == 1) {
            var record = res.data;
            if (record.status == 1) {
              // 生成成功 (状态1表示已生成)
              console.log('梦想图片生成成功，图片URL:', record.result_image);

              // 保存图片URL和记录ID
              _this8.predictedImageUrl = record.result_image;
              uni.setStorageSync('current_dream_record_id', recordId);

              // 标记API已完成
              _this8.apiCompleted = true;

              // 如果进度条也已完成，立即显示结果
              if (_this8.progressCompleted) {
                _this8.showResult();
              }
              // 如果进度条还没完成，等待进度条完成后在completeProcessing()中显示结果
            } else if (record.status == 2) {
              // 生成失败 (状态2表示生成失败)
              // uni.hideLoading();
              _this8.handleAPIError(record.error_msg || '图片生成失败');
            } else if (record.status == 0) {
              // 仍在生成中 (状态0表示生成中)
              if (pollCount >= maxPolls) {
                // 超过最大轮询次数
                // uni.hideLoading();
                _this8.handleAPIError('生成超时，请稍后查看记录');
              } else {
                // 继续轮询，每3秒查询一次
                setTimeout(checkStatus, 3000);
              }
            } else {
              // 未知状态
              // uni.hideLoading();
              _this8.handleAPIError('未知的生成状态');
            }
          } else {
            // uni.hideLoading();
            _this8.handleAPIError(res.msg || '获取生成状态失败');
          }
        }, function (error) {
          console.error('状态查询失败:', error);
          // uni.hideLoading();
          _this8.handleAPIError('检查生成状态失败');
        });
      };

      // 开始检查状态
      checkStatus();
    },
    // 处理API错误
    handleAPIError: function handleAPIError(message) {
      var _this9 = this;
      uni.showModal({
        title: '预测失败',
        content: message + '，是否重试？',
        success: function success(res) {
          if (res.confirm) {
            _this9.startProcessing();
          } else {
            _this9.currentStep = 'preview';
          }
        }
      });
    },
    // 与未来对话
    startFutureChat: function startFutureChat() {
      console.log('开始与未来对话');

      // 检查功能是否启用
      if (!this.futureTalkEnabled) {
        uni.showToast({
          title: '与未来对话功能未启用',
          icon: 'none'
        });
        return;
      }

      // 显示加载状态
      uni.showLoading({
        title: '正在获取对话链接...',
        mask: true
      });

      // 获取全局app对象
      var app = getApp();

      // 获取用户对话信息
      var dialogueName = uni.getStorageSync('user_dialogue_name') || '';
      var dialogueAge = uni.getStorageSync('user_dialogue_age') || '';
      var dialogueDream = uni.getStorageSync('user_dialogue_dream') || '';

      // 构建用户信息参数
      var userInfo = {};
      if (dialogueName) {
        userInfo.name = dialogueName;
      }
      if (dialogueAge) {
        userInfo.age = dialogueAge;
      }
      if (dialogueDream) {
        userInfo.dream = dialogueDream;
      }
      console.log('传递给与未来对话的用户信息:', userInfo);

      // 调用API获取与未来对话的链接，传递用户信息
      app.post('ApiDreamInspiration/getFutureTalkUrl', userInfo, function (res) {
        uni.hideLoading();
        console.log('获取与未来对话链接结果:', res);
        if (res.code == 1) {
          var data = res.data;
          var futureTalkUrl = data.url;
          if (futureTalkUrl) {
            console.log('准备跳转到链接:', futureTalkUrl);

            // 获取用户对话信息
            var _dialogueName = uni.getStorageSync('user_dialogue_name') || '';
            var _dialogueAge = uni.getStorageSync('user_dialogue_age') || '';
            var _dialogueDream = uni.getStorageSync('user_dialogue_dream') || '';

            // 构建用户信息参数字符串
            var userInfoParams = '';
            if (_dialogueName && _dialogueAge && _dialogueDream) {
              var userMessage = "\u6211\u53EB".concat(_dialogueName, "\uFF0C\u6211\u4ECA\u5E74").concat(_dialogueAge, "\u5C81\uFF0C\u6211\u7684\u68A6\u60F3\u662F").concat(_dialogueDream);
              userInfoParams = "&message=".concat(encodeURIComponent(userMessage));
              console.log('拼接用户信息参数:', userMessage);
            }

            // 判断是否为外部链接
            if (futureTalkUrl.startsWith('http://') || futureTalkUrl.startsWith('https://')) {
              // 在外链URL后面拼接用户信息参数
              var finalUrl = futureTalkUrl + (futureTalkUrl.includes('?') ? userInfoParams : userInfoParams.replace('&', '?'));
              console.log('最终跳转链接:', finalUrl);

              // 外部链接，使用webview打开

              var webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(finalUrl);
              uni.navigateTo({
                url: webviewUrl,
                success: function success() {
                  console.log('跳转到WebView成功');
                },
                fail: function fail(err) {
                  console.error('跳转到WebView失败', err);
                  uni.showToast({
                    title: '跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            } else {
              // 内部页面链接
              uni.navigateTo({
                url: futureTalkUrl,
                success: function success() {
                  console.log('跳转到内部页面成功');
                },
                fail: function fail(err) {
                  console.error('跳转到内部页面失败', err);
                  uni.showToast({
                    title: '跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            }
          } else {
            uni.showToast({
              title: '未配置对话链接',
              icon: 'none'
            });
          }
        } else {
          uni.showToast({
            title: res.msg || '获取对话链接失败',
            icon: 'none'
          });
        }
      }, function (error) {
        uni.hideLoading();
        console.error('获取与未来对话链接失败:', error);
        uni.showToast({
          title: '网络请求失败，请检查网络连接',
          icon: 'none'
        });
      });
    },
    // 获取我的梦想记录
    getMyDreamRecords: function getMyDreamRecords() {
      var app = getApp();
      app.post('ApiDreamInspiration/getMyRecords', {
        page: 1,
        limit: 10
      }, function (res) {
        if (res.code == 1) {
          console.log('我的梦想记录:', res.data);
          // 可以在这里处理历史记录显示
        } else {
          console.error('获取记录失败:', res.msg);
        }
      }, function (error) {
        console.error('获取记录失败:', error);
      });
    },
    // 查看历史记录
    viewHistory: function viewHistory() {
      // 可以跳转到历史记录页面或显示历史记录弹窗
      this.getMyDreamRecords();
    },
    // 检查梦想启蒙设置
    checkDreamInspirationSettings: function checkDreamInspirationSettings() {
      var _this10 = this;
      var app = getApp();
      app.post('ApiDreamInspiration/getSetting', {}, function (res) {
        if (res.code == 1) {
          var settings = res.data;

          // 检查梦想启蒙功能是否开启
          if (!settings.is_enabled) {
            uni.showModal({
              title: '功能未开启',
              content: '梦想启蒙功能暂未开启，请联系管理员',
              showCancel: false,
              confirmText: '知道了',
              success: function success() {
                uni.navigateBack();
              }
            });
            return;
          }

          // 设置与未来对话配置
          if (settings.future_talk_enabled !== undefined) {
            _this10.futureTalkEnabled = settings.future_talk_enabled == 1;
          }
          if (settings.future_talk_button_text) {
            _this10.futureTalkButtonText = settings.future_talk_button_text;
          }
          console.log('与未来对话配置:', {
            enabled: _this10.futureTalkEnabled,
            buttonText: _this10.futureTalkButtonText
          });
        } else {
          console.error('获取设置失败:', res.msg);
        }
      }, function (error) {
        console.error('检查设置失败:', error);
      });
    },
    // 返回
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 配置相关方法
    selectGender: function selectGender(index) {
      if (index === 0) return;
      this.genderIndex = index;
    },
    onInputFocus: function onInputFocus() {
      console.log('职业输入框获得焦点');
    },
    onInputBlur: function onInputBlur() {
      console.log('职业输入框失去焦点');
    },
    onProfessionInput: function onProfessionInput(e) {
      this.userProfession = e.detail.value;
      console.log('职业输入内容:', this.userProfession);
    },
    onInputConfirm: function onInputConfirm(e) {
      this.userProfession = e.detail.value;
      console.log('职业输入确认:', this.userProfession);
      // 可以在这里添加输入验证逻辑
    },
    saveConfig: function saveConfig() {
      var _this11 = this;
      // 验证性别选择
      if (this.genderIndex <= 0) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return;
      }

      // 验证职业输入（可选，但建议填写）
      if (!this.userProfession || this.userProfession.trim() === '') {
        uni.showModal({
          title: '提示',
          content: '建议填写职业信息以获得更准确的预测结果，是否继续？',
          success: function success(res) {
            if (res.confirm) {
              _this11.doSaveConfig();
            }
          }
        });
        return;
      }
      this.doSaveConfig();
    },
    doSaveConfig: function doSaveConfig() {
      try {
        var profession = this.userProfession.trim();
        uni.setStorageSync('user_gender', this.genderOptions[this.genderIndex]);
        uni.setStorageSync('user_profession', profession);
        console.log('保存配置:', {
          gender: this.genderOptions[this.genderIndex],
          profession: profession
        });
        uni.showToast({
          title: '配置已保存',
          icon: 'success'
        });
        this.hideConfigModal();
      } catch (e) {
        console.error('保存配置失败:', e);
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'error'
        });
      }
    },
    // 跳过配置
    skipConfig: function skipConfig() {
      var _this12 = this;
      uni.showModal({
        title: '跳过配置',
        content: '跳过配置可能影响AI预测准确性，确定要跳过吗？',
        success: function success(res) {
          if (res.confirm) {
            // 设置默认值
            uni.setStorageSync('user_gender', '未设置');
            uni.setStorageSync('user_profession', '未设置');
            uni.showToast({
              title: '已跳过配置',
              icon: 'none'
            });
            _this12.hideConfigModal();
          }
        }
      });
    },
    hideConfigModal: function hideConfigModal() {
      if (this.showConfigTip && this.genderIndex <= 0) {
        uni.showToast({
          title: '请先选择性别信息',
          icon: 'none'
        });
        return;
      }
      this.showConfig = false;
      this.showConfigTip = false;
    },
    // 强制显示配置
    forceShowConfig: function forceShowConfig() {
      this.showConfig = true;
      this.showConfigTip = true;
    },
    // 分享结果功能
    shareResult: function shareResult() {
      var _this13 = this;
      console.log('分享结果');

      // 检查是否有预测图片
      if (!this.predictedImageUrl) {
        uni.showToast({
          title: '暂无可分享的内容',
          icon: 'none'
        });
        return;
      }

      // 显示分享选项
      uni.showActionSheet({
        itemList: ['保存预测图片', '保存原始图片', '保存全部图片'],
        success: function success(res) {
          console.log('选择分享选项:', res.tapIndex);
          switch (res.tapIndex) {
            case 0:
              // 保存预测图片
              _this13.downloadImage(_this13.predictedImageUrl, '20年后的你');
              break;
            case 1:
              // 保存原始图片
              _this13.downloadImage(_this13.capturedImageUrl, '现在的你');
              break;
            case 2:
              // 保存全部图片
              _this13.downloadAllImages();
              break;
          }
        },
        fail: function fail(err) {
          console.log('取消分享:', err);
        }
      });
    },
    // 下载全部图片
    downloadAllImages: function downloadAllImages() {
      console.log('下载全部图片');
      uni.showLoading({
        title: '正在保存图片...',
        mask: true
      });
      var downloadCount = 0;
      var totalImages = 2;
      var checkComplete = function checkComplete() {
        downloadCount++;
        if (downloadCount >= totalImages) {
          uni.hideLoading();
          uni.showToast({
            title: '全部图片已保存',
            icon: 'success',
            duration: 2000
          });
        }
      };

      // 下载原始图片
      if (this.capturedImageUrl) {
        this.downloadImageSilent(this.capturedImageUrl, '现在的你', checkComplete);
      } else {
        checkComplete();
      }

      // 下载预测图片
      if (this.predictedImageUrl) {
        this.downloadImageSilent(this.predictedImageUrl, '20年后的你', checkComplete);
      } else {
        checkComplete();
      }
    },
    // 静默下载图片（不显示单独的loading）
    downloadImageSilent: function downloadImageSilent(imageUrl, title, callback) {
      console.log('静默下载图片:', imageUrl, title);
      if (!imageUrl) {
        if (callback) callback();
        return;
      }

      // 小程序环境下载图片
      uni.downloadFile({
        url: imageUrl,
        success: function success(downloadRes) {
          if (downloadRes.statusCode === 200) {
            // 保存图片到相册
            uni.saveImageToPhotosAlbum({
              filePath: downloadRes.tempFilePath,
              success: function success() {
                console.log(title + '保存成功');
                if (callback) callback();
              },
              fail: function fail(saveErr) {
                console.error(title + '保存失败:', saveErr);
                if (callback) callback();
              }
            });
          } else {
            console.error(title + '下载失败');
            if (callback) callback();
          }
        },
        fail: function fail(downloadErr) {
          console.error(title + '下载失败:', downloadErr);
          if (callback) callback();
        }
      });
    },
    // 图片预览功能
    previewImage: function previewImage(imageUrl) {
      var _this14 = this;
      var title = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '图片预览';
      console.log('预览图片:', imageUrl, title);
      if (!imageUrl) {
        uni.showToast({
          title: '图片不存在',
          icon: 'none'
        });
        return;
      }

      // 使用uni.previewImage预览图片
      uni.previewImage({
        urls: [imageUrl],
        // 图片URL数组
        current: imageUrl,
        // 当前显示的图片
        longPressActions: {
          itemList: ['保存图片'],
          success: function success(data) {
            console.log('长按操作选择:', data.tapIndex);
            if (data.tapIndex === 0) {
              // 保存图片
              _this14.downloadImage(imageUrl, title);
            }
          },
          fail: function fail(err) {
            console.log('长按操作失败:', err);
          }
        },
        success: function success() {
          console.log('图片预览成功');
        },
        fail: function fail(err) {
          console.error('图片预览失败:', err);
          uni.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    },
    // 图片下载功能
    downloadImage: function downloadImage(imageUrl) {
      var title = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '图片';
      console.log('下载图片:', imageUrl, title);
      if (!imageUrl) {
        uni.showToast({
          title: '图片不存在',
          icon: 'none'
        });
        return;
      }

      // 显示下载提示
      uni.showLoading({
        title: '正在保存图片...',
        mask: true
      });

      // 小程序环境下载图片
      uni.downloadFile({
        url: imageUrl,
        success: function success(downloadRes) {
          console.log('图片下载成功:', downloadRes);
          if (downloadRes.statusCode === 200) {
            // 保存图片到相册
            uni.saveImageToPhotosAlbum({
              filePath: downloadRes.tempFilePath,
              success: function success() {
                uni.hideLoading();
                uni.showToast({
                  title: title + '已保存到相册',
                  icon: 'success',
                  duration: 2000
                });
                console.log('图片保存到相册成功');
              },
              fail: function fail(saveErr) {
                uni.hideLoading();
                console.error('保存到相册失败:', saveErr);
                if (saveErr.errMsg.includes('auth')) {
                  // 权限问题
                  uni.showModal({
                    title: '权限提示',
                    content: '需要授权访问相册才能保存图片，请在设置中开启相册权限',
                    showCancel: false,
                    confirmText: '知道了'
                  });
                } else {
                  uni.showToast({
                    title: '保存失败: ' + saveErr.errMsg,
                    icon: 'none',
                    duration: 3000
                  });
                }
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '图片下载失败',
              icon: 'none'
            });
          }
        },
        fail: function fail(downloadErr) {
          uni.hideLoading();
          console.error('图片下载失败:', downloadErr);
          uni.showToast({
            title: '下载失败: ' + downloadErr.errMsg,
            icon: 'none',
            duration: 3000
          });
        }
      });
    },
    // 清除定时器
    clearTimers: function clearTimers() {
      if (this.processingTimer) {
        clearInterval(this.processingTimer);
        this.processingTimer = null;
      }
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 7264:
/*!*************************************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& ***!
  \*************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& */ 7265);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 7265:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[7258,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesB/dreamark/camera-new.js.map