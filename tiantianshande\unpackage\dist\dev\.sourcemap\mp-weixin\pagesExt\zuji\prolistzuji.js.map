{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?f7e3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?45f1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?0559", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?39ed", "uni-app:///pagesExt/zuji/prolistzuji.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?b8be", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?ef3a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?3aec", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolistzuji.vue?e20b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activeTab", "brands", "models", "memories", "colors", "<PERSON><PERSON><PERSON>", "selected<PERSON><PERSON>l", "<PERSON><PERSON><PERSON><PERSON>", "selectedColor", "selectedPayment", "selectedLease", "selectedCreditRange", "showPopup", "modelOptions", "memoriesOptions", "colorOptions", "periodOptions", "rateValue", "totalPrice", "sellPrice", "choosen<PERSON><PERSON>", "catagoryList", "choosenCategory", "activetab", "responseData", "guigeChoosenData", "bid", "business", "guigeList", "onLoad", "watch", "methods", "selectTab", "onBrandChange", "console", "onModelChange", "onMemoryChange", "onColorChange", "value", "onCreditChange", "selectPayment", "selectLease", "closePopup", "nextStep", "uni", "url", "getdata", "app", "aid", "that", "setRateValue", "calculatePrice", "handleCShangeGuigeData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;AACC;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsHpxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACArB;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAO;MACA;QACA;MACA;IACA;IACAZ;MACA;QACA;MACA;IACA;IACAoB;MAAA;MACA;QACA;QACA;UAAA;QAAA;QACA;QAEA;UAAA;QAAA;QACA;MAEA;IAEA;EAGA;EACAM;IACAC;MAAA;MAEA;MACA;QAAA;MAAA;MAEA;MAEA;MACA;MACA;MAEA;QACA;MACA;IAGA;IACA;IACAC;MACAC;MACA;MAEA;MACAA;MAEA;QACA;MACA;MACA;MACA;IAGA;IAEA;IACAC;MACA;MACA;;MAGA;MACA;MACA;IACA;IACAC;MAEA,oGACA;MACA;IACA;IACAC;MACA;MACA,4GACAC,MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IAEA;IACAC;MACA;IACA;IACA7B;MACA;IACA;IACA8B;MACA;IACA;IACAC;MACA;MACA;MAEA;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAC;QACArB;QACAsB;MACA;QAEAC;QACAA;QACAA;QACAA;QACAA;QACAA;UAAA;QAAA;QACAA;QAEAA;QACA;QAEAf;QACAe;QAEA;UACAA;QACA;MAEA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAjB;MACA;IAEA;IACA;IAEAkB;MACA;IACA;EACA;AAGA;AAAA,2B;;;;;;;;;;;;;ACnYA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAu6C,CAAgB,k3CAAG,EAAC,C;;;;;;;;;;;ACA37C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zuji/prolistzuji.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zuji/prolistzuji.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolistzuji.vue?vue&type=template&id=f18dacb8&\"\nvar renderjs\nimport script from \"./prolistzuji.vue?vue&type=script&lang=js&\"\nexport * from \"./prolistzuji.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolistzuji.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./prolistzuji.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zuji/prolistzuji.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=template&id=f18dacb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page-container\">\n\t\t<view class=\"header\">\n\t\t\t<image :src=\"business.logo\" class=\"store-image\"></image>\n\t\t\t<view class=\"store-info\">\n\t\t\t\t<text class=\"store-name\">{{business.name}}</text>\n\t\t\t\t<text class=\"store-address\">{{business.address}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"model-selection\">\n\t\t\t<text class=\"section-title\">请选择机型</text>\n\t\t\t<view class=\"tab\">\n\t\t\t\t<view v-for=\"item in catagoryList\" :key=\"item.id\"\n\t\t\t\t\t:class=\"['tab-item', activeTab === item.id ? 'active' : '']\" @click=\"selectTab(item.id)\">\n\t\t\t\t\t{{item.name}}\n\t\t\t\t\t<view class=\"active-after\" v-if=\"activeTab === item.id\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"product-info\">\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">品牌</text>\n\t\t\t\t<picker mode=\"selector\" :range=\"brands\" range-key=\"name\" @change=\"onBrandChange\">\n\t\t\t\t\t<view class=\"picker\">{{ selectedBrand }}<text class=\"arrow\">></text></view>\n\t\t\t\t</picker>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-for=\"(item, index) in guigeList\" :key=\"item.k\">\n\t\t\t\t<text class=\"label\">{{item.title}}</text>\n\t\t\t\t<picker mode=\"selector\" :range=\"item['items']\" range-key=\"title\"\n\t\t\t\t\t@change=\"handleCShangeGuigeData(index,  $event)\">\n\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t{{ item['items'][guigeChoosenData[index]] && item['items'][guigeChoosenData[index]].title }}<text\n\t\t\t\t\t\t\tclass=\"arrow\">></text></view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\n\t\t\t\t</picker>\n\t\t\t</view>\n\n\t\t</view>\n\n\n\t\t<view class=\"credit-selection\">\n\t\t\t<text class=\"section-title\">芝麻信用分范围</text>\n\t\t\t<text class=\"note\">*请选择客户支付宝芝麻信用分</text>\n\t\t\t<radio-group class=\"credit-options\" @change=\"onCreditChange\">\n\t\t\t\t<label class=\"radio-label\">\n\t\t\t\t\t<radio value=\"400以下\">400以下</radio>\n\t\t\t\t</label>\n\t\t\t\t<label class=\"radio-label\">\n\t\t\t\t\t<radio value=\"400-500\">400-500</radio>\n\t\t\t\t</label>\n\t\t\t\t<label class=\"radio-label\">\n\t\t\t\t\t<radio value=\"500以上\">500以上</radio>\n\t\t\t\t</label>\n\t\t\t\t<label class=\"radio-label\">\n\t\t\t\t\t<radio value=\"老客户专属\">老客户专属</radio>\n\t\t\t\t\t<text class=\"exclusive\">包过！<text class=\"info-icon\" @click=\"showPopup\">i</text></text>\n\t\t\t\t</label>\n\t\t\t</radio-group>\n\t\t</view>\n\n\t\t<view v-if=\"selectedCreditRange\" class=\"initial-payment-selection\">\n\t\t\t<text class=\"section-title\">首期金额</text>\n\t\t\t<view class=\"payment-options\">\n\t\t\t\t<view v-if=\"selectedCreditRange == '500以上' || selectedCreditRange == '老客户专属'\"\n\t\t\t\t\t:class=\"['payment-option', selectedPayment === '30%' ? 'selected' : '']\"\n\t\t\t\t\t@click=\"selectPayment('30%')\">30%</view>\n\t\t\t\t<view v-if=\"selectedCreditRange !== '400以下'\"\n\t\t\t\t\t:class=\"['payment-option', selectedPayment === '40%' ? 'selected' : '']\"\n\t\t\t\t\t@click=\"selectPayment('40%')\">40%</view>\n\t\t\t\t<view :class=\"['payment-option', selectedPayment === '50%' ? 'selected' : '']\"\n\t\t\t\t\t@click=\"selectPayment('50%')\">50%</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"selectedCreditRange\" class=\"lease-term-selection\">\n\t\t\t<text class=\"section-title\">租赁期数</text>\n\t\t\t<view class=\"lease-options\">\n\t\t\t\t<view :class=\"['lease-option', selectedLease === '6期' ? 'selected' : '']\" @click=\"selectLease('6期')\">6期\n\t\t\t\t</view>\n\n\t\t\t\t<view :class=\"['lease-option', selectedLease === '8期' ? 'selected' : '']\" @click=\"selectLease('8期')\">8期\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"selectedCreditRange\" class=\"contract-price\">\n\t\t\t<text class=\"contract-label\">设备总签约价</text>\n\t\t\t<text class=\"contract-price\">¥ {{totalPrice}}</text>\n\t\t</view>\n\n\t\t<view v-if=\"selectedCreditRange\" class=\"billing-details\">\n\t\t\t<text class=\"section-title\">账单明细</text>\n\t\t\t<view class=\"billing-item\">\n\t\t\t\t<text class=\"billing-label\">首期</text>\n\t\t\t\t<text class=\"billing-value\">¥ 2310</text>\n\t\t\t</view>\n\t\t\t<view class=\"billing-item\">\n\t\t\t\t<text class=\"billing-label\">第2期租金</text>\n\t\t\t\t<text class=\"billing-value\">¥ 1495</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"selectedCreditRange\" class=\"next-step\">\n\t\t\t<button type=\"primary\" @click=\"nextStep\">下一步</button>\n\t\t</view>\n\n\t\t<view v-if=\"showPopup\" class=\"popup-overlay\" @click=\"closePopup\">\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<text class=\"popup-text\">该选项仅供参考，具体情况请咨询店铺工作人员。</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tactiveTab: '新机',\n\t\t\t\tbrands: ['苹果', '三星'],\n\t\t\t\tmodels: {\n\t\t\t\t\t'苹果': ['苹果15 Pro', '苹果14 Pro'],\n\t\t\t\t\t'三星': ['三星S21', '三星S20']\n\t\t\t\t},\n\t\t\t\tmemories: {\n\t\t\t\t\t'苹果15 Pro': ['128g', '256g'],\n\t\t\t\t\t'苹果14 Pro': ['64g', '128g'],\n\t\t\t\t\t'三星S21': ['128g', '256g'],\n\t\t\t\t\t'三星S20': ['64g', '128g']\n\t\t\t\t},\n\t\t\t\tcolors: {\n\t\t\t\t\t'苹果15 Pro': ['原色钛金属', '黑色'],\n\t\t\t\t\t'苹果14 Pro': ['黑色', '白色'],\n\t\t\t\t\t'三星S21': ['黑色', '白色'],\n\t\t\t\t\t'三星S20': ['蓝色', '黑色']\n\t\t\t\t},\n\t\t\t\tselectedBrand: '苹果',\n\t\t\t\tselectedModel: '苹果15 Pro',\n\t\t\t\tselectedMemory: '128g',\n\t\t\t\tselectedColor: '原色钛金属',\n\t\t\t\tselectedPayment: '30%',\n\t\t\t\tselectedLease: '6期',\n\t\t\t\tselectedCreditRange: '',\n\t\t\t\tshowPopup: false,\n\t\t\t\tmodelOptions: [], // 型号选项\n\t\t\t\tmemoriesOptions: [], // 内存选项\n\t\t\t\tcolorOptions: [],\n\t\t\t\tperiodOptions: [],\n\t\t\t\trateValue: 1,\n\t\t\t\ttotalPrice: 0,\n\t\t\t\tsellPrice: 0,\n\t\t\t\tchoosenGood: {},\n\t\t\t\tcatagoryList: [], // 机型列表\n\t\t\t\tchoosenCategory: {},\n\t\t\t\tactivetab: null,\n\t\t\t\tresponseData: {},\n\t\t\t\tguigeChoosenData: [],\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tbusiness:'' ,\n\t\t\t\tguigeList: []\n\t\t\t};\n\t\t},\n\t\tonLoad: function(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.getdata();\r\n\t\t\tthis.bid = this.opt.bid ? this.opt.bid : 0;\n\t\t},\n\t\twatch: {\n\t\t\tselectedPayment(newVal, oldVal) {\n\t\t\t\tif (this.selectedPayment === '30%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '40%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '50%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t},\n\t\t\tselectedLease(newVal, oldVal) {\n\t\t\t\tif (this.selectedPayment === '30%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '40%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '50%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\trateValue(newVal, oldVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\tthis.totalPrice =parseFloat(this.choosenGood.sell_price * this.rateValue).toFixed(3)\n\t\t\t\t}\n\t\t\t},\n\t\t\tselectedBrand(newVal, oldVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\tthis.totalPrice = parseFloat(this.choosenGood.sell_price * this.rateValue).toFixed(3)\n\t\t\t\t}\n\t\t\t},\n\t\t\tguigeChoosenData(newVal, oldVal) {\n\t\t\t\tif(newVal.length == this.guigeList.length) {\n\t\t\t\t\tlet guigeIndexString = newVal.join(',')\n\t\t\t\t\tlet item = this.responseData.guigelist.find(_item => _item.ks === guigeIndexString && _item.proid === this.choosenGood.id)\n\t\t\t\t\tlet priceItems = item && item.items\n\t\t\t\t\t\n\t\t\t\t\tlet finalObj = priceItems.find(_t => _t.ks === guigeIndexString)\n\t\t\t\t\tthis.totalPrice = finalObj && finalObj.sell_price\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\n\n\t\t},\n\t\tmethods: {\n\t\t\tselectTab(tab) {\n\n\t\t\t\tthis.activeTab = tab;\n\t\t\t\tthis.brands = this.responseData.datalist.filter(item => item.cid == this.activeTab)\n\t\t\t\t\n\t\t\t\tthis.selectedBrand = this.brands[0] && this.brands[0].name || ''\n\n\t\t\t\tthis.choosenGood = this.brands[0] || null\n\t\t\t\tlet guigeObj = JSON.parse(this.choosenGood.guigedata)\n\t\t\t\tthis.guigeList = guigeObj\n\t\t\t\t\n\t\t\t\tfor(var i in guigeObj) {\n\t\t\t\t\tthis.guigeChoosenData[i] = 0\n\t\t\t\t}\n\n\n\t\t\t},\n\t\t\t// 选择品牌\n\t\t\tonBrandChange(e) {\n\t\t\t\tconsole.log('this.brands=======》》》》》》》》》=', this.brands, e)\n\t\t\t\tlet guigeList = JSON.parse(this.brands[e.detail.value].guigedata)\n\t\t\t\t\n\t\t\t\tthis.guigeList = guigeList\n\t\t\t\tconsole.log('this.guigeList=====', this.guigeList)\n\t\t\t\t\n\t\t\t\tfor(var i in guigeList) {\n\t\t\t\t\tthis.guigeChoosenData[i] = 0\n\t\t\t\t}\n\t\t\t\tthis.choosenGood = this.brands[e.detail.value]\n\t\t\t\tthis.selectedBrand = this.brands[e.detail.value]['name'];\n\n\t\t\t\n\t\t\t},\n\n\t\t\t// 选择型号\n\t\t\tonModelChange(e) {\n\t\t\t\t// this.selectedModel = this.modelOptions && this.modelOptions['items'] && this.modelOptions['items'][e.detail.value]['title']\n\t\t\t\tthis.selectedModel = e.detail.value\n\n\n\t\t\t\t// this.selectedModel = this.memoriesOptions[this.selectedBrand][e.detail.value];\n\t\t\t\t// this.selectedMemory = this.memories[this.selectedModel][0];\n\t\t\t\t// this.selectedColor = this.colors[this.selectedModel][0];\n\t\t\t},\n\t\t\tonMemoryChange(e) {\n\n\t\t\t\tthis.selectedMemory = this.memoriesOptions && this.memoriesOptions['items'] && this.memoriesOptions[\n\t\t\t\t\t'items'][e.detail.value]['title']\n\t\t\t\t// this.selectedMemory = this.memories[this.selectedModel][e.detail.value];\n\t\t\t},\n\t\t\tonColorChange(e) {\n\t\t\t\t// this.selectedColor = this.colors[this.selectedModel][e.detail.value];\n\t\t\t\tthis.selectedColor = this.colorOptions && this.colorOptions['items'] && this.colorOptions['items'][e.detail\n\t\t\t\t\t.value\n\t\t\t\t]['title']\n\t\t\t},\n\t\t\tonCreditChange(e) {\n\t\t\t\tthis.selectedCreditRange = e.detail.value;\n\t\t\t},\n\t\t\tselectPayment(payment) {\n\t\t\t\tthis.selectedPayment = payment;\n\n\t\t\t},\n\t\t\tselectLease(lease) {\n\t\t\t\tthis.selectedLease = lease;\n\t\t\t},\n\t\t\tshowPopup() {\n\t\t\t\tthis.showPopup = true;\n\t\t\t},\n\t\t\tclosePopup() {\n\t\t\t\tthis.showPopup = false;\n\t\t\t},\n\t\t\tnextStep() {\n\t\t\t\t// Add functionality for the next step button\n\t\t\t\tlet guigeString = this.guigeChoosenData.join(',')\n\t\t\t\t\n\t\t\t\tlet prodid = this.choosenGood.id\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pagesExt/zuji/submitBuy?guigeString=' + guigeString + '&prodid='+ prodid\n\t\t\t\t})\n\t\t\t},\n\t\t\t//获取数据\n\t\t\tgetdata: function() {\n\t\t\t\tvar that = this;\n\t\t\t\t// var aid = that.opt.aid;\n\t\t\t\tvar aid = 1;\r\n\t\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\t\tapp.get('Apizuji/prolist', {\r\n\t\t\t\t\tbid: bid,\n\t\t\t\t\taid: aid\n\t\t\t\t}, function(res) {\n\n\t\t\t\t\tthat.responseData = res\n\t\t\t\t\tthat.business = res.business\n\t\t\t\t\tthat.catagoryList = res.clist\n\t\t\t\t\tthat.choosenCategory = res.clist[0]\n\t\t\t\t\tthat.activeTab = res.clist[0].id\n\t\t\t\t\tthat.brands = res.datalist.filter(item => item.cid == that.activeTab)\n\t\t\t\t\tthat.selectedBrand = res.datalist[0].name\n\n\t\t\t\t\tthat.choosenGood = res.datalist[0]\n\t\t\t\t\tlet guigeObj = JSON.parse(res.datalist[0]['guigedata'])\n\n\t\t\t\t\tconsole.log(\"------------------\", guigeObj)\n\t\t\t\t\tthat.guigeList = guigeObj\n\n\t\t\t\t\tfor (var i in guigeObj) {\n\t\t\t\t\t\tthat.guigeChoosenData[i] = 0\n\t\t\t\t\t}\n\n\t\t\t\t})\n\t\t\t},\n\t\t\tsetRateValue() {\n\t\t\t\tif (this.selectedPayment === '30%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '40%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t} else if (this.selectedPayment === '50%') {\n\t\t\t\t\tif (this.selectedLease === '6期') {\n\t\t\t\t\t\tthis.rateValue = 1.37\n\t\t\t\t\t} else if (this.selectedLease === '8期') {\n\t\t\t\t\t\tthis.rateValue = 1.51\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tcalculatePrice() {\n\t\t\t\tconsole.log(' this.selectedBrand.sell_price----', this.selectedBrand.sell_price, this.rateValue)\n\t\t\t\tthis.totalPrice = this.choosenGood.sell_price * this.rateValue\n\n\t\t\t},\n\t\t\t// 修改规格数据\n\n\t\t\thandleCShangeGuigeData(index, e) {\n\t\t\t\tthis.$set(this.guigeChoosenData, index, e.target.value)\n\t\t\t}\n\t\t},\n\n\n\t}\n</script>\n\n<style>\n\t.page-container {\n\t\tbackground-color: #f0f0f0;\n\t}\n\n\t.header {\n\t\tdisplay: flex;\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.store-image {\n\t\twidth: 80px;\n\t\theight: 80px;\n\t\tborder-radius: 10px;\n\t\tobject-fit: cover;\n\t}\n\n\t.store-info {\n\t\tmargin-left: 20px;\n\t\tflex: 1;\n\t}\n\n\t.store-name {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t}\n\n\t.store-address {\n\t\tfont-size: 14px;\n\t\tcolor: #888;\n\t\tmargin-top: 5px;\n\t}\n\n\t.model-selection {\n\t\tpadding: 10px 20px 0px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tmargin: 10px;\n\t\tborder-radius: 8px;\n\t}\n\n\t.section-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.tab {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tmargin: 12rpx;\n\t\tmargin: 10px;\n\t}\n\n\t.tab-item {\n\t\tpadding: 10px 20px;\n\t\tborder-radius: 20px;\n\t\ttext-align: center;\n\t\tflex: 1;\n\t\tmargin: 0 5px;\n\t\tcursor: pointer;\n\t\tcolor: #ccc;\n\t\tfont-size: 14px;\n\t}\n\n\t.tab-item.active {\n\t\tcolor: #000;\n\t\tfont-weight: bold;\n\t}\n\n\n\t.product-info {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tmargin: 10px;\n\t\tborder-radius: 8px;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tpadding: 16px 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.label {\n\t\tfont-size: 14px;\n\t\tcolor: #555;\n\t}\n\n\t.picker {\n\t\tfont-size: 14px;\n\t\tcolor: #000;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.arrow {\n\t\tmargin-left: 5px;\n\t\tcolor: #888;\n\t}\n\n\t.credit-selection {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tmargin: 10px;\n\t\tborder-radius: 8px;\n\t}\n\n\t.note {\n\t\tfont-size: 12px;\n\t\tcolor: red;\n\t}\n\n\t.credit-options {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin-top: 10px;\n\t}\n\n\t.radio-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 12px;\n\t\tmargin-right: 10px;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.exclusive {\n\t\tmargin-left: 5px;\n\t\tbackground-color: #ff5a5f;\n\t\tcolor: #fff;\n\t\tpadding: 2px 5px;\n\t\tborder-radius: 5px;\n\t}\n\n\t.info-icon {\n\t\tmargin-left: 5px;\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\tpadding: 2px 5px;\n\t\tborder-radius: 50%;\n\t\tcursor: pointer;\n\t}\n\n\t.initial-payment-selection {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.payment-options {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\tmargin-top: 10px;\n\t\tmargin-right: 10px;\n\t}\n\n\t.payment-option {\n\t\tpadding: 6px 10px;\n\t\twidth: 120px;\n\t\tborder: 1px solid #ccc;\n\t\tborder-radius: 8px;\n\t\ttext-align: center;\n\t\tmargin: 0 5px;\n\t\tcursor: pointer;\n\t\tmax-width: 200px;\n\t\tfont-size: 16px\n\t}\n\n\t.payment-option.selected {\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\tborder-color: #007aff;\n\t}\n\n\t.lease-term-selection {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.lease-options {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\tmargin-top: 10px;\n\t}\n\n\t.lease-option {\n\t\tpadding: 6px 10px;\n\t\twidth: 120px;\n\t\tborder: 1px solid #ccc;\n\t\tborder-radius: 8px;\n\t\ttext-align: center;\n\t\tmargin-right: 10px;\n\n\t\tmargin: 0 5px;\n\t\tfont-size: 16px;\n\t\tcursor: pointer;\n\t}\n\n\t.lease-option.selected {\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\tborder-color: #007aff;\n\t}\n\n\t.contract-price {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t}\n\n\t.contract-label {\n\t\tfont-size: 16px;\n\t\tcolor: #555;\n\t}\n\n\t.contract-price {\n\t\tfont-size: 16px;\n\t\tcolor: red;\n\t}\n\n\t.billing-details {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.billing-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tpadding: 10px 0;\n\t}\n\n\t.billing-label {\n\t\tfont-size: 14px;\n\t\tcolor: #555;\n\t}\n\n\t.billing-value {\n\t\tfont-size: 14px;\n\t\tcolor: #000;\n\t}\n\n\t.next-step {\n\t\ttext-align: center;\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t}\n\n\tbutton {\n\t\twidth: 100%;\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tpadding: 8px;\n\t\tborder-radius: 8px;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t}\n\n\tbutton:active {\n\t\tbackground-color: #005bb5;\n\t}\n\n\t.popup-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.popup-content {\n\t\tbackground-color: #fff;\n\t\tpadding: 20px;\n\t\tborder-radius: 10px;\n\t\ttext-align: center;\n\t}\n\n\t.popup-text {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t}\n</style>\n\n<style lang=\"scss\">\n\t.tab {\n\t\t.tab-item {\n\t\t\tposition: relative;\n\n\t\t\t.active-after {\n\t\t\t\twidth: 10px;\n\t\t\t\theight: 3px;\n\t\t\t\tbackground-color: #005bb5;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 46%;\n\t\t\t\tborder-radius: 8px;\n\t\t\t\tbottom: 0px;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115097520\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolistzuji.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102199\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}