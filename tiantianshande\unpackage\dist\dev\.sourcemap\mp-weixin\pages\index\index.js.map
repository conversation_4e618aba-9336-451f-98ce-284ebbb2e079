{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?8466", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?d22c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?a94d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?54fa", "uni-app:///pages/index/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?9c4d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/index.vue?5ee8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform", "homeNavigationCustom", "navigationBarBackgroundColor", "navigationBarTextStyle", "id", "pageinfo", "menuStyle", "title", "menuAlign", "bgType", "navBgColor", "topGradient", "pagecontent", "sysset", "oglist", "guanggaopic", "guang<PERSON><PERSON>l", "guang<PERSON><PERSON><PERSON>", "copyright", "latitude", "longitude", "area", "locationInfo", "statusBarHeight", "screenWidth", "business", "xixie", "xdata", "display_buy", "cartnum", "cartprice", "code", "area_id", "area_name", "bannerList", "image", "scrollTop", "areaEnabled", "currentAreaId", "currentAreaName", "areaList", "isLocating", "locationFailed", "menuTop", "showSplash", "splashConfig", "splashStartTime", "splashDuration", "skipCountdown", "progressPercent", "splashTimer", "splashVideo", "splashImage", "onLoad", "that", "uni", "console", "app", "mask", "setTimeout", "url", "success", "fail", "onPullDownRefresh", "onPageScroll", "onUnload", "clearInterval", "methods", "showSplashScreen", "hideSplashScreen", "startSplashCountdown", "skipSplash", "handleSplashClick", "recordSplashShow", "splash_id", "action", "timestamp", "checkAndInitSplash", "getdata", "componentId", "componentStyle", "isSpecificComponent", "组件ID", "样式", "强制刷新", "时间戳", "targetComponent", "icon", "duration", "select_bid", "pid", "obj", "clearTimeout", "content", "showCancel", "autoLocation", "showsubqrcode", "closesubqrcode", "changePopupAddress", "setMendianData", "handleBannerClick", "getTextColor", "getSearchBoxBackground", "getSearchTextColor", "handleSearchTap", "initAreaData", "name", "getCurrentLocation", "is_store_mode", "handleLocationTap", "current_address", "location_info", "area_list", "current_area_id", "current_area_name", "area_config", "isValidPageInfo", "Object", "manualJump", "findComponentById", "closeGuang<PERSON>o"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6QAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrJA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuX9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MACAN;MACAO;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;IACA;IAEAA;IACAA;;IAEA;IACAC;MACAhD;IACA;IAGA;IACA+C;IACAE;;IAGA;IACAF;;IAEA;IACAC;MACAD;MACAA;MAEAC;MACAA;MAEAD;IACA;;IAEA;IACAC;MACAD;MACAA;MACAA;MACAA;;MAEA;MACAC;;MAEA;MACAD;IACA;;IAEA;IACAC;MACAC;MACA;QACA;QACAC;;QAEA;QACA;QACAD;QAEA;UACA;UACAA;;UAEA;UACA;;UAEA;UACAD;YACAhD;YACAmD;UACA;;UAEA;UACA;;UAEA;UACAC;YACAJ;UACA;QACA;UACA;UACA;UACAC;;UAEA;UACAD;YACAK;YACAC;cACAL;YACA;YACAM;cACAN;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;;IAEA;IACA;IACA;IACA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACAF;EACA;EAEAS;IACA;EACA;EACAC;IACAT;EACA;EACAU;IACA;IACAV;IACAA;IACA;IACAA;;IAEA;IACA;MACAW;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAZ;MACA;;MAEA;MACAD;MAEA;MACA;IACA;IAEA;IACAc;MACAb;MACA;;MAEA;MACAD;;MAEA;MACA;QACAW;QACA;MACA;IACA;IAEA;IACAI;MAAA;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACAJ;MACA;MAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;QACAf;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEAA;MACA;IACA;IAEA;IACAgB;MACA;QACAhB;QACA;QACA;QACA;QACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAgB;MACA;QACA;MACA;MAEA;QACAC;QACA1E;QACA2E;QACAC;MACA;MAEAnB;QACA;UACAD;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAqB;MAAA;MACA;QACArB;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEAA;MAEA;;MAEA;MACAG;QACA;MACA;IACA;IAEAmB;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACAC;QACAC;QACA;QAEA;UACAC;UACAzB;YACA0B;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;YACA7B;;YAEA;YACA;cACA;cACA;gBACAA;;gBAEA;gBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;kBACA8B;kBACA9B;;kBAEA;kBACAD;oBACAhD;oBACAgF;oBACAC;kBACA;;kBAEA;kBACA;gBACA;cACA;YACA;cACAhC;YACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MAEAA;MACAA;MAEA;QACApD;QACAqF;QACAC;QACArE;MACA;;MAEA;MACA;QACAsE;QACAA;MACA;;MAEA;MACAnC;MAEA;QACAmC;QACAA;MACA;QACAA;MACA;;MAEA;MACArC;MAEAG;QACAD;QACAF;QACA;UACA;UACAG;UACA;QACA;QACA;UACA;UACA;YACAD;YACA;YACA;cACA;gBACA;gBACAF;gBACAE;gBACA;cACA;YACA;YACA;UACA;;UAEA;UACAF;UACA;UACAA;UACAA;;UAEA;UACA;YACAE;YACAF;UACA;;UAEA;UACA;YACAE;YACA;UACA;YACA;YACAF;YACA;YACAA;UACA;UAEAA;UACAA;;UAEA;UACA;YACAE;YACA;YACA;cACAF;YACA;YACA;cACAA;cACA;YACA;UACA;;UAEA;UACA;YACAE;;YAEA;YACA;cACAA;cACAF;YACA;YAEA;cACA;;cAOA;;cAEAE;cACA;cACA;cACAA;;cAEA;cACAG;gBACA;gBACAJ;kBACAK;kBACAC;oBACAL;oBACAoC;kBACA;kBACA9B;oBACAN;oBACA;oBACAoC;kBACA;gBACA;cACA;;cAGA;YAMA;cACApC;cACAoC;cACArC;gBACAhD;gBACAsF;gBACAC;cACA;YACA;YAEA;UACA;;UAEA;UACA;UACAvC;YACAhD;UACA;UAEA+C;;UAEA;UACAA;;UAEA;UACA;YACA;;YAEA;YACA;;YAEA;YACA;YAEAE;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;;YAEA;YACA;YACA;YACA;YACA,4FACAuC;cACAvC;cACAF;YACA;cACAA;YACA;UACA;YACAA;UACA;UAEA;YACAjE;cACAwE;gBACAP;cACA;YACA;YACAA;YACA;YACAA;YAEAA;YACAA;YACAA;YACA;cACAK;gBACAL;cACA;YACA;YACA;cACAK;gBACAL;cACA;YACA;UACA;QACA;UACA;YACAG;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAuC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA7C;UACAK;QACA;MACA;IACA;IACA;IACAyC;MACA;QACA;MACA;;MAEA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MAEA;QACA;MACA;MAEA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA/C;IACA;IACA;IACAgD;MACA;MACA;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;;UAEA;UACAlD;UACAA;QACA;UACA;UACA;UACA;YACA;YACA;YACA;;YAEA;YACAA;YACAA;UACA;QACA;;QAEA;QACA;QAEAC;UACApD;UACAsG;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEAnD;MAEAC;QACAD;QACA;QACA;;QAEA;QACAC;UACAtC;UACAC;UACAwF;QACA;UACApD;UACA;YACA;YACA;YACA;YACA;YACAD;;YAEA;YACA;cACA;cACAC;cACA;YACA;cACA;cACA;gBACA;cACA;gBACA;cACA;YACA;UACA;YACA;YACAA;YACA;cACA;cACAC;YACA;UACA;UACA;QACA;MACA;QACAD;QACA;QACA;;QAEA;QACA;QACA;UACA;UACA;UAEA;YACA;YACA;YACA;YACAA;YACA;UACA;QACA;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAoD;MACA;QACA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;UACA1F;UACAC;UACA0F;UACAC;QACA;QACAtD;MACA;QACA;QACA;QACA;UAAA;UACA;YACAuD;YACAC;YACAC;YACAC;UACA;UACA1D;QACA;MACA;IACA;IAEA;IACA2D;MACA;MACA,wBACAC,yCACA;IACA;IACA;IACAC;MACA;MACA;QACA/D;UACAhD;UACAsF;UACAC;QACA;QACA;MACA;MAEAtC;MAEA;QACA;;QAKA;;QAEA;QACA;QACAD;UACAK;UACAE;YACAN;YACAD;cACAhD;cACAsF;cACAC;YACA;UACA;QACA;;QAGA;MAIA;QACAtC;QACAD;UACAhD;UACAsF;UACAC;QACA;MACA;IACA;IACA;IACAyB;MACA;MAEA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACAhE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1yCA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    dpXixieMendian: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-xixie-mendian/dp-xixie-mendian\" */ \"@/components/dp-xixie-mendian/dp-xixie-mendian.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpXixiePopupAddress: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-xixie-popup-address/dp-xixie-popup-address\" */ \"@/components/dp-xixie-popup-address/dp-xixie-popup-address.vue\"\n      )\n    },\n    dpXixieBuycart: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-xixie-buycart/dp-xixie-buycart\" */ \"@/components/dp-xixie-buycart/dp-xixie-buycart.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isValidPageInfo()\n  var m1 = _vm.isValidPageInfo() && _vm.pageinfo.bgType === \"solid\"\n  var m2 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m3 =\n    _vm.isValidPageInfo() &&\n    _vm.pageinfo.bgType === \"gradient\" &&\n    _vm.pageinfo.topGradient !== \"1\"\n  var m4 = !m3 ? _vm.isValidPageInfo() && _vm.pageinfo.bgImage : null\n  var m5 = _vm.isValidPageInfo() && _vm.pageinfo.menuStyle === \"hidden\"\n  var m6 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m7 = _vm.isValidPageInfo()\n  var m8 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m9 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m10 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m11 = !_vm.isValidPageInfo() || _vm.pageinfo.menuStyle === \"default\"\n  var m12 =\n    !m11 && !(_vm.pageinfo.leftMode !== \"location\") ? _vm.getTextColor() : null\n  var m13 =\n    !m11 && _vm.pageinfo.showSearch === \"1\"\n      ? _vm.getSearchBoxBackground()\n      : null\n  var m14 =\n    !m11 && _vm.pageinfo.showSearch === \"1\" ? _vm.getSearchTextColor() : null\n  var m15 =\n    !_vm.isValidPageInfo() ||\n    _vm.pageinfo.menuStyle === \"fixed\" ||\n    _vm.pageinfo.menuStyle === \"default\"\n  var m16 =\n    _vm.isValidPageInfo() &&\n    _vm.pageinfo.bgType === \"gradient\" &&\n    _vm.pageinfo.topGradient === \"1\"\n  var m17 = _vm.sysset.mode == 1 ? _vm.t(\"color1\") : null\n  var m18 = _vm.sysset.mode == 1 ? _vm.t(\"color1rgb\") : null\n  var m19 = _vm.sysset.mode == 1 ? _vm.t(\"color1\") : null\n  var m20 =\n    _vm.sysset.agent_card == 1 && _vm.sysset.agent_card_info\n      ? _vm.t(\"color2\")\n      : null\n  var m21 = _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m22 = _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.oglist && _vm.oglist.length > 0\n  var m23 = _vm.xixie && _vm.display_buy ? _vm.t(\"color1\") : null\n  var m24 = _vm.xixie && _vm.display_buy ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        g0: g0,\n        m23: m23,\n        m24: m24,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\" :style=\"{ backgroundColor: pageinfo.bgcolor }\">\r\n\t\t\r\n\t\t<!-- 开屏遮罩层 -->\r\n\t\t<view v-if=\"showSplash\" class=\"splash-overlay\">\r\n\t\t\t<view class=\"splash-container\">\r\n\t\t\t\t<!-- 图片开屏 -->\r\n\t\t\t\t<image \r\n\t\t\t\t\tv-if=\"splashConfig.display_type === 'image'\" \r\n\t\t\t\t\t:src=\"splashConfig.image_url\" \r\n\t\t\t\t\tclass=\"splash-image\"\r\n\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t@click=\"handleSplashClick\"\r\n\t\t\t\t/>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 视频开屏 -->\r\n\t\t\t\t<video \r\n\t\t\t\t\tv-if=\"splashConfig.display_type === 'video'\"\r\n\t\t\t\t\t:src=\"splashConfig.video_url\"\r\n\t\t\t\t\tclass=\"splash-video\"\r\n\t\t\t\t\t:autoplay=\"true\"\r\n\t\t\t\t\t:muted=\"true\"\r\n\t\t\t\t\t:controls=\"false\"\r\n\t\t\t\t\t:loop=\"false\"\r\n\t\t\t\t\t@click=\"handleSplashClick\"\r\n\t\t\t\t/>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 跳过按钮 -->\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-if=\"splashConfig.skip_enabled === 1\" \r\n\t\t\t\t\tclass=\"splash-skip\"\r\n\t\t\t\t\t@click=\"skipSplash\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text v-if=\"skipCountdown > 0\">{{skipCountdown}}s</text>\r\n\t\t\t\t\t<text v-else>跳过</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 进度条 -->\r\n\t\t\t\t<view class=\"splash-progress\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"splash-progress-bar\" \r\n\t\t\t\t\t\t:style=\"{ width: progressPercent + '%' }\"\r\n\t\t\t\t\t></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 链接按钮 -->\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-if=\"splashConfig.link_url && splashConfig.link_name\" \r\n\t\t\t\t\tclass=\"splash-link\"\r\n\t\t\t\t\t@click=\"handleSplashClick\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{splashConfig.link_name}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- #ifdef MP-WEIXIN  -->\r\n\t\t<!-- <official-account></official-account> -->\r\n\t\t<!-- 微信小程序环境下的自定义导航栏 -->\r\n\t\t<view class=\"custom-menu\" \r\n\t\t\t:class=\"[\r\n\t\t\t\t!isValidPageInfo() ? 'menu-default' : '',\r\n\t\t\t\tpageinfo.menuStyle === 'fixed' ? 'menu-fixed' : '',\r\n\t\t\t\tpageinfo.menuStyle === 'float' && pageinfo.scrollOpacity === '1' ? 'menu-float' : '',\r\n\t\t\t\tpageinfo.isOpacity === '1' ? 'menu-opacity' : '',\r\n\t\t\t\tpageinfo.bgType === 'gradient' ? 'menu-gradient' : '',\r\n\t\t\t\tpageinfo.menuStyle === 'default' ? 'menu-default' : ''\r\n\t\t\t]\"\r\n\t\t\t:style=\"{\r\n\t\t\t\theight: (menuTop+50) + 'px',\r\n\t\t\t\t'--nav-bg-color': isValidPageInfo() && pageinfo.bgType === 'solid' ? pageinfo.navBgColor : '',\r\n\t\t\t\tbackgroundColor: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '#ffffff' :\r\n\t\t\t\t\t(pageinfo.bgType === 'gradient' && pageinfo.topGradient === '1' ? 'transparent' :\r\n\t\t\t\t\t(pageinfo.bgType === 'solid' ? pageinfo.navBgColor : 'transparent')),\r\n\t\t\t\tbackgroundImage: isValidPageInfo() && pageinfo.bgType === 'gradient' && pageinfo.topGradient !== '1' ?\r\n\t\t\t\t\t`linear-gradient(${pageinfo.gradientDirection || 'to bottom'}, ${pageinfo.gradientStart || 'rgba(0,0,0,0.6)'}, ${pageinfo.gradientEnd || 'rgba(0,0,0,0)'} 50%, transparent 100%)` :\r\n\t\t\t\t\t(isValidPageInfo() && pageinfo.bgImage ? `url(${pageinfo.bgImage})` : 'none'),\r\n\t\t\t\tdisplay: isValidPageInfo() && pageinfo.menuStyle === 'hidden' ? 'none' : 'block',\r\n\t\t\t\tpaddingTop: menuTop+'px'\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<view class=\"menu-content\" \r\n\t\t\t\t:class=\"{ 'menu-content-default': !isValidPageInfo() || pageinfo.menuStyle === 'default' }\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\ttextAlign: !isValidPageInfo() ? 'center' : pageinfo.menuAlign,\r\n\t\t\t\t\tcolor: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '#000000' : pageinfo.menuTextColor,\r\n\t\t\t\t\theight: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '100%' : (pageinfo.menuHeight - pageinfo.searchTopMargin + 'px'),\r\n\t\t\t\t\tlineHeight: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? 'normal' : (pageinfo.menuHeight - pageinfo.searchTopMargin + 'px')\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<!-- 默认模式下的标题 -->\r\n\t\t\t\t<template v-if=\"!isValidPageInfo() || pageinfo.menuStyle === 'default'\">\r\n\t\t\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t\t\t<view class=\"nav-content\" :style=\"{ height: '44px' }\">\r\n\t\t\t\t\t\t<text class=\"default-title\">{{pageinfo.title || title || sysset.name || ''}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 其他模式的内容 -->\r\n\t\t\t\t<template v-else>\r\n\t\t\t\t\t<view class=\"menu-left\">\r\n\t\t\t\t\t\t<!-- Logo模式 -->\r\n\t\t\t\t\t\t<template v-if=\"pageinfo.leftMode !== 'location'\">\r\n\t\t\t\t\t\t\t<image class=\"menu-logo\" :src=\"sysset.logo\" mode=\"aspectFit\"/>\r\n\t\t<!-- \t\t\t\t\t<text class=\"menu-title\" :style=\"{ color: getTextColor() }\">\r\n\t\t\t\t\t\t\t\t{{pageinfo.title}}\r\n\t\t\t\t\t\t\t</text> -->\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 位置模式 -->\r\n\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t<view class=\"location-wrapper\" @tap=\"handleLocationTap\">\r\n\t\t\t\t\t\t\t\t<view class=\"location-content\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"location-text\" :style=\"{ color: getTextColor() }\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"pageinfo.locationType === 'nearby'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"isLocating\">定位中...</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-else-if=\"locationFailed\">定位失败</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-else>{{ area || '获取位置' }}</text>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{ currentAreaName || '选择区域' }}</text>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t\t\t\tclass=\"arrow-icon\" \r\n\t\t\t\t\t\t\t\t\t\t:class=\"{ 'is-locating': isLocating }\"\r\n\t\t\t\t\t\t\t\t\t\tsrc=\"/static/img/arrowdown.png\" \r\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 搜索框根据配置显示 -->\r\n\t\t\t\t\t<view v-if=\"pageinfo.showSearch === '1'\" \r\n\t\t\t\t\t\tclass=\"menu-right search-container\"\r\n\t\t\t\t\t\t:class=\"[\r\n\t\t\t\t\t\t\tpageinfo.searchPosition === 'left' ? 'search-left' : \r\n\t\t\t\t\t\t\tpageinfo.searchPosition === 'center' ? 'search-center' : \r\n\t\t\t\t\t\t\t'search-right'\r\n\t\t\t\t\t\t]\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"search-box\" \r\n\t\t\t\t\t\t\t@tap=\"handleSearchTap\" \r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\twidth: pageinfo.searchWidth + 'px',\r\n\t\t\t\t\t\t\t\theight: pageinfo.searchHeight + 'px',\r\n\t\t\t\t\t\t\t\tlineHeight: pageinfo.searchHeight + 'px',\r\n\t\t\t\t\t\t\t\tborderRadius: pageinfo.searchRadius + 'px',\r\n\t\t\t\t\t\t\t\tbackgroundColor: getSearchBoxBackground()\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t\tsrc=\"/static/img/search.png\" \r\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\t\twidth: (pageinfo.searchHeight * 0.5) + 'px',\r\n\t\t\t\t\t\t\t\t\theight: (pageinfo.searchHeight * 0.5) + 'px'\r\n\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<text :style=\"{ \r\n\t\t\t\t\t\t\t\tfontSize: (pageinfo.searchHeight * 0.4) + 'px',\r\n\t\t\t\t\t\t\t\tcolor: getSearchTextColor()\r\n\t\t\t\t\t\t\t}\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{{'搜索'}}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 导航栏占位元素 -->\r\n\t\t<view v-if=\"!isValidPageInfo() || pageinfo.menuStyle === 'fixed' || pageinfo.menuStyle === 'default'\" \r\n\t\t\t:style=\"{ height: (menuTop+40) + 'px' }\"\r\n\t\t></view>\r\n\t\t\r\n\t\t<!-- 添加顶部渐变延伸效果，从导航栏开始无缝延伸 -->\r\n\t\t<view v-if=\"isValidPageInfo() && pageinfo.bgType === 'gradient' && pageinfo.topGradient === '1'\"\r\n\t\t\tclass=\"top-gradient-extension\"\r\n\t\t\t:style=\"{\r\n\t\t\t\tbackgroundImage: `linear-gradient(${pageinfo.gradientDirection || 'to bottom'}, ${pageinfo.gradientStart || 'rgba(0,0,0,0.6)'}, ${pageinfo.gradientEnd || 'rgba(0,0,0,0)'} 30%, transparent 100%)`,\r\n\t\t\t\ttop: '0px'\r\n\t\t\t}\"\r\n\t\t></view>\r\n\t\t<!-- #endif -->\r\n\t\t\r\n\t\t<block v-if=\"xixie && xdata && xdata.xixie_mendian\">\r\n\t\t\t<dp-xixie-mendian :mendian_data=\"xdata.mendian_data\" @changePopupAddress=\"changePopupAddress\"></dp-xixie-mendian>\r\n\t\t</block>\r\n\r\n\t\t<block v-if=\"platform == 'wx' && xdata.xixie_mendian\">\r\n\t\t\t<view class=\"navigation\" :style=\"{ height: 44 + statusBarHeight + 'px', background: 'transparent' }\">\r\n\t\t\t\t<view :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t\t<view class=\"navcontent\">\r\n\t\t\t\t\t<view class=\"topinfo\">\r\n\t\t\t\t\t\t<image class=\"topinfoicon\" :src=\"sysset.logo\" />\r\n\t\t\t\t\t\t<view class=\"topinfotxt\" :style=\"{ color: '#fff' }\">{{ sysset.name }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"topsearch\" :style=\"{ width: screenWidth - 210 + 'px', background: 'rgba(255,255,255,0.2)' }\" @tap=\"goto\" data-url=\"/shopPackage/shop/search\">\r\n\t\t\t\t\t\t<image src=\"/static/img/search.png\" />\r\n\t\t\t\t\t\t<text style=\"font-size: 24rpx; color: #fff\">搜索感兴趣的商品</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"width: 100%\" :style=\"{ height: 44 + statusBarHeight + 'px' }\"></view>\r\n\t\t</block>\r\n\t\t\r\n\t\t<block v-if=\"sysset.mode == 1\">\r\n\t\t\t<view class=\"navigation\" :style=\"{ \r\n\t\t\t\theight: 44 + 'px', \r\n\t\t\t\tbackground: 'rgba(255,255,255,0.95)',\r\n\t\t\t\tbackdropFilter: 'blur(10px)',\r\n\t\t\t\tWebkitBackdropFilter: 'blur(10px)'\r\n\t\t\t}\">\r\n\t\t\t\t<view class=\"navcontent\">\r\n\t\t\t\t\t<view class=\"topinfo\">\r\n\t\t\t\t\t\t<image class=\"topinfoicon\" :src=\"sysset.logo\" mode=\"aspectFill\"/>\r\n\t\t\t\t\t\t<view class=\"topinfotxt\" :style=\"{ \r\n\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\tfontWeight: '600'\r\n\t\t\t\t\t\t}\">{{ sysset.name }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"topR\">\r\n\t\t\t\t\t\t<text class=\"btn-text\" \r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\tbackground: 'linear-gradient(90deg,' + t('color1') + ' 0%,rgba(' + t('color1rgb') + ',0.8) 100%)',\r\n\t\t\t\t\t\t\t\tboxShadow: '0 2px 8px ' + t('color1') + '40'\r\n\t\t\t\t\t\t\t}\" \r\n\t\t\t\t\t\t\**********=\"goto\" \r\n\t\t\t\t\t\t\tdata-url=\"/pagesExt/business/clist2\">\r\n\t\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t\tclass=\"switch-icon\" \r\n\t\t\t\t\t\t\t\t:src=\"pre_url+'/static/img/switch.png'\" \r\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\t\twidth: '16px',\r\n\t\t\t\t\t\t\t\t\theight: '16px',\r\n\t\t\t\t\t\t\t\t\ttransform: 'scale(1.2)',\r\n\t\t\t\t\t\t\t\t\timageRendering: 'crisp-edges'\r\n\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t切换\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<block v-if=\"sysset.address\">\r\n\t\t\t\t\t\t\t<text class=\"address-text\">{{ sysset.address }}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"width: 100%\" :style=\"{ height: 44 + 'px' }\"></view>\r\n\t\t</block>\r\n\r\n\t\t<!-- 外网模式 -->\r\n\t\t<block v-if=\"sysset.mode == 2\">\r\n\t\t\t<view class=\"outernet-mode\">\r\n\t\t\t\t<view class=\"loading-container\">\r\n\t\t\t\t\t<image v-if=\"sysset.logo\" class=\"outernet-logo\" :src=\"sysset.logo\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view class=\"outernet-title\" v-if=\"sysset.name\">{{sysset.name}}</view>\r\n\t\t\t\t\t<view class=\"spinner\"></view>\r\n\t\t\t\t\t<view class=\"loading-text\">正在加载...</view>\r\n\t\t\t\t\t<view class=\"manual-jump\" v-if=\"sysset.outernet_url\" @tap=\"manualJump\">\r\n\t\t\t\t\t\t<text>点击进入</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\r\n\t\t<block v-if=\"sysset.agent_card == 1 && sysset.agent_card_info\">\r\n\t\t\t<view style=\"height: 10rpx\"></view>\r\n\t\t\t<view class=\"agent-card\">\r\n\t\t\t\t<view class=\"flex-y-center row1\">\r\n\t\t\t\t\t<image class=\"logo\" :src=\"sysset.agent_card_info.logo\" />\r\n\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t<view class=\"title limitText flex\">{{ sysset.agent_card_info.shopname }}</view>\r\n\t\t\t\t\t\t<view class=\"limitText grey-text\">{{ sysset.agent_card_info.address }}</view>\r\n\t\t\t\t\t\t<view class=\"grey-text flex-y-center\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/my.png'\"></image>\r\n\t\t\t\t\t\t\t<view>{{ sysset.agent_card_info.name }}</view>\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/tel.png'\" style=\"margin-left: 30rpx\"></image>\r\n\t\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'tel::' + sysset.agent_card_info.tel\" style=\"position: relative\">\r\n\t\t\t\t\t\t\t\t{{ sysset.agent_card_info.tel }}\r\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'tel::' + sysset.agent_card_info.tel\">拨打</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t<image :src=\"pre_url + '/static/img/shop_vip.png'\" mode=\"aspectFit\" style=\"width: 180rpx; height: 48.5rpx\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-y-center flex-x-center agent-card-b\" :style=\"{ background: t('color2') }\">\r\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pagesExt/agent/card'\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/shop.png'\"></image>\r\n\t\t\t\t\t\t店铺信息\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/commission/poster'\">\r\n\t\t\t\t\t\t<image class=\"img img2\" :src=\"pre_url + '/static/img/card.png'\"></image>\r\n\t\t\t\t\t\t店铺海报\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\r\n\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t<view style=\"width: 100%; height: 88rpx\"></view>\r\n\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t<view class=\"headimg\">\r\n\t\t\t\t\t<image :src=\"sysset.logo\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"i\">\r\n\t\t\t\t\t\t欢迎进入\r\n\t\t\t\t\t\t<text :style=\"{ color: t('color1') }\">{{ sysset.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{ 'background-color': t('color1') }\">立即关注</view>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\" />\r\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width: 100%; height: 100%\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t</block>\r\n\r\n\t\t<dp :pagecontent=\"pagecontent\" :menuindex=\"menuindex\" @getdata=\"getdata\"></dp>\r\n\r\n\t\t<view :class=\"sysset.ddbb_position == 'bottom' ? 'bobaobox_bottom' : 'bobaobox'\" v-if=\"oglist && oglist.length > 0\">\r\n\t\t\t<swiper style=\"position: relative; height: 54rpx; width: 450rpx\" autoplay=\"true\" :interval=\"5000\" vertical=\"true\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in oglist\" :key=\"index\" @tap=\"goto\" :data-url=\"item.tourl\" class=\"flex-y-center\">\r\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width: 40rpx; height: 40rpx; border: 1px solid rgba(255, 255, 255, 0.7); border-radius: 50%; margin-right: 4px\"></image>\r\n\t\t\t\t\t<view style=\"width: 400rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 22rpx\">\r\n\t\t\t\t\t\t<text style=\"padding-right: 2px\">{{ item.nickname }}</text>\r\n\t\t\t\t\t\t<text style=\"padding-right: 4px\">{{ item.showtime }}</text>\r\n\t\t\t\t\t\t<text style=\"padding-right: 2px\" v-if=\"item.type == 'collage' && item.buytype == '2'\">发起拼团</text>\r\n\t\t\t\t\t\t<text v-else>购买了</text>\r\n\t\t\t\t\t\t<text>{{ item.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<block v-if=\"xixie && xdata && xdata.popup_address\">\r\n\t\t\t<dp-xixie-popup-address\r\n\t\t\t\t:xixie_login=\"xdata.xixie_login\"\r\n\t\t\t\t:xixie_location=\"xdata.xixie_location\"\r\n\t\t\t\t:address_latitude=\"latitude\"\r\n\t\t\t\t:address_longitude=\"longitude\"\r\n\t\t\t\t:code=\"code\"\r\n\t\t\t\t@changePopupAddress=\"changePopupAddress\"\r\n\t\t\t\t@getdata=\"getdata\"\r\n\t\t\t\t@setMendianData=\"setMendianData\"\r\n\t\t\t></dp-xixie-popup-address>\r\n\t\t</block>\r\n\t\t<block v-if=\"xixie && display_buy\">\r\n\t\t\t<dp-xixie-buycart :cartnum=\"cartnum\" :cartprice=\"cartprice\" :color=\"t('color1')\" :colorrgb=\"t('color1rgb')\"></dp-xixie-buycart>\r\n\t\t</block>\r\n\r\n\t\t<view v-if=\"copyright != ''\" class=\"copyright\">{{ copyright }}</view>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<dp-guanggao\r\n\t\t\t:guanggaopic=\"guanggaopic\"\r\n\t\t\t:guanggaourl=\"guanggaourl\"\r\n\t\t\t:guanggaostatus=\"guanggaostatus\"\r\n\t\t\t@close-guanggao=\"closeGuanggao\"\r\n\t\t></dp-guanggao>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex: -1,\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\tplatform: app.globalData.platform,\r\n\t\t\thomeNavigationCustom: app.globalData.homeNavigationCustom,\r\n\t\t\tnavigationBarBackgroundColor: app.globalData.navigationBarBackgroundColor,\r\n\t\t\tnavigationBarTextStyle: app.globalData.navigationBarTextStyle,\r\n\r\n\t\t\tid: 0,\r\n\t\t\tpageinfo: {\r\n\t\t\t\t// 设置默认菜单样式为居中标题\r\n\t\t\t\tmenuStyle: 'default',\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tmenuAlign: 'center',\r\n\t\t\t\tbgType: 'solid',\r\n\t\t\t\tnavBgColor: '#ffffff',\r\n\t\t\t\ttopGradient: '0' // 添加顶部渐变延伸控制\r\n\t\t\t},\r\n\t\t\tpagecontent: [],\r\n\t\t\tsysset: {},\r\n\t\t\ttitle: '',\r\n\t\t\toglist: [],\r\n\t\t\tguanggaopic: '',\r\n\t\t\tguanggaourl: '',\r\n\t\t\tguanggaostatus: '1', // 广告显示状态\r\n\t\t\tcopyright: '',\r\n\t\t\tlatitude: '',\r\n\t\t\tlongitude: '',\r\n\t\t\tarea: '',\r\n\t\t\tlocationInfo: null,\r\n\t\t\tstatusBarHeight: 20,\r\n\t\t\tscreenWidth: 375,\r\n\t\t\tbusiness: [],\r\n\r\n\t\t\txixie: false,\r\n\t\t\txdata: '',\r\n\t\t\tdisplay_buy: '',\r\n\t\t\tcartnum: 0,\r\n\t\t\tcartprice: 0,\r\n\t\t\tcode: '',\r\n\t\t\tarea_id: '',\r\n\t\t\tarea_name: '',\r\n\t\t\tbannerList: [\r\n\t\t\t\t{ image: '/static/images/banner1.png' },\r\n\t\t\t\t{ image: '/static/images/banner2.png' },\r\n\t\t\t\t{ image: '/static/images/banner3.png' }\r\n\t\t\t],\r\n\t\t\tscrollTop: 0,\r\n\r\n\t\t\t// 区域相关数据\r\n\t\t\tareaEnabled: false, // 区域功能是否启用\r\n\t\t\tcurrentAreaId: '', // 当前选中的区域ID\r\n\t\t\tcurrentAreaName: '', // 当前区域名称\r\n\t\t\tareaList: {}, // 区域列表\r\n\t\t\tisLocating: false, // 是否正在定位中\r\n\t\t\tlocationFailed: false, // 定位是否失败\r\n\t\t\tmenuTop : 0,\r\n\r\n\t\t\t// 开屏相关数据\r\n\t\t\tshowSplash: false,\r\n\t\t\tsplashConfig: {},\r\n\t\t\tsplashStartTime: 0,\r\n\t\t\tsplashDuration: 0,\r\n\t\t\tskipCountdown: 0,\r\n\t\t\tprogressPercent: 0,\r\n\t\t\tsplashTimer: null,\r\n\t\t\tsplashVideo: null,\r\n\t\t\tsplashImage: null,\r\n\t\t};\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tlet that = this;\r\n\t\tthat.opt = app.getopts(opt);\r\n\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\r\n\t\tthat.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\tthat.screenWidth = sysinfo.screenWidth;\r\n\r\n\t\t// 设置默认导航栏标题\r\n\t\tuni.setNavigationBarTitle({\r\n\t\t\ttitle: that.pageinfo.title || that.title || ''\r\n\t\t});\r\n\t\t\r\n\t\t// #ifdef MP-WEIXIN \r\n\t\tlet menu = uni.getMenuButtonBoundingClientRect();\r\n\t\tthat.menuTop = menu.top; \r\n        console.log(menu);\r\n\t\t// #endif\r\n\r\n\t\t// 初始化区域数据\r\n\t\tthat.initAreaData();\r\n\r\n\t\t// 监听区域选择事件\r\n\t\tuni.$on('city', function(data) {\r\n\t\t\tthat.currentAreaId = data.id;\r\n\t\t\tthat.currentAreaName = data.name;\r\n\r\n\t\t\tuni.setStorageSync('area_id', data.id);\r\n\t\t\tuni.setStorageSync('area_name', data.name);\r\n\r\n\t\t\tthat.getdata();\r\n\t\t});\r\n\r\n\t\t// 监听位置选择事件\r\n\t\tuni.$on('location_selected', function(data) {\r\n\t\t\tthat.latitude = data.latitude;\r\n\t\t\tthat.longitude = data.longitude;\r\n\t\t\tthat.area = data.display_name || (data.district + data.street); // 优先使用显示名称\r\n\t\t\tthat.locationInfo = data;\r\n\t\t\t\r\n\t\t\t// 保存位置信息\r\n\t\t\tuni.setStorageSync('location_info', data);\r\n\t\t\t\r\n\t\t\t// 重新获取数据\r\n\t\t\tthat.getdata(1);\r\n\t\t});\r\n\r\n\t\t// 监听商家切换事件\r\n\t\tuni.$on('updateAfterSwitch', (data) => {\r\n\t\t\tconsole.log('Received update event with data:', data);\r\n\t\t\tif (data && data.select_bid) {\r\n\t\t\t\t// 先保存新的select_bid到缓存\r\n\t\t\t\tapp.setCache('select_bid', data.select_bid);\r\n\t\t\t\t\r\n\t\t\t\t// 检测是否为iOS设备\r\n\t\t\t\tconst isIOS = uni.getSystemInfoSync().platform === 'ios';\r\n\t\t\t\tconsole.log('当前设备平台:', isIOS ? 'iOS' : '非iOS');\r\n\t\t\t\t\r\n\t\t\t\tif (isIOS) {\r\n\t\t\t\t\t// iOS设备使用更安全的方式：先更新数据，再使用navigateTo\r\n\t\t\t\t\tconsole.log('iOS设备：使用优化的页面切换方式');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 直接更新当前页面数据\r\n\t\t\t\t\tthis.opt.select_bid = data.select_bid;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示加载中\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '加载中...',\r\n\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 先获取数据\r\n\t\t\t\t\tthis.getdata();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 延迟关闭加载提示\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 非iOS设备使用原来的reLaunch方式\r\n\t\t\t\t\tconst newUrl = `/pages/index/index?select_bid=${data.select_bid}`;\r\n\t\t\t\t\tconsole.log('非iOS设备：使用reLaunch刷新页面，URL:', newUrl);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 使用reLaunch强制刷新页面\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: newUrl,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tconsole.log('Page refreshed with new URL:', newUrl);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('Failed to refresh page:', err);\r\n\t\t\t\t\t\t\t// 如果失败，至少更新数据\r\n\t\t\t\t\t\t\tthis.opt.select_bid = data.select_bid;\r\n\t\t\t\t\t\t\tthis.getdata();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// 检查是否需要自动定位\r\n\t\t// 从缓存中获取上次的位置信息\r\n\t\tconst lastLocation = uni.getStorageSync('location_info');\r\n\t\tif (lastLocation) {\r\n\t\t\tthis.latitude = lastLocation.latitude;\r\n\t\t\tthis.longitude = lastLocation.longitude;\r\n\t\t\tthis.area = lastLocation.district + lastLocation.street;\r\n\t\t\tthis.locationInfo = lastLocation;\r\n\t\t}\r\n\r\n\t\t// 获取数据，不自动请求定位\r\n\t\tthat.getdata();\r\n\t},\r\n\r\n\tonPullDownRefresh: function (e) {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPageScroll: function (e) {\r\n\t\tuni.$emit('onPageScroll', e);\r\n\t},\r\n\tonUnload() {\r\n\t\t// 移除事件监听\r\n\t\tuni.$off('city');\r\n\t\tuni.$off('location_selected');\r\n\t\t// 清理事件监听\r\n\t\tuni.$off('updateAfterSwitch');\r\n\t\t\r\n\t\t// 清理开屏定时器\r\n\t\tif (this.splashTimer) {\r\n\t\t\tclearInterval(this.splashTimer);\r\n\t\t\tthis.splashTimer = null;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 显示开屏\r\n\t\tshowSplashScreen() {\r\n\t\t\tconsole.log('显示开屏');\r\n\t\t\tthis.showSplash = true;\r\n\t\t\t\r\n\t\t\t// 通过事件通知tabbar隐藏\r\n\t\t\tuni.$emit('hideTabbar', true);\r\n\t\t\t\r\n\t\t\tthis.startSplashCountdown();\r\n\t\t\tthis.recordSplashShow();\r\n\t\t},\r\n\r\n\t\t// 隐藏开屏\r\n\t\thideSplashScreen() {\r\n\t\t\tconsole.log('隐藏开屏');\r\n\t\t\tthis.showSplash = false;\r\n\t\t\t\r\n\t\t\t// 通过事件通知tabbar显示\r\n\t\t\tuni.$emit('hideTabbar', false);\r\n\t\t\t\r\n\t\t\t// 清理倒计时\r\n\t\t\tif (this.splashTimer) {\r\n\t\t\t\tclearInterval(this.splashTimer);\r\n\t\t\t\tthis.splashTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 开始开屏倒计时\r\n\t\tstartSplashCountdown() {\r\n\t\t\tif (!this.splashConfig) return;\r\n\t\t\t\r\n\t\t\tthis.splashStartTime = Date.now();\r\n\t\t\tthis.splashDuration = parseInt(this.splashConfig.display_time) * 1000 || 3000; // 默认3秒\r\n\t\t\tthis.skipCountdown = parseInt(this.splashConfig.skip_time) || 2;\r\n\t\t\tthis.progressPercent = 0;\r\n\t\t\t\r\n\t\t\t// 清理之前的定时器\r\n\t\t\tif (this.splashTimer) {\r\n\t\t\t\tclearInterval(this.splashTimer);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.splashTimer = setInterval(() => {\r\n\t\t\t\tconst currentTime = Date.now();\r\n\t\t\t\tconst elapsedTime = currentTime - this.splashStartTime;\r\n\t\t\t\t\r\n\t\t\t\t// 更新进度条\r\n\t\t\t\tthis.progressPercent = Math.min((elapsedTime / this.splashDuration) * 100, 100);\r\n\t\t\t\t\r\n\t\t\t\t// 更新跳过倒计时\r\n\t\t\t\tconst skipTimeMs = parseInt(this.splashConfig.skip_time) * 1000;\r\n\t\t\t\tconst remainingSkipTime = Math.max(0, Math.ceil((skipTimeMs - elapsedTime) / 1000));\r\n\t\t\t\tthis.skipCountdown = remainingSkipTime;\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否应该结束开屏\r\n\t\t\t\tif (elapsedTime >= this.splashDuration) {\r\n\t\t\t\t\tthis.hideSplashScreen();\r\n\t\t\t\t}\r\n\t\t\t}, 100);\r\n\t\t},\r\n\r\n\t\t// 跳过开屏\r\n\t\tskipSplash() {\r\n\t\t\t// 检查跳过功能是否启用\r\n\t\t\tif (this.splashConfig.skip_enabled !== 1) {\r\n\t\t\t\tconsole.log('跳过功能未启用');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否还在等待期内\r\n\t\t\tif (this.skipCountdown > 0) {\r\n\t\t\t\tconsole.log('还在跳过等待期内，剩余', this.skipCountdown, '秒');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log('用户跳过开屏');\r\n\t\t\tthis.hideSplashScreen();\r\n\t\t},\r\n\r\n\t\t// 处理开屏点击\r\n\t\thandleSplashClick() {\r\n\t\t\tif (this.splashConfig && this.splashConfig.link_url) {\r\n\t\t\t\tconsole.log('点击开屏跳转:', this.splashConfig.link_url);\r\n\t\t\t\t// 先隐藏开屏\r\n\t\t\t\tthis.hideSplashScreen();\r\n\t\t\t\t// 然后跳转\r\n\t\t\t\tapp.gotopage(this.splashConfig.link_url);\r\n\t\t\t} else {\r\n\t\t\t\t// 如果没有链接，直接隐藏开屏\r\n\t\t\t\tthis.hideSplashScreen();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 记录开屏显示统计\r\n\t\trecordSplashShow() {\r\n\t\t\tif (!this.splashConfig || !this.splashConfig.id) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconst params = {\r\n\t\t\t\tsplash_id: this.splashConfig.id,\r\n\t\t\t\tplatform: app.globalData.platform || 'unknown',\r\n\t\t\t\taction: 'show',\r\n\t\t\t\ttimestamp: Date.now()\r\n\t\t\t};\r\n\r\n\t\t\tapp.get('ApiIndex/recordsplashshow', params, function(data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tconsole.log('开屏统计记录成功');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('开屏统计记录失败:', data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查并初始化开屏\r\n\t\tcheckAndInitSplash(splashConfig) {\r\n\t\t\tif (!splashConfig || splashConfig.is_enabled != 1) {\r\n\t\t\t\tconsole.log('开屏未启用');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 验证开屏内容\r\n\t\t\tif (splashConfig.display_type === 'image' && !splashConfig.image_url) {\r\n\t\t\t\tconsole.log('开屏图片URL为空');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (splashConfig.display_type === 'video' && !splashConfig.video_url) {\r\n\t\t\t\tconsole.log('开屏视频URL为空');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('开屏配置有效，准备显示:', splashConfig);\r\n\t\t\t\r\n\t\t\tthis.splashConfig = splashConfig;\r\n\t\t\t\r\n\t\t\t// 延迟一下显示开屏，确保页面已经渲染\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.showSplashScreen();\r\n\t\t\t}, 100);\r\n\t\t},\r\n\t\t\r\n\t\tgetdata(event) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar opt = this.opt;\r\n\t\t\tvar id = 0;\r\n\t\t\t\r\n\t\t\t// 检查是否是特定组件的刷新请求\r\n\t\t\tlet isSpecificComponent = false;\r\n\t\t\tlet componentId = '';\r\n\t\t\tlet componentStyle = '';\r\n\t\t\t\r\n\t\t\t// 判断是否为对象参数（来自组件的刷新请求）\r\n\t\t\tif (event && typeof event === 'object') {\r\n\t\t\t\t// 兼容不同环境的参数结构\r\n\t\t\t\tcomponentId = event.componentId || event.id || '';\r\n\t\t\t\tcomponentStyle = event.style || '';\r\n\t\t\t\tconst forceRefresh = event.forceRefresh || false;\r\n\t\t\t\t\r\n\t\t\t\tif (componentId) {\r\n\t\t\t\t\tisSpecificComponent = true;\r\n\t\t\t\t\tconsole.log('首页收到特定组件刷新请求:', {\r\n\t\t\t\t\t\t组件ID: componentId,\r\n\t\t\t\t\t\t样式: componentStyle,\r\n\t\t\t\t\t\t强制刷新: forceRefresh,\r\n\t\t\t\t\t\t时间戳: event._timestamp || new Date().getTime()\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理强制刷新标记\r\n\t\t\t\t\tif (forceRefresh) {\r\n\t\t\t\t\t\tconsole.log('检测到强制刷新标记，将模拟前端刷新');\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 尝试直接在前端进行数据刷新\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst targetComponent = this.findComponentById(componentId);\r\n\t\t\t\t\t\t\tif (targetComponent) {\r\n\t\t\t\t\t\t\t\tconsole.log('找到目标组件:', targetComponent.temp, targetComponent.id);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 确保组件有数据\r\n\t\t\t\t\t\t\t\tif (targetComponent.data && targetComponent.data.length > 0) {\r\n\t\t\t\t\t\t\t\t\t// 随机排序数据\r\n\t\t\t\t\t\t\t\t\tconst shuffled = [...targetComponent.data].sort(() => 0.5 - Math.random());\r\n\t\t\t\t\t\t\t\t\t// 直接更新组件数据\r\n\t\t\t\t\t\t\t\t\ttargetComponent.data = shuffled;\r\n\t\t\t\t\t\t\t\t\tconsole.log('前端数据刷新成功!');\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 告知用户\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '换一换成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 无需继续向后端发送请求\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('前端刷新数据失败:', error);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 从URL参数中获取select_bid\r\n\t\t\tconst pages = getCurrentPages();\r\n\t\t\tconst currentPage = pages[pages.length - 1];\r\n\t\t\tconst query = currentPage.options || {};\r\n\t\t\t\r\n\t\t\t// 优先使用URL参数中的select_bid\r\n\t\t\tvar select_bid = query.select_bid || opt.select_bid || app.getCache('select_bid');\r\n\t\t\t\r\n\t\t\tconsole.log('Loading data with select_bid:', select_bid);\r\n\t\t\tconsole.log('Current URL:', currentPage.route, query);\r\n\r\n\t\t\tlet obj = {\r\n\t\t\t\tid: id,\r\n\t\t\t\tselect_bid: select_bid,\r\n\t\t\t\tpid: app.globalData.pid,\r\n\t\t\t\tarea: that.area\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 如果是特定组件的刷新请求，添加相关参数\r\n\t\t\tif (isSpecificComponent) {\r\n\t\t\t\tobj.componentId = componentId;\r\n\t\t\t\tobj.componentStyle = componentStyle;\r\n\t\t\t}\r\n\r\n\t\t\t// 打印请求参数以便调试\r\n\t\t\tconsole.log('Request params:', obj);\r\n\r\n\t\t\tif (typeof event === 'boolean' && event) {\r\n\t\t\t\tobj.latitude = that.latitude;\r\n\t\t\t\tobj.longitude = that.longitude;\r\n\t\t\t} else {\r\n\t\t\t\tobj.area_id = that.area_id;\r\n\t\t\t}\r\n\r\n\t\t\t// 显示加载状态\r\n\t\t\tthat.loading = true;\r\n\r\n\t\t\tapp.get('ApiIndex/index', obj, function (data) {\r\n\t\t\t\tconsole.log('API response:', data);\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (data.status == 2) {\r\n\t\t\t\t\t//付费查看\r\n\t\t\t\t\tapp.goto('/pages/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id, 'redirect');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t// 处理特定组件的刷新\r\n\t\t\t\t\tif (isSpecificComponent && data.componentData) {\r\n\t\t\t\t\t\tconsole.log('接收到特定组件数据:', data.componentData);\r\n\t\t\t\t\t\t// 查找并更新特定组件\r\n\t\t\t\t\t\tfor (let i = 0; i < that.pagecontent.length; i++) {\r\n\t\t\t\t\t\t\tif (that.pagecontent[i].id === componentId) {\r\n\t\t\t\t\t\t\t\t// 只更新数据部分，保留其他属性\r\n\t\t\t\t\t\t\t\tthat.pagecontent[i].data = data.componentData;\r\n\t\t\t\t\t\t\t\tconsole.log('已更新组件数据:', componentId, that.pagecontent[i].temp);\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn; // 只更新组件数据，不做全局刷新\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar pagecontent = data.pagecontent;\r\n\t\t\t\t\tthat.title = data.pageinfo.title || '';\r\n\t\t\t\t\tif (data.oglist) that.oglist = data.oglist;\r\n\t\t\t\t\tthat.guanggaopic = data.guanggaopic;\r\n\t\t\t\t\tthat.guanggaourl = data.guanggaourl;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查开屏配置\r\n\t\t\t\t\tif (data.splash_config) {\r\n\t\t\t\t\t\tconsole.log('接收到开屏配置:', data.splash_config);\r\n\t\t\t\t\t\tthat.checkAndInitSplash(data.splash_config);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查pageinfo是否为空对象或未定义\r\n\t\t\t\t\tif (!data.pageinfo || Object.keys(data.pageinfo).length === 0) {\r\n\t\t\t\t\t\tconsole.log('未接收到后端菜单配置，使用默认居中标题样式');\r\n\t\t\t\t\t\t// 保持默认值不变\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 合并默认值和后端配置\r\n\t\t\t\t\t\tthat.pageinfo = Object.assign({}, that.pageinfo, data.pageinfo);\r\n\t\t\t\t\t\t// 确保title被正确设置\r\n\t\t\t\t\t\tthat.pageinfo.title = data.pageinfo.title || that.title || that.sysset.name || '';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.copyright = data.copyright;\r\n\t\t\t\t\tthat.sysset = data.sysset;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 门店模式下的处理\r\n\t\t\t\t\tif (data.sysset.mode == 1) {\r\n\t\t\t\t\t\tconsole.log('门店模式：主动请求定位');\r\n\t\t\t\t\t\t// 如果没有位置信息，主动请求定位\r\n\t\t\t\t\t\tif (!that.latitude || !that.longitude) {\r\n\t\t\t\t\t\t\tthat.getCurrentLocation();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.business) {\r\n\t\t\t\t\t\t\tthat.business = data.business;\r\n\t\t\t\t\t\t\tif (select_bid == '') app.setCache('select_bid', data.business.id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 外网模式处理 - 自动跳转到外部URL\r\n\t\t\t\t\tif (data.sysset.mode == 2 && data.sysset.outernet_url) {\r\n\t\t\t\t\t\tconsole.log('外网模式：准备跳转到外部URL', data.sysset.outernet_url);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 添加超时处理，如果3秒内没有成功跳转，仅显示手动跳转按钮\r\n\t\t\t\t\t\tlet jumpTimer = setTimeout(() => {\r\n\t\t\t\t\t\t\tconsole.log('跳转超时，显示手动跳转按钮');\r\n\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t}, 3000);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// 使用plus.runtime.openURL在app环境下打开外部链接\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tconsole.log('APP环境：使用plus.runtime.openURL跳转');\r\n\t\t\t\t\t\t\tplus.runtime.openURL(data.sysset.outernet_url);\r\n\t\t\t\t\t\t\tclearTimeout(jumpTimer);\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 在小程序环境下，使用web-view页面跳转\r\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\r\n\t\t\t\t\t\t\tconsole.log('小程序环境：准备跳转到WebView页面');\r\n\t\t\t\t\t\t\t// 修正WebView页面路径\r\n\t\t\t\t\t\t\tconst webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(data.sysset.outernet_url);\r\n\t\t\t\t\t\t\tconsole.log('跳转到的WebView路径：', webviewUrl);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 延迟500ms再跳转，让界面先渲染出来\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 使用reLaunch替代navigateTo，关闭所有页面并跳转到新页面，这样就不会有返回按钮\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl: webviewUrl,\r\n\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('WebView跳转成功');\r\n\t\t\t\t\t\t\t\t\t\tclearTimeout(jumpTimer);\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error('WebView跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t// 跳转失败时不显示弹窗，只在日志中输出错误\r\n\t\t\t\t\t\t\t\t\t\tclearTimeout(jumpTimer);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 在H5环境下直接跳转\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tconsole.log('H5环境：使用location.href直接跳转');\r\n\t\t\t\t\t\t\twindow.location.href = data.sysset.outernet_url;\r\n\t\t\t\t\t\t\tclearTimeout(jumpTimer);\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('外网跳转发生错误：', error);\r\n\t\t\t\t\t\t\tclearTimeout(jumpTimer);\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '跳转错误',\r\n\t\t\t\t\t\t\t\tcontent: '跳转外部链接时发生错误：' + error.message,\r\n\t\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\treturn; // 阻止继续执行后续代码\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 设置导航栏标题\r\n\t\t\t\t\tconst displayTitle = that.pageinfo.title || that.title || that.sysset.name || '';\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: displayTitle\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\t\t// 初始化区域数据\r\n\t\t\t\t\tthat.initAreaData();\r\n\r\n\t\t\t\t\t// 检查是否需要自动定位（非门店模式下的定位逻辑）\r\n\t\t\t\t\tif (data.sysset.mode != 1) {\r\n\t\t\t\t\t\tlet json = JSON.parse(data.sysset.area_set || '{}');\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 从两个地方获取自动定位设置：1. area_set中的auto_location 2. pageinfo中的auto_location\r\n\t\t\t\t\t\tlet autoLocation = json.auto_location === '1' || that.pageinfo.auto_location === '1';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查是否为附近模式\r\n\t\t\t\t\t\tlet isNearbyMode = that.pageinfo.locationType === 'nearby';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('定位设置检查：', {\r\n\t\t\t\t\t\t\t'area_set.auto_location': json.auto_location,\r\n\t\t\t\t\t\t\t'pageinfo.auto_location': that.pageinfo.auto_location,\r\n\t\t\t\t\t\t\t'pageinfo.locationType': that.pageinfo.locationType,\r\n\t\t\t\t\t\t\t'最终自动定位设置': autoLocation,\r\n\t\t\t\t\t\t\t'是否为附近模式': isNearbyMode,\r\n\t\t\t\t\t\t\t'当前纬度': that.latitude,\r\n\t\t\t\t\t\t\t'当前经度': that.longitude\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 当满足以下条件时自动请求定位：\r\n\t\t\t\t\t\t// 1. 自动定位设置为开启\r\n\t\t\t\t\t\t// 2. 当前没有位置信息或位置信息为空\r\n\t\t\t\t\t\t// 3. 位置类型为附近模式\r\n\t\t\t\t\t\tif ((that.latitude === '' || that.longitude === '' || !that.latitude || !that.longitude) && \r\n\t\t\t\t\t\t\t(autoLocation && isNearbyMode)) {\r\n\t\t\t\t\t\t\tconsole.log('开始自动请求定位...');\r\n\t\t\t\t\t\t\tthat.getCurrentLocation();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.pagecontent = data.pagecontent;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.pagecontent = data.pagecontent;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (data.xixie) {\r\n\t\t\t\t\t\twx.login({\r\n\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\tthat.code = res.code;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.xixie = data.xixie;\r\n\t\t\t\t\t\tvar xdata = data.xdata;\r\n\t\t\t\t\t\tthat.xdata = xdata;\r\n\r\n\t\t\t\t\t\tthat.display_buy = xdata.display_buy ? xdata.display_buy : false;\r\n\t\t\t\t\t\tthat.cartnum = xdata.cartnum ? xdata.cartnum : 0;\r\n\t\t\t\t\t\tthat.cartprice = xdata.cartprice ? xdata.cartprice : 0;\r\n\t\t\t\t\t\tif (xdata.cart_data) {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tthat.xcart_data = xdata.cart_data;\r\n\t\t\t\t\t\t\t}, 200);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (xdata.popup_address) {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tthat.popup_address = data.popup_address;\r\n\t\t\t\t\t\t\t}, 200);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (data.msg) {\r\n\t\t\t\t\t\tapp.alert(data.msg, function () {\r\n\t\t\t\t\t\t\tif (data.url) app.goto(data.url);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else if (data.url) {\r\n\t\t\t\t\t\tapp.goto(data.url);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.alert('您无查看权限');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tshowsubqrcode: function () {\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode: function () {\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\t\tchangePopupAddress: function (status) {\r\n\t\t\tthis.xdata.popup_address = status;\r\n\t\t},\r\n\t\tsetMendianData: function (data) {\r\n\t\t\tthis.mendian_data = data;\r\n\t\t},\r\n\t\thandleBannerClick(item) {\r\n\t\t\tif (item.url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: item.url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取文字颜色\r\n\t\tgetTextColor() {\r\n\t\t\tif (!this.isValidPageInfo()) {\r\n\t\t\t\treturn '#000000'; // 默认黑色文字\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.pageinfo.menuStyle === 'float' || this.pageinfo.menuStyle === 'fixed') {\r\n\t\t\t\tif (this.pageinfo.isOpacity === '1' && this.pageinfo.opacity < 0.5) {\r\n\t\t\t\t\treturn '#fff';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn this.pageinfo.menuTextColor || '#000000';\r\n\t\t},\r\n\t\t\r\n\t\t// 获取搜索框背景色\r\n\t\tgetSearchBoxBackground() {\r\n\t\t\t// 无论什么情况都返回白色背景\r\n\t\t\treturn '#ffffff';\r\n\t\t},\r\n\t\t\r\n\t\t// 获取搜索框文字颜色\r\n\t\tgetSearchTextColor() {\r\n\t\t\tif (!this.isValidPageInfo()) {\r\n\t\t\t\treturn '#666'; // 默认搜索框文字颜色\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.pageinfo.searchTextColor) {\r\n\t\t\t\treturn this.pageinfo.searchTextColor;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.pageinfo.menuStyle === 'float' || this.pageinfo.menuStyle === 'fixed') {\r\n\t\t\t\tif (this.pageinfo.isOpacity === '1' && this.pageinfo.opacity < 0.5) {\r\n\t\t\t\t\treturn '#fff';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn '#666';\r\n\t\t},\r\n\t\thandleSearchTap() {\r\n\t\t\tconst searchUrl = this.pageinfo.hrefurl || '/shopPackage/shop/search';\r\n\t\t\tapp.goto(searchUrl);\r\n\t\t},\r\n\t\t// 初始化区域数据\r\n\t\tinitAreaData() {\r\n\t\t\t// 先尝试从缓存获取区域信息\r\n\t\t\tconst cachedAreaId = uni.getStorageSync('area_id');\r\n\t\t\tconst cachedAreaName = uni.getStorageSync('area_name');\r\n\t\t\t\r\n\t\t\tif (this.sysset && this.sysset.area_on) {\r\n\t\t\t\tthis.areaEnabled = true;\r\n\t\t\t\t\r\n\t\t\t\t// 优先使用缓存的区域信息\r\n\t\t\t\tif (cachedAreaId && cachedAreaName) {\r\n\t\t\t\t\tthis.currentAreaId = cachedAreaId;\r\n\t\t\t\t\tthis.currentAreaName = cachedAreaName;\r\n\t\t\t\t} else if (this.sysset.current_area_id && this.sysset.current_area_name) {\r\n\t\t\t\t\t// 如果没有缓存，使用系统设置的默认区域\r\n\t\t\t\t\tthis.currentAreaId = this.sysset.current_area_id;\r\n\t\t\t\t\tthis.currentAreaName = this.sysset.current_area_name;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 保存到缓存\r\n\t\t\t\t\tuni.setStorageSync('area_id', this.currentAreaId);\r\n\t\t\t\t\tuni.setStorageSync('area_name', this.currentAreaName);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果都没有，尝试使用区域列表的第一个区域\r\n\t\t\t\t\tconst areaList = this.sysset.area_list || {};\r\n\t\t\t\t\tif (Object.keys(areaList).length > 0) {\r\n\t\t\t\t\t\tconst firstArea = Object.values(areaList)[0];\r\n\t\t\t\t\t\tthis.currentAreaId = firstArea.id;\r\n\t\t\t\t\t\tthis.currentAreaName = firstArea.name;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 保存到缓存\r\n\t\t\t\t\t\tuni.setStorageSync('area_id', this.currentAreaId);\r\n\t\t\t\t\t\tuni.setStorageSync('area_name', this.currentAreaName);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 区域列表\r\n\t\t\t\tthis.areaList = this.sysset.area_list || {};\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('当前区域信息：', {\r\n\t\t\t\t\tid: this.currentAreaId,\r\n\t\t\t\t\tname: this.currentAreaName\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取当前位置 - 修改为支持自动和手动触发\r\n\t\tgetCurrentLocation() {\r\n\t\t\tif (this.isLocating) return;\r\n\t\t\tthis.isLocating = true;\r\n\t\t\tthis.locationFailed = false;\r\n\t\t\t\r\n\t\t\tconsole.log('正在获取位置...');\r\n\t\t\t\r\n\t\t\tapp.getLocation((res) => {\r\n\t\t\t\tconsole.log('位置获取成功：', res);\r\n\t\t\t\tthis.latitude = res.latitude;\r\n\t\t\t\tthis.longitude = res.longitude;\r\n\t\t\t\t\r\n\t\t\t\t// 获取详细地址信息\r\n\t\t\t\tapp.get('apiIndex/getLocation', {\r\n\t\t\t\t\tlatitude: res.latitude,\r\n\t\t\t\t\tlongitude: res.longitude,\r\n\t\t\t\t\tis_store_mode: this.sysset.mode == 1 ? 1 : 0 // 添加门店模式标识\r\n\t\t\t\t}, (result) => {\r\n\t\t\t\t\tconsole.log('地址信息获取结果：', result);\r\n\t\t\t\t\tif(result.status === 1) {\r\n\t\t\t\t\t\t// 更新地址信息\r\n\t\t\t\t\t\tthis.area = result.data.district + result.data.street; // 只显示区域和街道\r\n\t\t\t\t\t\tthis.locationInfo = result.data; // 保存完整地址信息\r\n\t\t\t\t\t\t// 保存位置信息\r\n\t\t\t\t\t\tuni.setStorageSync('location_info', result.data);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 根据模式决定是否重新获取数据\r\n\t\t\t\t\t\tif (this.sysset.mode == 1) {\r\n\t\t\t\t\t\t\t// 门店模式：使用type=1调用getdata，传递经纬度\r\n\t\t\t\t\t\t\tconsole.log('门店模式：使用位置信息重新获取数据');\r\n\t\t\t\t\t\t\tthis.getdata(1);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 非门店模式：只在需要时重新获取数据\r\n\t\t\t\t\t\t\tif (this.pageinfo.locationType === 'nearby') {\r\n\t\t\t\t\t\t\t\tthis.getdata(1);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.pagecontent = this.pagecontent;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.locationFailed = true;\r\n\t\t\t\t\t\tconsole.error('获取地址信息失败：', result.msg || '未知错误');\r\n\t\t\t\t\t\tif (this.sysset.mode == 1) {\r\n\t\t\t\t\t\t\t// 门店模式下定位失败的处理\r\n\t\t\t\t\t\t\tapp.alert('获取位置信息失败，这可能会影响到您查看附近门店的功能');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isLocating = false;\r\n\t\t\t\t});\r\n\t\t\t}, (err) => {\r\n\t\t\t\tconsole.error('获取位置失败：', err);\r\n\t\t\t\tthis.locationFailed = true;\r\n\t\t\t\tthis.isLocating = false;\r\n\t\t\t\t\r\n\t\t\t\t// 获取失败时尝试使用上次保存的位置\r\n\t\t\t\tconst lastLocation = uni.getStorageSync('location_info');\r\n\t\t\t\tif (lastLocation) {\r\n\t\t\t\t\tthis.area = lastLocation.district + lastLocation.street;\r\n\t\t\t\t\tthis.locationInfo = lastLocation;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (this.sysset.mode == 1) {\r\n\t\t\t\t\t\t// 门店模式下使用上次位置\r\n\t\t\t\t\t\tthis.latitude = lastLocation.latitude;\r\n\t\t\t\t\t\tthis.longitude = lastLocation.longitude;\r\n\t\t\t\t\t\tconsole.log('门店模式：使用上次保存的位置信息');\r\n\t\t\t\t\t\tthis.getdata(1);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.sysset.mode == 1) {\r\n\t\t\t\t\t// 门店模式下没有历史位置信息的处理\r\n\t\t\t\t\tapp.alert('获取位置信息失败，这可能会影响到您查看附近门店的功能');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理位置点击\r\n\t\thandleLocationTap() {\r\n\t\t\tif (this.pageinfo.locationType === 'nearby') {\r\n\t\t\t\t// 如果是附近模式，可以先尝试获取位置\r\n\t\t\t\tif (!this.latitude || !this.longitude) {\r\n\t\t\t\t\t// 如果没有位置信息，先获取位置\r\n\t\t\t\t\tthis.getCurrentLocation();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 跳转到位置搜索页面\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\t\tlongitude: this.longitude,\r\n\t\t\t\t\tcurrent_address: this.area,\r\n\t\t\t\t\tlocation_info: this.locationInfo\r\n\t\t\t\t};\r\n\t\t\t\tapp.goto('/pages/index/location?data=' + encodeURIComponent(JSON.stringify(params)));\r\n\t\t\t} else {\r\n\t\t\t\t// 检查是否允许切换区域\r\n\t\t\t\tconst areaConfig = JSON.parse(this.sysset.area_set || '{}');\r\n\t\t\t\tif (areaConfig.switcharea == 0) { // 当 switcharea 为 0 时允许切换区域\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tarea_list: this.sysset.area_list,\r\n\t\t\t\t\t\tcurrent_area_id: this.currentAreaId,\r\n\t\t\t\t\t\tcurrent_area_name: this.currentAreaName,\r\n\t\t\t\t\t\tarea_config: areaConfig\r\n\t\t\t\t\t};\r\n\t\t\t\t\tapp.goto('/pages/index/city?data=' + encodeURIComponent(JSON.stringify(params)));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 检查pageinfo是否有效\r\n\t\tisValidPageInfo() {\r\n\t\t\t// 检查pageinfo是否为空对象或未定义或menuStyle未定义\r\n\t\t\treturn this.pageinfo && \r\n\t\t\t\t   Object.keys(this.pageinfo).length > 0 && \r\n\t\t\t\t   this.pageinfo.menuStyle !== undefined;\r\n\t\t},\r\n\t\t// 手动跳转到外部URL\r\n\t\tmanualJump: function() {\r\n\t\t\tconst that = this;\r\n\t\t\tif (!that.sysset || !that.sysset.outernet_url) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '跳转提示',\r\n\t\t\t\t\tcontent: '无效的外部链接',\r\n\t\t\t\t\tshowCancel: false\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log('手动跳转到外部URL：', that.sysset.outernet_url);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// APP环境\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.runtime.openURL(that.sysset.outernet_url);\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 小程序环境\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\r\n\t\t\t\tconst webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(that.sysset.outernet_url);\r\n\t\t\t\t// 使用reLaunch关闭所有页面并跳转到新页面，去除返回按钮\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: webviewUrl,\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tconsole.error('手动跳转失败', err);\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '跳转提示',\r\n\t\t\t\t\t\t\tcontent: '跳转失败，请稍后再试或联系管理员',\r\n\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// H5环境\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.location.href = that.sysset.outernet_url;\r\n\t\t\t\t// #endif\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('手动跳转发生错误：', error);\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '跳转错误',\r\n\t\t\t\t\tcontent: '跳转外部链接时发生错误：' + error.message,\r\n\t\t\t\t\tshowCancel: false\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 新增方法：根据ID查找组件\r\n\t\tfindComponentById(id) {\r\n\t\t\tif (!this.pagecontent || !this.pagecontent.length) return null;\r\n\r\n\t\t\tfor (let i = 0; i < this.pagecontent.length; i++) {\r\n\t\t\t\tif (this.pagecontent[i].id === id) {\r\n\t\t\t\t\treturn this.pagecontent[i];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\t\t},\r\n\r\n\t\t// 关闭广告弹窗\r\n\t\tcloseGuanggao() {\r\n\t\t\tconsole.log('关闭广告弹窗');\r\n\t\t\tthis.guanggaostatus = '0';\r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n.topR {\r\n\tflex: 1;\r\n\tdisplay: -webkit-box;\r\n\t-webkit-box-orient: vertical;\r\n\t-webkit-line-clamp: 1;\r\n\toverflow: hidden;\r\n\tcolor: #666;\r\n}\r\n\r\n.topR .btn-text {\r\n\tpadding: 4px 12px;\r\n\tbackground: v-bind('t(\"color1\")');\r\n\tcolor: #fff;\r\n\tborder-radius: 16px;\r\n\tfont-size: 13px;\r\n\tfont-weight: 500;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 2px 8px v-bind('`${t(\"color1\")}40`');\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 4px;\r\n}\r\n\r\n\r\n\r\n.btn-text:active {\r\n\ttransform: scale(0.95);\r\n\topacity: 0.9;\r\n\tbox-shadow: 0 1px 4px v-bind('`${t(\"color1\")}20`');\r\n}\r\n\r\n.address-text {\r\n\tfont-size: 13px;\r\n\tcolor: #333;\r\n\tmax-width: 180px;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\topacity: 0.85;\r\n\tpadding-left: 8px;\r\n\tposition: relative;\r\n}\r\n\r\n.address-text::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\twidth: 1px;\r\n\theight: 12px;\r\n\tbackground: rgba(0,0,0,0.08);\r\n}\r\n\r\n.follow_topbar {\r\n\theight: 88rpx;\r\n\twidth: 100%;\r\n\tmax-width: 640px;\r\n\tbackground: rgba(0, 0, 0, 0.8);\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tz-index: 13;\r\n}\r\n\r\n.follow_topbar .headimg {\r\n\theight: 64rpx;\r\n\twidth: 64rpx;\r\n\tmargin: 6px;\r\n\tfloat: left;\r\n}\r\n\r\n.follow_topbar .headimg image {\r\n\theight: 64rpx;\r\n\twidth: 64rpx;\r\n}\r\n\r\n.follow_topbar .info {\r\n\theight: 56rpx;\r\n\tpadding: 16rpx 0;\r\n}\r\n\r\n.follow_topbar .info .i {\r\n\theight: 28rpx;\r\n\tline-height: 28rpx;\r\n\tcolor: #ccc;\r\n\tfont-size: 24rpx;\r\n}\r\n\r\n.follow_topbar .info {\r\n\theight: 80rpx;\r\n\tfloat: left;\r\n}\r\n\r\n.follow_topbar .sub {\r\n\theight: 48rpx;\r\n\twidth: auto;\r\n\tbackground: #fc4343;\r\n\tpadding: 0 20rpx;\r\n\tmargin: 20rpx 16rpx 20rpx 0;\r\n\tfloat: right;\r\n\tfont-size: 24rpx;\r\n\tcolor: #fff;\r\n\tline-height: 52rpx;\r\n\tborder-radius: 6rpx;\r\n}\r\n\r\n.qrcodebox {\r\n\tbackground: #fff;\r\n\tpadding: 50rpx;\r\n\tposition: relative;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.qrcodebox .img {\r\n\twidth: 400rpx;\r\n\theight: 400rpx;\r\n}\r\n\r\n.qrcodebox .txt {\r\n\tcolor: #666;\r\n\tmargin-top: 20rpx;\r\n\tfont-size: 26rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.qrcodebox .close {\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n\tposition: absolute;\r\n\tbottom: -100rpx;\r\n\tleft: 50%;\r\n\tmargin-left: -25rpx;\r\n\tborder: 1px solid rgba(255, 255, 255, 0.6);\r\n\tborder-radius: 50%;\r\n\tpadding: 8rpx;\r\n}\r\n\r\n.bobaobox {\r\n\tposition: fixed;\r\n\ttop: calc(var(--window-top) + 180rpx);\r\n\tleft: 20rpx;\r\n\tz-index: 10;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tborder-radius: 30rpx;\r\n\tcolor: #fff;\r\n\tpadding: 0 10rpx;\r\n}\r\n\r\n.bobaobox_bottom {\r\n\tposition: fixed;\r\n\tbottom: calc(env(safe-area-inset-bottom) + 150rpx);\r\n\tleft: 0;\r\n\tright: 0;\r\n\twidth: 470rpx;\r\n\tmargin: 0 auto;\r\n\tz-index: 10;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tborder-radius: 30rpx;\r\n\tcolor: #fff;\r\n\tpadding: 0 10rpx;\r\n}\r\n\r\n@supports (bottom: env(safe-area-inset-bottom)) {\r\n\t.bobaobox_bottom {\r\n\t\tposition: fixed;\r\n\t\tbottom: calc(env(safe-area-inset-bottom) + 150rpx);\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 470rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tz-index: 10;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tborder-radius: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n}\r\n\r\n.navigation {\r\n\twidth: 100%;\r\n\tposition: fixed;\r\n\tz-index: 99;\r\n\tbox-shadow: 0 2px 8px rgba(0,0,0,0.06);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.navcontent {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\theight: 44px;\r\n\tpadding: 0 16px;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.topinfo {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n}\r\n\r\n.topinfoicon {\r\n\twidth: 28px;\r\n\theight: 28px;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n\ttransition: transform 0.3s ease;\r\n}\r\n\r\n.topinfoicon:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.topinfotxt {\r\n\tfont-size: 16px;\r\n\ttransition: color 0.3s ease;\r\n}\r\n\r\n.topR {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n}\r\n\r\n.btn-text {\r\n\tpadding: 4px 12px;\r\n\tcolor: #fff;\r\n\tborder-radius: 16px;\r\n\tfont-size: 13px;\r\n\tfont-weight: 500;\r\n\ttransition: all 0.3s ease;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 4px;\r\n}\r\n\r\n.switch-icon {\r\n\twidth: 16px !important;\r\n\theight: 12px !important;\r\n\topacity: 1;\r\n\tfilter: brightness(1.1) contrast(1.1);\r\n\ttransform: scale(1.2);\r\n\timage-rendering: -webkit-optimize-contrast;\r\n\t-webkit-font-smoothing: antialiased;\r\n}\r\n\r\n.btn-text:active {\r\n\ttransform: scale(0.95);\r\n\topacity: 0.9;\r\n}\r\n\r\n.address-text {\r\n\tfont-size: 13px;\r\n\tcolor: #333;\r\n\tmax-width: 180px;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\topacity: 0.85;\r\n\tpadding-left: 8px;\r\n\tposition: relative;\r\n}\r\n\r\n.address-text::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\twidth: 1px;\r\n\theight: 12px;\r\n\tbackground: rgba(0,0,0,0.08);\r\n}\r\n\r\n@supports (backdrop-filter: blur(10px)) {\r\n\t.navigation {\r\n\t\tbackground: rgba(255,255,255,0.8);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t}\r\n}\r\n\r\n.agent-card {\r\n\theight: auto;\r\n\tposition: relative;\r\n\tbackground-color: #fff;\r\n\tmargin: 0 20rpx 10rpx;\r\n\tfont-size: 24rpx;\r\n\tborder-radius: 0 10rpx 10rpx 10rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 0 8rpx 0px rgb(0 0 0 / 30%);\r\n}\r\n\r\n.agent-card .row1 {\r\n\tpadding: 20rpx 10rpx 20rpx 20rpx;\r\n}\r\n\r\n.agent-card .logo {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.agent-card .text {\r\n\tflex: 1;\r\n\tmargin-left: 20rpx;\r\n\tcolor: #666;\r\n\tline-height: 180%;\r\n}\r\n\r\n.agent-card .title {\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.agent-card .right {\r\n\theight: 120rpx;\r\n}\r\n\r\n.agent-card .btn {\r\n\tposition: absolute;\r\n\tright: -100rpx;\r\n\tpadding: 0 14rpx;\r\n\ttop: 0;\r\n\tborder: 1px solid #b6c26e;\r\n\tborder-radius: 10rpx;\r\n\tcolor: #b6c26e;\r\n}\r\n\r\n.agent-card .img {\r\n\tmargin-right: 6rpx;\r\n\twidth: 30rpx;\r\n\theight: 30rpx;\r\n}\r\n\r\n.agent-card .img2 {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n}\r\n\r\n.grey-text {\r\n\tcolor: #999;\r\n\tfont-weight: normal;\r\n}\r\n\r\n.agent-card-b view {\r\n\tline-height: 72rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #444;\r\n\twidth: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n}\r\n\r\n.agent-card-b view:first-child::after {\r\n\tcontent: '';\r\n\twidth: 1px;\r\n\theight: 28rpx;\r\n\tborder-right: 1px solid #444;\r\n\tposition: absolute;\r\n\tright: 0;\r\n}\r\n\r\n/* 修改指示点样式 */\r\n.wx-swiper-dots {\r\n\tposition: relative;\r\n\tleft: unset !important;\r\n\tright: 0;\r\n}\r\n\r\n.wx-swiper-dot {\r\n\twidth: 16rpx !important;\r\n\theight: 16rpx !important;\r\n\tbackground: rgba(255, 255, 255, 0.5) !important;\r\n}\r\n\r\n.wx-swiper-dot-active {\r\n\tbackground: #ffffff !important;\r\n}\r\n\r\n.custom-banner {\r\n\tposition: relative;\r\n\twidth: 100%;\r\n\theight: 360rpx;\r\n}\r\n\r\n.banner-background {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.swiper {\r\n\tposition: relative;\r\n\twidth: 92%;\r\n\theight: 300rpx;\r\n\tmargin: 30rpx auto;\r\n}\r\n\r\n.swiper-item {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 100%;\r\n}\r\n\r\n.slide-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 16rpx;\r\n}\r\n\r\n/* 导航栏基础样式 */\r\n.custom-menu {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\tz-index: 1000;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n/* 固定和悬浮导航样式 */\r\n.menu-fixed,\r\n.menu-float {\r\n\tposition: fixed;\r\n}\r\n\r\n/* 透明效果 */\r\n.menu-opacity {\r\n\tbackground: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0)) !important;\r\n\tbackdrop-filter: blur(5px);\r\n\t-webkit-backdrop-filter: blur(5px);\r\n}\r\n\r\n/* 渐变背景效果 */\r\n.menu-gradient {\r\n\tbackdrop-filter: none !important;\r\n\t-webkit-backdrop-filter: none !important;\r\n}\r\n\r\n.menu-gradient.menu-opacity {\r\n\tbackground: none !important;\r\n}\r\n\r\n/* 导航内容布局 */\r\n.menu-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n/* 左侧布局 */\r\n.menu-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.menu-logo {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.menu-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 搜索框容器 */\r\n.search-container {\r\n\tposition: relative;\r\n\theight: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n/* 搜索框基础样式 */\r\n.search-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 0 20rpx;\r\n\ttransition: all 0.3s ease;\r\n\tbox-sizing: border-box;\r\n\tcursor: pointer;\r\n}\r\n\r\n.search-box:active {\r\n\ttransform: scale(0.98);\r\n\topacity: 0.9;\r\n}\r\n\r\n.search-box image {\r\n\tmargin-right: 10rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.search-box text {\r\n\ttransition: color 0.3s ease;\r\n}\r\n\r\n/* 搜索框位置样式 */\r\n.search-left {\r\n\tmargin-right: auto;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.search-center {\r\n\tposition: absolute;\r\n\tleft: 28%;\r\n\ttransform: translateX(-28%);\r\n}\r\n\r\n.search-right {\r\n\tmargin-left: auto;\r\n}\r\n\r\n/* 文字阴影和搜索框阴影效果 */\r\n.menu-fixed .menu-title,\r\n.menu-float .menu-title,\r\n.menu-opacity .menu-title,\r\n.menu-gradient .menu-title {\r\n\ttext-shadow: 0 1px 2px rgba(0,0,0,0.3);\r\n}\r\n\r\n.menu-fixed .search-box,\r\n.menu-float .search-box,\r\n.menu-opacity .search-box,\r\n.menu-gradient .search-box {\r\n\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 确保纯色背景不被其他样式覆盖 */\r\n.custom-menu[style*=\"background-color\"] {\r\n\tbackground-color: var(--nav-bg-color) !important;\r\n}\r\n\r\n/* 搜索框悬浮效果 */\r\n.menu-float .search-box,\r\n.menu-fixed .search-box {\r\n\tbackground: rgba(255, 255, 255, 0.15);\r\n\tbackdrop-filter: blur(5px);\r\n\t-webkit-backdrop-filter: blur(5px);\r\n}\r\n\r\n/* 位置模式样式 */\r\n.location-wrapper {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 0 10rpx;\r\n\tcursor: pointer;\r\n\tmin-width: 160rpx;\r\n}\r\n\r\n.location-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-start;\r\n\t\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.location-text {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmax-width: 200rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 24rpx;\r\n\theight: 24rpx;\r\n\tmargin-left: 6rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.arrow-icon.is-locating {\r\n\tanimation: rotating 1s linear infinite;\r\n}\r\n\r\n@keyframes rotating {\r\n\tfrom {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\tto {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n.location-wrapper:active:not(.is-locating) .location-content {\r\n\topacity: 0.8;\r\n}\r\n\r\n.location-wrapper:active:not(.is-locating) .arrow-icon {\r\n\ttransform: rotate(180deg);\r\n}\r\n\r\n/* 定位状态样式 */\r\n.location-text .locating {\r\n\tcolor: rgba(255, 255, 255, 0.7);\r\n\tfont-weight: normal;\r\n}\r\n\r\n.location-text .location-failed {\r\n\tcolor: #ff4d4f;\r\n}\r\n\r\n/* 区域选择样式 */\r\n.area-disabled {\r\n\tcolor: rgba(255, 255, 255, 0.5);\r\n\tfont-style: italic;\r\n}\r\n\r\n/* 默认导航栏样式 */\r\n.menu-default {\r\n\tposition: fixed !important;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\tbackground-color: #ffffff !important;\r\n\tborder-bottom: 1rpx solid #f1f1f1;\r\n\tbox-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n\tz-index: 999;\r\n\tpadding: 0 !important;\r\n\tmargin: 0 !important;\r\n\theight: auto !important;\r\n}\r\n\r\n.menu-content-default {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tpadding: 0 !important;\r\n\tmargin: 0 !important;\r\n\tbackground-color: #ffffff !important;\r\n\tposition: relative;\r\n\theight: 100% !important;\r\n}\r\n\r\n.status-bar {\r\n\twidth: 100%;\r\n\tbackground-color: #ffffff;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.nav-content {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\twidth: 100%;\r\n\tposition: relative;\r\n\tbackground-color: #ffffff;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.default-title {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #000000;\r\n\ttext-align: center;\r\n\tmax-width: 400rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tline-height: 44px;\r\n}\r\n\r\n.menu-default .menu-left,\r\n.menu-default .menu-right,\r\n.menu-default .search-container {\r\n\tdisplay: none;\r\n}\r\n\r\n.outernet-mode {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground: #ffffff;\r\n\tz-index: 9999;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.outernet-logo {\r\n\twidth: 80px;\r\n\theight: 80px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 10px;\r\n\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.outernet-title {\r\n\tfont-size: 18px;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 80px;\r\n}\r\n\r\n.spinner {\r\n\tmargin: 0 auto;\r\n\tborder: 4px solid rgba(0, 0, 0, 0.1);\r\n\tborder-left-color: #3a8eee;\r\n\tborder-radius: 50%;\r\n\twidth: 40px;\r\n\theight: 40px;\r\n\tanimation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n\t0% {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n.loading-text {\r\n\tmargin-top: 15px;\r\n\tfont-size: 16px;\r\n\tfont-weight: bold;\r\n\ttext-align: center;\r\n\tcolor: #333;\r\n}\r\n\r\n.manual-jump {\r\n\tmargin-top: 20px;\r\n\tpadding: 8px 20px;\r\n\tbackground: linear-gradient(90deg, #3a8eee 0%, #44a4ff 100%);\r\n\tcolor: #fff;\r\n\tborder-radius: 20px;\r\n\tfont-size: 14px;\r\n\tbox-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\r\n\ttext-align: center;\r\n}\r\n\r\n.manual-jump:active {\r\n\ttransform: scale(0.95);\r\n\topacity: 0.9;\r\n}\r\n\r\n/* 添加顶部渐变延伸层样式 */\r\n.top-gradient-extension {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\twidth: 100%;\r\n\theight: 600rpx; /* 增加高度确保覆盖足够区域 */\r\n\tz-index: -1; /* 作为背景层，在所有内容下方 */\r\n\tpointer-events: none; /* 确保不影响点击事件 */\r\n}\r\n\r\n.container {\r\n\tposition: relative; /* 确保相对定位，使渐变层能够正确定位 */\r\n\tz-index: 1; /* 创建层叠上下文，让内容在渐变层上方 */\r\n}\r\n\r\n/* 开屏样式 */\r\n.splash-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tz-index: 9999999;\r\n\tbackground: #000;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.splash-container {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.splash-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.splash-video {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.splash-skip {\r\n\tposition: absolute;\r\n\ttop: 60rpx;\r\n\tright: 40rpx;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tcolor: #fff;\r\n\tpadding: 16rpx 24rpx;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 500;\r\n\tbackdrop-filter: blur(10rpx);\r\n\t-webkit-backdrop-filter: blur(10rpx);\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\tz-index: 100001;\r\n}\r\n\r\n.splash-skip:active {\r\n\ttransform: scale(0.95);\r\n\topacity: 0.8;\r\n}\r\n\r\n.splash-progress {\r\n\tposition: absolute;\r\n\tbottom: 100rpx;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\twidth: 400rpx;\r\n\theight: 8rpx;\r\n\tbackground: rgba(255, 255, 255, 0.3);\r\n\tborder-radius: 4rpx;\r\n\toverflow: hidden;\r\n\tbackdrop-filter: blur(5rpx);\r\n\t-webkit-backdrop-filter: blur(5rpx);\r\n\tz-index: 100001;\r\n}\r\n\r\n.splash-progress-bar {\r\n\theight: 100%;\r\n\tbackground: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));\r\n\tborder-radius: 4rpx;\r\n\ttransition: width 0.1s ease;\r\n\tbox-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.splash-link {\r\n\tposition: absolute;\r\n\tbottom: 200rpx;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));\r\n\tcolor: #333;\r\n\tpadding: 20rpx 40rpx;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tbackdrop-filter: blur(15rpx);\r\n\t-webkit-backdrop-filter: blur(15rpx);\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\tz-index: 100001;\r\n\ttext-align: center;\r\n\tmin-width: 200rpx;\r\n}\r\n\r\n.splash-link:active {\r\n\ttransform: translateX(-50%) scale(0.95);\r\n\topacity: 0.9;\r\n}\r\n\r\n/* 适配安全区域 */\r\n@supports (bottom: env(safe-area-inset-bottom)) {\r\n\t.splash-progress {\r\n\t\tbottom: calc(100rpx + env(safe-area-inset-bottom));\r\n\t}\r\n\t\r\n\t.splash-link {\r\n\t\tbottom: calc(200rpx + env(safe-area-inset-bottom));\r\n\t}\r\n}\r\n\r\n/* 适配不同屏幕尺寸 */\r\n@media screen and (max-width: 375px) {\r\n\t.splash-skip {\r\n\t\ttop: 50rpx;\r\n\t\tright: 30rpx;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tfont-size: 22rpx;\r\n\t}\r\n\t\r\n\t.splash-progress {\r\n\t\twidth: 300rpx;\r\n\t\tbottom: 80rpx;\r\n\t}\r\n\t\r\n\t.splash-link {\r\n\t\tbottom: 160rpx;\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n}\r\n\r\n@media screen and (min-width: 768px) {\r\n\t.splash-skip {\r\n\t\ttop: 80rpx;\r\n\t\tright: 60rpx;\r\n\t\tpadding: 20rpx 32rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.splash-progress {\r\n\t\twidth: 500rpx;\r\n\t\theight: 10rpx;\r\n\t\tbottom: 120rpx;\r\n\t}\r\n\t\r\n\t.splash-link {\r\n\t\tbottom: 240rpx;\r\n\t\tpadding: 24rpx 48rpx;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115022529\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}