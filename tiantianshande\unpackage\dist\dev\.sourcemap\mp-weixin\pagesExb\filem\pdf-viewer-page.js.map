{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?a391", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?f6ba", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?7e3e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?09bb", "uni-app:///pagesExb/filem/pdf-viewer-page.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?3e45", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/pdf-viewer-page.vue?ee6f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "id", "fileName", "pdfUrl", "viewerUrl", "loading", "error", "errorMsg", "onLoad", "console", "methods", "goBack", "uni", "loadPdf", "url", "method", "success", "fail", "handleError"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAowB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+BxxB;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IAEA;MACA;IACA;IAEA;MACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEAJ;;MAEA;MACAG;QACAE;QACAC;QACAf;UACAC;QACA;QACAe;UACAP;UAEA;YACA;YACA;;YAEA;YACA;YACA;YACA;YAEAA;YAEA;UACA;YACA;UACA;QACA;QACAQ;UACAR;UACA;QACA;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAAolC,CAAgB,gkCAAG,EAAC,C;;;;;;;;;;;ACAxmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/filem/pdf-viewer-page.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/filem/pdf-viewer-page.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pdf-viewer-page.vue?vue&type=template&id=4836589b&\"\nvar renderjs\nimport script from \"./pdf-viewer-page.vue?vue&type=script&lang=js&\"\nexport * from \"./pdf-viewer-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pdf-viewer-page.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/filem/pdf-viewer-page.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer-page.vue?vue&type=template&id=4836589b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer-page.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"pdf-viewer-container\">\r\n  <view class=\"header\">\r\n    <view class=\"back-btn\" @click=\"goBack\">\r\n      <text class=\"iconfont icon-back\"></text>\r\n    </view>\r\n    <view class=\"title\">\r\n      <text>{{fileName}}</text>\r\n    </view>\r\n    <view class=\"right-placeholder\"></view>\r\n  </view>\r\n  \r\n  <!-- PDF查看器 -->\r\n  <web-view v-if=\"pdfUrl\" :src=\"viewerUrl\" class=\"pdf-viewer\"></web-view>\r\n  \r\n  <!-- 加载中 -->\r\n  <view v-if=\"loading\" class=\"loading\">\r\n    <text>PDF加载中...</text>\r\n  </view>\r\n  \r\n  <!-- 错误提示 -->\r\n  <view v-if=\"error\" class=\"error\">\r\n    <text>{{errorMsg}}</text>\r\n    <view class=\"retry-btn\" @click=\"loadPdf\">\r\n      <text>重试</text>\r\n    </view>\r\n  </view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      id: '', // 文件ID\r\n      fileName: '', // 文件名\r\n      pdfUrl: '', // PDF文件URL\r\n      viewerUrl: '', // PDF.js查看器URL\r\n      loading: true, // 加载状态\r\n      error: false, // 错误状态\r\n      errorMsg: '' // 错误信息\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    console.log('2025-03-17 10:00:00-INFO-[pdf-viewer-page][onLoad_001] 页面加载参数:', options);\r\n    \r\n    if (options.id) {\r\n      this.id = options.id;\r\n    }\r\n    \r\n    if (options.name) {\r\n      this.fileName = decodeURIComponent(options.name);\r\n    }\r\n    \r\n    // 加载PDF文件\r\n    this.loadPdf();\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 加载PDF文件\r\n    loadPdf() {\r\n      this.loading = true;\r\n      this.error = false;\r\n      \r\n      console.log('2025-03-17 10:00:01-INFO-[pdf-viewer-page][loadPdf_001] 开始加载PDF文件, ID:', this.id);\r\n      \r\n      // 获取文件详情\r\n      uni.request({\r\n        url: this.$baseUrl + '/api/file/detail',\r\n        method: 'GET',\r\n        data: {\r\n          id: this.id\r\n        },\r\n        success: (res) => {\r\n          console.log('2025-03-17 10:00:02-INFO-[pdf-viewer-page][loadPdf_002] 文件详情获取成功:', res.data);\r\n          \r\n          if (res.data.code === 0 && res.data.data) {\r\n            const detail = res.data.data;\r\n            this.pdfUrl = detail.filepath;\r\n            \r\n            // 构建PDF.js查看器URL\r\n            // 注意：需要对PDF URL进行编码\r\n            const encodedPdfUrl = encodeURIComponent(this.pdfUrl);\r\n            this.viewerUrl = `/static/pdfjs/web/viewer.html?file=${encodedPdfUrl}`;\r\n            \r\n            console.log('2025-03-17 10:00:03-INFO-[pdf-viewer-page][loadPdf_003] PDF查看器URL:', this.viewerUrl);\r\n            \r\n            this.loading = false;\r\n          } else {\r\n            this.handleError('获取文件详情失败: ' + (res.data.msg || '未知错误'));\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('2025-03-17 10:00:04-ERROR-[pdf-viewer-page][loadPdf_004] 获取文件详情失败:', err);\r\n          this.handleError('网络请求失败，请检查网络连接');\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 处理错误\r\n    handleError(msg) {\r\n      this.loading = false;\r\n      this.error = true;\r\n      this.errorMsg = msg || '加载PDF失败，请稍后重试';\r\n      console.error('2025-03-17 10:00:05-ERROR-[pdf-viewer-page][handleError_001]', this.errorMsg);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.pdf-viewer-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 90rpx;\r\n  background-color: #007AFF;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.back-btn {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-btn text {\r\n  color: #fff;\r\n  font-size: 40rpx;\r\n}\r\n\r\n.title {\r\n  flex: 1;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.right-placeholder {\r\n  width: 80rpx;\r\n}\r\n\r\n.pdf-viewer {\r\n  flex: 1;\r\n  width: 100%;\r\n}\r\n\r\n.loading, .error {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.loading text {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n}\r\n\r\n.error text {\r\n  font-size: 30rpx;\r\n  color: #ff3b30;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.retry-btn {\r\n  background: #007AFF;\r\n  color: #fff;\r\n  padding: 20rpx 60rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.retry-btn:active {\r\n  background: #0056b3;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer-page.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pdf-viewer-page.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027758\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}