{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?9bcb", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?b988", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?abf1", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?85ae", "uni-app:///restaurant/takeaway/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?8eb6", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/takeaway/index.vue?563a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nomore", "nodata", "st", "cartListShow", "buydialogShow", "harr", "business", "datalist", "cartList", "numtotal", "numCat", "proid", "totalprice", "totalpricePack", "minprice", "currentActiveIndex", "animation", "scrollToViewId", "commentlist", "comment_nodata", "comment_nomore", "sysset", "onLoad", "onPullDownRefresh", "onShow", "onReachBottom", "methods", "getdata", "that", "app", "bid", "console", "changetab", "uni", "scrollTop", "duration", "getCommentList", "id", "pagenum", "clickRootItem", "addcart", "ggid", "num", "jlprice", "jltitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "currentTarget", "dataset", "clearShopCartFn", "gopay", "prodata", "gotoCatproductPage", "scroll", "countH", "buydialogChange", "handleClickMask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqL9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;YACAA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;QACA;QACA;QACA;UACAG;UACA;YACA;YACAA;YACA1B;UACA;QACA;UACAuB;QACA;QAEAA;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAR;MACAA;MACAA;MACAC;QAAAQ;QAAAnC;QAAAoC;MAAA;QACAV;QACAK;QACA;QACA;UACAL;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAZ;MACAC;QAAAlB;QAAA8B;QAAAC;QAAAZ;QAAAa;QAAAC;MAAA;QACAhB;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAgB;MACAC;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACAC;MACA;MACApB;QAAAC;MAAA;QACAF;MACA;IACA;IACAsB;MACA;MACA;QACArB;QACA;MACA;MACA;MACA;QACAsB;MACA;MACAtB;IACA;IACAuB;MACA;MACAvB;IACA;IACAwB;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;QACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnYA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/takeaway/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/takeaway/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=00365ea8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/takeaway/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=00365ea8&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.business_info_show ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.sysset.comment_show ? _vm.t(\"color1\") : null\n  var g0 =\n    _vm.isload && _vm.st == 0 ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && _vm.st == 0 && g0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.t(\"color1\")\n          var m4 = _vm.numCat[item.id] > 0 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (detail, index) {\n          var $orig = _vm.__get_orig(detail)\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  var g1 =\n    _vm.isload && _vm.st == 1\n      ? _vm.business.zhengming && _vm.business.zhengming.length > 0\n      : null\n  var g2 = _vm.isload && _vm.st == 2 ? _vm.commentlist.length : null\n  var m6 = _vm.isload ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m8 = _vm.isload && _vm.cartList.total > 0 ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && !(_vm.cartList.leftprice > 0) ? _vm.t(\"color1\") : null\n  var m11 =\n    _vm.isload && !(_vm.cartList.leftprice > 0) ? _vm.t(\"color1rgb\") : null\n  var l2 =\n    _vm.isload && _vm.cartListShow\n      ? _vm.__map(_vm.cartList.list, function (cart, index) {\n          var $orig = _vm.__get_orig(cart)\n          var g3 = parseFloat(\n            parseFloat(cart.guige.sell_price) + parseFloat(cart.jlprice)\n          ).toFixed(2)\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n  var g4 = _vm.isload && _vm.cartListShow ? _vm.cartList.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        l0: l0,\n        l1: l1,\n        g1: g1,\n        g2: g2,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        l2: l2,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"view-show\">\n\t\t\t<view class=\"topbannerbg\" :style=\"sysset.banner_show && business.pic?'background:url('+business.pic+') center no-repeat;background-size:cover;':''\"></view>\n\t\t\t<view class=\"topbannerbg2\"></view>\n\t\t\t<view class=\"topbanner\">\n\t\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<view class=\"f1\">{{business.name}}</view>\n\t\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\n\t\t\t\t\t<!-- <view class=\"f3\"><view class=\"flex1\"></view><view class=\"t2\">收藏<image class=\"img\" src=\"/static/img/like1.png\"/></view></view> -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"navtab\">\n\t\t\t\t<view class=\"item\" :class=\"st==0?'on':''\" @tap=\"changetab\" data-st=\"0\">点外卖<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t<view class=\"item\" :class=\"st==1?'on':''\" @tap=\"changetab\" data-st=\"1\" v-if=\"sysset.business_info_show\">商家信息<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t\t<view class=\"item\" :class=\"st==2?'on':''\" @tap=\"changetab\" data-st=\"2\" v-if=\"sysset.comment_show\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==0\" class=\"content\" style=\"overflow:hidden;display:flex;margin-top:86rpx\" :style=\"{height:'calc(100% - '+(menuindex>-1?460:360)+'rpx)'}\">\n\t\t\t\t<scroll-view v-if=\"datalist && datalist.length > 0\" class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':''\">\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\">\n\t\t\t\t\t\t<view class=\"before\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t\t{{item.name}}<view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"numCat[item.id]>0\">{{numCat[item.id]}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"nav_right\">\n\t\t\t\t\t<view class=\"nav_right-content\">\n\t\t\t\t\t\t<scroll-view @scroll=\"scroll\" class=\"detail-list\" :scrollIntoView=\"scrollToViewId\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':''\">\n\t\t\t\t\t\t\t<view v-for=\"(detail, index) in datalist\" :key=\"index\" class=\"classification-detail-item\">\n\t\t\t\t\t\t\t\t<view class=\"head\" :data-id=\"detail.id\" :id=\"'detail-' + detail.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{detail.name}}</view>\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"show-all\" @tap=\"gotoCatproductPage\">查看全部<text class=\"iconfont iconjiantou\"></text></view> -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"product-itemlist\">\n\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in detail.prolist\" :key=\"item.id\" :class=\"(item.stock <= 0 || item.stock_daily <= item.sales_daily) ? 'soldout' : ''\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-pic\" @click=\"goto\" :data-url=\"'product?id='+item.id\">\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"overlay\"><view class=\"text\">售罄</view></view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.limit_start>0\"><text style=\"overflow:hidden\">{{item.limit_start}}件起售</text></view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view> -->\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\" v-if=\"item.stock > 0 && item.stock_daily > item.sales_daily\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"numtotal[item.id]>0\" class=\"minus\" @tap.stop=\"addcart\" data-num=\"-1\" :data-proid=\"item.id\" :data-stock=\"item.stock\">-</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"numtotal[item.id]>0\" class=\"i\">{{numtotal[item.id]}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.ggcount>1\" class=\"plus\" @tap.stop=\"buydialogChange\" :data-proid=\"item.id\" :data-stock=\"item.stock\">+</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"plus\" @tap.stop=\"addcart\" data-num=\"1\" :data-proid=\"item.id\" :data-ggid=\"item.gglist[0].id\" :data-stock=\"item.stock\">+</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==1\" class=\"content1\" style=\"margin-top:86rpx;padding-top:20rpx\">\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">联系电话</text>\n\t\t\t\t\t<text class=\"t2\"><text v-if=\"business.tel\">{{business.tel}}</text><text v-else>暂无</text></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">商家地址</text>\n\t\t\t\t\t<text class=\"t2\"><text v-if=\"business.address\">{{business.address}}</text><text v-else>暂无</text></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">商家简介</text>\n\t\t\t\t\t<text class=\"t2\"><text v-if=\"business.desc\">{{business.desc}}</text><text v-else>暂无</text></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\">\n\t\t\t\t\t<text class=\"t1\">营业时间</text>\n\t\t\t\t\t<text class=\"t2\">{{sysset.start_hours}} 至 {{sysset.end_hours}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-col\" v-if=\"business.zhengming && business.zhengming.length > 0\">\n\t\t\t\t\t<text class=\"t1\">证照公示</text>\n\t\t\t\t\t<view id=\"content_picpreview\" class=\"flex t2\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in business.zhengming\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"st==2\" class=\"content2\" style=\"margin-top:86rpx;padding-top:20rpx\">\n\t\t\t\t<view class=\"comment\">\n\t\t\t\t\t<block v-if=\"commentlist.length>0\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in commentlist\" :key=\"index\" class=\"item\">\n\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t<image class=\"t1\" :src=\"item.headimg\"/>\n\t\t\t\t\t\t\t\t<view class=\"t2\">{{item.nickname}}</view>\n\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (item.score>item2?'2':'') + '.png'\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"color:#777;font-size:22rpx;\">{{item.createtime}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.content}}</text>\n\t\t\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.content_pic!=''\">\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in item.content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"item.content_pic\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.reply_content\">\n\t\t\t\t\t\t\t\t<view class=\"arrow\"></view>\n\t\t\t\t\t\t\t\t<view class=\"t1\">商家回复：{{item.reply_content}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<nodata v-show=\"comment_nodata\"></nodata>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\" controller=\"ApiRestaurantTakeaway\"></buydialog>\n\t\t<view style=\"height:auto;position:relative\">\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':''\">\n\t\t\t\t<view class=\"cart_ico\" :style=\"{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"handleClickMask\"><image class=\"img\" src=\"/static/img/cart.png\"/><view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"cartList.total>0\">{{cartList.total}}</view></view>\n\t\t\t\t<view class=\"text1\">合计</view>\n\t\t\t\t<view class=\"text2 flex1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx\">￥</text>{{totalprice}}</view>\n\t\t\t\t<view v-if=\"cartList.leftprice > 0\" class=\"op\" :style=\"{background:'#888',width:'220rpx'}\">差{{cartList.leftprice}}元起送</view>\n\t\t\t\t<view v-else class=\"op\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"gopay\">去结算</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"cartListShow\" class=\"popup__container\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':''\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':''\"></view>\n\t\t\t<view class=\"popup__modal\" style=\"min-height:400rpx;padding:0\">\n\t\t\t\t<view class=\"popup__title\" style=\"border-bottom:1px solid #EFEFEF\">\n\t\t\t\t\t<view class=\"popup__left flex-y-center\">打包费 <text>￥{{totalpricePack}}</text></view>\n\t\t\t\t\t<text class=\"popup__title-text\" style=\"color:#323232;font-weight:bold;font-size:32rpx\">购物车</text>\n\t\t\t\t\t<view class=\"popup__close flex-y-center\" @tap.stop=\"clearShopCartFn\" style=\"color:#999999;font-size:24rpx\"><image src=\"/static/img/del.png\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>清空</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\" style=\"padding:0\">\n\t\t\t\t\t<scroll-view scroll-y class=\"prolist\">\n\t\t\t\t\t\t<block v-for=\"(cart, index) in cartList.list\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"proitem\">\n\t\t\t\t\t\t\t\t<image :src=\"cart.guige.pic?cart.guige.pic:cart.product.pic\" class=\"pic flex0\"></image>\n\t\t\t\t\t\t\t\t<view class=\"con\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cart.product.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"cart.guige.name!='默认规格'\">{{cart.guige.name}}{{cart.jltitle}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"color:#ff5555;margin-top:10rpx;font-size:28rpx\">￥{{parseFloat(parseFloat(cart.guige.sell_price) + parseFloat(cart.jlprice)).toFixed(2)}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"addnum\">\n\t\t\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" src=\"/static/img/cart-minus.png\" @tap=\"addcart\" data-num=\"-1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\":data-jltitle=\"cart.jltitle\" :data-jlprice=\"cart.jlprice\"/></view>\n\t\t\t\t\t\t\t\t\t<text class=\"i\">{{cart.num}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" src=\"/static/img/cart-plus.png\" @tap=\"addcart\" data-num=\"1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\" :data-jltitle=\"cart.jltitle\" :data-jlprice=\"cart.jlprice\"/></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"!cartList.list.length\">\n\t\t\t\t\t\t\t<text class=\"nopro\">暂时没有商品喔~</text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n      nomore: false,\n\t\t\tnodata:false,\n\t\t\t\n\t\t\tst:0,\n\t\t\tcartListShow:false,\n\t\t\tbuydialogShow:false,\n\t\t\tharr:[],\n\t\t\tbusiness:{},\n      datalist: [],\n\t\t\tcartList:{},\n\t\t\tnumtotal:[],\n\t\t\tnumCat:{},\n\t\t\tproid:'',\n\t\t\ttotalprice:0.00,\n\t\t\ttotalpricePack:0.0,\n\t\t\tminprice:0.00,\n      currentActiveIndex: 0,\n      animation: true,\n      scrollToViewId: \"\",\n\t\t\tcommentlist:[],\n\t\t\tcomment_nodata:false,\n\t\t\tcomment_nomore:false,\n\t\t\tsysset:{},\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\t// this.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShow:function(){\n\t\tthis.getdata();\n\t},\n\tonReachBottom: function () {\n\t\tif (this.st == 2) {\n\t\t\tif (!this.comment_nodata && !this.comment_nomore) {\n\t\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\t\tthis.getCommentList(true);\n\t\t\t}\n\t\t}\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiRestaurantTakeaway/index', {bid:that.opt.bid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status==0){\n\t\t\t\t\tapp.alert(res.msg,function(){\n\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthat.business = res.business\n\t\t\t\tthat.datalist = res.data;\n\t\t\t\tthat.cartList = res.cartList;\n\t\t\t\tthat.numtotal = res.numtotal;\n\t\t\t\tthat.numCat = res.numCat;\n\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\tthat.totalprice = parseFloat(res.cartList.totalprice);\n\t\t\t\tthat.totalpricePack = parseFloat(res.cartList.totalpricePack);\n\t\t\t\tthat.minprice = parseFloat(res.sysset.min_price);\n\n\t\t\t\t//计算每个高度\n\t\t\t\tvar harr = [];\n\t\t\t\tvar clientwidth = uni.getSystemInfoSync().windowWidth;\n\t\t\t\tvar datalist = res.data;\n\t\t\t\tif(datalist && datalist.length > 0) {\n\t\t\t\t\tconsole.log(datalist.length)\n\t\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\n\t\t\t\t\t\tvar child = datalist[i].prolist;\n\t\t\t\t\t\tconsole.log(child)\n\t\t\t\t\t\tharr.push(Math.ceil(child.length) * 200 / 750 * clientwidth);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthat.nodata = true;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthat.harr = harr;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tchangetab:function(e){\n\t\t\tthis.st = e.currentTarget.dataset.st;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.commentlist = [];\n\t\t\tuni.pageScrollTo({\n\t\t\t\tscrollTop: 0,\n\t\t\t\tduration: 0\n\t\t\t});\n\t\t\tthis.getCommentList();\n\t\t},\n\t\tgetCommentList: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.commentlist = [];\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tvar pagenum = that.pagenum;\n\t\t\tvar st = that.st;\n\t\t\tthat.loading = true;\n\t\t\tthat.comment_nodata = false;\n\t\t\tthat.comment_nomore = false;\n\t\t\tapp.post('ApiRestaurantTakeaway/getdatalist', {id:that.opt.bid,st: st,pagenum: pagenum}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n        var data = res.data;\n        if (pagenum == 1) {\n          that.commentlist = data;\n          if (data.length == 0) {\n            that.comment_nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.comment_nomore = true;\n          } else {\n            var commentlist = that.commentlist;\n            var newdata = commentlist.concat(data);\n            that.commentlist = newdata;\n          }\n        }\n      });\n\t\t},\n    clickRootItem: function (t) {\n      var e = t.currentTarget.dataset;\n      this.scrollToViewId = 'detail-' + e.rootItemId;\n      this.currentActiveIndex = e.rootItemIndex;\n    },\n\t\taddcart:function(e){\n\t\t\tvar that = this;\n\t\t\tvar ks = that.ks;\n\t\t\tvar num = e.currentTarget.dataset.num;\n\t\t\tvar proid = e.currentTarget.dataset.proid;\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\r\n\t\t\tvar jlprice = e.currentTarget.dataset.jlprice;\r\n\t\t\tvar jltitle = e.currentTarget.dataset.jltitle;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiRestaurantTakeaway/addcart', {proid: proid,ggid: ggid,num: num,bid:that.opt.bid,jlprice:jlprice,jltitle:jltitle}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    //加入购物车弹窗后\n    afteraddcart: function (e) {\n\t\t\te.hasoption = false;\n      this.addcart({currentTarget:{dataset:e}});\n    },\n    clearShopCartFn: function () {\n      var that = this;\n      app.post(\"ApiRestaurantTakeaway/cartclear\", {bid:that.opt.bid}, function (res) {\n        that.getdata();\n      });\n    },\n    gopay: function () {\n      var cartList = this.cartList.list;\n      if (cartList.length == 0) {\n        app.alert('请先添加商品到购物车');\n        return;\n      }\n      var prodata = [];\n      for (var i = 0; i < cartList.length; i++) {\n        prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);\n      }\n      app.goto('buy?frompage=fastbuy&prodata=' + prodata.join('-'));\n    },\n    gotoCatproductPage: function (t) {\n      var e = t.currentTarget.dataset;\n      app.goto('prolist?cid=' + e.id);\n    },\n    scroll: function (e) {\n      var scrollTop = e.detail.scrollTop;\n      var harr = this.harr;\n      var countH = 0;\n      for (var i = 0; i < harr.length; i++) {\n        if (scrollTop >= countH && scrollTop < countH + harr[i]) {\n          this.currentActiveIndex = i;\n          break;\n        }\n        countH += harr[i];\n      }\n    },\n\t\tbuydialogChange: function (e) {\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n\t\thandleClickMask:function(){\n\t\t\tthis.cartListShow = !this.cartListShow;\n\t\t}\n  }\n};\n</script>\n<style>\npage {position: relative;width: 100%;height: 115%;}\n.container{height:100%;overflow:hidden;position: relative;}\n\n.topbannerbg{width:100%;height:264rpx;background:#fff;}\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:264rpx;background:rgba(0,0,0,0.3);top:0}\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:40rpx 20rpx;top:0}\n.topbanner .left{width:160rpx;height:160rpx;flex-shrink:0;margin-right:20rpx}\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\n.topbanner .right .f1{font-size:36rpx;font-weight:bold;color:#fff}\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\n\n.navtab{display:flex;width:100%;height:110rpx;background: #fff;position:absolute;z-index:10;padding:0 50rpx;border-radius:24rpx 24rpx 0 0;margin-top:-24rpx;}\n.navtab .item{flex:1;font-size:32rpx; text-align:center; color:#222222; height: 110rpx; line-height: 110rpx;overflow: hidden;position:relative}\n.navtab .item .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:20rpx;height:4px;border-radius:2px;width:40rpx}\n.navtab .on{font-size:36rpx;font-weight:bold}\n.navtab .on .after{display:block}\n\n.content1 .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}\n.content1 .item:last-child{ border-bottom: 0;}\n.content1 .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}\n.content1 .item .t2{color:#2B2B2B;font-size:24rpx;line-height:30rpx}\n\n.content2 .comment{padding:0 10rpx}\n.content2 .comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\n.content2 .comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\n.content2 .comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\n.content2 .comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\n.content2 .comment .item .f1 .t3{text-align:right;}\n.content2 .comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\n.content2 .comment .item .score{ font-size: 24rpx;color:#f99716;}\n.content2 .comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\n.content2 .comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\n.content2 .comment .item .f2 .t1{color:#333;font-size:28rpx;}\n.content2 .comment .item .f2 .t2{display:flex;width:100%}\n.content2 .comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.content2 .comment .item .f3{width:100%;padding:10rpx 0;position:relative}\n.content2 .comment .item .f3 .arrow{width: 16rpx;height: 16rpx;background:#eee;transform: rotate(45deg);position:absolute;top:0rpx;left:36rpx}\n.content2 .comment .item .f3 .t1{width:100%;border-radius:10rpx;padding:10rpx;font-size:22rpx;color:#888;background:#eee}\n\n\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\n\n.nav_left{width: 25%;height:100%;background:#F6F6F6;overflow-y:scroll;padding: 0 0 100rpx 0;}\n.nav_left .nav_left_items{line-height:50rpx;color:#999999;border-bottom:0px solid #E6E6E6;font-size:24rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\n.nav_left .nav_left_items.active{background: #fff;color:#222222;font-size:28rpx;font-weight:bold}\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-22rpx;left:0rpx;height:44rpx;border-radius:4rpx;width:6rpx}\n.nav_left .nav_left_items.active .before{display:block}\n.nav_left .nav_left_items .cartnum{position:absolute;top:8rpx;right:8rpx;width:36rpx;height:36rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:18rpx;color:#fff}\n\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #fff;box-sizing: border-box;padding:0 0 100rpx 0}\n.nav_right-content{background: #ffffff;padding:20rpx 10rpx 20rpx 20rpx;height:100%;position:relative}\n.detail-list {height:100%;overflow:scroll}\n.classification-detail-item {width: 100%;overflow: visible;background:#fff}\n.classification-detail-item .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;}\n.classification-detail-item .head .txt {color:#222222;font-weight:bold;font-size:28rpx;}\n.classification-detail-item .head .show-all {font-size: 22rpx;color:#949494;display:flex;align-items:center}\n\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:30rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:60rpx}\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;}\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999; margin-left: 6rpx;}\n.product-itemlist .product-info .p3-1:nth-child(1) {margin: 0;}\n.product-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\n.product-itemlist .addnum {position: absolute;right:10rpx;bottom:20rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\n.product-itemlist .addnum .plus {width:40rpx;height:40rpx;background:#FD4A46;color:#FFFFFF;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:28rpx}\n.product-itemlist .addnum .minus {width:40rpx;height:40rpx;background:#FFFFFF;color:#FD4A46;border:1px solid #FD4A46;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:28rpx}\n.product-itemlist .addnum .img{width:24rpx;height:24rpx}\n.product-itemlist .addnum .i {padding: 0 20rpx;color:#999999;font-size:28rpx}\n.overlay {background-color: rgba(0,0,0,.5); position: absolute; width:60%; height: 60%; border-radius: 50%; display: none; top: 20%; left: 20%;}\n.overlay .text{ color: #fff; text-align: center; transform: translateY(100%);}\n.product-itemlist .soldout .product-pic .overlay{ display: block;}\n\n.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}\n.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}\n.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}\n.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}\n.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}\n.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\n.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}\n.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\n.prolist .proitem .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.prolist .proitem .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\n.prolist .proitem .addnum .img{width:24rpx;height:24rpx}\n.prolist .proitem .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\n.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}\n\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\n.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}\n.footer .cart_ico .img{width:36rpx;height:36rpx;}\n.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}\n.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}\n.footer .text2 {font-size: 32rpx;font-weight:bold}\n.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\n.layui-imgbox-close image{width:100%;height:100%}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n\n.popup__left {position: absolute;    top: 36rpx;    left: 34rpx;color:#999999;font-size:24rpx}\n.popup__left text { color: #ff5555;}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066834\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}