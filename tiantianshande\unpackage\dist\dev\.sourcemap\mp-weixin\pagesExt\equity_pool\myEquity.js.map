{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?5ba0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?504d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?b9a5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?77a0", "uni-app:///pagesExt/equity_pool/myEquity.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?ec78", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/myEquity.vue?bb13"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "isload", "pre_url", "equity_value", "total_equity", "equity_num", "have<PERSON><PERSON>ord", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "msg", "isok", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmEjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;QACAC;QAAA;QACAF;MACA;;MACAG;IACA;EACA;;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MAEAC;QACAD;QACAE;QAEA;UACAF;UACAA;UACAA;UACAA;QACA;UACAA;YACAG;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAH;QACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/equity_pool/myEquity.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/equity_pool/myEquity.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myEquity.vue?vue&type=template&id=e0cf3ac4&\"\nvar renderjs\nimport script from \"./myEquity.vue?vue&type=script&lang=js&\"\nexport * from \"./myEquity.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myEquity.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/equity_pool/myEquity.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myEquity.vue?vue&type=template&id=e0cf3ac4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myEquity.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myEquity.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"equity-value-card\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t<view class=\"current-value\">\r\n\t\t\t\t\t<view class=\"label\">当前股权单价</view>\r\n\t\t\t\t\t<view class=\"value\">{{equity_value}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"my-equity\">\r\n\t\t\t\t\t<view class=\"equity-item\">\r\n\t\t\t\t\t\t<view class=\"equity-num\">{{total_equity.equity_num}}</view>\r\n\t\t\t\t\t\t<view class=\"equity-label\">持有股权数量</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"equity-item\">\r\n\t\t\t\t\t\t<view class=\"equity-num\">{{total_equity.equity_value}}</view>\r\n\t\t\t\t\t\t<view class=\"equity-label\">股权总价值</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 如果将来需要显示股权历史记录，可以在这里添加 -->\r\n\t\t\t<view class=\"no-record\" v-if=\"!haveRecord\">\r\n\t\t\t\t<image class=\"no-data-img\" src=\"/static/img/nodata.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"no-data-text\">暂无股权记录</view>\r\n\t\t\t\t<view class=\"tips\">持有股权可以分享平台收益，获得更多权益</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"action-btns\">\r\n\t\t\t\t<view class=\"action-btn\" @tap=\"goto\" data-url=\"/pagesExt/equity_pool/ranking\">\r\n\t\t\t\t\t<text class=\"iconfont icon_paihang\"></text>\r\n\t\t\t\t\t<text>股权排行榜</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn\" @tap=\"goto\" data-url=\"/pagesExt/equity_pool/index\">\r\n\t\t\t\t\t<text class=\"iconfont icon_guquan\"></text>\r\n\t\t\t\t\t<text>股权池概览</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 股权说明卡片 -->\r\n\t\t\t<view class=\"equity-desc\">\r\n\t\t\t\t<view class=\"desc-title\">股权说明</view>\r\n\t\t\t\t<view class=\"desc-content\">\r\n\t\t\t\t\t<view class=\"desc-item\">\r\n\t\t\t\t\t\t<view class=\"desc-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"desc-text\">股权是平台发展过程中的权益凭证</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\">\r\n\t\t\t\t\t\t<view class=\"desc-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"desc-text\">股权价值与平台发展状况密切相关</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\">\r\n\t\t\t\t\t\t<view class=\"desc-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"desc-text\">持有股权可以分享平台收益红利</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\">\r\n\t\t\t\t\t\t<view class=\"desc-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"desc-text\">股权价值可能会根据平台发展情况波动</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tequity_value: \"0.00\", // 单位股权价值\r\n\t\t\ttotal_equity: {\r\n\t\t\t\tequity_num: 0,    // 用户持有的股权数量\r\n\t\t\t\tequity_value: 0   // 用户股权价值\r\n\t\t\t},\r\n\t\t\thaveRecord: false    // 是否有股权记录\r\n\t\t};\r\n\t},\r\n\tonLoad: function(opt) {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function() {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.get('ApiEquityPool/myEquity', {}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.equity_value = parseFloat(res.data.equity_value).toFixed(2);\r\n\t\t\t\t\tthat.total_equity = res.data.total_equity;\r\n\t\t\t\t\tthat.haveRecord = res.data.equity_list && res.data.equity_list.length > 0;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.$refs.popmsg.show({\r\n\t\t\t\t\t\tmsg: res.msg || '获取数据失败，请重试',\r\n\t\t\t\t\t\tisok: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到指定页面\r\n\t\tgoto: function(e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.equity-value-card {\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tcolor: #fff;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.equity-value-card::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tbackground: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxjaXJjbGUgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiBjeD0iODAiIGN5PSI4MCIgcj0iNDAiIC8+CiAgICA8Y2lyY2xlIGZpbGw9InJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSIgY3g9IjEwIiBjeT0iMTAiIHI9IjIwIiAvPgo8L3N2Zz4=') no-repeat;\r\n\tbackground-size: cover;\r\n\topacity: 0.1;\r\n}\r\n\r\n.current-value {\r\n\tborder-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n\tpadding-bottom: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.label {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.8;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.value {\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.equity-value-card:active .value {\r\n\ttransform: scale(1.05);\r\n}\r\n\r\n.my-equity {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.equity-item {\r\n\ttext-align: center;\r\n\tflex: 1;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.equity-item:active {\r\n\ttransform: translateY(-5rpx);\r\n}\r\n\r\n.equity-num {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.equity-label {\r\n\tfont-size: 24rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.no-record {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 40rpx;\r\n\tmargin-bottom: 30rpx;\r\n\ttext-align: center;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.no-data-img {\r\n\twidth: 200rpx;\r\n\tmargin-bottom: 20rpx;\r\n\ttransition: all 0.5s ease;\r\n}\r\n\r\n.no-data-img:hover {\r\n\ttransform: rotate(5deg);\r\n}\r\n\r\n.no-data-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.tips {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tmargin-top: 20rpx;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.action-btns {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.action-btn {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx 0;\r\n\ttext-align: center;\r\n\twidth: 48%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.2s ease;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.action-btn::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.05);\r\n\topacity: 0;\r\n\ttransition: opacity 0.2s ease;\r\n}\r\n\r\n.action-btn:active {\r\n\ttransform: scale(0.98);\r\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.action-btn:active::after {\r\n\topacity: 1;\r\n}\r\n\r\n.action-btn .iconfont {\r\n\tfont-size: 50rpx;\r\n\tmargin-bottom: 10rpx;\r\n\tcolor: #f0505a;\r\n}\r\n\r\n.equity-desc {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.desc-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n\tpadding-bottom: 15rpx;\r\n}\r\n\r\n.desc-content {\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.desc-item {\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 15rpx;\r\n\tanimation: fadeIn 0.5s ease forwards;\r\n\topacity: 0;\r\n}\r\n\r\n.desc-item:nth-child(1) { animation-delay: 0.1s; }\r\n.desc-item:nth-child(2) { animation-delay: 0.2s; }\r\n.desc-item:nth-child(3) { animation-delay: 0.3s; }\r\n.desc-item:nth-child(4) { animation-delay: 0.4s; }\r\n\r\n@keyframes fadeIn {\r\n\tfrom { opacity: 0; transform: translateY(10rpx); }\r\n\tto { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.desc-dot {\r\n\twidth: 12rpx;\r\n\theight: 12rpx;\r\n\tborder-radius: 6rpx;\r\n\tbackground-color: #f0505a;\r\n\tmargin-top: 12rpx;\r\n\tmargin-right: 15rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.desc-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myEquity.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myEquity.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093217\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}