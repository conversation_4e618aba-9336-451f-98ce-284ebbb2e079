{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?4633", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?482a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?02ef", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?01a7", "uni-app:///pages/index/login.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?b3e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/login.vue?7683"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform2", "platform", "platformname", "platformimg", "logintype", "logintype_1", "logintype_2", "logintype_3", "logintype_4", "logintype_6", "isioslogin", "isgooglelogin", "google_client_id", "needsms", "logo", "name", "xystatus", "xyname", "xycontent", "xyname2", "xycontent2", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "isagree", "smsdjs", "tel", "<PERSON><PERSON>", "frompage", "wxloginclick", "iosloginclick", "googleloginclick", "login_bind", "login_setnickname", "login_mast", "reg_invite_code", "reg_invite_code_text", "parent", "tmplids", "default_headimg", "headimg", "nickname", "userIpAddress", "showAddressConfirm", "addressInfo", "country", "province", "city", "area", "can_edit_address", "onLoad", "console", "onPullDownRefresh", "methods", "getdata", "that", "app", "pid", "xlevel", "formSubmit", "pwd", "smscode", "yqcode", "ip_address", "uni", "setTimeout", "getPhoneNumber", "success", "iv", "encryptedData", "code", "setnicknameregister", "nosetnicknameregister", "bindregister", "nobindregister", "register", "url", "authlogin", "weixinlogin", "ioslogin", "provider", "userInfo", "fail", "googlelogin", "changelogintype", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showxieyiFun2", "hidexieyi2", "telinput", "uploadHeadimg", "count", "sizeType", "sourceType", "filePath", "onChooseAvatar", "time", "clearInterval", "getUserIpAddress", "method", "getUserIpAddressBackup", "getUserIpAddressBackup2", "<PERSON><PERSON><PERSON><PERSON>", "confirm", "cancelAddressEdit"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvHA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkP9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;IACAC;IAEA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACAC;UAAA;QACA;QACAD;QACAA;QACAA;QAWAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACAA;MA0DA;IACA;IACAI;MACA;MACA;MACA;QACAH;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MAEAA;MACA;MACA;QACA9B;QACAkC;QACAC;QACAxD;QACAoD;QACAK;QACAJ;QACAK;MACA;MAEAP;QACAA;QACA;UACAQ;UACA;UACA;YACAT;YACAA;YACA;UACA;UAEAC;UACA;YACAD;UACA;UACAA;YACAU;cACAT;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAU;MACA;MACAf;MACA;QACAK;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAlE;QAAA6E;UACAhB;UACA;UACA;UACAK;YAAAE;YAAAlB;YAAAC;YAAA2B;YAAAC;YAAAC;YAAAb;UAAA;YACA;cACAD;cACA;gBACAD;cACA;cACAA;gBACAU;kBACAT;gBACA;cACA;YACA;cACAA;YACA;YACA;UACA;QACA;MAAA;IACA;IACAe;MACA;MACA;MACA;MACA;QACAf;QACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAgB;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAjB;QACA;MACA;MACA;QACAA;QACA;MACA;MACAD;IACA;IACAmB;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;QACA;UACAA;QACA;MACA;QACAA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACApB;QAAAhB;QAAAC;QAAAf;QAAAmC;QAAAJ;QAAAC;MAAA;QACA;UACAF;UACA;YACAD;UACA;UACAA;YACAU;cACAT;YACA;UACA;QACA;UACAA;QACA;QACA;MACA;IACA;IACAqB;MACA;MACA;QACArB;QACA;MACA;MACAD;IACA;IACAuB;MACA;MACA;QACAvB;QACAA;QACA;MACA;MACAJ;MACAI;MACAC;QACAL;QACA;UACAK;UACAS;YACAd;YACAA;YACAK;UACA;QACA;UACAD;UACAA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;UACAA;QACA;UACAC;QACA;MACA;QAAA5B;MAAA;IACA;IACAmD;MACA;MACA;QACAxB;QACAA;QACA;MACA;MACAS;QACAgB;QACAb;UACAhB;UACA;UACAa;YACAgB;YACAb;cACA;cACAhB;cACA;gBACAK;kBAAAyB;kBAAAxB;kBAAAC;gBAAA;kBACAP;kBACA;oBACAK;oBACAS;sBACAd;sBACAA;sBACAK;oBACA;kBACA;oBACAD;oBACAA;oBACAA;oBACAA;oBACAA;kBACA;oBACAA;oBACAA;oBACAA;oBACAA;kBACA;oBACAC;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA0B;UACA/B;UACAK;QACA;MACA;IACA;IACA2B;MACA;MACA;QACA5B;QACAA;QACA;MACA;IAgDA;IACA6B;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACAlC;IACA;IACAmC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IAEA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA3B;QACA4B;QACAC;QACAC;QACA3B;UACA;UACA;UACAX;UACAQ;YACAY;YACAmB;YACA/E;YACAmD;cACAhB;cACAK;cACA;cACA;gBACAD;cACA;gBACAC;cACA;YACA;YACA0B;cACA1B;cACAA;YACA;UACA;QACA;QACA0B;QAAA;MAEA;IACA;IACAc;MACA7C;MACA;MACAK;MACAQ;QACAY;QACAmB;QACA/E;QACAmD;UACAX;UACA;UACA;YACAD;UACA;YACAC;UACA;QACA;QACA0B;UACA1B;UACAA;QACA;MACA;IACA;IACAK;MACA;MACA;MACAN;MACA;MACA;QACAC;QACAD;QACA;MACA;MACA;QACAC;QACAD;QACA;MACA;MACAC;QAAA9B;MAAA;QACA;UACA8B;UAAA;QACA;MACA;MACA;MACA;QACAyC;QACA;UACA1C;UACAA;UACA2C;QACA;UACA3C;QACA;MACA;IACA;IACA4C;MACA;MACA;MACAnC;QACAY;QACAwB;QACAjC;UACA;YACAZ;YACAJ;YACA;YACAa;UACA;YACA;YACAT;UACA;QACA;QACA2B;UACA/B;UACA;UACAI;QACA;MACA;IACA;IAEA8C;MACA;MACA;MACArC;QACAY;QACAwB;QACAjC;UACA;YACAZ;YACAJ;YACA;YACAa;UACA;YACA;YACAT;UACA;QACA;QACA2B;UACA/B;UACA;UACAI;QACA;MACA;IACA;IAEA+C;MACA;MACA;MACAtC;QACAY;QACAwB;QACAjC;UACA;YACAZ;YACAJ;YACA;YACAa;UACA;YACAb;UACA;QACA;QACA+B;UACA/B;UACAA;QACA;MACA;IACA;IAEA;IACAoD;MACA;MACA;QACA;QACA/C;UAAAgD;QAAA;UACA;YACAhD;YACAD;;YAEA;YACAU;cACAT;YACA;UACA;YACAA;UACA;QACA;MACA;QACA;QACAD;QACA;QACAC;MACA;IACA;IAEA;IACAiD;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1/BA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=c3d8bbfa&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/login.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=c3d8bbfa&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.logintype == 1 && _vm.xystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && _vm.logintype == 1 && _vm.xystatus == 1 && _vm.xyname2\n      ? _vm.t(\"color1\")\n      : null\n  var m2 = _vm.isload && _vm.logintype == 1 ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && _vm.logintype == 1 ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.isload && _vm.logintype == 2 ? _vm.t(\"color1\") : null\n  var m5 =\n    _vm.isload && _vm.logintype == 2 && _vm.xystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && _vm.logintype == 2 && _vm.xystatus == 1 && _vm.xyname2\n      ? _vm.t(\"color1\")\n      : null\n  var m7 = _vm.isload && _vm.logintype == 2 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.logintype == 2 ? _vm.t(\"color1rgb\") : null\n  var m9 = _vm.isload && _vm.logintype == 3 ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.logintype == 3 ? _vm.t(\"color1rgb\") : null\n  var m11 =\n    _vm.isload && _vm.logintype == 3 && _vm.xystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload && _vm.logintype == 3 && _vm.xystatus == 1 && _vm.xyname2\n      ? _vm.t(\"color1\")\n      : null\n  var m13 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1\") : null\n  var m14 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1rgb\") : null\n  var m15 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1\") : null\n  var m16 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1rgb\") : null\n  var m17 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m18 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  var m19 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1\") : null\n  var m20 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1rgb\") : null\n  var m21 = _vm.isload && _vm.showAddressConfirm ? _vm.t(\"color1\") : null\n  var m22 = _vm.isload && _vm.showAddressConfirm ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<block v-if=\"logintype==1\">\r\n\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t\t<view class=\"title\">用户登录</view>\r\n\t\t\t<view class=\"loginform\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" class=\"img\"/>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-pwd.png\" class=\"img\"/>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"pwd\" value=\"\" :password=\"true\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xieyi-item\" v-if=\"xystatus==1\">\r\n\t\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox class=\"checkbox\" value=\"1\" :checked=\"isagree\"/>我已阅读并同意</label></checkbox-group>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun\">{{xyname}}</text>\r\n\t\t\t\t\t<text @tap=\"showxieyiFun\" v-if=\"xyname2\">和</text>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun2\" v-if=\"xyname2\">{{xyname2}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"form-btn\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" form-type=\"submit\">登录</button>\r\n\t\t\t\t<button class=\"form-btn2\" @tap=\"goback\" v-if=\"!login_mast\">暂不登录</button>\r\n\t\t\t\t<button v-if=\"platform2 == 'ios' && logintype_4==true\" class=\"ioslogin-btn\" @tap=\"ioslogin\" style=\"width:100%\"><image src=\"/static/images/apple.png\"/>通过Apple登录</button>\r\n\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t<button v-if=\"logintype_6==true\" class=\"googlelogin-btn\" @tap=\"googlelogin\">Google登录</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<div v-if=\"logintype_6==true\" class=\"googlelogin-btn2\" id=\"googleloginBtn\" data-shape=\"circle\">Google登录</div>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t\t</form>\r\n\t\t\t<view class=\"regtip\">\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"reg\" data-opentype=\"redirect\">注册账号</view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view @tap=\"goto\" data-url=\"getpwd\" data-opentype=\"redirect\" v-if=\"needsms\">忘记密码？</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<block v-if=\"logintype_2 || logintype_3\">\r\n\t\t\t\t<view class=\"othertip\">\r\n\t\t\t\t\t<view class=\"othertip-line\"></view>\r\n\t\t\t\t\t<view class=\"othertip-text\">\r\n\t\t\t\t\t\t<text class=\"txt\">其他方式登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"othertip-line\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"othertype\">\r\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_3\" @tap=\"weixinlogin\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/login-'+platformimg+'.png'\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">{{platformname}}登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_2\" @tap=\"changelogintype\" data-type=\"2\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/reg-tellogin.png'\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">手机号登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</block>\r\n\t\t<block v-if=\"logintype==2\">\r\n\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t\t<view class=\"title\">用户登录</view>\r\n\t\t\t<view class=\"loginform\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" class=\"img\"/>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-code.png\" class=\"img\"/>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\r\n\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\r\n\t\t\t\t</view>\r\n        <view class=\"form-item\" v-if=\"reg_invite_code && !parent\">\r\n        \t<image src=\"/static/img/reg-yqcode.png\" class=\"img\"/>\r\n        \t<input type=\"text\" class=\"input\" :placeholder=\"'请输入邀请人'+reg_invite_code_text+'(选填)'\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"yqcode\" value=\"\"/>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"reg_invite_code && parent\" style=\"color: #333;\">\r\n          邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}} \r\n        </view>\r\n\t\t\t\t<view class=\"xieyi-item\" v-if=\"xystatus==1\">\r\n\t\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox class=\"checkbox\" value=\"1\" :checked=\"isagree\"/>我已阅读并同意</label></checkbox-group>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun\">{{xyname}}</text>\r\n\t\t\t\t\t<text @tap=\"showxieyiFun\" v-if=\"xyname2\">和</text>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun2\" v-if=\"xyname2\">{{xyname2}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">登录</button>\r\n\t\t\t\t<button class=\"form-btn2\" @tap=\"goback\" v-if=\"!login_mast\">暂不登录</button>\r\n\t\t\t\t<button v-if=\"platform2 == 'ios' && logintype_4==true\" class=\"ioslogin-btn\" @tap=\"ioslogin\" style=\"width:100%\"><image src=\"/static/images/apple.png\"/>通过Apple登录</button>\r\n\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t<button v-if=\"logintype_6==true\" class=\"googlelogin-btn\" @tap=\"googlelogin\">Google登录</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<div v-if=\"logintype_6==true\" class=\"googlelogin-btn2\" id=\"googleloginBtn\" data-shape=\"circle\">Google登录</div>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t\t</form>\r\n\t\t\t<block v-if=\"logintype_1 || logintype_3\">\r\n\t\t\t\t<view class=\"othertip\">\r\n\t\t\t\t\t<view class=\"othertip-line\"></view>\r\n\t\t\t\t\t<view class=\"othertip-text\">\r\n\t\t\t\t\t\t<text class=\"txt\">其他方式登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"othertip-line\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"othertype\">\r\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_3\" @tap=\"weixinlogin\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/login-'+platformimg+'.png'\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">{{platformname}}登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_1\" @tap=\"changelogintype\" data-type=\"1\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/reg-tellogin.png'\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">密码登录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</block>\r\n\t\t<block v-if=\"logintype==3\">\r\n\t\t\t<view class=\"authlogin\">\r\n\t\t\t\t<view class=\"authlogin-logo\"><image :src=\"logo\" style=\"width:100%;height:100%\"/></view>\r\n\t\t\t\t<view class=\"authlogin-name\">授权登录{{name}}</view>\r\n\t\t\t\t<button class=\"authlogin-btn\" @tap=\"authlogin\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">{{platformname}}授权登录</button>\r\n\t\t\t\t<button class=\"authlogin-btn2\" @tap=\"goback\" v-if=\"!login_mast\">暂不登录</button>\r\n\t\t\t\t<button v-if=\"platform2 == 'ios' && logintype_4==true\" class=\"ioslogin-btn\" @tap=\"ioslogin\"><image src=\"/static/images/apple.png\"/>通过Apple登录</button>\r\n\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t<button v-if=\"logintype_6==true\" class=\"googlelogin-btn\" @tap=\"googlelogin\">Google登录</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<div v-if=\"logintype_6==true\" class=\"googlelogin-btn2\" id=\"googleloginBtn\" data-shape=\"circle\">Google登录</div>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view class=\"xieyi-item\" v-if=\"xystatus==1\">\r\n\t\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox class=\"checkbox\" value=\"1\" :checked=\"isagree\"/>我已阅读并同意</label></checkbox-group>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun\">{{xyname}}</text>\r\n\t\t\t\t\t<text @tap=\"showxieyiFun\" v-if=\"xyname2\">和</text>\r\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun2\" v-if=\"xyname2\">{{xyname2}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<!-- 绑定手机号 -->\r\n\t\t<block v-if=\"logintype==4\">\r\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\r\n\t\t\t\t<view class=\"authlogin\">\r\n\t\t\t\t\t<view class=\"authlogin-logo\"><image :src=\"logo\" style=\"width:100%;height:100%\"/></view>\r\n\t\t\t\t\t<view class=\"authlogin-name\">授权登录{{name}}</view>\r\n\t\t\t\t\t<button class=\"authlogin-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">{{platformname}}授权绑定手机号</button>\r\n\t\t\t\t\t<button class=\"authlogin-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!--  #ifndef MP-WEIXIN -->\r\n\t\t\t\t<form @submit=\"bindregister\" @reset=\"formReset\">\r\n\t\t\t\t\t<view class=\"title\">绑定手机号</view>\r\n\t\t\t\t\t<view class=\"loginform\">\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" class=\"img\"/>\r\n\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/reg-code.png\" class=\"img\"/>\r\n\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\r\n\t\t\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\r\n\t\t\t\t\t\t<button class=\"form-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</form>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t</block>\r\n\r\n\t\t\r\n\t\t<!-- 设置头像昵称 -->\r\n\t\t<block v-if=\"logintype==5\">\r\n\t\t\t<form @submit=\"setnicknameregister\" @reset=\"formReset\">\r\n\t\t\t\t<view class=\"title\">请设置头像昵称</view>\r\n\t\t\t\t<view class=\"loginform\">\r\n\t\t\t\t\t<!--  #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\r\n\t\t\t\t\t\t<view class=\"flex1\">头像</view>\r\n\t\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" style=\"width:100rpx;height:100rpx;\">\r\n\t\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100%;height:100%;border-radius:50%\"></image>\r\n\t\t\t\t\t\t</button> \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\r\n\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\r\n\t\t\t\t\t\t<input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!--  #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\r\n\t\t\t\t\t\t<view class=\"flex1\">头像</view>\r\n\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100rpx;height:100rpx;border-radius:50%\" @tap=\"uploadHeadimg\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" value=\"\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\r\n\t\t\t\t\t<button class=\"form-btn2\" @tap=\"nosetnicknameregister\" v-if=\"login_setnickname==1\">暂不设置</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\t\t</block>\r\n\r\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"xycontent\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"showxieyi2\" class=\"xieyibox\">\r\n\t\t\t<view class=\"xieyibox-content\">\r\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\r\n\t\t\t\t\t<parse :content=\"xycontent2\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi2\">已阅读并同意</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view v-if=\"showAddressConfirm\" class=\"address-confirm-box\">\r\n\t\t\t<view class=\"address-confirm-content\">\r\n\t\t\t\t<view class=\"address-confirm-title\">地址确认</view>\r\n\t\t\t\t<view class=\"address-confirm-info\">\r\n\t\t\t\t\t<view>国家：{{addressInfo.country}}</view>\r\n\t\t\t\t\t<view>省份：{{addressInfo.province}}</view>\r\n\t\t\t\t\t<view>城市：{{addressInfo.city}}</view>\r\n\t\t\t\t\t<view>地区：{{addressInfo.area}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"address-confirm-btns\">\r\n\t\t\t\t\t<button class=\"address-confirm-btn\" @tap=\"confirmAddress(true)\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确认</button>\r\n\t\t\t\t\t<button class=\"address-confirm-btn\" @tap=\"confirmAddress(false)\" v-if=\"addressInfo.can_edit_address\">修改</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n     \t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tplatform2:app.globalData.platform2,\r\n\t\t\t\r\n\t\t\tplatform:'',\r\n\t\t\tplatformname:'',\r\n\t\t\tplatformimg:'weixin',\r\n\t\t\tlogintype:0,\r\n\t\t\tlogintype_1:true,\r\n\t\t\tlogintype_2:false,\r\n\t\t\tlogintype_3:false,\r\n\t\t\tlogintype_4:false,\r\n\t\t\tlogintype_6:false,\r\n\t\t\tisioslogin:false,\r\n\t\t\tisgooglelogin:false,\r\n\t\t\tgoogle_client_id:'',\r\n\t\t\tneedsms:false,\r\n\t\t\tlogo:'',\r\n\t\t\tname:'',\r\n\t\t\txystatus:0,\r\n\t\t\txyname:'',\r\n\t\t\txycontent:'',\r\n\t\t\txyname2:'',\r\n\t\t\txycontent2:'',\r\n\t\t\tshowxieyi:false,\r\n\t\t\tshowxieyi2:false,\r\n\t\t\tisagree:false,\r\n      smsdjs: '',\r\n\t\t\ttel:'',\r\n      hqing: 0,\r\n\t\t\tfrompage:'/pages/my/usercenter',\r\n\t\t\twxloginclick:false,\r\n\t\t\tiosloginclick:false,\r\n\t\t\tgoogleloginclick:false,\r\n\t\t\tlogin_bind:0,\r\n\t\t\tlogin_setnickname:0,\r\n\t\t\tlogin_mast:false,\r\n      reg_invite_code:0,\r\n      reg_invite_code_text:'',\r\n      parent:{},\r\n\t\t\ttmplids:[],\r\n\t\t\tdefault_headimg:app.globalData.pre_url + '/static/img/touxiang.png',\r\n\t\t\theadimg:'',\r\n\t\t\tnickname:'',\r\n\t\t\tuserIpAddress: '',\r\n\t\t\tshowAddressConfirm: false,\r\n\t\t\taddressInfo: {\r\n\t\t\t\tcountry: '',\r\n\t\t\t\tprovince: '',\r\n\t\t\t\tcity: '',\r\n\t\t\t\tarea: '',\r\n\t\t\t\tcan_edit_address: 0\r\n\t\t\t}\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);\r\n\t\tconsole.log(this.frompage,'this.opt.frompage');\r\n\t\t\r\n\t\tif(this.opt.logintype) this.logintype = this.opt.logintype;\r\n\t\tif(this.opt.login_bind) this.login_bind = this.opt.login_bind;\r\n\t\tthis.getdata();\r\n\t\tthis.getUserIpAddress();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiIndex/login', {pid:app.globalData.pid,xlevel:app.globalData.xlevel}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\tapp.alert(res.mg);return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.logintype_1 = res.logintype_1;\r\n\t\t\t\tthat.logintype_2 = res.logintype_2;\r\n\t\t\t\tthat.logintype_3 = res.logintype_3;\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif(that.platform2 == 'ios'){\r\n\t\t\t\t\tif (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {\r\n\t\t\t\t\t\tconsole.log('已安装微信')\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.logintype_3 = false;\r\n\t\t\t\t\t\tconsole.log('未安装微信')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthat.logintype_4 = res.logintype_4;\r\n\t\t\t\tthat.logintype_6 = res.logintype_6 || false;\r\n\t\t\t\tthat.google_client_id = res.google_client_id || '';\r\n\t\t\t\tthat.login_mast = res.login_mast;\r\n\t\t\t\tthat.needsms = res.needsms;\r\n        that.reg_invite_code = res.reg_invite_code;\r\n        that.reg_invite_code_text = res.reg_invite_code_text;\r\n        that.parent = res.parent;\r\n\t\t\t\tif(that.logintype==0){\r\n\t\t\t\t\tif(that.logintype_1){\r\n\t\t\t\t\t\tthat.logintype = 1;\r\n\t\t\t\t\t}else if(that.logintype_2){\r\n\t\t\t\t\t\tthat.logintype = 2;\r\n\t\t\t\t\t}else if(that.logintype_3){\r\n\t\t\t\t\t\tthat.logintype = 3;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.xystatus = res.xystatus;\r\n\t\t\t\tthat.xyname = res.xyname;\r\n\t\t\t\tthat.xycontent = res.xycontent;\r\n\t\t\t\tthat.xyname2 = res.xyname2;\r\n\t\t\t\tthat.xycontent2 = res.xycontent2;\r\n\t\t\t\tthat.logo = res.logo;\r\n\t\t\t\tthat.name = res.name;\r\n\t\t\t\tthat.platform = res.platform;\r\n\t\t\t\tif(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){\r\n\t\t\t\t\tthat.platformname = '一键';\r\n\t\t\t\t\tthat.platformimg = 'weixin';\r\n\t\t\t\t}\r\n\t\t\t\tif(that.platform == 'toutiao'){\r\n\t\t\t\t\tthat.platformname = '头条';\r\n\t\t\t\t\tthat.platformimg = 'toutiao';\r\n\t\t\t\t}\r\n\t\t\t\tif(that.platform == 'alipay'){\r\n\t\t\t\t\tthat.platformname = '支付宝';\r\n\t\t\t\t\tthat.platformimg = 'alipay';\r\n\t\t\t\t}\r\n\t\t\t\tif(that.platform == 'qq'){\r\n\t\t\t\t\tthat.platformname = 'QQ';\r\n\t\t\t\t\tthat.platformimg = 'qq';\r\n\t\t\t\t}\r\n\t\t\t\tif(that.platform == 'baidu'){\r\n\t\t\t\t\tthat.platformname = '百度';\r\n\t\t\t\t\tthat.platformimg = 'baidu';\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(that.logintype_6){\r\n\t\t\t\t\tvar hm = document.createElement('script');\r\n\t\t\t\t\thm.src=\"https://accounts.google.com/gsi/client\";\r\n\t\t\t\t\tdocument.body.appendChild(hm);\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tgoogle.accounts.id.initialize({\r\n\t\t\t\t\t\t\tclient_id: that.google_client_id,\r\n\t\t\t\t\t\t\tcallback: function(response){\r\n\t\t\t\t\t\t\t\tconsole.log(response);\r\n\t\t\t\t\t\t\t\tvar credential = response.credential;\r\n\t\t\t\t\t\t\t\tvar base64Url = credential.split('.')[1];\r\n\t\t\t\t\t\t\t\tvar base64 = base64Url.replace(/-/g,'+').replace(/_/g,'/');\r\n\t\t\t\t\t\t\t\tvar jsonPayload = decodeURIComponent(\r\n\t\t\t\t\t\t\t\t\twindow.atob(base64).split('').map(function(c){\r\n\t\t\t\t\t\t\t\t\t\treturn '%'+('00'+c.charCodeAt(0).toString(16)).slice(-2);\r\n\t\t\t\t\t\t\t\t\t}).join('')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\tvar resdata = JSON.parse(jsonPayload);\r\n\t\t\t\t\t\t\t\tresdata.openId = resdata.sub;\r\n\t\t\t\t\t\t\t\tconsole.log(resdata);\r\n\t\t\t\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/googlelogin',{userInfo:resdata,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\r\n\t\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\r\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\r\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tgoogle.accounts.id.renderButton(\r\n\t\t\t\t\t\t\tdocument.getElementById(\"googleloginBtn\"),\r\n\t\t\t\t\t\t\t{ theme: \"outline\", size: \"large\",width:'300'}  // customization attributes\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\tgoogle.accounts.id.prompt();\r\n\t\t\t\t\t},500);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t},\r\n    formSubmit: function (e) {\r\n\t\t\tvar that = this;\r\n      var formdata = e.detail.value;\r\n      if (formdata.tel == ''){\r\n        app.alert('请输入手机号');\r\n        return;\r\n      }\r\n\t\t\tif(that.logintype == 1){\r\n\t\t\t\tif (formdata.pwd == '') {\r\n\t\t\t\t\tapp.alert('请输入密码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(that.logintype == 2){\r\n\t\t\t\tif (formdata.smscode == '') {\r\n\t\t\t\t\tapp.alert('请输入短信验证码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\t\tapp.error('请先阅读并同意用户注册协议');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif(that.logintype == 4){\r\n\t\t\t\tif (typeof(formdata.pwd) != 'undefined' && formdata.pwd == '') {\r\n\t\t\t\t\tapp.alert('请输入密码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof(formdata.smscode) != 'undefined' && formdata.smscode == '') {\r\n\t\t\t\t\tapp.alert('请输入短信验证码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\t// 添加IP地址参数\r\n\t\t\tvar params = {\r\n\t\t\t\ttel: formdata.tel,\r\n\t\t\t\tpwd: formdata.pwd,\r\n\t\t\t\tsmscode: formdata.smscode,\r\n\t\t\t\tlogintype: that.logintype,\r\n\t\t\t\tpid: app.globalData.pid,\r\n\t\t\t\tyqcode: formdata.yqcode,\r\n\t\t\t\txlevel: app.globalData.xlevel,\r\n\t\t\t\tip_address: that.userIpAddress || uni.getStorageSync('user_ip_address') || ''\r\n\t\t\t};\r\n\t\t\t\r\n      app.post(\"ApiIndex/loginsub\", params, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (res.status == 1) {\r\n\t\t\t\t\tuni.setStorageSync('mid',res.mid);\r\n\t\t\t\t\t// 检查是否需要确认地址\r\n\t\t\t\t\tif (res.need_confirm_address && res.address_info) {\r\n\t\t\t\t\t\tthat.addressInfo = res.address_info;\r\n\t\t\t\t\t\tthat.showAddressConfirm = true;\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tif(res.tmplids){\r\n\t\t\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n        } else {\r\n          app.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n\t\tgetPhoneNumber: function (e) {\r\n\t\t\tvar that = this\r\n\t\t\tconsole.log(e);\r\n\t\t\tif(e.detail.errMsg == \"getPhoneNumber:fail user deny\"){\r\n\t\t\t\tapp.error('请同意授权获取手机号');return;\r\n\t\t\t}\r\n\t\t\tif(!e.detail.iv || !e.detail.encryptedData){\r\n\t\t\t\tapp.error('请同意授权获取手机号');return;\r\n\t\t\t}\r\n\t\t\twx.login({success (res1){\r\n\t\t\t\tconsole.log(res1);\r\n\t\t\t\tvar code = res1.code;\r\n\t\t\t\t//用户允许授权\r\n\t\t\t\tapp.post('ApiIndex/wxRegister',{xlevel:app.globalData.xlevel,headimg:that.headimg,nickname:that.nickname,iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\r\n\t\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\t\tapp.success(res2.msg);\r\n\t\t\t\t\t\tif(res2.tmplids){\r\n\t\t\t\t\t\t\tthat.tmplids = res2.tmplids;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t})\r\n\t\t\t}});\r\n\t\t},\r\n\t\tsetnicknameregister:function(e){\r\n\t\t\t//console.log(e);\r\n\t\t\t//return;\r\n\t\t\tthis.nickname = e.detail.value.nickname;\r\n\t\t\tif(this.nickname == '' || this.headimg == ''){\r\n\t\t\t\tapp.alert('请设置头像和昵称');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(this.login_bind!=0){\r\n\t\t\t\tthis.logintype = 4;\r\n\t\t\t\tthis.isioslogin = false;\r\n\t\t\t\tthis.isgooglelogin = false;\r\n\t\t\t}else{\r\n\t\t\t\tthis.register(this.headimg,this.nickname,'','');\r\n\t\t\t}\r\n\t\t},\r\n\t\tnosetnicknameregister:function(){\r\n\t\t\tthis.nickname = '';\r\n\t\t\tthis.headimg = '';\r\n\t\t\tif(this.login_bind!=0){\r\n\t\t\t\tthis.logintype = 4;\r\n\t\t\t\tthis.isioslogin = false;\r\n\t\t\t\tthis.isgooglelogin = false;\r\n\t\t\t}else{\r\n\t\t\t\tthis.register('','','','');\r\n\t\t\t}\r\n\t\t},\r\n\t\tbindregister:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar formdata = e.detail.value;\r\n      if (formdata.tel == ''){\r\n        app.alert('请输入手机号');\r\n        return;\r\n      }\r\n\t\t\tif (formdata.smscode == '') {\r\n\t\t\t\tapp.alert('请输入短信验证码');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthat.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);\r\n\t\t},\r\n\t\tnobindregister:function(){\r\n\t\t\tthis.register(this.headimg,this.nickname,'','');\r\n\t\t},\r\n\t\tregister:function(headimg,nickname,tel,smscode){\r\n\t\t\tvar that = this;\r\n\t\t\tvar url = '';\r\n\t\t\tif(that.platform == 'app') {\r\n\t\t\t\turl = 'ApiIndex/appwxRegister';\r\n\t\t\t\tif(that.isioslogin){\r\n\t\t\t\t\turl = 'ApiIndex/iosRegister';\r\n\t\t\t\t}\r\n\t\t\t} else if(that.platform=='mp' || that.platform=='h5') {\r\n\t\t\t\turl = 'ApiIndex/shouquanRegister';\r\n\t\t\t} else {\r\n\t\t\t\turl = 'ApiIndex/'+that.platform+'Register';\r\n\t\t\t}\r\n\t\t\tif(that.isgooglelogin){\r\n\t\t\t\turl = 'ApiIndex/googleRegister';\r\n\t\t\t}\r\n\t\t\tapp.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\r\n\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\tapp.success(res2.msg);\r\n\t\t\t\t\tif(res2.tmplids){\r\n\t\t\t\t\t\tthat.tmplids = res2.tmplids;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t});\r\n\t\t},\r\n\t\tauthlogin:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\tapp.error('请先阅读并同意用户注册协议');\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tthat.weixinlogin();\r\n\t\t},\r\n\t\tweixinlogin:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\tthat.showxieyi = true;\r\n\t\t\t\tthat.wxloginclick = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tconsole.log('weixinlogin')\r\n\t\t\tthat.wxloginclick = false;\r\n\t\t\tapp.authlogin(function(res){\r\n\t\t\t\tconsole.log(res);\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tconsole.log('frompage')\r\n\t\t\t\t\t\tconsole.log(that.frompage)\r\n\t\t\t\t\t\tapp.goto( that.frompage,'redirect');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} else if (res.status == 3) {\r\n\t\t\t\t\tthat.logintype = 5;\r\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname;\r\n\t\t\t\t\tthat.login_bind = res.login_bind;\r\n\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\tthat.isgooglelogin = false;\r\n\t\t\t\t} else if (res.status == 2) {\r\n\t\t\t\t\tthat.logintype = 4;\r\n\t\t\t\t\tthat.login_bind = res.login_bind;\r\n\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\tthat.isgooglelogin = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t},{frompage:that.frompage});\r\n\t\t},\r\n\t\tioslogin:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\tthat.showxieyi = true;\r\n\t\t\t\tthat.iosloginclick = true;\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.login({  \r\n\t\t\t\tprovider: 'apple',  \r\n\t\t\t\tsuccess: function (loginRes) {  \r\n\t\t\t\t\tconsole.log(loginRes);\r\n\t\t\t\t\t// 登录成功  \r\n\t\t\t\t\tuni.getUserInfo({  \r\n\t\t\t\t\t\tprovider: 'apple',  \r\n\t\t\t\t\t\tsuccess(res) { \r\n\t\t\t\t\t\t\t// 获取用户信息成功\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tif(res.userInfo && res.userInfo.openId){\r\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/ioslogin',{userInfo:res.userInfo,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\r\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\r\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\r\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}  \r\n\t\t\t\t\t})  \r\n\t\t\t\t},  \r\n\t\t\t\tfail: function (err) {  \r\n\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\tapp.error('登录失败');\r\n\t\t\t\t}  \r\n\t\t\t});  \r\n\t\t},\r\n\t\tgooglelogin:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\tthat.showxieyi = true;\r\n\t\t\t\tthat.googleloginclick = true;\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tuni.login({  \r\n\t\t\t\tprovider: 'google',  \r\n\t\t\t\tsuccess: function (loginRes) {  \r\n\t\t\t\t\tconsole.log(loginRes);\r\n\t\t\t\t\t// 登录成功  \r\n\t\t\t\t\tuni.getUserInfo({  \r\n\t\t\t\t\t\tprovider: 'google',  \r\n\t\t\t\t\t\tsuccess(res) { \r\n\t\t\t\t\t\t\t// 获取用户信息成功\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t//alert(JSON.stringify(res));\r\n\t\t\t\t\t\t\t//if(res.userInfo && res.userInfo.openId){\r\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/googlelogin',{userInfo:res.userInfo,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\r\n\t\t\t\t\t\t\t\t\tconsole.log(res2);\r\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('frompage')\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(that.frompage)\r\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 3) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 5;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_setnickname = res2.login_setnickname\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\r\n\t\t\t\t\t\t\t\t\t\tthat.logintype = 4;\r\n\t\t\t\t\t\t\t\t\t\tthat.isioslogin = false;\r\n\t\t\t\t\t\t\t\t\t\tthat.isgooglelogin = true;\r\n\t\t\t\t\t\t\t\t\t\tthat.login_bind = res2.login_bind\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tapp.error(res2.msg);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t//}\r\n\t\t\t\t\t\t}  \r\n\t\t\t\t\t})  \r\n\t\t\t\t},  \r\n\t\t\t\tfail: function (err) {  \r\n\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\tapp.error('登录失败');\r\n\t\t\t\t}  \r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tchangelogintype:function(e){\r\n\t\t\tvar logintype = e.currentTarget.dataset.type\r\n\t\t\tthis.logintype = logintype;\r\n\t\t},\r\n    isagreeChange: function (e) {\r\n      var val = e.detail.value;\r\n      if (val.length > 0) {\r\n        this.isagree = true;\r\n      } else {\r\n        this.isagree = false;\r\n      }\r\n      console.log(this.isagree);\r\n    },\r\n    showxieyiFun: function () {\r\n      this.showxieyi = true;\r\n    },\r\n    hidexieyi: function () {\r\n      this.showxieyi = false;\r\n\t\t\tthis.isagree = true;\r\n\t\t\tif(this.wxloginclick){\r\n\t\t\t\tthis.weixinlogin();\r\n\t\t\t}\r\n\t\t\tif(this.iosloginclick){\r\n\t\t\t\tthis.ioslogin();\r\n\t\t\t}\r\n\t\t\tif(this.googleloginclick){\r\n\t\t\t\tthis.googlelogin();\r\n\t\t\t}\r\n\r\n    },\r\n    showxieyiFun2: function () {\r\n      this.showxieyi2 = true;\r\n    },\r\n    hidexieyi2: function () {\r\n      this.showxieyi2 = false;\r\n\t\t\tthis.isagree = true;\r\n\t\t\tif(this.wxloginclick){\r\n\t\t\t\tthis.weixinlogin();\r\n\t\t\t}\r\n\t\t\tif(this.iosloginclick){\r\n\t\t\t\tthis.ioslogin();\r\n\t\t\t}\r\n\t\t\tif(this.googleloginclick){\r\n\t\t\t\tthis.googlelogin();\r\n\t\t\t}\r\n    },\r\n    telinput: function (e) {\r\n      this.tel = e.detail.value\r\n    },\r\n\t\tuploadHeadimg:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tsizeType: ['original', 'compressed'],\r\n\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\r\n\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\r\n\t\t\t\t\tapp.showLoading('上传中');\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\r\n\t\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) { //alert(res.errMsg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonChooseAvatar:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('上传中');\r\n\t\t\tuni.uploadFile({\r\n\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\r\n\t\t\t\tfilePath: e.detail.avatarUrl,\r\n\t\t\t\tname: 'file',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    smscode: function () {\r\n      var that = this;\r\n      if (that.hqing == 1) return;\r\n      that.hqing = 1;\r\n      var tel = that.tel;\r\n      if (tel == '') {\r\n        app.alert('请输入手机号码');\r\n        that.hqing = 0;\r\n        return false;\r\n      }\r\n      if (!/^1[3456789]\\d{9}$/.test(tel)) {\r\n        app.alert(\"手机号码有误，请重填\");\r\n        that.hqing = 0;\r\n        return false;\r\n      }\r\n      app.post(\"ApiIndex/sendsms\", {tel: tel}, function (data) {\r\n        if (data.status != 1) {\r\n          app.alert(data.msg);return;\r\n        }\r\n      });\r\n      var time = 120;\r\n      var interval1 = setInterval(function () {\r\n        time--;\r\n        if (time < 0) {\r\n          that.smsdjs = '重新获取';\r\n          that.hqing = 0;\r\n          clearInterval(interval1);\r\n        } else if (time >= 0) {\r\n          that.smsdjs = time + '秒';\r\n        }\r\n      }, 1000);\r\n    },\r\n\t\tgetUserIpAddress: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 使用ipify API获取IP地址\r\n\t\t\tuni.request({\r\n\t\t\t\turl: 'https://api.ipify.org?format=json',\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif(res.data && res.data.ip) {\r\n\t\t\t\t\t\tthat.userIpAddress = res.data.ip;\r\n\t\t\t\t\t\tconsole.log('获取到IP地址:', that.userIpAddress);\r\n\t\t\t\t\t\t// 本地存储IP地址\r\n\t\t\t\t\t\tuni.setStorageSync('user_ip_address', that.userIpAddress);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 第一个API失败，尝试备用方法\r\n\t\t\t\t\t\tthat.getUserIpAddressBackup();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(err) {\r\n\t\t\t\t\tconsole.log('获取IP地址失败:', err);\r\n\t\t\t\t\t// 尝试使用备用方法\r\n\t\t\t\t\tthat.getUserIpAddressBackup();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tgetUserIpAddressBackup: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 备用方法，使用ip-api.com\r\n\t\t\tuni.request({\r\n\t\t\t\turl: 'https://ip-api.com/json',\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif(res.data && res.data.query) {\r\n\t\t\t\t\t\tthat.userIpAddress = res.data.query; // ip-api使用query字段返回IP\r\n\t\t\t\t\t\tconsole.log('备用方法获取到IP地址:', that.userIpAddress);\r\n\t\t\t\t\t\t// 本地存储IP地址\r\n\t\t\t\t\t\tuni.setStorageSync('user_ip_address', that.userIpAddress);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 尝试第三个备用API\r\n\t\t\t\t\t\tthat.getUserIpAddressBackup2();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(err) {\r\n\t\t\t\t\tconsole.log('备用方法获取IP地址失败:', err);\r\n\t\t\t\t\t// 尝试第三个备用API\r\n\t\t\t\t\tthat.getUserIpAddressBackup2();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tgetUserIpAddressBackup2: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 第三备用方法，使用jsonip.com\r\n\t\t\tuni.request({\r\n\t\t\t\turl: 'https://jsonip.com',\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif(res.data && res.data.ip) {\r\n\t\t\t\t\t\tthat.userIpAddress = res.data.ip;\r\n\t\t\t\t\t\tconsole.log('第三备用方法获取到IP地址:', that.userIpAddress);\r\n\t\t\t\t\t\t// 本地存储IP地址\r\n\t\t\t\t\t\tuni.setStorageSync('user_ip_address', that.userIpAddress);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('所有IP获取方法均失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(err) {\r\n\t\t\t\t\tconsole.log('第三备用方法获取IP地址失败:', err);\r\n\t\t\t\t\tconsole.log('所有IP获取方法均失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 确认地址\r\n\t\tconfirmAddress: function(isConfirmed) {\r\n\t\t\tvar that = this;\r\n\t\t\tif (isConfirmed) {\r\n\t\t\t\t// 用户确认地址\r\n\t\t\t\tapp.post(\"ApiIndex/confirm_address\", {confirm: 1}, function(res) {\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\tapp.success(res.msg || '地址确认成功');\r\n\t\t\t\t\t\tthat.showAddressConfirm = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 登录成功后跳转\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(res.msg || '地址确认失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 用户要修改地址，跳转到专门的地址修改页面\r\n\t\t\t\tthat.showAddressConfirm = false;\r\n\t\t\t\tvar frompage = encodeURIComponent(that.frompage);\r\n\t\t\t\tapp.goto('/pages/my/editaddress?frompage=' + frompage, 'navigate');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 取消修改地址\r\n\t\tcancelAddressEdit: function() {\r\n\t\t\tthis.showAddressEdit = false;\r\n\t\t\tthis.showAddressConfirm = true;\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\npage{background:#ffffff}\r\n.container{width:100%;}\r\n.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}\r\n.loginform{ width:100%;padding:0 50rpx;border-radius:5px;background: #FFF;}\r\n.loginform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx}\r\n/*.loginform .form-item:last-child{border:0}*/\r\n.loginform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}\r\n.loginform .form-item .input{flex:1;color: #000;}\r\n.loginform .form-item .code{font-size:30rpx}\r\n.xieyi-item{display:flex;align-items:center;margin-top:30rpx}\r\n.xieyi-item{font-size:24rpx;color:#B2B5BE}\r\n.xieyi-item .checkbox{transform: scale(0.6);}\r\n.loginform .form-btn{margin-top:60rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}\r\n.loginform .form-btn2{width:100%;height:80rpx;line-height:80rpx;background:#EEEEEE;border-radius:40rpx;color:#A9A9A9;margin-top:30rpx}\r\n.regtip{color:#737785;font-size:26rpx;display:flex;width:100%;padding:0 80rpx;margin-top:30rpx}\r\n\r\n.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:160rpx;}\r\n.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}\r\n.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\r\n.othertip-text .txt{color:#A3A3A3;font-size:22rpx}\r\n\r\n.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}\r\n.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}\r\n.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}\r\n.othertype-item .txt{color:#A3A3A3;font-size:24rpx}\r\n\r\n.authlogin{display:flex;flex-direction:column;align-items:center}\r\n.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}\r\n.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}\r\n.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}\r\n.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}\r\n.ioslogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}\r\n.ioslogin-btn image{width:26rpx;height:26rpx;margin-right:16rpx;}\r\n\r\n.googlelogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}\r\n\r\n.googlelogin-btn2{margin-top:30rpx;display:flex;justify-content:center;align-items:center}\r\n\r\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\r\n\r\n.address-confirm-box{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\r\n.address-confirm-content{width:90%;margin:0 auto;height:auto;margin-top:30%;background:#fff;color:#333;padding:30rpx;position:relative;border-radius:12rpx;}\r\n.address-confirm-title{font-size:36rpx;text-align:center;font-weight:bold;margin-bottom:30rpx;}\r\n.address-confirm-info{font-size:30rpx;margin-bottom:30rpx;line-height:1.8;}\r\n.address-confirm-btns{display:flex;justify-content:space-between;}\r\n.address-confirm-btn{flex:1;height:80rpx;line-height:80rpx;color:#fff;font-size:30rpx;border-radius:40rpx;margin:0 20rpx;}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115021586\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}