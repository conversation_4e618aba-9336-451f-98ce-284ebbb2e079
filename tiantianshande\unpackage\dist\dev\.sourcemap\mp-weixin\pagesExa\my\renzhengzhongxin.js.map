{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?18ef", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?726b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?6538", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?9317", "uni-app:///pagesExa/my/renzhengzhongxin.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?54d5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/renzhengzhongxin.vue?5663"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "items", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "console", "goToAuthPage", "url", "goToProfile", "getmenuindex"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,gxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BzxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACA;EAEA;EACAC;IACA;EACA;EACAC;IAEA;IACAC;MACA;MACAC;MACAC;QAEAC;QACAF;QACA;UACAA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAE;MACA;QACAlB;UACAmB;QACA;MACA;QACAH;MACA;IACA;IAEAI;MACA;MACApB;QACAmB;MACA;IACA;;IAGAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,ikCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/renzhengzhongxin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/renzhengzhongxin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./renzhengzhongxin.vue?vue&type=template&id=f93e3278&\"\nvar renderjs\nimport script from \"./renzhengzhongxin.vue?vue&type=script&lang=js&\"\nexport * from \"./renzhengzhongxin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./renzhengzhongxin.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/renzhengzhongxin.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./renzhengzhongxin.vue?vue&type=template&id=f93e3278&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./renzhengzhongxin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./renzhengzhongxin.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <block v-if=\"isload\">\r\n      <!-- 认证项目列表 -->\r\n      <view v-for=\"(item, index) in items\" :key=\"index\" class=\"content\" @tap=\"goToAuthPage(item)\">\r\n        <view class=\"f1\">\r\n          <image :src=\"item.icon\" class=\"t3\" mode=\"aspectFit\"></image>\r\n          <view class=\"text-section\">\r\n            <text class=\"t1\">{{ item.title }}</text>\r\n            <text class=\"t2\">{{ item.subtitle }}</text>\r\n          </view>\r\n          <text class=\"flex1\"></text>\r\n        </view>\r\n        <!-- 认证状态丝带，标记在右上角 -->\r\n        <view :class=\"item.status ? 'ribbon-3 certified' : 'ribbon-3 uncertified'\">\r\n          <span>{{ item.status ? '已认证' : '未认证' }}</span>\r\n        </view>\r\n      </view>\r\n\r\n      <view style=\"height:140rpx\"></view>\r\n      <!-- 如需添加其他按钮，可在此处添加 -->\r\n\t  <!-- 返回个人中心的按钮 -->\r\n\t  <view class=\"back-btn\" @tap=\"goto\" data-url=\"/pages/my/usercenter\">返回个人中心</view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      menuindex: -1,\r\n      items: [], // 认证项目列表，将从服务器获取\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.getdata();\r\n\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  methods: {\r\n\t  \t  \r\n    // 获取认证项目列表和用户的认证状态\r\n    getdata: function () {\r\n      var that = this;\r\n      that.loading = true;\r\n      app.post('ApiMy/getRenzhengItems', {}, function (data) {\r\n\t\t  \r\n\t\t  console.log(data);\r\n        that.loading = false;\r\n        if (data.status == 1) {\r\n          that.isload = true;\r\n          that.items = data.data.items; // 从服务器获取的认证项目列表\r\n        } else {\r\n          app.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n    // 跳转到对应的认证页面\r\n    goToAuthPage: function (item) {\r\n      if (item.url) {\r\n        wx.navigateTo({\r\n          url: item.url\r\n        });\r\n      } else {\r\n        app.alert('该认证暂未开放');\r\n      }\r\n    },\r\n\t\r\n\tgoToProfile: function () {\r\n\t  // 直接跳转到个人中心 tab 页\r\n\t  wx.switchTab({\r\n\t    url: '/pages/my/usercenter' // 个人中心 tab 页的路径\r\n\t  });\r\n\t},\r\n\r\n\r\n    getmenuindex: function (index) {\r\n      this.menuindex = index;\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n\t.back-btn {\r\n\t  width: 90%;\r\n\t  margin: 20rpx auto;\r\n\t  padding: 20rpx;\r\n\t  text-align: center;\r\n\t  background-color: #ff4246; /* 按钮颜色 */\r\n\t  color: #fff;\r\n\t  font-size: 32rpx;\r\n\t  border-radius: 8rpx;\r\n\t  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1); /* 按钮阴影效果 */\r\n\t  cursor: pointer;\r\n\t  transition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.back-btn:hover {\r\n\t  background-color: #ff2e2e; /* 悬停时颜色变化 */\r\n\t}\r\n\r\n.container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n.content { \r\n  width: 94%; \r\n  margin: 20rpx 3%; \r\n  background: #fff; \r\n  border-radius: 5px; \r\n  padding: 20rpx 40rpx; \r\n  position: relative; /* 让认证状态丝带定位在右上角 */\r\n}\r\n.content .f1 { \r\n  height: 96rpx; \r\n  line-height: 46rpx; \r\n  display: flex; \r\n  align-items: center; \r\n}\r\n.content .f1 .t1 { \r\n  color: #2B2B2B; \r\n  font-weight: bold; \r\n  font-size: 30rpx; \r\n}\r\n.content .f1 .t2 { \r\n  color: #999999; \r\n  font-size: 28rpx; \r\n  margin-left: 0rpx; \r\n}\r\n.content .t3 { \r\n  width: 68rpx; \r\n  height: 68rpx; \r\n  margin-right: 10rpx; \r\n}\r\n.text-section { \r\n  display: flex; \r\n  flex-direction: column; \r\n  margin-left: 20rpx; \r\n}\r\n.ribbon-3 {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  overflow: hidden;\r\n}\r\n.ribbon-3 > span {\r\n  position: absolute;\r\n  top: 20%;\r\n  right: -40%;\r\n  z-index: 2;\r\n  width: 150%;\r\n  height: 32rpx;\r\n  overflow: hidden;\r\n  transform: rotate(45deg);\r\n  background: #57DD43;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: white;\r\n  font-size: 24rpx;\r\n}\r\n.certified > span {\r\n  background: #57DD43; /* 绿色背景代表已认证 */\r\n}\r\n.uncertified > span {\r\n  background: #FF5252; /* 红色背景代表未认证 */\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./renzhengzhongxin.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./renzhengzhongxin.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061015\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}