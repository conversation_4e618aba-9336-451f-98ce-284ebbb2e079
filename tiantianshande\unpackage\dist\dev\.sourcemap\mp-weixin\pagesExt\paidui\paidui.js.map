{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?1d02", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?6089", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?975e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?6886", "uni-app:///pagesExt/paidui/paidui.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?93c5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/paidui/paidui.vue?1543"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "pageinfo", "loading", "isload", "modalVisible", "selectedOption", "menuindex", "showModal", "nodata", "nomore", "datalist", "textset", "pagenum", "userinfo", "set", "st", "st1", "primary_color", "options", "secondary_color", "xuanze", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "numFilter", "timestampToDate", "getdata", "that", "app", "value", "text", "amount", "description", "options2", "uni", "title", "console", "showExplanation", "closeExplanation", "duihuantixian", "id", "showModal2", "hideModal", "selectOption2", "option", "backToOptions", "confirmEndQueue", "duihuantixian2", "duihuantixian3", "setTimeout", "topay", "changetab", "scrollTop", "duration", "changetab2", "showDistributionFlow", "paidui_id", "showDistributionModal", "content", "showCancel", "confirmText", "checkDataConsistency", "cancelText", "success", "icon", "syncPaiduiData", "showDataFixMenu", "itemList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiP/wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAEA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAd;QAAAC;QAAAJ;MAAA;QACAgB;QACA;QACAA;QACAA;QACA;QACA,uCACA;UACA;YAAAE;YAAAC;YAAAC;YAAAC;UAAA;UACAC;QACA;QACA,iCACA;UACA;YAAAJ;YAAAC;YAAAC;YAAAC;UAAA;UACAC;QACA;QACA,6CACA;UACA;YAAAJ;YAAAC;YAAAC;YAAAC;UAAA;UACAC;QACA;QACAN;QACA;UACAA;UACAO;YACAC;UACA;UACAR;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACAS;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAZ;MACAC;QAAAY;MAAA;QACAb;QACAA;QACA;UACAC;UACA;QACA;UACAA;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAhB;MACA,yBACA;QACAiB;MACA,gCACA;QACAA;MACA,gCACA;QACAA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA,8BACA;QACAnB;MACA,qCACA;QACAA;MACA,qCACA;QACAA;MACA;MACA;IAEA;;IACAoB;MACA;MACApB;MACAC;QAAAY;MAAA;QACAb;QACAA;QACA;UACAC;UACA;QACA;UACAA;QACA;MACA;IACA;IACAoB;MACA;MACArB;MACAC;QAAAY;MAAA;QACAb;QACAA;QACA;UACAC;UACA;QACA;UACAA;UACAqB;YACArB;UACA;UACA;QACA;MACA;IACA;;IACAsB;MACA;MACAvB;MACAC;QACAD;QACAA;QACA;UACAC;UACA;QACA;UACAA;QACA;MACA;IACA;IACAuB;MACA;MACAjB;QACAkB;QACAC;MACA;MACA;IACA;IACAC;MACA;MACApB;QACAkB;QACAC;MACA;MACA;IACA;IACAE;MACA;MACA3B;QAAA4B;MAAA;QACA;UACA7B;QACA;UACAC;QACA;MACA;IACA;IACA6B;MACA;MACA;MACAC;MACAA;MACAA;MACAA;MAEAA;MACA;QACA5D;UACA4D;UACAA;QACA;MACA;QACAA;MACA;MAEAxB;QACAC;QACAuB;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAlC;MACAC;QACAD;QACA;UACA;YACA;YACAO;cACAC;cACAuB;cACAC;cACAC;cACAE;cACAC;gBACA;kBACApC;gBACA;cACA;YACA;UACA;YACAO;cACAC;cACA6B;YACA;UACA;QACA;UACApC;QACA;MACA;QACAD;QACAC;MACA;IACA;IAEA;IACAqC;MACA;MACAtC;MACAC;QACAD;QACA;UACAO;YACAC;YACAuB;YACAC;YACAC;YACAG;cACA;cACApC;YACA;UACA;QACA;UACAC;QACA;MACA;QACAD;QACAC;MACA;IACA;IAEA;IACAsC;MACA;MACAhC;QACAiC;QACAJ;UACA;YACApC;UACA;YACAO;cACAC;cACAuB;cACAC;cACAC;cACAE;cACAC;gBACA;kBACApC;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9lBA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/paidui/paidui.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/paidui/paidui.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./paidui.vue?vue&type=template&id=b09d72aa&\"\nvar renderjs\nimport script from \"./paidui.vue?vue&type=script&lang=js&\"\nexport * from \"./paidui.vue?vue&type=script&lang=js&\"\nimport style0 from \"./paidui.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/paidui/paidui.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./paidui.vue?vue&type=template&id=b09d72aa&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"排队中\") : null\n  var m2 = _vm.isload ? _vm.t(\"已完成\") : null\n  var m3 = _vm.isload ? _vm.t(\"商品\") : null\n  var m4 = _vm.isload ? _vm.t(\"门店\") : null\n  var m5 = _vm.isload ? _vm.t(\"买单\") : null\n  var m6 = _vm.isload ? _vm.t(\"独立排队\") : null\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = item.status == 0 ? _vm.t(\"color1\") : null\n          var m8 = item.status == 1 ? _vm.t(\"color1\") : null\n          var m9 = item.status == 2 ? _vm.t(\"color1\") : null\n          var m10 = item.name ? _vm.timestampToDate(item.createtime) : null\n          var g1 = Number(item.bili).toFixed(0)\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n            m9: m9,\n            m10: m10,\n            g1: g1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./paidui.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./paidui.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"banner\" :style=\"{background:t('color1')}\">\n\t\t\t<image :src=\"userinfo.headimg\"/>\n\t\t\t<view class=\"info\" style=\"line-height: 120rpx;padding-top: 0;\">\n\t\t\t\t <text class=\"nickname\" style=\"color: azure;\">{{userinfo.nickname}}</text>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view>\n\t\t\t<view class=\"contentdata\">\n\t\t\t\t<view class=\"order\">\n\t\t\t\t\t<view class=\"head\">\n\t\t\t\t\t\t<text class=\"f1\">排队数据看板</text>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"topay\">\n\t\t\t\t\t\t\t<text>红包转至余额</text>\n\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f3\" @tap=\"showDataFixMenu\">\n\t\t\t\t\t\t\t<text>数据修复</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"content2\">\n\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.yingde}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">应补贴红包</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.leiji}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">合计补贴红包</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t<!-- \t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.daijiang}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">待补贴金额</text>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.zailushang}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">在路上</text>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"content2\">\n\t\t\t\t\t<!-- \t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.zailushang}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">在路上</text>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.daizhuan}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">可转至余额</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">{{userinfo.zhuanhua}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">已转至余额</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">￥{{userinfo.quandaijiang}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">平台排队金额</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item\"  style=\"border: none;\">\n\t\t\t\t\t\t\t<text class=\"t1\">{{userinfo.quanorder}}</text>\n\t\t\t\t\t\t\t<text class=\"t3\">排队订单数</text>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t</view>\n\t\t\t\t<!-- \t<view class=\"content\">\n\t\t\t\t\t\n\t\t\t\t\t</view> -->\n\t\t\t\t\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"container3\" v-if=\"set.endtime.remark != 0\">\n\t\t\t  <!--  <view class=\"header\">买单</view> -->\n\t\t\t  <view class=\"card\">\n\t\t\t    <view class=\"content23\">\n\t\t\t      <text class=\"discount-text\">\n\t\t\t        活动结束时间:\n\t\t\t        <text class=\"highlight\">{{set.endtime.endtime}}</text>\n\t\t\t        <text class=\"question-icon\" @tap=\"showExplanation\">?</text>\n\t\t\t      </text>\n\t\t\t      <!--  <text class=\"sub-text\">先付后返</text> -->\n\t\t\t    </view>\n\t\t\t    <!-- <button class=\"pay-button\"  :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"'/pagesExt/maidan/pay?bid='\">点击买单</button> -->\n\t\t\t  </view>\n\t\t\t\n\t\t\t  <!-- Explanation Modal -->\n\t\t\t  <view v-if=\"showModal\" class=\"modal\" @tap=\"closeExplanation\">\n\t\t\t    <view class=\"modal-content\" @tap.stop>\n\t\t\t      <text class=\"close\" @tap=\"closeExplanation\">&times;</text>\n\t\t\t      <text>{{set.endtime.remark}}</text>\n\t\t\t    </view>\n\t\t\t  </view>\n\t\t\t</view>\n\t\t<dd-tab :itemdata=\"[t('排队中'),t('已完成')]\" :itemst=\"['1','2']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" ></dd-tab>\n\t\t<dd-tab :itemdata=\"[t('商品'),t('门店'),t('买单'),t('独立排队')]\" :itemst=\"['1','2','3','4']\" :st=\"st1\" :isfixed=\"false\" @changetab=\"changetab2\"></dd-tab>\n\t\t<view class=\"order-content\">\n\t\t\t\n\t\t\t<block  v-if=\"datalist && datalist.length>0\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content\">\n\t\t\t\t<view class=\"order-box\">\n\t\t\t\t\t<!-- <text class=\"t1\" style=\"font-weight:bold;\">奖励队列：{{item.id}}</text>\n\t\t\t\t\t<text class=\"t1\" style=\"font-weight:bold;\" v-if=\"st1==4\">商家排队队列数量：{{item.duilicount}}</text> -->\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"f1\" ><image src=\"/static/img/ico-shop.png\"></image>{{item.bunessname}}</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\n\t\t\t\t\t<text  class=\"st4\">\n\t\t\t\t\t\t<block  v-if=\"item.status==0\"><text class=\"t1\"  :style=\"{color:t('color1')}\">{{item.statusname}}</text></block>\n\t\t\t\t\t\t<block  v-if=\"item.status==1\"><text class=\"t1\" :style=\"{color:t('color1')}\">{{item.statusname}}</text></block>\n\t\t\t\t\t\t<block  v-if=\"item.status==2\"><text class=\"t1\" :style=\"{color:t('color1')}\">{{item.statusname}}</text></block>\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<block >\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<!-- :data-url=\"'/shopPackage/shop/product?id=' + item.product.id\" -->\n\t\t\t\t\t\t<view @tap.stop=\"goto\"  :data-url=\"'/shopPackage/shop/product?id='+item.proid\"  v-if=\"item.qu==0\">\n\t\t\t\t\t\t\t<image  :src=\"item.pic\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.qu==1\">\n\t\t\t\t\t\t\t<image  :src=\"item.pic\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.name\" class=\"detail\">\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t\t\t<text class=\"t2\"  @tap.stop=\"goto\"  :data-url=\"'/pagesExt/order/detail?id='+item.orderid\">排队时间：{{timestampToDate(item.createtime)}}</text>\n\t\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.totalprice}}</text>\n\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item.num}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<!-- <view class=\"bottom\">\n\t\t\t\t\t<view class=\"l\">\n\t\t\t\t\t\t<text class=\"t1\">分红进度：{{item.progress_percent}}%</text>\n\t\t\t\t\t\t<text class=\"t2\">目标金额：￥{{item.paidui_jiang}}</text>\n\t\t\t\t\t\t<text class=\"t2\">已获得：￥{{item.shiji_dedao}}</text>\n\t\t\t\t\t\t<text class=\"t2\">剩余：￥{{item.remaining_amount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"r\">\n\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"t1\">前面还有{{item.qiancount}}人</text>\n\t\t\t\t\t\t<text v-if=\"item.status==1\"  class=\"t1\">已免单￥{{item.shiji_dedao}}</text>\n\t\t\t\t\t\t<text v-if=\"item.status==2\"  class=\"t1\">已完成</text>\n\t\t\t\t\t\t<text v-if=\"item.status==3\"  class=\"t1\">已取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t\n\t\t\t\t<!-- 分红流向信息 -->\n\t\t\t\t<!-- <view class=\"distribution-info\" v-if=\"item.distribution_info && item.distribution_info.length > 0\">\n\t\t\t\t\t<view class=\"distribution-header\">\n\t\t\t\t\t\t<text class=\"distribution-title\">💰 分红流向</text>\n\t\t\t\t\t\t<text class=\"distribution-count\">共{{item.distribution_info.length}}笔收益</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"distribution-list\">\n\t\t\t\t\t\t<view v-for=\"(dist, distIndex) in item.distribution_info\" :key=\"distIndex\" class=\"distribution-item\">\n\t\t\t\t\t\t\t<view class=\"distribution-amount\">+￥{{dist.amount}}</view>\n\t\t\t\t\t\t\t<view class=\"distribution-details\">\n\t\t\t\t\t\t\t\t<text class=\"distribution-time\">{{dist.time}}</text>\n\t\t\t\t\t\t\t\t<text class=\"distribution-type\">{{dist.type}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"distribution-more\" @tap=\"showDistributionFlow(item)\">\n\t\t\t\t\t\t<text>查看详细流向 →</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t\n\t\t\t\t<!-- 分红来源信息 -->\n\t\t\t<!-- \t<view class=\"distribution-source\" v-if=\"item.distribution_source && item.distribution_source.length > 0\">\n\t\t\t\t\t<view class=\"source-header\">\n\t\t\t\t\t\t<text class=\"source-title\">📈 分红来源</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"source-list\">\n\t\t\t\t\t\t<view v-for=\"(source, sourceIndex) in item.distribution_source\" :key=\"sourceIndex\" class=\"source-item\">\n\t\t\t\t\t\t\t<view class=\"source-amount\">+￥{{source.amount}}</view>\n\t\t\t\t\t\t\t<view class=\"source-details\">\n\t\t\t\t\t\t\t\t<text class=\"source-time\">{{source.time}}</text>\n\t\t\t\t\t\t\t\t<text class=\"source-type\">{{source.source_type}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t\t\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<view class=\"info-item\"  style=\"border: none;\">\n\t\t\t\t\t\t<text class=\"amount\">{{Number(item.bili).toFixed(0)}}%</text>\n\t\t\t\t\t\t<text class=\"label\">补贴比例</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\"  style=\"border: none;\">\n\t\t\t\t\t\t<text class=\"amount\">￥{{item.paidui_jiang}}</text>\n\t\t\t\t\t\t<text class=\"label\">应补贴红包</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-item\"  style=\"border: none;\">\n\t\t\t\t\t\t<text class=\"amount\">￥{{item.shiji_dedao}}</text>\n\t\t\t\t\t\t<text class=\"label\">已补贴红包</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"footer\" v-if=\"item.status==0\">\n\t\t\t\t  <text class=\"date pw\">目前排位：{{item.qiancount}}</text>\n\t\t\t\t  <text class=\"action\" @click=\"showModal2\" v-if=\" userinfo.duijifen == 1 || userinfo.duiyue == 1 || userinfo.duichoujiang == 1\">\n\t\t\t\t\t  结束排队，领取补贴 &gt;</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view v-if=\"modalVisible\" class=\"modal-overlay\">\n\t\t\t\t  <view class=\"modal-content\">\n\t\t\t\t    <view v-if=\"!selectedOption\">\n\t\t\t\t      <text class=\"modal-title\">选择结束排队的原因</text>\n\t\t\t\t      <view class=\"option\" v-for=\"option in options\" :key=\"option.value\" @click=\"selectOption2(option,item.id,option.value,item.butieyue,item.butiejifen,item.butieduijiang)\">\n\t\t\t\t        <text>{{ option.text }}</text>\n\t\t\t\t      </view>\n\t\t\t\t    </view>\n\t\t\t\t    <view v-if=\"selectedOption\" class=\"modal-info\">\n\t\t\t\t\t\t  <text class=\"modal-title\">结束排队，领取补贴</text>\n\t\t\t\t\t    <text class=\"modal-amount\">{{ selectedOption.amount }}</text>\n\t\t\t\t\t    <view class=\"modal-text\">\n\t\t\t\t\t      <text>{{ selectedOption.description }}</text>\n\t\t\t\t\t    </view>\n\t\t\t\t\t    <button class=\"modal-button confirm\" @click=\"confirmEndQueue(item.id)\">确认结束领取补贴</button>\n\t\t\t\t\t    <button class=\"modal-button back\" @click=\"backToOptions\">返回选择</button>\n\t\t\t\t    </view>\n\t\t\t\t    <button class=\"modal-button continue\" @click=\"hideModal\">继续排队等待补贴 &gt;</button>\n\t\t\t\t  </view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"op\" v-if=\"st1==4\">\n\t\t\t\t\t<text class=\"t1\" style=\"font-weight:bold;\" >商家排队队列数量：{{item.duilicount}}</text>\n\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tpageinfo: [],\n\t\t\tloading:false,\n      isload: false,\n\t   modalVisible: false,\n\t      selectedOption: null,\n\t\t\tmenuindex:-1,\n\t\t\t showModal: false,\n      nodata: false,\n      nomore: false,\n      datalist: [],\n\t\t\ttextset:{},\n      pagenum: 1,\n\t  userinfo: [],\n\t  set:[],\n\t  st: 1,\n\t  st1: 1,\n\t  primary_color : '',\n\t   options: [],\n\t  secondary_color : '',\n\t  xuanze:'',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || 1;\n\t\tvar that = this;\n\t\tthis.getdata();\n\t\tthis.primary_color = app.getCache('primary_color')\n\t\tthis.secondary_color = app.getCache('secondary_color')\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n\t  numFilter (value) {\n\t  // 截取当前数据到小数点后两位\n\t  return parseFloat(value).toFixed(2);\n\t  },\n\ttimestampToDate(timestamp) {\n\t    const date = new Date(timestamp*1000); // 如果timestamp是数值，直接使用；如果是字符串，确保是数值字符串\n\t    const year = date.getFullYear();\n\t    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n\t    const day = date.getDate().toString().padStart(2, '0');\n\t    const hours = date.getHours().toString().padStart(2, '0');\n\t    const minutes = date.getMinutes().toString().padStart(2, '0');\n\t    const seconds = date.getSeconds().toString().padStart(2, '0');\n\t    return `${year}-${month}-${day} ${hours}:${minutes}`;\n\t  },\n    getdata: function (loadmore) {\n\t\t\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\n\t  var st1 = that.st1;\n      var pagenum = that.pagenum;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n      app.post('ApiPaidui/paidui', {st: st,st1: st1,pagenum: pagenum}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n\t\tthat.userinfo = res.userinfo;\n\t\tthat.set = res.set;\n\t\tvar options2=[];\n\t\tif(that.set.paidui_duihuan_money >0)\n\t\t{\t\n\t\t\tvar list= {value: 'reason1', text: '余额', amount: '¥ 0.05', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};\n\t\t\toptions2.push(list)\n\t\t}\n\t\tif(that.set.paidui_duihuan >0)\n\t\t{\n\t\t\tvar list={value: 'reason2', text: '积分', amount: '100积分', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};\n\t\t\toptions2.push(list)\n\t\t}\t\n\t\tif(that.set.paidui_duihuan_choujiang != '')\n\t\t{\n\t\t\tvar list= {value: 'reason3', text: '抽奖', amount: '1次', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};\n\t\t\toptions2.push(list)\n\t\t}\n\t\tthat.options  = options2;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('排队补贴')\n\t\t\t\t\t});\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n\t\tconsole.log('tg',that.datalist);\n      });\n    },\n\tshowExplanation() {\n\t      this.showModal = true;\n\t    },\n\t    closeExplanation() {\n\t      this.showModal = false;\n\t    },\n\tduihuantixian: function (id) {\n\t  var that = this;\n\t  that.loading = true;\n\t  app.post('ApiPaidui/tixian', {id:id}, function(res) {\n\t  \tthat.loading = false;\n\t  \tthat.getdata();\n\t  \tif (res.status == 0) {\n\t  \t\tapp.error(res.msg);\n\t  \t\treturn;\n\t  \t}else{\n\t  \t\tapp.error('兑换成功');\n\t  \t}\n\t  });\n\t},\n\tshowModal2() {\n\t  this.modalVisible = true;\n\t},\n\thideModal() {\n\t  this.modalVisible = false;\n\t  this.selectedOption = null;\n\t},\n\tselectOption2(option,orderid,xuanze,butie1,butie2,butie3) {\n\t   var that = this;\n\t   that.xuanze = xuanze;\n\t   if( xuanze == 'reason1')\n\t   {\n\t\t   option.amount = '¥'+butie1;\n\t   }else if(xuanze == 'reason2')\n\t   {\n\t\t   option.amount = butie2+'积分'; \n\t   }else if(xuanze == 'reason3')\n\t   {\n\t\t   option.amount = butie3+'次';\n\t   }\n\t   this.selectedOption = option;\n\t},\n\tbackToOptions() {\n\t  this.selectedOption = null;\n\t},\n\tconfirmEndQueue(orderid) {\n\t  this.hideModal();\n\t  var that = this;\n\t  if( that.xuanze == 'reason1')\n\t  {\n\t\t\tthat.duihuantixian2(orderid)\n\t  }else if(that.xuanze == 'reason2')\n\t  {\n\t\t\tthat.duihuantixian(orderid)\n\t  }else if(that.xuanze == 'reason3')\n\t  {\n\t\t\tthat.duihuantixian3(orderid)\n\t  }\n\t  // 处理结束排队逻辑\n\t  \n\t},\n\tduihuantixian2: function (id) {\n\t  var that = this;\n\t  that.loading = true;\n\t  app.post('ApiPaidui/tixian2', {id:id}, function(res) {\n\t  \tthat.loading = false;\n\t  \tthat.getdata();\n\t  \tif (res.status == 0) {\n\t  \t\tapp.error(res.msg);\n\t  \t\treturn;\n\t  \t}else{\n\t  \t\tapp.error('兑换成功');\n\t  \t}\n\t  });\n\t},\n\tduihuantixian3: function (id) {\n\t  var that = this;\n\t  that.loading = true;\n\t  app.post('ApiPaidui/tixian3', {id:id}, function(res) {\n\t  \tthat.loading = false;\n\t  \tthat.getdata();\n\t  \tif (res.status == 0) {\n\t  \t\tapp.error(res.msg);\n\t  \t\treturn;\n\t  \t}else{\n\t\t\tapp.success(res.msg);\n\t\t\tsetTimeout(function () {\n\t\t\t  app.goto(res.data);\n\t\t\t}, 1000);\n\t  \t\t// app.error('兑换成功');\n\t  \t}\n\t  });\n\t},\n\ttopay: function(e) {\n\t\tvar that = this;\n\t\tthat.loading = true;\n\t\tapp.post('ApiPaidui/zhuanhua', {}, function(res) {\n\t\t\tthat.loading = false;\n\t\t\tthat.getdata();\n\t\t\tif (res.status == 0) {\n\t\t\t\tapp.error(res.msg);\n\t\t\t\treturn;\n\t\t\t}else{\n\t\t\t\tapp.error('转化成功');\n\t\t\t}\n\t\t});\n\t},\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n\tchangetab2: function (st1) {\n\t\tthis.st1 = st1;\n\t\tuni.pageScrollTo({\n\t\t\tscrollTop: 0,\n\t\t\tduration: 0\n\t\t});\n\t\tthis.getdata();\n\t},\n\tshowDistributionFlow: function(item) {\n\t\tvar that = this;\n\t\tapp.post('ApiPaidui/paiduiDistributionFlow', {paidui_id: item.id}, function(res) {\n\t\t\tif (res.status == 1) {\n\t\t\t\tthat.showDistributionModal(res.data);\n\t\t\t} else {\n\t\t\t\tapp.error(res.msg || '获取分红流向失败');\n\t\t\t}\n\t\t});\n\t},\n\tshowDistributionModal: function(data) {\n\t\tvar that = this;\n\t\tvar content = '【分红流向详情】\\n\\n';\n\t\tcontent += '排队进度：' + data.progress_info.progress_percent + '%\\n';\n\t\tcontent += '目标金额：￥' + data.progress_info.target_amount + '\\n';\n\t\tcontent += '已获得：￥' + data.progress_info.received_amount + '\\n';\n\t\tcontent += '剩余：￥' + data.progress_info.remaining_amount + '\\n\\n';\n\t\t\n\t\tcontent += '分红记录：\\n';\n\t\tif (data.flow_records && data.flow_records.length > 0) {\n\t\t\tdata.flow_records.forEach(function(record) {\n\t\t\t\tcontent += '• ' + record.time_text + ' +￥' + record.money + '\\n';\n\t\t\t\tcontent += '  来源：' + record.source_type + '\\n';\n\t\t\t});\n\t\t} else {\n\t\t\tcontent += '暂无分红记录\\n';\n\t\t}\n\t\t\n\t\tuni.showModal({\n\t\t\ttitle: '分红流向详情',\n\t\t\tcontent: content,\n\t\t\tshowCancel: false,\n\t\t\tconfirmText: '知道了'\n\t\t});\n\t},\n\t\n\t// 检查数据一致性\n\tcheckDataConsistency: function() {\n\t\tvar that = this;\n\t\tthat.loading = true;\n\t\tapp.post('ApiPaidui/checkDataConsistency', {}, function(res) {\n\t\t\tthat.loading = false;\n\t\t\tif (res.status == 1) {\n\t\t\t\tif (res.need_fix) {\n\t\t\t\t\t// 发现数据不一致，询问是否修复\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '数据不一致',\n\t\t\t\t\t\tcontent: res.msg + '\\n\\n是否立即修复？',\n\t\t\t\t\t\tshowCancel: true,\n\t\t\t\t\t\tconfirmText: '立即修复',\n\t\t\t\t\t\tcancelText: '稍后处理',\n\t\t\t\t\t\tsuccess: function(modalRes) {\n\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\tthat.syncPaiduiData();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '数据一致性正常',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tapp.error(res.msg || '检查失败');\n\t\t\t}\n\t\t}, function() {\n\t\t\tthat.loading = false;\n\t\t\tapp.error('网络请求失败');\n\t\t});\n\t},\n\t\n\t// 数据修复\n\tsyncPaiduiData: function() {\n\t\tvar that = this;\n\t\tthat.loading = true;\n\t\tapp.post('ApiPaidui/syncPaiduiData', {}, function(res) {\n\t\t\tthat.loading = false;\n\t\t\tif (res.status == 1) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '修复完成',\n\t\t\t\t\tcontent: res.msg,\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t// 刷新页面数据\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tapp.error(res.msg || '修复失败');\n\t\t\t}\n\t\t}, function() {\n\t\t\tthat.loading = false;\n\t\t\tapp.error('网络请求失败');\n\t\t});\n\t},\n\t\n\t// 显示数据修复菜单\n\tshowDataFixMenu: function() {\n\t\tvar that = this;\n\t\tuni.showActionSheet({\n\t\t\titemList: ['检查数据一致性', '修复数据', '取消'],\n\t\t\tsuccess: function(res) {\n\t\t\t\tif (res.tapIndex == 0) {\n\t\t\t\t\tthat.checkDataConsistency();\n\t\t\t\t} else if (res.tapIndex == 1) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '确认修复',\n\t\t\t\t\t\tcontent: '此操作将修复分红记录与排队记录的数据不一致问题，是否继续？',\n\t\t\t\t\t\tshowCancel: true,\n\t\t\t\t\t\tconfirmText: '确认修复',\n\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\tsuccess: function(modalRes) {\n\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\tthat.syncPaiduiData();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n  }\n};\n</script>\n<style>\n.container3 {\n  padding: 16px;\n  width: 95%;\n  background-color: #f5f5f5;\n  \n}\n\n.container3 {\n  padding: 10px;\n  width: 100%;\n  background-color: #f5f5f5;\n  margin-top: -10px;\n}\n\n.header {\n  font-size: 18px;\n  font-weight: bold;\n  padding-bottom: 16px;\n}\n\n.card {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background-color: #ffffff;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.content23 {\n  display: flex;\n  flex-direction: column;\n}\n\n.discount-text {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.highlight {\n  color: #ff0000;\n}\n\n.sub-text {\n  font-size: 14px;\n  color: #666666;\n  margin-top: 8px;\n}\n\n.pay-button {\n  background-color: #ff4d4f;\n  color: #ffffff;\n  border: none;\n  border-radius: 10px;\n  padding: 4px 16px;\n  font-size: 14px;\n  cursor: pointer;\n  margin-left: auto;\n  margin-right: -10px;\n}\n.content2{ width:94%;margin:20rpx 0%;}\n.content .item{width:100%;background:#fff;margin:20rpx 0;padding:40rpx 30rpx;border-radius:8px;display:flex;align-items:center}\n.content .item:last-child{border:0}\n.content .item .f1{width:500rpx;display:flex;flex-direction:column}\n.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}\n.content .item .f1 .t2{color:#666666;font-size:24rpx;margin-top:10rpx}\n.content .item .f1 .t3{color:#666666;font-size:24rpx;margin-top:10rpx}\n.content .item .f2{ flex:1;width:200rpx;font-size:36rpx;text-align:right}\n.content .item .f2 .t1{color:#03bc01}\n.content .item .f2 .t2{color:#000000}\n.content .item .f3{ flex:1;width:200rpx;font-size:32rpx;text-align:right}\n.content .item .f3 .t1{color:#03bc01}\n.content .item .f3 .t2{color:#000000}\n.banner{ display:flex;width:100%;height:560rpx;padding:40rpx 32rpx;color:#fff;position:relative}\n.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}\n.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}\n.banner .info .nickname{font-size:32rpx;padding-bottom:12rpx;color:black;}\n.banner .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}\n.banner .set image{width:50rpx;height:50rpx;border-radius:0}\n\n.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-380rpx;position:relative;margin-bottom:20rpx}\n\n\n.order3{width:100%;background:#fff;padding:0 20rpx;margin-top:-60rpx;border-radius:16rpx }\n.order3 .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}\n.order3 .head .f1{flex:auto;color:#333}\n.order3 .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\n.order3 .head .f2 image{ width:30rpx;height:30rpx;}\n.order3 .head .t3{ width: 40rpx; height: 40rpx;}\n.order3 .content{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}\n.order3 .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\n.order3 .content .item image{ width:50rpx;height:50rpx}\n.order3 .content .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}\n.order3 .content .item .t3{ padding-top:3px;color:#666}\n.order3 .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}\n\n\n\n.order{width:100%;background:#fff;padding:0 20rpx;margin-top:20rpx;border-radius:16rpx }\n.order .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}\n.order .head .f1{flex:auto;color:#333}\n.order .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\n.order .head .f2 image{ width:30rpx;height:30rpx;}\n.order .head .f3{ display:flex;align-items:center;color:#666;width:120rpx;padding:10rpx 0;text-align:right;justify-content:flex-end;font-size:24rpx}\n.order .head .t3{ width: 40rpx; height: 40rpx;}\n.order .content{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}\n.order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\n.order .content .item image{ width:50rpx;height:50rpx}\n.order .content .item .t1{color:#FE2B2E; font-size:36rpx;font-weight:bold;}\n.order .content .item .t3{ padding-top:3px;color:#666}\n.order .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}\n\n\n.order .content2{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}\n.order .content2 .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\n.order .content2 .item image{ width:50rpx;height:50rpx}\n.order .content2 .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}\n.order .content2 .item .t3{ padding-top:3px;color:#666}\n.order .content2 .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}\n\n.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}\n.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}\n.list .item:last-child{border-bottom:0;}\n.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}\n.list .f1 image{ width:40rpx;height:40rpx;}\n.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\n.list .f2{color:#222}\n.list .f3{ color: #FC5648;text-align:right;flex:1;}\n.list .f4{ width: 24rpx; height: 24rpx;}\n.topsearch{width:94%;margin:16rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n\n.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}\n.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}\n.content .label .t1{flex:1}\n.content .label .t2{ width:300rpx;text-align:right}\n\n.content .item{width: 100%;padding: 32rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}\n.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}\n.content .item .f1{display:flex;flex:1;align-items:center;}\n.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}\n.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}\n.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}\n\n.content .item .f2{display:flex;flex-direction:column;width:400rpx;text-align:right;border-left:1px solid #eee}\n.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}\n.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}\n.content .item .f2 .t3{ display:flex;justify-content:space-around;margin-top:10rpx; flex-wrap: wrap;}\n.content .item .f2 .t3 .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-top: 10rpx;}\n\n\t.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}\n\t.sheet-item .item-img {width: 44rpx;height: 44rpx;}\n\t.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}\n\t.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}\n\t.man-btn {\n\t\tline-height: 100rpx;\n\t\ttext-align: center;\n\t\tbackground: #FFFFFF;\n\t\tfont-size: 30rpx;\n\t\tcolor: #FF4015;\n\t}\n.data-empty{background:#fff}\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#333}\n.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.order-box .content .detail .x1{ flex:1}\n.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n\n.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\n.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n.question-icon {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  line-height: 20px;\n  text-align: center;\n  border-radius: 50%;\n  background-color: #d3d3d3; /* 浅灰色背景 */\n  color: #000; /* 问号颜色 */\n  margin-left: 5px;\n  font-weight: bold;\n  cursor: pointer;\n  position: relative;\n  top: -2px; /* 调整问号图标位置 */\n}\n.modal {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: fixed;\n  z-index: 1;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.4);\n}\n.modal-content {\n  background-color: #fefefe;\n  padding: 20px;\n  border: 1px solid #888;\n  width: 80%;\n}\n.close {\n  color: #aaa;\n  float: right;\n  font-size: 28px;\n  font-weight: bold;\n}\n.close:hover,\n.close:focus {\n  color: black;\n  text-decoration: none;\n  cursor: pointer;\n}  /* 其他样式省略 */\n  \n  .footer {\n    display: flex;\n    justify-content: space-between;\n    padding: 15px;\n    background-color: white;\n    margin-top: 10px;\n    border-top: 1px solid #e5e5e5;\n  }\n  \n  .date {\n    font-size: 14px;\n    color: #999;\n  }\n  \n  .action {\n    font-size: 14px;\n    color: #d92c1e;\n    cursor: pointer;\n  }\n  \n  .floating-button {\n    position: fixed;\n    bottom: 10px;\n    right: 10px;\n    background-color: #666;\n    color: white;\n    padding: 10px 20px;\n    border-radius: 50px;\n    font-size: 14px;\n  }\n  \n  .modal-overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.5);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  \n  .modal-content {\n    width: 80%;\n    background-color: white;\n    border-radius: 10px;\n    padding: 20px;\n    text-align: center;\n  }\n  \n  .modal-title {\n    font-size: 18px;\n    font-weight: bold;\n    margin-bottom: 10px;\n  }\n  \n  .modal-info {\n    margin-top: 20px;\n  }\n  \n  .modal-amount {\n    font-size: 24px;\n    font-weight: bold;\n    color: #d92c1e;\n    margin-bottom: 20px;\n  }\n  \n  .modal-text {\n    font-size: 14px;\n    color: #666;\n    text-align: left;\n    margin-bottom: 20px;\n  }\n  \n  .modal-button {\n    display: block;\n    width: 100%;\n    padding: 10px;\n    margin-top: 10px;\n    border: none;\n    border-radius: 5px;\n    font-size: 16px;\n    cursor: pointer;\n  }\n  \n  .confirm {\n    background-color: #d92c1e;\n    color: white;\n  }\n  \n  .back {\n    background-color: #f5f5f5;\n    color: #333;\n  }\n  \n  .option {\n    padding: 10px;\n    background-color: #f5f5f5;\n    margin-bottom: 10px;\n    cursor: pointer;\n  }\n  \n  .option:hover {\n    background-color: #e5e5e5;\n  }\n\n\t\n\t.info-row {\n\t  display: flex;\n\t  justify-content: space-around;\n\t}\n\t\n\t.info-item {\n\t  display: flex;\n\t  flex-direction: column;\n\t  align-items: center;\n\t  text-align: center;\n\t}\n\t\n\t.amount {\n\t  font-size: 40rpx;\n\t  color: #d92c1e;\n\t  font-weight: bold;\n\t}\n\t\n\t.pw {\n\t  color: #d92c1e;\n\t  font-weight: bold;\n\t}\n\n/* 分红流向信息样式 */\n.distribution-info {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 15px;\n  margin-top: 15px;\n}\n\n.distribution-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.distribution-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n\n.distribution-count {\n  font-size: 12px;\n  color: #666;\n}\n\n.distribution-list {\n  margin-bottom: 12px;\n}\n\n.distribution-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.distribution-item:last-child {\n  border-bottom: none;\n}\n\n.distribution-amount {\n  font-size: 16px;\n  font-weight: bold;\n  color: #52c41a;\n}\n\n.distribution-details {\n  text-align: right;\n}\n\n.distribution-time {\n  font-size: 12px;\n  color: #666;\n  display: block;\n}\n\n.distribution-type {\n  font-size: 12px;\n  color: #1890ff;\n  display: block;\n}\n\n.distribution-more {\n  text-align: center;\n  padding: 8px;\n  background: #fff;\n  border-radius: 4px;\n  color: #1890ff;\n  font-size: 14px;\n}\n\n/* 分红来源信息样式 */\n.distribution-source {\n  background: #fff7e6;\n  border-radius: 8px;\n  padding: 15px;\n  margin-top: 10px;\n}\n\n.source-header {\n  margin-bottom: 12px;\n}\n\n.source-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n\n.source-list {\n  margin-bottom: 8px;\n}\n\n.source-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 0;\n  border-bottom: 1px solid #ffe7ba;\n}\n\n.source-item:last-child {\n  border-bottom: none;\n}\n\n.source-amount {\n  font-size: 14px;\n  font-weight: bold;\n  color: #fa8c16;\n}\n\n.source-details {\n  text-align: right;\n}\n\n.source-time {\n  font-size: 12px;\n  color: #666;\n  display: block;\n}\n\n.source-type {\n  font-size: 12px;\n  color: #fa8c16;\n  display: block;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./paidui.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./paidui.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103113\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}