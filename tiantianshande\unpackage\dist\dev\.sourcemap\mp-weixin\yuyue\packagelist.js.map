{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?97e9", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?ddc6", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?3cb2", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?f61d", "uni-app:///yuyue/packagelist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?978e", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagelist.vue?2fb7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "packageList", "page", "limit", "total", "loading", "nodata", "onLoad", "uni", "title", "onReachBottom", "methods", "getList", "that", "app", "console", "item", "gotoDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6BpxB;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;MACA;MACAC;MACAC;QACAZ;QACAC;QACA;MACA;QACAU;QACA;UACA;YACA;YACA;cACAE;cACAC;YACA;YACA;UACA;UACA;YACAH;UACA;YACAA;UACA;UACAA;UACA;UACA;YACAA;UACA;QACA;UACAC;UACA;YACAD;UACA;QACA;MACA;QACAA;QACA;UACAA;QACA;;QACAC;MACA;IACA;IACA;IACAG;MACA;MACAH;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packagelist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packagelist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packagelist.vue?vue&type=template&id=01020338&\"\nvar renderjs\nimport script from \"./packagelist.vue?vue&type=script&lang=js&\"\nexport * from \"./packagelist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packagelist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packagelist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagelist.vue?vue&type=template&id=01020338&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1\")\n  var g0 = !_vm.loading && _vm.packageList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagelist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagelist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"package-list\">\r\n\t\t\t<view class=\"package-item\" v-for=\"(item, index) in packageList\" :key=\"index\" @tap=\"gotoDetail(item.id)\">\r\n\t\t\t\t<image class=\"package-pic\" :src=\"item.pic\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"package-info\">\r\n\t\t\t\t\t<view class=\"package-name\">{{ item.name }}</view>\r\n\t\t\t\t\t<view class=\"package-desc\">\r\n\t\t\t\t\t\t<text>含 {{ item.service_count || 0 }} 项服务</text>\r\n\t\t\t\t\t\t<!-- 可以根据需要添加更多描述信息，如有效期等 -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"package-bottom\">\r\n\t\t\t\t\t\t<view class=\"package-price\" :style=\"{color:t('color1')}\">￥<text class=\"price-value\">{{ item.sell_price }}</text></view>\r\n\t\t\t\t\t\t<button class=\"buy-btn\" :style=\"{background:t('color1')}\">立即抢购</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"loading-tip\" v-if=\"loading\">加载中...</view>\r\n\t\t<view class=\"nodata-tip\" v-if=\"nodata\">没有更多套餐了~</view>\r\n\t\t<view class=\"empty-tip\" v-if=\"!loading && packageList.length === 0\">\r\n\t\t\t<image src=\"/static/img/nodata.png\" class=\"empty-img\" mode=\"widthFix\"></image>\r\n\t\t\t<text>暂无服务套餐</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tpackageList: [], // 套餐列表\r\n\t\t\tpage: 1,         // 当前页码\r\n\t\t\tlimit: 10,       // 每页数量\r\n\t\t\ttotal: 0,        // 总条数\r\n\t\t\tloading: false,  // 加载状态\r\n\t\t\tnodata: false    // 是否没有更多数据\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 获取 globalData\r\n\t\t// const app = getApp();\r\n\t\t// if(app.globalData.config && app.globalData.config.t) {\r\n\t\t//     // this.t = app.globalData.config.t; // 移除赋值\r\n\t\t// } else {\r\n\t\t//     console.warn('onLoad 时 globalData.config.t 未准备好');\r\n\t\t// }\r\n\r\n\t\tthis.getList(); // 页面加载时获取第一页数据\r\n\t\tuni.setNavigationBarTitle({\r\n\t\t\ttitle: '服务套餐'\r\n\t\t});\r\n\t},\r\n\tonReachBottom() {\r\n\t\t// 页面滚动到底部时加载更多\r\n\t\tif (this.loading || this.nodata) {\r\n\t\t\treturn; // 如果正在加载或没有更多数据，则不执行\r\n\t\t}\r\n\t\tthis.page++;\r\n\t\tthis.getList();\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取套餐列表\r\n\t\tgetList() {\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.loading || that.nodata) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiYuyuePackage/getList', {\r\n\t\t\t\tpage: that.page,\r\n\t\t\t\tlimit: that.limit\r\n\t\t\t\t// bid: xxx // 如果需要按商家筛选，传入bid\r\n\t\t\t}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tconst data = (res.data || []).map(item => {\r\n\t\t\t\t\t\t// 清理错误的图片URL前缀\r\n\t\t\t\t\t\tif (item.pic && item.pic.startsWith('https://localhost')) {\r\n\t\t\t\t\t\t\tconsole.warn('发现错误的图片前缀，已清理:', item.pic);\r\n\t\t\t\t\t\t\titem.pic = item.pic.substring('https://localhost'.length);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn item;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (that.page === 1) {\r\n\t\t\t\t\t\tthat.packageList = data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.packageList = that.packageList.concat(data);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.total = res.total || 0;\r\n\t\t\t\t\t// 判断是否没有更多数据\r\n\t\t\t\t\tif (data.length < that.limit || that.packageList.length >= that.total) {\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg || '获取套餐列表失败');\r\n\t\t\t\t\tif (that.page > 1) {\r\n\t\t\t\t\t\tthat.page--; // 加载失败，页码回退\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}, function() {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (that.page > 1) {\r\n\t\t\t\t\tthat.page--; // 请求失败，页码回退\r\n\t\t\t\t}\r\n\t\t\t\tapp.error('请求失败');\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 跳转到套餐详情页\r\n\t\tgotoDetail(id) {\r\n\t\t\tif (!id) return;\r\n\t\t\tapp.goto('/yuyue/packagedetail?id=' + id);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.package-list {\r\n\t/* 列表容器样式 */\r\n}\r\n\r\n.package-item {\r\n\tdisplay: flex;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\toverflow: hidden;\r\n}\r\n\r\n.package-pic {\r\n\twidth: 180rpx;\r\n\theight: 180rpx;\r\n\tborder-radius: 12rpx;\r\n\tmargin-right: 20rpx;\r\n\tflex-shrink: 0; /* 防止图片被压缩 */\r\n}\r\n\r\n.package-info {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n\toverflow: hidden; /* 防止内容溢出 */\r\n}\r\n\r\n.package-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.package-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 10rpx;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n.package-bottom {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-end;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.package-price {\r\n\tfont-size: 24rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.package-price .price-value {\r\n\tfont-size: 36rpx;\r\n\tmargin-left: 4rpx;\r\n}\r\n\r\n.buy-btn {\r\n\tpadding: 0 24rpx;\r\n\theight: 56rpx;\r\n\tline-height: 56rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #fff;\r\n\tborder-radius: 28rpx;\r\n\ttext-align: center;\r\n\t/* 移除默认按钮边框 */\r\n\tborder: none;\r\n\toutline: none;\r\n\tmargin: 0; /* 移除按钮默认外边距 */\r\n}\r\n/* H5平台需要额外处理按钮边框 */\r\nbutton::after {\r\n\tborder: none;\r\n}\r\n\r\n/* 加载和无数据提示 */\r\n.loading-tip, .nodata-tip, .empty-tip {\r\n\ttext-align: center;\r\n\tcolor: #999;\r\n\tfont-size: 24rpx;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.empty-tip {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding-top: 100rpx;\r\n}\r\n\r\n.empty-img {\r\n\twidth: 200rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagelist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagelist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041171\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}