{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?c815", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?f42e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?1a97", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?4e56", "uni-app:///pagesExa/tuanzhang/buydialog/buydialog.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?fb89", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buydialog/buydialog.vue?6ade"], "names": ["data", "ks", "product", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "<PERSON><PERSON><PERSON><PERSON>", "jlprice", "jltitle", "gwcnum", "isload", "loading", "canaddcart", "shopset", "glassrecord", "showglass", "totalprice", "jlselected", "props", "btntype", "default", "menuindex", "controller", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proid", "tid", "bid", "type", "mounted", "uni", "that", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getdata", "console", "app", "tt", "promotionId", "id", "buydialogChange", "getglassrecord", "pagenum", "listrow", "showLinkChange", "ggchange", "jlchange", "title", "jlselect", "tobuy", "prodata", "addcart", "glass_record_id", "ggid", "num", "gwcplus", "gwcminus", "gwcinput", "hidePriceLink"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2GlxB;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;MAAAH;IAAA;IACAI;IACAC;MAAAL;IAAA;IACAM;MACAC;MACAP;IACA;EACA;EACAQ;IACA;IACAC;MACAC;IACA;IACAA;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MACA;MACAC;MACA;QACAC;QACAA;UAAAX;QAAA;UACAW;UACA;YACAC;cAAAC;YAAA;UACA;YACAF;UACA;QACA;QACA;MACA;MAEAL;MACAK;QAAAG;QAAAZ;MAAA;QACAQ;UACAI;UACAZ;QACA;QACAI;QACAA;QACAA;QACA;UACAA;QACA;QAEAA;QACAA;QACA;QACA;QACA;UACA1B;QACA;QACA0B;QACAA;QACAA;QACA,mCACAA,6CAEAA;QACAA;QACA;UAAA;UACAA;QACA;QACA;QACA;UACAA;UACAA;QACA;QACA;UAEAA;UACAA;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;MACA;MACA;QACAL;UAAAM;UAAAC;UAAAJ;QAAA;UACA;UACA;YACAR;UACA;QACA;MACA;IACA;IACAa;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAxC;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MAAA;IACA;IACAyC;MAEA;MACA;MACA;MACA;MACA;QACA;UACAtC;UACAuC;UACAC;QACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAb;QACA;MACA;MACA;MACA;QACA;QACAc;MACA;MACA;MACA;QACAd;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;IACA;IACA;IACAe;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAf;QACA;MACA;MACA;MACA;QACAgB;MACA;MACA;QACAhB;UAAAV;UAAAD;UAAA4B;UAAAC;UAAAF;QAAA;UACA;YACAhB;UACA;YACAA;UACA;QACA;MACA;MAEA;QAAAX;QAAA4B;QAAAC;QAAA9C;QAAAC;MAAA;MACA;IACA;IACA;IACA8C;MACA;MACA;MACA;QACAnB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;IACA;IACA;IACAoB;MACA;MACA;MACA;QACA;UACA;YACApB;UACA;UACA;QACA;MACA;QACA;UACA;YACAA;UACA;UACA;QACA;MACA;MAEA;IACA;IACA;IACAqB;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;YACArB;UACA;UACA1B;QACA;MACA;QACA;UACA;YACA0B;UACA;UACA1B;QACA;MAEA;MACA;QACA0B;QACA1B;MACA;MAEA;IACA;IACAgD;MACAtB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChZA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhang/buydialog/buydialog.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./buydialog.vue?vue&type=template&id=b5f75736&\"\nvar renderjs\nimport script from \"./buydialog.vue?vue&type=script&lang=js&\"\nexport * from \"./buydialog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buydialog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhang/buydialog/buydialog.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buydialog.vue?vue&type=template&id=b5f75736&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    (_vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\")\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    _vm.product.is_member_yh == 0 &&\n    _vm.product.is_newcustom == 1 &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload &&\n    !(\n      _vm.controller == \"ApiRestaurantShop\" ||\n      _vm.controller == \"ApiRestaurantTakeaway\"\n    ) &&\n    !(_vm.product.is_member_yh == 0 && _vm.product.is_newcustom == 1) &&\n    (_vm.product.price_type != 1 || _vm.nowguige.sell_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m3 = _vm.isload && _vm.nowguige.balance_price ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload\n    ? _vm.jialiaodata.length > 0 &&\n      (_vm.controller == \"ApiRestaurantShop\" ||\n        _vm.controller == \"ApiRestaurantTakeaway\")\n    : null\n  var m4 = _vm.isload && _vm.product.price_type == 1 ? _vm.t(\"color2\") : null\n  var m5 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    _vm.shopset &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.nowguige.commission > 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m7 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.product.hide_price != 1 &&\n    _vm.btntype == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.product.hide_price != 1 &&\n    _vm.btntype == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m9 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.product.hide_price != 1 &&\n    _vm.btntype == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    !(_vm.product.price_type == 1) &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.product.hide_price == 1\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buydialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buydialog.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view v-if=\"isload\">\r\n\t\t\t<view class=\"buydialog-mask\" @tap=\"buydialogChange\"></view>\r\n\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"image\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<image :src=\"nowguige.pic || product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"nowguige.pic || product.pic\"/>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view v-if=\"controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway'\" >\r\n\t\t\t\t\t\t<view class=\"price\"   :style=\"{color:t('color1')}\" >￥{{totalprice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else-if=\"product.is_member_yh == 0 && product.is_newcustom == 1\">\r\n\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" v-if=\"product.price_type != 1 || nowguige.sell_price > 0\" >￥{{product.yh_price}}<text v-if=\"nowguige.market_price > nowguige.sell_price\" class=\"t2\">￥{{nowguige.market_price}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\" v-if=\"product.price_type != 1 || nowguige.sell_price > 0\" >￥{{nowguige.sell_price}}<text v-if=\"nowguige.market_price > nowguige.sell_price\" class=\"t2\">￥{{nowguige.market_price}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<text class=\"choosename\" v-if=\"product.limit_start > 1\"> {{product.limit_start}}件起售</text>\r\n\t\t\t\t\t<view class=\"stock\" v-if=\"!shopset || shopset.hide_stock!=1\">库存：{{nowguige.stock}}</view>\r\n\t\t\t\t\t<view class=\"choosename\" v-if=\"product.limit_start<=1\">已选规格: {{nowguige.name}}{{jltitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<block v-if=\"showglass\">\r\n\t\t\t\t\t<view v-if=\"!glassrecord.id\">\r\n\t\t\t\t\t\t<view class=\"glassinfo\" @tap=\"goto\" :data-url=\"'/pagesExt/glass/add'\">\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t完善你的视力档案\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex flex-e\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/img/arrowright.png\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"glassinfo\" v-else @tap=\"goto\" :data-url=\"'/pagesExt/glass/index?c=1&sid='+glassrecord.id\">\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t视力档案\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex flex-e\">\r\n\t\t\t\t\t\t\t<text>{{glassrecord.type==1?'近视':'远视'}}，右眼{{glassrecord.degress_right}}度，左眼{{glassrecord.degress_left}}度</text>\r\n\t\t\t\t\t\t\t<image src=\"@/static/img/arrowright.png\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"nowguige.balance_price\" style=\"width:94%;margin:10rpx 3%;font-size:24rpx;\" :style=\"{color:t('color1')}\">首付款金额：{{nowguige.advance_price}}元，尾款金额：{{nowguige.balance_price}}元</view>\r\n\t\t\t\t<view style=\"max-height:50vh;overflow:scroll\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--加料-->\r\n\t\t\t\t<view style=\"max-height:50vh;overflow:scroll\" v-if=\"jialiaodata.length > 0 && (controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway')\">\r\n\t\t\t\t\t<view   class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name\">加料</view>\r\n\t\t\t\t\t\t<view  class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- <view v-for=\"(item,index) in tagList\" :key=\"index\" class=\"item\" :class=\"item.active?'active':''\" @click=\"tagClick(index)\">{{item.name}}</view> -->\r\n\t\t\t\t\t\t\t<view v-for=\"(jlitem, jlindex) in jialiaodata\" :key=\"jlindex\"  class=\"item2\" :class=\"jlitem.active?'on':''\" @click=\"jlchange(jlindex)\">{{jlitem.jltitle}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"product.price_type == 1\">\r\n\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"showLinkChange\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</button>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\"><image class=\"img\" src=\"/static/img/cart-minus.png\"/></view>\r\n\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\"><image class=\"img\" src=\"/static/img/cart-plus.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-text\" :style=\"{color:t('color1')}\" v-if=\"shopset && shopset.showcommission==1 && nowguige.commission > 0\">分享好友购买预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{nowguige.commission}}</text>{{nowguige.commission_desc}}</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"nowguige.stock <= 0\">\r\n\t\t\t\t\t\t\t<button class=\"nostock\">库存不足</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<!-- \t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"product.hide_price != 1 && btntype==0 && canaddcart\">加入购物车</button>\r\n\t\t\t\t\t\t -->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"product.hide_price != 1 && btntype==0\">立即购买</button>\r\n\t\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"product.hide_price != 1 && btntype==1\">确 定</button>\r\n\t\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"product.hide_price != 1 && btntype==2\">确 定</button>\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"hidePriceLink\" v-if=\"product.hide_price == 1\">{{product.hide_price_detail_text}}</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t</view>\r\n\t</template>\r\n\t<script>\r\n\t\tvar app = getApp();\r\n\t\texport default {\r\n\t\t\tdata() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tks:'',\r\n\t\t\t\t\tproduct:{},\r\n\t\t\t\t\tguigelist:{},\r\n\t\t\t\t\tguigedata:{},\r\n\t\t\t\t\tggselected:{},\r\n\t\t\t\t\tnowguige:{},\r\n\t\t\t\t\tjialiaodata:[],\r\n\t\t\t\t\tjlprice:0,\r\n\t\t\t\t\tjltitle:'',\r\n\t\t\t\t\tgwcnum:1,\r\n\t\t\t\t\tisload:false,\r\n\t\t\t\t\tloading:false,\r\n\t\t\t\t\tcanaddcart:true,\r\n\t\t\t\t\tshopset:{},\r\n\t\t\t\t\tglassrecord:{},\r\n\t\t\t\t\tshowglass:false,\r\n\t\t\t\t\ttotalprice:0,\r\n\t\t\t\t\tjlselected:[]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tprops: {\r\n\t\t\t\tbtntype:{default:0},\r\n\t\t\t\tmenuindex:{default:-1},\r\n\t\t\t\tcontroller:{default:'ApiTuanzhang'},\r\n\t\t\t\tneedaddcart:{default:true},\r\n\t\t\t\tproid:{},\r\n\t\t\t\ttid:{default:0},\r\n\t\t\t\tbid: {\r\n\t\t\t\t\ttype: [String, Number],\r\n\t\t\t\t\tdefault: 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmounted:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tuni.$on('getglassrecord', function(data) {\r\n\t\t\t\t\t that.getglassrecord()\r\n\t\t\t\t});\r\n\t\t\t\tthat.getdata();\r\n\t\t\t},\r\n\t\t\tbeforeDestroy(){\r\n\t\t\t\tuni.$off('getglassrecord')\r\n\t\t\t},\r\n\t\t\tmethods:{\r\n\t\t\t\tgetdata:function(){\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tconsole.log('准备发送请求，bid:', that.bid);\r\n\t\t\t\t\tif(this.controller == 'ApiShop' && app.globalData.isdouyin == 1){\r\n\t\t\t\t\t\tapp.showLoading('加载中');\r\n\t\t\t\t\t\tapp.post('ApiShop/getDouyinProductId',{proid:that.proid},function(res){\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\t\t\ttt.openEcGood({promotionId:res.douyin_product_id});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid, bid:that.bid || that.$parent.bid || 0},function(res){\r\n\t\t\t\t\t\tconsole.log('请求发送的参数:', {\r\n\t\t\t\t\t\t\tid: that.proid,\r\n\t\t\t\t\t\t\tbid: that.bid\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\t\tif(!that.product.limit_start){\r\n\t\t\t\t\t\t\tthat.product.limit_start = 1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\t\tthat.ggselected = ggselected;\r\n\t\t\t\t\t\tif(that.nowguige.limit_start > 0)\r\n\t\t\t\t\t\t\tthat.gwcnum = that.nowguige.limit_start;\r\n\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\tthat.gwcnum = that.product.limit_start;\r\n\t\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t//是否是眼睛产品\r\n\t\t\t\t\t\tif(that.product.product_type==1){\r\n\t\t\t\t\t\t\tthat.showglass = true\r\n\t\t\t\t\t\t\tthat.getglassrecord()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(that.controller =='ApiRestaurantShop' ||that.controller =='ApiRestaurantTakeaway'){\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.jialiaodata = res.jialiaodata;\r\n\t\t\t\t\t\t\tthat.totalprice = that.nowguige.sell_price;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tbuydialogChange:function(){\r\n\t\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\t},\r\n\t\t\t\tgetglassrecord:function(e){\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar grid = app.getCache('_glass_record_id');\r\n\t\t\t\t\tif(that.showglass===true && (!that.glassrecord || (that.glassrecord && that.glassrecord.id!=grid))){\r\n\t\t\t\t\t\tapp.post('ApiGlass/myrecord', {pagenum:1,listrow:1,id:grid}, function (resG) {\r\n\t\t\t\t\t\t\tvar datalist = resG.data;\r\n\t\t\t\t\t\t\tif(datalist.length>0){\r\n\t\t\t\t\t\t\t\tthat.glassrecord = datalist[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tshowLinkChange:function () {\r\n\t\t\t\t\tthis.$emit('showLinkChange');\r\n\t\t\t\t},\r\n\t\t\t\t//选择规格\r\n\t\t\t\tggchange: function (e){\r\n\t\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\t\t\tvar ggselected = this.ggselected;\r\n\t\t\t\t\tggselected[itemk] = idx;\r\n\t\t\t\t\tvar ks = ggselected.join(',');\r\n\t\t\t\t\tthis.ggselected = ggselected;\r\n\t\t\t\t\tthis.ks = ks;\r\n\t\t\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\t\tif (this.gwcnum < this.nowguige.limit_start) {\r\n\t\t\t\t\t\t\tthis.gwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.totalprice = parseFloat( parseFloat(this.nowguige.sell_price) +this.jlprice).toFixed(2); ;\r\n\t\t\t\t},\r\n\t\t\t\tjlchange:function(index){\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.jialiaodata[index].active =this.jialiaodata[index].active==true?false: true;\r\n\t\t\t\t\tvar jlprice = 0;\r\n\t\t\t\t\tvar title = '';\r\n\t\t\t\t\tlet jlselect = [];\r\n\t\t\t\t\tfor(let i=0;i<this.jialiaodata.length;i++){\r\n\t\t\t\t\t\tif(this.jialiaodata[i].active){\r\n\t\t\t\t\t\t\tjlprice = jlprice+parseFloat(this.jialiaodata[i].price);\t\r\n\t\t\t\t\t\t\ttitle +=','+this.jialiaodata[i].jltitle;\r\n\t\t\t\t\t\t\tjlselect.push(this.jialiaodata[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.jltitle =title;\r\n\t\t\t\t\tthis.jlprice = jlprice;\r\n\t\t\t\t\t this.totalprice =parseFloat( parseFloat(this.nowguige.sell_price) +jlprice).toFixed(2);\r\n\t\t\t\t\tthis.jlselected = jlselect;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.jialiaodata = this.jialiaodata;\r\n\t\t\t\t},\r\n\t\t\t\ttobuy: function (e) {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar ks = that.ks;\r\n\t\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\t\tif (stock < num) {\r\n\t\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\t\t\tif(that.showglass){\r\n\t\t\t\t\t\tvar glass_record_id = app.getCache('_glass_record_id');\r\n\t\t\t\t\t\tprodata += ',' + glass_record_id;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\t\tif(this.controller == 'ApiTuanzhang'){\r\n\t\t\t\t\t\tapp.goto('/pagesExa/tuanzhang/buy?prodata=' + prodata);\r\n\t\t\t\t\t}else if(this.controller == 'ApiSeckill'){\r\n\t\t\t\t\t\tapp.goto('/activity/seckill/buy?prodata=' + prodata);\r\n\t\t\t\t\t}else if(this.controller == 'ApiSeckill2'){\r\n\t\t\t\t\t\tapp.goto('/activity/seckill2/buy?prodata=' + prodata);\r\n\t\t\t\t\t}else if(this.controller == 'ApiRestaurantTakeaway'){\r\n\t\t\t\t\t\tapp.goto('/restaurant/takeaway/buy?prodata=' + prodata);\r\n\t\t\t\t\t}else if(this.controller == 'ApiRestaurantShop'){\r\n\t\t\t\t\t\tapp.goto('/restaurant/shop/buy?prodata=' + prodata);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t//加入购物车操作\r\n\t\t\t\taddcart: function () {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar ks = that.ks;\r\n\t\t\t\t\tvar num = that.gwcnum;\r\n\t\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\t\tvar tid = that.tid;\r\n\t\t\t\t\tif (num < 1) num = 1;\r\n\t\t\t\t\tif (stock < num) {\r\n\t\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\t\t\t\t\r\n\t\t\t\t\tvar glass_record_id = 0;\r\n\t\t\t\t\tif(that.showglass){\r\n\t\t\t\t\t\tglass_record_id = app.getCache('_glass_record_id');\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(this.needaddcart){\r\n\t\t\t\t\t\tapp.post(this.controller+'/addcart', {tid:tid,proid: proid,ggid: ggid,num: num,glass_record_id:glass_record_id}, function (res) {\r\n\t\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\t\tapp.success('添加成功');\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\tthis.$emit('addcart',{proid: proid,ggid: ggid,num: num,jlprice:this.jlprice,jltitle:this.jltitle});\r\n\t\t\t\t\tthis.$emit('buydialogChange');\r\n\t\t\t\t},\r\n\t\t\t\t//加\r\n\t\t\t\tgwcplus: function (e) {\r\n\t\t\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\t\t\tvar ks = this.ks;\r\n\t\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\t\treturn 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\t\treturn 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t\t},\r\n\t\t\t\t//减\r\n\t\t\t\tgwcminus: function (e) {\r\n\t\t\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\t\t\tvar ks = this.ks;\r\n\t\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t\t\t},\r\n\t\t\t\t//输入\r\n\t\t\t\tgwcinput: function (e) {\r\n\t\t\t\t\tvar ks = this.ks;\r\n\t\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\t\treturn this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tgwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tgwcnum = this.product.limit_start;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\t\tgwcnum = this.product.perlimitdan;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t\t},\r\n\t\t\t\thidePriceLink(){\r\n\t\t\t\t\tapp.goto(this.product.hide_price_link)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t</script>\r\n\t<style>\r\n\t\r\n\t.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\r\n\t.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\r\n\t.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\r\n\t.buydialog .close .image{ width: 30rpx; height:30rpx; }\r\n\t.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:30rpx 0px 20rpx 0; border-bottom:0; height: 190rpx;}\r\n\t.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n\t.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n\t.buydialog .title .price .t1{ font-size:26rpx}\r\n\t.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa; margin-left: 6rpx;}\r\n\t.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\t.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\t\r\n\t.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\r\n\t.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n\t.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n\t.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\r\n\t.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n\t.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n\t.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n\t.buydialog .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.buydialog .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.buydialog .addnum .img{width:24rpx;height:24rpx}\r\n\t.buydialog .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}\r\n\t.buydialog .tips-text{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n\t\r\n\t.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:60rpx;}\r\n\t.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n\t.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n\t.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\r\n\t.glassinfo{width: 94%;margin: 10rpx 3% 0 3%;color: #333;display: flex;justify-content: space-between;align-items: center;padding: 6rpx 10rpx;\r\n\tbackground: #f4f4f4;border-radius: 10rpx;}\r\n\t.glassinfo image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}\r\n\t</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buydialog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buydialog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115059530\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}