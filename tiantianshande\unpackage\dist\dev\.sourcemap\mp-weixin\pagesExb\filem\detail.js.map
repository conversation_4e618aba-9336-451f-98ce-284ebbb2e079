{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?d431", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?83bb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?ab46", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?aa0f", "uni-app:///pagesExb/filem/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?b429", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/detail.vue?9604"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PdfViewer", "data", "opt", "loading", "isload", "menuindex", "detail", "id", "canPreview", "previewType", "previewContent", "useSimplePdfPreview", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "desc", "pic", "callback", "that", "onShareTimeline", "imageUrl", "query", "methods", "sharecallback", "getdata", "app", "console", "uni", "downloadFile", "url", "success", "filePath", "fail", "checkPreview", "filetype", "getFileIcon", "icon", "openPdfViewer"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+F/wB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACAL;MACAC;MACAC;MACAC;QACAC;MACA;IACA;IACA;IACA;MACAJ;MACAM;MACAC;IACA;EACA;EACAC;IACAC;MACA;IAAA,CACA;IACAC;MACA;MACA;MACAN;MAEAO;QAAAnB;MAAA;QACAY;QACA;UACAA;;UAEA;UACAQ;UACAA;UACAA;;UAEA;UACAC;YACAb;UACA;;UAEA;UACAI;UAEAA;UACAS;QACA;UACAF;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MACAV;MAEAO;QAAAnB;MAAA;QACAY;QACA;UACA;UACA;UACA;;UAEA;;UAEAzB;YACAoC;YACAC;cACA;cACA;cACArC;gBACAsC;gBACAD;kBACAJ;gBACA;gBACAM;kBACAN;kBACAD;gBACA;cACA;YACA;YACAO;cACAN;cACAD;YACA;UACA;;UAGA;;UAWA;QA0BA;UACAA;QACA;MACA;IACA;IAEA;IACAQ;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACAC;UACAR;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;;QAEA;;QAEA;QAGA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IACAS;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACAD;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAE;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MAEA;IACA;IACA;IACAC;MACAV;QACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9WA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/filem/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/filem/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=c831d116&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/filem/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=c831d116&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.getFileIcon() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"file-icon\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"getFileIcon()\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"title\" v-if=\"detail.showname==1\">{{detail.name}}</text>\r\n\t\t\t\t<view class=\"subname\" v-if=\"detail.subname\">{{detail.subname}}</view>\r\n\t\t\t\t<view class=\"fileinfo\">\r\n\t\t\t\t\t<view class=\"infoitem\" v-if=\"detail.showsendtime==1\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"/static/img/time.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"text\">发布时间：{{detail.createtime}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"infoitem\" v-if=\"detail.showuploader==1\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"/static/img/user.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"text\">上传者：{{detail.uploader}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"infoitem\" v-if=\"detail.showviewcount==1\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"/static/img/view.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"text\">查看次数：{{detail.viewcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"infoitem\" v-if=\"detail.showdownloads==1\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"/static/img/download.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"text\">下载次数：{{detail.downloads}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"infoitem\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"/static/img/size.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"text\">文件大小：{{detail.filesize_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 文件内容预览区域 -->\r\n\t\t\t\t<view class=\"file-preview\" v-if=\"canPreview\">\r\n\t\t\t\t\t<!-- 根据文件类型显示不同的预览 -->\r\n\t\t\t\t\t<block v-if=\"previewType == 'image'\">\r\n\t\t\t\t\t\t<image class=\"preview-image\" :src=\"detail.filepath\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"previewType == 'pdf'\">\r\n\t\t\t\t\t\t<!-- 简化版PDF预览 -->\r\n\t\t\t\t\t\t<view class=\"simple-pdf-preview\">\r\n\t\t\t\t\t\t\t<view class=\"pdf-preview-header\">\r\n\t\t\t\t\t\t\t\t<text>PDF文件预览</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<image class=\"pdf-preview-image\" src=\"/static/img/filetypes/pdf.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view class=\"pdf-preview-info\">\r\n\t\t\t\t\t\t\t\t<text>文件名: {{detail.name}}</text>\r\n\t\t\t\t\t\t\t\t<text>类型: PDF文档</text>\r\n\t\t\t\t\t\t\t\t<text>点击下方\"预览\"按钮查看完整内容</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"previewType == 'text'\">\r\n\t\t\t\t\t\t<view class=\"preview-text\">\r\n\t\t\t\t\t\t\t<text>{{previewContent}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"no-preview\">\r\n\t\t\t\t\t\t\t<text>暂不支持此类型文件在线预览</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"no-preview\" v-else>\r\n\t\t\t\t\t<text>暂不支持此类型文件在线预览</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 操作按钮区域 -->\r\n\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t<!-- PDF预览按钮 -->\r\n\t\t\t\t\t<view class=\"preview-btn\" v-if=\"previewType == 'pdf'\" @click=\"openPdfViewer\">\r\n\t\t\t\t\t\t<text>预览</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 下载按钮 -->\r\n\t\t\t\t\t<view class=\"download-btn\" @click=\"downloadFile\">\r\n\t\t\t\t\t\t<text>下载文件</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 分类信息 -->\r\n\t\t\t\t<view class=\"category-info\" v-if=\"detail.category\">\r\n\t\t\t\t\t<text>分类：{{detail.category.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n// 导入PDF预览组件\r\nimport PdfViewer from './components/pdf-viewer.vue';\r\n\r\nexport default {\r\n  components: {\r\n    PdfViewer\r\n  },\r\n  data() {\r\n    return {\r\n\t\topt: {},\r\n\t\tloading: false,\r\n\t\tisload: false,\r\n\t\tmenuindex: -1,\r\n\r\n\t\tdetail: {},\r\n\t\tid: 0,\r\n\t\t\r\n\t\t// 预览相关\r\n\t\tcanPreview: false,\r\n\t\tpreviewType: '', // image, pdf, text, none\r\n\t\tpreviewContent: '',\r\n\t\tuseSimplePdfPreview: false,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.id = this.opt.id || 0;\r\n\t\tthis.getdata();\r\n  },\r\n  onPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n  },\r\n  onShareAppMessage: function() {\r\n\t\tvar that = this;\r\n\t\treturn this._sharewx({\r\n\t\t\ttitle: this.detail.name, \r\n\t\t\tdesc: this.detail.subname, \r\n\t\t\tpic: '/static/img/filetypes/' + (this.detail.filetype_icon || 'file') + '.png',\r\n\t\t\tcallback: function() {\r\n\t\t\t\tthat.sharecallback();\r\n\t\t\t}\r\n\t\t});\r\n  },\r\n  onShareTimeline: function() {\r\n\t\tvar that = this;\r\n\t\tvar sharewxdata = this._sharewx({\r\n\t\t\ttitle: this.detail.name, \r\n\t\t\tdesc: this.detail.subname, \r\n\t\t\tpic: '/static/img/filetypes/' + (this.detail.filetype_icon || 'file') + '.png',\r\n\t\t\tcallback: function() {\r\n\t\t\t\tthat.sharecallback();\r\n\t\t\t}\r\n\t\t});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n  },\r\n  methods: {\r\n\tsharecallback: function() {\r\n\t\t// 分享回调\r\n\t},\r\n\tgetdata: function() {\r\n\t\tvar that = this;\r\n\t\tvar id = that.id;\r\n\t\tthat.loading = true;\r\n\t\t\r\n\t\tapp.get('ApiFilem/detail', {id: id}, function (res) {\r\n\t\t\tthat.loading = false;\r\n\t\t\tif (res.status == 1) {\r\n\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\r\n\t\t\t\t// 调试日志\r\n\t\t\t\tconsole.log('文件详情:', JSON.stringify(res.detail));\r\n\t\t\t\tconsole.log('文件路径:', res.detail.filepath);\r\n\t\t\t\tconsole.log('文件类型:', res.detail.filetype);\r\n\t\t\t\t\r\n\t\t\t\t// 设置页面标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.detail.name\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否可以预览\r\n\t\t\t\tthat.checkPreview();\r\n\t\t\t\t\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t} else {\r\n\t\t\t\tapp.alert(res.msg || '获取文件详情失败');\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\t\r\n\t// 下载文件\r\n\tdownloadFile: function() {\r\n\t\tvar that = this;\r\n\t\tvar id = that.id;\r\n\t\tthat.loading = true;\r\n\t\t\r\n\t\tapp.get('ApiFilem/download', {id: id}, function (res) {\r\n\t\t\tthat.loading = false;\r\n\t\t\tif (res.status == 1) {\r\n\t\t\t\t// 执行下载或打开文件\r\n\t\t\t\tvar url = res.url;\r\n\t\t\t\tvar filename = res.filename;\r\n\t\t\t\t\r\n\t\t\t\t// 小程序环境下使用小程序API下载\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\twx.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tvar filePath = res.tempFilePath;\r\n\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\twx.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功');\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\t\t\tconsole.log('打开文档失败', error);\r\n\t\t\t\t\t\t\t\tapp.alert('文件打开失败，可能是不支持的文件类型');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\tconsole.log('下载文件失败', error);\r\n\t\t\t\t\t\tapp.alert('文件下载失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// H5环境下使用浏览器下载\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tvar a = document.createElement('a');\r\n\t\t\t\ta.href = url;\r\n\t\t\t\ta.download = filename;\r\n\t\t\t\ta.target = '_blank';\r\n\t\t\t\tdocument.body.appendChild(a);\r\n\t\t\t\ta.click();\r\n\t\t\t\tdocument.body.removeChild(a);\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// APP环境下使用APP API下载\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\tvar filePath = res.tempFilePath;\r\n\t\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\tconsole.log('打开文档成功');\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('打开文档失败', error);\r\n\t\t\t\t\t\t\t\t\tapp.alert('文件打开失败，可能是不支持的文件类型');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\tconsole.log('下载文件失败', error);\r\n\t\t\t\t\t\tapp.alert('文件下载失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tapp.alert(res.msg || '获取下载地址失败');\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\t\r\n\t// 检查文件是否可以预览\r\n\tcheckPreview: function() {\r\n\t\t// 获取文件类型，如果filetype为空，则从文件路径中提取扩展名\r\n\t\tlet filetype = this.detail.filetype ? this.detail.filetype.toLowerCase() : '';\r\n\t\t\r\n\t\t// 如果filetype为空，尝试从文件路径中提取扩展名\r\n\t\tif (!filetype && this.detail.filepath) {\r\n\t\t\tconst filepath = this.detail.filepath;\r\n\t\t\tconst fileExtMatch = filepath.match(/\\.([^.]+)$/);\r\n\t\t\tif (fileExtMatch && fileExtMatch[1]) {\r\n\t\t\t\tfiletype = fileExtMatch[1].toLowerCase();\r\n\t\t\t\tconsole.log('从文件路径提取的扩展名:', filetype);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 图片类型可以预览\r\n\t\tif (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(filetype) > -1) {\r\n\t\t\tthis.canPreview = true;\r\n\t\t\tthis.previewType = 'image';\r\n\t\t\treturn;\r\n\t\t}\r\n        \r\n        // PDF类型可以预览\r\n        if (filetype === 'pdf') {\r\n            this.canPreview = true;\r\n            this.previewType = 'pdf';\r\n            \r\n            // 在小程序环境中使用简化版预览\r\n            // #ifdef MP-WEIXIN\r\n            this.useSimplePdfPreview = true;\r\n            // #endif\r\n            \r\n            return;\r\n        }\r\n\t\t\r\n\t\t// 文本类型可以预览（根据需求可添加更多）\r\n\t\tif (['txt', 'log', 'md', 'json', 'xml', 'html', 'css', 'js'].indexOf(filetype) > -1) {\r\n\t\t\tthis.canPreview = true;\r\n\t\t\tthis.previewType = 'text';\r\n\t\t\t// 这里可以通过API获取文本内容，但需要后端支持\r\n\t\t\t// 目前暂时不实现文本预览功能\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\t// 其他类型暂不支持预览\r\n\t\tthis.canPreview = false;\r\n\t\tthis.previewType = '';\r\n\t},\r\n\tgetFileIcon: function() {\r\n\t\t// 获取文件类型\r\n\t\tlet filetype = this.detail.filetype ? this.detail.filetype.toLowerCase() : '';\r\n\t\t\r\n\t\t// 如果filetype为空，尝试从文件路径中提取扩展名\r\n\t\tif (!filetype && this.detail.filepath) {\r\n\t\t\tconst filepath = this.detail.filepath;\r\n\t\t\tconst fileExtMatch = filepath.match(/\\.([^.]+)$/);\r\n\t\t\tif (fileExtMatch && fileExtMatch[1]) {\r\n\t\t\t\tfiletype = fileExtMatch[1].toLowerCase();\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 根据文件类型返回对应的图标\r\n\t\tlet icon = filetype;\r\n\t\t\r\n\t\t// 如果有明确的文件类型图标，使用该图标\r\n\t\tif (this.detail.filetype_icon && this.detail.filetype_icon !== 'icon-file') {\r\n\t\t\ticon = this.detail.filetype_icon;\r\n\t\t} else if (filetype === 'pdf') {\r\n\t\t\ticon = 'pdf';\r\n\t\t} else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'image';\r\n\t\t} else if (['doc', 'docx'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'word';\r\n\t\t} else if (['xls', 'xlsx'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'excel';\r\n\t\t} else if (['ppt', 'pptx'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'ppt';\r\n\t\t} else if (['txt', 'log'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'txt';\r\n\t\t} else if (['zip', 'rar', '7z', 'tar', 'gz'].indexOf(filetype) > -1) {\r\n\t\t\ticon = 'zip';\r\n\t\t} else {\r\n\t\t\ticon = 'file';\r\n\t\t}\r\n\t\t\r\n\t\treturn '/static/img/filetypes/' + icon + '.png';\r\n\t},\r\n\t// 打开PDF预览页面\r\n\topenPdfViewer: function() {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pagesExa/filem/pdf-viewer-page?id=' + this.id + '&name=' + encodeURIComponent(this.detail.name)\r\n\t\t});\r\n\t}\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {padding:20rpx;}\r\n.header {background:#fff;border-radius:12rpx;padding:30rpx;margin-bottom:20rpx;}\r\n.file-icon {display:flex;justify-content:center;margin-bottom:20rpx;}\r\n.file-icon .image {width:120rpx;height:120rpx;}\r\n.title {font-size:34rpx;font-weight:bold;color:#333;line-height:48rpx;text-align:center;margin-bottom:20rpx;display:block;}\r\n.subname {font-size:28rpx;color:#666;line-height:40rpx;margin-bottom:30rpx;text-align:center;}\r\n\r\n.fileinfo {background:#f8f8f8;border-radius:8rpx;padding:20rpx;margin-bottom:30rpx;}\r\n.fileinfo .infoitem {display:flex;align-items:center;margin-bottom:10rpx;}\r\n.fileinfo .infoitem:last-child {margin-bottom:0;}\r\n.fileinfo .infoitem .icon {width:32rpx;height:32rpx;margin-right:10rpx;}\r\n.fileinfo .infoitem .text {font-size:26rpx;color:#666;line-height:40rpx;}\r\n\r\n.file-preview {margin-bottom:30rpx;border:1px solid #eee;border-radius:8rpx;overflow:hidden;background:#f9f9f9;}\r\n.preview-image {width:100%;height:auto;}\r\n.pdf-preview {width:100%;height:800rpx;border:none;background:#fff;}\r\n.preview-text {padding:20rpx;font-size:26rpx;color:#333;line-height:40rpx;max-height:600rpx;overflow-y:auto;background:#fff;}\r\n.no-preview {padding:100rpx 0;text-align:center;color:#999;font-size:28rpx;}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.preview-btn, .download-btn {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 30rpx;\r\n  line-height: 80rpx;\r\n  border-radius: 8rpx;\r\n  margin: 0 10rpx;\r\n}\r\n\r\n.preview-btn {\r\n  background: #4285f4;\r\n  color: #fff;\r\n}\r\n\r\n.preview-btn:active {\r\n  background: #3367d6;\r\n}\r\n\r\n.download-btn {\r\n  background: #007AFF;\r\n  color: #fff;\r\n}\r\n\r\n.download-btn:active {\r\n  background: #0056b3;\r\n}\r\n\r\n.category-info {font-size:26rpx;color:#999;text-align:center;}\r\n\r\n.simple-pdf-preview {\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  text-align: center;\r\n}\r\n.pdf-preview-header {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  margin-bottom: 30rpx;\r\n  font-weight: bold;\r\n}\r\n.pdf-preview-image {\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  margin: 20rpx auto;\r\n}\r\n.pdf-preview-info {\r\n  margin-top: 30rpx;\r\n  text-align: left;\r\n}\r\n.pdf-preview-info text {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  line-height: 50rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027938\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}