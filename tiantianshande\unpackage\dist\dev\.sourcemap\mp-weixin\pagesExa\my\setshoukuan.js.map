{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?a2af", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?9df1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?e99f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?5c77", "uni-app:///pagesExa/my/setshoukuan.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?b0d4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setshoukuan.vue?3c3e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "prodata", "userinfo", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uploadHeadimg", "headimg", "uploadwximg", "wximg", "uploadzfbimg", "zfbimg", "delaccount", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6DpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;MACA;IACA;IACAE;MACA;MACAD;QACA;QACAD;QACAC;UAAAE;QAAA;MACA;IACA;IACAC;MACA;MACAH;QACA;QACAD;QACAC;UAAAI;QAAA;MACA;IACA;IACAC,sCACA;MACA;MACAL;QACA;QACAD;QACAC;UAAAM;QAAA;MACA;IACA;IACAC;MACAP;QACAA;QACAA;UACAA;UACA;YACAA;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA,sBACA;QACAR;MACA;QACAA;MACA;QACAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvJA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/setshoukuan.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/setshoukuan.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setshoukuan.vue?vue&type=template&id=2a69cdf8&\"\nvar renderjs\nimport script from \"./setshoukuan.vue?vue&type=script&lang=js&\"\nexport * from \"./setshoukuan.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setshoukuan.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/setshoukuan.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setshoukuan.vue?vue&type=template&id=2a69cdf8&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setshoukuan.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setshoukuan.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"info-item\" style=\"height:136rpx;line-height:136rpx\">\r\n\t\t\t\t<view class=\"t1\" style=\"flex:1;\">微信二维码</view>\r\n\t\t\t\t<image :src=\"userinfo.wximg\" style=\"width:88rpx;height:88rpx;\" @tap=\"uploadwximg\"/>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" style=\"height:156rpx;line-height:156rpx;margin-right:100rpx;\">\r\n\t\t\t\t<view class=\"t1\" style=\"flex:1;\">支付宝二维码</view>\r\n\t\t\t\t<image :src=\"userinfo.zfbimg\" style=\"width:88rpx;height:88rpx;\" @tap=\"uploadzfbimg\"/>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view> \r\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setaliaccount\">\r\n\t\t\t\t<view class=\"t1\">支付宝账号</view>\r\n\t\t\t\t<view class=\"t2\">{{userinfo.aliaccount}}</view>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"info-item\" @tap=\"goto\" data-url=\"setusd\">\r\n\t\t\t\t<view class=\"t1\">U地址</view>\r\n\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setbankinfo\">\r\n\t\t\t\t<text class=\"t1\">银行卡</text>\r\n\t\t\t\t<text class=\"t2\">{{userinfo.bankname ? '已设置' : ''}}</text>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"info-item\" @tap=\"fanhui\">\r\n\t\t\t\t<view class=\"t1\"  style=\"background-color: red; color: #fff; width: 100%;text-align: center; border-radius: 10rpx;\">返回</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- view class=\"content\">\r\n\t\t\t<view class=\"info-item\" @tap=\"logout\">\r\n\t\t\t\t<view class=\"t1\">退出登录</view>\r\n\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t<!-- <view class=\"content\">\r\n\t\t\t<view class=\"info-item\" @tap=\"delaccount\">\r\n\t\t\t\t<view class=\"t1\">注销账号</view>\r\n\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<!-- #endif -->\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tprodata:'',\r\n\t\t\tuserinfo:{},\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiMy/set', {}, function (data) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.userinfo = data.userinfo;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tuploadHeadimg:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar headimg = urls[0];\r\n\t\t\t\tthat.userinfo.headimg = headimg;\r\n\t\t\t\tapp.post('ApiMiaosha/setfield',{headimg:headimg});\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tuploadwximg:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar wximg = urls[0];\r\n\t\t\t\tthat.userinfo.wximg = wximg;\r\n\t\t\t\tapp.post('ApiMiaosha/setfield',{wximg:wximg});\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tuploadzfbimg:function()\r\n\t\t{\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar zfbimg = urls[0];\r\n\t\t\t\tthat.userinfo.zfbimg = zfbimg;\r\n\t\t\t\tapp.post('ApiMiaosha/setfield',{zfbimg:zfbimg});\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tdelaccount:function(){\r\n\t\t\tapp.confirm('注销账号后该账号下的所有数据都将删除并且无法恢复，确定要注销吗？',function(){\r\n\t\t\t\tapp.showLoading('注销中');\r\n\t\t\t\tapp.get('ApiMy/delaccount', {}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif(data.status == 1){\r\n\t\t\t\t\t\tapp.alert(data.msg,function(){\r\n\t\t\t\t\t\t\tapp.goto('/pages/index/index');\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\tfanhui:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tif(that.opt.prodata)\r\n\t\t\t{\r\n\t\t\t    app.goto('/shopPackage/shop/buy?prodata='+that.opt.prodata);\t\r\n\t\t\t}else if(that.opt.id){\r\n\t\t\t\t app.goto('/pagesExa/miaosha/buy?id='+that.opt.id);\t\r\n\t\t\t}else{\r\n\t\t\t\tapp.goto('/pages/my/usercenter');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// app.get('ApiIndex/logout', {}, function (data) {\r\n\t\t\t// \tapp.showLoading(false);\r\n\t\t\t// \tif(data.status == 0){\r\n\t\t\t// \t\tapp.alert(data.msg);\r\n\t\t\t// \t}\r\n\t\t\t// });\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}\r\n.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}\r\n.info-item:last-child{border:none}\r\n.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}\r\n.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}\r\n\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setshoukuan.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setshoukuan.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061057\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}