{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?8ed2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?b448", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?fc0e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?fca8", "uni-app:///pagesExt/zuji/submitBuy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?6de3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/submitBuy.vue?7446"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "ggselected", "proid", "product", "guigeString", "formData", "userName", "idCard", "phone", "area", "address", "email", "companyName", "companyPosition", "salary", "companyAddress", "emergencyName1", "emergencyphone1", "emergencyRelationship1", "emergencyName2", "emergencyphone2", "emergencyRelationship2", "imgs", "onLoad", "methods", "getProductData", "that", "app", "id", "console", "guige<PERSON>r", "topay"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoJlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MAEA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA;UACAA;UACAA;UAEA;UACAG;UACA;YACAC;UACA;UAEAD;UACAH;QAEA;UACAC;QACA;MACA;IACA;IAEA;IACAI;MACA,cAEA;MACAJ,0DAEA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/NA;AAAA;AAAA;AAAA;AAA67C,CAAgB,w4CAAG,EAAC,C;;;;;;;;;;;ACAj9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zuji/submitBuy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zuji/submitBuy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./submitBuy.vue?vue&type=template&id=1e143da6&scoped=true&\"\nvar renderjs\nimport script from \"./submitBuy.vue?vue&type=script&lang=js&\"\nexport * from \"./submitBuy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./submitBuy.vue?vue&type=style&index=0&id=1e143da6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1e143da6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zuji/submitBuy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submitBuy.vue?vue&type=template&id=1e143da6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submitBuy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submitBuy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"product-info-box\">\r\n\t\t\t<view class=\"ing-box\">\r\n\t\t\t\t\r\n\t\t\t\t<image  class=\"img\" :src=\"product && product.pic\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"title-text\">{{product.name}}</view>\r\n\t\t\t\t<view class=\"config-text\">{{guigeString}}</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<form @submit=\"topay\">\r\n\r\n\t\t\t<view class=\"item-group\">\r\n\t\t\t\t<view class=\"group-title\">\r\n\t\t\t\t\t<view class=\"title-text\">个人信息</view>\r\n\t\t\t\t\t<view class=\"title-tips\">*请填写真实地址，精确到门牌号，如有虚假直接拒单</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">姓名</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.name\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">身份证号</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t\t<input :modal=\"formData.idCard\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">手机号码 </view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.phone\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">所在地区</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.area\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">居住地址</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.address\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">电子邮箱</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.email\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-group\">\r\n\t\t\t\t<view class=\"group-title\">\r\n\t\t\t\t\t<view class=\"title-text\">单位信息</view>\r\n\t\t\t\t\t<view class=\"title-tips\">*请填写真实工作，核实到虚假工作直接拒单</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">单位名称</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.companyName\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">职位</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.companyPosition\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">每月收入 </view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.salary\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">单位地址</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.companyAddress\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-group\">\r\n\t\t\t\t<view class=\"group-title\">\r\n\t\t\t\t\t<view class=\"title-text\">紧急联系人信息</view>\r\n\t\t\t\t\t<view class=\"title-tips\">*请预留两名直系亲属联系电话，如有虚假直接拒单</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"emergency-label\">联系人1</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">姓名</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyName1\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">关系</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyRelationship1\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">手机号码 </view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyphone1\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"emergency-label\">联系人2</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">姓名</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyName2\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">关系</view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyRelationship2\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"item-label\">手机号码 </view>\r\n\t\t\t\t\t<view class=\"item-control\">\r\n\t\t\t\t\t\t<input :modal=\"formData.emergencyphone2\" placeholder=\"请输入\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-group\">\r\n\t\t\t\t<view class=\"group-title\">\r\n\t\t\t\t\t<view class=\"title-text\">推广合影</view>\r\n\t\t\t\t\t<view class=\"title-tips\">*请上传你与商铺店主的合影</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"upload-box\">+</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"submit-btn\" form-type=\"submit\">提交信息</button>\r\n\t\t</form>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tggselected:'',\r\n\t\t\t\tproid: null,\r\n\t\t\t\tproduct: {},\r\n\t\t\t\tguigeString:'',\r\n\t\t\t\tformData:{\r\n\t\t\t\t\tuserName: '', // 姓名\r\n\t\t\t\t\tidCard: '', // 身份证\r\n\t\t\t\t\tphone: '',// 手机号码\r\n\t\t\t\t\tarea: '',// 所在地区\r\n\t\t\t\t\taddress: '', // 居住地址\r\n\t\t\t\t\temail: '', // 邮箱\r\n\t\t\t\t\tcompanyName: '', // 单位名称,\r\n\t\t\t\t\tcompanyPosition: '', // 职位\r\n\t\t\t\t\tsalary: '', // 薪资\r\n\t\t\t\t\tcompanyAddress: '', // 单位地址\r\n\t\t\t\t\temergencyName1: '', // 紧急联系人1\r\n\t\t\t\t\temergencyphone1:'',// 紧急联系人1电话\r\n\t\t\t\t\temergencyRelationship1:'', // 紧急联系人1关系\r\n\t\t\t\t\temergencyName2: '', // 紧急联系人2\r\n\t\t\t\t\temergencyphone2:'',// 紧急联系人2电话\r\n\t\t\t\t\temergencyRelationship2:'', // 紧急联系人2关系\r\n\t\t\t\t\timgs: ''\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.ggselected = opt.guigeString\r\n\t\t\tthis.proid = opt.prodid\r\n\t\t\tthis.getProductData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetProductData:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t that.loading = true;\r\n\t\t\t\tapp.get('ApiCycle/product', {id:that.proid}, function (res) {\r\n\t\t\t\t\t that.loading = false;\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tthat.productDetail = res\r\n\t\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tlet guigearr = []\r\n\t\t\t\t\t\tconsole.log('res.guigelist=====', res.guigelist)\r\n\t\t\t\t\t\tfor(var i in res.guigelist) {\r\n\t\t\t\t\t\t\tguigearr.push(res.guigelist[i].name)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('guigearr=====', guigearr)\r\n\t\t\t\t\t\tthat.guigeString = guigearr.join('|')\r\n\t\t\t\t\t\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tapp.post('ApiCycle/createOrder',params, function(data) {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground-color: #f1f1f1;\r\n\t\tpadding-bottom: 20px;\r\n\r\n\t\t.product-info-box {\r\n\t\t\tpadding: 12px;\r\n\t\t\tborder-radius: 9px;\r\n\t\t\tmargin: 12px;\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: center;\r\n\t\t\t.ing-box {\r\n\t\t\t\twidth: 50px;\r\n\t\t\t\theight: 50px;\r\n\t\t\t\tmargin-right: 12px;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t.img {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.info {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\talign-items: flex-start;\r\n\t\t\t\t.title-text {\r\n\t\t\t\t\tfont-size:16px;\r\n\t\t\t\t\tfont-weight:bold;\r\n\t\t\t\t\tmargin-bottom: 12px;\r\n\t\t\t\t}\r\n\t\t\t\t.config-text {\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tcolor: #ccc;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.item-group {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tmargin: 12px;\r\n\t\t\tborder-radius: 8px;\r\n\t\t\tpadding: 12px;\r\n\r\n\t\t\t.group-title {\r\n\t\t\t\tmargin-bottom: 10px;\r\n\r\n\t\t\t\t.title-text {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title-tips {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #ff0000;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.form-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfOnt-size: 16px;\r\n\t\t\t\theight: 50px;\r\n\t\t\t\tborder-bottom: 1px solid #F1F1F1;\r\n\t\t\t\t.item-label {\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\twidth: 100px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.upload-box {\r\n\t\t\t\twidth: 80px;\r\n\t\t\t\theight: 80px;\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #ccc;\r\n\t\t\t}\r\n\t\t\t.emergency-label {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t\t.submit-btn {\r\n\t\t\tborder-radius: 20px;\r\n\t\t\tbackground-color: #005bb5;\r\n\t\t\tcolor: #fff;\r\n\t\t\tmargin: 12px;\r\n\t\t\theight: 50px;\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submitBuy.vue?vue&type=style&index=0&id=1e143da6&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submitBuy.vue?vue&type=style&index=0&id=1e143da6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115101339\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}