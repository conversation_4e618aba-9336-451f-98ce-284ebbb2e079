{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?ace8", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?d1f4", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?4b21", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?a0c1", "uni-app:///restaurant/shop/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?bdf5", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/shop/buy.vue?119c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "address", "usescore", "scoredk_money", "totalprice", "couponvisible", "cuxiaovisible", "renshuvisible", "renshu", "bid", "nowbid", "needaddress", "linkman", "tel", "userinfo", "manjian_money", "cxid", "latitude", "longitude", "allbuydata", "alltotalprice", "cuxiaoin<PERSON>", "tableId", "tableinfo", "ordertype", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "prodata", "frompage", "scoredk", "inputLinkman", "inputTel", "inputfield", "calculatePrice", "chooseCoupon", "topay", "buydata", "cuxiaoid", "couponrid", "message", "field1", "field2", "field3", "field4", "field5", "a<PERSON><PERSON><PERSON>", "tableid", "showRenshuSelect", "changerenshu", "<PERSON><PERSON><PERSON><PERSON>", "showCouponList", "handleClickMask", "showCuxiaoList", "changecx", "console", "id", "chooseCuxiao"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkK5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAT;QAAAU;MAAA;QACAH;QACA;UACA;YACAC;cACA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAjB;MACA;IACA;IACA;IACAkB;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEAlB;QACAC;MACA;MAEA;QACA;MACA;QACA;MACA;MAEA;MACAA;MACA;MAEA;MACA;MACA;MAEA;QACAjB;QACAiB;MACA;QACAjB;QACAiB;MACA;MAEA;MACAA;MACAS;MACAA;IACA;IACAS;MACA;MACA;MACA;MACA;MACA;QACAnB;QACAA;QACAA;QACAA;QACA;QACA;MACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;MACA;MACA;IACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;UACA;YACAT;YACA;UACA;QACA;QACAU;UACA/B;UACAsB;UACAU;UACAC;UACAlC;UACAmC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEAlB;MACA;MACA;QACAmB;MACA;MACAnB;QAAAE;QAAAQ;QAAAU;QAAAhD;MAAA;QACA4B;QACA;UACA;UACAA;UACA;QACA;QACA,yBACAA,oFACA;UACA,yCACAA,iEAEAA;QAEA;MACA;IACA;IACAqB;MACA;MACA;IACA;IACAC;MACA;MACAvB;IACA;IACAwB;MACA;MACA;MACA;MACAxB;MACA;MACAA;IACA;IACAyB;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA7B;MACA;QACAA;QACA;MACA;MACAC;QAAA6B;MAAA;QACA9B;MACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MACA;QACAzC;QACAA;QACAA;MACA;QACA;QACAuC;QACA;UACA;UACAvC;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;UACA;UACA;UACA;UACA;UACAA;QACA;QACAA;QACAA;QACAA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/cA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/shop/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/shop/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=09ea70e1&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/shop/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=09ea70e1&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var l0 = _vm.__map(buydata.prodata, function (item, index2) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = parseFloat(\n            parseFloat(item.guige.sell_price) + parseFloat(item.jldata.jlprice)\n          ).toFixed(2)\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n        var m0 = buydata.leveldk_money > 0 ? _vm.t(\"会员\") : null\n        var m1 = _vm.ordertype == \"create_order\" ? _vm.t(\"优惠券\") : null\n        var m2 =\n          _vm.ordertype == \"create_order\" && buydata.couponCount > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m3 =\n          _vm.ordertype == \"create_order\" && !(buydata.couponCount > 0)\n            ? _vm.t(\"优惠券\")\n            : null\n        var m4 =\n          _vm.ordertype == \"create_order\" && buydata.cuxiaoCount > 0\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          l0: l0,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var m5 =\n    _vm.isload &&\n    _vm.ordertype == \"create_order\" &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.ordertype == \"create_order\" &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m7 = _vm.isload ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m9 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var m10 =\n    _vm.isload && _vm.cuxiaovisible && _vm.cxid == 0 ? _vm.t(\"color1\") : null\n  var l2 =\n    _vm.isload && _vm.cuxiaovisible\n      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.cxid == item.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 = _vm.isload && _vm.cuxiaovisible ? _vm.t(\"color1\") : null\n  var l3 =\n    _vm.isload && _vm.renshuvisible\n      ? _vm.__map(\n          [\n            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\n            20,\n          ],\n          function (item, __i0__) {\n            var $orig = _vm.__get_orig(item)\n            var m13 = _vm.renshu == item ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m13: m13,\n            }\n          }\n        )\n      : null\n  var m14 = _vm.isload && _vm.renshuvisible ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        l2: l2,\n        m12: m12,\n        l3: l3,\n        m14: m14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"address-add flex-y-center\">\n\t\t\t<view class=\"f1\">桌台信息</view>\n\t\t\t<view class=\"f2 flex1\" v-if=\"tableinfo.id\">\n\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{tableinfo.name}}<text style=\"font-size:24rpx;font-weight:normal;color:#666;margin-left:10rpx\">{{tableinfo.seat}}人桌</text></view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"f2 flex1\">请扫描桌台二维码</view>\n\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"></image>\n\t\t</view>\n\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\n\t\t\t<view class=\"btitle\"><image class=\"img\" src=\"/static/img/ico-shop.png\"/>{{buydata.business.name}}</view>\n\t\t\t<view class=\"bcontent\">\n\t\t\t\t<view class=\"product\">\n\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\" class=\"item flex\">\n\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + item.product.id\"><image :src=\"item.product.pic\"></image></view>\n\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}{{item.jldata.jltitle}}</view>\n\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-weight:bold;\">￥{{parseFloat(parseFloat(item.guige.sell_price)+parseFloat(item.jldata.jlprice)).toFixed(2)}}</text>\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{item.num}}</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"ordertype == 'create_order'\">\n\t\t\t\t\t<text class=\"f1\">就餐人数</text>\n\t\t\t\t\t<text class=\"f2\" @tap=\"showRenshuSelect\" :data-bid=\"buydata.bid\">{{buydata.renshu>0 ? buydata.renshu+'人' : '请选择人数'}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<text class=\"f1\">菜品金额</text>\n\t\t\t\t\t<text class=\"f2\">¥{{buydata.product_price}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.leveldk_money>0\">\n\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\n\t\t\t\t\t<text class=\"f2\">-¥{{buydata.leveldk_money}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"buydata.manjian_money>0\">\n\t\t\t\t\t<text class=\"f1\">满减活动</text>\n\t\t\t\t\t<text class=\"f2\">-¥{{buydata.manjian_money}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"ordertype == 'create_order'\">\n\t\t\t\t\t<text class=\"f1\">{{buydata.tea_fee_text}}</text>\n\t\t\t\t\t<text class=\"f2\">+¥{{buydata.tea_fee * (buydata.renshu>0 ? buydata.renshu : 1)}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"ordertype == 'create_order'\">\n\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\n\t\t\t\t\t<view v-if=\"buydata.couponCount > 0\" class=\"f2\" @tap=\"showCouponList\" :data-bid=\"buydata.bid\"><text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{buydata.couponrid!=0?buydata.couponList[buydata.couponkey].couponname:buydata.couponCount+'张可用'}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"ordertype == 'create_order' && buydata.cuxiaoCount > 0\">\n\t\t\t\t\t<view class=\"f1\">促销活动</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showCuxiaoList\" :data-bid=\"buydata.bid\"><text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{buydata.cuxiaoname?buydata.cuxiaoname:buydata.cuxiaoCount+'个可用'}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"remark\">\r\n\t\t\t\t\t<text class=\"f1\">备注</text>\r\n\t\t\t\t\t<input type=\"text\" class=\"flex1\" placeholder=\"请输入您的口味或要求\" @input=\"inputfield\" data-field=\"message\" :data-bid=\"buydata.bid\" placeholder-style=\"color:#cdcdcd;font-size:28rpx\"></input>\r\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"scoredk\" v-if=\"ordertype == 'create_order' && userinfo.score2money>0 && (userinfo.scoremaxtype==0 || (userinfo.scoremaxtype==1 && userinfo.scoredkmaxmoney>0))\">\n\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoremaxtype==0 && userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtype==1\">最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t</view>\n\t\t\t</checkbox-group>\n\t\t</view>\n\t\t<view style=\"width: 100%; height:110rpx;\"></view>\n\t\t<view class=\"footer flex\">\n\t\t\t<view class=\"text1 flex1\">总计：\n\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{alltotalprice}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"op\" @tap=\"topay\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</view>\n\t\t</view>\n\n\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<couponlist :couponlist=\"allbuydata[bid].couponList\" :choosecoupon=\"true\" :selectedrid=\"allbuydata[bid].couponrid\" :bid=\"bid\" @chooseCoupon=\"chooseCoupon\"></couponlist>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"cuxiaovisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"cuxiao-desc\">\n\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changecx\" data-id=\"0\">\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">不使用促销</text></view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid==0 ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-for=\"(item, index) in allbuydata[bid].cuxiaolist\" :key=\"index\" class=\"cuxiao-item\" @tap=\"changecx\" :data-id=\"item.id\">\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text> <text style=\"color:#333;padding-left:20rpx\">{{item.name}}</text></view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid==item.id ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view id=\"cxproinfo\" v-if=\"cuxiaoinfo.product\" style=\"padding:0 40rpx\">\n\t\t\t\t\t\t<view class=\"product\">\n\t\t\t\t\t\t\t<view class=\"item flex\" style=\"background:#f5f5f5\">\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + cuxiaoinfo.product.id\"><image :src=\"cuxiaoinfo.product.pic\"></image></view>\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cuxiaoinfo.product.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{cuxiaoinfo.guige.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{cuxiaoinfo.guige.sell_price}}</text><text style=\"padding-left:20rpx\"> × 1</text></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\n\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\n\t\t\t\t\t\t<view style=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\" :style=\"{background:t('color1')}\" @tap=\"chooseCuxiao\">确 定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"renshuvisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择就餐人数</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"cuxiao-desc\">\n\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changerenshu\" :data-id=\"item\" v-for=\"item in [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">{{item}}人</text></view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"renshu==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\n\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\n\t\t\t\t\t\t<view style=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\" :style=\"{background:t('color1')}\" @tap=\"chooseRenshu\">确 定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n      address: [],\n      usescore: 0,\n      scoredk_money: 0,\n      totalprice: '0.00',\n      couponvisible: false,\n      cuxiaovisible: false,\n\t\t\trenshuvisible:false,\n\t\t\trenshu:1,\n      bid: 0,\n      nowbid: 0,\n      needaddress: 1,\n      linkman: '',\n      tel: '',\n\t\t\tuserinfo:{},\n      manjian_money: 0,\n      cxid: 0,\n      latitude: \"\",\n      longitude: \"\",\n      allbuydata: \"\",\n      alltotalprice: \"\",\n      cuxiaoinfo: false,\n\t\t\ttableId:'',\n\t\t\ttableinfo:{},\r\n\t\t\tordertype:'create_order'\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.tableId = this.opt.tableId || '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiRestaurantShop/buy', {prodata: that.opt.prodata,tableId:that.tableId,frompage:that.opt.frompage}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tif (res.msg) {\n\t\t\t\t\t\tapp.alert(res.msg, function () {\n\t\t\t\t\t\t\tif (res.url) {\n\t\t\t\t\t\t\t\tapp.goto(res.url);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (res.url) {\n\t\t\t\t\t\tapp.goto(res.url);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.alert('您没有权限购买该商品');\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.tableinfo = res.tableinfo;\n\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\tthat.allbuydata = res.allbuydata;\n\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\r\n\t\t\t\tthat.ordertype = res.ordertype;\n\t\t\t\tthat.calculatePrice();\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    //积分抵扣\n    scoredk: function (e) {\n      var usescore = e.detail.value[0];\n      if (!usescore) usescore = 0;\n      this.usescore = usescore;\n      this.calculatePrice();\n    },\n    inputLinkman: function (e) {\n      this.linkman = e.detail.value;\n    },\n    inputTel: function (e) {\n      this.tel = e.detail.value;\n    },\n    inputfield: function (e) {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = e.currentTarget.dataset.bid;\n\t\t\tvar field = e.currentTarget.dataset.field;\n      allbuydata[bid][field] = e.detail.value;\n      this.allbuydata = allbuydata;\n    },\n    //计算价格\n    calculatePrice: function () {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var alltotalprice = 0;\n      for (var k in allbuydata) {\n        var product_price = parseFloat(allbuydata[k].product_price);\n        var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣\n        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动\n        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 \n        var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动  \n        var tea_fee = parseFloat(allbuydata[k].tea_fee) * (allbuydata[k].renshu ? allbuydata[k].renshu : 1); //+茶位费\n       \n        var totalprice = product_price + tea_fee - leveldk_money - manjian_money - coupon_money + cuxiao_money;\n        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\n\n        allbuydata[k].totalprice = totalprice.toFixed(2);\n        alltotalprice += totalprice;\n      }\n\n      if (that.usescore) {\n        var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣\n      } else {\n        var scoredk_money = 0;\n      }\n\n      var oldalltotalprice = alltotalprice;\n      alltotalprice = alltotalprice - scoredk_money;\n      if (alltotalprice < 0) alltotalprice = 0;\n\n      var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\n      var scoremaxtype = parseInt(that.userinfo.scoremaxtype);\n      var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);\n\n      if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {\n        scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;\n        alltotalprice = oldalltotalprice - scoredk_money;\n      } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {\n        scoredk_money = scoredkmaxmoney;\n        alltotalprice = oldalltotalprice - scoredk_money;\n      }\n\n      if (alltotalprice < 0) alltotalprice = 0;\n      alltotalprice = alltotalprice.toFixed(2);\n      that.alltotalprice = alltotalprice;\n      that.allbuydata = allbuydata;\n    },\n\t\tchooseCoupon:function(e){\n      var allbuydata = this.allbuydata;\n\t\t\tvar bid = e.bid;\n\t\t\tvar couponrid = e.rid;\n      var couponkey = e.key;\n\t\t\tif (couponrid == allbuydata[bid].couponrid) {\n        allbuydata[bid].couponkey = 0;\n        allbuydata[bid].couponrid = 0;\n        allbuydata[bid].coupontype = 1;\n        allbuydata[bid].coupon_money = 0;\n        this.allbuydata = allbuydata;\n        this.couponvisible = false;\n      } else {\n        var couponList = allbuydata[bid].couponList;\n        var coupon_money = couponList[couponkey]['money'];\n        var coupontype = couponList[couponkey]['type'];\n        allbuydata[bid].couponkey = couponkey;\n        allbuydata[bid].couponrid = couponrid;\n        allbuydata[bid].coupontype = coupontype;\n        allbuydata[bid].coupon_money = coupon_money;\n        this.allbuydata = allbuydata;\n        this.couponvisible = false;\n      }\n      this.calculatePrice();\n\t\t},\n    //提交并支付\n    topay: function () {\n      var that = this;\n      var usescore = this.usescore;\n      var frompage = that.opt.frompage ? that.opt.frompage : '';\n      var allbuydata = that.allbuydata;\n      var buydata = [];\r\n\t\t\t\r\n\t\t\tfor (var i in allbuydata) {\r\n\t\t\t\tif(that.ordertype == 'create_order'){\r\n\t\t\t\t\tif(allbuydata[i].tea_fee > 0 && allbuydata[i].renshu==0){\r\n\t\t\t\t\t\tapp.error('请选择就餐人数');\r\n\t\t\t\t\t return;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tbuydata.push({\r\n\t\t\t\t\tbid: allbuydata[i].bid,\r\n\t\t\t\t\tprodata: allbuydata[i].prodatastr,\r\n\t\t\t\t\tcuxiaoid: allbuydata[i].cuxiaoid,\r\n\t\t\t\t\tcouponrid: allbuydata[i].couponrid,\r\n\t\t\t\t\trenshu: allbuydata[i].renshu,\r\n\t\t\t\t\tmessage: allbuydata[i].message,\r\n\t\t\t\t\tfield1: allbuydata[i].field1,\r\n\t\t\t\t\tfield2: allbuydata[i].field2,\r\n\t\t\t\t\tfield3: allbuydata[i].field3,\r\n\t\t\t\t\tfield4: allbuydata[i].field4,\r\n\t\t\t\t\tfield5: allbuydata[i].field5\r\n\t\t\t\t});\r\n\t\t\t}\n      \n\t\t\tapp.showLoading('提交中');\r\n\t\t\tvar apiurl = 'ApiRestaurantShop/createOrder';\r\n\t\t\tif(that.ordertype == 'edit_order'){\r\n\t\t\t\tapiurl = 'ApiRestaurantShop/editOrder';\r\n\t\t\t}\n      app.post(apiurl, {frompage: frompage,buydata: buydata,tableid:that.tableId,usescore: usescore}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 0) {\n          //that.showsuccess(res.data.msg);\n          app.error(res.msg);\n          return;\n        }\r\n\t\t\t\tif(frompage == 'admin')\r\n\t\t\t\t\tapp.goto('/admin/restaurant/tableWaiterDetail?id=' + that.tableId, 'redirect');\r\n\t\t\t\telse {\r\n\t\t\t\t\tif(res.pay_after && res.pay_after != 1)\r\n\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid, 'redirect');\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tapp.goto('/restaurant/shop/orderlist', 'redirect');\r\n\t\t\t\t\t\t\r\n\t\t\t\t}\n      });\n    },\n\t\tshowRenshuSelect:function(e){\n\t\t\tthis.renshuvisible = true;\n      this.bid = e.currentTarget.dataset.bid;\n\t\t},\n\t\tchangerenshu: function (e) {\n      var that = this;\n      that.renshu = e.currentTarget.dataset.id;\n\t\t},\n    chooseRenshu: function () {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = that.bid;\n\t\t\tthat.allbuydata[bid].renshu = that.renshu\n\t\t\tthis.renshuvisible = false;\n\t\t\tthat.calculatePrice();\n\t\t},\n    showCouponList: function (e) {\n      this.couponvisible = true;\n      this.bid = e.currentTarget.dataset.bid;\n    },\n    handleClickMask: function () {\n      this.couponvisible = false;\n      this.cuxiaovisible = false;\n      this.renshuvisible = false;\n    },\n    showCuxiaoList: function (e) {\n      this.cuxiaovisible = true;\n      this.bid = e.currentTarget.dataset.bid;\n    },\n    changecx: function (e) {\n      var that = this;\n      var cxid = e.currentTarget.dataset.id;\n      console.log(cxid);\n      that.cxid = cxid;\n      if (cxid == 0) {\n        that.cuxiaoinfo = false;\n        return;\n      }\n      app.post(\"ApiRestaurantShop/getcuxiaoinfo\", {id: cxid}, function (res) {\n        that.cuxiaoinfo = res;\n      });\n    },\n    chooseCuxiao: function () {\n      var that = this;\n      var allbuydata = that.allbuydata;\n      var bid = that.bid;\n      var cxid = that.cxid;\n      if (cxid == 0) {\n        allbuydata[bid].cuxiaoid = '';\n        allbuydata[bid].cuxiao_money = 0;\n        allbuydata[bid].cuxiaoname = '不使用促销';\n      } else {\n        var cxtype = that.cuxiaoinfo.info.type;\n\t\t\t\tconsole.log(cxtype);\n        if (cxtype == 1 || cxtype == 6) {\n          //满额立减 满件立减\n          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;\n        } else if (cxtype == 2) {\n          //满额赠送\n          allbuydata[bid].cuxiao_money = 0;\n        } else if (cxtype == 3) {\n          //加价换购\n          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];\n        } else if (cxtype == 4 || cxtype == 5) {\n\t\t\t\t\tvar product_price = parseFloat(allbuydata[bid].product_price);\n\t\t\t\t\tvar leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣\n\t\t\t\t\tvar manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动\n          //满额打折 满件打折\n          allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;\n        }\n        allbuydata[bid].cuxiaoid = cxid;\n        allbuydata[bid].cuxiaotype = cxtype;\n        allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];\n      }\n      this.allbuydata = allbuydata;\n      this.cuxiaovisible = false;\n      this.calculatePrice();\n    },\n  }\n}\r\n</script>\r\n<style>\r\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\r\n.address-add .f1{margin-right:20rpx}\r\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\r\n.address-add .f2{ color: #666; }\r\n.address-add .f3{ width: 26rpx; height: 26rpx;}\r\n\r\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\r\n.linkitem .f1{width:160rpx;color:#111111}\r\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\r\n\r\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\r\n\r\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\r\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\r\n\r\n.bcontent{width:100%;padding:0 20rpx}\r\n\r\n.product{width:100%;border-bottom:1px solid #f4f4f4} \r\n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\r\n.product .item:last-child{border:none}\r\n.product .info{padding-left:20rpx;}\r\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .info .f2{color: #999999; font-size:24rpx}\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\r\n.product image{ width:140rpx;height:140rpx}\r\n\r\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\r\n.freight .f1{color:#333;margin-bottom:10rpx}\r\n.freight .f2{color: #111111;text-align:right;flex:1}\r\n.freight .f3{width: 24rpx;height:28rpx;}\r\n.freighttips{color:red;font-size:24rpx;}\r\n\n.freight-ul{width:100%;display:flex;}\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\n\n\r\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\r\n.price .f1{color:#333}\r\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.price .f3{width: 24rpx;height:24rpx;}\r\n\r\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\r\n.scoredk .f1{color:#333333}\r\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\r\n\r\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\r\n.remark .f1{color:#333;width:200rpx}\r\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\r\n\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding:0 20rpx;display:flex;align-items:center;z-index:8}\r\n.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1  text{color: #e94745;font-size: 32rpx;}\r\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\r\n\r\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\r\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\n.storeitem .panel .f1{color:#333}\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\n.storeitem .radio-item:last-child{border:0}\n.storeitem .radio-item .f1{color:#666;flex:1}\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\n.storeitem .radio .radio-img{width:100%;height:100%}\r\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n\r\n.cuxiao-desc{width:100%}\r\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\r\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066203\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}