{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?f19a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?c8f3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?973d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?b282", "uni-app:///pagesExt/electricityForm/recordDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?bd89", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordDetail.vue?3f2b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "textset", "detail", "team", "storeinfo", "shopset", "invoice", "show_reply", "reply_textarea", "reply_textarea_count", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "showReply", "postReply", "content", "reply_textarea_input", "todel", "orderid", "setTimeout", "toclose", "orderCollect", "showhxqr", "closeHxqr", "openMendian"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4PrxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAC;MACA;QACAF;QACAA;QACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;QACAH;QACA;MACA;MAEA;MACAD;MACAC;QACAC;QACAG;MACA;QACAL;QACA;UACAA;UACAA;UACAA;UACAC;QACA,OAEAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACA;MACAN;QACAA;QACAA;UACAO;QACA;UACAP;UACAA;UACAQ;YACAT;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;MACA;MACAT;QACAA;QACAA;UACAO;QACA;UACAP;UACAA;UACAQ;YACAT;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACA;MACAV;QACAA;QACAA;UACAO;QACA;UACAP;UACAA;UACAQ;YACAT;UACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxYA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/electricityForm/recordDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/electricityForm/recordDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recordDetail.vue?vue&type=template&id=728c1a70&\"\nvar renderjs\nimport script from \"./recordDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./recordDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recordDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/electricityForm/recordDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordDetail.vue?vue&type=template&id=728c1a70&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formContent.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"ordertop\"\r\n\t\t\t\t:style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\r\n\t\t\t\t<!-- <view class=\"f1\" v-if=\"detail.status==0\">\r\n\t\t\t\t\t<view class=\"t1\">等待买家付款</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\r\n\t\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\r\n\r\n\t\t\t\t\t<block v-if=\"detail.buytype!=1\">\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"team.status==1\">拼团中</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"team.status==2 && detail.freight_type!=1\">拼团成功,我们会尽快为您发货</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"team.status==2 && detail.freight_type==1\">拼团成功,请尽快前往自提地点取货</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"team.status==3\">拼团失败,已退款</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">我们会尽快为您发货</view>\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">请尽快前往自提地点取货</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\r\n\t\t\t\t\t<view class=\"t1\">订单已发货</view>\r\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">发货信息：{{detail.express_com}}\r\n\t\t\t\t\t\t{{detail.express_no}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\r\n\t\t\t\t\t<view class=\"t1\">订单已完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\r\n\t\t\t\t\t<view class=\"t1\">订单已取消</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"address\">\r\n\t\t\t\t<!-- <view class=\"img\">\r\n\t\t\t\t\t<image src=\"/static/img/address3.png\"></image>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.title}}</text>\r\n\t\t\t\t\t<!-- <text class=\"t2\" v-if=\"detail.freight_type!=1\" user-select=\"true\"\r\n\t\t\t\t\t\tselectable=\"true\">地址：{{detail.area}}{{detail.address}}</text> -->\r\n\t\t\t\t\t<!-- <text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openMendian\" :data-storeinfo=\"storeinfo\"\r\n\t\t\t\t\t\t:data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\" user-select=\"true\"\r\n\t\t\t\t\t\tselectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"product\" v-if=\"detail.is_servic == 1\">\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + detail.proid\">\r\n\t\t\t\t\t\t<image :src=\"detail.userInfo.headimg\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{detail.userInfo.nickname}}</text>\r\n\t\t\t\t\t\t<!-- <view class=\"t2 flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t<text>{{detail.ggname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && detail.iscomment==0 && shopset.comment==1\"\r\n\t\t\t\t\t\t\t\**********=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">去评价</view>\r\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && detail.iscomment==1\" @tap.stop=\"goto\"\r\n\t\t\t\t\t\t\t\t:data-url=\"'comment?orderid=' + detail.id\">查看评价</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{detail.sell_price}}</text><text\r\n\t\t\t\t\t\t\t\tclass=\"x2\">×{{detail.num}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t4 flex flex-x-bottom\">\r\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && detail.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">去评价</view>\r\n\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && detail.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">查看评价</view>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"orderinfo\"\r\n\t\t\t\tv-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\r\n\t\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\"\r\n\t\t\t\t\t\tselectable=\"true\">{{detail.freight_content}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"orderinfo\" v-if=\"(detail.formContent).length > 0\">\r\n\t\t\t\t<view class=\"item\" v-for=\"item in detail.formContent\" :key=\"item.id\">\r\n\t\t\t\t\t<text class=\"t1\">{{item.val1}}</text>\r\n\t\t\t\t\t<text class=\"t2\">{{item.value}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">编号</text>\r\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">提交时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\r\n\t\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\r\n\t\t\t\t\t<text class=\"t1\">发货时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\r\n\t\t\t\t\t<text class=\"t1\">收货时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<!-- <view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">商品金额</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">配送方式</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<!-- <view class=\"item\" v-if=\"detail.freight_time\">\r\n\t\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\r\n\t\t\t\t</view> -->\r\n\r\n\t\t\t\t<!-- <view class=\"item\" v-if=\"detail.scoredk_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">积分</text>\r\n\t\t\t\t\t<text class=\"t2 red\">{{detail.scoredkscore}}</text>\r\n\t\t\t\t</view> -->\r\n\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">状态</text>\r\n\t\t\t\t\t<text v-if=\"detail.already_replied==0\" class=\"t2\">待回复</text>\r\n\t\t\t\t\t<text v-if=\"detail.already_replied==1\" class=\"t2\">已回复</text>\r\n\t\t\t\t\t<!-- <text class=\"t2\" v-if=\"detail.status==0\">未付款</text> -->\r\n\t\t\t\t\t<!-- <block v-if=\"detail.status==1\">\r\n\t\t\t\t\t\t<block v-if=\"detail.buytype!=1\">\r\n\t\t\t\t\t\t\t<text v-if=\"team.status==1\" class=\"t2\">拼团中</text>\r\n\t\t\t\t\t\t\t<text v-if=\"team.status==2 && detail.freight_type!=1\" class=\"t2\">拼团成功,待发货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"team.status==2 && detail.freight_type==1\" class=\"t2\">拼团成功,待提货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"team.status==3\" class=\"t2\">拼团失败,已退款</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<text v-if=\"detail.freight_type!=1\" class=\"t2\">待发货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"detail.freight_type==1\" class=\"t2\">待提货</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block> -->\r\n\t\t\t\t\t<!-- <text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'/pagesExt/electricityForm/recordReplyList?id=' + detail.id\">\r\n\t\t\t\t\t<text class=\"t1\">回复内容</text>\r\n\t\t\t\t\t<text class=\"t2\">点击查看</text>\r\n\t\t\t\t\t<!-- <text class=\"t2\">{{detail.replyCount}}</text> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"item\" v-if=\"detail.money>0\">\r\n\t\t\t\t\t<text class=\"t1\">未中奖红包</text>\r\n\t\t\t\t\t<text class=\"t2 red\">￥{{detail.money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- <view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\r\n\t\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\">\r\n\t\t\t\t\t\t<image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\"\r\n\t\t\t\t\t\t\t:data-url=\"item[1]\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t<view class=\"orderinfo\" v-if=\"!show_reply\">\r\n\t\t\t\t<view class=\"btn2\" @tap=\"showReply\">回复</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"orderinfo\" v-if=\"show_reply\">\t\t\t\t\r\n\t\t\t\t<!-- <textarea name=\"reply\" class='textarea' :placeholder=\"请输入内容\" placeholder-style=\"font-size:28rpx\" :style=\"{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}\"  @input=\"setfield\" /> -->\r\n\t\t\t\t<textarea name=\"reply\" class='textarea' placeholder=\"请输入内容\" placeholder-style=\"font-size:28rpx\"  v-model=\"reply_textarea\" maxlength=\"1000\" @input=\"reply_textarea_input\" />\r\n\t\t\t\t<view class=\"btn2\" @tap=\"postReply\">提交</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view style=\"width:100%;height:160rpx\"></view>\r\n\r\n\t\t\t<view class=\"bottom notabbarbot\" v-if=\"detail.status!=3\">\r\n\t\t\t\t<!-- <block v-if=\"detail.status==0\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">关闭订单</view>\r\n\t\t\t\t\t<view class=\"btn1\" @tap=\"goto\" :data-url=\"'/pages/pay/pay?id=' + detail.payorderid\">去付款</view>\r\n\t\t\t\t</block> -->\r\n\r\n\t\t\t\t<!-- <block v-if=\"detail.status==2 || detail.status==3\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\"\r\n\t\t\t\t\t\t:data-url=\"'/pagesExt/order/logistics?express_com=' + detail.express_com + '&express_no=' + detail.express_no\"\r\n\t\t\t\t\t\tv-if=\"detail.freight_type!=3 && detail.freight_type!=4\">查看物流</view>\r\n\t\t\t\t</block> -->\r\n\r\n\t\t\t\t<!-- <block v-if=\"([1,2,3]).includes(detail.status) && invoice && team.status==2\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\"\r\n\t\t\t\t\t\t:data-url=\"'/pagesExt/order/invoice?type=lucky_collage&orderid=' + detail.id\">发票</view>\r\n\t\t\t\t</block> -->\r\n\t\t\t\t<!-- <block\r\n\t\t\t\t\tv-if=\"(detail.buytype==1 || team.status==2) && (detail.status==1 || detail.status==2) && detail.freight_type==1\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"showhxqr\">核销码</view>\r\n\t\t\t\t</block> -->\r\n\t\t\t\t<block v-if=\"detail.status==3\">\r\n\t\t\t\t\t<view class=\"btn2\">已完成</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<!-- <block v-if=\"detail.status==4\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\r\n\t\t\t\t</block> -->\r\n\t\t\t</view>\r\n\t\t\t<!-- <uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\" class=\"img\" />\r\n\t\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup> -->\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\ttextset: {},\r\n\t\t\t\tdetail: {},\r\n\t\t\t\tteam: {},\r\n\t\t\t\tstoreinfo: {},\r\n\t\t\t\tshopset: {},\r\n\t\t\t\tinvoice: 0,\r\n\t\t\t\t\r\n\t\t\t\tshow_reply: false,\r\n\t\t\t\treply_textarea:'',\r\n\t\t\t\treply_textarea_count: 0,\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function(option) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiElectricityForm/getRecordDetail', {\r\n\t\t\t\t\tid: that.opt.id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\t// that.team = res.team;\r\n\t\t\t\t\t// that.storeinfo = res.storeinfo;\r\n\t\t\t\t\t// that.shopset = res.shopset;\r\n\t\t\t\t\t// that.textset = app.globalData.textset;\r\n\t\t\t\t\t// that.invoice = res.invoice;\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowReply: function(){\r\n\t\t\t\tthis.show_reply = true\r\n\t\t\t},\r\n\t\t\tpostReply: function(){\r\n\t\t\t\tif( this.reply_textarea_count <= 0){\r\n\t\t\t\t\tapp.error('内容不能为空')\t\t\t\t\t\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthat.loading = true\r\n\t\t\t\tapp.post('ApiElectricityForm/postReply', {\r\n\t\t\t\t\tid: that.opt.id,\r\n\t\t\t\t\tcontent: that.reply_textarea\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false\t\t\t\t\t\r\n\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\tthat.show_reply = false\r\n\t\t\t\t\t\tthat.reply_textarea = ''\r\n\t\t\t\t\t\tthat.reply_textarea_count = 0\r\n\t\t\t\t\t\tapp.success(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tapp.error(res.msg)\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\treply_textarea_input: function(e){\r\n\t\t\t\tthis.reply_textarea_count = e.detail.value.length\r\n\t\t\t},\r\n\t\t\ttodel: function(e) {\r\n\t\t\t\treturn \r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar orderid = e.currentTarget.dataset.id;\r\n\t\t\t\tapp.confirm('确定要删除该订单吗?', function() {\r\n\t\t\t\t\tapp.showLoading('删除中');\r\n\t\t\t\t\tapp.post('ApiElectricityForm/delOrder', {\r\n\t\t\t\t\t\torderid: orderid\r\n\t\t\t\t\t}, function(data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoclose: function(e) {\r\n\t\t\t\treturn\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar orderid = e.currentTarget.dataset.id;\r\n\t\t\t\tapp.confirm('确定要关闭该订单吗?', function() {\r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\t\tapp.post('ApiElectricityForm/closeOrder', {\r\n\t\t\t\t\t\torderid: orderid\r\n\t\t\t\t\t}, function(data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\torderCollect: function(e) {\r\n\t\t\t\treturn\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar orderid = e.currentTarget.dataset.id;\r\n\t\t\t\tapp.confirm('确定要收货吗?', function() {\r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\t\tapp.post('ApiElectricityForm/orderCollect', {\r\n\t\t\t\t\t\torderid: orderid\r\n\t\t\t\t\t}, function(data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowhxqr: function() {\r\n\t\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t\t},\r\n\t\t\tcloseHxqr: function() {\r\n\t\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t\t},\r\n\t\t\topenMendian: function(e) {\r\n\t\t\t\treturn\r\n\t\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\r\n\t\t\t\tapp.goto('/shopPackage/shop/mendian?id=' + storeinfo.id);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.ordertop {\r\n\t\twidth: 100%;\r\n\t\theight: 220rpx;\r\n\t\tpadding: 50rpx 0 0 70rpx\r\n\t}\r\n\r\n\t.ordertop .f1 {\r\n\t\tcolor: #fff\r\n\t}\r\n\r\n\t.ordertop .f1 .t1 {\r\n\t\tfont-size: 32rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx\r\n\t}\r\n\r\n\t.ordertop .f1 .t2 {\r\n\t\tfont-size: 24rpx\r\n\t}\r\n\r\n\t.address {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.address .img {\r\n\t\twidth: 40rpx\r\n\t}\r\n\r\n\t.address image {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t.address .info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.address .info .t1 {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.address .info .t2 {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999\r\n\t}\r\n\r\n\t.product {\r\n\t\twidth: 94%;\r\n\t\tmargin: 0 3%;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-top: 16rpx;\r\n\t\tpadding: 14rpx 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.product .content {\r\n\t\tdisplay: flex;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 0px;\r\n\t\tborder-bottom: 1px #e5e5e5 dashed;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.product .content:last-child {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.product .content image {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t}\r\n\r\n\t.product .content .detail {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-left: 14rpx;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.product .content .detail .t1 {\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.product .content .detail .t2 {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 26rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.product .content .detail .t3 {\r\n\t\tdisplay: flex;\r\n\t\tcolor: #ff4246;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.product .content .detail .t4 {\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.product .content .detail .x1 {\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.product .content .detail .x2 {\r\n\t\twidth: 100rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: right;\r\n\t\tmargin-right: 8rpx\r\n\t}\r\n\r\n\t.product .content .comment {\r\n\t\tposition: absolute;\r\n\t\ttop: 64rpx;\r\n\t\tright: 10rpx;\r\n\t\tborder: 1px #ffc702 solid;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground: #fff;\r\n\t\tcolor: #ffc702;\r\n\t\tpadding: 0 10rpx;\r\n\t\theight: 46rpx;\r\n\t\tline-height: 46rpx;\r\n\t}\r\n\r\n\t.orderinfo {\r\n\t\twidth: 94%;\r\n\t\tmargin: 0 3%;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-top: 16rpx;\r\n\t\tpadding: 14rpx 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.orderinfo .item {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-bottom: 1px dashed #ededed;\r\n\t}\r\n\r\n\t.orderinfo .item:last-child {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.orderinfo .item .t1 {\r\n\t\twidth: 200rpx;\r\n\t}\r\n\r\n\t.orderinfo .item .t2 {\r\n\t\tflex: 1;\r\n\t\ttext-align: right\r\n\t}\r\n\r\n\t.orderinfo .item .red {\r\n\t\tcolor: red\r\n\t}\r\n\r\n\t.bottom {\r\n\t\twidth: 100%;\r\n\t\theight: 92rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0px;\r\n\t\tleft: 0px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.btn1 {\r\n\t\tmargin-left: 20rpx;\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #FB4343;\r\n\t\tborder-radius: 3px;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.btn2 {\r\n\t\tmargin-left: 20rpx;\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid #cdcdcd;\r\n\t\tborder-radius: 3px;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.btn3 {\r\n\t\tfont-size: 24rpx;\r\n\t\twidth: 120rpx;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid #cdcdcd;\r\n\t\tborder-radius: 3px;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.hxqrbox {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 50rpx;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx\r\n\t}\r\n\r\n\t.hxqrbox .img {\r\n\t\twidth: 400rpx;\r\n\t\theight: 400rpx\r\n\t}\r\n\r\n\t.hxqrbox .txt {\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.hxqrbox .close {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: -100rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -25rpx;\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.5);\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 8rpx\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093123\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}