{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?61a0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?eb61", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?137e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?6afd", "uni-app:///pagesExt/cityagent/withdrawlog.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?1d0d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdrawlog.vue?96ab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "st", "type", "datalist", "textset", "pagenum", "set", "t", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "loaded", "confirm_shoukuan", "a", "content"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkCpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IACA;IACA;IACAC;MACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MAEAC;QAAAf;QAAAI;QAAAH;MAAA;QACAa;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAG;gBACAJ;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;kBAAAb;kBAAAQ;kBAAAU;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnIA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/withdrawlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/withdrawlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdrawlog.vue?vue&type=template&id=435390a1&\"\nvar renderjs\nimport script from \"./withdrawlog.vue?vue&type=script&lang=js&\"\nexport * from \"./withdrawlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdrawlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/withdrawlog.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawlog.vue?vue&type=template&id=435390a1&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 =\n          item.status == 4 &&\n          (item.wx_state == \"WAIT_USER_CONFIRM\" ||\n            item.wx_state == \"TRANSFERING\")\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawlog.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"content\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<text class=\"t1\">提现金额：¥{{item.withdraw_amount}}</text>\n\t\t\t\t\t\t<text class=\"t2\">{{item.createtime_format || item.createtime}}</text>\n\t\t\t\t\t\t<text class=\"t2\">提现方式：{{item.withdraw_type}}</text>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.status==2\">驳回原因：{{item.reason || '无'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f3\">\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0\">{{item.status_text}}</text>\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==1\">{{item.status_text}}</text>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.status==2\">{{item.status_text}}</text>\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==3\">{{item.status_text}}</text>\n\t\t\t\t\t\t<block v-if=\"item.status==4\">\n\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @click=\"confirm_shoukuan(item.id)\" v-if=\"item.wx_state=='WAIT_USER_CONFIRM' || item.wx_state=='TRANSFERING'\">确认收款</view>\n\t\t\t\t\t\t\t<view class=\"t1\" v-else-if=\"item.wx_state=='FAIL'\">转账失败</view>\n\t\t\t\t\t\t\t<view class=\"t1\" v-else>处理中</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt:{},\n\t\t\tloading:false,\n\t\t\tisload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tnodata: false,\n\t\t\tnomore: false,\n\t\t\tst: 1, // 固定为提现记录\n\t\t\ttype:0,\n\t\t\tdatalist: [],\n\t\t\ttextset:{},\n\t\t\tpagenum: 1,\n\t\t\tset:{},\n\t\t};\n\t},\n\t\n\t// t函数用于获取主题颜色和多语言\n\tt: function(key) {\n\t\treturn getApp().t(key);\n\t},\n\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.type = this.opt.type || 0;\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: '提现记录'\n\t\t});\n\t\tvar that = this;\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonReachBottom: function () {\n\t\tif (!this.nodata && !this.nomore) {\n\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\tthis.getdata(true);\n\t\t}\n\t},\n\tmethods: {\n\t\tgetdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tvar st = that.st;\n\t\t\tvar type = that.type;\n\t\t\tvar pagenum = that.pagenum;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\t\n\t\t\tapp.post('ApiCityAgent/withdrawlog', {st: st, pagenum: pagenum, type: type}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar data = res.list || [];\n\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\tthat.textset = app.globalData.textset || {};\n\t\t\t\t\tthat.set = res.set || {};\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t\tthat.loaded();\n\t\t\t\t}else{\n\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar datalist = that.datalist;\n\t\t\t\t\t\tvar newdata = datalist.concat(data);\n\t\t\t\t\t\tthat.datalist = newdata;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 数据加载完成\n\t\tloaded: function() {\n\t\t\tthis.isload = true;\n\t\t},\n\t\t\n\t\t// 确认收款\n\t\tasync confirm_shoukuan(id){\n\t\t\tvar that = this;\n\t\t\ttry {\n\t\t\t\tvar a = await app.shoukuan(id,'cityagent_withdrawlog','');\n\t\t\t\tthat.getdata();\n\t\t\t} catch (error) {\n\t\t\t\tthat.$refs.popmsg.open({ type: 'error', title: '错误', content: '操作失败，请稍后重试' });\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style>\n\n.content{ width:94%;margin:20rpx 3%;}\n.content .item{width:100%;background:#fff;margin:20rpx 0;padding:40rpx 30rpx;border-radius:8px;display:flex;align-items:center}\n.content .item:last-child{border:0}\n.content .item .f1{width:500rpx;display:flex;flex-direction:column}\n.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}\n.content .item .f1 .t2{color:#666666;font-size:24rpx;margin-top:10rpx}\n.content .item .f1 .t3{color:#666666;font-size:24rpx;margin-top:10rpx;display: flex;}\n.content .item .f1 .t3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px;align-content: center;}\n\n.content .item .f3{ flex:1;width:200rpx;font-size:32rpx;text-align:right}\n.content .item .f3 .t1{color:#03bc01}\n.content .item .f3 .t2{color:#000000}\n\n.data-empty{background:#fff}\n.btn1{height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;padding: 0 15rpx;float: right;font-size: 25rpx;margin-left: 10rpx}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103077\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}