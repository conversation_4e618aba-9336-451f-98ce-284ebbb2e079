{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?ad48", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?a6b4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?fdb9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?cb1b", "uni-app:///pagesExt/zuji/prolist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?860d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/prolist.vue?f6f2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nomore", "nodata", "keyword", "pagenum", "datalist", "history_list", "history_show", "order", "field", "oldcid", "<PERSON>ecid", "catchegid", "cid", "gid", "cid2", "oldcid2", "catchecid2", "clist", "clist2", "glist", "paramlist", "catcheparams", "proparams", "productlisttype", "showfilter", "cpid", "bid", "latitude", "longitude", "test", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "getprolist", "paramClick", "console", "showDrawer", "closeDrawer", "change", "searchChange", "searchbtn", "searchConfirm", "searchproduct", "sortClick", "groupClick", "cateClick", "cate2Click", "filterConfirm", "filterReset", "filterClick", "addHistory", "historylist", "newhistorylist", "historyClick", "deleteSearchHistory"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkIhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;MACAA;MACAC;QACA1B;QACAC;QACAa;QACAZ;MACA;QACAuB;QACAA;QACAA;QACAA;QACAA;QACA;UACAC;YACAD;YACAA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;MAEA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAF;MACAA;MACAA;MACAA;MACA;MACAC;QACAnC;QACAD;QACAM;QACAD;QACAM;QACAD;QACAE;QACAW;QACAC;QACAC;QACAC;QACAN;MACA;QACAe;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEAG;MACAC;MACA;MACA;MACA;MACA;MACAA;IACA;IACA;IACAC;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACAH;MACA;IACA;IACAI;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAT;MACA;QACA;UACAA;UACAC;QACA;UACAD;UACAC;QACA;MACA;IACA;IACAS;MACA;MACA;MACAV;MACAA;IACA;IACAW;MACA;MACAX;MACAA;MACAA;MACAA;IACA;IACAY;MACA;MACA;MACAZ;MACAA;MACAA;IACA;IACAa;MACA;MACA;MACA;MACAb;IACA;IACAc;MACA;MACA;MACA;MACAd;IACA;IACAe;MACA;MACA;MACA;MACAf;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACApB;MACAD;IACA;IACAsB;MACA;MACA;MACA;MACAtB;MACAA;IACA;IACAuB;MACA;MACAvB;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7ZA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zuji/prolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zuji/prolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolist.vue?vue&type=template&id=68e5542c&\"\nvar renderjs\nimport script from \"./prolist.vue?vue&type=script&lang=js&\"\nexport * from \"./prolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zuji/prolist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=template&id=68e5542c&\"", "var components\ntry {\n  components = {\n    uniDrawer: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-drawer/uni-drawer\" */ \"@/components/uni-drawer/uni-drawer.vue\"\n      )\n    },\n    dpCycleItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cycle-item/dp-cycle-item\" */ \"@/components/dp-cycle-item/dp-cycle-item.vue\"\n      )\n    },\n    dpCycleItemlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cycle-itemlist/dp-cycle-itemlist\" */ \"@/components/dp-cycle-itemlist/dp-cycle-itemlist.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? !_vm.history_list || _vm.history_list.length == 0 : null\n  var m0 =\n    _vm.isload && (!_vm.field || _vm.field == \"sort\") ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.field == \"sales\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.field == \"sell_price\" ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l0 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = _vm.catchecid == item.id ? _vm.t(\"color1\") : null\n          var m8 = _vm.catchecid == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var m9 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l1 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist2, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.catchecid2 == item.id ? _vm.t(\"color1\") : null\n          var m12 = _vm.catchecid2 == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n          }\n        })\n      : null\n  var l3 = _vm.isload\n    ? _vm.__map(_vm.paramlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m13 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1\") : null\n        var m14 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1rgb\") : null\n        var l2 = _vm.__map(item.params, function (item2, index) {\n          var $orig = _vm.__get_orig(item2)\n          var m15 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1\") : null\n          var m16 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n          }\n        })\n        return {\n          $orig: $orig,\n          m13: m13,\n          m14: m14,\n          l2: l2,\n        }\n      })\n    : null\n  var m17 = _vm.isload ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l0: l0,\n        m9: m9,\n        m10: m10,\n        l1: l1,\n        l3: l3,\n        m17: m17,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"search-container\" :style=\"history_show?'height:100%;':''\">\r\n\t\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的商品\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\"\r\n\t\t\t\t\t\t\t@confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search-btn\" @tap=\"searchbtn\">\r\n\t\t\t\t\t\t<image src=\"/static/img/show-cascades.png\" style=\"height:36rpx;width:36rpx\"\r\n\t\t\t\t\t\t\tv-if=\"!history_show && productlisttype=='itemlist'\" />\r\n\t\t\t\t\t\t<image src=\"/static/img/show-list.png\" style=\"height:36rpx;width:36rpx\"\r\n\t\t\t\t\t\t\tv-if=\"!history_show && productlisttype=='item2'\" />\r\n\t\t\t\t\t\t<text v-if=\"history_show\">搜索</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-history\" v-show=\"history_show\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\r\n\t\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/del.png\" style=\"width:36rpx;height:36rpx\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search-history-list\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\"\r\n\t\t\t\t\t\t\t:data-value=\"item\" @tap=\"historyClick\">{{item}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/tanhao.png\" style=\"width:36rpx;height:36rpx;margin-right:10rpx\" />\r\n\t\t\t\t\t\t\t暂无记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-navbar\" v-show=\"!history_show\">\r\n\t\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\"\r\n\t\t\t\t\t\t:style=\"(!field||field=='sort')?'color:'+t('color1'):''\" data-field=\"sort\" data-order=\"desc\">综合\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\"\r\n\t\t\t\t\t\t:style=\"field=='sales'?'color:'+t('color1'):''\" data-field=\"sales\" data-order=\"desc\">销量</view>\r\n\t\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" data-field=\"sell_price\"\r\n\t\t\t\t\t\t:data-order=\"order=='asc'?'desc':'asc'\">\r\n\t\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\r\n\t\t\t\t\t\t<text class=\"iconfont iconshangla\"\r\n\t\t\t\t\t\t\t:style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t\t\t<text class=\"iconfont icondaoxu\"\r\n\t\t\t\t\t\t\t:style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\" @click.stop=\"showDrawer('showRight')\">\r\n\t\t\t\t\t\t筛选 <text :class=\"'iconfont iconshaixuan ' + (showfilter?'active':'')\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-drawer ref=\"showRight\" mode=\"right\" @change=\"change($event,'showRight')\" :width=\"280\">\r\n\t\t\t\t\t<view class=\"filter-scroll-view\" style=\"max-height:100%;padding-bottom:110rpx;overflow:hidden auto\">\r\n\t\t\t\t\t\t<view class=\"filter-scroll-view-box\">\r\n\t\t\t\t\t\t\t<view class=\"search-filter\">\r\n\t\t\t\t\t\t\t\t<view class=\"filter-title\">筛选</view>\r\n\r\n\t\t\t\t\t\t\t\t<block v-if=\"!bid || bid <=0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\**********=\"cateClick\" :data-cid=\"oldcid\">全部</view>\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\**********=\"cateClick\" :data-cid=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"catchecid2==oldcid2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\**********=\"cate2Click\" :data-cid2=\"oldcid2\">全部</view>\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"catchecid2==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\**********=\"cate2Click\" :data-cid2=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in paramlist\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-content-title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"catcheparams[item.name]==''?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\**********=\"paramClick\" :data-paramkey=\"item.name\" data-paramval=\"\">全部</view>\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item2, index) in item.params\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"filter-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"catcheparams[item.name]==item2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\**********=\"paramClick\" :data-paramkey=\"item.name\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:data-paramval=\"item2\">{{item2}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t\t\t\t<view class=\"search-filter-btn\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"filterReset\">重置</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" :style=\"{background:t('color1')}\" @tap=\"filterConfirm\">确定</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-drawer>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product-container\">\r\n\t\t\t\t<block v-if=\"datalist && datalist.length>0\">\r\n\t\t\t\t\t<dp-cycle-item v-if=\"productlisttype=='item2'\" :data=\"datalist\"\r\n\t\t\t\t\t\t:menuindex=\"menuindex\"></dp-cycle-item>\r\n\t\t\t\t\t<dp-cycle-itemlist v-if=\"productlisttype=='itemlist'\" :data=\"datalist\"\r\n\t\t\t\t\t\t:menuindex=\"menuindex\"></dp-cycle-itemlist>\r\n\t\t\t\t</block>\r\n\t\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tnodata: false,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\thistory_list: [],\r\n\t\t\t\thistory_show: false,\r\n\t\t\t\torder: '',\r\n\t\t\t\tfield: '',\r\n\t\t\t\toldcid: \"\",\r\n\t\t\t\tcatchecid: \"\",\r\n\t\t\t\tcatchegid: \"\",\r\n\t\t\t\tcid: \"\",\r\n\t\t\t\tgid: '',\r\n\t\t\t\tcid2: '',\r\n\t\t\t\toldcid2: \"\",\r\n\t\t\t\tcatchecid2: \"\",\r\n\t\t\t\tclist: [],\r\n\t\t\t\tclist2: [],\r\n\t\t\t\tglist: [],\r\n\t\t\t\tparamlist: [],\r\n\t\t\t\tcatcheparams: {},\r\n\t\t\t\tproparams: {},\r\n\t\t\t\tproductlisttype: 'item2',\r\n\t\t\t\tshowfilter: \"\",\r\n\t\t\t\tcpid: 0,\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\ttest: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.oldcid = this.opt.cid || '';\r\n\t\t\tthis.catchecid = this.opt.cid;\r\n\t\t\tthis.cid = this.opt.cid;\r\n\t\t\tthis.cid2 = this.opt.cid2 || '';\r\n\t\t\tthis.oldcid2 = this.opt.cid2 || '';\r\n\t\t\tthis.catchecid2 = this.opt.cid2;\r\n\t\t\tthis.gid = this.opt.gid;\r\n\t\t\tthis.cpid = this.opt.cpid || 0;\r\n\t\t\tthis.bid = this.opt.bid ? this.opt.bid : 0;\r\n\t\t\t//console.log(this.bid);\r\n\t\t\tif (this.cpid > 0) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '可用商品列表'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tvar productlisttype = app.getCache('productlisttype');\r\n\t\t\tif (productlisttype) this.productlisttype = productlisttype;\r\n\t\t\tthis.history_list = app.getCache('search_history_list');\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getprolist();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\tthat.datalist = [];\r\n\t\t\t\tvar cid = that.opt.cid;\r\n\t\t\t\tvar gid = that.opt.gid;\r\n\t\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\t\tvar cid2 = that.cid2;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiCycle/prolist', {\r\n\t\t\t\t\tcid: cid,\r\n\t\t\t\t\tgid: gid,\r\n\t\t\t\t\tbid: bid,\r\n\t\t\t\t\tcid2: cid2\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\t\tthat.clist2 = res.clist2;\r\n\t\t\t\t\tthat.glist = res.glist;\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\tif (that.latitude == '' && that.longitude == '' && res.needlocation) {\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetprolist: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\tvar order = that.order;\r\n\t\t\t\tvar field = that.field;\r\n\t\t\t\tvar gid = that.gid;\r\n\t\t\t\tvar cid = that.cid;\r\n\t\t\t\tvar cid2 = that.cid2;\r\n\t\t\t\tvar cpid = that.cpid;\r\n\t\t\t\tthat.history_show = false;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\t\tapp.post('ApiCycle/getprolist', {\r\n\t\t\t\t\tpagenum: pagenum,\r\n\t\t\t\t\tkeyword: keyword,\r\n\t\t\t\t\tfield: field,\r\n\t\t\t\t\torder: order,\r\n\t\t\t\t\tgid: gid,\r\n\t\t\t\t\tcid: cid,\r\n\t\t\t\t\tcid2: cid2,\r\n\t\t\t\t\tcpid: cpid,\r\n\t\t\t\t\tbid: bid,\r\n\t\t\t\t\tlatitude: that.latitude,\r\n\t\t\t\t\tlongitude: that.longitude,\r\n\t\t\t\t\tproparams: that.proparams\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tparamClick: function(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tvar paramkey = e.currentTarget.dataset.paramkey;\r\n\t\t\t\tvar paramval = e.currentTarget.dataset.paramval;\r\n\t\t\t\tthis.catcheparams[paramkey] = paramval;\r\n\t\t\t\tthis.test = Math.random();\r\n\t\t\t\tconsole.log(this.catcheparams);\r\n\t\t\t},\r\n\t\t\t// 打开窗口\r\n\t\t\tshowDrawer(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tthis.$refs[e].open()\r\n\t\t\t},\r\n\t\t\t// 关闭窗口\r\n\t\t\tcloseDrawer(e) {\r\n\t\t\t\tthis.$refs[e].close()\r\n\t\t\t},\r\n\t\t\t// 抽屉状态发生变化触发\r\n\t\t\tchange(e, type) {\r\n\t\t\t\tconsole.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));\r\n\t\t\t\tthis[type] = e\r\n\t\t\t},\r\n\t\t\tsearchChange: function(e) {\r\n\t\t\t\tthis.keyword = e.detail.value;\r\n\t\t\t\tif (e.detail.value == '') {\r\n\t\t\t\t\tthis.history_show = true;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsearchbtn: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (that.history_show) {\r\n\t\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\t\tthat.searchproduct();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.productlisttype == 'itemlist') {\r\n\t\t\t\t\t\tthat.productlisttype = 'item2';\r\n\t\t\t\t\t\tapp.setCache('productlisttype', 'item2');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.productlisttype = 'itemlist';\r\n\t\t\t\t\t\tapp.setCache('productlisttype', 'itemlist');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsearchConfirm: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = e.detail.value;\r\n\t\t\t\tthat.keyword = keyword\r\n\t\t\t\tthat.searchproduct();\r\n\t\t\t},\r\n\t\t\tsearchproduct: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\tthat.datalist = [];\r\n\t\t\t\tthat.addHistory();\r\n\t\t\t\tthat.getprolist();\r\n\t\t\t},\r\n\t\t\tsortClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar t = e.currentTarget.dataset;\r\n\t\t\t\tthat.field = t.field;\r\n\t\t\t\tthat.order = t.order;\r\n\t\t\t\tthat.searchproduct();\r\n\t\t\t},\r\n\t\t\tgroupClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar gid = e.currentTarget.dataset.gid;\r\n\t\t\t\tif (gid === true) gid = '';\r\n\t\t\t\tthat.catchegid = gid\r\n\t\t\t},\r\n\t\t\tcateClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cid = e.currentTarget.dataset.cid;\r\n\t\t\t\tif (cid === true) cid = '';\r\n\t\t\t\tthat.catchecid = cid;\r\n\t\t\t},\r\n\t\t\tcate2Click: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cid2 = e.currentTarget.dataset.cid2;\r\n\t\t\t\tif (cid2 === true) cid2 = '';\r\n\t\t\t\tthat.catchecid2 = cid2\r\n\t\t\t},\r\n\t\t\tfilterConfirm() {\r\n\t\t\t\tthis.cid = this.catchecid;\r\n\t\t\t\tthis.cid2 = this.catchecid2;\r\n\t\t\t\tthis.gid = this.catchegid;\r\n\t\t\t\tthis.proparams = this.catcheparams;\r\n\t\t\t\tthis.searchproduct();\r\n\t\t\t\tthis.$refs['showRight'].close()\r\n\t\t\t},\r\n\t\t\tfilterReset() {\r\n\t\t\t\tthis.catchecid = this.oldcid;\r\n\t\t\t\tthis.catchecid2 = this.oldcid2;\r\n\t\t\t\tthis.catchegid = '';\r\n\t\t\t},\r\n\t\t\tfilterClick: function() {\r\n\t\t\t\tthis.showfilter = !this.showfilter\r\n\t\t\t},\r\n\t\t\taddHistory: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\tif (app.isNull(keyword)) return;\r\n\t\t\t\tvar historylist = app.getCache('search_history_list');\r\n\t\t\t\tif (app.isNull(historylist)) historylist = [];\r\n\t\t\t\thistorylist.unshift(keyword);\r\n\t\t\t\tvar newhistorylist = [];\r\n\t\t\t\tfor (var i in historylist) {\r\n\t\t\t\t\tif (historylist[i] != keyword || i == 0) {\r\n\t\t\t\t\t\tnewhistorylist.push(historylist[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (newhistorylist.length > 5) newhistorylist.splice(5, 1);\r\n\t\t\t\tapp.setCache('search_history_list', newhistorylist);\r\n\t\t\t\tthat.history_list = newhistorylist\r\n\t\t\t},\r\n\t\t\thistoryClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = e.currentTarget.dataset.value;\r\n\t\t\t\tif (keyword.length == 0) return;\r\n\t\t\t\tthat.keyword = keyword;\r\n\t\t\t\tthat.searchproduct();\r\n\t\t\t},\r\n\t\t\tdeleteSearchHistory: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.history_list = null;\r\n\t\t\t\tapp.removeCache(\"search_history_list\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.search-container {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 9;\r\n\t\ttop: var(--window-top)\r\n\t}\r\n\r\n\t.topsearch {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 20rpx;\r\n\t}\r\n\r\n\t.topsearch .f1 {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 0;\r\n\t\tbackground-color: #f7f7f7;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.topsearch .f1 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 10px\r\n\t}\r\n\r\n\t.topsearch .f1 input {\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.topsearch .search-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: #5a5a5a;\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-left: 20rpx\r\n\t}\r\n\r\n\t.search-navbar {\r\n\t\tdisplay: flex;\r\n\t\ttext-align: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 5rpx 0\r\n\t}\r\n\r\n\t.search-navbar-item {\r\n\t\tflex: 1;\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tposition: relative;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #323232\r\n\t}\r\n\r\n\t.search-navbar-item .iconshangla {\r\n\t\tposition: absolute;\r\n\t\ttop: -4rpx;\r\n\t\tpadding: 0 6rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #7D7D7D\r\n\t}\r\n\r\n\t.search-navbar-item .icondaoxu {\r\n\t\tposition: absolute;\r\n\t\ttop: 8rpx;\r\n\t\tpadding: 0 6rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #7D7D7D\r\n\t}\r\n\r\n\t.search-navbar-item .iconshaixuan {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #7d7d7d\r\n\t}\r\n\r\n\t.search-history {\r\n\t\tpadding: 24rpx 34rpx;\r\n\t}\r\n\r\n\t.search-history .search-history-title {\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.search-history .delete-search-history {\r\n\t\tfloat: right;\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tmargin-top: -15rpx;\r\n\t}\r\n\r\n\t.search-history-list {\r\n\t\tpadding: 24rpx 0 0 0;\r\n\t}\r\n\r\n\t.search-history-list .search-history-item {\r\n\t\tdisplay: inline-block;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin: 0 10rpx 10rpx 0;\r\n\t\tbackground: #ddd;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.filter-scroll-view {\r\n\t\tmargin-top: var(--window-top)\r\n\t}\r\n\r\n\t.search-filter {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\ttext-align: left;\r\n\t\twidth: 100%;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.filter-content-title {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tmargin-bottom: 10rpx\r\n\t}\r\n\r\n\t.filter-title {\r\n\t\tcolor: #BBBBBB;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #F8F8F8;\r\n\t\tpadding: 60rpx 0 30rpx 20rpx;\r\n\t}\r\n\r\n\t.search-filter-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t}\r\n\r\n\t.search-filter-content .filter-item {\r\n\t\tbackground: #F4F4F4;\r\n\t\tborder-radius: 28rpx;\r\n\t\tcolor: #2B2B2B;\r\n\t\tfont-weight: bold;\r\n\t\tmargin: 10rpx 10rpx;\r\n\t\tmin-width: 140rpx;\r\n\t\theight: 56rpx;\r\n\t\tline-height: 56rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 30rpx\r\n\t}\r\n\r\n\t.search-filter-content .close {\r\n\t\ttext-align: right;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff4544;\r\n\t\twidth: 100%;\r\n\t\tpadding-right: 20rpx\r\n\t}\r\n\r\n\t.search-filter button .icon {\r\n\t\tmargin-top: 6rpx;\r\n\t\theight: 54rpx;\r\n\t}\r\n\r\n\t.search-filter-btn {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 30rpx 30rpx;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.search-filter-btn .btn {\r\n\t\twidth: 240rpx;\r\n\t\theight: 66rpx;\r\n\t\tline-height: 66rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 33rpx;\r\n\t\tcolor: #2B2B2B;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.search-filter-btn .btn2 {\r\n\t\twidth: 240rpx;\r\n\t\theight: 66rpx;\r\n\t\tline-height: 66rpx;\r\n\t\tborder-radius: 33rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.product-container {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 190rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tpadding: 0 24rpx\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115097507\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}