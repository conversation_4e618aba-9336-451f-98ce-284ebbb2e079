{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?8976", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?7472", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?4355", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?8120", "uni-app:///pagesExt/business/sales-ranking.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?38a6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/sales-ranking.vue?cdd9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "rankingType", "rankingList", "totalCount", "dateTypeOptions", "value", "label", "currentDateType", "dateValue", "datePickerMode", "dateFields", "displayDateValue", "categoryOptions", "id", "name", "currentCategory", "keyword", "currentPage", "perPage", "computed", "totalPages", "onLoad", "methods", "switchRankingType", "handleDateTypeChange", "handleDateChange", "handleCategoryChange", "handleSearch", "prevPage", "nextPage", "getCategories", "app", "console", "fetchRankingData", "uni", "title", "ranking_type", "pagenum", "pernum", "params"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmLtxB;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MAAA;MACAC;MACAC;MAEA;MACAC,kBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;QAAAF;QAAAC;MAAA;MACAE;MACAC;MACAC;MACAC;MAEA;MACAC;QAAAC;QAAAC;MAAA;MACAC;QAAAF;QAAAC;MAAA;MAEA;MACAE;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;MAAA;;MAGA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACA;UACA;UACA;YAAAlB;YAAAC;UAAA;QACA;UACAkB;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;MACA;MAEA;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAC;QACAA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEAR;QACAG;QAEA;UACA;UACA;QACA;UACAH;UACA;UACA;QACA;MACA;QACAG;QACAH;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3WA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/sales-ranking.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/sales-ranking.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./sales-ranking.vue?vue&type=template&id=60700c4c&\"\nvar renderjs\nimport script from \"./sales-ranking.vue?vue&type=script&lang=js&\"\nexport * from \"./sales-ranking.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sales-ranking.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/sales-ranking.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sales-ranking.vue?vue&type=template&id=60700c4c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.rankingList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sales-ranking.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sales-ranking.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"data-ranking-container\">\r\n      <!-- 顶部背景和标题区域 -->\r\n      <view class=\"banner-area\">\r\n        <view class=\"radial-bg\"></view>\r\n        <view class=\"title-area\">\r\n          <text class=\"main-title\">数据榜</text>\r\n        </view>\r\n        <view class=\"trophy-container\">\r\n          <!-- 奖杯图标 -->\r\n          <view class=\"trophy-icon\">\r\n            <view class=\"trophy-cup\">\r\n              <view class=\"trophy-top\"></view>\r\n              <view class=\"trophy-middle\"></view>\r\n              <view class=\"trophy-bottom\"></view>\r\n              <view class=\"trophy-base\"></view>\r\n            </view>\r\n          </view>\r\n          <view class=\"trophy-decorations\">\r\n            <view class=\"decoration left-leaf\"></view>\r\n            <view class=\"decoration right-leaf\"></view>\r\n            <view class=\"decoration top-sparkle\"></view>\r\n            <view class=\"decoration bottom-sparkle\"></view>\r\n          </view>\r\n          <!-- 彩旗装饰 -->\r\n          <view class=\"banner-flags\">\r\n            <view class=\"flag-string\"></view>\r\n            <view class=\"flag flag-1\"></view>\r\n            <view class=\"flag flag-2\"></view>\r\n            <view class=\"flag flag-3\"></view>\r\n            <view class=\"flag flag-4\"></view>\r\n            <view class=\"flag flag-5\"></view>\r\n          </view>\r\n          <text class=\"top-text\">TOP</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 排名类型切换 -->\r\n      <view class=\"type-switcher\">\r\n        <view \r\n          class=\"type-item\" \r\n          :class=\"{ active: rankingType === 'business' }\" \r\n          @tap=\"switchRankingType('business')\"\r\n        >队伍排名</view>\r\n        <view \r\n          class=\"type-item\" \r\n          :class=\"{ active: rankingType === 'category' }\" \r\n          @tap=\"switchRankingType('category')\"\r\n        >学校排名</view>\r\n        <!-- <view class=\"type-item\">个人数据</view> -->\r\n      </view>\r\n      \r\n      <!-- 筛选栏 -->\r\n      <view class=\"filter-bar\">\r\n        <!-- 时间类型选择 -->\r\n        <view class=\"filter-item date-type\">\r\n          <picker \r\n            mode=\"selector\" \r\n            :range=\"dateTypeOptions\" \r\n            @change=\"handleDateTypeChange\"\r\n            range-key=\"label\"\r\n          >\r\n            <view class=\"picker-content\">\r\n              <text>{{ currentDateType.label }}</text>\r\n              <text class=\"iconfont iconxiala\"></text>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <!-- 日期选择 -->\r\n        <view class=\"filter-item date-picker\">\r\n          <picker \r\n            :mode=\"datePickerMode\" \r\n            :value=\"dateValue\" \r\n            :fields=\"dateFields\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n            <view class=\"picker-content\">\r\n              <text>{{ displayDateValue || '选择日期' }}</text>\r\n              <text class=\"iconfont iconxiala\"></text>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <!-- 学校选择 - 只在队伍排名时显示 -->\r\n        <view class=\"filter-item category-picker\" v-if=\"rankingType === 'business'\">\r\n          <picker \r\n            mode=\"selector\" \r\n            :range=\"categoryOptions\" \r\n            @change=\"handleCategoryChange\"\r\n            range-key=\"name\"\r\n          >\r\n            <view class=\"picker-content\">\r\n              <text>{{ currentCategory.name || '全部学校' }}</text>\r\n              <text class=\"iconfont iconxiala\"></text>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 搜索框 -->\r\n      <view class=\"search-bar\">\r\n        <input \r\n          class=\"search-input\" \r\n          type=\"text\" \r\n          v-model=\"keyword\" \r\n          :placeholder=\"rankingType === 'business' ? '搜索队伍名称' : '搜索学校名称'\"\r\n          @confirm=\"handleSearch\"\r\n        />\r\n        <button class=\"search-btn\" @tap=\"handleSearch\">搜索</button>\r\n      </view>\r\n      \r\n      <!-- 数据列表 -->\r\n      <view class=\"ranking-list\">\r\n        <!-- 列表头部 -->\r\n        <view class=\"list-header\">\r\n          <view class=\"header-rank\">排名</view>\r\n          <view class=\"header-info\">{{ rankingType === 'business' ? '队伍信息' : '学校信息' }}</view>\r\n          <view class=\"header-sales\">销售总金额(元)</view>\r\n          <!-- <view class=\"header-detail\">详情</view> -->\r\n        </view>\r\n        \r\n        <!-- 列表内容 -->\r\n        <scroll-view scroll-y class=\"list-content\" v-if=\"rankingList.length > 0\">\r\n          <view \r\n            class=\"list-item\" \r\n            v-for=\"(item, index) in rankingList\" \r\n            :key=\"rankingType === 'business' ? item.business_id : item.category_id\"\r\n          >\r\n            <!-- 排名 -->\r\n            <view class=\"rank-num\" :class=\"{'top-rank': item.rank <= 3}\">{{ item.rank }}</view>\r\n            \r\n            <!-- 信息 -->\r\n            <view class=\"item-info\">\r\n              <template v-if=\"rankingType === 'business'\">\r\n                <image class=\"item-logo\" :src=\"item.logo || '/static/img/shop_addr.png'\" mode=\"aspectFill\"></image>\r\n                <text class=\"item-name\">{{ item.name }}</text>\r\n              </template>\r\n              <template v-else>\r\n                <text class=\"item-name\">{{ item.name }}</text>\r\n              </template>\r\n            </view>\r\n            \r\n            <!-- 销售额 -->\r\n            <view class=\"item-sales\">¥ {{ item.sales_price }}</view>\r\n            \r\n            <!-- 详情按钮 -->\r\n           <!-- <view class=\"item-detail\">详情</view> -->\r\n          </view>\r\n        </scroll-view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-else>\r\n          <image class=\"empty-icon\" src=\"/static/img/order.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"empty-text\">暂无数据</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 分页 -->\r\n      <view class=\"pagination\" v-if=\"totalCount > 0\">\r\n        <view class=\"page-info\">共 {{ totalCount }} 条</view>\r\n        <view class=\"page-controls\">\r\n          <view \r\n            class=\"page-btn prev\" \r\n            :class=\"{ disabled: currentPage <= 1 }\"\r\n            @tap=\"prevPage\"\r\n          >上一页</view>\r\n          <view class=\"page-current\">{{ currentPage }} / {{ totalPages }}</view>\r\n          <view \r\n            class=\"page-btn next\" \r\n            :class=\"{ disabled: currentPage >= totalPages }\"\r\n            @tap=\"nextPage\"\r\n          >下一页</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </template>\r\n  \r\n  <script>\r\n  const app = getApp();\r\n  \r\n  export default {\r\n    data() {\r\n      return {\r\n        // 排名数据\r\n        rankingType: 'business', // 'business'或'category'\r\n        rankingList: [],\r\n        totalCount: 0,\r\n        \r\n        // 筛选条件\r\n        dateTypeOptions: [\r\n          { value: '', label: '全部时间' },\r\n          { value: 'year', label: '按年' },\r\n          { value: 'month', label: '按月' },\r\n          { value: 'day', label: '按日' }\r\n        ],\r\n        currentDateType: { value: '', label: '全部时间' },\r\n        dateValue: '',\r\n        datePickerMode: 'date',\r\n        dateFields: 'day',\r\n        displayDateValue: '',\r\n        \r\n        // 学校\r\n        categoryOptions: [{ id: '', name: '全部学校' }],\r\n        currentCategory: { id: '', name: '全部学校' },\r\n        \r\n        // 搜索\r\n        keyword: '',\r\n        \r\n        // 分页\r\n        currentPage: 1,\r\n        perPage: 10\r\n      };\r\n    },\r\n    \r\n    computed: {\r\n      totalPages() {\r\n        return Math.ceil(this.totalCount / this.perPage) || 1;\r\n      }\r\n    },\r\n    \r\n    onLoad() {\r\n      this.getCategories();\r\n      this.fetchRankingData();\r\n    },\r\n    \r\n    methods: {\r\n      // 切换排名类型\r\n      switchRankingType(type) {\r\n        if (this.rankingType !== type) {\r\n          this.rankingType = type;\r\n          this.currentPage = 1;\r\n          this.fetchRankingData();\r\n        }\r\n      },\r\n      \r\n      // 日期类型变更\r\n      handleDateTypeChange(e) {\r\n        const index = e.detail.value;\r\n        this.currentDateType = this.dateTypeOptions[index];\r\n        \r\n        // 根据日期类型设置日期选择器的模式\r\n        switch (this.currentDateType.value) {\r\n          case 'year':\r\n            this.datePickerMode = 'date';\r\n            this.dateFields = 'year';\r\n            break;\r\n          case 'month':\r\n            this.datePickerMode = 'date';\r\n            this.dateFields = 'month';\r\n            break;\r\n          case 'day':\r\n            this.datePickerMode = 'date';\r\n            this.dateFields = 'day';\r\n            break;\r\n          default:\r\n            this.dateValue = '';\r\n            this.displayDateValue = '';\r\n            break;\r\n        }\r\n        \r\n        // 清空已选日期并重置分页\r\n        this.dateValue = '';\r\n        this.displayDateValue = '';\r\n        this.currentPage = 1;\r\n        this.fetchRankingData();\r\n      },\r\n      \r\n      // 日期变更\r\n      handleDateChange(e) {\r\n        this.dateValue = e.detail.value;\r\n        this.displayDateValue = this.dateValue;\r\n        this.currentPage = 1;\r\n        this.fetchRankingData();\r\n      },\r\n      \r\n      // 学校变更\r\n      handleCategoryChange(e) {\r\n        const index = e.detail.value;\r\n        this.currentCategory = this.categoryOptions[index];\r\n        this.currentPage = 1;\r\n        this.fetchRankingData();\r\n      },\r\n      \r\n      // 搜索\r\n      handleSearch() {\r\n        this.currentPage = 1;\r\n        this.fetchRankingData();\r\n      },\r\n      \r\n      // 上一页\r\n      prevPage() {\r\n        if (this.currentPage > 1) {\r\n          this.currentPage--;\r\n          this.fetchRankingData();\r\n        }\r\n      },\r\n      \r\n      // 下一页\r\n      nextPage() {\r\n        if (this.currentPage < this.totalPages) {\r\n          this.currentPage++;\r\n          this.fetchRankingData();\r\n        }\r\n      },\r\n      \r\n      // 获取学校列表\r\n      getCategories() {\r\n        app.get('/ApiBusiness/getBusinessCategories', {}, (res) => {\r\n          if (res.status === 1 && res.data && res.data.length > 0) {\r\n            // 添加所有学校选项\r\n            this.categoryOptions = [{ id: '', name: '全部学校' }].concat(res.data);\r\n          } else {\r\n            console.error('获取学校列表失败:', res.msg || '未知错误');\r\n          }\r\n        });\r\n      },\r\n      \r\n      // 获取排名数据\r\n      fetchRankingData() {\r\n        uni.showLoading({\r\n          title: '加载中...'\r\n        });\r\n        \r\n        const params = {\r\n          ranking_type: this.rankingType,\r\n          pagenum: this.currentPage,\r\n          pernum: this.perPage\r\n        };\r\n        \r\n        // 添加日期筛选\r\n        if (this.currentDateType.value && this.dateValue) {\r\n          params.date_type = this.currentDateType.value;\r\n          params.date_value = this.dateValue;\r\n        }\r\n        \r\n        // 添加学校筛选\r\n        if (this.rankingType === 'business' && this.currentCategory.id) {\r\n          params.category_id = this.currentCategory.id;\r\n        }\r\n        \r\n        // 添加搜索关键词\r\n        if (this.keyword) {\r\n          params.business_keyword = this.keyword;\r\n        }\r\n        \r\n        app.get('ApiBusiness/salesRanking', params, (res) => {\r\n          uni.hideLoading();\r\n          \r\n          if (res.status === 1) {\r\n            this.rankingList = res.data || [];\r\n            this.totalCount = res.total_count || 0;\r\n          } else {\r\n            app.error(res.msg || '获取数据失败');\r\n            this.rankingList = [];\r\n            this.totalCount = 0;\r\n          }\r\n        }, () => {\r\n          uni.hideLoading();\r\n          app.error('网络请求失败');\r\n        });\r\n      }\r\n    }\r\n  };\r\n  </script>\r\n  \r\n  <style>\r\n  /* 全局背景和字体 */\r\n  .data-ranking-container {\r\n    padding: 0;\r\n    min-height: 100vh;\r\n    background-color: #ffffff;\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\r\n  }\r\n  \r\n  /* 顶部背景和标题区域 */\r\n  .banner-area {\r\n    position: relative;\r\n    height: 400rpx;\r\n    background: linear-gradient(to bottom, #33d695, #00c480);\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .radial-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-image: repeating-linear-gradient(0deg, transparent, transparent 20px, rgba(255,255,255,0.05) 20px, rgba(255,255,255,0.05) 40px);\r\n    transform: rotate(45deg) scale(5);\r\n    opacity: 0.7;\r\n  }\r\n  \r\n  .title-area {\r\n    position: absolute;\r\n    top: 80rpx;\r\n    left: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .main-title {\r\n    font-size: 60rpx;\r\n    font-weight: 600;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);\r\n  }\r\n  \r\n  .trophy-container {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -30%);\r\n    width: 240rpx;\r\n    height: 280rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .trophy-icon {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .trophy-cup {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .trophy-top {\r\n    position: absolute;\r\n    top: 10rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 140rpx;\r\n    height: 40rpx;\r\n    background-color: #ffcc33;\r\n    border-radius: 70rpx 70rpx 0 0;\r\n    box-shadow: 0 -4rpx 0 rgba(0,0,0,0.1) inset;\r\n  }\r\n  \r\n  .trophy-middle {\r\n    position: absolute;\r\n    top: 50rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 120rpx;\r\n    height: 130rpx;\r\n    background-color: #ffcc33;\r\n    border-radius: 10rpx;\r\n    box-shadow: 0 0 0 10rpx #ffcc33, \r\n                -25rpx 0 0 -3rpx #e6b800,\r\n                25rpx 0 0 -3rpx #ffdb66;\r\n  }\r\n  \r\n  .trophy-bottom {\r\n    position: absolute;\r\n    top: 180rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 70rpx;\r\n    height: 30rpx;\r\n    background-color: #ffcc33;\r\n    border-radius: 0 0 10rpx 10rpx;\r\n  }\r\n  \r\n  .trophy-base {\r\n    position: absolute;\r\n    top: 210rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 120rpx;\r\n    height: 20rpx;\r\n    background-color: #ffb833;\r\n    border-radius: 10rpx;\r\n    box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.2);\r\n  }\r\n  \r\n  .trophy-decorations {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n  }\r\n  \r\n  .decoration {\r\n    position: absolute;\r\n  }\r\n  \r\n  .left-leaf {\r\n    position: absolute;\r\n    top: 90rpx;\r\n    left: 20rpx;\r\n    width: 40rpx;\r\n    height: 120rpx;\r\n    background-color: #00aa66;\r\n    border-radius: 50% 20rpx 50% 20rpx;\r\n    transform: rotate(-20deg);\r\n    opacity: 0.9;\r\n  }\r\n  \r\n  .right-leaf {\r\n    position: absolute;\r\n    top: 90rpx;\r\n    right: 20rpx;\r\n    width: 40rpx;\r\n    height: 120rpx;\r\n    background-color: #00aa66;\r\n    border-radius: 20rpx 50% 20rpx 50%;\r\n    transform: rotate(20deg);\r\n    opacity: 0.9;\r\n  }\r\n  \r\n  .top-sparkle {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 60rpx;\r\n    width: 20rpx;\r\n    height: 20rpx;\r\n    background-color: white;\r\n    border-radius: 50%;\r\n    box-shadow: 0 0 10rpx white;\r\n    animation: sparkle 2s infinite alternate;\r\n  }\r\n  \r\n  .bottom-sparkle {\r\n    position: absolute;\r\n    bottom: 60rpx;\r\n    left: 40rpx;\r\n    width: 15rpx;\r\n    height: 15rpx;\r\n    background-color: white;\r\n    border-radius: 50%;\r\n    box-shadow: 0 0 8rpx white;\r\n    animation: sparkle 1.5s 0.5s infinite alternate;\r\n  }\r\n  \r\n  @keyframes sparkle {\r\n    0% {\r\n      opacity: 0.4;\r\n      transform: scale(0.8);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  \r\n  /* 彩旗装饰 */\r\n  .banner-flags {\r\n    position: absolute;\r\n    bottom: 25rpx;\r\n    left: -160rpx;\r\n    width: 560rpx;\r\n    height: 60rpx;\r\n    z-index: 0;\r\n  }\r\n  \r\n  .flag-string {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 2rpx;\r\n    background-color: rgba(255, 255, 255, 0.7);\r\n  }\r\n  \r\n  .flag {\r\n    position: absolute;\r\n    top: 0;\r\n    width: 30rpx;\r\n    height: 40rpx;\r\n    border-radius: 2rpx;\r\n  }\r\n  \r\n  .flag-1 {\r\n    left: 40rpx;\r\n    background-color: #ff6666;\r\n    transform: rotate(-5deg);\r\n  }\r\n  \r\n  .flag-2 {\r\n    left: 120rpx;\r\n    background-color: #ffcc33;\r\n    transform: rotate(8deg);\r\n  }\r\n  \r\n  .flag-3 {\r\n    left: 200rpx;\r\n    background-color: #66ccff;\r\n    transform: rotate(-7deg);\r\n  }\r\n  \r\n  .flag-4 {\r\n    left: 280rpx;\r\n    background-color: #ff9966;\r\n    transform: rotate(5deg);\r\n  }\r\n  \r\n  .flag-5 {\r\n    left: 360rpx;\r\n    background-color: #99cc66;\r\n    transform: rotate(-3deg);\r\n  }\r\n  \r\n  .top-text {\r\n    position: absolute;\r\n    bottom: 40rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    font-size: 60rpx;\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    letter-spacing: 5rpx;\r\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);\r\n    z-index: 1;\r\n    background-color: #00aa66;\r\n    padding: 2rpx 40rpx;\r\n    border-radius: 10rpx;\r\n  }\r\n  \r\n  /* 排名类型切换 */\r\n  .type-switcher {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    margin: 0;\r\n    background-color: #33d695;\r\n    padding: 0;\r\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);\r\n  }\r\n  \r\n  .type-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    padding: 30rpx 10rpx;\r\n    font-size: 32rpx;\r\n    color: rgba(255,255,255,0.8);\r\n    position: relative;\r\n    transition: all 0.3s ease;\r\n  }\r\n  \r\n  .type-item.active {\r\n    color: #ffffff;\r\n    font-weight: 600;\r\n    background-color: transparent;\r\n    position: relative;\r\n  }\r\n  \r\n  .type-item.active::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 30%;\r\n    width: 40%;\r\n    height: 6rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 3rpx;\r\n  }\r\n  \r\n  /* 筛选栏 */\r\n  .filter-bar {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 20rpx;\r\n    padding: 30rpx 20rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 0;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: none;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  \r\n  .filter-item {\r\n    margin-right: 0;\r\n    margin-bottom: 0;\r\n    flex-grow: 1;\r\n  }\r\n  \r\n  .picker-content {\r\n    background-color: #f9f9f9;\r\n    border-radius: 30rpx;\r\n    padding: 16rpx 30rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    border: 1px solid #eeeeee;\r\n    box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.02);\r\n  }\r\n  \r\n  .iconxiala {\r\n    margin-left: 10rpx;\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n  }\r\n  \r\n  /* 搜索栏 */\r\n  .search-bar {\r\n    display: flex;\r\n    padding: 0 20rpx 20rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 0;\r\n    margin-bottom: 10rpx;\r\n  }\r\n  \r\n  .search-input {\r\n    flex: 1;\r\n    height: 76rpx;\r\n    background-color: #f9f9f9;\r\n    border-radius: 38rpx;\r\n    padding: 0 30rpx;\r\n    font-size: 28rpx;\r\n    border: 1px solid #eeeeee;\r\n  }\r\n  \r\n  .search-btn {\r\n    width: 160rpx;\r\n    height: 76rpx;\r\n    line-height: 76rpx;\r\n    margin-left: 20rpx;\r\n    background-color: #33d695;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n    text-align: center;\r\n    border-radius: 38rpx;\r\n    box-shadow: 0 4rpx 8rpx rgba(51, 214, 149, 0.3);\r\n  }\r\n  \r\n  /* 数据列表 */\r\n  .ranking-list {\r\n    background-color: #ffffff;\r\n    border-radius: 0;\r\n    margin: 0 20rpx 30rpx;\r\n    box-shadow: none;\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .list-header {\r\n    display: flex;\r\n    padding: 25rpx 20rpx;\r\n    background-color: #ffffff;\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n    font-weight: 400;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  \r\n  .header-rank {\r\n    width: 120rpx;\r\n    text-align: center;\r\n  }\r\n  \r\n  .header-info {\r\n    flex: 1;\r\n    padding-left: 10rpx;\r\n  }\r\n  \r\n  .header-sales {\r\n    width: 220rpx;\r\n    text-align: right;\r\n    padding-right: 20rpx;\r\n  }\r\n  \r\n  .header-detail {\r\n    width: 100rpx;\r\n    text-align: center;\r\n  }\r\n  \r\n  .list-content {\r\n    max-height: 900rpx;\r\n  }\r\n  \r\n  .list-item {\r\n    display: flex;\r\n    padding: 25rpx 20rpx;\r\n    border-bottom: 1px solid #f8f8f8;\r\n    align-items: center;\r\n  }\r\n  .list-item:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .rank-num {\r\n    width: 120rpx;\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n    color: #333333;\r\n  }\r\n  \r\n  .rank-num.top-rank {\r\n    color: #ff9500;\r\n    font-size: 38rpx;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .item-info {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-left: 10rpx;\r\n  }\r\n  \r\n  .item-logo {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border-radius: 10rpx;\r\n    margin-right: 20rpx;\r\n    border: 1px solid #f0f0f0;\r\n  }\r\n  \r\n  .item-name {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    font-weight: 400;\r\n  }\r\n  \r\n  .item-sales {\r\n    width: 220rpx;\r\n    text-align: right;\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n    color: #ff6666;\r\n    padding-right: 20rpx;\r\n  }\r\n  \r\n  .item-detail {\r\n    width: 100rpx;\r\n    text-align: center;\r\n    font-size: 26rpx;\r\n    color: #33d695;\r\n    font-weight: 400;\r\n  }\r\n  \r\n  /* 空状态 */\r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 120rpx 0;\r\n    background-color: #ffffff;\r\n  }\r\n  \r\n  .empty-icon {\r\n    width: 220rpx;\r\n    height: 220rpx;\r\n    margin-bottom: 30rpx;\r\n    opacity: 0.7;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n  \r\n  /* 分页 */\r\n  .pagination {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 30rpx 0 80rpx;\r\n  }\r\n  \r\n  .page-info {\r\n    font-size: 26rpx;\r\n    color: #999999;\r\n    margin-bottom: 25rpx;\r\n  }\r\n  \r\n  .page-controls {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .page-btn {\r\n    padding: 12rpx 35rpx;\r\n    background-color: #f8f8f8;\r\n    color: #666666;\r\n    font-size: 26rpx;\r\n    border-radius: 6rpx;\r\n    border: 1px solid #eeeeee;\r\n    margin: 0 10rpx;\r\n  }\r\n  \r\n  .page-btn.disabled {\r\n    color: #cccccc;\r\n    border-color: #f0f0f0;\r\n    background-color: #f8f8f8;\r\n  }\r\n  \r\n  .page-current {\r\n    margin: 0 25rpx;\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    font-weight: 500;\r\n  }\r\n  </style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sales-ranking.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sales-ranking.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098477\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}