{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?5a3e", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?39bc", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?7ecb", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?00d9", "uni-app:///yuyue/product.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?d2fe", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/product.vue?c6b2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "textset", "pre_url", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "console", "imageUrl", "query", "onUnload", "clearInterval", "uni", "methods", "getdata", "that", "app", "id", "swiper<PERSON><PERSON>e", "payvideo", "setTimeout", "parsevideo", "buydialogChange", "currgg", "switchTopTab", "date", "proid", "switchDateTab", "selectDate", "addfavorite", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "chooseTime", "currentTarget", "dataset", "btntype", "hidetimeDialog", "itemList", "success", "scene", "sharedata", "sharelink", "ggid", "num", "yydate", "order_flow_mode"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqShxB;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,wDACA,oDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,mDACA,gEACA,wDACA,oDACA,wDACA,iDACA,+CACA,iDACA,yDACA,gDACA,qDACA,sDACA,uDACA,qDACA,wDAEA,wDACA,yDACA,uDACA,sDACA,mDACA,+DACA,2DACA,4DACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAmBA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACAE;IACAA;IACA;MACAH;MACAI;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;MACAC;MACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACA;QACAT;QAEAQ;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;QACAA;QACAR;QAEAK;UACAR;QACA;QACA;UACA,yCACA;YACAW;YACAA;UACA;UACAA;UACAA;UACAA;QACA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAX;UAAAC;QAAA;MACA;IACA;IACAa;MACA;MACAH;IACA;IACAI;MACA;MACA;MACAC;QACAR;MACA;IACA;IACAS;MACA;MACA;MACAT;IACA;IACAU;MAAA;MACA;MAEA;QACA;MACA;MACA;;MAEA;MACA;QACAF;UACA;QACA;;QAEA;QACA;MACA;IACA;IACAG;MACA;MACA;MACAR;MACAA;MACAA;;MAEA;MACAA;;MAEA;MACA;QACA;QACAA;QACAK;UACAL;QACA;MACA;QACA;QACAA;QACAK;UACAL;QACA;MACA;IACA;IACAS;MACA;MACA;MACA;MACA;MACAT;MACAA;MACA;MACA;MACA;MACA;MACAA;MACAC;QAAAS;QAAAC;MAAA;QACAX;QACAA;MACA;IAEA;IACAY;MACA;MACA;MACA;MACA;MACA;QACAX;QAAA;MACA;MACAD;MACAA;MACA;MACAA;MACA;QAAAA;MAAA;MACAA;IACA;IACAa;MAAA;MACA;MACA;QACAb;MACA;MACA;QACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAI;UACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACA;MACAb;QAAAU;QAAAI;MAAA;QACA;UACAf;QACA;QACAC;MACA;IACA;IACAe;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAlB;MACAA;MACAC;MACAA;QAAAU;MAAA;QACAV;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEAmB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAzB;;QAEA;QACA;UACAA;UACA;UACAK;YACAR;UACA;QACA;MACA;MACA;QACAG;;QAEA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACA0B;MACA;MACA;QACAzB;QACA;UAAA0B;YAAAC;cAAAC;YAAA;UAAA;QAAA;QACA;MACA;MAEA;MACA7B;MACAA;MACA;MACAA;MACAA;MACAC;QAAAS;QAAAC;MAAA;QACAX;QACAA;MACA;IACA;IACA8B;MACA;IACA;EAAA,6DAEA;IACA;EACA,8DACA;IACA;EACA,uDACA;IACA7B;IACA;EACA,wDACA;IACA;IACAD;IACAH;MACAkC;MACAC;QACA;UACA;UACA;YACAC;UACA;UACA;UACAC;UACAA;UACAA;UACAA;UACA;UACAA;UACAA;UACA;UACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACA;kBACA;kBACA;oBACAC;kBACA;kBACA;oBACAA;kBACA;kBACAD;gBACA;cACA;YACA;UACA;UACArC;QACA;MACA;IACA;EACA,6DACA;IACA;EACA,8DACA;IACA;EACA,qDACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAL;MACAmB;MACAyB;MACAC;MACAC;MACAC;IACA;IAEA;IACA;MACAtC;MACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA;MACAA;IACA;MACA;MACAA;IACA;EACA,iEACA;IAEA;IACA;MAEA;MACA;MACA;QAEA;QACA;MACA;MAEA;IACA;MAEA;QAAA0B;UAAAC;YAAAC;UAAA;QAAA;MAAA;IACA;EACA,yDACA;IACA;EAAA,CACA,yDACA;IACA;EACA,0DACA;IACA;EACA;AAGA;AAAA,2B;;;;;;;;;;;;;ACryBA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=746c2fef&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/product.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=template&id=746c2fef&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    yybuydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/yybuydialog/yybuydialog\" */ \"@/components/yybuydialog/yybuydialog.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var g1 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g2 =\n    _vm.isload && _vm.isplay == 0 && g1 > 1 ? _vm.product.pics.length : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var g3 = _vm.isload ? _vm.fuwulist.length : null\n  var g4 = _vm.isload && g3 > 0 ? _vm.fuwulist.length : null\n  var g5 = _vm.isload ? _vm.couponlist.length : null\n  var l0 =\n    _vm.isload && g5 > 0\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.t(\"color1rgb\")\n          var m4 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var m5 = _vm.isload && _vm.commentcount > 0 ? _vm.t(\"color1\") : null\n  var g6 = _vm.isload && _vm.commentcount > 0 ? _vm.commentlist.length : null\n  var m6 =\n    _vm.isload && _vm.product.status == 1 && _vm.isfuwu ? _vm.t(\"color1\") : null\n  var m7 =\n    _vm.isload && _vm.product.status == 1 && !_vm.isfuwu\n      ? _vm.t(\"color1\")\n      : null\n  var m8 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m9 =\n    _vm.isload && _vm.sharetypevisible && !(m8 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m10 =\n    _vm.isload && _vm.sharetypevisible && !(m8 == \"app\") && !(m9 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var l1 =\n    _vm.isload && _vm.timeDialogShow\n      ? _vm.__map(_vm.datelist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 = _vm.isload && _vm.timeDialogShow ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m2: m2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        l0: l0,\n        m5: m5,\n        g6: g6,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        l1: l1,\n        m12: m12,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"sysset.showgzts\">\n\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\n\t\t\t<view class=\"follow_topbar\">\n\t\t\t\t<view class=\"headimg\"><image :src=\"sysset.logo\"/></view>\n\t\t\t\t<view class=\"info\">\n\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\n\t\t\t</view>\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\n\t\t\t\t<view class=\"qrcodebox\">\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\"/>\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\n\t\t\t\t\t\t<image :src=\"pre_url + '/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</uni-popup>\n\t\t</block>\n\t\t\n\t\t<dp-guanggao :guanggaopic=\"guanggaopic\" :guanggaourl=\"guanggaourl\"></dp-guanggao>\n\n\t\t<view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'/yuyue/product?id=' + item.proid\" class=\"flex-y-center\">\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\n\t\t\t\t\t<view style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</view>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t</view>\n\t\t<!-- <view class=\"topbox\"><image src=\"/static/img/goback.png\" class=\"goback\" /></view> -->\n\t\t<view class=\"swiper-container\" v-if=\"isplay==0\" >\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"500000\" @change=\"swiperChange\">\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\n\t\t\t\t\t</swiper-item>\n\t\t\t\t</block>\n\t\t\t</swiper>\n\t\t\t<view class=\"imageCount\" v-if=\"(product.pics).length > 1\">{{current+1}}/{{(product.pics).length}}</view>\n\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\"><image :src=\"pre_url + '/static/img/video.png'\"/><view class=\"txt\">{{product.video_duration}}</view></view>\n\t\t</view>\n\t\n\t\t<!-- 添加视频播放组件 -->\n\t\t<view v-if=\"isplay==1\" class=\"videobox\" :class=\"{'float-video': isFloatVideo}\">\n\t\t\t<video id=\"myVideo\" class=\"video\" :src=\"product.video\" :controls=\"true\" :autoplay=\"true\" :show-center-play-btn=\"false\" @play=\"videoPlay\" @pause=\"videoPause\" @ended=\"videoEnded\"></video>\n\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">关闭</view>\n\t\t</view>\n\n\t\t<view class=\"header\"> \n\t\t\t<view class=\"price_share\">\n\t\t\t\t<view class=\"title\">{{product.name}}</view>\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url + '/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\n\t\t\t</view>\n\t\t\t<view class=\"pricebox flex\">\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text><text style=\"font-size:24rpx;font-weight:normal;padding-left:6rpx\">元/{{product.danwei}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"sales_stock\">\n\t\t\t\t\t<view class=\"f1\" >已售：{{product.sales}} </view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\n\t\t\n\t\t\t<view style=\"margin:20rpx 0;color:red;font-size:22rpx\" v-if=\"product.balance_price > 0\">定金金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\n\t\t\t\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"fuwulist.length>0\">\n\t\t\t\t<view class=\"fuwupoint\" v-if=\"fuwulist.length>0\">\n\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t<image :src=\"pre_url + '/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"cuxiaopoint\" v-if=\"couponlist.length>0\">\n\t\t\t<view class=\"f0\">优惠</view>\n\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t</view>\n\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t<image :src=\"pre_url + '/static/img/arrow-point.png'\" mode=\"widthFix\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"choosebox\">\n\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\n\t\t\t\t<view class=\"f0\">选择服务</view>\n\t\t\t\t<view class=\"f1 flex1\">{{ggname}} <text v-if=\"num\">× {{num}}</text></view>\n\t\t\t\t<image class=\"f2\" :src=\"pre_url + '/static/img/arrowright.png'\"/>\n\t\t\t</view>\n\t\t\t<view v-if=\"!isfuwu && order_flow_mode === 0\" class=\"choosedate\" @tap=\"chooseTime\">\n\t\t\t\t<view class=\"f0\">服务时间</view>\n\t\t\t\t<view class=\"f1 flex1\">{{days}} {{selectDates}}</view>\n\t\t\t\t<image class=\"f2\" :src=\"pre_url + '/static/img/arrowright.png'\"/>\n\t\t\t</view>\n\t\t\t<view v-if=\"order_flow_mode === 1\" class=\"choosedate\">\n\t\t\t\t<view class=\"f0\">预约流程</view>\n\t\t\t\t<view class=\"f1 flex1\" style=\"color:#ff4d4f\">先支付后预约</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"commentbox\" v-if=\"commentcount > 0\">\n\t\t\t<view class=\"title\">\n\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评率 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url + '/static/img/arrowright.png'\"/></view>\n\t\t\t</view>\n\t\t\t<view class=\"comment\">\n\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\n\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\n\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url + '/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\n\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\n\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">服务详情</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\n\t\t<view class=\"detail\">\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\n\t\t</view>\n\t\t\n\t\t<!-- #ifdef MP-TOUTIAO -->\n\t\t<view class=\"dp-cover\" v-if=\"video_status\">\n\t\t\t<button open-type=\"share\" data-channel=\"video\" class=\"dp-cover-cover\" :style=\"{\n\t\t\t\tzIndex:10,\n\t\t\t\ttop:'60vh',\n\t\t\t\tleft:'80vw',\n\t\t\t\twidth:'110rpx',\n\t\t\t\theight:'110rpx'\n\t\t\t}\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/uploadvideo2.png'\" :style=\"{width:'110rpx',height:'110rpx'}\"/>\n\t\t\t</button>\n\t\t</view>\n\t\t<!-- #endif -->\n\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':''\" v-if=\"product.status==1\">\n\t\t\t<view class=\"f1\">\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'prolist?bid=' + product.bid\" >\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/shou.png'\"/>\n\t\t\t\t\t<view class=\"t1\">首页</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/kefu.png'\"/>\n\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/kefu.png'\"/>\n\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t</button>\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url + (isfavorite ? '/static/img/shoucang2.png' : '/static/img/shoucang.png')\"/>\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\">\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" v-if=\"isfuwu\" @tap=\"handleAppointment\">立即预约</view>\n\t\t\t\t<view v-else class=\"tobuy flex-x-center flex-y-center\" @tap=\"handleAppointment\" :style=\"{background:t('color1')}\">立即预约</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<yybuydialog v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" :order_flow_mode=\"order_flow_mode\" @currgg=\"currgg\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\" @addcart=\"addcart\" :isfuwu=\"isfuwu\" @tobuy=\"tobuy\"></yybuydialog>\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\n\n\t\t\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\n\t\t\t\t<!-- <view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"sharetypecontent\">\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/weixin.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/weixin.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/weixin.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/weixin.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/sharepic.png'\"/>\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\n\t\t\t<view class=\"main\">\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url + '/static/img/close.png'\"/></view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view v-if=\"timeDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidetimeDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择时间</text>\n\t\t\t\t\t<image :src=\"pre_url + '/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\**********=\"hidetimeDialog\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-tab\">\n\t\t\t\t\t<view class=\"order-tab2\">\n\t\t\t\t\t\t<block v-for=\"(item, index) in datelist\" :key=\"index\">\n\t\t\t\t\t\t\t<view  :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\" >\n\t\t\t\t\t\t\t\t<view class=\"datetext\">{{item.weeks}}</view>\n\t\t\t\t\t\t\t\t<view class=\"datetext2\">{{item.date}}</view>\n\t\t\t\t\t\t\t\t<view class=\"after\"  :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t\t\t</view>\t\t\t\n\t\t\t\t\t\t</block>\n\t\t\t\t\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex daydate\">\n\t\t\t\t\t<block v-for=\"(item,index2) in timelist\" :key=\"index2\">\n\t\t\t\t\t\t<view :class=\"'date ' + ((timeindex==index2 && item.status==1) ? 'on' : '') + (item.status==0 ?'hui' : '') \"  @tap=\"switchDateTab\" :data-index2=\"index2\" :data-status=\"item.status\" :data-time=\"item.timeint\"> {{item.time}}</view>\t\t\t\t\t\t\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"op\">\n\t\t\t\t\t<button class=\"tobuy on\" :style=\"{backgroundColor:t('color1')}\" @tap=\"selectDate\" >确 定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\n\t\t\t\t\t\t<image :src=\"pre_url + '/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidefuwudetail\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\n\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt:{},\n\t\t\tloading:false,\n\t\t\tisload: false,\n\t\t\tmenuindex:-1,\n\t\t\ttextset:{},\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tisload:false,\n\t\t\tbuydialogShow: false,\n\t\t\tbtntype:1,\n\t\t\tisfavorite: false,\n\t\t\tcurrent: 0,\n\t\t\tisplay: 0,\n\t\t\tshowcuxiaodialog: false,\n\t\t\tshowfuwudialog:false,\n\t\t\tbusiness: \"\",\n\t\t\tproduct: [],\n\t\t\tcartnum: \"\",\n\t\t\tcommentlist: \"\",\n\t\t\tcommentcount: \"\",\n\t\t\tcuxiaolist: \"\",\n\t\t\tcouponlist: \"\",\n\t\t\tfuwulist: [],\n\t\t\tpagecontent: \"\",\n\t\t\tshopset: {},\n\t\t\tsysset:{},\n\t\t\ttitle: \"\",\n\t\t\tbboglist: \"\",\n\t\t\tsharepic: \"\",\n\t\t\tsharetypevisible: false,\n\t\t\tshowposter: false,\n\t\t\tposterpic: \"\",\n\t\t\tscrolltopshow: false,\n\t\t\tkfurl:'',\n\t\t\tggname:'请选择服务',\n\t\t\ttimeDialogShow: false,\n\t\t\tdatelist:[],\n\t\t\tdaydate:[],\n\t\t\tcurTopIndex: 0,\n\t\t\tindex:0,\n\t\t\tday: -1,\n\t\t\tdays:'请选择服务时间',\n\t\t\tdates:'',\n\t\t\tnum:0,\n\t\t\ttimeindex:-1,\n\t\t\tstartTime:0,\n\t\t\tselectDates:'',\n\t\t\ttimelist:[],\n\t\t\t\n\t\t\tguanggaopic: \"\",\n\t\t\tguanggaourl: \"\",\n\t\t\tvideo_status:0,\n\t\t\tvideo_title:'',\n\t\t\tvideo_tag:[],\n\t\t\tisfuwu:false,\n\t\t\torder_flow_mode: 0,\n\t\t\tserviceSelected: false,\n\t\t\tisFloatVideo: false\n\t\t};\n\t},\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShareAppMessage:function(shareOption){\n\t\t//#ifdef MP-TOUTIAO\n\t\tconsole.log(shareOption);\n\t\t\treturn {\n\t\t\t\t\n\t\t\t\ttitle: this.video_title,\n\t\t\t\tchannel: \"video\",\n\t\t\t\textra: {\n\t\t\t\t        hashtag_list: this.video_tag,\n\t\t\t\t      },\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log(\"分享成功\");\n\t\t\t\t},\n\t\t\t\t fail: (res) => {\n\t\t\t\t    console.log(res);\n\t\t\t\t    // 可根据 res.errCode 处理失败case\n\t\t\t\t  },\n\t\t\t};\n\t\t//#endif\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\n\t},\n\tonShareTimeline:function(){\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\n\t\tvar query = (sharewxdata.path).split('?')[1];\n\t\tconsole.log(sharewxdata)\n\t\tconsole.log(query)\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n\tonUnload: function () {\n\t\tclearInterval(interval);\n\t\t// 确保离开页面时停止视频播放\n\t\tif (this.isplay === 1) {\n\t\t\tuni.createVideoContext('myVideo').stop();\n\t\t\tthis.isplay = 0;\n\t\t\tthis.isFloatVideo = false;\n\t\t}\n\t},\n\n\tmethods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar id = this.opt.id || 0;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiYuyue/product', {id: id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 打印完整接口返回数据结构便于调试\n\t\t\t\tconsole.log(\"接口返回数据:\", JSON.stringify(res));\n\t\t\t\t\n\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\tvar product = res.product;\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\n\t\t\t\tthat.business = res.business;\n\t\t\t\tthat.product = product;\n\t\t\t\tthat.cartnum = res.cartnum;\n\t\t\t\tthat.commentlist = res.commentlist;\n\t\t\t\tthat.commentcount = res.commentcount;\n\t\t\t\tthat.pagecontent = pagecontent;\n\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\tthat.title = product.name;\n\t\t\t\tthat.isfavorite = res.isfavorite;\n\t\t\t\tthat.fuwulist = res.fuwulist2;\n\t\t\t\tthat.sharepic = product.pics[0];\n\t\t\t\tthat.isfuwu = res.isfuwu;\n\t\t\t\t\n\t\t\t\t// 修正获取order_flow_mode的方式,优先从product对象中获取\n\t\t\t\tvar flowMode = product.order_flow_mode || res.order_flow_mode || 0;\n\t\t\t\tthat.order_flow_mode = parseInt(flowMode);\n\t\t\t\tconsole.log(\"修正后的流程模式:\", that.order_flow_mode, typeof that.order_flow_mode);\n\t\t\t\t\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: product.name\n\t\t\t\t});\n\t\t\t\tif(res.set){\n\t\t\t\t\tif(res.set.ad_status && res.set.ad_pic)\n\t\t\t\t\t{\t\n\t\t\t\t\t\tthat.guanggaopic = res.set.ad_pic;\n\t\t\t\t\t\tthat.guanggaourl = res.set.ad_link;\n\t\t\t\t\t}\n\t\t\t\t\tthat.video_status = res.set.video_status;\n\t\t\t\t\tthat.video_title = res.set.video_title;\n\t\t\t\t\tthat.video_tag = res.set.video_tag;\n\t\t\t\t}\n\t\t\t\tthat.datelist = res.datelist;\n\t\t\t\tthat.daydate = res.daydate;\n\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid='+product.bid;\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\n\t\t\t\t}\n\t\t\t\tif(that.business && that.business.kfurl){\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\n\t\t\t\t}\n\t\t\t\tthat.loaded({title:product.name,pic:product.pic});\n\t\t\t});\n\t\t},\n\t\tswiperChange: function (e) {\n\t\t\tvar that = this;\n\t\t\tthat.current = e.detail.current\n\t\t},\n\t\tpayvideo: function () {\n\t\t\tthis.isplay = 1;\n\t\t\t// 添加一点延时确保DOM已更新\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.createVideoContext('myVideo').play();\n\t\t\t}, 100);\n\t\t},\n\t\tparsevideo: function () {\n\t\t\tthis.isplay = 0;\n\t\t\tthis.isFloatVideo = false;\n\t\t\tuni.createVideoContext('myVideo').stop();\n\t\t},\n\t\tbuydialogChange: function (e) {\n\t\t\tvar wasShowing = this.buydialogShow;\n\t\t\t\n\t\t\tif(!this.buydialogShow && e && e.currentTarget){\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype;\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t\t\n\t\t\t// 如果弹窗从显示状态变为隐藏状态，且已选择服务，且不是\"先支付后预约\"模式，则自动弹出时间选择\n\t\t\tif (wasShowing && !this.buydialogShow && this.serviceSelected && this.order_flow_mode !== 1 && !this.isfuwu) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.chooseTime();\n\t\t\t\t}, 300); // 延迟300毫秒，让服务选择弹窗先关闭\n\t\t\t\t\n\t\t\t\t// 重置服务选择状态，避免下次关闭弹窗时再次触发时间选择\n\t\t\t\tthis.serviceSelected = false;\n\t\t\t}\n\t\t},\n\t\tcurrgg: function (e) {\n\t\t\tvar that = this\n\t\t\tthis.ggname = e.ggname;\n\t\t\tthat.ggid = e.ggid\n\t\t\tthat.proid = e.proid\n\t\t\tthat.num = e.num\n\t\t\t\n\t\t\t// 保存服务选择状态\n\t\t\tthat.serviceSelected = true;\n\t\t\t\n\t\t\t// 如果需要选择时间（非先支付后预约模式），则在服务选择后自动弹出时间选择\n\t\t\tif (that.order_flow_mode !== 1 && !that.isfuwu) {\n\t\t\t\t// 关闭服务选择弹窗并打开时间选择\n\t\t\t\tthat.buydialogShow = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthat.chooseTime(); // 打开时间选择窗口\n\t\t\t\t}, 300);\n\t\t\t} else {\n\t\t\t\t// 如果是先支付后预约模式或者是服务类型，直接进入下一步\n\t\t\t\tthat.buydialogShow = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthat.tobuy(); // 直接进入下一步\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t},\n\t\tswitchTopTab: function (e) {\n\t\t  var that = this;\n\t\t  var id = e.currentTarget.dataset.id;\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\n\t\t  this.curTopIndex = index;\n\t\t  that.days = that.datelist[index].year+that.datelist[index].date\n\t\t  that.nowdate = that.datelist[index].nowdate\n\t\t   // if(!that.dates ){ that.dates = that.daydate[0] }\n\t\t  this.curIndex = -1;\n\t\t  this.curIndex2 = -1;\n\t\t  //检测预约时间是否可预约\n\t\t  that.loading = true;\n\t\t  app.get('ApiYuyue/isgetTime', { date: that.days,proid:this.opt.id}, function (res) {\n\t\t\t  that.loading = false;\n\t\t\t  that.timelist = res.data;\n\t\t  })\n\t\t\t\n\t\t},\n\t\tswitchDateTab: function (e) {\n\t\t  var that = this;\n\t\t  var index2 = parseInt(e.currentTarget.dataset.index2);\n\t\t  var timeint = e.currentTarget.dataset.time\n\t\t  var status = e.currentTarget.dataset.status\n\t\t  if(status==0){\n\t\t\t\tapp.error('此时间不可选择');return;\t\n\t\t  }\n\t\t  that.timeint = timeint\n\t\t  that.timeindex = index2\n\t\t\t//console.log(that.timelist);\n\t\t  that.starttime1 = that.timelist[index2].time\n\t\t  if(!that.days || that.days=='请选择服务时间'){ that.days = that.datelist[0].year + that.datelist[0].date }\n\t\t  that.selectDates = that.starttime1;\n\t\t},\n\t\tselectDate: function(e) {\n\t\t\tvar that = this\n\t\t\tif(that.timeindex >= 0 && that.timelist[that.timeindex].status==0){\n\t\t\t\tthat.starttime1 = '';\n\t\t\t}\n\t\t\tif(!that.starttime1){\n\t\t\t\tapp.error('请选择预约时间');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 关闭时间选择弹窗\n\t\t\tthis.timeDialogShow = false;\n\t\t\t\n\t\t\t// 如果已经选择了服务和时间，直接进入下一步\n\t\t\tif(this.ggid && this.ggid !== undefined) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.tobuy();\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t},\n\t\t//收藏操作\n\t\taddfavorite: function () {\n\t\t\tvar that = this;\n\t\t\tvar proid = that.product.id;\n\t\t\tapp.post('ApiYuyue/addfavorite', {proid: proid,type: 'yuyue'}, function (data) {\n\t\t\t\tif (data.status == 1) {\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\n\t\t\t\t}\n\t\t\t\tapp.success(data.msg);\n\t\t\t});\n\t\t},\n\t\tshareClick: function () {\n\t\t\tthis.sharetypevisible = true;\n\t\t},\n\t\thandleClickMask: function () {\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshowPoster: function () {\n\t\t\tvar that = this;\n\t\t\tthat.showposter = true;\n\t\t\tthat.sharetypevisible = false;\n\t\t\tapp.showLoading('生成海报中');\n\t\t\tapp.post('ApiYuyue/getposter', {proid: that.product.id}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (data.status == 0) {\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t} else {\n\t\t\t\t\tthat.posterpic = data.poster;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tposterDialogClose: function () {\n\t\t\tthis.showposter = false;\n\t\t},\n\t\tshowfuwudetail: function () {\n\t\t\tthis.showfuwudialog = true;\n\t\t},\n\t\thidefuwudetail: function () {\n\t\t\tthis.showfuwudialog = false\n\t\t},\n\t\tshowcuxiaodetail: function () {\n\t\t\tthis.showcuxiaodialog = true;\n\t\t},\n\t\thidecuxiaodetail: function () {\n\t\t\tthis.showcuxiaodialog = false\n\t\t},\n\t\tgetcoupon:function(){\n\t\t\tthis.showcuxiaodialog = false;\n\t\t\tthis.getdata();\n\t\t},\n\t\tonPageScroll: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar scrollY = e.scrollTop;     \n\t\t\tif (scrollY > 200) {\n\t\t\t\tthat.scrolltopshow = true;\n\t\t\t\t\n\t\t\t\t// 当页面滚动超过视频区域时，如果视频正在播放则显示浮动视频\n\t\t\t\tif (that.isplay === 1) {\n\t\t\t\t\tthat.isFloatVideo = true;\n\t\t\t\t\t// 确保视频持续播放\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.createVideoContext('myVideo').play();\n\t\t\t\t\t}, 100);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(scrollY < 150) {\n\t\t\t\tthat.scrolltopshow = false;\n\t\t\t\t\n\t\t\t\t// 当页面回到顶部附近时，如果是浮动视频则恢复正常大小\n\t\t\t\tif (that.isplay === 1) {\n\t\t\t\t\tthat.isFloatVideo = false;\n\t\t\t\t}\n\t\t\t}\n\t\t},\t\n\t\t\n\t\t//选择时间 \n\t\tchooseTime: function(e) {\n\t\t\t// 防止重复打开服务选择弹窗\n\t\t\tif(!this.ggid || this.ggid == undefined) {\n\t\t\t\tapp.error('请先选择服务');\n\t\t\t\tthis.buydialogChange({currentTarget:{dataset:{btntype:2}}});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tvar that = this;\n\t\t\tthat.timeDialogShow = true;\n\t\t\tthat.timeIndex = -1;\n\t\t\tvar curTopIndex = that.datelist[0];\n\t\t\tthat.nowdate = that.datelist[that.curTopIndex].year+that.datelist[that.curTopIndex].date;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiYuyue/isgetTime', { date: that.nowdate,proid:this.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.timelist = res.data;\n\t\t\t});\n\t\t},\t\n\t\thidetimeDialog: function() {\n\t\t\tthis.timeDialogShow = false;\n\t\t},\n\t\t\n\t\tshowfuwudetail: function () {\n\t\t\tthis.showfuwudialog = true;\n\t\t},\n\t\thidefuwudetail: function () {\n\t\t\tthis.showfuwudialog = false\n\t\t},\n\t\tsharemp:function(){\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\tthis.sharetypevisible = false\n\t\t},\n\t\tshareapp:function(){\n\t\t\tvar that = this;\n\t\t\tthat.sharetypevisible = false;\n\t\t\tuni.showActionSheet({\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\n        success: function (res){\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\tsharedata.title = that.product.name;\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/yuyue/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\tif(sharelist){\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/yuyue/product'){\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t}\n        }\n      });\n\t\t},\n\t\tshowsubqrcode:function(){\n\t\t\tthis.$refs.qrcodeDialog.open();\n\t\t},\n\t\tclosesubqrcode:function(){\n\t\t\tthis.$refs.qrcodeDialog.close();\n\t\t},\n\t\ttobuy: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar ks = that.ks;\n\t\t\tvar proid = that.product.id;\n\t\t\tvar ggid = that.ggid;\n\t\t\tvar num = that.num;\n\t\t\tvar yydate = that.days+' '+that.selectDates\n\n\t\t\t// 添加日志便于调试\n\t\t\tconsole.log(\"tobuy方法被调用:\", {\n\t\t\t\tproid,\n\t\t\t\tggid,\n\t\t\t\tnum,\n\t\t\t\tyydate,\n\t\t\t\torder_flow_mode: that.order_flow_mode\n\t\t\t});\n\n\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\n\t\t\tif(!ggid || ggid==undefined){ \n\t\t\t\tapp.error('请选择服务'); \n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 确保order_flow_mode是数字类型\n\t\t\tvar flowMode = that.order_flow_mode; // 已在getdata方法中转换为数字类型\n\t\t\t\n\t\t\t// 根据流程模式判断是否需要选择预约时间\n\t\t\tif(flowMode !== 1){\n\t\t\t\t// 此代码已在handleAppointment中处理，不再需要重复判断\n\t\t\t\tapp.goto('/yuyue/buy?prodata=' + prodata +'&yydate='+yydate);\n\t\t\t} else {\n\t\t\t\t// 先支付后预约模式直接跳转到下单页\n\t\t\t\tapp.goto('/yuyue/buy?prodata=' + prodata +'&order_flow_mode=1');\n\t\t\t}\n\t\t},\n\t\thandleAppointment: function () {\n\t\t\t\n\t\t\t// 检查是否已选择服务\n\t\t\tif(this.ggid && this.ggid !== undefined) {\n\t\t\t\t\n\t\t\t\t// 检查是否需要选择时间（非先支付后预约模式）且未选择时间\n\t\t\t\tvar yydate = this.days+' '+this.selectDates;\n\t\t\t\tif(this.order_flow_mode !== 1 && (!yydate || yydate==='请选择服务时间 ' || yydate==='请选择服务时间')) {\n\t\t\t\t\t\n\t\t\t\t\tthis.chooseTime();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.tobuy();\n\t\t\t} else {\n\t\t\t\t\n\t\t\t\tthis.buydialogChange({currentTarget:{dataset:{btntype:2}}});\n\t\t\t}\n\t\t},\n\t\tvideoPlay: function () {\n\t\t\t// this.isFloatVideo = true; // 移除这行，播放时不直接触发浮动\n\t\t},\n\t\tvideoPause: function () {\n\t\t\tthis.isFloatVideo = false;\n\t\t},\n\t\tvideoEnded: function () {\n\t\t\tthis.isFloatVideo = false;\n\t\t}\n\t}\n\n};\n</script>\n<style>\n/* 设置页面全局样式 */\nuni-page-body {\n    font-size: 14px;\n    background: #ffffff;\n}\n\t.dp-cover{height: auto; position: relative;}\n\t.dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}\n\t\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}\n.follow_topbar .info {height:80rpx; float:left;}\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.qrcodebox .img{width:400rpx;height:400rpx}\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}\n.goback img{ width:64rpx ; height: 64rpx;}\n\n.swiper-container{position:relative}\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\n.swiper-item-view{width: 100%;height: 750rpx;}\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\n\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:80rpx;}\n\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\n.provideo image{width:50rpx;height:50rpx;}\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\n\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\n.videobox .video{width:100%;height:650rpx;}\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx;}\n\n.header {padding: 20rpx 0; background: #fff; width: 100%; border-radius:10rpx; margin: 0; position: relative;}\n.header .pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between; padding: 0 3%;}\n.header .pricebox .price{display:flex;align-items:flex-end}\n.header .pricebox .price .f1{font-size:36rpx;color:#51B539;font-weight:bold}\n.header .pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between; padding: 0 3%;}\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\n.header .sellpoint{font-size:28rpx;color: #666;padding:20rpx 3% 0 3%;}\n.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#777777; }\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\n\n.choosebox{/* margin: auto;width: 94%; */ width: 100%; border-radius:10rpx; background: #fff; }\n\n.choose{ display:flex;align-items:center;justify-content: center; /* margin: auto; */ height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; border-bottom:1px solid #eee }\n.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\n.choose .f2{ width: 32rpx; height: 32rpx;}\n\n\n.choosedate{ display:flex;align-items:center;ustify-content: center; /* margin:auto; */ height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }\n.choosedate .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\n.choosedate .f2{ width: 32rpx; height: 32rpx;}\n\n.cuxiaodiv{background:#fff;margin-top:20rpx;}\n\n.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\n.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\n.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\n.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}\n.fuwupoint .f1 .t:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\n.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\n.fuwupoint .f2 .img{width:32rpx;height:32rpx;}\n.fuwudialog-content{font-size:24rpx}\n.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}\n.fuwudialog-content .f1:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\n.fuwudialog-content .f2{color:#777}\n\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\n.cuxiaopoint .f1 .t1{padding:0 4px}\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\n\n/* 添加浮动视频的样式 */\n.float-video {\n  position: fixed;\n  bottom: 120rpx;\n  right: 20rpx;\n  width: 300rpx !important;\n  height: 180rpx !important;\n  z-index: 99;\n  background: #000;\n  border-radius: 10rpx;\n  box-shadow: 0 0 10rpx rgba(0,0,0,0.3);\n  transition: all 0.3s ease;\n}\n\n.float-video .video {\n  width: 100%;\n  height: 180rpx;\n}\n\n.float-video .parsevideo {\n  position: absolute;\n  top: -40rpx;\n  right: 0;\n  height: 40rpx;\n  line-height: 40rpx;\n  padding: 0 10rpx;\n  background: rgba(0,0,0,0.6);\n  color: #fff;\n  border-top-left-radius: 10rpx;\n  border-top-right-radius: 10rpx;\n  font-size: 20rpx;\n}\n\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\n.popup__title-text{font-size:32rpx}\n.popup__close{position:absolute;top:34rpx;right:34rpx}\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\n.service-item .prefix{padding-top: 2px;}\n.service-item .suffix{padding-left: 10rpx;}\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\n\n\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\n.shop .p2{padding-left:10rpx}\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\n\n.detail{min-height:200rpx; /* width: 94%; margin: auto; */ width:100%; padding:0 3%; border-radius: 10rpx;}\n\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\n\n.commentbox{/* width:90%; */ width:100%; background:#fff;padding:0 3%;border-radius:10rpx; /* margin: auto; */ margin-top:20rpx; }\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\n\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\n.comment .item .f1 .t3{text-align:right;}\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\n.comment .item .f2 .t2{display:flex;width:100%}\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\n\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 30rpx 0 10rpx;align-items:center;}\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:50%;position:relative}\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 36rpx; border: none;font-size:28rpx;font-weight:bold; background: #FD4A46;}\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\n\n.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}\n.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}\n.sharetypecontent button::after{border:0}\n.sharetypecontent .f1 .img{width:90rpx;height:90rpx}\n.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}\n.sharetypecontent .f2 .img{width:90rpx;height:90rpx}\n.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}\n\n.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}\n.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}\n.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}\n.posterDialog .close .img{ width:40rpx;height:40rpx;}\n.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\n.posterDialog .content .img{width:540rpx;height:960rpx}\n\n/*时间范围*/\n.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}\n.order-tab{ }\n.order-tab2{display:flex;width:auto;min-width:100%;overflow-x: scroll;}\n.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; width: 20%;}\n.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}\n.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}\n.order-tab2 .on{color:#222222;}\n.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}\n.order-tab2 .on .after{display:block}\n.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx; }\n.daydate .date{ width:20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}\n.daydate .on{ background:red; color:#fff;}\n.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 36rpx; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;background: #FD4A46;}\n\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\n.cuxiaopoint .f1 .t1{padding:0 4px}\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\n\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045863\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}