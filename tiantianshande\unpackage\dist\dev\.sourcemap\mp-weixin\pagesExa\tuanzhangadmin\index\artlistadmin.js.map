{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?a1d2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?07a2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?0682", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?43c8", "uni-app:///pagesExa/tuanzhangadmin/index/artlistadmin.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?031d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/artlistadmin.vue?b8df"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "datalist", "pagenum", "clist", "cnamelist", "cidlist", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "console", "that", "app", "bid", "cid", "uni", "title", "searchConfirm", "changetab", "scrollTop", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyMrxB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,gDACA,+CACA,oDACA,+CACA,sDACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAC;QAAAb;QAAAF;MAAA;QACAY;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACA;YACA;YACAR;YACAC;YACA;cACAD;cACAC;YACA;YACAO;YACAA;UACA;UAEAI;YACAC;UACA;UACAL;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAN;MACAA;IACA;IACAO;MACA;MACAH;QACAI;QACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7TA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/index/artlistadmin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/index/artlistadmin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./artlistadmin.vue?vue&type=template&id=7ec18072&\"\nvar renderjs\nimport script from \"./artlistadmin.vue?vue&type=script&lang=js&\"\nexport * from \"./artlistadmin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./artlistadmin.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/index/artlistadmin.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlistadmin.vue?vue&type=template&id=7ec18072&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    daihuotuanList: function () {\n      return import(\n        /* webpackChunkName: \"components/daihuotuan-list/daihuotuan-list\" */ \"@/components/daihuotuan-list/daihuotuan-list.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.clist.length > 0 && !_vm.look_type : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlistadmin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlistadmin.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的文章\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<dd-tab :itemdata=\"cnamelist\" :itemst=\"cidlist\" :st=\"cid\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"clist.length>0 && !look_type\"></dd-tab>\r\n\t\t\r\n\r\n\t\t<view class=\"article_list\">\r\n\t\t\t<!--横排-->\r\n\t\t\t<view v-if=\"listtype=='0'\" class=\"article-itemlist\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"article-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"article-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--双排-->\r\n\t\t\t<view v-if=\"listtype=='1'\" class=\"article-item2\" v-for=\"(item,index) in datalist\" :key=\"item.id\" :style=\"{marginRight:index%2==0?'2%':'0'}\" @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"article-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"article-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<daihuotuan-list v-if=\"listtype=='2'\" :list=\"datalist\" ref=\"waterfall\"></daihuotuan-list>\r\n\t\t\t<!--单排-->\r\n\t\t\t<view v-if=\"listtype=='3'\" class=\"article-item1\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"article-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"article-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                    <block v-if=\"item.po_status && item.po_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                            {{item.po_name}} {{item.po_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pt_status && item.pt_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                            {{item.pt_name}} {{item.pt_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pth_status && item.pth_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                            {{item.pth_name}} {{item.pth_content}}\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"item.pf_status && item.pf_status==1\">\r\n                    \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                            {{item.pf_name}} {{item.pf_content}}\r\n                        </view>\r\n                    </block>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n            <!-- 三排显示s -->\r\n            <view v-if=\"listtype=='4'\" class=\"article-item3\" v-for=\"(item,index) in datalist\" :key=\"item.id\" :style=\"{marginRight:(index+1)%3==0?'0':'2%'}\" @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n                <view class=\"article-info\">\r\n                \t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n                </view>\r\n                <view class=\"article-pic\">\r\n                \t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.po_status && item.po_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                        {{item.po_name}} {{item.po_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pt_status && item.pt_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                        {{item.pt_name}} {{item.pt_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pth_status && item.pth_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                        {{item.pth_name}} {{item.pth_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pf_status && item.pf_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                        {{item.pf_name}} {{item.pf_content}}\r\n                    </view>\r\n                </view>\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p2\">\r\n            \t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n            \t\t</view>\r\n                    <view class=\"p2\">\r\n                    \t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n                    </view>\r\n            \t</view>\r\n            </view>\r\n            <!-- 三排显示e -->\r\n            \r\n            <!--单排三图s-->\r\n            <view v-if=\"listtype=='5'\" class=\"article-item1\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n            \t</view>\r\n                <view class=\"article-pic\">\r\n                    <block v-if=\"item.pic\" v-for=\"(img,index) in item.pic\">\r\n                        <image class=\"image\" :src=\"img\" style=\"width: 220rpx;height: 220rpx;margin: 8rpx;\"/>\r\n                    </block>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.po_status && item.po_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.po_name\">\r\n                        {{item.po_name}} {{item.po_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pt_status && item.pt_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pt_name\">\r\n                        {{item.pt_name}} {{item.pt_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pth_status && item.pth_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pth_name\">\r\n                        {{item.pth_name}} {{item.pth_content}}\r\n                    </view>\r\n                </view>\r\n                <view class=\"article-info\" v-if=\"item.pf_status && item.pf_status==1\" style=\"padding:0rpx 20rpx;\">\r\n                \t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.pf_name\">\r\n                        {{item.pf_name}} {{item.pf_content}}\r\n                    </view>\r\n                </view>\r\n            \t<view class=\"article-info\">\r\n            \t\t<view class=\"p2\">\r\n            \t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n            \t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n            \t\t</view>\r\n            \t</view>\r\n            </view>\r\n            <!--单排三图e-->\r\n\t\t</view>\r\n\t</block>\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tnodata:false,\r\n\t\t\tnomore:false,\r\n\t\t\tkeyword:'',\r\n      datalist: [],\r\n      pagenum: 1,\r\n\t\t\tclist:[],\r\n\t\t\tcnamelist:[],\r\n\t\t\tcidlist:[],\r\n      datalist: [],\r\n      cid: 0,\r\n\t\t\tbid: 0,\r\n\t\t\tlisttype:0,\r\n            set:'',\r\n            look_type:false,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.cid = this.opt.cid || 0;\t\r\n\t\tthis.bid = this.opt.bid || 0;\r\n        this.look_type = this.opt.look_type || false;\r\n        if(this.opt.keyword) {\r\n        \tthis.keyword = this.opt.keyword;\r\n        }\r\n    this.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nomore && !this.nodata) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var keyword = that.keyword;\r\n      var cid = that.cid;\r\n\t\t\tconsole.log(cid)\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('Apidaihuoyiuan/getartlist', {bid:that.bid,cid: cid,pagenum: pagenum,keyword:keyword}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.listtype = res.listtype || 0;\r\n\t\t\t\t\tthat.clist    = res.clist;\r\n                    that.set      = res.set;\r\n\t\t\t\t\tif((res.clist).length > 0){\r\n\t\t\t\t\t\tvar cnamelist = [];\r\n\t\t\t\t\t\tvar cidlist = [];\r\n\t\t\t\t\t\tcnamelist.push('全部');\r\n\t\t\t\t\t\tcidlist.push('0');\r\n\t\t\t\t\t\tfor(var i in that.clist){\r\n\t\t\t\t\t\t\tcnamelist.push(that.clist[i].name);\r\n\t\t\t\t\t\t\tcidlist.push(that.clist[i].id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.cnamelist = cnamelist;\r\n\t\t\t\t\t\tthat.cidlist = cidlist;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.title\r\n\t\t\t\t\t});\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    searchConfirm: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword\r\n      that.getdata();\r\n    },\r\n    changetab: function (cid) {\r\n      this.cid = cid;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n\t\t\tif(this.listtype==2){\r\n\t\t\t\tthis.$refs.waterfall.refresh();\r\n\t\t\t}\r\n      this.getdata();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage{background:#f6f6f7}\r\n.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}\r\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}\r\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}\r\n\r\n.article_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}\r\n.article_list .article-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}\r\n.article_list .article-item1 .article-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}\r\n.article_list .article-item1 .article-pic .image{width: 100%;height:auto}\r\n.article_list .article-item1 .article-info {padding:10rpx 20rpx 20rpx 20rpx;}\r\n.article_list .article-item1 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.article_list .article-item1 .article-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}\r\n.article_list .article-item1 .article-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}\r\n.article_list .article-item1 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.article_list .article-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\r\n/*.article-item2:nth-child(even){margin-right:2%}*/\r\n.article_list .article-item2 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\r\n.article_list .article-item2 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.article_list .article-item2 .article-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\r\n.article_list .article-item2 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.article_list .article-item2 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.article_list .article-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}\r\n.article_list .article-itemlist .article-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}\r\n.article_list .article-itemlist .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.article_list .article-itemlist .article-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}\r\n.article_list .article-itemlist .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}\r\n.article_list .article-itemlist .article-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}\r\n\r\n.article_list .article-item3 {width: 32%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\r\n/*.article-item3:nth-child(even){margin-right:2%}*/\r\n.article_list .article-item3 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\r\n.article_list .article-item3 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.article_list .article-item3 .article-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\r\n.article_list .article-item3 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.article_list .article-item3 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlistadmin.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./artlistadmin.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115055382\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}