{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?82f1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?2401", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?cdad", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?0311", "uni-app:///pagesExt/tuozhanyuan/apply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?12e3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/apply.vue?d712"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "levelNames", "levelIndex", "selectedLevel", "isagree", "<PERSON><PERSON><PERSON><PERSON>", "idcard_front", "idcard_back", "idcard_hold", "other_files", "info", "tset", "levels", "showApplyConditions", "showUpgradeConditions", "applyConditionsList", "upgradeConditionsList", "simpleApply", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "console", "levelChange", "updateConditionsDisplay", "clickCondition", "clickUpgradeCondition", "subform", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "pics", "removeimg", "goBack", "delta", "handleConditionClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyL9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;;QAEA;QACA;QACA;QACA;UACAxB;;UAEA;UACA;YACAW;YACA;UACA;;UAEA;UACA;YACAA;YACA;UACA;;UAEA;UACA;YACA;cACAA;YACA;cACAc;cACAd;YACA;UACA;;UAEA;UACA;YACA;cACAA;YACA;cACAc;cACAd;YACA;UACA;QACA;QACAU;QACAA;;QAEA;QACAA;QACAA;QACAA;;QAEA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;UACA;YACA;cACAA;cACAA;cACAA;cACA;YACA;UACA;QACA;UACA;UACAA;UACAA;UACAA;QACA;QAEAA;MACA;IACA;IAEAK;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;QACArB;MACA;;MAEA;MACA;QACAa;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACAb;QACA;UACAa;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACAA;MACAA;QAAAb;MAAA;QACAa;QACA;UACAA;UACAS;YACAT;UACA;QACA;UACAA;QACA;MACA;IACA;IAEAU;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MAEA;MACA;MAEAb;QACA;UACAc;QACA;QACAf;MACA;IACA;IAEAgB;MACA;MACA;MACA;MACA;MACAD;MACAf;IACA;IAEAiB;MACAf;QACAgB;MACA;IACA;IAEAC;MACA;MACA;QACAlB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpeA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=7917548d&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/apply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=7917548d&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload && !_vm.simpleApply ? _vm.idcard_front.length : null\n  var g1 = _vm.isload && !_vm.simpleApply ? _vm.idcard_front.join(\",\") : null\n  var g2 = _vm.isload && !_vm.simpleApply ? _vm.idcard_back.length : null\n  var g3 = _vm.isload && !_vm.simpleApply ? _vm.idcard_back.join(\",\") : null\n  var g4 = _vm.isload && !_vm.simpleApply ? _vm.idcard_hold.length : null\n  var g5 = _vm.isload && !_vm.simpleApply ? _vm.idcard_hold.join(\",\") : null\n  var g6 = _vm.isload && !_vm.simpleApply ? _vm.other_files.join(\",\") : null\n  var m0 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <block v-if=\"isload\">\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==2\">\n        <parse :content=\"tset.verify_reject || '审核未通过：'\"/>{{info.reason}}，请修改后重新提交\n      </view>\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==0\">\n        <parse :content=\"tset.verify_notice || '您的拓展员申请已提交成功，请耐心等待审核，平台将于3个工作日内联系您核实信息，请留意来电'\"/>\n      </view>\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==1\">\n        <parse :content=\"tset.verify_success || '恭喜您审核通过！您已成为拓展员'\"/>\n      </view>\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\n        <parse :content=\"tset.verify_normal || '温馨提示：成为拓展员后可享受推广分佣权益'\"/>\n      </view>\n\n      <form @submit=\"subform\">\n        <view class=\"apply_box\">\n          <view class=\"apply_item\">\n            <view>真实姓名<text style=\"color:red\"> *</text></view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"realname\" :value=\"info.realname\" placeholder=\"请填写真实姓名\"></input></view>\n          </view>\n          <view class=\"apply_item\">\n            <view>联系电话<text style=\"color:red\"> *</text></view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写手机号码\"></input></view>\n          </view>\n          <view class=\"apply_item\">\n            <view>微信号<text style=\"color:red\"> *</text></view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"wechat\" :value=\"info.wechat\" placeholder=\"请填写微信号\"></input></view>\n          </view>\n          <view class=\"apply_item\">\n            <view>邀请码</view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"invite_code\" :value=\"info.invite_code\" placeholder=\"请填写邀请人邀请码（可选）\"></input></view>\n          </view>\n        </view>\n\n        <view class=\"apply_box\" v-if=\"!simpleApply\">\n          <view class=\"apply_item\">\n            <view>申请等级<text style=\"color:red\"> *</text></view>\n            <view>\n              <picker @change=\"levelChange\" :value=\"levelIndex\" :range=\"levelNames\">\n                <view class=\"picker\">{{levelNames[levelIndex] || '请选择申请等级'}}</view>\n              </picker>\n            </view>\n          </view>\n          <view class=\"apply_item\">\n            <view style=\"white-space: nowrap; min-width: 120rpx;\">申请理由<text style=\"color:red\"> *</text></view>\n            <view class=\"reason-container\">\n              <view class=\"flex-y-center\">\n                <textarea name=\"reason\"\n                          placeholder=\"请详细说明您的申请理由，如：推广经验、客户资源、市场优势等\"\n                          :value=\"info.reason\"\n                          style=\"height: 160rpx; font-size: 28rpx; line-height: 1.5;\">\n                </textarea>\n              </view>\n            </view>\n          </view>\n          <view class=\"apply_item\">\n            <view>详细地址<text style=\"color:red\"> *</text></view>\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"address\" :value=\"info.address\" placeholder=\"请输入详细地址\"></input></view>\n          </view>\n        </view>\n\n        <!-- 申请条件显示 -->\n        <view class=\"apply_box\" v-if=\"showApplyConditions && !simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\">\n            <text style=\"color: #333; font-weight: bold;\">申请条件</text>\n          </view>\n          <view class=\"condition-item\" v-for=\"(condition, index) in applyConditionsList\" :key=\"index\">\n            <view class=\"condition-text\">{{condition.name}}</view>\n            <view class=\"condition-status\"\n                  :class=\"condition.completed ? 'completed' : 'incomplete'\"\n                  @tap=\"clickCondition(condition, index)\">\n              {{condition.completed ? '已满足' : '未满足'}}\n              <text v-if=\"!condition.completed && condition.type === 'buy_product'\" style=\"font-size: 20rpx; margin-left: 8rpx;\">(点击购买)</text>\n            </view>\n          </view>\n          <view style=\"padding: 10rpx 0; font-size: 24rpx; color: #999;\">\n            <text>注：申请此等级需要满足以上所有条件</text>\n          </view>\n        </view>\n\n        <!-- 升级条件显示 -->\n        <view class=\"apply_box\" v-if=\"showUpgradeConditions && !simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\">\n            <text style=\"color: #333; font-weight: bold;\">升级条件</text>\n          </view>\n          <view class=\"condition-item\" v-for=\"(condition, index) in upgradeConditionsList\" :key=\"index\">\n            <view class=\"condition-text\">{{condition.name}}</view>\n            <view class=\"condition-status\"\n                  :class=\"condition.completed ? 'completed' : 'incomplete'\"\n                  @tap=\"clickUpgradeCondition(condition, index)\">\n              {{condition.completed ? '已完成' : '未完成'}}\n              <text v-if=\"!condition.completed && condition.type === 'buy_product'\" style=\"font-size: 20rpx; margin-left: 8rpx;\">(点击购买)</text>\n            </view>\n          </view>\n          <view style=\"padding: 10rpx 0; font-size: 24rpx; color: #999;\">\n            <text>注：成为此等级后，满足以上条件可升级到更高等级</text>\n          </view>\n        </view>\n\n        <view class=\"apply_box\" v-if=\"!simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>身份证正面<text style=\"color:red\"> *</text></text></view>\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n            <view v-for=\"(item, index) in idcard_front\" :key=\"index\" class=\"layui-imgbox\">\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"idcard_front\"><image src=\"/static/img/ico-del.png\"></image></view>\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n            </view>\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"idcard_front\" v-if=\"idcard_front.length==0\"></view>\n          </view>\n          <input type=\"text\" hidden=\"true\" name=\"idcard_front\" :value=\"idcard_front.join(',')\" maxlength=\"-1\"></input>\n        </view>\n\n        <view class=\"apply_box\" v-if=\"!simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>身份证反面<text style=\"color:red\"> *</text></text></view>\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n            <view v-for=\"(item, index) in idcard_back\" :key=\"index\" class=\"layui-imgbox\">\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"idcard_back\"><image src=\"/static/img/ico-del.png\"></image></view>\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n            </view>\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"idcard_back\" v-if=\"idcard_back.length==0\"></view>\n          </view>\n          <input type=\"text\" hidden=\"true\" name=\"idcard_back\" :value=\"idcard_back.join(',')\" maxlength=\"-1\"></input>\n        </view>\n\n        <view class=\"apply_box\" v-if=\"!simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>手持身份证照片<text style=\"color:red\"> *</text></text></view>\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n            <view v-for=\"(item, index) in idcard_hold\" :key=\"index\" class=\"layui-imgbox\">\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"idcard_hold\"><image src=\"/static/img/ico-del.png\"></image></view>\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n            </view>\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"idcard_hold\" v-if=\"idcard_hold.length==0\"></view>\n          </view>\n          <input type=\"text\" hidden=\"true\" name=\"idcard_hold\" :value=\"idcard_hold.join(',')\" maxlength=\"-1\"></input>\n        </view>\n\n        <view class=\"apply_box\" v-if=\"!simpleApply\">\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>其他证明材料</text></view>\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n            <view v-for=\"(item, index) in other_files\" :key=\"index\" class=\"layui-imgbox\">\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"other_files\"><image src=\"/static/img/ico-del.png\"></image></view>\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n            </view>\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"other_files\"></view>\n          </view>\n          <input type=\"text\" hidden=\"true\" name=\"other_files\" :value=\"other_files.join(',')\" maxlength=\"-1\"></input>\n        </view>\n\n        <block v-if=\"tset.xieyi_show==1\">\n          <view class=\"flex-y-center\" style=\"margin-left:20rpx;color:#999\" v-if=\"!info.id || info.status==2\">\n            <checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox value=\"1\" :checked=\"isagree\"></checkbox>阅读并同意</label></checkbox-group>\n            <text style=\"color:#666\" @tap=\"showxieyiFun\">《拓展员入驻协议》</text>\n          </view>\n        </block>\n\n        <view style=\"padding:30rpx 0\">\n          <button v-if=\"!info.id || info.status==2\" form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\n        </view>\n      </form>\n\n      <!-- 底部返回按钮 -->\n      <view class=\"content\">\n        <view class=\"back-button\" @tap=\"goBack\">\n          <text class=\"t1\">返回</text>\n        </view>\n      </view>\n\n      <!-- 协议弹窗 -->\n      <view id=\"xieyi\" :hidden=\"!showxieyi\" style=\"width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)\">\n        <view style=\"width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px\">\n          <view style=\"overflow:scroll;height:100%;\">\n            <parse :content=\"tset.xieyi\"/>\n          </view>\n          <view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center\" @tap=\"hidexieyi\">已阅读并同意</view>\n        </view>\n      </view>\n    </block>\n    <loading v-if=\"loading\"></loading>\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\n    <popmsg ref=\"popmsg\"></popmsg>\n  </view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      opt: {},\n      loading: false,\n      isload: false,\n      pre_url: app.globalData.pre_url,\n      levelNames: [],\n      levelIndex: 0,\n      selectedLevel: null,\n      isagree: false,\n      showxieyi: false,\n      idcard_front: [],\n      idcard_back: [],\n      idcard_hold: [],\n      other_files: [],\n      info: {},\n      tset: {},\n      levels: [],\n      showApplyConditions: false,\n      showUpgradeConditions: false,\n      applyConditionsList: [],\n      upgradeConditionsList: [],\n      simpleApply: false\n    };\n  },\n\n  onLoad: function (opt) {\n    this.opt = app.getopts(opt);\n    this.getdata();\n  },\n  \n  onPullDownRefresh: function () {\n    this.getdata();\n  },\n  \n  methods: {\n    getdata: function () {\n      var that = this;\n      that.loading = true;\n      app.get('ApiTuozhanyuan/apply', {}, function (res) {\n        that.loading = false;\n        if (res.status == 2) {\n          app.alert(res.msg, function () {\n            app.goto('/admin/index/index', 'redirect');\n          });\n          return;\n        }\n        uni.setNavigationBarTitle({\n          title: res.title || '拓展员申请'\n        });\n        \n        // 处理等级数据\n        var levels = res.levels || [];\n        var levelNames = [];\n        for (var i in levels) {\n          levelNames.push(levels[i].name);\n          \n          // 转换属性名：apply_conditions -> applyConditions\n          if (levels[i].apply_conditions) {\n            levels[i].applyConditions = levels[i].apply_conditions;\n            delete levels[i].apply_conditions;\n          }\n          \n          // 转换属性名：upgrade_conditions -> upgradeConditions\n          if (levels[i].upgrade_conditions) {\n            levels[i].upgradeConditions = levels[i].upgrade_conditions;\n            delete levels[i].upgrade_conditions;\n          }\n          \n          // 解析申请条件（如果是字符串格式）\n          if (levels[i].applyConditions && typeof levels[i].applyConditions === 'string') {\n            try {\n              levels[i].applyConditions = JSON.parse(levels[i].applyConditions);\n            } catch (e) {\n              console.log('解析申请条件失败:', e);\n              levels[i].applyConditions = [];\n            }\n          }\n          \n          // 解析升级条件（如果是字符串格式）\n          if (levels[i].upgradeConditions && typeof levels[i].upgradeConditions === 'string') {\n            try {\n              levels[i].upgradeConditions = JSON.parse(levels[i].upgradeConditions);\n            } catch (e) {\n              console.log('解析升级条件失败:', e);\n              levels[i].upgradeConditions = [];\n            }\n          }\n        }\n        that.levels = levels;\n        that.levelNames = levelNames;\n        \n        // 处理现有申请信息\n        that.tset = res.tset || {};\n        that.info = res.info || {};\n        that.simpleApply = res.simpleApply || false;\n        \n        // 处理图片数据\n        that.idcard_front = res.info && res.info.idcard_front ? res.info.idcard_front.split(',') : [];\n        that.idcard_back = res.info && res.info.idcard_back ? res.info.idcard_back.split(',') : [];\n        that.idcard_hold = res.info && res.info.idcard_hold ? res.info.idcard_hold.split(',') : [];\n        that.other_files = res.info && res.info.other_files ? res.info.other_files.split(',') : [];\n        \n        // 设置默认选中的等级\n        if (that.info.level_id) {\n          // 如果有现有申请，选择对应等级\n          for (var i = 0; i < that.levels.length; i++) {\n            if (that.levels[i].id == that.info.level_id) {\n              that.levelIndex = i;\n              that.selectedLevel = that.levels[i];\n              that.updateConditionsDisplay();\n              break;\n            }\n          }\n        } else if (that.levels.length > 0) {\n          // 如果没有现有申请，默认选择第一个等级\n          that.levelIndex = 0;\n          that.selectedLevel = that.levels[0];\n          that.updateConditionsDisplay();\n        }\n\n        that.loaded();\n      });\n    },\n\n    levelChange: function (e) {\n      this.levelIndex = e.detail.value;\n      this.selectedLevel = this.levels[this.levelIndex];\n      this.updateConditionsDisplay();\n    },\n\n    updateConditionsDisplay: function() {\n      if (this.selectedLevel) {\n        // 处理申请条件\n        if (this.selectedLevel.applyConditions && this.selectedLevel.applyConditions.length > 0) {\n          this.showApplyConditions = true;\n          this.applyConditionsList = this.selectedLevel.applyConditions;\n        } else {\n          this.showApplyConditions = false;\n          this.applyConditionsList = [];\n        }\n\n        // 处理升级条件\n        if (this.selectedLevel.upgradeConditions && this.selectedLevel.upgradeConditions.length > 0) {\n          this.showUpgradeConditions = true;\n          this.upgradeConditionsList = this.selectedLevel.upgradeConditions;\n        } else {\n          this.showUpgradeConditions = false;\n          this.upgradeConditionsList = [];\n        }\n      } else {\n        this.showApplyConditions = false;\n        this.showUpgradeConditions = false;\n        this.applyConditionsList = [];\n        this.upgradeConditionsList = [];\n      }\n    },\n\n    clickCondition: function(condition, index) {\n      this.handleConditionClick(condition);\n    },\n\n    clickUpgradeCondition: function(condition, index) {\n      this.handleConditionClick(condition);\n    },\n    \n    subform: function (e) {\n      var that = this;\n      var info = e.detail.value;\n      \n      // 添加 id 到表单数据中\n      if (that.info && that.info.id) {\n        info.id = that.info.id;\n      }\n      \n      // 验证必填项\n      if (info.realname == '') {\n        app.error('请填写真实姓名');\n        return false;\n      }\n      if (info.tel == '') {\n        app.error('请填写联系电话');\n        return false;\n      }\n      if (info.wechat == '') {\n        app.error('请填写微信号');\n        return false;\n      }\n\n      // 如果不是简单申请模式，验证其他必填项\n      if (!that.simpleApply) {\n        if (info.reason == '') {\n          app.error('请填写申请理由');\n          return false;\n        }\n        if (info.address == '') {\n          app.error('请填写详细地址');\n          return false;\n        }\n        if (info.idcard_front == '') {\n          app.error('请上传身份证正面');\n          return false;\n        }\n        if (info.idcard_back == '') {\n          app.error('请上传身份证反面');\n          return false;\n        }\n        if (info.idcard_hold == '') {\n          app.error('请上传手持身份证照片');\n          return false;\n        }\n      }\n      \n      // 设置申请等级\n      if (!that.simpleApply) {\n        info.level_id = that.selectedLevel ? that.selectedLevel.id : null;\n        if (!info.level_id) {\n          app.error('请选择申请等级');\n          return false;\n        }\n      }\n      \n      // 检查是否同意协议\n      if (that.tset.xieyi_show == 1 && !that.isagree) {\n        app.error('请先阅读并同意拓展员入驻协议');\n        return false;\n      }\n      \n      // 提交表单数据\n      app.showLoading('提交中');\n      app.post(\"ApiTuozhanyuan/apply\", { info: info }, function (res) {\n        app.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goto('/pages/index/index');\n          }, 1500);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n    \n    isagreeChange: function (e) {\n      var val = e.detail.value;\n      this.isagree = val.length > 0;\n    },\n    \n    showxieyiFun: function () {\n      this.showxieyi = true;\n    },\n    \n    hidexieyi: function () {\n      this.showxieyi = false;\n      this.isagree = true;\n    },\n    \n    uploadimg: function (e) {\n      var that = this;\n      var field = e.currentTarget.dataset.field;\n      var pics = that[field];\n      if (!pics) pics = [];\n      \n      var maxCount = 1;\n      if (field == 'other_files') maxCount = 5;\n      \n      app.chooseImage(function (urls) {\n        for (var i = 0; i < urls.length; i++) {\n          pics.push(urls[i]);\n        }\n        that[field] = pics;\n      }, maxCount);\n    },\n    \n    removeimg: function (e) {\n      var that = this;\n      var index = e.currentTarget.dataset.index;\n      var field = e.currentTarget.dataset.field;\n      var pics = that[field];\n      pics.splice(index, 1);\n      that[field] = pics;\n    },\n    \n    goBack: function () {\n      uni.navigateBack({\n        delta: 1\n      });\n    },\n\n    handleConditionClick: function (condition) {\n      // 如果是未满足的购买商品条件，跳转到商品页面\n      if (!condition.completed && condition.type === 'buy_product' && condition.product_id) {\n        app.goto('/shopPackage/shop/product?id=' + condition.product_id);\n      }\n    }\n  }\n};\n</script>\n\n<style>\nradio{transform: scale(0.6);}\ncheckbox{transform: scale(0.6);}\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\n.apply_title { background: #fff}\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\n\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\n.apply_box .apply_item:last-child{ border:none}\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\n.apply_item input::placeholder{ color:#999999}\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\n.layui-imgbox-close image{width:100%;height:100%}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n\n/* 返回按钮样式 */\n.back-button {\n  width: 90%;\n  background: #b60000;\n  color: #fff;\n  text-align: center;\n  height: 96rpx;\n  line-height: 96rpx;\n  border-radius: 50px;\n  margin-top: 0rpx;\n  margin: auto;\n}\n\n.back-button .t1 {\n  font-size: 30rpx;\n  color: #fff;\n}\n\n/* 升级条件样式 */\n.condition-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1px solid #eee;\n}\n\n.condition-text {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.condition-status {\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.condition-status.completed {\n  background: #e8f5e8;\n  color: #52c41a;\n}\n\n.condition-status.incomplete {\n  background: #fff2e8;\n  color: #fa8c16;\n}\n\n.picker {\n  text-align: right;\n  color: #111;\n  font-size: 28rpx;\n}\n\n/* 申请理由相关样式 */\n.reason-container {\n  width: 100%;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103562\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}