{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?27e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?da09", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?b514", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?115c", "uni-app:///pagesExa/tuanzhangadmin/index/mtuanlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?9490", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/mtuanlist.vue?326e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "datalist", "pagenum", "clist", "cnamelist", "cidlist", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "console", "that", "app", "bid", "cid", "item", "uni", "title", "searchConfirm", "changetab", "scrollTop", "duration", "contactMerchant", "phoneNumber", "success", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsFlxB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,gDACA,+CACA,oDACA,+CACA,sDACA,+DACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAA;MACAA;MACAC;QACAC;QACAC;QACAb;QACAF;MACA;QACAY;QACA;QACA;QACAD;;QAEA;QACA;UACAlB;YACA;YACA;cACAuB;YACA;cACAA;YACA;YACA;YACAL;YACA;cACAA;cACA;gBACA;kBACAK;gBACA;kBACAL;gBACA;cACA;cACA;gBACAK;gBACAL;cACA;YACA;UACA;QACA;QAEA;UACAC;UACAA;UACAA;UACA;YACA;YACA;YACAR;YACAC;YACA;cACAD;cACAC;YACA;YACAO;YACAA;UACA;UAEAK;YACAC;UACA;UACAN;UACA;UACAD;UACA;YACAC;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACAP;MACAA;IACA;IACAQ;MACA;MACAH;QACAI;QACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACAN;QACAO;QACAC;UACAd;QACA;QACAe;UACAf;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/PA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/index/mtuanlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/index/mtuanlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mtuanlist.vue?vue&type=template&id=7363a6a5&\"\nvar renderjs\nimport script from \"./mtuanlist.vue?vue&type=script&lang=js&\"\nexport * from \"./mtuanlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mtuanlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/index/mtuanlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mtuanlist.vue?vue&type=template&id=7363a6a5&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m3 = _vm.listtype == \"6\" ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m3: m3,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mtuanlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mtuanlist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"background-container\">\r\n\t\t\t\t<view class=\"header-bg\" :style=\"'background:'+t('color1')\"></view>\r\n\t\t\t\t<view class=\"header-gradient\" :style=\"'background-image: linear-gradient('+t('color1')+', #f1f1f1);'\">\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-container\">\r\n\t\t\t\t<view class=\"search-container\" :style=\"'background:'+t('color1')\">\r\n\t\t\t\t\t<view class=\"search-bar\">\r\n\t\t\t<!-- \t\t\t<image class=\"back-icon\" src=\"/static/img/arrow-left.png\"></image> -->\r\n\t\t\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的团\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"article_list\">\r\n\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!--单排三图e-->\r\n\t\t\t\t<!-- 新增类型6 -->\r\n\r\n\t\t\t\t<view style=\"position: absolute;top:50px;left: 0;width: 100%;\">\r\n\r\n\t\t\t\t\t<view v-if=\"listtype=='6'\" class=\"item-list\" v-for=\"(item, index) in datalist\" :key=\"item.id\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"item-header\" style=\"justify-content: space-between;\">\r\n\t\t\t\t\t\t\t<view style=\"display: flex;align-items: center;\">\r\n\t\t\t\t\t\t\t\t<image class=\"item-image\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"item-author\">{{item.author}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view :style=\"'background:'+t('color1')\" class=\"mbtn\" @click=\"goto\" :data-url=\"'./tuanedit?id='+item.id\">发布我的团</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @click=\"goto\" :data-url=\"'/daihuobiji/kuaituan/detail?id='+item.id\">\r\n\t\t\t\t\t\t\t<view class=\"item-name\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"item-price\">\r\n\t\t\t\t\t\t\t\t<text class=\"currency-symbol\">￥</text>\r\n\t\t\t\t\t\t\t\t<text class=\"price-value\">{{item.priceRange}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-pics\">\r\n\t\t\t\t\t\t\t\t<image class=\"item-pic\" v-for=\"(ite, ind) in item.pics\" :key=\"ind\" :src=\"ite\" v-if=\"ind<3\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size: 12px;color: #999;padding: 8px 0;border-top: 1px solid #f5f5f5;margin-top: 10px;\" v-if=\"item.totalCommission\">\r\n\t\t\t\t\t\t\t\t预估佣金：<text style=\"color: #FF6B00;\">￥{{item.totalCommission}}{{item.commission_desc || '元'}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-footer\">\r\n\t\t\t\t\t\t\t\t<view class=\"viewers\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"viewer-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsrc=\"https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png\"></image>\r\n\t\t\t\t\t\t\t\t\t<image class=\"viewer-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsrc=\"https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png\"></image>\r\n\t\t\t\t\t\t\t\t\t<image class=\"viewer-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsrc=\"https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"view-count\">{{item.readcount}}人看过</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"contact-btn\" @click.stop=\"goto\" :data-url=\"'/pagesExt/kefu/index?bid=' + (item.bid || 0)\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"contact-icon\" src=\"/static/img/phone.png\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"contact-text\">联系商家</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tnodata: false,\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tclist: [],\r\n\t\t\t\tcnamelist: [],\r\n\t\t\t\tcidlist: [],\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\tcid: 0,\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tlisttype: 0,\r\n\t\t\t\tset: '',\r\n\t\t\t\tlook_type: false,\r\n\t\t\t\ttotalCommission: 0,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.cid = this.opt.cid || 0;\r\n\t\t\tthis.bid = this.opt.bid || 0;\r\n\t\t\tthis.look_type = this.opt.look_type || false;\r\n\t\t\tif (this.opt.keyword) {\r\n\t\t\t\tthis.keyword = this.opt.keyword;\r\n\t\t\t}\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (!this.nomore && !this.nodata) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getdata(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function(loadmore) {\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\tvar cid = that.cid;\r\n\t\t\t\tconsole.log(cid)\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('Apidaihuoyiuan/getartlistfabu', {\r\n\t\t\t\t\tbid: that.bid,\r\n\t\t\t\t\tcid: cid,\r\n\t\t\t\t\tpagenum: pagenum,\r\n\t\t\t\t\tkeyword: keyword\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\t// 添加调试日志\r\n\t\t\t\t\tconsole.log('原始数据:', data);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保数据存在并处理图片和佣金字段\r\n\t\t\t\t\tif (data && data.length > 0) {\r\n\t\t\t\t\t\tdata.forEach((item) => {\r\n\t\t\t\t\t\t\t// 处理图片\r\n\t\t\t\t\t\t\tif (item.pic2) {\r\n\t\t\t\t\t\t\t\titem.pics = item.pic2.split(',');\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\titem.pics = [];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 处理佣金，添加调试日志\r\n\t\t\t\t\t\t\tconsole.log('处理前的item:', item);\r\n\t\t\t\t\t\t\tif (item.data) {\r\n\t\t\t\t\t\t\t\tconsole.log('item.data:', item.data);\r\n\t\t\t\t\t\t\t\tif (typeof item.data === 'string') {\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\titem.data = JSON.parse(item.data);\r\n\t\t\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('解析item.data失败:', e);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (item.data && item.data.commission) {\r\n\t\t\t\t\t\t\t\t\titem.commission = item.data.commission;\r\n\t\t\t\t\t\t\t\t\tconsole.log('设置佣金:', item.commission);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.listtype = res.listtype || 0;\r\n\t\t\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\t\t\tthat.set = res.set;\r\n\t\t\t\t\t\tif ((res.clist).length > 0) {\r\n\t\t\t\t\t\t\tvar cnamelist = [];\r\n\t\t\t\t\t\t\tvar cidlist = [];\r\n\t\t\t\t\t\t\tcnamelist.push('全部');\r\n\t\t\t\t\t\t\tcidlist.push('0');\r\n\t\t\t\t\t\t\tfor (var i in that.clist) {\r\n\t\t\t\t\t\t\t\tcnamelist.push(that.clist[i].name);\r\n\t\t\t\t\t\t\t\tcidlist.push(that.clist[i].id);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.cnamelist = cnamelist;\r\n\t\t\t\t\t\t\tthat.cidlist = cidlist;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\ttitle: res.title\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\t// 添加调试日志\r\n\t\t\t\t\t\tconsole.log('处理后的datalist:', that.datalist);\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsearchConfirm: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = e.detail.value;\r\n\t\t\t\tthat.keyword = keyword\r\n\t\t\t\tthat.getdata();\r\n\t\t\t},\r\n\t\t\tchangetab: function(cid) {\r\n\t\t\t\tthis.cid = cid;\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: 0,\r\n\t\t\t\t\tduration: 0\r\n\t\t\t\t});\r\n\t\t\t\tif (this.listtype == 2) {\r\n\t\t\t\t\tthis.$refs.waterfall.refresh();\r\n\t\t\t\t}\r\n\t\t\t\tthis.getdata();\r\n\t\t\t},\r\n\t\t\tcontactMerchant(phone) {\r\n\t\t\t\tif (!phone) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('拨打电话成功');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log('拨打电话失败', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground: #f6f6f7\r\n\t}\r\n\r\n\t.topsearch {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.topsearch .f1 {\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 35rpx;\r\n\t\tborder: 0;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tflex: 1;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.topsearch .f1 image {\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\tmargin-left: 10px\r\n\t}\r\n\r\n\t.topsearch .f1 input {\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\r\n\t.article_list {\r\n\t\tpadding: 10rpx 16rpx;\r\n\t\tbackground: #f6f6f7;\r\n\t\tmargin-top: 6rpx;\r\n\t}\r\n\r\n\t.article_list .article-item1 {\r\n\t\twidth: 100%;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-pic {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-pic .image {\r\n\t\twidth: 100%;\r\n\t\theight: auto\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-info {\r\n\t\tpadding: 10rpx 20rpx 20rpx 20rpx;\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-info .p1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-info .t1 {\r\n\t\tword-break: break-all;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-info .t2 {\r\n\t\tword-break: break-all;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tpadding-top: 4rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.article_list .article-item1 .article-info .p2 {\r\n\t\tflex-grow: 0;\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 10rpx 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #a88;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.article_list .article-item2 {\r\n\t\twidth: 49%;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t/*.article-item2:nth-child(even){margin-right:2%}*/\r\n\t.article_list .article-item2 .article-pic {\r\n\t\twidth: 100%;\r\n\t\theight: 0;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t\tpadding-bottom: 70%;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t}\r\n\r\n\t.article_list .article-item2 .article-pic .image {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: auto\r\n\t}\r\n\r\n\t.article_list .article-item2 .article-info {\r\n\t\tpadding: 10rpx 20rpx 20rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.article_list .article-item2 .article-info .p1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.article_list .article-item2 .article-info .p2 {\r\n\t\tflex-grow: 0;\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-top: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #a88;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.article_list .article-itemlist {\r\n\t\twidth: 100%;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tpadding: 12rpx;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.article_list .article-itemlist .article-pic {\r\n\t\twidth: 35%;\r\n\t\theight: 0;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t\tpadding-bottom: 25%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.article_list .article-itemlist .article-pic .image {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: auto\r\n\t}\r\n\r\n\t.article_list .article-itemlist .article-info {\r\n\t\twidth: 65%;\r\n\t\theight: auto;\r\n\t\toverflow: hidden;\r\n\t\tpadding: 0 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.article_list .article-itemlist .article-info .p1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t\theight: 92rpx\r\n\t}\r\n\r\n\t.article_list .article-itemlist .article-info .p2 {\r\n\t\tdisplay: flex;\r\n\t\tflex-grow: 0;\r\n\t\tflex-shrink: 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #a88;\r\n\t\toverflow: hidden;\r\n\t\tpadding-bottom: 6rpx\r\n\t}\r\n\r\n\t.article_list .article-item3 {\r\n\t\twidth: 32%;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t/*.article-item3:nth-child(even){margin-right:2%}*/\r\n\t.article_list .article-item3 .article-pic {\r\n\t\twidth: 100%;\r\n\t\theight: 0;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t\tpadding-bottom: 70%;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t}\r\n\r\n\t.article_list .article-item3 .article-pic .image {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: auto\r\n\t}\r\n\r\n\t.article_list .article-item3 .article-info {\r\n\t\tpadding: 10rpx 20rpx 20rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.article_list .article-item3 .article-info .p1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.article_list .article-item3 .article-info .p2 {\r\n\t\tflex-grow: 0;\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-top: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #a88;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.p3 {\r\n\t\tcolor: #8c8c8c;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\r\n\r\n\t.background-container {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.header-bg {\r\n\t\tbackground: #58A27E;\r\n\t\theight: 200px;\r\n\t}\r\n\r\n\t.header-gradient {\r\n\t\tbackground-image: linear-gradient(#58A27E, #f1f1f1);\r\n\t\theight: 100px;\r\n\t}\r\n\r\n\t.content-container {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.search-bar {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.back-icon {\r\n\t\twidth: 18px;\r\n\t\theight: 18px;\r\n\t}\r\n\r\n\t.search-container {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 23rpx;\r\n\t\tbackground: #5AA37B;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.search-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tflex: 1;\r\n\t/* \tmargin-left: 15px; */\r\n\t}\r\n\r\n\t.search-box .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.search-box .search-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #C2C2C2;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.top-nav {\r\n\t\tmargin-top: 30px;\r\n\t\tmargin-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0;\r\n\t\tbackground-color: #fff;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttext-align: center;\r\n\t\tflex: 1;\r\n\t\tbackground: #5AA37B;\r\n\t}\r\n\r\n\t.nav-icon {\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\tmargin-bottom: 6rpx;\r\n\t}\r\n\r\n\t.nav-label {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.item-list {\r\n\t\tbackground: #fff;\r\n\t\tmargin: 10px;\r\n\t\tpadding: 15px;\r\n\t\tborder-radius: 8px; \r\n\t}\r\n\r\n\t.item-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\tpadding-bottom: 10px;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.item-image {\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t\tborder-radius: 5px;\r\n\t}\r\n\r\n\t.item-author {\r\n\t\tmargin: 5px;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.item-name {\r\n\t\toverflow-wrap: break-word;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\r\n\t.item-price {\r\n\t\tmargin: 10px 0;\r\n\t\tcolor: red;\r\n\t}\r\n\r\n\t.currency-symbol {\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.price-value {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.item-pics {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.item-pic {\r\n\t\twidth: 100px;\r\n\t\theight: 100px;\r\n\t\tborder-radius: 8px;\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.item-footer {\r\n\t\tdisplay: flex;\r\n\t\tbackground: #F6F6F6;\r\n\t\tpadding: 10px;\r\n\t\tborder-radius: 5px;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.viewers {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.viewer-icon {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tborder-radius: 50px;\r\n\t\tmargin-left: -10px;\r\n\t}\r\n\r\n\t.view-count {\r\n\t\tfont-size: 12px;\r\n\t\tmargin-left: 10px;\r\n\t\tcolor: #507C85;\r\n\t}\r\n\r\n\t.share {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 10px;\r\n\t\tborder-left: 1px solid #ccc;\r\n\t}\r\n\r\n\t.share-icon {\r\n\t\twidth: 15px;\r\n\t\theight: 15px;\r\n\t}\r\n\r\n\t.share-text {\r\n\t\tfont-size: 12px;\r\n\t\tmargin-left: 10px;\r\n\t\tcolor: #507C85;\r\n\t}\r\n\t\r\n\t.mbtn{\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50px;\r\n\t\tpadding: 5px 10px;\r\n\t}\r\n\r\n\t.footer-actions {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.contact-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 5px 15px;\r\n\t\tbackground: #f1f1f1;\r\n\t\tborder-radius: 20px;\r\n\t}\r\n\r\n\t.contact-icon {\r\n\t\twidth: 16px;\r\n\t\theight: 16px;\r\n\t}\r\n\r\n\t.contact-text {\r\n\t\tfont-size: 12px;\r\n\t\tmargin-left: 5px;\r\n\t\tcolor: #507C85;\r\n\t}\r\n\r\n\t/* 修改share样式，移除左边框 */\r\n\t.share {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mtuanlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mtuanlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115055992\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}