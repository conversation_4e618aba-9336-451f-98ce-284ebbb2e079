{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?be80", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?2491", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?fe2c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?850c", "uni-app:///pagesExb/daxuepage/clist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?55ac", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/clist.vue?1b71"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "pagenum", "datalist", "clist", "curIndex", "curCid", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "scrolltolower", "getblist", "longitude", "latitude", "cid", "uni", "switchRightTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsE9wB;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,yDACA,qDACA,kDACA;EAEA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;MACAC;QACAD;QACAA;QACAA;QACAC;UACA;UACA;UACAD;UACAA;UACAA;QACA,GACA;UACAA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAH;MACAA;MACAA;MACAC;QAAAG;QAAAC;QAAAd;QAAAe;MAAA;QACAN;QACAO;QACA;QACA;UACAP;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACA;MACA;MACAR;MACAA;MACAA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/daxuepage/clist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/daxuepage/clist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./clist.vue?vue&type=template&id=79563654&\"\nvar renderjs\nimport script from \"./clist.vue?vue&type=script&lang=js&\"\nexport * from \"./clist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./clist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/daxuepage/clist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=template&id=79563654&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view @tap.stop=\"goto\" data-url=\"/pagesExt/business/blist\" class=\"search-container\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<view class=\"search-text\">搜索感兴趣的商家</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content-container\">\r\n\t\t\t<view class=\"nav_left\">\r\n\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == -1 ? 'active' : '')\" @tap=\"switchRightTab\" data-index=\"-1\" data-id=\"0\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>全部</view>\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t<view class=\"nav_right-content\">\r\n\t\t\t\t\t<scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.id\">\r\n\t\t\t\t\t\t\t<image class=\"logo\" :src=\"item.logo\"></image>\r\n\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\"><block v-if=\"item.tel\">电话：<text style=\"font-weight:bold\">{{item.tel}}</text></block></view>\r\n\t\t\t\t\t\t\t\t<view class=\"f4\">地址：<text style=\"font-weight:bold\">{{item.address}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.juli\">距离：{{item.juli}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<nomore text=\"没有更多商家了\" v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t\t<nodata text=\"暂无相关商家\" v-if=\"nodata\"></nodata>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\" loadstyle=\"left:62.5%\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n\r\n<!-- <view class=\"container\" :class=\"menuindex>-1?'tabbarbot':''\">\r\n\t<view class=\"nav_left\">\r\n    <block v-for=\"(item, index) in clist\" :key=\"index\">\r\n      <view :class=\"'nav_left_items ' + (curIndex == index ? 'active' : '')\" @tap=\"switchRightTab\" :data-index=\"index\" :data-id=\"item.id\">\r\n      {{item.name}}\r\n      </view>\r\n    </block>\r\n  </view>\r\n  <view class=\"nav_right\">\r\n    <scroll-view class=\"classify-box\" scroll-y=\"true\" @scrolltolower=\"scrolltolower\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.id\">\r\n\t\t\t\t<image :src=\"item.logo\"></image>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"f2\"><block v-if=\"item.tel\"><text class=\"fa fa-phone\"></text> {{item.tel}}</block></view>\r\n\t\t\t\t\t<view class=\"f4\"><text class=\"fa fa-map-marker\"></text> {{item.address}}</view>\r\n\t\t\t\t\t<view class=\"f3\" v-if=\"item.juli\">距离：{{item.juli}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n    </scroll-view>\r\n  </view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view> -->\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tnodata:false,\r\n\t\t\tnomore:false,\r\n      pagenum: 1,\r\n      datalist: [],\r\n      clist: [],\r\n      curIndex: -1,\r\n      curCid: 0,\r\n      nomore: false,\r\n      longitude: \"\",\r\n      latitude: \"\",\r\n      blist: \"\"\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.curCid = this.opt.cid || 0;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.pagenum = 1;\r\n\t\t\tthat.prolist = [];\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiBusiness/clist', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\t\tthat.getblist();\r\n\t\t\t\t},\r\n\t\t\t\tfunction () {\r\n\t\t\t\t\tthat.getblist();\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n    scrolltolower: function () {\r\n      if (!this.nodata && !this.nomore) {\r\n        this.pagenum = this.pagenum + 1;\r\n        this.getblist();\r\n      }\r\n    },\r\n    getblist: function () {\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var cid = that.curCid;\r\n      var longitude = that.longitude;\r\n      var latitude = that.latitude;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('ApiBusiness/clist', {longitude: longitude,latitude: latitude,pagenum: pagenum,cid: cid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    //事件处理函数\r\n    switchRightTab: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var index = parseInt(e.currentTarget.dataset.index);\r\n      that.curIndex = index;\r\n      that.curCid = id;\r\n      that.pagenum = 1;\r\n      that.blist = [];\r\n      this.getblist();\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {height:100%;}\r\n.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}\r\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\r\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\r\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\r\n\r\n.content-container{flex:1;height:100%;display:flex;overflow: hidden;}\r\n\r\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#999999;font-size:24rpx;position: relative;padding:25rpx 30rpx;}\r\n.nav_left .nav_left_items.active{background: #fff;color:#222222;font-size:28rpx;font-weight:bold}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}\r\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}\r\n.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}\r\n.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.nav_right .item{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f5f5f5 solid;position:relative}\r\n.nav_right .item:last-child{ border-bottom: 0; }\r\n.nav_right .item .logo{ width: 160rpx; height: 160rpx;}\r\n.nav_right .item .detail{width:100%;overflow:hidden;display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.nav_right .item .detail .f1{color:#323232;font-weight:bold;font-size:28rpx;line-height:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.nav_right .item .detail .f2{margin-top:6rpx;height: 40rpx;line-height: 40rpx;color: #888;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.nav_right .item .detail .f3{margin-top:6rpx;height: 40rpx;line-height: 40rpx;color: #31C88E;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.nav_right .item .detail .f4{margin-top:6rpx;line-height: 40rpx;color: #999;font-size:24rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.nav_right .item .detail .f5{margin-top:6rpx;display:flex;height: 35rpx;line-height: 35rpx;font-size:24rpx;color: #ff4246;font-size: 22rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n/*\r\n.nomore-footer-tips{background:#ffffff}\r\n.container {position:fixed;width: 100%;max-width:640px;background-color: #fff;color: #939393;top: 0;bottom: 0;}\r\n.nav_left{overflow-y: scroll;width: 25%;height: 100%;background: #f5f5f5; box-sizing: border-box;text-align: center;position: absolute; top:var(--window-top); left: 0;}\r\n.nav_left .nav_left_items{height:100rpx;line-height:100rpx;color:#666666;padding:0;border-bottom: 1px solid #E6E6E6;font-size:28rpx;position: relative;border-right:1px solid #E6E6E6}\r\n.nav_left .nav_left_items.active{background: #fff;color:#FC4343;border-left:3px solid #FC4343}\r\n\r\n.nav_right{position: absolute;display:flex;flex-direction:column;top: var(--window-top);right: 0;flex: 1;width:75%;height: 100%;padding:0px 20rpx 20rpx 20rpx ;background: #fff;box-sizing: border-box;overflow-y: hidden;}\r\n\r\n.classify-box{ width: 100%;overflow-y: scroll; height:100%;}\r\n.nav_right_items{ width:100%;border-bottom:1px #eeeeee solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }\r\n\r\n.nav_right .item{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f5f5f5 solid;position:relative}\r\n.nav_right .item:last-child{ border-bottom: 0; }\r\n.nav_right .item .logo{ width: 160rpx; height: 160rpx;}\r\n.nav_right .item .detail{width:100%;overflow:hidden;display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.nav_right .item .detail .f1{height: 40rpx;line-height: 40rpx;color: #111;}\r\n.nav_right .item .detail .f2{height: 40rpx;line-height: 40rpx;color: #888;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f3{height: 40rpx;line-height: 40rpx;color: #31C88E;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f4{height: 40rpx;line-height: 40rpx;color: #999;overflow: hidden;font-size: 24rpx;}\r\n.nav_right .item .detail .f5{display:flex;height: 35rpx;line-height: 35rpx;color: #ff4246;font-size: 22rpx}\r\n*/\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./clist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027547\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}