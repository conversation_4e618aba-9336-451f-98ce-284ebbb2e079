{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?7aa2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?0312", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?d0b3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?ec30", "uni-app:///pagesExt/tuozhanyuan/shagjigenjinjilu.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?f860", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shagjigenjinjilu.vue?1916"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "datalist", "pagenum", "nomore", "nodata", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "app", "setTimeout", "id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,gxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBzxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MACA;QACA;QACA;MACA;MAEA;MACA;QACAC;QACAC;UAAA;QAAA;QACA;MACA;MAEA;MACA;MACA;MAEAD;QAAAE;QAAAV;MAAA;QACA;QACA;QAEA;UACA;UACA;QACA;UACA;UACA;YACA;UACA;QACA;QAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,ikCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/shagjigenjinjilu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/shagjigenjinjilu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shagjigenjinjilu.vue?vue&type=template&id=a0bed29c&\"\nvar renderjs\nimport script from \"./shagjigenjinjilu.vue?vue&type=script&lang=js&\"\nexport * from \"./shagjigenjinjilu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shagjigenjinjilu.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/shagjigenjinjilu.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shagjigenjinjilu.vue?vue&type=template&id=a0bed29c&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.datalist.length : null\n  var g1 = _vm.isload && _vm.isload ? _vm.datalist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shagjigenjinjilu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shagjigenjinjilu.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n    <block v-if=\"isload\">\r\n        <view v-if=\"datalist.length === 0\" class=\"no-data\">暂无跟进记录</view>\r\n       <block v-if=\"isload\">\r\n             <view v-if=\"!datalist.length\" class=\"no-data\">暂无跟进记录</view>\r\n             <view v-for=\"(item, index) in datalist\" :key=\"item.id\" class=\"item\">\r\n               <view class=\"follow-up-content\">\r\n                 <text class=\"content\">{{ item.follow_up_content }}</text>\r\n                 <text class=\"date\">跟进时间：{{ item.follow_up_date }}</text>\r\n               </view>\r\n             </view>\r\n           </block>\r\n    </block>\r\n    \r\n    <nomore v-if=\"nomore\"></nomore>\r\n    <nodata v-if=\"nodata\"></nodata>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n      nodata: false,\r\n    };\r\n  },\r\n\r\n  onLoad(opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.getdata();\r\n  },\r\n  \r\n  onPullDownRefresh() {\r\n    this.getdata();\r\n  },\r\n  \r\n  onReachBottom() {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum++;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    getdata(loadmore) {\r\n      if (!loadmore) {\r\n        this.pagenum = 1;\r\n        this.datalist = [];\r\n      }\r\n\r\n      const id = this.opt.id;\r\n      if (!id) {\r\n        app.error('参数错误');\r\n        setTimeout(() => app.goback(), 1000);\r\n        return;\r\n      }\r\n\r\n      this.nodata = false;\r\n      this.nomore = false;\r\n      this.loading = true;\r\n\r\n      app.post('ApiTuozhancrm/getFollowUps', { id, pagenum: this.pagenum }, (res) => {\r\n        this.loading = false;\r\n        const data = res.data || [];\r\n        \r\n        if (this.pagenum === 1) {\r\n          this.datalist = data;\r\n          this.nodata = data.length === 0;\r\n        } else {\r\n          this.nomore = data.length === 0;\r\n          if (!this.nomore) {\r\n            this.datalist = this.datalist.concat(data);\r\n          }\r\n        }\r\n\r\n        this.isload = true; // 数据加载完毕\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.container { padding: 20rpx; }\r\n.item { width: 94%; margin: 0 3%; padding: 20rpx; background: #fff; margin-top: 20rpx; border-radius: 20rpx; }\r\n.product-item2 { display: flex; padding: 20rpx 0; border-bottom: 1px solid #E6E6E6; }\r\n.product-pic { width: 180rpx; height: 180rpx; background: #ffffff; overflow: hidden; }\r\n.product-pic image { width: 100%; height: 100%; }\r\n.product-info { flex: 1; padding: 5rpx 10rpx; }\r\n.p1 { word-break: break-all; text-overflow: ellipsis; overflow: hidden; height: 80rpx; line-height: 40rpx; font-size: 30rpx; color: #111; }\r\n.p2 { font-size: 32rpx; height: 40rpx; line-height: 40rpx; }\r\n.p2 .t2 { margin-left: 10rpx; font-size: 26rpx; color: #888; text-decoration: line-through; }\r\n.p3 { font-size: 24rpx; height: 50rpx; line-height: 50rpx; overflow: hidden; }\r\n.p3 .t1 { color: #aaa; font-size: 24rpx; }\r\n.p3 .t2 { color: #888; font-size: 24rpx; }\r\n.foot { display: flex; align-items: center; width: 100%; height: 100rpx; line-height: 100rpx; color: #999; font-size: 24rpx; }\r\n.no-data { text-align: center; color: #999; font-size: 24rpx; margin-top: 50rpx; }\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shagjigenjinjilu.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shagjigenjinjilu.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103401\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}