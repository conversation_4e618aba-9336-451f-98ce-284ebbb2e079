{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?52f4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?f41b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?a983", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?784d", "uni-app:///pagesExb/daxuepage/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?74c3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?9a9d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?aef8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/index.vue?8f3f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "speciality", "fractionalLine", "data", "opt", "loading", "isload", "menuindex", "pre_url", "is<PERSON><PERSON>", "st", "business", "countcomment", "couponcount", "pics", "pagenum", "datalist", "topbackhide", "nomore", "nodata", "title", "sysset", "guanggaopic", "guang<PERSON><PERSON>l", "pageinfo", "pagecontent", "showfw", "yuyue_clist", "yuyue_cid", "video_status", "video_title", "video_tag", "bset", "isfavorite", "articles", "tabbarList", "icon", "text", "switchIndex", "onLoad", "onPullDownRefresh", "onReachBottom", "onPageScroll", "uni", "onShareAppMessage", "that", "getdata", "app", "id", "pic", "res", "changetab", "scrollTop", "duration", "getDataList", "openLocation", "latitude", "longitude", "name", "scale", "phone", "phoneNumber", "fail", "<PERSON><PERSON><PERSON><PERSON>", "fnPreviewImage", "current", "urls", "loop", "longPressActions", "addfavorite", "proid", "type", "switchTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;AACA;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4P9wB;AAAA;EAEAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IAmBA;MACAxB;IACA;EACA;AAAA,wEACA;EACA;IACA;IACA;IACA;MACAyB;IACA;IACA;MACAA;IACA;EACA;AACA,oEACA;EACAC;IACA;IACA;IACAD;IACAE;MACAC;IACA;MACAH;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;MACAA;MACAA;MACA;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;MACA;MAEAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;QACAA;QACAA;MACA;MACA;QACAA;QACAA;QACAA;MACA;MAEAA;QACAzB;QACA6B;MACA;MAEA;QACAJ;QACAF;UACAvB;QACA;QACAyB;MACA;QACA;UACA;UACAE,kFACAG;UACA;QACA;QACA;UACA;UACAL;UAEAA;UACAA;UACAA;UAEAA;UACAA;UACAA;UACAF;YACAvB;UACA;QACA;UACA2B;QACA;MACA;IACA;EACA;EACAI;IACA;IACA;IACA;IACA;IACAR;MACAS;MACAC;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;IACA;IACA;IACAT;IACAA;IACAA;IACAE;MACAC;MACAtC;MACAK;MACAa;IACA;MACAiB;MACAF;MACA;MACA;QACAE;QACA;UACAA;QACA;MACA;QACA;UACAA;QACA;UACA;UACA;UACAA;QACA;MACA;IACA;EACA;EACAU;IACA;IACA;IACA;IACA;IACAZ;MACAa;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAjB;MACAkB;MACAC;IACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;EACAC;IACArB;MACAsB;MACAC;MACAC;MACAC;IACA;EACA;EACA;EACAC;IACA;IACAtB;MACAuB;MACAC;IACA;MACA;QACA1B;MACA;MACAE;IACA;EACA;EACA;EACAyB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1iBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/daxuepage/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/daxuepage/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=26f194b2&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/daxuepage/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=26f194b2&\"", "var components\ntry {\n  components = {\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.isdiy && _vm.switchIndex == 0 ? _vm.pics.length : null\n  var g1 = !_vm.isdiy && _vm.switchIndex == 2 ? _vm.articles.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isdiy\">\r\n\t\t\t<view :style=\"'display:flex;min-height: 100vh;flex-direction: column;background-color:' + pageinfo.bgcolor\">\r\n\t\t\t\t<view class=\"container\">\r\n\t\t\t\t\t<!-- <dp :pagecontent=\"pagecontent\" :menuindex=\"menuindex\"></dp> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<dp-guanggao :guanggaopic=\"guanggaopic\" :guanggaourl=\"guanggaourl\"></dp-guanggao>\r\n\t\t</block>\r\n\t\t<block v-else>\r\n\t\t\t<view v-if=\"switchIndex == 0\">\r\n\t\t\t\t<!-- 头部标题 -->\r\n\t\t\t\t<view class=\"flex\" style=\"padding: 30rpx;padding-right: 0;padding-bottom: 0;\">\r\n\t\t\t\t\t<view style=\"margin-right: 10rpx;\" v-if=\"business\">\r\n\t\t\t\t\t\t<image :src=\"business.logo\" style=\"width: 130rpx;height: 130rpx;\" alt=\"\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"flex: 1;\" v-if=\"business\">\r\n\t\t\t\t\t\t<view class=\"text-h2 text-bold\" style=\"margin-bottom: 10rpx;\">\r\n\t\t\t\t\t\t\t{{business.name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t<view v-for=\"item in business.biaoqian_names\" class=\"tag-item\">\r\n\t\t\t\t\t\t\t\t{{item}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"bg-blue flex align-center\" style=\"margin-bottom: 10rpx;\" @click=\"addfavorite\">\r\n\t\t\t\t\t\t\t<view style=\"margin-right: 10rpx;\">\r\n\t\t\t\t\t\t\t\t<image v-if=\"isfavorite\" :src=\"pre_url+'/static/icon/collect.png'\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\">\r\n\t\t\t\t\t\t\t\t\t<image v-else :src=\"pre_url+'/static/icon/notCollect.png'\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t收藏\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"bg-blue flex align-center\" open-type=\"share\" style=\"height: 28.86px;\">\r\n\t\t\t\t\t\t\t<view style=\"margin-right: 10rpx;line-height: 0px;\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/share2.png'\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t分享\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"box\">\r\n\t\t\t\t\t<!-- 文章 -->\r\n\t\t\t\t\t<view class=\"mb30\">\r\n\t\t\t\t\t\t<view class=\"article-item\" v-for=\"item in articles\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t:data-url=\"'/pagesExa/daxuepage/articledetail?id=' + item.id\">\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\">\r\n\t\t\t\t\t\t\t\t<view style=\"margin-right: 10rpx;\" class=\"text-bold\">\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/fire.png'\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/right.png'\" style=\"width: 20rpx;height: 20rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 学校简介 -->\r\n\t\t\t\t\t<view class=\"mb30\">\r\n\t\t\t\t\t\t<view class=\"text-h2 text-bold mb30\">\r\n\t\t\t\t\t\t\t学校简介\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"bg-grey flex align-center\" @tap=\"goto\" :data-url=\"'/pagesExa/daxuepage/schoolBlurb?id=' + opt.id\">\r\n\t\t\t\t\t\t\t<view class=\"text-overflow\">\r\n\t\t\t\t\t\t\t\t{{business.desc}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/right.png'\" style=\"width: 20rpx;height: 20rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 基本信息 -->\r\n\t\t\t\t\t<view class=\"mb30\">\r\n\t\t\t\t\t\t<view class=\"text-h2 text-bold mb30\">\r\n\t\t\t\t\t\t\t基本信息\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"flex align-center\" style=\"margin-bottom: 40rpx;\">\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/time.png'\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text-h3 text-bold\">\r\n\t\t\t\t\t\t\t\t\t\t{{business.established_year}} 年\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 20rpx;color: #dddddd;\">\r\n\t\t\t\t\t\t\t\t\t\t建校时间\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/area.png'\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text-h3 text-bold\">\r\n\t\t\t\t\t\t\t\t\t\t{{business.campus_area}} 亩\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 20rpx;color: #dddddd;\">\r\n\t\t\t\t\t\t\t\t\t\t占地面积\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"flex align-center\">\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/building.png'\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text-h3 text-bold\">\r\n\t\t\t\t\t\t\t\t\t\t{{business.department_count}} 个\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 20rpx;color: #dddddd;\">\r\n\t\t\t\t\t\t\t\t\t\t院系数量\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/speciality.png'\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text-h3 text-bold\">\r\n\t\t\t\t\t\t\t\t\t\t{{business.major_count}} 个\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 20rpx;color: #dddddd;\">\r\n\t\t\t\t\t\t\t\t\t\t专业数量\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 院校环境 -->\r\n\t\t\t\t\t<view class=\"mb30\" v-if=\"pics.length\">\r\n\t\t\t\t\t\t<view class=\"text-h2 text-bold mb30\">\r\n\t\t\t\t\t\t\t院校环境\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap;width: 100%;\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tstyle=\"border-radius: 30rpx;overflow: hidden;margin-right: 20rpx;width: 340rpx;height: 200rpx;display: inline-block;\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in pics\" @click=\"fnPreviewImage(index)\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" style=\"width: 100%;height: 100%;\" alt=\"\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 校区地址 -->\r\n\t\t\t\t\t<view class=\"mb30\">\r\n\t\t\t\t\t\t<view class=\"text-h2 text-bold mb30\">\r\n\t\t\t\t\t\t\t校区地址\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"position: relative;\">\r\n\t\t\t\t\t\t\t<map style=\"width: 100%; height: 300rpx;\" :latitude=\"business.latitude\"\r\n\t\t\t\t\t\t\t\t:longitude=\"business.longitude\" :scale=\"12\">\r\n\t\t\t\t\t\t\t</map>\r\n\t\t\t\t\t\t\t<view class=\"map-address\">\r\n\t\t\t\t\t\t\t\t校区地址：{{business.city}}{{business.district}} {{business.address}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 开设专业 -->\r\n\t\t\t<view v-if=\"switchIndex == 1\">\r\n\t\t\t\t<speciality :daxueid=\"opt.id\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 招生资料 -->\r\n\t\t\t<view v-if=\"switchIndex == 2\">\r\n\t\t\t\t<view class=\"mb30\">\r\n\t\t\t\t\t<view class=\"article-item\" v-for=\"item in articles\" @tap=\"goto\"\r\n\t\t\t\t\t\t:data-url=\"'/pagesExa/daxuepage/articledetail?id=' + item.id\">\r\n\t\t\t\t\t\t<view class=\"flex align-center\">\r\n\t\t\t\t\t\t\t<view style=\"margin-right: 10rpx;\" class=\"text-bold\">\r\n\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/fire.png'\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/icon/right.png'\" style=\"width: 20rpx;height: 20rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<nodata v-if=\"articles.length == 0\"></nodata>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 分数线 -->\r\n\t\t\t<view v-if=\"switchIndex == 3\">\r\n\t\t\t\t<fractional-line :daxueid=\"opt.id\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 底部菜单 -->\r\n\t\t\t<view class=\"tabbar\">\r\n\t\t\t\t<view class=\"tabbar-item\" v-for=\"(item, index) in tabbarList\" :key=\"index\" @click=\"switchTab(index)\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<image :src=\"item.icon\" style=\"width: 40rpx;height: 40rpx;\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text>{{ item.text }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\r\n\t\t<!-- #ifdef MP-TOUTIAO -->\r\n\t\t<view class=\"dp-cover\" v-if=\"video_status\">\r\n\t\t\t<button open-type=\"share\" data-channel=\"video\" class=\"dp-cover-cover\" :style=\"{\r\n\t\t\tzIndex:10,\r\n\t\t\ttop:'60vh',\r\n\t\t\tleft:'80vw',\r\n\t\t\twidth:'110rpx',\r\n\t\t\theight:'110rpx'\r\n\t\t}\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/uploadvideo2.png'\" :style=\"{width:'110rpx',height:'110rpx'}\" />\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\r\n\t\t<!-- <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar> -->\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport speciality from './components/speciality.vue'\r\n\timport fractionalLine from './components/line.vue'\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tspeciality,\r\n\t\t\tfractionalLine\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\r\n\t\t\t\tisdiy: 0,\r\n\r\n\t\t\t\tst: 0,\r\n\t\t\t\tbusiness: [],\r\n\t\t\t\tcountcomment: 0,\r\n\t\t\t\tcouponcount: 0,\r\n\t\t\t\tpics: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\ttopbackhide: false,\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tnodata: false,\r\n\r\n\t\t\t\ttitle: \"\",\r\n\t\t\t\tsysset: \"\",\r\n\t\t\t\tguanggaopic: \"\",\r\n\t\t\t\tguanggaourl: \"\",\r\n\t\t\t\tpageinfo: \"\",\r\n\t\t\t\tpagecontent: \"\",\r\n\t\t\t\tshowfw: false,\r\n\t\t\t\tyuyue_clist: [],\r\n\t\t\t\tyuyue_cid: 0,\r\n\t\t\t\tvideo_status: 0,\r\n\t\t\t\tvideo_title: '',\r\n\t\t\t\tvideo_tag: [],\r\n\t\t\t\tbset: '',\r\n\t\t\t\tisfavorite: false,\r\n\t\t\t\tarticles: [], // 文章\r\n\t\t\t\ttabbarList: [{\r\n\t\t\t\t\t\ticon: require('@/static/tabbar/menu.png'),\r\n\t\t\t\t\t\ttext: '学校主页'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: require('@/static/tabbar/menu.png'),\r\n\t\t\t\t\t\ttext: '开设专业'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: require('@/static/tabbar/menu.png'),\r\n\t\t\t\t\t\ttext: '招生资料'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: require('@/static/tabbar/menu.png'),\r\n\t\t\t\t\t\ttext: '分数线'\r\n\t\t\t\t\t}\r\n\t\t\t\t], // 底部菜单\r\n\t\t\t\tswitchIndex: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.opt.bid = this.opt.id;\r\n\t\t\tthis.st = this.opt.st || 0;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (this.isdiy == 0) {\r\n\t\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\t\tthis.getDataList(true);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll: function(e) {\r\n\t\t\tuni.$emit('onPageScroll', e);\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\t//#ifdef MP-TOUTIAO\r\n\t\t\tconsole.log(shareOption);\r\n\t\t\treturn {\r\n\r\n\t\t\t\ttitle: this.video_title,\r\n\t\t\t\tchannel: \"video\",\r\n\t\t\t\textra: {\r\n\t\t\t\t\thashtag_list: this.video_tag,\r\n\t\t\t\t},\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log(\"分享成功\");\r\n\t\t\t\t},\r\n\t\t\t\tfail: (res) => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t// 可根据 res.errCode 处理失败case\r\n\t\t\t\t},\r\n\t\t\t};\r\n\t\t\t//#endif\r\n\t\t\treturn this._sharewx({\r\n\t\t\t\ttitle: this.business.name\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPageScroll: function(e) {\r\n\t\t\tif (this.isdiy == 0) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar scrollY = e.scrollTop;\r\n\t\t\t\tif (scrollY > 200 && !that.topbackhide) {\r\n\t\t\t\t\tthat.topbackhide = true;\r\n\t\t\t\t}\r\n\t\t\t\tif (scrollY < 150 && that.topbackhide) {\r\n\t\t\t\t\tthat.topbackhide = false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = that.opt.id || 0;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiDaxue/index', {\r\n\t\t\t\t\tid: id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.isdiy = res.isdiy;\r\n\t\t\t\t\tthat.business = res.daxue;\r\n\t\t\t\t\tthat.countcomment = res.countcomment;\r\n\t\t\t\t\tthat.couponcount = res.couponcount;\r\n\t\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\t\tvar bset = res.bset;\r\n\t\t\t\t\tthat.bset = bset;\r\n\t\t\t\t\tthat.articles = res.articles\r\n\t\t\t\t\tif (bset) {\r\n\t\t\t\t\t\tif (bset.show_product) {\r\n\t\t\t\t\t\t\tthat.st = 0;\r\n\t\t\t\t\t\t} else if (bset.show_comment) {\r\n\t\t\t\t\t\t\tthat.st = 1;\r\n\t\t\t\t\t\t} else if (bset.show_detail) {\r\n\t\t\t\t\t\t\tthat.st = 2;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.guanggaopic = res.guanggaopic;\r\n\t\t\t\t\tthat.guanggaourl = res.guanggaourl;\r\n\t\t\t\t\tthat.pageinfo = res.pageinfo;\r\n\t\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\t\tthat.showfw = res.showfw || false;\r\n\t\t\t\t\tthat.isfavorite = that.business.isfavorite\r\n\t\t\t\t\tif (that.showfw) {\r\n\t\t\t\t\t\tthat.st = -1;\r\n\t\t\t\t\t\tthat.yuyue_clist = res.yuyue_clist;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.yuyueset) {\r\n\t\t\t\t\t\tthat.video_status = res.yuyueset.video_status;\r\n\t\t\t\t\t\tthat.video_title = res.yuyueset.video_title;\r\n\t\t\t\t\t\tthat.video_tag = res.yuyueset.video_tag;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.loaded({\r\n\t\t\t\t\t\ttitle: that.business.name,\r\n\t\t\t\t\t\tpic: that.business.logo\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (res.isdiy == 0) {\r\n\t\t\t\t\t\tthat.isload = 1;\r\n\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\ttitle: that.business.name\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.getDataList();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\t\t\t//付费查看\r\n\t\t\t\t\t\t\tapp.goto('/pages/pay/pay?fromPage=index&id=' + res.payorderid + '&pageid=' + that\r\n\t\t\t\t\t\t\t\t.res.id, 'redirect');\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tvar pagecontent = res.pagecontent;\r\n\t\t\t\t\t\t\tthat.isdiy = 1;\r\n\r\n\t\t\t\t\t\t\tthat.title = res.pageinfo.title;\r\n\t\t\t\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\t\t\t\tthat.guanggaopic = res.guanggaopic;\r\n\r\n\t\t\t\t\t\t\tthat.guanggaourl = res.guanggaourl;\r\n\t\t\t\t\t\t\tthat.pageinfo = res.pageinfo;\r\n\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\ttitle: res.pageinfo.title\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchangetab: function(e) {\r\n\t\t\t\tvar st = e.currentTarget.dataset.st;\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.st = st;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: 0,\r\n\t\t\t\t\tduration: 0\r\n\t\t\t\t});\r\n\t\t\t\tthis.getDataList();\r\n\t\t\t},\r\n\t\t\tgetDataList: function(loadmore) {\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tvar st = that.st;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('ApiDaxue/getdatalist', {\r\n\t\t\t\t\tid: that.business.id,\r\n\t\t\t\t\tst: st,\r\n\t\t\t\t\tpagenum: pagenum,\r\n\t\t\t\t\tyuyue_cid: that.yuyue_cid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\topenLocation: function(e) {\r\n\t\t\t\t//console.log(e)\r\n\t\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tname: address,\r\n\t\t\t\t\tscale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tphone: function(e) {\r\n\t\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\tfail: function() {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//改变子分类\r\n\t\t\tchangeyuyueCTab: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\tthis.nodata = false;\r\n\t\t\t\tthis.yuyue_cid = id;\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t\tthis.nomore = false;\r\n\t\t\t\tthis.getDataList();\r\n\t\t\t},\r\n\t\t\t// 图片预览\r\n\t\t\tfnPreviewImage: function(index) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: index,\r\n\t\t\t\t\turls: this.pics,\r\n\t\t\t\t\tloop: true,\r\n\t\t\t\t\tlongPressActions: true\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//收藏操作\r\n\t\t\taddfavorite: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.post('ApiDaxue/addfavorite', {\r\n\t\t\t\t\tproid: that.business.id,\r\n\t\t\t\t\ttype: 'daxue'\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 跳转\r\n\t\t\tswitchTab: function(index) {\r\n\t\t\t\tthis.switchIndex = index\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.container2 {\r\n\t\tpadding: 16px;\r\n\t\twidth: 95%;\r\n\t\tbackground-color: #f5f5f5;\r\n\r\n\t}\r\n\r\n\t.container2 {\r\n\t\tpadding: 10px;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmargin-top: -10px;\r\n\t}\r\n\r\n\t.header {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tpadding-bottom: 16px;\r\n\t}\r\n\r\n\t.card {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 16px;\r\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.discount-text {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.highlight {\r\n\t\tcolor: #ff0000;\r\n\t}\r\n\r\n\t.sub-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-top: 8px;\r\n\t}\r\n\r\n\t.pay-button {\r\n\t\tbackground-color: #ff4d4f;\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 10px;\r\n\t\tpadding: 4px 16px;\r\n\t\tfont-size: 14px;\r\n\t\tcursor: pointer;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: -10px;\r\n\t}\r\n\r\n\t.container {\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.nodiydata {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column\r\n\t}\r\n\r\n\t.nodiydata .swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 1\r\n\t}\r\n\r\n\t.nodiydata .swiper .image {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.nodiydata .topcontent {\r\n\t\twidth: 94%;\r\n\t\tmargin-left: 3%;\r\n\t\tpadding: 24rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: -120rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 16rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.nodiydata .topcontent .logo {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tmargin-top: -104rpx;\r\n\t\tborder: 2px solid rgba(255, 255, 255, 0.5);\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.nodiydata .topcontent .logo .img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.nodiydata .topcontent .title {\r\n\t\tcolor: #222222;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 12rpx\r\n\t}\r\n\r\n\t.nodiydata .topcontent .desc {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.nodiydata .topcontent .desc .f1 {\r\n\t\tmargin: 20rpx 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #FC5648;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.nodiydata .topcontent .desc .f1 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.nodiydata .topcontent .desc .f2 {\r\n\t\tmargin: 10rpx 0;\r\n\t\tpadding-left: 60rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.nodiydata .topcontent .tel {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 16rpx 40rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t\tfont-weight: normal\r\n\t}\r\n\r\n\t.nodiydata .topcontent .tel .img {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.nodiydata .topcontent .address {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-top: 20rpx\r\n\t}\r\n\r\n\t.nodiydata .topcontent .address .f1 {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tmargin-right: 8rpx\r\n\t}\r\n\r\n\t.nodiydata .topcontent .address .f2 {\r\n\t\tflex: 1;\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 26rpx\r\n\t}\r\n\r\n\t.nodiydata .topcontent .address .f3 {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 26rpx;\r\n\t\theight: 26rpx\r\n\t}\r\n\r\n\t.nodiydata .contentbox {\r\n\t\twidth: 94%;\r\n\t\tmargin-left: 3%;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin-bottom: 32rpx;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.nodiydata .shop_tab {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t}\r\n\r\n\t.nodiydata .shop_tab .cptab_text {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tcolor: #646566;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.nodiydata .shop_tab .cptab_current {\r\n\t\tcolor: #323233;\r\n\t}\r\n\r\n\t.nodiydata .shop_tab .after {\r\n\t\tdisplay: none;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -16rpx;\r\n\t\tbottom: 10rpx;\r\n\t\theight: 3px;\r\n\t\tborder-radius: 1.5px;\r\n\t\twidth: 32rpx\r\n\t}\r\n\r\n\t.nodiydata .shop_tab .cptab_current .after {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\r\n\t.nodiydata .cp_detail {\r\n\t\tmin-height: 500rpx\r\n\t}\r\n\r\n\t.nodiydata .comment .item {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f1 {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f1 .t1 {\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f1 .t2 {\r\n\t\tpadding-left: 10rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f1 .t3 {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f1 .t3 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 10rpx\r\n\t}\r\n\r\n\t.nodiydata .comment .item .score {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #f99716;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .score image {\r\n\t\twidth: 140rpx;\r\n\t\theight: 50rpx;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-bottom: 6rpx;\r\n\t\tmargin-right: 6rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 .t1 {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 .t2 {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 .t2 image {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tmargin: 10rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 .t3 {\r\n\t\tcolor: #aaa;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f2 .t3 {\r\n\t\tcolor: #aaa;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f3 {\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f3 .arrow {\r\n\t\twidth: 16rpx;\r\n\t\theight: 16rpx;\r\n\t\tbackground: #eee;\r\n\t\ttransform: rotate(45deg);\r\n\t\tposition: absolute;\r\n\t\ttop: 0rpx;\r\n\t\tleft: 36rpx\r\n\t}\r\n\r\n\t.nodiydata .comment .item .f3 .t1 {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 10rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #888;\r\n\t\tbackground: #eee\r\n\t}\r\n\r\n\t.nodiydata .nomore-footer-tips {\r\n\t\tbackground: #fff !important\r\n\t}\r\n\r\n\t.nodiydata .covermy {\r\n\t\tposition: fixed;\r\n\t\tz-index: 99999;\r\n\t\tcursor: pointer;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 9999;\r\n\t\ttop: 81vh;\r\n\t\tleft: 82vw;\r\n\t\tcolor: #fff;\r\n\t\tbackground-color: rgba(92, 107, 129, 0.6);\r\n\t\twidth: 110rpx;\r\n\t\theight: 110rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\r\n\t.classify-ul {\r\n\t\twidth: 100%;\r\n\t\theight: 70rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.classify-li {\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\tbackground: #F5F6F8;\r\n\t\tborder-radius: 22rpx;\r\n\t\tcolor: #6C737F;\r\n\t\tfont-size: 20rpx;\r\n\t\ttext-align: center;\r\n\t\theight: 44rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tpadding: 0 28rpx;\r\n\t\tmargin: 12rpx 10rpx 12rpx 0\r\n\t}\r\n\r\n\t.dp-cover {\r\n\t\theight: auto;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.dp-cover-cover {\r\n\t\tposition: fixed;\r\n\t\tz-index: 99999;\r\n\t\tcursor: pointer;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: inherit;\r\n\t}\r\n</style>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.box {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.text-bold {\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.justify-center {\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.justify-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.text-h2 {\r\n\t\tcolor: #000;\r\n\t\tfont-size: 34rpx;\r\n\t}\r\n\r\n\t.mb30 {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.text-h3 {\r\n\t\tcolor: #000;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.tag-item {\r\n\t\tbackground-color: #ededed;\r\n\t\tcolor: #b7b7b7;\r\n\t\tfont-size: 24rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 6rpx 14rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.bg-blue {\r\n\t\tborder-radius: 30rpx 0 0 30rpx;\r\n\t\tbackground-color: #19a7ff;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 6rpx 14rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.icon {\r\n\t\tmargin-right: 20rpx;\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 130rpx;\r\n\t\tbackground-color: #ededed;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.text-overflow {\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: normal;\r\n\t}\r\n\r\n\t.bg-grey {\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #ededed;\r\n\t\tcolor: #515151;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.article-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.map-address {\r\n\t\tposition: absolute;\r\n\t\tbackground-color: rgba(169, 169, 169, 0.8);\r\n\t\tpadding: 10rpx 6rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 60%;\r\n\t\theight: 100rpx;\r\n\t\tleft: 20%;\r\n\t\ttop: 35%;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.tabbar {\r\n\t\tposition: fixed;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: row;\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tbottom: 10rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tbackground: #fafafa;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tborder-top: 1px solid #efefef;\r\n\t\tz-index: 999999;\r\n\t\tbox-sizing: content-box;\r\n\t}\r\n\r\n\t.tabbar-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.tabbar-item text:first-child {\r\n\t\tfont-size: 48rpx;\r\n\t\t/* 根据设计调整 */\r\n\t}\r\n\r\n\t.tabbar-item text:last-child {\r\n\t\tfont-size: 24rpx;\r\n\t\t/* 根据设计调整 */\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027959\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027363\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}