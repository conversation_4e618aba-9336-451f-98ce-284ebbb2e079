{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?9c12", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?e0e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?5cf0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?e2b4", "uni-app:///pagesExt/business/maps.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?3a1b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?dee0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?67a3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/maps.vue?79a8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "latitude", "longitude", "scale", "bottomData", "marker", "currentMerchant", "merchantList", "onLoad", "computed", "<PERSON><PERSON><PERSON>", "onPullDownRefresh", "methods", "getNearbyBusiness", "app", "pagenum", "field", "order", "keyword", "that", "res", "id", "iconPath", "rotate", "width", "height", "alpha", "callout", "content", "color", "fontSize", "borderRadius", "borderWidth", "bgColor", "display", "setDistance", "getLocation", "uni", "type", "complete", "callouttap", "navigateToMerchantPage", "console", "url", "onControltap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACc;AACwB;;;AAGzF;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwG7wB;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAf;QACAD;QACAiB;MACA;QAAA;QACA;UACAC;UACAC;YACAD;cACAE;cACApB;cAAA;cACAC;cAAA;cACAoB;cAAA;cACAC;cAAA;cACAC;cACAC;cACAC;cAAA;cACAC;gBAAA;gBACAC;gBAAA;gBACAC;gBAAA;gBACAC;gBAAA;gBACAC;gBAAA;gBACAC;gBACAC;gBAAA;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;UACA;YACApB;YACAA;UACA;UACA;UACAA;QACA;MACA;IACA;IACA;IACAqB;MACA;QAAA;MAAA;MACA;IACA;IACA;IACAC;MACAC;MACA;QACAL;UACAM;QACA;MACA;QACAD;MACA;IACA;IACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChNA;AAAA;AAAA;AAAA;AAA63C,CAAgB,40CAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAq5C,CAAgB,o2CAAG,EAAC,C;;;;;;;;;;;ACAz6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/maps.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/maps.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./maps.vue?vue&type=template&id=db26c47c&scoped=true&\"\nvar renderjs\nimport script from \"./maps.vue?vue&type=script&lang=js&\"\nexport * from \"./maps.vue?vue&type=script&lang=js&\"\nimport style0 from \"./maps.vue?vue&type=style&index=0&lang=less&\"\nimport style1 from \"./maps.vue?vue&type=style&index=1&id=db26c47c&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"db26c47c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/maps.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=template&id=db26c47c&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.currentMerchant && _vm.currentMerchant && _vm.currentMerchant.juli\n      ? _vm.setDistance(_vm.currentMerchant.juli)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page-section page-section-gap map-container\">\r\n\t\t<map style=\"width: 100%; height: 100vh;\" :layer-style='5' :show-location='true' :latitude=\"latitude\"\r\n\t\t\t:longitude=\"longitude\" :markers=\"marker\" :scale=\"scale\" @markertap=\"markertap\" @callouttap='callouttap'>\r\n\t\t\t<!-- 弹出层 -->\r\n\t\t\t<cover-view>\r\n\t\t\t\t<uni-popup ref=\"popup\" type=\"bottom\" border-radius=\"10px 10px 0 0\">\r\n\t\t\t\t\t\t<div class=\"map-marker-popup-container\" v-if=\"currentMerchant\">\r\n\t\t\t\t\t\t\t<div class=\"oilStation-top\">\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-top-icon\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-top-iconBg\">\r\n\t\t\t\t\t\t\t\t\t\t<!-- @tap.stop=\"preViewImage(currentMerchant.logo)\" -->\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"oilStation-top-iconBg-img\" :src=\"currentMerchant.logo\"\r\n\t\t\t\t\t\t\t\t\t\t\tmode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-top-name\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-top-nameBox\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-top-nameBox-name\">{{ currentMerchant.name }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-top-nameBox-address\">{{ currentMerchant.address }}</text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-top-navigation\" >\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-top-navigation-iconBg\"  @click=\"navigateToMerchantPage\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont icondingwei\"></text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-top-navigation-iconBg-span\" >租机</text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"oilStation-bottom\">\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-collectBox\">\r\n\t\t\t\t\t\t\t\t\t<!-- <text class=\"oilStation-bottom-collectBox-collect\">加收藏</text> -->\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-collectBox-score\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont iconstar\"></text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-bottom-collectBox-score-span\">4.9分</text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-lineUpBox\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-lineUpBox-lineUp\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-lineUpBox-headBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"oilStation-bottom-lineUpBox-headBox-medal\" :src=\"currentMerchant.logo\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tmode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"oilStation-bottom-lineUpBox-headBox-medal\" :src=\"currentMerchant.logo\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tmode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"oilStation-bottom-lineUpBox-headBox-medal\" :src=\"currentMerchant.logo\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tmode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-lineUpBox-lineUpNum\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-bottom-lineUpBox-lineUpNum-num\">1622</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-bottom-lineUpBox-lineUpNum-txt\">人正在租机</text>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-distanceBox\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"oilStation-bottom-distanceBox-distance\">\r\n\t\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"oilStation-bottom-distanceBox-num\" v-if=\"currentMerchant && currentMerchant.juli\">{{ setDistance(currentMerchant.juli) }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"oilStation-bottom-distanceBox-txt\">km</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont icondingwei\" style=\"font-size: 30rpx;\"></text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- <div class=\"todayOilPrice\">\r\n\t\t\t\t\t\t\t\t<div class=\"oil-title\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"oil-title-txt\">今日油价</text>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"todayOil-content\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"todayOil-priceByOilNum\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-sheng shengActive\">6.75/L</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-oilNum\">92#</text>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"myCarLabel\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"myCarLabel-txt\">我的车辆</text>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"todayOil-priceByOilNum\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-sheng\">7.20/L</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-oilNum\"> 95#</text>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"myCarLabel\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"myCarLabel-txt\">我的车辆</text>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"todayOil-priceByOilNum\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-sheng\">7.84/L</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-oilNum\">98#</text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"todayOil-priceByOilNum\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-sheng\">8.72/L</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"todayOil-priceByOilNum-oilNum\">101#</text>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</uni-popup>\r\n\t\t\t\t\r\n\t\t\t</cover-view>\r\n\t\t</map>\r\n\r\n\t</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlatitude: 23.106574, //纬度\r\n\t\t\t\tlongitude: 113.324587,   \r\n\t\t\t\tscale: 12, //缩放级别\r\n\t\t\t\tbottomData: false,\r\n\t\t\t\tmarker: [],\r\n\t\t\t\tcurrentMerchant: null, // 当前点击商家\r\n\t\t\t\tmerchantList: [], // 附近商家列表\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getLocation()\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcoverbottom() {\r\n\t\t\t\tlet data = '100rpx'\r\n\t\t\t\treturn data\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getLocation();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取附近商家\r\n\t\t\tgetNearbyBusiness() {\r\n\t\t\t\tthis.marker = [];\r\n\t\t\t\tconst that = this;\r\n\t\t\t\tapp.post('ApiBusiness/blist', {\r\n\t\t\t\t\tpagenum: 1,\r\n\t\t\t\t\tfield: \"juli\",\r\n\t\t\t\t\torder: \"asc\",\r\n\t\t\t\t\tlongitude: this.longitude,\r\n\t\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\t\tkeyword: ''\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.data?.length) {\r\n\t\t\t\t\t\tthat.merchantList = res.data;\r\n\t\t\t\t\t\tres.data.forEach(item => {\r\n\t\t\t\t\t\t\tthat.marker.push({\r\n\t\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\t\tlatitude: item.latitude, // 纬度\r\n\t\t\t\t\t\t\t\tlongitude: item.longitude, //经度\r\n\t\t\t\t\t\t\t\ticonPath: '/static/img/address.png', // 显示的图标   \r\n\t\t\t\t\t\t\t\trotate: 0, // 旋转度数\r\n\t\t\t\t\t\t\t\twidth: 20,\r\n\t\t\t\t\t\t\t\theight: 30,\r\n\t\t\t\t\t\t\t\talpha: 0.5, // 图片透明度\r\n\t\t\t\t\t\t\t\tcallout: { // 自定义标记点上方的气泡窗口 点击有效\r\n\t\t\t\t\t\t\t\t\tcontent: item.name, // 文本\r\n\t\t\t\t\t\t\t\t\tcolor: '#ffffff', // 文字颜色\r\n\t\t\t\t\t\t\t\t\tfontSize: 14, // 文本大小\r\n\t\t\t\t\t\t\t\t\tborderRadius: 15, // 边框圆角\r\n\t\t\t\t\t\t\t\t\tborderWidth: '10',\r\n\t\t\t\t\t\t\t\t\tbgColor: '#e51860', // 背景颜色\r\n\t\t\t\t\t\t\t\t\tdisplay: 'ALWAYS', // 常显\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetDistance(distance) {\r\n\t\t\t\tif (distance.includes(\"km\")) {\r\n\t\t\t\t\treturn distance.slice(0, -2)\r\n\t\t\t\t}\r\n\t\t\t\treturn distance\r\n\t\t\t},\r\n\t\t\tgetLocation() {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\tcomplete: function(res) {\r\n\t\t\t\t\t\tif (res.longitude && res.latitude) {\r\n\t\t\t\t\t\t\tthat.longitude = res.longitude\r\n\t\t\t\t\t\t\tthat.latitude = res.latitude\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 获取附近商家\r\n\t\t\t\t\t\tthat.getNearbyBusiness()\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//地图标注点icon点击事件\r\n\t\t\tcallouttap(e) {\r\n\t\t\t\tthis.currentMerchant = this.merchantList.find(merchant => merchant.id === e.detail.markerId)\r\n\t\t\t\tthis.$refs.popup.open('top')\r\n\t\t\t},\r\n\t\t\t// 点击租机按钮跳转\r\n\t\t\t  navigateToMerchantPage() {\r\n\t\t\t        console.log('Navigating to merchant page', this.currentMerchant);\r\n\t\t\t        if (this.currentMerchant && this.currentMerchant.id) {\r\n\t\t\t            uni.navigateTo({\r\n\t\t\t                url: `/pagesExt/zuji/prolistzuji?bid=${this.currentMerchant.id}`\r\n\t\t\t            });\r\n\t\t\t        } else {\r\n\t\t\t            console.log('Current merchant is null or has no id');\r\n\t\t\t        }\r\n\t\t\t    },\r\n\t\t\tonControltap() {\r\n\t\t\t\tthis.getLocation()\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\">\r\n\t.map-marker-popup-container {\r\n\t\tpadding: 80rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 40rpx 40rpx 0 0;\r\n\r\n\t\t.oilStation-top {\r\n\t\t\twidth: 594rpx;\r\n\t\t\theight: 135rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-icon {\r\n\t\t\twidth: 110rpx;\r\n\t\t\t/* height: 100%; */\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-iconBg {\r\n\t\t\twidth: 90rpx;\r\n\t\t\theight: 90rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-color: #ffb92e;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-iconBg-img {\r\n\t\t\twidth: 90rpx;\r\n\t\t\theight: 90rpx !important;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-name {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 0 15rpx;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-nameBox {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: flex-start;\r\n\t\t\tjustify-content: space-evenly;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-nameBox-name {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-nameBox-address {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-navigation {\r\n\t\t\twidth: 120rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-navigation-iconBg {\r\n\t\t\twidth: 120rpx;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground-color: #ffc02e;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-family: unibtn;\r\n\t\t\tfont-weight: 400;\r\n\t\t}\r\n\r\n\t\t.icondingwei {\r\n\t\t\tfont-size: 50rpx;\r\n\t\t\tcolor: #101010;\r\n\t\t}\r\n\r\n\t\t.oilStation-top-navigation-iconBg-span {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #101010;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-lineUp {\r\n\t\t\twidth: 250rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tbackground-color: #eef1f4;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-headBox {\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-headBox-medal {\r\n\t\t\twidth: 38rpx !important;\r\n\t\t\theight: 38rpx !important;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tmargin-left: -10rpx;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-headBox-medal:first-child {\r\n\t\t\tmargin-left: 0;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-lineUpNum {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #555555;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-lineUpNum-num {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #ff6f21;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-lineUpBox-lineUpNum-txt {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-distanceBox {\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-distanceBox-distance {\r\n\t\t\twidth: 155rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t\tpadding: 0 10rpx;\r\n\t\t\tborder-width: 1rpx;\r\n\t\t\tborder-style: solid;\r\n\t\t\tborder-color: #dddddd;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #555555;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-distanceBox-num {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #3294ff;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-distanceBox-txt {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-collectBox {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-collectBox-collect {\r\n\t\t\twidth: 110rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tline-height: 40rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #555555;\r\n\t\t\tbackground-color: #eef1f4;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t}\r\n\r\n\t\t.oilStation-bottom-collectBox-score {\r\n\t\t\tfont-size: 18rpx;\r\n\t\t\tcolor: #ffb92e;\r\n\t\t\tmargin-top: 5rpx;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t.iconstar {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #ffb92e;\r\n\t\t\tmargin: 0 5rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.todayOilPrice {\r\n\t\t\twidth: 594rpx;\r\n\t\t\theight: 240rpx;\r\n\t\t\tborder-bottom-width: 1px;\r\n\t\t\tborder-bottom-style: solid;\r\n\t\t\tborder-bottom-color: #eef1f4;\r\n\t\t}\r\n\t\t\r\n\t\t.oil-title {\r\n\t\t\twidth: 594rpx;\r\n\t\t\tmargin: 40rpx 0 20rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.oil-title-txt {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t}\r\n\t\t\r\n\t\t.todayOil-content {\r\n\t\t\twidth: 594rpx;\r\n\t\t\theight: 155rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: flex-start;\r\n\t\t\tflex-direction: row;\r\n\t\t\tpadding-left: 40rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.todayOil-priceByOilNum {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\t\t\r\n\t\t.todayOil-priceByOilNum-sheng {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #ffc02e;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.todayOil-priceByOilNum-sheng:first-letter {\r\n\t\t\tfont-size: 35rpx;\r\n\t\t\t/* vertical-align: auto; */\r\n\t\t}\r\n\t\t\r\n\t\t.shengActive {\r\n\t\t\tbackground-image: linear-gradient(to right, #ff644f, #ff7f3f);\r\n\t\t\t/* -webkit-background-clip: text;\r\n\t\t\t-webkit-text-fill-color: transparent; */\r\n\t\t}\r\n\t\t\r\n\t\t.todayOil-priceByOilNum-oilNum {\r\n\t\t\tfont-size: 25rpx;\r\n\t\t\tcolor: #777777;\r\n\t\t\tmargin: 5rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.myCarLabel {\r\n\t\t\twidth: 117rpx;\r\n\t\t\theight: 33rpx;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tbackground-color: #ffc02e;\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: -20rpx;\r\n\t\t\tbottom: -40rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #101010;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tz-index: 10;\r\n\t\t}\r\n\t\t\r\n\t\t.myCarLabel-txt {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #101010;\r\n\t\t}\r\n\r\n\r\n\t}\r\n</style>\r\n<style lang='less' scoped>\r\n\t.map-container {\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098396\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=1&id=db26c47c&lang=less&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--10-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--10-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\less-loader\\\\dist\\\\cjs.js??ref--10-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--10-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=1&id=db26c47c&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098378\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}