{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?2b30", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?92d1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?2ebd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?7101", "uni-app:///pagesExt/ranking/listjituan.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?675d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/listjituan.vue?432f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "nomore", "rankType", "page", "limit", "rankList", "monthList", "monthIndex", "currentMonth", "settings", "pre_url", "medals", "computed", "getMonthName", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getRankingSetting", "app", "that", "getMonthList", "getMedalImage", "getRankingList", "type", "month", "uni", "changeRankType", "onMonthChange", "goToProduct", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqEnxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MAAA;;MAEAC;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;QACA;UACAC;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAF;QACA;UACAC;UACAA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAE;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MAEAH;MACA;QACAI;QACArB;QACAC;QACAqB;MACA;MAEAN;QACAC;QACAM;QAEA;UACA;YACAN;UACA;YACAA;UACA;UACAA;QACA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAO;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAH;QACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjNA;AAAA;AAAA;AAAA;AAAs6C,CAAgB,i3CAAG,EAAC,C;;;;;;;;;;;ACA17C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/ranking/listjituan.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/ranking/listjituan.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./listjituan.vue?vue&type=template&id=0f2b8638&\"\nvar renderjs\nimport script from \"./listjituan.vue?vue&type=script&lang=js&\"\nexport * from \"./listjituan.vue?vue&type=script&lang=js&\"\nimport style0 from \"./listjituan.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/ranking/listjituan.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./listjituan.vue?vue&type=template&id=0f2b8638&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.rankList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 =\n          _vm.rankType === 1 && index < 3 ? _vm.getMedalImage(index) : null\n        var m1 =\n          _vm.rankType === 2 && index < 3 ? _vm.getMedalImage(index) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./listjituan.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./listjituan.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"container\">\r\n\t\t\t\t<!-- 排行榜类型选择 -->\r\n\t\t\t\t<view class=\"tab-box\">\r\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{'active': rankType === 1}\" @tap=\"changeRankType(1)\">个人风云榜</view>\r\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{'active': rankType === 2}\" @tap=\"changeRankType(2)\">商品风云榜</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 筛选条件 -->\r\n\t\t\t\t<view class=\"filter-box\">\r\n\t\t\t\t\t<view class=\"filter-row\">\r\n\t\t\t\t\t\t<picker :value=\"monthIndex\" :range=\"monthList\" range-key=\"name\" @change=\"onMonthChange\">\r\n\t\t\t\t\t\t\t<view class=\"picker-item\">\r\n\t\t\t\t\t\t\t\t<text>{{getMonthName}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"icon-arrow\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 排行榜列表 -->\r\n\t\t\t\t<view class=\"rank-list\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in rankList\" :key=\"item.id\">\r\n\t\t\t\t\t\t<!-- 个人风云榜显示 -->\r\n\t\t\t\t\t\t<view class=\"rank-item\" v-if=\"rankType === 1\">\r\n\t\t\t\t\t\t\t<view class=\"rank-num\" :class=\"'rank-' + (index + 1)\">\r\n\t\t\t\t\t\t\t\t<image v-if=\"index < 3\" :src=\"getMedalImage(index)\" mode=\"aspectFit\" class=\"medal-icon\"></image>\r\n\t\t\t\t\t\t\t\t<text v-else>{{index + 1}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.nickname || '匿名用户'}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 商品风云榜显示 -->\r\n\t\t\t\t\t\t<view class=\"rank-item\" v-if=\"rankType === 2\" @tap=\"goToProduct(item.id)\">\r\n\t\t\t\t\t\t\t<view class=\"rank-num\" :class=\"'rank-' + (index + 1)\">\r\n\t\t\t\t\t\t\t\t<image v-if=\"index < 3\" :src=\"getMedalImage(index)\" mode=\"aspectFit\" class=\"medal-icon\"></image>\r\n\t\t\t\t\t\t\t\t<text v-else>{{index + 1}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t<view class=\"name product-link\">{{item.name || '-'}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"sales-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"sales-item\" v-for=\"(tier, idx) in item.tier_sales\" :key=\"idx\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"tier-name\">{{tier.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"tier-num\">{{tier.num}}盒</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"total-sales\">\r\n\t\t\t\t\t\t\t\t<text>总销量</text>\r\n\t\t\t\t\t\t\t\t<text class=\"num\">{{item.total_num || 0}}盒</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 加载更多 -->\r\n\t\t\t\t<view class=\"loading-more\" v-if=\"loading\">加载中...</view>\r\n\t\t\t\t<view class=\"no-more\" v-if=\"nomore\">没有更多数据了</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t</view>\r\n\t</template>\r\n\t\r\n\t<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisload: false,\r\n\t\t\t\tloading: true,\r\n\t\t\t\tnomore: false,\r\n\t\t\t\t\r\n\t\t\t\trankType: 1, // 1个人风云榜 2商品风云榜\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\t\r\n\t\t\t\trankList: [],\r\n\t\t\t\tmonthList: [],\r\n\t\t\t\tmonthIndex: 0,\r\n\t\t\t\tcurrentMonth: '', // 当前选中的月份值\r\n\t\t\t\t\r\n\t\t\t\tsettings: null,\r\n\t\t\t\tpre_url: '',  // 域名前缀\r\n\t\t\t\t\r\n\t\t\t\tmedals: {\r\n\t\t\t\t\t0: '/static/img/gold-medal.png',\r\n\t\t\t\t\t1: '/static/img/silver-medal.png',\r\n\t\t\t\t\t2: '/static/img/bronze-medal.png'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tgetMonthName() {\r\n\t\t\t\tif (this.monthList.length > 0 && this.monthList[this.monthIndex]) {\r\n\t\t\t\t\treturn this.monthList[this.monthIndex].name;\r\n\t\t\t\t}\r\n\t\t\t\treturn '选择月份';\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.pre_url = app.globalData.pre_url;  // 获取域名前缀\r\n\t\t\tthis.getMonthList(); // 先获取月份列表\r\n\t\t\tthis.getRankingSetting();\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.rankList = [];\r\n\t\t\tthis.getRankingList();\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif(!this.nomore && !this.loading) {\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.getRankingList();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取排行榜设置\r\n\t\t\tgetRankingSetting() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.get('ApiJietikaohe/getRankingSetting', {}, function(res) {\r\n\t\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\t\tthat.settings = res.data;\r\n\t\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取月份列表\r\n\t\t\tgetMonthList() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.get('ApiJietikaohe/getMonthList', {}, function(res) {\r\n\t\t\t\t\tif(res.code == 1 && res.data && res.data.length > 0) {\r\n\t\t\t\t\t\tthat.monthList = res.data;\r\n\t\t\t\t\t\tthat.monthIndex = 0; // 默认选中第一个月份\r\n\t\t\t\t\t\tthat.currentMonth = res.data[0].value;\r\n\t\t\t\t\t\tthat.getRankingList(); // 获取第一个月份的数据\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取奖牌图片\r\n\t\t\tgetMedalImage(index) {\r\n\t\t\t\treturn this.pre_url + this.medals[index] || '';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取排行榜数据\r\n\t\t\tgetRankingList() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(!that.currentMonth) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tvar params = {\r\n\t\t\t\t\ttype: that.rankType,\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\tmonth: that.currentMonth\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tapp.get('ApiJietikaohe/getAllRankingList', params, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\t\tif(that.page == 1) {\r\n\t\t\t\t\t\t\tthat.rankList = res.data.list || [];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.rankList = that.rankList.concat(res.data.list || []);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.nomore = that.rankList.length >= (res.data.total || 0);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.rankList = [];\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换排行类型\r\n\t\t\tchangeRankType(type) {\r\n\t\t\t\tif(this.rankType === type) return;\r\n\t\t\t\tthis.rankType = type;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.rankList = [];\r\n\t\t\t\tthis.getRankingList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择月份\r\n\t\t\tonMonthChange(e) {\r\n\t\t\t\tthis.monthIndex = e.detail.value;\r\n\t\t\t\tthis.currentMonth = this.monthList[this.monthIndex].value;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.rankList = [];\r\n\t\t\t\tthis.getRankingList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到商品详情\r\n\t\t\tgoToProduct(id) {\r\n\t\t\t\tif(!id) return;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/product/detail?id=' + id\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t</script>\r\n\t\r\n\t<style lang=\"scss\">\r\n\t.container {\r\n\t\tpadding: 30rpx 20rpx;\r\n\t\tbackground: #F8F9FD;\r\n\t\tmin-height: 100vh;\r\n\t\t\r\n\t\t.tab-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tbackground: #fff;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);\r\n\t\t\tposition: relative;\r\n\t\t\toverflow: hidden;\r\n\t\t\t\r\n\t\t\t&::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\theight: 200%;\r\n\t\t\t\tbackground: linear-gradient(180deg, rgba(250, 81, 81, 0.05) 0%, rgba(250, 81, 81, 0) 100%);\r\n\t\t\t\topacity: 0.5;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.tab-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 24rpx 0;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tcolor: #FA5151;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\ttransform: scale(1.02);\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\t\tbackground: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.filter-box {\r\n\t\t\tbackground: #fff;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);\r\n\t\t\t\r\n\t\t\t.filter-row {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tgap: 24rpx;\r\n\t\t\t\t\r\n\t\t\t\t.picker-item {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground: #F8F9FD;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\t\tbackground: #F0F2F5;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.icon-arrow {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\twidth: 0;\r\n\t\t\t\t\t\theight: 0;\r\n\t\t\t\t\t\tborder-left: 12rpx solid transparent;\r\n\t\t\t\t\t\tborder-right: 12rpx solid transparent;\r\n\t\t\t\t\t\tborder-top: 12rpx solid #999;\r\n\t\t\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\t\t\ttransition: transform 0.3s ease;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.rank-list {\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);\r\n\t\t\t\r\n\t\t\t.rank-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 30rpx 20rpx;\r\n\t\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.04);\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: rgba(250, 81, 81, 0.02);\r\n\t\t\t\t\ttransform: translateX(4rpx);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.rank-num {\r\n\t\t\t\t\twidth: 72rpx;\r\n\t\t\t\t\theight: 72rpx;\r\n\t\t\t\t\tline-height: 72rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.rank-1, &.rank-2, &.rank-3 {\r\n\t\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\t\tbox-shadow: none;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.medal-icon {\r\n\t\t\t\t\t\t\twidth: 72rpx;\r\n\t\t\t\t\t\t\theight: 72rpx;\r\n\t\t\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:not(.rank-1):not(.rank-2):not(.rank-3) {\r\n\t\t\t\t\t\tbackground: #F8F9FD;\r\n\t\t\t\t\t\tborder: 2rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.info {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.product-link {\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t\tcolor: #FA5151;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 添加文字溢出省略\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\tmax-width: 400rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.sales-info {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\t\tgap: 16rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.sales-item {\r\n\t\t\t\t\t\t\tbackground: #F8F9FD;\r\n\t\t\t\t\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.tier-name {\r\n\t\t\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.tier-num {\r\n\t\t\t\t\t\t\t\tcolor: #FA5151;\r\n\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.total-sales {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.num {\r\n\t\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\tcolor: #FA5151;\r\n\t\t\t\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.loading-more,\r\n\t\t.no-more {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\r\n\t\t\t&::before,\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 1px;\r\n\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1));\r\n\t\t\t\tmargin: 0 24rpx;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&::after {\r\n\t\t\t\tbackground: linear-gradient(90deg, rgba(0, 0, 0, 0.1), transparent);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.rank-list {\r\n\t\t.rank-item {\r\n\t\t\t.rank-num {\r\n\t\t\t\t.medal-icon {\r\n\t\t\t\t\twidth: 72rpx;\r\n\t\t\t\t\theight: 72rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./listjituan.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./listjituan.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115104519\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}