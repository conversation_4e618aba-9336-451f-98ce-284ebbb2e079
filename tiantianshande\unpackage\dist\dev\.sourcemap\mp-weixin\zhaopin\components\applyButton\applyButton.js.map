{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?e4d0", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?0cc2", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?6dc2", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?4c80", "uni-app:///zhaopin/components/applyButton/applyButton.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?1d9d", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/applyButton/applyButton.vue?b2f8"], "names": ["components", "openButton", "data", "partJobFavoriteId", "partJobVoClone", "jobFeeVO", "buttons", "btn2", "btn1", "collectImgClone", "mounted", "console", "partJobVo", "hasToken", "template", "buttonStatus", "watch", "handler", "immediate", "deep", "collectImg", "props", "type", "default", "isShow", "buttonTitle", "baseInfo", "agreementVo", "methods", "t", "onLoginSuccess", "onPromptlyClicked", "onPromptly", "uni", "title", "icon", "templateId", "hasApply", "payHandle", "collect", "jumpToReport", "jumpToAgreement", "cancelHandle", "handleShare", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgIpxB;AAAA,gBAEA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAJ;MACAK;QACAN;QACA;MACA;MACAO;MACAC;IACA;IACAC;MACAH;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAE;IACAR;MACAS;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAX;MACAU;MACAC;QAAA;MAAA;IACA;IACAH;MACAE;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QAAA;MAAA;IACA;IACAI;MACAL;MACAC;QAAA;MAAA;IACA;EACA;EACAK;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACArB;MACA;QACAsB;UACAC;UACAC;QACA;QACA;MACA;MACAxB;QACAI;QACAqB;QACAC;QACAxB;MACA;MACAF;MACA;IACA;IACA2B;MACA;IACA;IACAC;MACA5B;MACAA;MACA;IACA;IACA6B;IACAC;IACAC;IACAC;MACAV;QACAW;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACtQA;AAAA;AAAA;AAAA;AAAu6C,CAAgB,k3CAAG,EAAC,C;;;;;;;;;;;ACA37C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/components/applyButton/applyButton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./applyButton.vue?vue&type=template&id=3facaf9c&\"\nvar renderjs\nimport script from \"./applyButton.vue?vue&type=script&lang=js&\"\nexport * from \"./applyButton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./applyButton.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/components/applyButton/applyButton.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./applyButton.vue?vue&type=template&id=3facaf9c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.partJobVoClone.template.templateId !== 16 &&\n    _vm.partJobVoClone.template.templateId !== 13 &&\n    _vm.partJobVoClone.hasApply &&\n    _vm.partJobVoClone.buttonStatus !== 9\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.partJobVoClone.template.templateId !== 16 &&\n    _vm.partJobVoClone.template.templateId !== 13 &&\n    _vm.partJobVoClone.buttonStatus === 6\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.partJobVoClone.template.templateId === 13 &&\n    _vm.partJobVoClone.hasApply &&\n    _vm.partJobVoClone.buttonStatus !== 9\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.partJobVoClone.template.templateId === 13 &&\n    _vm.partJobVoClone.buttonStatus === 6 &&\n    _vm.partJobVoClone.jobFeeVO.rushStatus === 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.partJobVoClone.template.templateId === 13 &&\n    _vm.partJobVoClone.buttonStatus === 6 &&\n    !(_vm.partJobVoClone.jobFeeVO.rushStatus === 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.partJobVoClone.template.templateId === 16 &&\n    _vm.partJobVoClone.hasApply &&\n    _vm.partJobVoClone.buttonStatus !== 9\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.partJobVoClone.template.templateId === 16 &&\n    _vm.partJobVoClone.buttonStatus === 6\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./applyButton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./applyButton.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"foot-box\">\r\n        <view style=\"display:none;\">\r\n            <text>模板ID: {{partJobVoClone.template.templateId}}</text>\r\n            <text>按钮状态: {{partJobVoClone.buttonStatus}}</text>\r\n            <text>是否已报名: {{partJobVoClone.hasApply}}</text>\r\n        </view>\r\n        <view class=\"foot-agreement\" v-if=\"agreementVo.result === 2\">\r\n            <view class=\"foot-agreement-left\">\r\n                <text class=\"ellipsis\">团团建议先阅读</text>\r\n                <text @tap=\"jumpToAgreement\" class=\"foot-agreement-green ellipsis ptp_exposure\" data-ptpid=\"m1dr-oerp-qafg-nvde\">《{{ agreementVo.title }}》</text>\r\n                哦～\r\n            </view>\r\n        </view>\r\n        <view class=\"foot-apply\" v-if=\"partJobVoClone.template.templateId !== 16 && partJobVoClone.template.templateId !== 13\">\r\n            <view class=\"action-buttons\">\r\n                <view class=\"relative\">\r\n                    <view @tap=\"collect\" class=\"shareCode ptp_exposure\" data-ptpid=\"63bf-1709-ae9f-e11b\">\r\n                        <view :class=\"'shareview-image iconfont ' + collectImgClone\"></view>\r\n                        <view class=\"shareview\">收藏</view>\r\n                    </view>\r\n                </view>\r\n                <view class=\"relative\">\r\n                    <view @tap=\"handleShare\" class=\"shareCode ptp_exposure\">\r\n                        <view class=\"shareview-image iconfont iconshare\"></view>\r\n                        <view class=\"shareview\">分享</view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            <view @tap=\"jumpToReport\" class=\"applybtn ptp_exposure\" data-ptpid=\"b9e6-1750-b71f-2f5e\" v-if=\"partJobVoClone.hasApply && partJobVoClone.buttonStatus !== 9\" :style=\"{background: t('color1')}\">\r\n                查看报名\r\n            </view>\r\n            <view class=\"applybtn gray\" v-if=\"partJobVoClone.buttonStatus === 3\">已结束</view>\r\n            <view class=\"applybtn gray\" v-if=\"partJobVoClone.buttonStatus === 4\">已暂停</view>\r\n            <view class=\"relative\">\r\n                <open-button @initData=\"onPromptlyClicked\" class=\"login-mask\" openType=\"getPhoneNumber\" ptpId=\"26ca-1dd6-b35f-b3d0-0\" v-if=\"!hasToken\"></open-button>\r\n                <view @tap=\"onPromptly\" class=\"applybtn ptp_exposure\" data-ptpid=\"26ca-1dd6-b35f-b3d0\" id=\"pid=26ca-1dd6-b35f-b3d0\" v-if=\"partJobVoClone.buttonStatus === 6\" :style=\"{background: t('color1')}\">\r\n                    {{ partJobVoClone.is_apply ? '已报名' : (agreementVo.result === 2 ? '我已同意，立即报名' : '立即报名') }}\r\n                </view>\r\n                <view @tap=\"onPromptly\" class=\"applybtn yellow ptp_exposure\" data-ptpid=\"e43c-1674-9609-8478\" v-if=\"partJobVoClone.buttonStatus === 7\">\r\n                    <view>排队报名</view>\r\n                    <view class=\"applybtn-desc\" v-if=\"partJobVoClone.queueCount >= 20\">（前面有{{ partJobVoClone.queueCount }}人待录取）</view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        <view class=\"foot-apply\" v-if=\"partJobVoClone.template.templateId === 13\">\r\n            <view class=\"apply-price\" v-if=\"partJobVoClone.jobFeeVO.rushStatus === 1\">¥ {{ partJobVoClone.jobFeeVO.feeRushPrice }}</view>\r\n            <view\r\n                @tap=\"jumpToReport\"\r\n                :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' ptp_exposure'\"\r\n                data-ptpid=\"b9e6-1750-b71f-2f5e\"\r\n                v-if=\"partJobVoClone.hasApply && partJobVoClone.buttonStatus !== 9\"\r\n                :style=\"{background: t('color1')}\"\r\n            >\r\n                查看报名\r\n            </view>\r\n            <view :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' gray'\" v-if=\"partJobVoClone.buttonStatus === 3\">已结束</view>\r\n            <view :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' gray'\" v-if=\"partJobVoClone.buttonStatus === 4\">已暂停</view>\r\n            <view class=\"relative\" v-if=\"partJobVoClone.buttonStatus === 6\">\r\n                <open-button @initData=\"onPromptlyClicked\" class=\"login-mask\" openType=\"getPhoneNumber\" ptpId=\"dkfi-dp01-cjgf-k2jf-0\" v-if=\"!hasToken\"></open-button>\r\n                <view\r\n                    @tap=\"onPromptly\"\r\n                    :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' ptp_exposure'\"\r\n                    data-ptpid=\"9f71-dk1n-zbcn-du1n\"\r\n                    v-if=\"partJobVoClone.jobFeeVO.rushStatus === 1\"\r\n                    :style=\"{background: t('color1')}\"\r\n                >\r\n                    {{ partJobVoClone.jobFeeVO.buttons.btn2 }}\r\n                </view>\r\n                <view\r\n                    @tap=\"onPromptly\"\r\n                    :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' ptp_exposure'\"\r\n                    data-ptpid=\"dkfi-dp01-cjgf-k2jf\"\r\n                    v-else\r\n                    :style=\"{background: t('color1')}\"\r\n                >\r\n                    {{ partJobVoClone.jobFeeVO.buttons.btn1 }}\r\n                </view>\r\n            </view>\r\n            <view\r\n                @tap=\"payHandle\"\r\n                :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' ptp_exposure'\"\r\n                data-ptpid=\"127f-1kfp-0281-jkd1\"\r\n                v-if=\"partJobVoClone.buttonStatus === 9\"\r\n            >\r\n                待支付\r\n            </view>\r\n            <view class=\"relative\" v-if=\"partJobVoClone.buttonStatus === 7\">\r\n                <open-button @initData=\"onPromptlyClicked\" class=\"login-mask\" openType=\"getPhoneNumber\" ptpId=\"e43c-1674-9609-8478-0\" v-if=\"!hasToken\"></open-button>\r\n                <view\r\n                    @tap=\"onPromptly\"\r\n                    :class=\"'course-apply-btn ' + (partJobVoClone.jobFeeVO.rushStatus !== 1 ? 'width-686' : '') + ' column ptp_exposure'\"\r\n                    data-ptpid=\"e43c-1674-9609-8478\"\r\n                >\r\n                    <view>排队报名</view>\r\n                    <view class=\"applybtn-desc\" v-if=\"partJobVoClone.queueCount >= 20\">（前面有{{ partJobVoClone.queueCount }}人待录取）</view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        <view class=\"foot-apply\" v-if=\"partJobVoClone.template.templateId === 16\">\r\n            <view\r\n                @tap=\"jumpToReport\"\r\n                class=\"apply-btn ptp_exposure\"\r\n                data-ptpid=\"b9e6-1750-b71f-2f5e\"\r\n                id=\"pid=b9e6-1750-b71f-2f5e\"\r\n                v-if=\"partJobVoClone.hasApply && partJobVoClone.buttonStatus !== 9\"\r\n                :style=\"{background: t('color1')}\"\r\n            >\r\n                查看报名\r\n            </view>\r\n            <view class=\"apply-btn gray\" v-if=\"partJobVoClone.buttonStatus === 3\">已结束</view>\r\n            <view class=\"apply-btn gray\" v-if=\"partJobVoClone.buttonStatus === 4\">已暂停</view>\r\n            <view class=\"relative\">\r\n                <open-button @initData=\"onPromptlyClicked\" class=\"login-mask\" openType=\"getPhoneNumber\" ptpId=\"9f71-dk1n-zbcn-du1n-0\" v-if=\"!hasToken\"></open-button>\r\n                <view @tap=\"onPromptly\" class=\"apply-btn ptp_exposure\" data-ptpid=\"9f71-dk1n-zbcn-du1n\" v-if=\"partJobVoClone.buttonStatus === 6\" :style=\"{background: t('color1')}\">\r\n                    {{ partJobVoClone.is_apply ? '已报名' : (agreementVo.result === 2 ? baseInfo.noProtocolBtnText || baseInfo.btnText : baseInfo.btnText) }}\r\n                </view>\r\n                <view @tap=\"onPromptly\" class=\"apply-btn column ptp_exposure\" data-ptpid=\"e43c-1674-9609-8478\" v-if=\"partJobVoClone.buttonStatus === 7\">\r\n                    <view>排队报名</view>\r\n                    <view class=\"applybtn-desc\" v-if=\"partJobVoClone.queueCount >= 20\">（前面有{{ partJobVoClone.queueCount }}人待录取）</view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport openButton from '../../components/openButton/openButton';\r\nvar app = getApp();\r\n\r\nexport default {\r\n    components: {\r\n        openButton\r\n    },\r\n    data() {\r\n        return {\r\n            partJobFavoriteId: '',\r\n            partJobVoClone: {\r\n                jobFeeVO: {\r\n                    buttons: {\r\n                        btn2: '',\r\n                        btn1: ''\r\n                    }\r\n                }\r\n            },\r\n\t\t\tcollectImgClone: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log('applyButton mounted');\r\n        console.log('初始props数据:', {\r\n            partJobVo: this.partJobVo,\r\n            hasToken: this.hasToken,\r\n            template: this.partJobVo.template,\r\n            buttonStatus: this.partJobVo.buttonStatus\r\n        });\r\n    },\r\n\twatch: {\r\n\t    partJobVo: {\r\n\t        handler: function (newVal, oldVal) {\r\n\t            console.log('applyButton - partJobVo 更新:', newVal);\r\n\t            this.partJobVoClone = newVal;\r\n\t        },\r\n\t        immediate: true,\r\n\t        deep: true\r\n\t    },\r\n\t    collectImg: {\r\n\t        handler: function (newVal, oldVal) {\r\n\t\t\t\tthis.collectImgClone = newVal;\r\n\t        },\r\n\t        immediate: true,\r\n\t\t\tdeep: true\r\n\t    }\r\n\t},\r\n    props: {\r\n        hasToken: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        isShow: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        partJobVo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        collectImg: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        buttonTitle: {\r\n            type: String,\r\n            default: '立即报名'\r\n        },\r\n        baseInfo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        agreementVo: {\r\n            type: Object,\r\n            default: () => ({})\r\n        }\r\n    },\r\n    methods: {\r\n        t(text) {\r\n            if(text=='color1'){\r\n                return getApp().globalData.initdata.color1;\r\n            }else if(text=='color2'){\r\n                return getApp().globalData.initdata.color2;\r\n            }else if(text=='color1rgb'){\r\n                var color1rgb = getApp().globalData.initdata.color1rgb;\r\n                return color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n            }else if(text=='color2rgb'){\r\n                var color2rgb = getApp().globalData.initdata.color2rgb;\r\n                return color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n            }else{\r\n                return getApp().globalData.initdata.textset[text] || text;\r\n            }\r\n        },\r\n        onLoginSuccess: function () {\r\n            this.$emit('loginSuccess');\r\n        },\r\n        onPromptlyClicked: function () {\r\n            this.$emit('loginSuccess');\r\n            this.onPromptly();\r\n        },\r\n        onPromptly: function () {\r\n            console.log('applyButton - onPromptly 被触发');\r\n            if (this.partJobVoClone.is_apply) {\r\n                uni.showToast({\r\n                    title: '您已报名过该职位',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n            console.log('按钮状态详情:', {\r\n                buttonStatus: this.partJobVoClone.buttonStatus,\r\n                templateId: this.partJobVoClone.template.templateId,\r\n                hasApply: this.partJobVoClone.hasApply,\r\n                hasToken: this.hasToken\r\n            });\r\n            console.log('完整的partJobVoClone:', this.partJobVoClone);\r\n            this.$emit('promptly');\r\n        },\r\n        payHandle: function () {\r\n            this.$emit('pay');\r\n        },\r\n        collect: function() {\r\n            console.log('收藏按钮被点击');\r\n            console.log('当前收藏状态:', this.collectImgClone);\r\n            this.$emit('collect');\r\n        },\r\n        jumpToReport: function () {},\r\n        jumpToAgreement: function () {},\r\n        cancelHandle: function () {},\r\n        handleShare() {\r\n            uni.navigateTo({\r\n                url: '/activity/commission/poster'\r\n            });\r\n        }\r\n    },\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n\t@import './applyButton.scss';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./applyButton.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./applyButton.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115079887\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}