{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?ae9f", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?31a5", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?a231", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?2da5", "uni-app:///zhaopin/company.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?5838", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/company.vue?171d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "moveDrag", "rate", "data", "loading", "isLoaded", "opt", "companyId", "companyName", "companyData", "time", "pageNum", "pageSize", "jobList", "current", "bannerList", "url", "type", "assistList", "isShowAll", "autoplay", "totalCount", "swiperAutoplay", "scrollTop", "disableScroll", "moduleList", "isEnd", "isModuleLoading", "onLoad", "onShow", "onReachBottom", "methods", "getCompanyDetail", "that", "app", "id", "console", "style", "serviceUrl", "team", "logo", "name", "companyEyeCheckInfo", "reg<PERSON>tatus", "fromTime", "regLocation", "creditCode", "orgNumber", "businessScope", "shareContentClassifys", "weixinTalk", "weixinFriend", "sinaWb", "qqshare", "qqtalk", "uni", "title", "icon", "getCompanyPositions", "company_id", "page", "limit", "addressDetail", "partJobId", "titleSimple", "salary", "clearingForm", "key", "value", "welfare", "dataSource", "jobLineType", "category", "entryCount", "companyType", "brandName", "companyLogo", "parentClassId", "classId", "c1", "c2", "c3", "labelList", "serviceLabels", "labelId", "labelName", "labelStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listStyle", "jobIntroduction", "introductionDesc", "newJobIntroduction", "ptpModParam", "loadmore", "showAllHandle", "<PERSON><PERSON><PERSON><PERSON>", "getNetworkType", "videoPlayHandle", "videoPauseHandle", "onShareAppMessage", "path", "disableScrollControl", "saveRef"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoIhxB;AAAA,eACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;EACAC;EACAC;IACA;IACAC;MACA;MACAC;MACAC;QACAC;MACA;QACAC;QACAH;QACA;UACA;UACA;UACAA;;UAEA;UACA;YACAA;cAAA;gBACAjB;gBACAC;cACA;YAAA;UACA;YACAgB;cACAjB;cACAC;YACA;UACA;UAEAgB;YACAI;YACAC;YACAC;YACAC;YACAC;YACAC;cACAzB;cACA0B;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;UACArB;QACA;UACAsB;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAzB;MACAC;QACAyB;QACAC;QACAC;MACA;QACAzB;QACAH;QACA;UACA;UACA;UACA;YAAA;cACA6B;cACAC;cACAP;cACAQ;cACAC;cACAC;gBACAC;gBACAC;cACA;cACAC;cACA7D;cACAgC;cACA8B;cACAC;cACAC;cACAC;cACAC;gBACAP;gBACAC;cACA;cACAO;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;gBACAC,gBACA;kBACAC;kBACAC;kBACAC;gBACA,EACA;gBACAC,aACA;kBACAH;kBACAC;kBACAC;gBACA;cAEA;cACAE;cACAC,kBACA;gBACAjC;gBACAkC;cACA,GACA;gBACAlC;gBACAkC;cACA,EACA;cACAC;cACAC;gBACAtB;cACA;YACA;UAAA;UAEA;YACArC;UACA;YACAA;UACA;UAEAA;UACAA;QACA;UACAsB;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAoC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;MACA;QACA3C;QACA4C;MACA;IACA;IACAC;MACA;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAA27C,CAAgB,s4CAAG,EAAC,C;;;;;;;;;;;ACA/8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/company.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/company.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./company.vue?vue&type=template&id=57cfcfee&scoped=true&\"\nvar renderjs\nimport script from \"./company.vue?vue&type=script&lang=js&\"\nexport * from \"./company.vue?vue&type=script&lang=js&\"\nimport style0 from \"./company.vue?vue&type=style&index=0&id=57cfcfee&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57cfcfee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/company.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./company.vue?vue&type=template&id=57cfcfee&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoaded ? _vm.bannerList.length : null\n  var g1 = _vm.isLoaded && g0 > 0 ? _vm.bannerList.length : null\n  var g2 = _vm.isLoaded && g0 > 0 && g1 > 1 ? _vm.bannerList.length : null\n  var g3 =\n    _vm.isLoaded && _vm.companyData.style ? _vm.companyData.team.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./company.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./company.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\" :disableScroll=\"disableScroll\" v-if=\"isLoaded\">\r\n\t\t<view class=\"header\" v-if=\"bannerList.length > 0\">\r\n\t\t\t<swiper :autoplay=\"swiperAutoplay\" @change=\"currentHandle\" class=\"swiper\" :current=\"current\" :indicatorDots=\"false\">\r\n\t\t\t\t<swiper-item\r\n\t\t\t\t\t:class=\"'header-swiper-item ptp_exposure_swiper_' + index\"\r\n\t\t\t\t\t:id=\"'pid=2fm1-zmc2-29fj-1kfn-' + (index + 1)\"\r\n\t\t\t\t\tkey=\"url\"\r\n\t\t\t\t\tv-for=\"(item, index) in bannerList\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<video\r\n\t\t\t\t\t\t:autoplay=\"autoplay\"\r\n\t\t\t\t\t\t@pause=\"videoPauseHandle\"\r\n\t\t\t\t\t\t@play=\"videoPlayHandle\"\r\n\t\t\t\t\t\tclass=\"swiper-item-pic\"\r\n\t\t\t\t\t\tid=\"video\"\r\n\t\t\t\t\t\t:muted=\"true\"\r\n\t\t\t\t\t\t:poster=\"item.poster\"\r\n\t\t\t\t\t\t:src=\"item.url\"\r\n\t\t\t\t\t\tv-if=\"item.type === 'video'\"\r\n\t\t\t\t\t></video>\r\n\r\n\t\t\t\t\t<image class=\"swiper-item-pic\" mode=\"aspectFill\" :src=\"item.url\" v-if=\"item.type === 'pic'\"></image>\r\n\r\n\t\t\t\t\t<view class=\"header-swiper-bottom\" v-if=\"item.type === 'pic'\"></view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"swiper-index\" v-if=\"bannerList.length > 1\">{{ current + 1 }}/{{ bannerList.length }}</view>\r\n\t\t</view>\r\n\t\t<view class=\"name\">{{ companyName }}</view>\r\n\t\t<view class=\"info\">\r\n\t\t\t<block v-if=\"companyData.style\">\r\n\t\t\t\t<image class=\"info-image\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20201203_companyIcon.png\"></image>\r\n\t\t\t\t<view>青团认证</view>\r\n\t\t\t\t<image class=\"info-icon\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210106_icon.png\" v-if=\"companyData.companyLabelType === 1\"></image>\r\n\t\t\t\t<image class=\"info-icon2\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210106_icon2.png\" v-if=\"companyData.companyLabelType === 2\"></image>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<image class=\"info-image\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20200821_authIcon.png\"></image>\r\n\t\t\t\t<view>企业认证</view>\r\n\t\t\t\t<image class=\"info-icon\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210106_icon.png\" v-if=\"companyData.companyLabelType === 1\"></image>\r\n\t\t\t\t<image class=\"info-icon2\" mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20210106_icon2.png\" v-if=\"companyData.companyLabelType === 2\"></image>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<block v-if=\"companyData.style\">\r\n\t\t\t<view class=\"title-box\">\r\n\t\t\t\t<view>服务保障</view>\r\n\t\t\t\t<view class=\"title-box-tips\">官方自营</view>\r\n\t\t\t</view>\r\n\t\t\t<image class=\"serve-image\" mode=\"widthFix\" :src=\"companyData.serviceUrl\" v-if=\"companyData.serviceUrl\"></image>\r\n\t\t\t<view class=\"title-box\">\r\n\t\t\t\t<view>招聘顾问团队</view>\r\n\t\t\t\t<view class=\"title-box-tips\">官方自营</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"assist-box\" v-if=\"companyData.team.length > 0\">\r\n\t\t\t\t<scroll-view class=\"assist-list\" :scrollX=\"true\">\r\n\t\t\t\t\t<view class=\"assist-list-item\" v-for=\"(item, index) in companyData.team\" :key=\"index\">\r\n\t\t\t\t\t\t<image class=\"assist-list-img ptp_exposure_static\" :id=\"'pid=dj1k-dn1b-ug1b-vh72-' + (index + 1)\" :src=\"item.headImg\" v-if=\"item.headImg\"></image>\r\n\r\n\t\t\t\t\t\t<image class=\"assist-list-icon\" src=\"https://qiniu-image.qtshe.com/20201203_companyIcon3.png\"></image>\r\n\r\n\t\t\t\t\t\t<view class=\"assist-list-title\">{{ item.userName }}</view>\r\n\r\n\t\t\t\t\t\t<view class=\"assist-list-desc\">{{ item.people }}</view>\r\n\r\n\t\t\t\t\t\t<view class=\"assist-list-rate\"><rate :limit=\"5\" :rate=\"item.rank\"></rate></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<view class=\"company-title\">企业基本信息</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"part\">\r\n\t\t\t\t<view class=\"part-title\">企业类型</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.type === 1 ? '个体' : '公司' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\">\r\n\t\t\t\t<view class=\"part-title\">经营状态</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.regStatus || '--' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\">\r\n\t\t\t\t<view class=\"part-title\">成立日期</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ time }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\">\r\n\t\t\t\t<view class=\"part-title\">注册地址</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.regLocation || '--' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\">\r\n\t\t\t\t<view class=\"part-title\">统一信用代码</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.creditCode || '--' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\" v-if=\"isShowAll\">\r\n\t\t\t\t<view class=\"part-title\">组织机构代码</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.orgNumber || '--' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"part\" v-if=\"isShowAll\">\r\n\t\t\t\t<view class=\"part-title\">经营范围</view>\r\n\t\t\t\t<view class=\"part-desc\">{{ companyData.companyEyeCheckInfo.businessScope || '--' }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-bottom\">\r\n\t\t\t\t<view>以上信息由</view>\r\n\t\t\t\t<image mode=\"scaleToFill\" src=\"https://qiniu-image.qtshe.com/20200822_companyIcon.png\"></image>\r\n\t\t\t\t<view>提供</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"showAllHandle\" class=\"content-button ptp_exposure_static\" data-ptpid=\"ff0d-1875-b777-e0c0\" id=\"pid=ff0d-1875-b777-e0c0\" v-if=\"!isShowAll\">\r\n\t\t\t\t<text style=\"margin-right: 4rpx\">展开全部</text>\r\n\t\t\t\t<text class=\"iconfont iconarrow_down\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-mask\" v-if=\"!isShowAll\"></view>\r\n\t\t</view>\r\n\t\t<move-drag\r\n\t\t\t@disable=\"disableScrollControl\"\r\n\t\t\t@loadmore=\"loadmore\"\r\n\t\t\t:isEnd=\"isEnd\"\r\n\t\t\t:isLoading=\"isModuleLoading\"\r\n\t\t\t:limit=\"400\"\r\n\t\t\t:list=\"moduleList\"\r\n\t\t\tptpId=\"22df-1mag-1kfy-290f\"\r\n\t\t\tref=\"saveRef\"\r\n\t\t\t:start=\"0\"\r\n\t\t\t:startDirction=\"true\"\r\n\t\t\t:title=\"'在招岗位 ' + totalCount\"\r\n\t\t></move-drag>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport moveDrag from './components/moveDrag/index';\r\nimport rate from './components/rate/index';\r\n\r\nvar app = getApp();\r\nexport default {\r\n\tcomponents: {\r\n\t\tmoveDrag,\r\n\t\trate\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tisLoaded: false,\r\n\t\t\topt: {},\r\n\t\t\tcompanyId: '',\r\n\t\t\tcompanyName: '',\r\n\t\t\tcompanyData: {},\r\n\t\t\ttime: '--',\r\n\t\t\tpageNum: 1,\r\n\t\t\tpageSize: 10,\r\n\t\t\tjobList: [],\r\n\t\t\tcurrent: 0,\r\n\t\t\tbannerList: [\r\n\t\t\t\t{\r\n\t\t\t\t\turl: 'https://qiniu-image.qtshe.com/20201210_companyBg.png',\r\n\t\t\t\t\ttype: 'pic'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tassistList: [],\r\n\t\t\tisShowAll: false,\r\n\t\t\tautoplay: false,\r\n\t\t\ttotalCount: 0,\r\n\t\t\tswiperAutoplay: true,\r\n\t\t\tscrollTop: 0,\r\n\t\t\tdisableScroll: false,\r\n\t\t\tmoduleList: [],\r\n\t\t\tisEnd: false,\r\n\t\t\tisModuleLoading: false\r\n\t\t};\r\n\t},\r\n\tonLoad: function(opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getCompanyDetail();\r\n\t\tthis.getCompanyPositions();\r\n\t},\r\n\tonShow: function() {},\r\n\tonReachBottom: function() {},\r\n\tmethods: {\r\n\t\t// 获取企业详情\r\n\t\tgetCompanyDetail() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiZhaopin/getCompanyDetail', {\r\n\t\t\t\tid: that.opt.id\r\n\t\t\t}, function(res) {\r\n\t\t\t\tconsole.log('企业详情接口返回:', res);\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status === 1) {\r\n\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\t// 处理企业基本信息\r\n\t\t\t\t\tthat.companyName = data.name;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理轮播图\r\n\t\t\t\t\tif(data.photos) {\r\n\t\t\t\t\t\tthat.bannerList = data.photos.split(',').map(url => ({\r\n\t\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\t\ttype: 'pic'\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.bannerList = [{\r\n\t\t\t\t\t\t\turl: data.logo || 'https://qiniu-image.qtshe.com/20201210_companyBg.png',\r\n\t\t\t\t\t\t\ttype: 'pic'\r\n\t\t\t\t\t\t}];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.companyData = {\r\n\t\t\t\t\t\tstyle: 0,\r\n\t\t\t\t\t\tserviceUrl: null,\r\n\t\t\t\t\t\tteam: null,\r\n\t\t\t\t\t\tlogo: data.logo || 'https://qiniu-image.qtshe.com/company_logo_default.png',\r\n\t\t\t\t\t\tname: data.name,\r\n\t\t\t\t\t\tcompanyEyeCheckInfo: {\r\n\t\t\t\t\t\t\ttype: 1,\r\n\t\t\t\t\t\t\tregStatus: data.nature || '--',\r\n\t\t\t\t\t\t\tfromTime: new Date(data.create_time).getTime() || '--',\r\n\t\t\t\t\t\t\tregLocation: data.address || '--',\r\n\t\t\t\t\t\t\tcreditCode: data.credit_code || '--',\r\n\t\t\t\t\t\t\torgNumber: data.org_number || '--',\r\n\t\t\t\t\t\t\tbusinessScope: data.introduction || '--'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tshareContentClassifys: {\r\n\t\t\t\t\t\t\tweixinTalk: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`,\r\n\t\t\t\t\t\t\tweixinFriend: `【${data.name}】${data.position_count}个职位招聘中，点击查看更多招聘信息！`,\r\n\t\t\t\t\t\t\tsinaWb: `【${data.name}】${data.position_count}个职位招聘中，点击查看更多招聘信息！`,\r\n\t\t\t\t\t\t\tqqshare: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`,\r\n\t\t\t\t\t\t\tqqtalk: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.isLoaded = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取企业详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取企业职位列表\r\n\t\tgetCompanyPositions() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.isModuleLoading = true;\r\n\t\t\tapp.get('ApiZhaopin/getCompanyPositions', {\r\n\t\t\t\tcompany_id: that.opt.id,\r\n\t\t\t\tpage: that.pageNum,\r\n\t\t\t\tlimit: that.pageSize\r\n\t\t\t}, function(res) {\r\n\t\t\t\tconsole.log('企业职位列表接口返回:', res);\r\n\t\t\t\tthat.isModuleLoading = false;\r\n\t\t\t\tif(res.status === 1) {\r\n\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\t// 处理职位列表\r\n\t\t\t\t\tconst newList = data.list.map(item => ({\r\n\t\t\t\t\t\taddressDetail: item.work_address,\r\n\t\t\t\t\t\tpartJobId: item.id,\r\n\t\t\t\t\t\ttitle: item.title,\r\n\t\t\t\t\t\ttitleSimple: item.title,\r\n\t\t\t\t\t\tsalary: item.salary,\r\n\t\t\t\t\t\tclearingForm: {\r\n\t\t\t\t\t\t\tkey: '0',\r\n\t\t\t\t\t\t\tvalue: '其他'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\twelfare: Object.values(item.formatted_options || {}).flat().join(','),\r\n\t\t\t\t\t\tcompanyName: data.company.name,\r\n\t\t\t\t\t\tlogo: data.company.logo,\r\n\t\t\t\t\t\tdataSource: 0,\r\n\t\t\t\t\t\tjobLineType: 1,\r\n\t\t\t\t\t\tcategory: 1,\r\n\t\t\t\t\t\tentryCount: item.views || 0,\r\n\t\t\t\t\t\tcompanyType: {\r\n\t\t\t\t\t\t\tkey: '1',\r\n\t\t\t\t\t\t\tvalue: '企业'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tbrandName: data.company.name,\r\n\t\t\t\t\t\tcompanyLogo: data.company.logo,\r\n\t\t\t\t\t\tparentClassId: 10139,\r\n\t\t\t\t\t\tclassId: 10465,\r\n\t\t\t\t\t\tc1: *********,\r\n\t\t\t\t\t\tc2: *********,\r\n\t\t\t\t\t\tc3: *********,\r\n\t\t\t\t\t\tlabelList: {\r\n\t\t\t\t\t\t\tserviceLabels: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tlabelId: 61,\r\n\t\t\t\t\t\t\t\t\tlabelName: '企业认证',\r\n\t\t\t\t\t\t\t\t\tlabelStyle: '{\"id\":10,\"icon\":\"\",\"color\":\"#72AAFA\",\"borderColor\":\"#72AAFA\",\"background\":\"#FFFFFF\"}'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\tdescLabels: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tlabelId: 19,\r\n\t\t\t\t\t\t\t\t\tlabelName: '最新发布',\r\n\t\t\t\t\t\t\t\t\tlabelStyle: '{\"id\":6,\"icon\":\"\",\"color\":\"#FA5555\",\"borderColor\":\"#FEEEEE\",\"background\":\"#FEEEEE\"}'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlistStyle: 3,\r\n\t\t\t\t\t\tjobIntroduction: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttitle: '学历要求',\r\n\t\t\t\t\t\t\t\tintroductionDesc: item.education\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttitle: '经验要求',\r\n\t\t\t\t\t\t\t\tintroductionDesc: item.experience\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tnewJobIntroduction: item.description ? item.description.replace(/<[^>]+>/g,'').slice(0, 50) + '...' : '',\r\n\t\t\t\t\t\tptpModParam: {\r\n\t\t\t\t\t\t\tdataSource: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}));\r\n\r\n\t\t\t\t\tif(that.pageNum === 1) {\r\n\t\t\t\t\t\tthat.moduleList = newList;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.moduleList = [...that.moduleList, ...newList];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.totalCount = data.total;\r\n\t\t\t\t\tthat.isEnd = that.moduleList.length >= data.total;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取职位列表失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tloadmore: function() {\r\n\t\t\tif(!this.isEnd && !this.isModuleLoading) {\r\n\t\t\t\tthis.pageNum++;\r\n\t\t\t\tthis.getCompanyPositions();\r\n\t\t\t}\r\n\t\t},\r\n\t\tshowAllHandle: function() {\r\n\t\t\tthis.isShowAll = true;\r\n\t\t},\r\n\t\tcurrentHandle: function(t) {},\r\n\t\tgetNetworkType: function() {},\r\n\t\tvideoPlayHandle: function() {},\r\n\t\tvideoPauseHandle: function() {},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '青团社兼职',\r\n\t\t\t\tpath: '/zhaopin/company?companyId=' + this.opt.id\r\n\t\t\t};\r\n\t\t},\r\n\t\tdisableScrollControl: function(t) {\r\n\t\t\tthis.disableScroll = t;\r\n\t\t},\r\n\t\tsaveRef: function(t) {}\r\n\t}\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n@import './company.scss';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./company.vue?vue&type=style&index=0&id=57cfcfee&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./company.vue?vue&type=style&index=0&id=57cfcfee&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115060327\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}