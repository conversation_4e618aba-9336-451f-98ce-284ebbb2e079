{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?88f3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?aa9f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?e0e0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?22cf", "uni-app:///pagesExt/zhuanzhang/zhuanzhang.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?ad99", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/zhuanzhang.vue?0f8d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "mobile", "textset", "canrecharge", "userinfo", "giveset", "shuoming", "money", "paypwd", "moneyduan", "give_coupon_list", "give_coupon_show", "give_coupon_close_url", "caninput", "transfer", "needPayPwd", "hasPayPwd", "fromSetPwd", "onLoad", "onPullDownRefresh", "onShow", "console", "methods", "getdata", "app", "that", "uni", "title", "moneyinput", "mobileinput", "paypwdinput", "gotoSetPayPwd", "selectgiveset", "topay", "setTimeout", "params"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyDnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;QACAA;QACA;UACAA;UACA;QACA;QACAC;QACAA;QACAC;UACAC;QACA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAA;QACA;QAEA;UACAA;QACA;QAEAJ;UACAN;UACAC;QACA;QAEAS;MACA;IACA;IACAG;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;UACA;YACA;cACAf;YACA;UACA;QACA;QACA;QACA;MACA;IACA;IACAoB;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAP;IACA;IACAQ;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MAEA;QACA;UACAT;UACAU;YACAT;UACA;UACA;QACA;QAEA;UACA;QACA;MACA;MAEAJ;QACAd;QACAN;QACAc;MACA;MAEAU;MAEA;QACAlB;QACAN;MACA;MAEA;QACAkC;MACA;MAEAX;QACAC;QACAA;QACA;UACAD;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zhuanzhang/zhuanzhang.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zhuanzhang/zhuanzhang.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./zhuanzhang.vue?vue&type=template&id=0891f42b&\"\nvar renderjs\nimport script from \"./zhuanzhang.vue?vue&type=script&lang=js&\"\nexport * from \"./zhuanzhang.vue?vue&type=script&lang=js&\"\nimport style0 from \"./zhuanzhang.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zhuanzhang/zhuanzhang.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhuanzhang.vue?vue&type=template&id=0891f42b&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"余额\") : null\n  var m2 =\n    _vm.isload && !(_vm.needPayPwd && !_vm.hasPayPwd) ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && _vm.transfer ? _vm.t(\"color2\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhuanzhang.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhuanzhang.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<block v-if=\"isload\">\n\t\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\">\n\t\t\t\t<view class=\"f1\">{{t('余额')}}转账</view>\n\t\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\">￥</text>{{userinfo.money}}</view>\n\t\t\t\t<view class=\"f3\" @tap=\"goto\" data-url=\"/pagesExb/money/moneylog?st=4\"><text>转账记录</text><text\n\t\t\t\t\t\tclass=\"iconfont iconjiantou\" style=\"font-size:20rpx\"></text></view>\n\t\t\t</view>\n\t\t\t<view class=\"content2\">\n\t\t\t\t<block v-if=\"caninput==1\">\n\t\t\t\t\t<view class=\"item3\">\n\t\t\t\t\t\t<view class=\"f1\">￥</view>\n\t\t\t\t\t\t<view class=\"f2\"><input type=\"digit\" name=\"money\" :value=\"money\" placeholder=\"请输入转账金额\"\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" @input=\"moneyinput\"\n\t\t\t\t\t\t\t\tstyle=\"font-size:60rpx\" /></view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-if=\"caninput==1\">\n\t\t\t\t\t<view class=\"item3\">\n\t\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"mobile\" :value=\"mobile\" placeholder=\"请输入对方注册的手机号\"\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" @input=\"mobileinput\"\n\t\t\t\t\t\t\t\tstyle=\"font-size:60rpx\" /></view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-if=\"caninput==1 && needPayPwd && hasPayPwd\">\n\t\t\t\t\t<view class=\"item3\">\n\t\t\t\t\t\t<view class=\"f2\"><input type=\"password\" name=\"paypwd\" :value=\"paypwd\" placeholder=\"请输入支付密码\"\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" @input=\"paypwdinput\"\n\t\t\t\t\t\t\t\tstyle=\"font-size:60rpx\" /></view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-if=\"caninput==1 && needPayPwd && !hasPayPwd\">\n\t\t\t\t\t<view class=\"pay-pwd-tip\">\n\t\t\t\t\t\t<view class=\"tip-text\">您需要设置支付密码才能进行转账</view>\n\t\t\t\t\t\t<view class=\"set-pwd-btn\" @tap=\"gotoSetPayPwd\">立即设置</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<view style=\"margin-top:40rpx;padding:0 30rpx;line-height:42rpx;\" v-if=\"shuoming\">\n\t\t\t\t\t<parse :content=\"shuoming\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\" v-if=\"!(needPayPwd && !hasPayPwd)\">\n\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去转账</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"op\" v-if=\"transfer\">\n\t\t\t\t<view class=\"btn\" @tap=\"goto\" data-url=\"rechargeToMember\" :style=\"{background:t('color2')}\">转账</view>\n\t\t\t</view>\n\t\t</block>\n\t\t<loading v-if=\"loading\"></loading>\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\topt: {},\n\t\t\t\tloading: false,\n\t\t\t\tisload: false,\n\t\t\t\tmenuindex: -1,\n\t\t\t\tmobile:'',\n\t\t\t\ttextset: {},\n\t\t\t\tcanrecharge: 0,\n\t\t\t\tuserinfo: {},\n\t\t\t\tgiveset: [],\n\t\t\t\tshuoming: '',\n\t\t\t\tmoney: '',\n\t\t\t\tpaypwd:'',\n\t\t\t\tmoneyduan: 0,\n\t\t\t\tgive_coupon_list: \"\",\n\t\t\t\tgive_coupon_show: false,\n\t\t\t\tgive_coupon_close_url: \"\",\n\t\t\t\tcaninput: 1,\n\t\t\t\ttransfer: false,\n\t\t\t\tneedPayPwd: false,\n\t\t\t\thasPayPwd: false,\n\t\t\t\tfromSetPwd: false\n\t\t\t};\n\t\t},\n\n\t\tonLoad: function(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.getdata();\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.getdata();\n\t\t},\n\t\tonShow: function() {\n\t\t\tif(this.isload) {\n\t\t\t\tconsole.log('2024-01-01 00:00:00-INFO-[zhuanzhang.vue][onShow_001] 页面显示，检查是否需要刷新');\n\t\t\t\tthis.getdata();\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgetdata: function() {\n\t\t\t\tvar that = this;\n\t\t\t\tapp.loading = true;\n\t\t\t\tapp.get('ApiMoney/recharge', {}, function(res) {\n\t\t\t\t\tapp.loading = false;\n\t\t\t\t\tif (res.canrecharge == 0) {\n\t\t\t\t\t\tapp.goto('moneylog?st=0', 'redirect');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('余额') + '转账'\n\t\t\t\t\t});\n\t\t\t\t\tthat.canrecharge = res.canrecharge;\n\t\t\t\t\tthat.giveset = res.giveset;\n\t\t\t\t\tthat.caninput = res.caninput;\n\t\t\t\t\tthat.shuoming = res.shuoming;\n\t\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\t\tthat.transfer = res.transfer;\n\t\t\t\t\t\n\t\t\t\t\tif (res.money_transfer_need_paypwd !== undefined) {\n\t\t\t\t\t\tthat.needPayPwd = res.money_transfer_need_paypwd == 1;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (res.has_paypwd !== undefined) {\n\t\t\t\t\t\tthat.hasPayPwd = res.has_paypwd == 1;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('2024-01-01 00:00:00-INFO-[zhuanzhang.vue][getdata_001] 支付密码配置:', {\n\t\t\t\t\t\tneedPayPwd: that.needPayPwd, \n\t\t\t\t\t\thasPayPwd: that.hasPayPwd\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tthat.loaded();\n\t\t\t\t});\n\t\t\t},\n\t\t\tmoneyinput: function(e) {\n\t\t\t\tvar money = e.detail.value;\n\t\t\t\tvar giveset = this.giveset;\n\n\t\t\t\tif (parseFloat(money) < 0) {\n\t\t\t\t\tapp.error('必须大于0');\n\t\t\t\t} else {\n\t\t\t\t\tvar moneyduan = 0;\n\t\t\t\t\tif (giveset.length > 0) {\n\t\t\t\t\t\tfor (var i in giveset) {\n\t\t\t\t\t\t\tif (money * 1 >= giveset[i]['money'] * 1 && giveset[i]['money'] * 1 > moneyduan) {\n\t\t\t\t\t\t\t\tmoneyduan = giveset[i]['money'] * 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.money = money;\n\t\t\t\t\tthis.moneyduan = moneyduan;\n\t\t\t\t}\n\t\t\t},\n\t\t\tmobileinput: function(e) {\n\t\t\t\tvar mobile = e.detail.value;\n\t\t\t\tthis.mobile = mobile;\n\t\t\t},\n\t\t\tpaypwdinput: function(e) {\n\t\t\t\tvar paypwd = e.detail.value;\n\t\t\t\tthis.paypwd = paypwd.trim();\n\t\t\t},\n\t\t\tgotoSetPayPwd: function() {\n\t\t\t\tthis.fromSetPwd = true;\n\t\t\t\tapp.goto('/pagesExa/my/paypwd');\n\t\t\t},\n\t\t\tselectgiveset: function(e) {\n\t\t\t\tvar money = e.currentTarget.dataset.money;\n\t\t\t\tthis.money = money;\n\t\t\t\tthis.moneyduan = money;\n\t\t\t},\n\t\t\ttopay: function(e) {\n\t\t\t\tvar that = this;\n\t\t\t\tvar money = that.money;\n\t\t\t\tvar mobile = that.mobile;\n\t\t\t\tvar paypwd = that.paypwd;\n\t\t\t\t\n\t\t\t\tif(!money){\n\t\t\t\t\treturn app.error('请输入转账金额');\n\t\t\t\t}\n\t\t\t\tif(parseFloat(money) > parseFloat(this.userinfo.money)){\n\t\t\t\t\treturn app.error('转账金额超过余额');\n\t\t\t\t}\n\t\t\t\tif(!mobile){\n\t\t\t\t\treturn app.error('请输入手机号');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif(this.needPayPwd) {\n\t\t\t\t\tif(!this.hasPayPwd) {\n\t\t\t\t\t\tapp.error('请先设置支付密码');\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\tthat.gotoSetPayPwd();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif(!paypwd) {\n\t\t\t\t\t\treturn app.error('请输入支付密码');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('2024-01-01 00:00:00-INFO-[zhuanzhang.vue][topay_001] 提交转账:', {\n\t\t\t\t\tmoney: money,\n\t\t\t\t\tmobile: mobile,\n\t\t\t\t\tneedPayPwd: that.needPayPwd\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthat.loading = true;\n\t\t\t\t\n\t\t\t\tvar params = {\n\t\t\t\t\tmoney: money,\n\t\t\t\t\tmobile: mobile\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif(this.needPayPwd) {\n\t\t\t\t\tparams.paypwd = paypwd;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tapp.post('ApiMoney/rechargeToMember', params, function(res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error('转账成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column\n\t}\n\n\t.mymoney {\n\t\twidth: 94%;\n\t\tmargin: 20rpx 3%;\n\t\tborder-radius: 10rpx 56rpx 10rpx 10rpx;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 70rpx 0\n\t}\n\n\t.mymoney .f1 {\n\t\tmargin: 0 0 0 60rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\tfont-size: 24rpx;\n\t}\n\n\t.mymoney .f2 {\n\t\tmargin: 20rpx 0 0 60rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 64rpx;\n\t\tfont-weight: bold\n\t}\n\n\t.mymoney .f3 {\n\t\theight: 56rpx;\n\t\tpadding: 0 10rpx 0 20rpx;\n\t\tborder-radius: 28rpx 0px 0px 28rpx;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tfont-size: 20rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: absolute;\n\t\ttop: 94rpx;\n\t\tright: 0\n\t}\n\n\t.content2 {\n\t\twidth: 94%;\n\t\tmargin: 10rpx 3%;\n\t\tborder-radius: 10rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground: #fff\n\t}\n\n\t.content2 .item1 {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\tborder-bottom: 1px solid #F0F0F0;\n\t\tpadding: 0 30rpx\n\t}\n\n\t.content2 .item1 .f1 {\n\t\tflex: 1;\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\theight: 120rpx;\n\t\tline-height: 120rpx\n\t}\n\n\t.content2 .item1 .f2 {\n\t\tcolor: #FC4343;\n\t\tfont-size: 44rpx;\n\t\tfont-weight: bold;\n\t\theight: 120rpx;\n\t\tline-height: 120rpx\n\t}\n\n\t.content2 .item2 {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\tpadding: 0 30rpx\n\t}\n\n\t.content2 .item2 .f1 {\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tcolor: #999999;\n\t\tfont-size: 28rpx\n\t}\n\n\t.content2 .item3 {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\tpadding: 0 30rpx;\n\t\tborder-bottom: 1px solid #F0F0F0;\n\t}\n\n\t.content2 .item3 .f1 {\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tfont-size: 60rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\tmargin-right: 20rpx\n\t}\n\n\t.content2 .item3 .f2 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 60rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold\n\t}\n\n\t.content2 .item3 .f2 input {\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t}\n\t\n\t.pay-pwd-tip {\n\t\tmargin: 30rpx;\n\t\tpadding: 30rpx;\n\t\tbackground-color: #FFF8E7;\n\t\tborder-radius: 10rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.pay-pwd-tip .tip-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #FF9500;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.pay-pwd-tip .set-pwd-btn {\n\t\tpadding: 15rpx 40rpx;\n\t\tbackground-color: #FF9500;\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 28rpx;\n\t\tborder-radius: 40rpx;\n\t}\n\n\t.op {\n\t\twidth: 96%;\n\t\tmargin: 20rpx 2%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 40rpx\n\t}\n\n\t.op .btn {\n\t\tflex: 1;\n\t\theight: 100rpx;\n\t\tline-height: 100rpx;\n\t\tbackground: #07C160;\n\t\twidth: 90%;\n\t\tmargin: 0 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\t.op .btn .img {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-right: 20rpx\n\t}\n\n\t.giveset {\n\t\twidth: 100%;\n\t\tpadding: 20rpx 0rpx;\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: center\n\t}\n\n\t.giveset .item {\n\t\tmargin: 10rpx;\n\t\tpadding: 15rpx 0;\n\t\twidth: 210rpx;\n\t\theight: 120rpx;\n\t\tbackground: #FDF6F6;\n\t\tborder-radius: 10rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\t.giveset .item .t1 {\n\t\tcolor: #545454;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.giveset .item .t2 {\n\t\tcolor: #8C8C8C;\n\t\tfont-size: 20rpx;\n\t\tmargin-top: 6rpx\n\t}\n\n\t.giveset .item.active .t1 {\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx\n\t}\n\n\t.giveset .item.active .t2 {\n\t\tcolor: #fff;\n\t\tfont-size: 20rpx\n\t}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhuanzhang.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhuanzhang.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102872\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}