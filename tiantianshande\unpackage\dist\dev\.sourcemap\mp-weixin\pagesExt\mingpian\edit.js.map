{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?85fa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?d718", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?9754", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?632f", "uni-app:///pagesExt/mingpian/edit.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?cffe", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/mingpian/edit.vue?450e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "field_list", "pagecontent", "bgpic", "headimg", "bglist", "bglistshow", "address", "latitude", "longitude", "test", "onLoad", "methods", "getdata", "that", "app", "subform", "console", "setTimeout", "uploadbgpic", "uni", "itemList", "success", "currentTarget", "dataset", "field", "pernum", "bgChange", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "dialogDetailtxtConfirm", "detailAddpic", "pics", "detailAddvideo", "sourceType", "url", "filePath", "name", "fail", "detailMoveup", "detailMovedown", "detailMovedel", "changeBglistDialog", "uploadimg", "removeimg", "bgpics", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9NA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqO7wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAE;MACA;MACA;MACAC;MACA;QACAF;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACA;QACAE;QACAA;QACA;UACAF;UAAA;QACA;MACA;MACAA;MACAA;QAAAf;QAAAE;MAAA;QACA;UACAa;QACA;UACAA;UACAG;YACAH;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACA;QACAC;UACAC;UACAC;YACAL;YACA;cACAH;YACA;cACAA;gBAAAS;kBAAAC;oBAAAC;oBAAAC;kBAAA;gBAAA;cAAA;YACA;UACA;QACA;MACA;QACAZ;UAAAS;YAAAC;cAAAC;cAAAC;YAAA;UAAA;QAAA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAb;MACA;IACA;IACAc;MACA;MACAd;MACA;MACA;MACAf;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACA;MACA;IACA;IACA8B;MACA;MACAjB;QACA;QACA;QACA;UACA;UACAkB;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACA;QACA/B;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;cAAA;YAAA;YAAA;cAAA;YAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAY;MACA;IACA;IAEAoB;MACA;MACAd;QACAe;QACAb;UACA;UACAP;UACAK;YACAgB;YACAC;YACAC;YACAhB;cACAP;cACA;cACA;gBACAD;gBACA;gBACA;gBACAZ;kBAAA;kBAAA;kBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;sBAAA;oBAAA;oBAAA;sBAAA;oBAAA;kBAAA;kBAAA;kBAAA;kBAAA;gBAAA;gBACAY;cACA;gBACAC;cACA;YACA;YACAwB;cACAxB;cACAA;YACA;UACA;QACA;QACAwB;UACAtB;QACA;MACA;IACA;;IACAuB;MACA;MACA;MACA,eACAtC;IACA;IACAuC;MACA;MACA;MACA,oCACAvC;IACA;IACAwC;MACA;MACA;MACAxC;IACA;IACAyC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA7B;QACA;UACAkB;QACA;QACA;QACA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;QACA;QACAC;QACAhC;MACA;QACA;QACAV;QACAU;MACA;IACA;IACAiC;MACA9B;MACA;MACAG;QACAE;UACAL;UACAH;UACAA;UACAA;QACA;QACAyB;UACAtB;UACA;YACA;YACAF;cACAK;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9dA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/mingpian/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/mingpian/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=03ec6c1a&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/mingpian/edit.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=template&id=03ec6c1a&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.bgpic.length : null\n  var g1 = _vm.isload ? _vm.bgpic.join(\",\") : null\n  var g2 = _vm.isload ? _vm.headimg.length : null\n  var g3 = _vm.isload ? _vm.headimg.join(\",\") : null\n  var l0 =\n    _vm.isload && _vm.bglistshow\n      ? _vm.__map(_vm.bglist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g4 = _vm.bgpic.join(\",\")\n          var m0 = g4 == item ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            g4: g4,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"width:100%\"><!-- <text style=\"color:red;padding-right:6rpx;\">*</text> -->请选择名片背景</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in bgpic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"bgpic\"><image style=\"display:block\" src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadbgpic\" data-field=\"bgpic\" data-pernum=\"1\" v-if=\"bgpic.length==0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"bgpic\" :value=\"bgpic.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"width:100%\"><text style=\"color:red;padding-right:6rpx;\">*</text>请上传个人照片（上传正方形照片）</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in headimg\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"headimg\"><image style=\"display:block\" src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"headimg\" data-pernum=\"1\" v-if=\"headimg.length==0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"headimg\" :value=\"headimg.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\"><text style=\"color:red;padding-right:6rpx;\">*</text>请输入姓名</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"realname\" :value=\"info.realname\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"width:100%\"><text style=\"color:red;padding-right:6rpx;\">*</text>请添加头衔(最多添加3个)</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<input type=\"text\" name=\"touxian1\" :value=\"info.touxian1\" placeholder=\"公司名称+职务，如：某科技有限公司|总经理\" placeholder-style=\"color:#888\" style=\"text-align:left\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"margin-top:10rpx\">\r\n\t\t\t\t\t\t<input type=\"text\" name=\"touxian2\" :value=\"info.touxian2\" placeholder=\"公司名称+职务，如：某科技有限公司|总经理\" placeholder-style=\"color:#888\" style=\"text-align:left\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"margin-top:10rpx\">\r\n\t\t\t\t\t\t<input type=\"text\" name=\"touxian3\" :value=\"info.touxian3\" placeholder=\"公司名称+职务，如：某科技有限公司|总经理\" placeholder-style=\"color:#888\" style=\"text-align:left\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"width:100%\">联系信息<text style=\"font-size:24rpx;color:#888\">(前三项将显示在名片封面上)</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-for=\"(item,index) in field_list\" v-if=\"item.isshow==1\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\"><text style=\"color:red;padding-right:6rpx;\">{{item.required==1 ? '*' : ' '}}</text>请输入{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" :name=\"index\" :value=\"index=='address' ? address : info[index]\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t\t<view class=\"f3\" v-if=\"index=='address'\" @tap=\"selectzuobiao\" style=\"color:#58e;font-size:24rpx;margin-left:6rpx\">选择位置</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<input name=\"latitude\" :value=\"latitude\" hidden=\"true\"/>\r\n\t\t\t\t<input name=\"longitude\" :value=\"longitude\" hidden=\"true\"/>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<text>个人简介</text>\r\n\t\t\t\t\t<view class=\"detailop\"><view class=\"btn\" @tap=\"detailAddtxt\">+文本</view><view class=\"btn\" @tap=\"detailAddpic\">+图片</view><view class=\"btn\" @tap=\"detailAddvideo\">+视频</view></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"detaildp\">\r\n\t\t\t\t\t\t\t<view class=\"op\"><view class=\"flex1\"></view><view class=\"btn\" @tap=\"detailMoveup\" :data-index=\"index\">上移</view><view class=\"btn\" @tap=\"detailMovedown\" :data-index=\"index\">下移</view><view class=\"btn\" @tap=\"detailMovedel\" :data-index=\"index\">删除</view></view>\r\n\t\t\t\t\t\t\t<view class=\"detailbox\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t\t\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t\t\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t\t\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t\t\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\"></dp-text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t\t\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t\t\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t\t\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t\t\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t\t\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t\t\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\"></dp-cube> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t\t\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\"></dp-picture> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='pictures'\"> \r\n\t\t\t\t\t\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t\t\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\" :shopinfo=\"setData.shopinfo\"></dp-shop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t\t\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-product> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t\t\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\"></dp-collage> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t\t\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\"></dp-kanjia> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t\t\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\"></dp-seckill> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\"></dp-scoreshop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t\t\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t\t\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t\t\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\"></dp-business> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t\t\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t\t\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t\t\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t\t\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-richtext> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t\t\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-form> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t\t\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-userinfo> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"width:100%\">自定义分享标题(不填写则按框中内容显示)</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<input type=\"text\" name=\"sharetitle\" :value=\"info.sharetitle\" placeholder=\"您好，这是我的名片，望惠存！\" placeholder-style=\"color:#888\" style=\"text-align:left\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"savebtn\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\r\n\t\t\r\n\t\t<view class=\"popup__container\" v-if=\"bglistshow\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"changeGlistDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择名片背景图</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"changeBglistDialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in bglist\" :key=\"item\">\r\n\t\t\t\t\t\t<view class=\"clist-item flex-y-center\" @tap=\"bgChange\" :data-pic=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\"><img :src=\"item\" style=\"width:300rpx\"/></view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"bgpic.join(',')==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<uni-popup id=\"dialogDetailtxt\" ref=\"dialogDetailtxt\" type=\"dialog\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请输入文本内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<textarea value=\"\" placeholder=\"请输入文本内容\" @input=\"catcheDetailtxt\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"dialogDetailtxtConfirm\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-popup-dialog__close\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t<span class=\"uni-popup-dialog__close-icon \"></span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tfield_list:[],\r\n\t\t\tpagecontent:[],\r\n\t\t\tbgpic:[],\r\n\t\t\theadimg:[],\r\n\t\t\tbglist:[],\r\n\t\t\tbglistshow:false,\r\n\t\t\taddress:'',\r\n      latitude:'',\r\n      longitude:'',\r\n\t\t\ttest:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiMingpian/edit',{}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info || {};\r\n\t\t\t\tthat.address = res.info.address || '';\r\n\t\t\t\tthat.latitude = res.info.latitude || '';\r\n\t\t\t\tthat.longitude = res.info.longitude || '';\r\n\t\t\t\tif(that.info['bgpic']){\r\n\t\t\t\t\tthat.bgpic = (that.info['bgpic']).split(',');\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.bgpic = [];\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info['headimg']){\r\n\t\t\t\t\tthat.headimg = (that.info['headimg']).split(',');\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.headimg = [];\r\n\t\t\t\t}\r\n\t\t\t\tthat.field_list = res.field_list;\r\n\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\tthat.bglist = res.bglist;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n\t\t\tconsole.log(formdata)\r\n\t\t\tif(formdata.headimg == ''){\r\n\t\t\t\tapp.alert('请上传个人照片');return;\r\n\t\t\t}\r\n\t\t\tif(formdata.realname == ''){\r\n\t\t\t\tapp.alert('请输入姓名');return;\r\n\t\t\t}\r\n\t\t\tif(formdata.touxian1 == '' && formdata.touxian2 == '' && formdata.touxian3 == ''){\r\n\t\t\t\tapp.alert('请填写至少一个头衔');return;\r\n\t\t\t}\r\n\t\t\tfor(var i in that.field_list){\r\n\t\t\t\tvar thisfield = that.field_list[i];\r\n\t\t\t\tconsole.log(i)\r\n\t\t\t\tconsole.log(thisfield)\r\n\t\t\t\tif(thisfield.required == 1 && formdata[i] == ''){\r\n\t\t\t\t\tapp.alert('请输入'+thisfield.name);return;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tapp.showLoading('保存中');\r\n      app.post('ApiMingpian/save', {info:formdata,pagecontent:that.pagecontent}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('index', 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tuploadbgpic:function(){\r\n      var that = this;\r\n\t\t\tvar bglist = this.bglist;\r\n\t\t\tif(bglist.length > 0){\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['选择系统背景','自己上传背景（700×480像素）'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log(res.tapIndex)\r\n\t\t\t\t\t\tif (res.tapIndex == 0) {\r\n\t\t\t\t\t\t\tthat.bglistshow = true;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.uploadimg({currentTarget:{dataset:{field:\"bgpic\",pernum:\"1\"}}});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tthat.uploadimg({currentTarget:{dataset:{field:\"bgpic\",pernum:\"1\"}}});\r\n\t\t\t}\r\n\t\t},\r\n\t\tbgChange:function(e){\r\n\t\t\tvar pic = e.currentTarget.dataset.pic;\r\n\t\t\tthis.bgpic = [pic];\r\n\t\t\tthis.bglistshow = false;\r\n\t\t},\r\n\t\tdetailAddtxt:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t},\r\n\t\tdialogDetailtxtClose:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tcatcheDetailtxt:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t},\r\n\t\tdialogDetailtxtConfirm:function(e){\r\n\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\tconsole.log(detailtxt)\r\n\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"text\",\"params\":{\"content\":detailtxt,\"showcontent\":detailtxt,\"bgcolor\":\"#ffffff\",\"fontsize\":\"14\",\"lineheight\":\"20\",\"letter_spacing\":\"0\",\"bgpic\":\"\",\"align\":\"left\",\"color\":\"#000\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"5\",\"padding_y\":\"5\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tdetailAddpic:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pics = [];\r\n\t\t\t\tfor(var i in urls){\r\n\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tpics.push({\"id\":picid,\"imgurl\":urls[i],\"hrefurl\":\"\",\"option\":\"0\"})\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"picture\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":pics,\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t},9);\r\n\t\t},\r\n\t\t\r\n\t\tdetailAddvideo:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseVideo({\r\n        sourceType: ['album', 'camera'],\r\n        success: function (res) {\r\n          var tempFilePath = res.tempFilePath;\r\n          app.showLoading('上传中');\r\n          uni.uploadFile({\r\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n            filePath: tempFilePath,\r\n            name: 'file',\r\n            success: function (res) {\r\n              app.showLoading(false);\r\n              var data = JSON.parse(res.data);\r\n              if (data.status == 1) {\r\n                that.video = data.url;\r\n\t\t\t\t\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\t\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"video\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"src\":data.url,\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n              } else {\r\n                app.alert(data.msg);\r\n              }\r\n            },\r\n            fail: function (res) {\r\n              app.showLoading(false);\r\n              app.alert(res.errMsg);\r\n            }\r\n          });\r\n        },\r\n        fail: function (res) {\r\n          console.log(res); //alert(res.errMsg);\r\n        }\r\n      });\r\n\t\t},\r\n\t\tdetailMoveup:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index > 0)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index-1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedown:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index < pagecontent.length-1)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index+1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedel:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.splice(index,1);\r\n\t\t},\r\n\t\tchangeBglistDialog:function(){\r\n\t\t\tthis.bglistshow = !this.bglistshow\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\tif(!pernum) pernum = 1;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},pernum);\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'bgpic'){\r\n\t\t\t\tvar bgpics = that.bgpic\r\n\t\t\t\tbgpics.splice(index,1);\r\n\t\t\t\tthat.bgpic = bgpics;\r\n\t\t\t}else if(field == 'headimg'){\r\n\t\t\t\tvar headimg = that.headimg\r\n\t\t\t\theadimg.splice(index,1);\r\n\t\t\t\tthat.headimg = headimg;\r\n\t\t\t}\r\n\t\t},\r\n    selectzuobiao: function () {\r\n\t\t\tconsole.log('selectzuobiao')\r\n      var that = this;\r\n      uni.chooseLocation({\r\n        success: function (res) {\r\n          console.log(res);\r\n          that.address = res.address + res.name;\r\n          that.latitude = res.latitude;\r\n          that.longitude = res.longitude;\r\n        },\r\n        fail: function (res) {\r\n\t\t\t\t\tconsole.log(res)\r\n          if (res.errMsg == 'chooseLocation:fail auth deny') {\r\n            //$.error('获取位置失败，请在设置中开启位置信息');\r\n            app.confirm('获取位置失败，请在设置中开启位置信息', function () {\r\n              uni.openSetting({});\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:0px solid #eee }\r\n.form-item .f1{color:#222;width:250rpx;flex-shrink:0;}\r\n.form-item .f2{display:flex;align-items:center;flex:1}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: 1px solid #f1f1f1;color:#111;font-size:28rpx; /*text-align: right;*/height:70rpx;padding:0 10rpx;border-radius:6rpx}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none;background:#4A84FF}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:5;color:#999;font-size:32rpx;background:#fff;border-radius:50%}\r\n.layui-imgbox-close image{width:100%;height:100%}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n.detailop{display:flex;line-height:60rpx}\r\n.detailop .btn{border:1px solid #ccc;margin-right:10rpx;padding:0 16rpx;color:#222;border-radius:10rpx}\r\n.detaildp{position:relative;line-height:50rpx}\r\n.detaildp .op{width:100%;display:flex;justify-content:flex-end;font-size:24rpx;height:60rpx;line-height:60rpx;margin-top:10rpx}\r\n.detaildp .op .btn{background:rgba(0,0,0,0.4);margin-right:10rpx;padding:0 10rpx;color:#fff}\r\n.detaildp .detailbox{border:2px dashed #00a0e9}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\r\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093852\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}