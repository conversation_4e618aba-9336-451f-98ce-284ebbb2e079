{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?c518", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?47c3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?42bf", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?c746", "uni-app:///pagesExa/my/leveluppay.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?6513", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/leveluppay.vue?67cc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "wxpayst", "alipay", "moneypay", "userinfo", "paypwd", "hiddenmodalput", "orderinfo", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "orderid", "getpwd", "cancel", "modalinput", "topay", "typeid", "setTimeout", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmDnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;QACA;UACAD;UACAA;UACAA;UACAA;UACAA;QACA;QACAA;MACA;IACA;IACAG;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAN;QACAC;UACAA;YAAAC;YAAAK;YAAAd;UAAA;YACA;cACAQ;cACA;YACA;YACA;cACA;cACAA;cACAO;gBACAP;cACA;cACA;YACA;UACA;QACA;MACA;QACAA;UAAAC;UAAAK;QAAA;UACA;YACAN;YACA;UACA;UACA;YACA;YACAA;YACAO;cACAP;YACA;YACA;UACA;UACA;UACAQ;YACA;YACA;YACA;YACA;YACA;YACA;cACAR;cACAO;gBACAP;cACA;YACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/leveluppay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/leveluppay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./leveluppay.vue?vue&type=template&id=23ec9955&\"\nvar renderjs\nimport script from \"./leveluppay.vue?vue&type=script&lang=js&\"\nexport * from \"./leveluppay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./leveluppay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/leveluppay.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./leveluppay.vue?vue&type=template&id=23ec9955&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.moneypay == 1 && _vm.userinfo.haspwd\n      ? _vm.t(\"余额\")\n      : null\n  var m1 =\n    _vm.isload && _vm.moneypay == 1 && !_vm.userinfo.haspwd\n      ? _vm.t(\"余额\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./leveluppay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./leveluppay.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"content\">\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"t1\">订单名称:</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.title}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"t1\">订单编号:</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"t1\">支付金额:</text>\n\t\t\t\t<text class=\"t2\" style=\"color:#e94745\">￥{{orderinfo.totalprice}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<button class=\"fpay-btn\" @tap=\"topay\" data-typeid=\"1\" v-if=\"wxpayst==1\">微信支付</button>\n\t\t<button class='fpay-btn' @tap=\"topay\" data-typeid=\"10\" v-if=\"alipay==1\" style=\"background:#108EE9;margin-top:20rpx\">支付宝支付</button>\n\t\t<block v-if=\"moneypay==1\">\n\t\t\t<button class=\"fpay-btn2\" @tap=\"modalinput\" v-if=\"userinfo.haspwd\">{{t('余额')}}支付（当前余额¥{{userinfo.money}}）</button>\n\t\t\t<button class=\"fpay-btn2\" @tap=\"topay\" data-typeid=\"2\" v-else>{{t('余额')}}支付（当前余额¥{{userinfo.money}}）</button>\n\t\t</block>\n\n\t\t<view v-if=\"userinfo.haspwd\" :class=\"'weui-demo-dialog ' + (!hiddenmodalput ? 'weui-demo-dialog_show' : '')\">\n\t\t\t<view class=\"weui-mask\" @tap=\"cancel\"></view>\n\t\t\t<view class=\"weui-dialog__wrp\">\n\t\t\t\t<view class=\"weui-dialog\">\n\t\t\t\t\t<view class=\"weui-dialog__hd\">\n\t\t\t\t\t\t<view class=\"weui-dialog__title\">支付密码</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"weui-dialog__bd\">\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 130rpx;\">\n\t\t\t\t\t\t\t<text style=\"font-size:40rpx;color:#000\"></text><input type=\"digit\" placeholder=\"请输入支付密码\" @input=\"getpwd\"></input>\n\t\t\t\t\t\t</view> \n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"weui-dialog__ft\">\n\t\t\t\t\t\t<view class=\"weui-dialog__btn weui-dialog__btn_default\" @tap=\"cancel\">取消</view>\n\t\t\t\t\t\t<view class=\"weui-dialog__btn\" @tap=\"topay\" data-typeid=\"2\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\twxpayst:0,\n\t\t\talipay:0,\n\t\t\tmoneypay:0,\n\t\t\tuserinfo:{},\n      paypwd: '',\n      hiddenmodalput: true,\n      orderinfo: []\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiWxpay/uppay', {orderid: that.opt.orderid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t} else {\n\t\t\t\t\tthat.wxpayst = res.wxpayst;\n\t\t\t\t\tthat.alipay = res.alipay;\n\t\t\t\t\tthat.moneypay = res.moneypay;\n\t\t\t\t\tthat.orderinfo = res.orderinfo;\n\t\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getpwd: function (e) {\n      var paypwd = e.detail.value;\n      this.paypwd = paypwd\n    },\n    cancel: function () {\n      this.hiddenmodalput = true\n    },\n    modalinput: function () {\n      this.hiddenmodalput = !this.hiddenmodalput\n    },\n    topay: function (e) {\n      var that = this;\n      var typeid = e.currentTarget.dataset.typeid;\n      var orderid = this.orderinfo.id;\n      if (typeid == 2) {\n        that.hiddenmodalput = true;\n        app.confirm('确定用' + that.t('余额') + '支付吗?', function () {\n          app.post('ApiWxpay/uppay', {orderid: orderid,typeid: typeid,paypwd: that.paypwd}, function (data) {\n            if (data.status == 0) {\n              app.error(data.msg);\n              return;\n            }\n            if (data.status == 2) {\n              //无需付款\n              app.success(data.msg);\n              setTimeout(function () {\n                app.goto('/pages/my/usercenter');\n              }, 1000);\n              return;\n            }\n          });\n        });\n      } else {\n        app.post('ApiWxpay/uppay', {orderid: orderid,typeid: typeid}, function (data) {\n          if (data.status == 0) {\n            app.error(data.msg);\n            return;\n          }\n          if (data.status == 2) {\n            //无需付款\n            app.success(data.msg);\n            setTimeout(function () {\n              app.goto('/pages/my/usercenter');\n            }, 1000);\n            return;\n          }\n          var opt = data.data;\n\t\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\t'timeStamp': opt.timeStamp,\n\t\t\t\t\t\t'nonceStr': opt.nonceStr,\n\t\t\t\t\t\t'package': opt.package,\n\t\t\t\t\t\t'signType': 'MD5',\n\t\t\t\t\t\t'paySign': opt.paySign,\n\t\t\t\t\t\t'success': function (res) {\n\t\t\t\t\t\t\tapp.success('付款完成');\n\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\tapp.goto('/pages/my/usercenter');\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'fail': function (res) {}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t}\n    }\n  }\n}\r\n</script>\r\n<style>\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx 20rpx;}\r\n.info-item2{ display:flex;width: 100%; background: #fff;padding: 0 3%;padding:20rpx 20rpx; margin-bottom:20rpx;}\r\n.info-item2 .t1{ width:70rpx; }\r\n.info-item2 .t2{ color: #000;}\r\n.info-item2 .x2{ color: #888;}\r\n\r\n.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding: 0 3%;  border-bottom: 1px #f3f3f3 solid;}\r\n.info-item:last-child{border:none}\r\n.info-item .t1{ width: 200rpx; height: 80rpx; line-height: 80rpx; color: #000; }\r\n.info-item .t2{ height: 80rpx;line-height: 80rpx; color: #000;text-align:right;flex:1}\r\n\r\r\n.fpay-btn{ width: 90%; margin: 0 5%; height: 40px; line-height: 40px; margin-top: 20px; float: left; border-radius: 5px; color: #fff; background: #1aac19; border: none; font-size: 15px; }\r\n.fpay-btn2{ width: 90%; margin: 0 5%; height: 40px; line-height: 40px; margin-top: 10px; float: left; border-radius: 5px; color: #fff; background: #e2cc05; border: none; font-size: 15px; }\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./leveluppay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./leveluppay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115060970\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}