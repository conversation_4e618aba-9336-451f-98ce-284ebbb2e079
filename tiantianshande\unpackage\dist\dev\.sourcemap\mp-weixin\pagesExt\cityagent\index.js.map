{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?224b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?d658", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?d398", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?952a", "uni-app:///pagesExt/cityagent/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?e4eb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/index.vue?8b11"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "agentinfo", "statistics", "onLoad", "onPullDownRefresh", "methods", "getColor", "console", "getColorRgb", "getdata", "that", "app", "uni", "title", "goto", "<PERSON><PERSON><PERSON><PERSON>", "goWithdraw", "goConvert", "content", "success", "convertBalance", "setTimeout", "showCancel", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2L9wB;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAD;QACA;MACA;IACA;IAEA;IACAE;MACA;MACAC;;MAEA;MACAC;QACAD;QACAE;QAEA;UACAD;UACA;UACA;YACAA;UACA;UACA;QACA;;QAEA;QACAC;UACAC;QACA;;QAEA;QACAH;QACAA;QACAA;MACA;IACA;IAEA;IACAI;MACA;MACA;QACAH;MACA;IACA;IAEA;IACAI;MACAJ;IACA;IAEA;IACAK;MACAL;IACA;IAEA;IACAM;MACA;;MAEA;MACA;QACAN;QACA;MACA;;MAEA;MACAC;QACAC;QACAK;QACAC;UACA;YACAT;UACA;QACA;MACA;IACA;IAEA;IACAU;MACA;MAEAR;QACAC;MACA;;MAEA;MACAQ;QACAT;QACAA;UACAC;UACAK;UACAI;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5UA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8a3cfe8e&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=8a3cfe8e&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.getColor(\"color1\") || \"#4CAF50\" : null\n  var m1 = _vm.isload ? _vm.getColorRgb(\"color1rgb\") || \"76,175,80\" : null\n  var g0 = _vm.isload\n    ? _vm.agentinfo &&\n      _vm.agentinfo.coverage_areas &&\n      _vm.agentinfo.coverage_areas.length > 0\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部横幅 -->\n\t\t<view class=\"banner\" :style=\"{background:'linear-gradient(180deg,'+(getColor('color1') || '#4CAF50')+' 0%,rgba('+(getColorRgb('color1rgb') || '76,175,80')+',0) 100%)'}\">\n\t\t\t<image :src=\"(agentinfo && agentinfo.headimg) || '/static/img/default-avatar.png'\" background-size=\"cover\"/>\n\t\t\t<view class=\"info\">\n\t\t\t\t<text class=\"nickname\">{{(agentinfo && agentinfo.name) || '加载中...'}}（代理ID：{{(agentinfo && agentinfo.id) || '--'}}）</text>\n\t\t\t\t<text>联系电话：{{(agentinfo && agentinfo.tel) || '--'}}</text>\n\t\t\t\t<text>代理级别：{{(agentinfo && agentinfo.agent_level_name) || '--'}}</text>\n\t\t\t\t<text>覆盖区域：{{(agentinfo && agentinfo.coverage_area_names) || '暂无'}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"contentdata\">\n\t\t\t<!-- 我的收益 -->\n\t\t\t<view class=\"order\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<text class=\"f1\">我的收益</text>\n\t\t\t\t\t<view class=\"f2\" @tap=\"goWithdraw\">\n\t\t\t\t\t\t<text>申请提现</text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(agentinfo && agentinfo.money) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">账户余额</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(agentinfo && agentinfo.total_income) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">累计收入</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">{{(agentinfo && agentinfo.commission_rate) || '0'}}%</text>\n\t\t\t\t\t\t<text class=\"t3\">佣金比例</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<!-- <view class=\"action-buttons\">\n\t\t\t\t\t<view class=\"btn-group\">\n\t\t\t\t\t\t<view class=\"action-btn convert-btn\" @tap=\"goConvert\" :style=\"{background:'linear-gradient(90deg,'+getColor('color1')+' 0%,rgba('+getColorRgb('color1rgb')+',0.8) 100%)', color:'#fff'}\">\n\t\t\t\t\t\t\t<image src=\"/static/img/icon-convert.png\"></image>\n\t\t\t\t\t\t\t<text>转佣金</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\n\t\t\t<!-- 业务数据 -->\n\t\t\t<view class=\"order\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<text class=\"f1\">业务数据</text>\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"statistics\">\n\t\t\t\t\t\t<text>查看详情</text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">{{(statistics && statistics.today_orders) || '0'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">今日订单数</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(statistics && statistics.today_amount) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">今日业绩</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(statistics && statistics.today_commission) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">今日佣金</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">{{(statistics && statistics.month_orders) || '0'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">本月订单数</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(statistics && statistics.month_amount) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">本月业绩</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<text class=\"t1\">￥{{(statistics && statistics.month_commission) || '0.00'}}</text>\n\t\t\t\t\t\t<text class=\"t3\">本月佣金</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 覆盖区域 -->\n\t\t\t<view class=\"order\" v-if=\"agentinfo && agentinfo.coverage_areas && agentinfo.coverage_areas.length > 0\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<text class=\"f1\">覆盖区域</text>\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"coverage\">\n\t\t\t\t\t\t<text>管理区域</text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"coverage-areas\">\n\t\t\t\t\t<view class=\"area-item\" v-for=\"(area, index) in agentinfo.coverage_areas\" :key=\"index\">\n\t\t\t\t\t\t<text class=\"area-name\">{{area.name}}</text>\n\t\t\t\t\t\t<text class=\"area-type\">{{area.type}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 功能菜单 -->\n\t\t\t<view class=\"list\">\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"income\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-income.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">收益明细</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"withdrawlog?st=1\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-withdraw.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">提现记录</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"item\" @tap=\"goConvert\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-convert.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">转佣金</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"orders\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-order.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">订单统计</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"merchant\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-merchant.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">商户管理</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"settings\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-setting.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">代理设置</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"help\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-help.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">帮助中心</view>\n\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view style=\"width:100%;height:20rpx\"></view>\n\t</block>\n\t\n\t<!-- 加载中组件 -->\n\t<loading v-if=\"loading\"></loading>\n\t\n\t<!-- 底部导航 -->\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t\n\t<!-- 消息提示 -->\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {}, // 页面参数\n\t\t\tloading: false, // 加载状态\n\t\t\tisload: false, // 数据是否加载完成\n\t\t\tpre_url: app.globalData.pre_url, // 图片前缀\n\t\t\tagentinfo: {}, // 代理信息\n\t\t\tstatistics: {}, // 统计数据\n\t\t};\n\t},\n\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n\t},\n\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\n\tmethods: {\n\t\t// 安全获取颜色值\n\t\tgetColor: function(colorKey) {\n\t\t\ttry {\n\t\t\t\tif (typeof this.t === 'function') {\n\t\t\t\t\treturn this.t(colorKey);\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.log('获取颜色失败:', e);\n\t\t\t\treturn null;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 安全获取RGB颜色值\n\t\tgetColorRgb: function(colorKey) {\n\t\t\ttry {\n\t\t\t\tif (typeof this.t === 'function') {\n\t\t\t\t\treturn this.t(colorKey);\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.log('获取RGB颜色失败:', e);\n\t\t\t\treturn null;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取代理数据\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\t// 调用代理信息接口\n\t\t\tapp.get('ApiCityAgent/getAgentInfo', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t// 如果未绑定代理，跳转到绑定页面\n\t\t\t\t\tif (res.code == 'NOT_AGENT') {\n\t\t\t\t\t\tapp.goto('/pagesExt/cityagent/bind');\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置导航标题\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '代理中心'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 更新数据\n\t\t\t\tthat.agentinfo = res.agentinfo;\n\t\t\t\tthat.statistics = res.statistics;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 页面跳转\n\t\tgoto: function (e) {\n\t\t\tvar url = e.currentTarget.dataset.url;\n\t\t\tif (url) {\n\t\t\t\tapp.goto('/pagesExt/cityagent/' + url);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 提现\n\t\ttowithdraw: function () {\n\t\t\tapp.goto('/pagesExt/cityagent/withdraw');\n\t\t},\n\t\t\n\t\t// 申请提现\n\t\tgoWithdraw: function() {\n\t\t\tapp.goto('/pagesExt/cityagent/withdraw');\n\t\t},\n\t\t\n\t\t// 余额转佣金\n\t\tgoConvert: function() {\n\t\t\tvar that = this;\n\t\t\t\n\t\t\t// 检查余额是否足够\n\t\t\tif (!that.agentinfo || !that.agentinfo.money || parseFloat(that.agentinfo.money) <= 0) {\n\t\t\t\tapp.error('账户余额不足');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 弹出确认对话框\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '余额转换佣金',\n\t\t\t\tcontent: '是否将账户余额转换为佣金？转换后可提现到微信/支付宝',\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthat.convertBalance();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 执行余额转换\n\t\tconvertBalance: function() {\n\t\t\tvar that = this;\n\t\t\t\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '转换中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 暂时显示功能开发中\n\t\t\tsetTimeout(function() {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '功能提示', \n\t\t\t\t\tcontent: '余额转换功能正在开发中，敬请期待',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t}, 1000);\n\t\t},\n\t\t\n\t\t// 数据加载完成\n\t\tloaded: function () {\n\t\t\tthis.isload = true;\n\t\t}\n\t}\n};\n</script>\n\n<style>\n/* 顶部横幅样式 */\n.banner {\n\tdisplay: flex;\n\twidth: 100%;\n\theight: 560rpx;\n\tpadding: 40rpx 32rpx;\n\tcolor: #fff;\n\tposition: relative;\n}\n\n.banner image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tmargin-right: 20rpx;\n}\n\n.banner .info {\n\tdisplay: flex;\n\tflex: auto;\n\tflex-direction: column;\n\tpadding-top: 10rpx;\n}\n\n.banner .info .nickname {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tpadding-bottom: 12rpx;\n}\n\n.banner .info text {\n\tfont-size: 28rpx;\n\tmargin-bottom: 8rpx;\n}\n\n/* 内容区域样式 */\n.contentdata {\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n\tpadding: 0 30rpx;\n\tmargin-top: -380rpx;\n\tposition: relative;\n\tmargin-bottom: 20rpx;\n}\n\n/* 订单卡片样式 */\n.order {\n\twidth: 100%;\n\tbackground: #fff;\n\tpadding: 0 20rpx;\n\tmargin-top: 20rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.order .head {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1px solid #eee;\n}\n\n.order .head .f1 {\n\tflex: auto;\n\tcolor: #333;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.order .head .f2 {\n\tdisplay: flex;\n\talign-items: center;\n\tcolor: #FE2B2E;\n\tfont-size: 28rpx;\n\tpadding: 10rpx 0;\n}\n\n.order .head .f2 image {\n\twidth: 30rpx;\n\theight: 30rpx;\n\tmargin-left: 10rpx;\n}\n\n.order .content {\n\tdisplay: flex;\n\twidth: 100%;\n\tpadding: 20rpx 0;\n\talign-items: center;\n\tfont-size: 24rpx;\n}\n\n.order .content .item {\n\tpadding: 10rpx 0;\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tposition: relative;\n}\n\n.order .content .item .t1 {\n\tcolor: #FE2B2E;\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n}\n\n.order .content .item .t3 {\n\tpadding-top: 10rpx;\n\tcolor: #666;\n\tfont-size: 24rpx;\n}\n\n/* 操作按钮样式 */\n.action-buttons {\n\tpadding: 20rpx 0 30rpx 0;\n}\n\n.btn-group {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tgap: 20rpx;\n}\n\n.action-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tposition: relative;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.action-btn image {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tmargin-right: 10rpx;\n}\n\n.action-btn text {\n\tcolor: inherit;\n}\n\n.withdraw-btn {\n\tcolor: #fff;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.withdraw-btn:active {\n\ttransform: translateY(2rpx);\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.convert-btn {\n\tbackground: #fff;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.convert-btn:active {\n\ttransform: translateY(2rpx);\n\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 覆盖区域样式 */\n.coverage-areas {\n\tpadding: 20rpx 0;\n}\n\n.area-item {\n\tdisplay: inline-block;\n\tmargin: 5rpx 10rpx 5rpx 0;\n\tpadding: 10rpx 20rpx;\n\tbackground: #f8f8f8;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n.area-name {\n\tcolor: #333;\n\tmargin-right: 10rpx;\n}\n\n.area-type {\n\tcolor: #999;\n\tfont-size: 20rpx;\n}\n\n/* 功能列表样式 */\n.list {\n\twidth: 100%;\n\tbackground: #fff;\n\tmargin-top: 20rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 30rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.list .item {\n\theight: 100rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tborder-bottom: 1px solid #eee;\n}\n\n.list .item:last-child {\n\tborder-bottom: 0;\n}\n\n.list .f1 {\n\twidth: 50rpx;\n\theight: 50rpx;\n\tmargin-right: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.list .f1 image {\n\twidth: 40rpx;\n\theight: 40rpx;\n}\n\n.list .f2 {\n\tcolor: #222;\n\tflex: 1;\n}\n\n.list .f3 {\n\tcolor: #FC5648;\n\ttext-align: right;\n\tmargin-right: 20rpx;\n}\n\n.list .f4 {\n\twidth: 24rpx;\n\theight: 24rpx;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103040\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}