{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?0a08", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?071e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?cc87", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?a1dc", "uni-app:///pagesExa/my/setavatar.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?4d49", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setavatar.vue?0571"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "headimg", "nickname", "default_headimg", "platform", "showManualForm", "wxUserInfoLoading", "onLoad", "onPullDownRefresh", "methods", "getdata", "app", "that", "console", "getWxUserInfo", "location", "submitAvatarNickname", "setTimeout", "formSubmit", "uploadHeadimg", "uni", "count", "sizeType", "sourceType", "success", "url", "filePath", "name", "fail", "onChooseAvatar"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6FlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;QACAC;QACAA;QACAA;QACAA;;QAEA;QACA;UACAC;UACAD;QACA;MACA;IACA;IAEA;IACAE;MACA;MAEA;MACA;MACAH;;MAEA;MACAA;QACAA;QACAC;QAEA;UACA;UACAA;UACAA;;UAEA;UACAA;QACA;UACA;UACAC;UACAE;QACA;UACA;UACAJ;UACAC;QACA;MACA;QACAD;QACAC;QACAD;QACAC;MACA;IACA;IAEA;IACAI;MACA;QACAL;QACA;QACA;MACA;MAEA;QACAA;QACA;QACA;MACA;MAEA;MACAA;MACAA;QAAAV;QAAAC;MAAA;QACAS;QACA;UACAA;UACAM;YACAN;UACA;QACA;UACAA;UACAC;QACA;MACA;IACA;IACAM;MACA;MACA;MAEA;QACAP;QACA;MACA;MACA;QACAA;QACA;MACA;MAEAA;MACAA;QAAAV;QAAAC;MAAA;QACAS;QACA;UACAA;UACAM;YACAN;UACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAQ;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;UACA;UACA;UACAb;UACAS;YACAK;YACAC;YACAC;YACAH;cACAb;cACA;cACA;gBACAC;cACA;gBACAD;cACA;YACA;YACAiB;cACAjB;cACAA;YACA;UACA;QACA;QACAiB;UACAjB;QACA;MACA;IACA;IACA;IACAkB;MACA;MACAlB;MACAS;QACAK;QACAC;QACAC;QACAH;UACAb;UACA;UACA;YACAC;UACA;YACAD;UACA;QACA;QACAiB;UACAjB;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9RA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/setavatar.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/setavatar.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setavatar.vue?vue&type=template&id=39a3ba82&\"\nvar renderjs\nimport script from \"./setavatar.vue?vue&type=script&lang=js&\"\nexport * from \"./setavatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setavatar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/setavatar.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setavatar.vue?vue&type=template&id=39a3ba82&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setavatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setavatar.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- 公众号模式 - 微信授权获取 -->\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<view v-if=\"platform === 'mp'\" class=\"auth-container\">\r\n\t\t\t<view class=\"title\">请授权获取头像昵称</view>\r\n\t\t\t<view class=\"auth-info\">\r\n\t\t\t\t<image :src=\"headimg || default_headimg\" class=\"auth-avatar\"></image>\r\n\t\t\t\t<view class=\"auth-nickname\">{{nickname || '未设置昵称'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"auth-btn\" @tap=\"getWxUserInfo\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t微信授权获取\r\n\t\t\t</button>\r\n\t\t\t<view class=\"auth-tip\">授权后将自动获取您的微信头像和昵称</view>\r\n\t\t\t<view class=\"manual-setting\" @tap=\"showManualForm = true\">\r\n\t\t\t\t<text>手动设置</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 手动设置表单 -->\r\n\t\t<form v-if=\"platform !== 'mp' || showManualForm\" @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t<view class=\"avatar-setting\">\r\n\t\t\t<view class=\"title\">请设置头像昵称</view>\r\n\t\t\t<view class=\"form\">\r\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\r\n\t\t\t\t<view class=\"form-item avatar-item\">\r\n\t\t\t\t\t<view class=\"label\">头像</view>\r\n\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-btn\">\r\n\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" class=\"avatar-img\"></image>\r\n\t\t\t\t\t</button> \r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item nickname-item\">\r\n\t\t\t\t\t<view class=\"label\">昵称</view>\r\n\t\t\t\t\t<input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" v-model=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!--  #ifndef MP-WEIXIN -->\r\n\t\t\t\t<view class=\"form-item avatar-item\">\r\n\t\t\t\t\t<view class=\"label\">头像</view>\r\n\t\t\t\t\t<image :src=\"headimg || default_headimg\" class=\"avatar-img\" @tap=\"uploadHeadimg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item nickname-item\">\r\n\t\t\t\t\t<view class=\"label\">昵称</view>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" v-model=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"set-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保 存</button>\r\n\t\t</form>\r\n\t\t<!-- #endif -->\r\n\t\t\r\n\t\t<!-- 非公众号模式 - 正常表单 -->\r\n\t\t<!-- #ifndef H5 -->\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t<view class=\"avatar-setting\">\r\n\t\t\t<view class=\"title\">请设置头像昵称</view>\r\n\t\t\t<view class=\"form\">\r\n\t\t\t\t<!--  #ifdef MP-WEIXIN -->\r\n\t\t\t\t<view class=\"form-item avatar-item\">\r\n\t\t\t\t\t<view class=\"label\">头像</view>\r\n\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-btn\">\r\n\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" class=\"avatar-img\"></image>\r\n\t\t\t\t\t</button> \r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item nickname-item\">\r\n\t\t\t\t\t<view class=\"label\">昵称</view>\r\n\t\t\t\t\t<input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" v-model=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!--  #ifndef MP-WEIXIN -->\r\n\t\t\t\t<view class=\"form-item avatar-item\">\r\n\t\t\t\t\t<view class=\"label\">头像</view>\r\n\t\t\t\t\t<image :src=\"headimg || default_headimg\" class=\"avatar-img\" @tap=\"uploadHeadimg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item nickname-item\">\r\n\t\t\t\t\t<view class=\"label\">昵称</view>\r\n\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" v-model=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"set-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保 存</button>\r\n\t\t</form>\r\n\t\t<!-- #endif -->\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\theadimg: '',\r\n\t\t\tnickname: '',\r\n\t\t\tdefault_headimg: app.globalData.pre_url + '/static/img/touxiang.png',\r\n\t\t\tplatform: app.globalData.platform || '',\r\n\t\t\tshowManualForm: false,\r\n\t\t\twxUserInfoLoading: false\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthis.loading = true\r\n\t\t\tapp.get('ApiMy/setavatar', {}, function(res){\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.headimg = res.headimg || '';\r\n\t\t\t\tthat.nickname = res.nickname || '';\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\t\r\n\t\t\t\t// 如果是公众号模式且没有头像或昵称，自动尝试获取\r\n\t\t\t\tif(that.platform === 'mp' && (!that.headimg || that.headimg === that.default_headimg || !that.nickname)) {\r\n\t\t\t\t\tconsole.log('公众号模式自动尝试获取头像昵称');\r\n\t\t\t\t\tthat.getWxUserInfo();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 微信公众号授权获取用户信息\r\n\t\tgetWxUserInfo: function(){\r\n\t\t\tif(this.wxUserInfoLoading) return;\r\n\t\t\t\r\n\t\t\tvar that = this;\r\n\t\t\tthis.wxUserInfoLoading = true;\r\n\t\t\tapp.showLoading('授权中');\r\n\t\t\t\r\n\t\t\t// 调用公众号获取用户信息接口\r\n\t\t\tapp.post('ApiIndex/getWxUserInfo', {}, function(res){\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tthat.wxUserInfoLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t// 授权成功，获取头像昵称\r\n\t\t\t\t\tthat.headimg = res.headimg || that.headimg;\r\n\t\t\t\t\tthat.nickname = res.nickname || that.nickname;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 自动保存\r\n\t\t\t\t\tthat.submitAvatarNickname();\r\n\t\t\t\t} else if(res.url) {\r\n\t\t\t\t\t// 需要跳转授权\r\n\t\t\t\t\tconsole.log('需要跳转授权: ' + res.url);\r\n\t\t\t\t\tlocation.href = res.url;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 授权失败，显示手动设置表单\r\n\t\t\t\t\tapp.alert(res.msg || '授权失败，请手动设置');\r\n\t\t\t\t\tthat.showManualForm = true;\r\n\t\t\t\t}\r\n\t\t\t}, function(err){\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tthat.wxUserInfoLoading = false;\r\n\t\t\t\tapp.alert('网络请求失败，请手动设置');\r\n\t\t\t\tthat.showManualForm = true;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 自动提交头像昵称\r\n\t\tsubmitAvatarNickname: function(){\r\n\t\t\tif(!this.headimg || this.headimg == this.default_headimg){\r\n\t\t\t\tapp.alert('获取头像失败，请手动设置');\r\n\t\t\t\tthis.showManualForm = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(!this.nickname){\r\n\t\t\t\tapp.alert('获取昵称失败，请手动设置');\r\n\t\t\t\tthis.showManualForm = true;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('保存中');\r\n\t\t\tapp.post(\"ApiMy/setavatarsub\", {headimg: this.headimg, nickname: this.nickname}, function(data){\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(data.status == 1){\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tapp.goback(true);\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\tthat.showManualForm = true;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    formSubmit: function (e) {\r\n      var formdata = e.detail.value;\r\n\t\t\tvar nickname = formdata.nickname || this.nickname;\r\n\t\t\t\r\n\t\t\tif (!this.headimg || this.headimg == this.default_headimg) {\r\n        app.alert('请设置头像'); \r\n        return;\r\n      }\r\n\t\t\tif (nickname == '') {\r\n        app.alert('请输入昵称'); \r\n        return;\r\n      }\r\n\t\t\t\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post(\"ApiMy/setavatarsub\", {headimg: this.headimg, nickname: nickname}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            app.goback(true);\r\n          }, 1000);\r\n        } else {\r\n          app.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\t\t// 非微信小程序头像上传\r\n\t\tuploadHeadimg:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tsizeType: ['original', 'compressed'],\r\n\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\r\n\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\r\n\t\t\t\t\tapp.showLoading('上传中');\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\r\n\t\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) {\r\n\t\t\t\t\tapp.alert('选择图片失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 微信小程序头像选择\r\n\t\tonChooseAvatar:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('上传中');\r\n\t\t\tuni.uploadFile({\r\n\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\r\n\t\t\t\tfilePath: e.detail.avatarUrl,\r\n\t\t\t\tname: 'file',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f6f6f6;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.avatar-setting {\r\n\twidth: 94%;\r\n\tmargin: 20rpx 3%;\r\n\tborder-radius: 10rpx;\r\n\tbackground: #FFF;\r\n\tpadding: 40rpx 30rpx;\r\n}\r\n\r\n.title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\ttext-align: center;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.form {\r\n\twidth: 100%;\r\n}\r\n\r\n.form-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\twidth: 100%;\r\n\tborder-bottom: 1px #ededed solid;\r\n\tmin-height: 120rpx;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.form-item:last-child {\r\n\tborder: 0;\r\n}\r\n\r\n.form-item .label {\r\n\tcolor: #333;\r\n\twidth: 120rpx;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n.form-item .input {\r\n\tflex: 1;\r\n\tcolor: #333;\r\n\tfont-size: 30rpx;\r\n\ttext-align: right;\r\n}\r\n\r\n.avatar-btn {\r\n\tbackground: none;\r\n\tborder: none;\r\n\tpadding: 0;\r\n\tmargin: 0;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n}\r\n\r\n.avatar-img {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid #e0e0e0;\r\n}\r\n\r\n.set-btn {\r\n\twidth: 90%;\r\n\tmargin: 60rpx 5%;\r\n\theight: 96rpx;\r\n\tline-height: 96rpx;\r\n\tborder-radius: 48rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-weight: bold;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.avatar-item {\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.nickname-item {\r\n\tjustify-content: space-between;\r\n}\r\n\r\n/* 公众号授权样式 */\r\n.auth-container {\r\n\twidth: 94%;\r\n\tmargin: 20rpx 3%;\r\n\tborder-radius: 10rpx;\r\n\tbackground: #FFF;\r\n\tpadding: 40rpx 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.auth-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmargin: 30rpx 0;\r\n}\r\n\r\n.auth-avatar {\r\n\twidth: 150rpx;\r\n\theight: 150rpx;\r\n\tborder-radius: 50%;\r\n\tmargin-bottom: 20rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n}\r\n\r\n.auth-nickname {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.auth-btn {\r\n\twidth: 80%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tborder-radius: 45rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 32rpx;\r\n\tmargin: 30rpx 0;\r\n}\r\n\r\n.auth-tip {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tmargin: 20rpx 0;\r\n}\r\n\r\n.manual-setting {\r\n\tmargin-top: 30rpx;\r\n\tpadding: 10rpx 20rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tborder-bottom: 1rpx solid #999;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setavatar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setavatar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061047\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}