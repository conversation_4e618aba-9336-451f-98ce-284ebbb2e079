{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?aa5a", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?5394", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?13a9", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?60c9", "uni-app:///yuyue/my.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?b824", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/my.vue?86e6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "loading", "popmsg", "data", "opt", "isload", "menuindex", "pre_url", "set", "worker", "checked", "showform", "searchmember", "sets", "enable_promote", "enable_calendar", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "title", "switchchange", "console", "st", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON>", "needResult", "scanType", "success", "uni", "loaded", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+G;AAC/G;AACsD;AACL;AACa;;;AAG9D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAuvB,CAAgB,kwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8J3wB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAH;MACAI;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;YAAAE;UAAA;QACA;UACAD;QACA;MACA;QACAD;MACA;IACA;IACAG;MACAC;MACA;MACA;MACAH;QAAAI;MAAA;QACA;UACAL;UACAC;QACA;UACAA;UACAD;YACAA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;QACAL;QAAA;MACA;QACA;QACAM;UAAA;UACAA;YACAC;YAAA;YACAC;YAAA;YACAC;cACA;cACA;cACAT;cACA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;QACAU;UACAD;YACAN;YACA;YACA;YACAH;UACA;QACA;MACA;IACA;IACAW;MACA;MACAD;MACA;QACAA;UAAAT;QAAA;MACA;IACA;IACAW;MAAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;MAEA;QACAb;UACAA;QACA;QACA;MACA;MAEAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChSA;AAAA;AAAA;AAAA;AAAukC,CAAgB,mjCAAG,EAAC,C;;;;;;;;;;;ACA3lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=256bf95c&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/my.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=template&id=256bf95c&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"isload\">\n\t\t<view class=\"banner\" :style=\"{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\n\t\t\t<image :src=\"worker.headimg\" background-size=\"cover\"/>\n\t\t\t<view class=\"info\">\n\t\t\t\t\t<text class=\"nickname\">{{worker.realname}}</text>\n\t\t\t\t\t<text>{{worker.tel}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"sets\" v-if=\"sets\"  @tap=\"goto\" data-url=\"sets\"><image src=\"/static/img/set.png\" /></view>\n\t\t</view>\n\t\t<view class=\"contentdata\">\n\t\t\t<view class=\"custom_field\">\n\t\t\t\t<view class='item' data-url='jdorderlist?st=3' @tap='goto'>\n\t\t\t\t\t<text class=\"t1\">累计服务</text>\n\t\t\t\t\t<text class='t2' :style=\"{color:t('color1')}\">{{worker.totalnum}}次</text>\n\t\t\t\t</view>\n\t\t\t\t<view class='item'>\n\t\t\t\t\t<text class=\"t1\">总收入</text>\n\t\t\t\t\t<text class='t2' :style=\"{color:t('color1')}\">{{worker.totalmoney}}元</text>\n\t\t\t\t</view>\n\t\t\t\t<view class='item'>\n\t\t\t\t\t<text class=\"t1\">好评率</text>\n\t\t\t\t\t<text class='t2' :style=\"{color:t('color1')}\">{{worker.comment_haopercent}}%</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\n\t\t\t<view class=\"listcontent\">\n\t\t\t\t<view class=\"list\" v-if=\"enable_calendar\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"calendar\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-calendar.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">工作日历</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\" v-if=\"enable_promote\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"worker/promote\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-tuiguang.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">推广产品</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"withdraw\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-qianbao.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">我的钱包</view>\n\t\t\t\t\t\t<text class=\"f3\">余额：{{worker.money}}</text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"moneylog\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-zhangdan.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">账单明细</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"comments\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-comment.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">客户评价</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-jiedan.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">接单状态</view>\n\t\t\t\t\t\t<view class=\"f3\"><switch value=\"1\" :checked=\"worker.status==1?true:false\" @change=\"switchchange\"></switch></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setinfo\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-shenfen.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">提现设置</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"list\" v-if=\"searchmember\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/activity/searchmember/searchmember\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-shenfen.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">一键查看</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setpwd\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-shenfen.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">修改密码</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"list\">\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"login\">\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"pre_url+'/static/img/peisong/ico-logout.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"f2\">退出登录</view>\n\t\t\t\t\t\t<text class=\"f3\"></text>\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"tabbar\">\n\t\t\t<view class=\"tabbar-bot\"></view>\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\n\t\t\t\t<view @tap=\"goto\" data-url=\"dating\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/home.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">大厅</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/order.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">订单</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist?st=3\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/orderwc.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">已完成</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"showform\" @tap=\"goto\" data-url=\"formlog\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/dangan.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">档案</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"my\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/my2.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\" :style=\"{color: t('color1')}\">我的</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\n\t</view>\n\t<loading v-else></loading>\n\t</template>\n\t\n\t<script>\n\tvar app = getApp();\n\timport loading from '@/components/loading/loading';\n\timport popmsg from '@/components/popmsg/popmsg.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tloading,\n\t\t\tpopmsg\n\t\t},\n\t  data() {\n\t\t\treturn {\n\t\t\t\topt:{},\n\t\t\t\tloading:false,\n\t\t  isload: false,\n\t\t\t\tmenuindex:-1,\n\t\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\tset:{},\n\t\t\t\tworker:{},\n\t\t\t\tchecked:'',\n\t\t\t\tshowform:0,\n\t\t\t\tsearchmember:false,\n\t\t\t\tsets:false,\n\t\t\t\tenable_promote: false,\n\t\t\t\tenable_calendar: false\n\t\t};\n\t  },\n\t  onLoad: function (opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.getdata();\n\t  },\n\t\tonPullDownRefresh: function () {\n\t\t\tthis.getdata();\n\t\t},\n\t  methods: {\n\t\t\tgetdata:function(){\n\t\t\t\tvar that = this;\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.get('ApiYuyueWorker/my', {}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tif(res.status === 1){\n\t\t\t\t\t\tthat.worker = res.worker;\n\t\t\t\t\t\tthat.showform = res.showform;\n\t\t\t\t\t\tthat.searchmember = res.searchmember;\n\t\t\t\t\t\tthat.sets = res.sets;\n\t\t\t\t\t\tthat.enable_promote = res.enable_promote;\n\t\t\t\t\t\tthat.enable_calendar = res.enable_calendar;\n\t\t\t\t\t\tthat.loaded({title:'服务人员中心'});\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\t}\n\t\t\t\t}, function(){\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t});\n\t\t\t},\n\t\tswitchchange: function (e) {\n\t\t  console.log(e);\n\t\t  var value = e.detail.value ? 1 : 0;\n\t\t  var that = this;\n\t\t  app.post('ApiYuyueWorker/setpsst', {st: value}, function (data) {\n\t\t\t\tif(data.status === 1){\n\t\t\t\t\tthat.worker.status = value;\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t}else{\n\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\tthat.$nextTick(() => {\n\t\t\t\t\t\tthat.worker.status = value === 1 ? 0 : 1;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsaoyisao: function (d) {\n\t\t  var that = this;\n\t\t\t\tif(app.globalData.platform == 'h5'){\n\t\t\t\t\tapp.alert('请使用微信扫一扫功能扫码核销');return;\n\t\t\t\t}else if(app.globalData.platform == 'mp'){\n\t\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\n\t\t\t\t\t\tjweixin.scanQRCode({\n\t\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\n\t\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\n\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\n\t\t\t\t\t\t\t\tvar params = content.split('?')[1];\n\t\t\t\t\t\t\t\tapp.goto('/admin/hexiao/hexiao?'+params);\n\t\t\t\t\t\t\t\t//if(content.length == 18 && (/^\\d+$/.test(content))){ //是十八位数字 付款码\n\t\t\t\t\t\t\t\t//\tlocation.href = \"{:url('shoukuan')}/aid/{$aid}/auth_code/\"+content\n\t\t\t\t\t\t\t\t//}else{\n\t\t\t\t\t\t\t\t//\tlocation.href = content;\n\t\t\t\t\t\t\t\t//}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tuni.scanCode({\n\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\tvar content = res.result;\n\t\t\t\t\t\t\tvar params = content.split('?')[1];\n\t\t\t\t\t\t\tapp.goto('/admin/hexiao/hexiao?'+params);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t},\n\t\tloaded: function(obj) {\n\t\t  this.isload = true;\n\t\t  uni.stopPullDownRefresh();\n\t\t  if(obj && obj.title) {\n\t\t\tuni.setNavigationBarTitle({ title: obj.title });\n\t\t  }\n\t\t},\n\t\tgoto: function (e) {\n\t\t  let url;\n\t\t  if (typeof e === 'string') {\n\t\t\turl = e;\n\t\t  } else {\n\t\t\turl = e.currentTarget.dataset.url;\n\t\t  }\n\t\t  let opentype = e.currentTarget?.dataset?.opentype || '';\n\t\t  if (!url) return false;\n\t\t  \n\t\t  if(url === 'login'){\n\t\t\t app.confirm('确定要退出登录吗？', function(){\n\t\t\t\t app.goto('/yuyue/login', 'reLaunch');\n\t\t\t });\n\t\t\t return;\n\t\t  }\n\t\t  \n\t\t  app.goto(url, opentype);\n\t\t}\n\t  }\n\t};\n\t</script>\n\t<style>\n\t@import \"./common.css\";\n\t.banner{ display:flex;width:100%;height:322rpx;padding:80rpx 32rpx 40rpx 32rpx;color:#fff;position:relative;\n\tbackground: linear-gradient(135deg, #06A051 0%, rgba(6, 160, 81, 0.8) 100%);}\n\t.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}\n\t.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}\n\t.banner .info .nickname{font-size:32rpx;font-weight:bold;padding-bottom:12rpx}\n\t.banner .sets{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}\n\t.banner .sets image{width:50rpx;height:50rpx;border-radius:0}\n\t\n\t.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-100rpx;position:relative;margin-bottom:20rpx}\n\t\n\t.custom_field{display:flex;width:100%;align-items:center;padding:30rpx 8rpx;background:#fff;border-radius:16rpx}\n\t.custom_field .item{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center}\n\t.custom_field .item .t1{color:#666;font-size:26rpx;margin-top:10rpx}\n\t.custom_field .item .t2{color:#111;font-weight:bold;font-size:36rpx;margin-top:20rpx}\n\t\n\t.score{ display:flex;width:100%;align-items:center;padding:10rpx 20rpx;background:#fff;border-top:1px dotted #eee}\n\t.score .f1 .t2{color:#ff3300}\n\t\n\t.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;margin-bottom:20rpx;border-radius:16rpx}\n\t.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:1px solid #eee}\n\t.list .item:last-child{border-bottom:0;margin-bottom:20rpx}\n\t.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center}\n\t.list .f1 image{ width:44rpx;height:44rpx;}\n\t.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\n\t.list .f2{color:#222;font-weight:bold;margin-left:10rpx}\n\t.list .f3{ color:#06A051;font-size:26rpx;text-align:right;flex:1}\n\t.list .f4{ width: 40rpx; height: 40rpx;}\n\t\n\tswitch{transform:scale(.7);}\n\n\t.tabbar {position: fixed;bottom: 0;left: 0;width: 100%;z-index: 99;}\n\t.tabbar-bot{height:env(safe-area-inset-bottom)}\n\t.tabbar-bar {display: flex;width: 100%;min-height: 100rpx;background: #fff;box-shadow: 0px -1px 3px rgba(0,0,0,0.06);padding-bottom: env(safe-area-inset-bottom);padding-top: 1px;justify-content: space-around;align-items: center;}\n\t.tabbar-item {display: flex;flex-direction: column;align-items: center;justify-content: center;width: auto;}\n\t.tabbar-image-box {position: relative;}\n\t.tabbar-icon {width: 54rpx;height: 54rpx;display: block;}\n\t.tabbar-text {font-size: 20rpx;color: #333;margin-top: 6rpx;}\n\t</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115053138\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}