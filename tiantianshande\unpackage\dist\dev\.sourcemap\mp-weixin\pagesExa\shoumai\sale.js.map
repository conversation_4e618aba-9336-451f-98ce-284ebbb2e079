{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?4597", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?3cd1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?824a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?2b45", "uni-app:///pagesExa/shoumai/sale.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?bc0e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/sale.vue?78c3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "userinfo", "settings", "availableAssets", "selectedAssetType", "selectedAsset", "amount", "discount", "onLoad", "onPullDownRefresh", "uni", "methods", "getdata", "that", "app", "setTimeout", "title", "processAvailableAssets", "selectAssetType", "discountChange", "moneyinput", "formSubmit", "asset_type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6F7wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;EACA;EAEAC;IACA;IACAC;MACA;MACAC;;MAEA;MACAC;QACAD;QAEA;UACAC;UACA;QACA;;QAEA;QACA;UACAA;UACAC;YACAD;UACA;UACA;QACA;;QAEA;QACAJ;UACAM;QACA;;QAEA;QACAH;QACAA;;QAEA;QACAA;QAEAA;MACA;IACA;IAEA;IACAI;MACA;MACA;;MAEA;MACA;MAEA;QAAA;MAAA;QACAd;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;QACAA;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;QACAA;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;QACAA;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;QACAA;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;QACAA;UAAA;QAAA;MACA;MAEA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAe;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAN;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;MACA;QACAA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAO;MACA;;MAEA;MACA;QACAP;QACA;MACA;MAEA;MACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;MACAA;QACAA;;QAEA;QACAA;UACAR;UACAC;UACAe;QACA;UACAR;UAEA;YACAA;YACA;UACA;YACAA;YACAC;cACAD;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxUA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/shoumai/sale.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/shoumai/sale.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./sale.vue?vue&type=template&id=9f9d7c3a&\"\nvar renderjs\nimport script from \"./sale.vue?vue&type=script&lang=js&\"\nexport * from \"./sale.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sale.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/shoumai/sale.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sale.vue?vue&type=template&id=9f9d7c3a&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.selectedAsset ? _vm.t(\"color1\") : null\n  var g0 =\n    _vm.isload && _vm.selectedAsset ? (_vm.discount * 100).toFixed(0) : null\n  var g1 =\n    _vm.isload && _vm.selectedAsset && _vm.amount > 0\n      ? (_vm.amount * _vm.discount).toFixed(2)\n      : null\n  var g2 =\n    _vm.isload && _vm.amount > 0 && _vm.settings && _vm.settings.sale_fee > 0\n      ? (_vm.amount * _vm.discount * _vm.settings.sale_fee).toFixed(2)\n      : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sale.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sale.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t<form @submit=\"formSubmit\">\r\n\t\t<!-- 资产类型选择 -->\r\n\t\t<view class=\"asset-type-selector\">\r\n\t\t\t<view class=\"selector-title\">選擇售賣資產類型</view>\r\n\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(asset, index) in availableAssets\" \r\n\t\t\t\t\t:key=\"asset.type\" \r\n\t\t\t\t\tclass=\"asset-item\" \r\n\t\t\t\t\t:class=\"{active: selectedAssetType === asset.type}\"\r\n\t\t\t\t\t@tap=\"selectAssetType(asset.type, index)\">\r\n\t\t\t\t\t<text>{{asset.name}}</text>\r\n\t\t\t\t\t<text class=\"asset-amount\">{{asset.amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 资产余额显示 -->\r\n\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\" v-if=\"selectedAsset\">\r\n\t\t\t<view class=\"f1\">{{selectedAsset.name}}</view>\r\n\t\t\t<view class=\"f2\">{{selectedAsset.amount}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 售卖说明 -->\r\n\t\t<view class=\"notice-box\">\r\n\t\t\t<view class=\"notice-title\">售卖說明</view>\r\n\t\t\t<view class=\"notice-content\">\r\n\t\t\t\t<text v-if=\"selectedAsset\">1. {{selectedAsset.name}}售卖折扣率范围: {{selectedAsset.min_discount}} - {{selectedAsset.max_discount}}</text>\r\n\t\t\t\t<text>2. 您的資產將以折扣價格出售給其他用戶</text>\r\n\t\t\t\t<text>3. 買家將直接向您支付相應的金額</text>\r\n\t\t\t\t<text>4. 交易成功前您的資產將被凍結</text>\r\n\t\t\t\t<text>5. 請務必先設置錢包信息才能進行售賣</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"content2\">\r\n\t\t\t<!-- 售卖金额 -->\r\n\t\t\t<view class=\"item2\"><view class=\"f1\">出售{{selectedAsset ? selectedAsset.name : '資產'}}</view></view>\r\n\t\t\t<view class=\"item3\">\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<input class=\"input\" type=\"digit\" name=\"amount\" v-model=\"amount\" placeholder=\"請輸入金額\" placeholder-style=\"color:#999;font-size:40rpx\" @input=\"moneyinput\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 折扣率设置 -->\r\n\t\t\t<view class=\"discount-section\" v-if=\"selectedAsset\">\r\n\t\t\t\t<view class=\"discount-title\">折扣率設置</view>\r\n\t\t\t\t<view class=\"discount-slider\">\r\n\t\t\t\t\t<slider \r\n\t\t\t\t\t\t:min=\"selectedAsset.min_discount * 100\" \r\n\t\t\t\t\t\t:max=\"selectedAsset.max_discount * 100\" \r\n\t\t\t\t\t\t:value=\"discount * 100\" \r\n\t\t\t\t\t\tshow-value \r\n\t\t\t\t\t\t@change=\"discountChange\"\r\n\t\t\t\t\t\tactiveColor=\"#533CD7\"\r\n\t\t\t\t\t\tbackgroundColor=\"#eee\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"discount-value\">\r\n\t\t\t\t\t<text>当前折扣率: {{(discount * 100).toFixed(0)}}%</text>\r\n\t\t\t\t\t<text v-if=\"amount > 0\">折扣后金额: {{(amount * discount).toFixed(2)}} 元</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 最低售卖限制提示 -->\r\n\t\t\t<view class=\"item4\" v-if=\"amount > 0 && settings && settings.sale_fee > 0\">\r\n\t\t\t\t<text>手续费率: {{settings.sale_fee * 100}}%, 手续费: {{(amount * discount * settings.sale_fee).toFixed(2)}} 元</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item4\" v-if=\"amount > 0 && settings && settings.min_amount > 0\">\r\n\t\t\t\t<text style=\"color:#ff6b6b\">最低售卖金额: {{settings.min_amount}} 元</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 提交按钮 -->\r\n\t\t<button class=\"btn\" :style=\"{background:t('color1')}\" @tap=\"formSubmit\">確 認 售 出</button>\r\n\t\t\r\n\t\t<!-- 钱包设置链接 -->\r\n\t\t<view style=\"width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center\" @tap=\"goto\" data-url=\"/pagesExa/shoumai/walletsite\">\r\n\t\t\t<text style=\"margin-right:10rpx\">{{ userinfo.set_wallet == 0 ? '請先設定USTD帳戶' : '修改USTD帳戶' }}</text>\r\n\t\t\t<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t</view>\r\n\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\topt:{},\r\n\t\tloading: false,\r\n      \tisload: false,\r\n\t\tmenuindex: -1,\r\n\t\t\r\n\t\t// 用户信息\r\n      \tuserinfo: [],\r\n\t\t\r\n\t\t// 设置信息\r\n\t\tsettings: null,\r\n\t\t\r\n\t\t// 可用资产类型\r\n\t\tavailableAssets: [],\r\n\t\tselectedAssetType: \"\",\r\n\t\tselectedAsset: null,\r\n\t\t\r\n\t\t// 金额和折扣\r\n\t\tamount: \"\",\r\n\t\tdiscount: 0.9, // 默认折扣\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\tthis.opt = app.getopts(opt);\r\n\tthis.getdata();\r\n  },\r\n  \r\n  onPullDownRefresh: function () {\r\n\tthis.getdata();\r\n\tuni.stopPullDownRefresh();\r\n  },\r\n  \r\n  methods: {\r\n\t// 获取数据\r\n\tgetdata: function () {\r\n\t\tvar that = this;\r\n\t\tthat.loading = true;\r\n\t\t\r\n\t\t// 获取售卖设置信息\r\n\t\tapp.get('ApiShoumai/getSaleSettings', {}, function (res) {\r\n\t\t\tthat.loading = false;\r\n\r\n\t\t\tif (!res.status) {\r\n\t\t\t\tapp.error(res.msg || '获取设置失败');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查钱包设置\r\n\t\t\tif (res.data.user_info.set_wallet === 0) {\r\n\t\t\t\tapp.error('請先設置錢包信息');\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tapp.goto(\"/pagesExa/shoumai/walletsite\")\r\n\t\t\t\t}, 1000);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 设置标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: '资产售卖'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 保存设置和用户信息\r\n\t\t\tthat.settings = res.data.settings;\r\n\t\t\tthat.userinfo = res.data.user_info;\r\n\t\t\t\r\n\t\t\t// 处理可用资产类型\r\n\t\t\tthat.processAvailableAssets(res.data);\r\n\t\t\t\r\n\t\t\tthat.isload = true;\r\n\t\t});\r\n\t},\r\n\t\r\n\t// 处理可用资产类型\r\n\tprocessAvailableAssets: function(data) {\r\n\t\tvar assets = data.assets || [];\r\n\t\tvar settings = data.settings;\r\n\t\t\r\n\t\t// 过滤可用资产类型\r\n\t\tvar availableAssets = [];\r\n\t\t\r\n\t\tif (settings.enable_money === 1 && assets.find(a => a.type === 'money')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'money'));\r\n\t\t}\r\n\t\t\r\n\t\tif (settings.enable_score === 1 && assets.find(a => a.type === 'score')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'score'));\r\n\t\t}\r\n\t\t\r\n\t\tif (settings.enable_commission === 1 && assets.find(a => a.type === 'commission')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'commission'));\r\n\t\t}\r\n\t\t\r\n\t\tif (settings.enable_heiscore === 1 && assets.find(a => a.type === 'heiscore')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'heiscore'));\r\n\t\t}\r\n\t\t\r\n\t\tif (settings.enable_contribution === 1 && assets.find(a => a.type === 'contribution')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'contribution'));\r\n\t\t}\r\n\t\t\r\n\t\tif (settings.enable_score_huangjin === 1 && assets.find(a => a.type === 'score_huangjin')) {\r\n\t\t\tavailableAssets.push(assets.find(a => a.type === 'score_huangjin'));\r\n\t\t}\r\n\t\t\r\n\t\tthis.availableAssets = availableAssets;\r\n\t\t\r\n\t\t// 如果有可用资产，默认选择第一个\r\n\t\tif (availableAssets.length > 0) {\r\n\t\t\tthis.selectAssetType(availableAssets[0].type, 0);\r\n\t\t}\r\n\t},\r\n\t\r\n\t// 选择资产类型\r\n\tselectAssetType: function(type, index) {\r\n\t\tthis.selectedAssetType = type;\r\n\t\tthis.selectedAsset = this.availableAssets[index];\r\n\t\t\r\n\t\t// 设置折扣默认值为中间值\r\n\t\tif (this.selectedAsset) {\r\n\t\t\tvar minDiscount = parseFloat(this.selectedAsset.min_discount);\r\n\t\t\tvar maxDiscount = parseFloat(this.selectedAsset.max_discount);\r\n\t\t\tthis.discount = ((minDiscount + maxDiscount) / 2).toFixed(2);\r\n\t\t}\r\n\t\t\r\n\t\t// 清空金额\r\n\t\tthis.amount = \"\";\r\n\t},\r\n\t\r\n\t// 折扣滑块变化处理\r\n\tdiscountChange: function(e) {\r\n\t\tthis.discount = (e.detail.value / 100).toFixed(2);\r\n\t},\r\n\t\t\r\n    // 金额输入处理\r\n    moneyinput: function (e) {\r\n\t\tif (!this.selectedAsset) {\r\n\t\t\tapp.error('請先選擇資產類型');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tvar assetAmount = parseFloat(this.selectedAsset.amount);\r\n\t\tvar amount = parseFloat(e.detail.value);\r\n      \r\n\t\t// 清空金额时的处理\r\n\t\tif (e.detail.value === '' || isNaN(amount)) {\r\n\t\t\tthis.amount = '';\r\n\t\t\treturn;\r\n\t\t}\r\n      \r\n\t\t// 金额验证\r\n\t\tif (amount <= 0) {\r\n\t\t\tapp.error('金額必須大於0');\r\n\t\t\tthis.amount = '';\r\n\t\t\treturn;\r\n\t\t} else if (amount > assetAmount) {\r\n\t\t\tapp.error(this.selectedAsset.name + '餘額不足');\r\n\t\t\tthis.amount = assetAmount.toString();\r\n\t\t\treturn;\r\n\t\t}\r\n\t  \r\n\t\tthis.amount = amount.toString();\r\n    },\r\n\t\r\n    // 表单提交\r\n    formSubmit: function () {\r\n\t\tvar that = this;\r\n\t\t\r\n\t\t// 验证资产类型选择\r\n\t\tif (!that.selectedAsset) {\r\n\t\t\tapp.error('請選擇要售賣的資產類型');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tvar assetAmount = parseFloat(that.selectedAsset.amount);\r\n\t\tvar amount = parseFloat(that.amount);\r\n\t\tvar discount = parseFloat(that.discount);\r\n      \r\n\t\t// 验证输入\r\n\t\tif (isNaN(amount) || amount <= 0) {\r\n\t\t\tapp.error('金額必須大於0');\r\n\t\t\treturn;\r\n\t\t}\r\n      \r\n\t\t// 验证最低售卖金额\r\n\t\tif (that.settings.min_amount > 0 && amount < that.settings.min_amount) {\r\n\t\t\tapp.error('金額必須大於' + that.settings.min_amount);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// 验证资产余额\r\n\t\tif (amount > assetAmount) {\r\n\t\t\tapp.error(that.selectedAsset.name + '餘額不足');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\t// 验证折扣率范围\r\n\t\tvar minDiscount = parseFloat(that.selectedAsset.min_discount);\r\n\t\tvar maxDiscount = parseFloat(that.selectedAsset.max_discount);\r\n\t\t\r\n\t\tif (discount < minDiscount || discount > maxDiscount) {\r\n\t\t\tapp.error('折扣率必須在' + (minDiscount * 100) + '%-' + (maxDiscount * 100) + '%之間');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\t\r\n\t\t// 二次确认\r\n\t\tvar discountedPrice = (amount * discount).toFixed(2);\r\n\t\tapp.confirm('確認出售' + amount + that.selectedAsset.name + '嗎？折扣后約為' + discountedPrice + '元', function() {\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\t\r\n\t\t\t// 提交售卖信息\r\n\t\t\tapp.post('ApiShoumai/postSale', {\r\n\t\t\t\tamount: amount,\r\n\t\t\t\tdiscount: discount,\r\n\t\t\t\tasset_type: that.selectedAssetType\r\n\t\t\t}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goto('/pagesExa/shoumai/index?my=1');\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column}\r\n\r\n/* 资产类型选择器样式 */\r\n.asset-type-selector {\r\n\twidth: 94%;\r\n\tmargin: 20rpx 3%;\r\n\tbackground: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n}\r\n.selector-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n.selector-content {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n}\r\n.asset-item {\r\n\twidth: calc(33.33% - 20rpx);\r\n\tmargin: 10rpx;\r\n\tbackground: #f5f5f5;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n.asset-item.active {\r\n\tbackground: #533CD7;\r\n\tcolor: #fff;\r\n}\r\n.asset-amount {\r\n\tfont-size: 24rpx;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}\r\n.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}\r\n.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}\r\n.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}\r\n\r\n.notice-box {\r\n\twidth: 94%;\r\n\tmargin: 10rpx 3% 20rpx 3%;\r\n\tbackground: #f8f8fc;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tborder-left: 4px solid #533CD7;\r\n}\r\n.notice-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n.notice-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n.notice-content text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n\tline-height: 1.5;\r\n}\r\n\r\n/* 折扣率设置样式 */\r\n.discount-section {\r\n\twidth: 94%;\r\n\tmargin: 0 3%;\r\n\tpadding: 20rpx 0;\r\n\tborder-top: 1px solid #F0F0F0;\r\n}\r\n.discount-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n.discount-slider {\r\n\tpadding: 0 20rpx;\r\n}\r\n.discount-value {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-top: 10rpx;\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}\r\n.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}\r\n.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}\r\n.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}\r\n\r\n.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}\r\n.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}\r\n\r\n.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}\r\n.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}\r\n.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}\r\n.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}\r\n.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sale.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./sale.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061105\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}