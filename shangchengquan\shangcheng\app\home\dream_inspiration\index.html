<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>{:t('梦想记录')}</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	{include file="public/css"/}
</head>
<body>
	<div class="layui-fluid">
		<div class="layui-row layui-col-space15">
			<div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<i class="fa fa-magic"></i> {:t('梦想记录')}
				</div>
				<div class="layui-card-body" pad15>
					<!-- 操作按钮区域 -->
					<div class="layui-row" style="margin-bottom: 15px;">
						<div class="layui-col-md4">
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">
								<i class="layui-icon layui-icon-delete"></i> {:t('批量删除')}
							</button>
							<button class="layui-btn layui-btn-normal layuiadmin-btn-list" onclick="location.reload()">
								<i class="layui-icon layui-icon-refresh"></i> {:t('刷新')}
							</button>
							<button class="layui-btn layui-btn-warm layuiadmin-btn-list" onclick="exportData()">
								<i class="layui-icon layui-icon-export"></i> {:t('导出')}
							</button>
						</div>
						<div class="layui-col-md8">
							<form class="layui-form" style="text-align: right;">
								<div class="layui-inline">
									<label class="layui-form-label" style="width: 60px;">{:t('昵称')}</label>
									<div class="layui-input-block" style="width: 120px; margin-left: 90px;">
										<input type="text" name="nickname" placeholder="{:t('请输入昵称')}" autocomplete="off" class="layui-input">
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label" style="width: 40px;">{:t('性别')}</label>
									<div class="layui-input-block" style="width: 100px; margin-left: 70px;">
										<select name="gender">
											<option value="">{:t('全部')}</option>
											<option value="1">{:t('男')}</option>
											<option value="0">{:t('女')}</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label" style="width: 40px;">{:t('状态')}</label>
									<div class="layui-input-block" style="width: 100px; margin-left: 70px;">
										<select name="status">
											<option value="">{:t('全部')}</option>
											<option value="1">{:t('已生成')}</option>
											<option value="0">{:t('生成中')}</option>
											<option value="-1">{:t('生成失败')}</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<button class="layui-btn layuiadmin-btn-replys" lay-submit lay-filter="LAY-user-front-search">
										<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
									</button>
								</div>
							</form>
						</div>
					</div>
					<!-- 数据表格 -->
					<table class="layui-hide" id="LAY-user-manage" lay-filter="LAY-user-manage"></table>

					<!-- 操作按钮模板 -->
					<script type="text/html" id="table-useradmin-admin">
						<a class="layui-btn layui-btn-normal layui-btn-xs table-btn" lay-event="detail" title="{:t('查看详情')}">
							<i class="layui-icon layui-icon-about"></i>{:t('详情')}
						</a>
						<a class="layui-btn layui-btn-warm layui-btn-xs table-btn" lay-event="regenerate" title="{:t('重新生成')}">
							<i class="layui-icon layui-icon-refresh-3"></i>{:t('重生成')}
						</a>
						<a class="layui-btn layui-btn-danger layui-btn-xs table-btn" lay-event="del" title="{:t('删除记录')}">
							<i class="layui-icon layui-icon-delete"></i>{:t('删除')}
						</a>
					</script>

					<!-- 状态显示模板 -->
					<script type="text/html" id="table-useradmin-status">
						{{# if(d.status == 1){ }}
							<span class="layui-badge layui-bg-green"><i class="layui-icon layui-icon-ok"></i> {:t('已生成')}</span>
						{{# } else if(d.status == 0) { }}
							<span class="layui-badge layui-bg-orange"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> {:t('生成中')}</span>
						{{# } else { }}
							<span class="layui-badge layui-bg-red"><i class="layui-icon layui-icon-close"></i> {:t('生成失败')}</span>
						{{# } }}
					</script>

					<!-- 性别显示模板 -->
					<script type="text/html" id="table-useradmin-gender">
						{{# if(d.gender == 1){ }}
							<span class="layui-badge layui-bg-blue"><i class="layui-icon layui-icon-male"></i> {:t('男')}</span>
						{{# } else { }}
							<span class="layui-badge layui-bg-red"><i class="layui-icon layui-icon-female"></i> {:t('女')}</span>
						{{# } }}
					</script>

					<!-- 图片显示模板 -->
					<script type="text/html" id="table-useradmin-image">
						{{# if(d.result_image){ }}
							<div class="image-preview" onclick="previewImage('{{d.result_image}}', '{{d.nickname}}的梦想图片')">
								<img src="{{d.result_image}}" style="width:80px;height:80px;object-fit:cover;border-radius:8px;cursor:pointer;border:2px solid #e6e6e6;transition:all 0.3s;"
									onmouseover="this.style.borderColor='#1890ff'" onmouseout="this.style.borderColor='#e6e6e6'">
								<div class="image-mask">
									<i class="layui-icon layui-icon-search" style="color:#fff;font-size:16px;"></i>
								</div>
							</div>
						{{# } else { }}
							<div style="text-align:center;padding:10px;">
								<i class="layui-icon layui-icon-picture" style="font-size:24px;color:#ccc;"></i>
								<div style="color:#999;font-size:12px;margin-top:5px;">{:t('暂无图片')}</div>
							</div>
						{{# } }}
					</script>

					<!-- 梦想内容显示模板 -->
					<script type="text/html" id="table-useradmin-dream">
						<div class="dream-content" title="{{d.dream_content}}">
							{{# if(d.dream_content && d.dream_content.length > 50){ }}
								{{d.dream_content.substring(0, 50)}}...
							{{# } else { }}
								{{d.dream_content || '暂无内容'}}
							{{# } }}
						</div>
					</script>
          </div>
        </div>
    </div>
  </div>

	{include file="public/js"/}
	<script>
		var table = layui.table;
		var form = layui.form;
		var layer = layui.layer;

		// 渲染数据表格
		var tableIns = table.render({
			elem: '#LAY-user-manage',
			url: '{:url("index")}',
			cols: [[
				{type: 'checkbox', fixed: 'left'},
				{field: 'id', width: 80, title: 'ID', sort: true},
				{field: 'nickname', title: '{:t("昵称")}', minWidth: 120},
				{field: 'gender', title: '{:t("性别")}', width: 80, templet: '#table-useradmin-gender', align: 'center'},
				{field: 'dream_content', title: '{:t("梦想内容")}', minWidth: 250, templet: '#table-useradmin-dream'},
				{field: 'profession', title: '{:t("职业")}', width: 100},
				{field: 'result_image', title: '{:t("生成图片")}', width: 120, templet: '#table-useradmin-image', align: 'center'},
				{field: 'status', title: '{:t("状态")}', width: 120, templet: '#table-useradmin-status', align: 'center'},
				{field: 'create_time_text', title: '{:t("创建时间")}', width: 160, sort: true},
				{title: '{:t("操作")}', width: 200, align: 'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
			]],
			page: true,
			limit: 20,
			limits: [10, 20, 30, 50, 100],
			height: 'full-220',
			text: '{:t("对不起，加载出现异常！")}',
			even: true,
			loading: true
		});

		// 监听搜索表单提交
		form.on('submit(LAY-user-front-search)', function(data){
			var field = data.field;
			// 执行表格重载
			tableIns.reload({
				where: field,
				page: {
					curr: 1 // 重新从第1页开始
				}
			});
			return false;
		});

		// 监听工具条操作
		table.on('tool(LAY-user-manage)', function(obj){
			var data = obj.data;
			var layEvent = obj.event;

			if(layEvent === 'del'){
				// 删除记录
				layer.confirm('{:t("确定要删除这条梦想记录吗？")}', {
					icon: 3,
					title: '{:t("删除确认")}'
				}, function(index){
					var loadIndex = layer.load(2);
					$.post('{:url("del")}', {id: data.id}, function(res){
						layer.close(loadIndex);
						if(res.code == 1){
							layer.msg(res.msg, {icon: 1});
							obj.del(); // 删除对应行（tr）的DOM结构
						} else {
							layer.msg(res.msg, {icon: 2});
						}
					}).fail(function(){
						layer.close(loadIndex);
						layer.msg('{:t("网络请求失败")}', {icon: 2});
					});
					layer.close(index);
				});
			} else if(layEvent === 'detail'){
				// 查看详情
				openmax('{:url("detail")}/id/' + data.id, '{:t("梦想记录详情")}');
			} else if(layEvent === 'regenerate'){
				// 重新生成
				layer.confirm('{:t("确定要重新生成这张梦想图片吗？")}', {
					icon: 3,
					title: '{:t("重新生成确认")}'
				}, function(index){
					var loadIndex = layer.load(2, {content: '{:t("正在重新生成，请稍候...")}'});
					$.post('{:url("regenerate")}', {id: data.id}, function(res){
						layer.close(loadIndex);
						if(res.code == 1){
							layer.msg(res.msg, {icon: 1});
							// 刷新表格
							tableIns.reload();
						} else {
							layer.msg(res.msg, {icon: 2});
						}
					}).fail(function(){
						layer.close(loadIndex);
						layer.msg('{:t("网络请求失败")}', {icon: 2});
					});
					layer.close(index);
				});
			}
		});

		// 批量删除功能
		function datadel(id){
			var checkStatus = table.checkStatus('LAY-user-manage');
			var data = checkStatus.data;
			if(data.length === 0){
				layer.msg('{:t("请选择要删除的数据")}', {icon: 2});
				return;
			}

			var ids = [];
			for(var i = 0; i < data.length; i++){
				ids.push(data[i].id);
			}

			layer.confirm('{:t("确定删除选中的")} ' + data.length + ' {:t("条记录吗？")}', {
				icon: 3,
				title: '{:t("批量删除确认")}'
			}, function(index){
				var loadIndex = layer.load(2, {content: '{:t("正在删除，请稍候...")}'});
				$.post('{:url("delall")}', {ids: ids.join(',')}, function(res){
					layer.close(loadIndex);
					if(res.code == 1){
						layer.msg(res.msg, {icon: 1});
						tableIns.reload();
					} else {
						layer.msg(res.msg, {icon: 2});
					}
				}).fail(function(){
					layer.close(loadIndex);
					layer.msg('{:t("网络请求失败")}', {icon: 2});
				});
				layer.close(index);
			});
		}

		// 导出数据功能
		function exportData(){
			var checkStatus = table.checkStatus('LAY-user-manage');
			var data = checkStatus.data;

			if(data.length === 0){
				layer.confirm('{:t("是否导出全部数据？")}', {
					icon: 3,
					title: '{:t("导出确认")}'
				}, function(index){
					window.open('{:url("export")}');
					layer.close(index);
				});
			} else {
				var ids = [];
				for(var i = 0; i < data.length; i++){
					ids.push(data[i].id);
				}
				window.open('{:url("export")}?ids=' + ids.join(','));
			}
		}

		// 图片预览功能
		function previewImage(src, title){
			layer.photos({
				photos: {
					title: title || '{:t("图片预览")}',
					data: [{
						src: src,
						alt: title || '{:t("梦想图片")}'
					}]
				},
				anim: 5,
				shade: 0.8
			});
		}

		// 自动刷新功能（可选）
		var autoRefresh = false;
		var refreshTimer = null;

		function toggleAutoRefresh(){
			if(autoRefresh){
				clearInterval(refreshTimer);
				autoRefresh = false;
				layer.msg('{:t("已关闭自动刷新")}', {icon: 1});
			} else {
				refreshTimer = setInterval(function(){
					tableIns.reload();
				}, 30000); // 30秒刷新一次
				autoRefresh = true;
				layer.msg('{:t("已开启自动刷新（30秒）")}', {icon: 1});
			}
		}

		// 页面卸载时清理定时器
		window.addEventListener('beforeunload', function(){
			if(refreshTimer){
				clearInterval(refreshTimer);
			}
		});
	</script>

	<style>
		/* 自定义样式 */
		.image-preview {
			position: relative;
			display: inline-block;
		}

		.image-preview .image-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0;
			transition: opacity 0.3s;
			border-radius: 8px;
		}

		.image-preview:hover .image-mask {
			opacity: 1;
		}

		.dream-content {
			line-height: 1.5;
			word-break: break-all;
			max-height: 60px;
			overflow: hidden;
		}

		.table-btn {
			margin-right: 5px;
		}

		.table-btn:last-child {
			margin-right: 0;
		}

		/* 状态标签动画 */
		.layui-anim-rotate {
			animation: layui-rotate 2s linear infinite;
		}

		@keyframes layui-rotate {
			from { transform: rotate(0deg); }
			to { transform: rotate(360deg); }
		}
	</style>
</body>
</html>
