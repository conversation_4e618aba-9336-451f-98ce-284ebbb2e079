{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?6a7b", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?e112", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?54fd", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?021a", "uni-app:///yuyue/comment.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?8b84", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comment.vue?436e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "orderInfo", "rating", "content", "imageList", "computed", "canSubmit", "onLoad", "methods", "getOrderInfo", "console", "app", "id", "that", "setRating", "chooseImage", "deleteImage", "submitComment", "order_id", "score", "pics", "oid"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0EhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MACAC;QACAC;MACA;QACAF;;QAEA;QACA;UACA;UACAG;UACAH;QACA;UACA;UACAG;UACAH;QACA;UACA;UACA;UACAC;UACAD;QACA;MACA;IACA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAJ;QACA;MACA;MACA;MACAA;QACA;QACAE;QACA;UACA;UACAA;QACA;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;MACAP;MACAA;MACAA;MACAA;MAEA;QACAA;QACAC;QACA;MACA;;MAEA;MACAD;QACAQ;QACAC;QACAhB;QACAiB;MACA;MACAT;QACAU;QACAF;QACAhB;QACAiB;MACA;QACA;UACAT;YACAA;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/comment.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/comment.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./comment.vue?vue&type=template&id=4ab6195f&\"\nvar renderjs\nimport script from \"./comment.vue?vue&type=script&lang=js&\"\nexport * from \"./comment.vue?vue&type=script&lang=js&\"\nimport style0 from \"./comment.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/comment.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comment.vue?vue&type=template&id=4ab6195f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.content.length\n  var g1 = _vm.imageList.length\n  var m0 = _vm.canSubmit ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comment.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<view class=\"order-info\">\r\n\t\t<view class=\"product-info\">\r\n\t\t\t<image :src=\"orderInfo.propic\" mode=\"aspectFill\"></image>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t<text class=\"name\">{{orderInfo.proname}}</text>\r\n\t\t\t\t<text class=\"price\">￥{{orderInfo.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view class=\"comment-form\">\r\n\t\t<view class=\"rating\">\r\n\t\t\t<text class=\"label\">服务评分：</text>\r\n\t\t\t<view class=\"stars\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"n in 5\" \r\n\t\t\t\t\t:key=\"n\" \r\n\t\t\t\t\tclass=\"star\" \r\n\t\t\t\t\t:class=\"{'active': n <= rating}\"\r\n\t\t\t\t\t@tap=\"setRating(n)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"iconfont\" :class=\"n <= rating ? 'iconxingshifill' : 'iconxingshi'\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"textarea-box\">\r\n\t\t\t<textarea \r\n\t\t\t\tv-model=\"content\" \r\n\t\t\t\tplaceholder=\"请输入您的评价内容\" \r\n\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\tclass=\"textarea\"\r\n\t\t\t></textarea>\r\n\t\t\t<text class=\"word-count\">{{content.length}}/200</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"upload-box\">\r\n\t\t\t<view class=\"label\">上传图片(最多3张)：</view>\r\n\t\t\t<view class=\"image-list\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(item, index) in imageList\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"image-item\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<image :src=\"item\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"delete-btn\" @tap=\"deleteImage(index)\">\r\n\t\t\t\t\t\t<text class=\"iconfont iconclose\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"upload-btn\" \r\n\t\t\t\t\t@tap=\"chooseImage\" \r\n\t\t\t\t\tv-if=\"imageList.length < 3\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"iconfont iconjiahao\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view class=\"footer\">\r\n\t\t<button \r\n\t\t\tclass=\"submit-btn\" \r\n\t\t\t:disabled=\"!canSubmit\"\r\n\t\t\t@tap=\"submitComment\"\r\n\t\t\t:style=\"{backgroundColor: canSubmit ? t('color1') : '#cccccc'}\"\r\n\t\t>提交评价</button>\r\n\t</view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\torderId: '',\r\n\t\t\torderInfo: {},\r\n\t\t\trating: 5,\r\n\t\t\tcontent: '',\r\n\t\t\timageList: []\r\n\t\t}\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\tcanSubmit() {\r\n\t\t\treturn this.rating > 0 && this.content.trim().length > 0;\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad(options) {\r\n\t\tthis.orderId = options.oid;\r\n\t\tthis.getOrderInfo();\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t// 获取订单信息\r\n\t\tgetOrderInfo() {\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log('getOrderInfo called with orderId:', that.orderId);\r\n\t\t\tapp.get('ApiYuyue/orderDetail', {\r\n\t\t\t\tid: that.orderId\r\n\t\t\t}, function(res) {\r\n\t\t\t\tconsole.log('Response from ApiYuyue/orderDetail:', res);\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否返回了订单详情\r\n\t\t\t\tif (res && res.detail) {\r\n\t\t\t\t\t// 直接使用返回的detail对象作为订单信息\r\n\t\t\t\t\tthat.orderInfo = res.detail;\r\n\t\t\t\t\tconsole.log('Order info loaded successfully:', that.orderInfo);\r\n\t\t\t\t} else if (res && res.status == 1 && res.data) {\r\n\t\t\t\t\t// 兼容之前的数据结构\r\n\t\t\t\t\tthat.orderInfo = res.data;\r\n\t\t\t\t\tconsole.log('Order info loaded successfully (using res.data):', that.orderInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 错误处理：如果没有detail也没有data\r\n\t\t\t\t\tlet errorMsg = (res && res.msg) ? res.msg : '获取订单信息失败';\r\n\t\t\t\t\tapp.error(errorMsg);\r\n\t\t\t\t\tconsole.error('Failed to get order info:', errorMsg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 设置评分\r\n\t\tsetRating(n) {\r\n\t\t\tthis.rating = n;\r\n\t\t},\r\n\t\t\r\n\t\t// 选择并上传图片 (调用全局方法)\r\n\t\tchooseImage() {\r\n\t\t\tvar that = this;\r\n\t\t\tif (that.imageList.length >= 3) {\r\n\t\t\t\tapp.error('最多上传3张图片');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tvar count = 3 - that.imageList.length;\r\n\t\t\tapp.chooseImage(function(urls) {\r\n\t\t\t\t// 返回的是上传成功后的URL数组\r\n\t\t\t\tthat.imageList = that.imageList.concat(urls);\r\n\t\t\t\tif (that.imageList.length > 3) {\r\n\t\t\t\t\t// 以防万一，如果返回的urls过多，截取前3张\r\n\t\t\t\t\tthat.imageList = that.imageList.slice(0, 3);\r\n\t\t\t\t}\r\n\t\t\t}, count);\r\n\t\t},\r\n\t\t\r\n\t\t// 删除图片\r\n\t\tdeleteImage(index) {\r\n\t\t\tthis.imageList.splice(index, 1);\r\n\t\t},\r\n\t\t\r\n\t\t// 提交评价\r\n\t\tsubmitComment() {\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log('submitComment function called.');\r\n\t\t\tconsole.log('Current rating:', that.rating);\r\n\t\t\tconsole.log('Current content:', that.content);\r\n\t\t\tconsole.log('canSubmit computed value:', that.canSubmit);\r\n\r\n\t\t\tif (!that.canSubmit) {\r\n\t\t\t\tconsole.log('Submit button is disabled. Submission prevented.');\r\n\t\t\t\tapp.error('请先评分并填写评价内容'); // 或者其他更合适的提示\r\n\t\t\t\treturn; // 阻止后续执行\r\n\t\t\t}\r\n\r\n\t\t\t// 添加日志打印发送的数据\r\n\t\t\tconsole.log('Submitting comment with data:', {\r\n\t\t\t\torder_id: that.orderId,\r\n\t\t\t\tscore: that.rating,\r\n\t\t\t\tcontent: that.content,\r\n\t\t\t\tpics: that.imageList.join(',')\r\n\t\t\t});\r\n\t\t\tapp.post('ApiYuyue/comment', {\r\n\t\t\t\toid: that.orderId,\r\n\t\t\t\tscore: that.rating,\r\n\t\t\t\tcontent: that.content,\r\n\t\t\t\tpics: that.imageList.join(',')\r\n\t\t\t}, function(res) {\r\n\t\t\t\tif(res.status == 1) {\r\n\t\t\t\t\tapp.alert('评价成功', function() {\r\n\t\t\t\t\t\tapp.goto('/yuyue/orderlist');\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tmin-height: 100vh;\r\n\tbackground: #f5f5f5;\r\n}\r\n\r\n.order-info {\r\n\tbackground: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.product-info {\r\n\tdisplay: flex;\r\n}\r\n\r\n.product-info image {\r\n\twidth: 140rpx;\r\n\theight: 140rpx;\r\n\tborder-radius: 8rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.info {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.price {\r\n\tfont-size: 32rpx;\r\n\tcolor: #ff4d4f;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.comment-form {\r\n\tbackground: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx 20rpx;\r\n}\r\n\r\n.rating {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.stars {\r\n\tdisplay: flex;\r\n}\r\n\r\n.star {\r\n\tfont-size: 40rpx;\r\n\tcolor: #ccc;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.star.active {\r\n\tcolor: #ffc107;\r\n}\r\n\r\n.textarea-box {\r\n\tposition: relative;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.textarea {\r\n\twidth: 100%;\r\n\theight: 200rpx;\r\n\tborder: 1px solid #e8e8e8;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 20rpx;\r\n\tfont-size: 28rpx;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.word-count {\r\n\tposition: absolute;\r\n\tright: 20rpx;\r\n\tbottom: 20rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.upload-box {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.image-list {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.image-item {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tposition: relative;\r\n\tmargin-right: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.image-item image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.delete-btn {\r\n\tposition: absolute;\r\n\ttop: -10rpx;\r\n\tright: -10rpx;\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tborder-radius: 50%;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.upload-btn {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tborder: 1px dashed #ccc;\r\n\tborder-radius: 8rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: #999;\r\n\tfont-size: 60rpx;\r\n}\r\n\r\n.footer {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tpadding: 20rpx;\r\n\tbackground: #fff;\r\n\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.submit-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 30rpx;\r\n\tborder-radius: 40rpx;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comment.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comment.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115044687\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}