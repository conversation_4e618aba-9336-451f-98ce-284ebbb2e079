{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?291a", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?833f", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?e1e5", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?2db2", "uni-app:///zhaopin/myApply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?06b9", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myApply.vue?318d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "currentTab", "swiper<PERSON><PERSON>rent", "applyJobs", "isLoading", "isRefreshing", "noMoreData", "page", "pageSize", "pre_url", "statusList", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "desc", "pic", "onShareTimeline", "imageUrl", "query", "onReachBottom", "methods", "switchTab", "swiper<PERSON><PERSON>e", "getStatusText", "loadApplyJobs", "limit", "params", "app", "uni", "Object", "tags", "console", "id", "salary", "companyName", "companyLogo", "applyTime", "status", "statusText", "icon", "onRefresh", "loadMore", "viewJobDetail", "url", "goToJobList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8HhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACAH;MACAC;MACAC;IACA;IACA;IACA;MACAF;MACAI;MACAC;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACAlB;QACAmB;MACA;;MAEA;MACA;QACAC;MACA;MAEAC;QACA;QACA;QACA;QACAC;QAEA;UACA;YACA;YACA;YACA;cACA;gBACA;gBACAC;kBACA;oBACAC;kBACA;gBACA;cACA;gBACAC;cACA;YACA;YACA;YACA;YACA;YACA;YAEA;cACAC;cACAnB;cACAoB;cACAC;cACAC;cACAL;cACAM;cACAC;cACAC;YACA;UACA;UAEA;YACA;UACA;YACA;UACA;UAEA;UACA;UACA;QACA;UACAV;YACAf;YACA0B;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAd;QACAe;MACA;IACA;IAEA;IACAC;MACAhB;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzTA;AAAA;AAAA;AAAA;AAAm6C,CAAgB,82CAAG,EAAC,C;;;;;;;;;;;ACAv7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/myApply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/myApply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myApply.vue?vue&type=template&id=6b939ba4&\"\nvar renderjs\nimport script from \"./myApply.vue?vue&type=script&lang=js&\"\nexport * from \"./myApply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myApply.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/myApply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myApply.vue?vue&type=template&id=6b939ba4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.statusList, function (tab, __i0__) {\n        var $orig = _vm.__get_orig(tab)\n        var m0 = _vm.currentTab === tab ? _vm.t(\"color1\") : null\n        var m1 = _vm.currentTab === tab ? _vm.t(\"color1\") : null\n        var m2 = _vm.currentTab === tab ? _vm.t(\"color1rgb\") : null\n        var m3 = _vm.currentTab === tab ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var l2 = _vm.isload\n    ? _vm.__map(\n        [\"all\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"],\n        function (status, index) {\n          var $orig = _vm.__get_orig(status)\n          var g0 = _vm.applyJobs.length\n          var l1 =\n            g0 > 0\n              ? _vm.__map(_vm.applyJobs, function (job, index) {\n                  var $orig = _vm.__get_orig(job)\n                  var m4 = _vm.getStatusText(job.status, job.statusText)\n                  return {\n                    $orig: $orig,\n                    m4: m4,\n                  }\n                })\n              : null\n          var m5 = !(g0 > 0) ? _vm.t(\"color1\") : null\n          var m6 = !(g0 > 0) ? _vm.t(\"color1\") : null\n          var m7 = !(g0 > 0) ? _vm.t(\"color1rgb\") : null\n          var g1 = _vm.isLoading && _vm.applyJobs.length > 0\n          var g2 = _vm.noMoreData && _vm.applyJobs.length > 0\n          return {\n            $orig: $orig,\n            g0: g0,\n            l1: l1,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n            g1: g1,\n            g2: g2,\n          }\n        }\n      )\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, job) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        job = _temp2.job\n      var _temp, _temp2\n      return _vm.viewJobDetail(job.id)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myApply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myApply.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <block v-if=\"isload\">\r\n      <view class=\"apply-page\">\r\n        <!-- 顶部筛选栏 -->\r\n        <scroll-view \r\n          scroll-x \r\n          class=\"filter-bar\" \r\n          :scroll-with-animation=\"true\"\r\n          :scroll-into-view=\"'tab-' + currentTab\"\r\n        >\r\n          <view \r\n            v-for=\"tab in statusList\"\r\n            :key=\"tab\"\r\n            class=\"filter-item\"\r\n            :id=\"'tab-' + tab\"\r\n            :class=\"{ active: currentTab === tab }\"\r\n            :style=\"{\r\n              background: currentTab === tab \r\n                ? `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`\r\n                : '#f8f9fa',\r\n              color: currentTab === tab ? '#fff' : '#666',\r\n              boxShadow: currentTab === tab \r\n                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.2)`\r\n                : '0 2rpx 6rpx rgba(0, 0, 0, 0.02)',\r\n              border: currentTab === tab \r\n                ? `1rpx solid rgba(${t('color1rgb')}, 0.1)`\r\n                : '1rpx solid rgba(0, 0, 0, 0.03)'\r\n            }\"\r\n            @tap=\"switchTab(tab)\"\r\n          >\r\n            {{ tab === 'all' ? '全部' : \r\n               tab === '1' ? '已投递' :\r\n               tab === '2' ? '已查看' :\r\n               tab === '3' ? '初筛通过' :\r\n               tab === '4' ? '待面试' :\r\n               tab === '5' ? '面试通过' :\r\n               tab === '6' ? '已入职' : '已结束'\r\n            }}\r\n          </view>\r\n        </scroll-view>\r\n\r\n        <!-- 职位列表 -->\r\n        <swiper \r\n          class=\"swiper-content\" \r\n          :current=\"swiperCurrent\"\r\n          @change=\"swiperChange\"\r\n          :style=\"{ height: 'calc(100vh - 120rpx)' }\"\r\n        >\r\n          <swiper-item v-for=\"(status, index) in ['all', '1', '2', '3', '4', '5', '6', '7']\" :key=\"index\">\r\n            <scroll-view \r\n              scroll-y \r\n              class=\"job-list\"\r\n              @scrolltolower=\"loadMore\"\r\n              refresher-enabled\r\n              :refresher-triggered=\"isRefreshing\"\r\n              @refresherrefresh=\"onRefresh\"\r\n            >\r\n              <!-- 列表内容 -->\r\n              <block v-if=\"applyJobs.length > 0\">\r\n                <view \r\n                  v-for=\"(job, index) in applyJobs\" \r\n                  :key=\"index\"\r\n                  class=\"job-card\"\r\n                  @tap=\"viewJobDetail(job.id)\"\r\n                >\r\n                  <!-- 职位信息 -->\r\n                  <view class=\"job-info\">\r\n                    <view class=\"job-header\">\r\n                      <text class=\"job-title\">{{ job.title }}</text>\r\n                      <text class=\"job-salary\">{{ job.salary }}</text>\r\n                    </view>\r\n                    <view class=\"company-info\">\r\n                      <image class=\"company-logo\" :src=\"job.companyLogo\" mode=\"aspectFit\"></image>\r\n                      <text class=\"company-name\">{{ job.companyName }}</text>\r\n                      <text class=\"apply-time\">{{ job.applyTime }}</text>\r\n                    </view>\r\n                  </view>\r\n                  \r\n                  <!-- 底部标签和状态 -->\r\n                  <view class=\"job-footer\">\r\n                    <view class=\"job-tags\">\r\n                      <text class=\"tag\" v-for=\"(tag, tagIndex) in job.tags\" :key=\"tagIndex\">\r\n                        {{ tag }}\r\n                      </text>\r\n                    </view>\r\n                    <view class=\"status-tag\" :class=\"job.status\">\r\n                      {{ getStatusText(job.status, job.statusText) }}\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </block>\r\n\r\n              <!-- 空状态 -->\r\n              <view v-else class=\"empty-state\">\r\n                <image class=\"empty-icon\" src=\"/static/icons/empty-apply.png\" mode=\"aspectFit\"></image>\r\n                <text class=\"empty-text\">暂无投递记录</text>\r\n                <button \r\n                  class=\"browse-btn\" \r\n                  @tap=\"goToJobList\"\r\n                  :style=\"{\r\n                    background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`,\r\n                    boxShadow: `0 8rpx 20rpx rgba(${t('color1rgb')}, 0.25)`\r\n                  }\"\r\n                >去浏览职位</button>\r\n              </view>\r\n\r\n              <!-- 加载状态 -->\r\n              <view v-if=\"isLoading && applyJobs.length > 0\" class=\"loading-more\">\r\n                正在加载更多...\r\n              </view>\r\n              <view v-if=\"noMoreData && applyJobs.length > 0\" class=\"no-more-data\">\r\n                没有更多数据了\r\n              </view>\r\n            </scroll-view>\r\n          </swiper-item>\r\n        </swiper>\r\n      </view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      currentTab: 'all',\r\n      swiperCurrent: 0,\r\n      applyJobs: [],\r\n      isLoading: false,\r\n      isRefreshing: false,\r\n      noMoreData: false,\r\n      page: 1,\r\n      pageSize: 10,\r\n      pre_url: app.globalData.pre_url,\r\n      statusList: ['all', '1', '2', '3', '4', '5', '6', '7']\r\n    }\r\n  },\r\n\r\n  onLoad(opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.loadApplyJobs();\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.onRefresh();\r\n  },\r\n\r\n  onShareAppMessage() {\r\n    return this._sharewx({\r\n      title: '我投递的工作',\r\n      desc: '查看投递记录',\r\n      pic: ''\r\n    });\r\n  },\r\n\r\n  onShareTimeline() {\r\n    var sharewxdata = this._sharewx({\r\n      title: '我投递的工作',\r\n      desc: '查看投递记录',\r\n      pic: ''\r\n    });\r\n    var query = (sharewxdata.path).split('?')[1];\r\n    return {\r\n      title: sharewxdata.title,\r\n      imageUrl: sharewxdata.imageUrl,\r\n      query: query\r\n    }\r\n  },\r\n\r\n  onReachBottom() {\r\n    if (!this.noMoreData && !this.isLoading) {\r\n      this.loadMore();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 切换标签\r\n    switchTab(tab) {\r\n      if (this.currentTab === tab) return;\r\n      this.currentTab = tab;\r\n      this.swiperCurrent = this.statusList.indexOf(tab);\r\n      this.page = 1;\r\n      this.noMoreData = false;\r\n      this.applyJobs = [];\r\n      this.loadApplyJobs();\r\n    },\r\n\r\n    // swiper 切换事件\r\n    swiperChange(e) {\r\n      const index = e.detail.current;\r\n      const tab = this.statusList[index];\r\n      if (this.currentTab === tab) return;\r\n      this.currentTab = tab;\r\n      this.page = 1;\r\n      this.noMoreData = false;\r\n      this.applyJobs = [];\r\n      this.loadApplyJobs();\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status, statusText) {\r\n      return statusText || '已投递';\r\n    },\r\n\r\n    // 加载投递的职位\r\n    loadApplyJobs() {\r\n      if (this.isLoading || this.noMoreData) return;\r\n      this.isLoading = true;\r\n      this.loading = true;\r\n\r\n      const params = {\r\n        page: this.page,\r\n        limit: this.pageSize\r\n      };\r\n      \r\n      // 根据当前标签筛选状态\r\n      if (this.currentTab !== 'all') {\r\n        params.status = parseInt(this.currentTab);\r\n      }\r\n\r\n      app.get('apiZhaopin/getApplyList', params, (res) => {\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.isRefreshing = false;\r\n        uni.stopPullDownRefresh();\r\n\r\n        if (res.code === 0) {\r\n          const jobList = res.data.map(item => {\r\n            // 合并所有标签\r\n            const tags = [];\r\n            if (item.options) {\r\n              try {\r\n                const options = JSON.parse(item.options);\r\n                Object.values(options).forEach(values => {\r\n                  if (Array.isArray(values)) {\r\n                    tags.push(...values);\r\n                  }\r\n                });\r\n              } catch (e) {\r\n                console.error('解析options失败:', e);\r\n              }\r\n            }\r\n            // 添加基本信息标签\r\n            if (item.education) tags.push(item.education);\r\n            if (item.experience) tags.push(item.experience);\r\n            if (item.address) tags.push(item.address);\r\n\r\n            return {\r\n              id: item.position_id,\r\n              title: item.position_name || item.title,\r\n              salary: item.salary,\r\n              companyName: item.company_name,\r\n              companyLogo: item.company_logo,\r\n              tags: tags,\r\n              applyTime: item.create_time,\r\n              status: item.status,\r\n              statusText: item.status_text\r\n            };\r\n          });\r\n\r\n          if (this.page === 1) {\r\n            this.applyJobs = jobList;\r\n          } else {\r\n            this.applyJobs = [...this.applyJobs, ...jobList];\r\n          }\r\n\r\n          this.noMoreData = jobList.length < this.pageSize || this.applyJobs.length >= res.count;\r\n          this.page++;\r\n          this.isload = true;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '加载失败，请重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 下拉刷新\r\n    async onRefresh() {\r\n      this.isRefreshing = true;\r\n      this.page = 1;\r\n      this.noMoreData = false;\r\n      await this.loadApplyJobs();\r\n    },\r\n\r\n    // 加载更多\r\n    loadMore() {\r\n      this.loadApplyJobs();\r\n    },\r\n\r\n    // 查看职位详情\r\n    viewJobDetail(jobId) {\r\n      uni.navigateTo({\r\n        url: `/zhaopin/partdetails?id=${jobId}`\r\n      });\r\n    },\r\n\r\n    // 跳转到职位列表\r\n    goToJobList() {\r\n      uni.switchTab({\r\n        url: '/zhaopin/index'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.apply-page {\r\n  min-height: 100vh;\r\n  background-color: #f7f8fa;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  \r\n  .filter-bar {\r\n    display: flex;\r\n    background-color: rgba(255, 255, 255, 0.98);\r\n    padding: 20rpx 24rpx;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 100;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\r\n    backdrop-filter: blur(10px);\r\n    overflow-x: auto;\r\n    white-space: nowrap;\r\n    scrollbar-width: none;\r\n    -webkit-overflow-scrolling: touch;\r\n    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);\r\n    \r\n    &::-webkit-scrollbar {\r\n      display: none;\r\n    }\r\n    \r\n    .filter-item {\r\n      padding: 12rpx 28rpx;\r\n      font-size: 26rpx;\r\n      color: #666;\r\n      background-color: #f8f9fa;\r\n      border-radius: 32rpx;\r\n      margin-right: 16rpx;\r\n      transition: all 0.25s ease-in-out;\r\n      position: relative;\r\n      overflow: hidden;\r\n      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);\r\n      flex-shrink: 0;\r\n      display: inline-block;\r\n      border: 1rpx solid rgba(0, 0, 0, 0.03);\r\n      \r\n      &:last-child {\r\n        margin-right: 24rpx;\r\n      }\r\n      \r\n      &:first-child {\r\n        margin-left: 4rpx;\r\n      }\r\n      \r\n      &.active {\r\n        background: v-bind('`linear-gradient(135deg, ${t(\"color1\")}, ${t(\"color1\")}dd)`');\r\n        color: #fff;\r\n        font-weight: 500;\r\n        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t(\"color1rgb\")}, 0.2)`');\r\n        border: v-bind('`1rpx solid rgba(${t(\"color1rgb\")}, 0.1)`');\r\n        transform: translateY(-1rpx);\r\n      }\r\n\r\n      &:active {\r\n        transform: scale(0.95) translateY(0);\r\n        transition: all 0.15s ease-in-out;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .job-list {\r\n    height: calc(100vh - 120rpx);\r\n    padding: 16rpx;\r\n    \r\n    .job-card {\r\n      margin: 20rpx 12rpx;\r\n      padding: 28rpx;\r\n      background-color: #fff;\r\n      border-radius: 16rpx;\r\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      border: 1rpx solid rgba(0, 0, 0, 0.02);\r\n      \r\n      &:active {\r\n        transform: scale(0.985);\r\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);\r\n      }\r\n      \r\n      .job-info {\r\n        .job-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 20rpx;\r\n          \r\n          .job-title {\r\n            font-size: 32rpx;\r\n            font-weight: 600;\r\n            color: #2c3e50;\r\n            line-height: 1.4;\r\n          }\r\n          \r\n          .job-salary {\r\n            font-size: 30rpx;\r\n            font-weight: 600;\r\n            color: #ff4d4f;\r\n            background: rgba(255, 77, 79, 0.08);\r\n            padding: 4rpx 16rpx;\r\n            border-radius: 6rpx;\r\n          }\r\n        }\r\n        \r\n        .company-info {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 24rpx;\r\n          \r\n          .company-logo {\r\n            width: 56rpx;\r\n            height: 56rpx;\r\n            border-radius: 12rpx;\r\n            margin-right: 16rpx;\r\n            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n            border: 1rpx solid rgba(0, 0, 0, 0.04);\r\n          }\r\n          \r\n          .company-name {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            margin-right: 16rpx;\r\n          }\r\n          \r\n          .apply-time {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n            background: #f8f9fa;\r\n            padding: 2rpx 12rpx;\r\n            border-radius: 4rpx;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .job-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-top: 20rpx;\r\n        \r\n        .job-tags {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          gap: 12rpx;\r\n          margin-right: 16rpx;\r\n          \r\n          .tag {\r\n            padding: 4rpx 16rpx;\r\n            background-color: #f8f9fa;\r\n            border-radius: 4rpx;\r\n            font-size: 24rpx;\r\n            color: #666;\r\n            border: 1rpx solid #eee;\r\n          }\r\n        }\r\n        \r\n        .status-tag {\r\n          padding: 6rpx 16rpx;\r\n          font-size: 24rpx;\r\n          border-radius: 4rpx;\r\n          font-weight: 500;\r\n          \r\n          // 已投递\r\n          &[class=\"1\"] {\r\n            color: #faad14;\r\n            background: rgba(250, 173, 20, 0.1);\r\n            border: 1rpx solid rgba(250, 173, 20, 0.3);\r\n          }\r\n          \r\n          // 已查看\r\n          &[class=\"2\"] {\r\n            color: v-bind('t(\"color1\")');\r\n            background: v-bind('`rgba(${t(\"color1rgb\")}, 0.1)`');\r\n            border: v-bind('`1rpx solid rgba(${t(\"color1rgb\")}, 0.3)`');\r\n          }\r\n          \r\n          // 初筛通过\r\n          &[class=\"3\"] {\r\n            color: #52c41a;\r\n            background: rgba(82, 196, 26, 0.1);\r\n            border: 1rpx solid rgba(82, 196, 26, 0.3);\r\n          }\r\n          \r\n          // 待面试\r\n          &[class=\"4\"] {\r\n            color: #722ed1;\r\n            background: rgba(114, 46, 209, 0.1);\r\n            border: 1rpx solid rgba(114, 46, 209, 0.3);\r\n          }\r\n          \r\n          // 面试通过\r\n          &[class=\"5\"] {\r\n            color: #13c2c2;\r\n            background: rgba(19, 194, 194, 0.1);\r\n            border: 1rpx solid rgba(19, 194, 194, 0.3);\r\n          }\r\n          \r\n          // 已入职\r\n          &[class=\"6\"] {\r\n            color: #389e0d;\r\n            background: rgba(56, 158, 13, 0.1);\r\n            border: 1rpx solid rgba(56, 158, 13, 0.3);\r\n          }\r\n          \r\n          // 已结束\r\n          &[class=\"7\"] {\r\n            color: #999;\r\n            background: rgba(0, 0, 0, 0.05);\r\n            border: 1rpx solid rgba(0, 0, 0, 0.1);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding-top: 240rpx;\r\n    \r\n    .empty-icon {\r\n      width: 240rpx;\r\n      height: 240rpx;\r\n      margin-bottom: 40rpx;\r\n      opacity: 0.8;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n      margin-bottom: 48rpx;\r\n    }\r\n    \r\n    .browse-btn {\r\n      padding: 24rpx 64rpx;\r\n      background: v-bind('`linear-gradient(135deg, ${t(\"color1\")}, ${t(\"color1\")}dd)`');\r\n      color: #fff;\r\n      font-size: 28rpx;\r\n      border-radius: 44rpx;\r\n      box-shadow: v-bind('`0 8rpx 20rpx rgba(${t(\"color1rgb\")}, 0.25)`');\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      border: none;\r\n      font-weight: 500;\r\n      letter-spacing: 2rpx;\r\n      \r\n      &:active {\r\n        transform: translateY(2rpx) scale(0.98);\r\n        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t(\"color1rgb\")}, 0.2)`');\r\n        background: v-bind('`linear-gradient(135deg, ${t(\"color1\")}, ${t(\"color1\")}dd)`');\r\n      }\r\n    }\r\n  }\r\n  \r\n  .loading-more,\r\n  .no-more-data {\r\n    text-align: center;\r\n    padding: 30rpx 0;\r\n    font-size: 26rpx;\r\n    color: #999;\r\n    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.02));\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myApply.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myApply.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115047024\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}