{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?1256", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?0d15", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?3f66", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?24c5", "uni-app:///pagesExb/money/cashcoupon.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?0691", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/money/cashcoupon.vue?cb56"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "inject", "data", "opt", "loading", "isload", "activeTab", "datalist", "incomeList", "pagenum", "nodata", "nomore", "currentBalance", "totalCount", "currentDate", "showDatePickerModal", "picker<PERSON><PERSON><PERSON>", "years", "months", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "initCurrentDate", "initDatePicker", "switchTab", "showDatePicker", "hideDatePicker", "onPickerChange", "confirmDatePicker", "getdata", "that", "app", "date", "uni", "title", "st", "calculateStats", "totalIncomeCount", "formatAmount", "smartFormatAmount", "decimalPart", "formatHighPrecisionAmount"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2GnxB;AAAA,eAEA;EACAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MAEAC;MACAA;MACAA;;MAEA;MACA;QACA;QACAC;UACAvB;UACAwB;QACA;UACAF;UAEA;YACA;YAEA;cACA;cACAG;gBACAC;cACA;cAEAJ;cAEA;gBACAA;cACA;;cAEA;cACA;gBACAA;gBACAA;cACA;cAEAA;YACA;cACA;gBACAA;cACA;gBACA;gBACA;gBACAA;cACA;YACA;UACA;YACAA;YACA;cACAA;YACA;UACA;QACA;MACA;QACA;QACA;;QAEAC;UACAI;UACA3B;UACAwB;QACA;UACAF;UACA;UAEA;YACA;YACAG;cACAC;YACA;YAEAJ;YAEA;cACAA;YACA;;YAEA;YACAA;YACAA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IAIA;IACAM;MACA;;MAEA;MACAnC;QACA;UACAoC;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACA;MACA;MAEA;MACA;MACA;QACAC;MACA;MAEA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;QACA;QACA;MACA;MAEA;MACA;MACA;QACAD;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnaA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/money/cashcoupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/money/cashcoupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cashcoupon.vue?vue&type=template&id=4f0bafe4&\"\nvar renderjs\nimport script from \"./cashcoupon.vue?vue&type=script&lang=js&\"\nexport * from \"./cashcoupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cashcoupon.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/money/cashcoupon.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashcoupon.vue?vue&type=template&id=4f0bafe4&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.activeTab === \"income\"\n      ? _vm.formatHighPrecisionAmount(_vm.currentBalance)\n      : null\n  var m1 =\n    _vm.isload && !(_vm.activeTab === \"income\")\n      ? _vm.smartFormatAmount(_vm.currentBalance)\n      : null\n  var m2 = _vm.isload ? _vm.t(\"消费券\") : null\n  var m3 =\n    _vm.isload && _vm.activeTab === \"income\"\n      ? _vm.formatHighPrecisionAmount(_vm.totalCount)\n      : null\n  var m4 =\n    _vm.isload && !(_vm.activeTab === \"income\")\n      ? _vm.smartFormatAmount(_vm.totalCount)\n      : null\n  var m5 = _vm.isload && _vm.activeTab === \"income\" ? _vm.t(\"消费券\") : null\n  var l0 =\n    _vm.isload && _vm.activeTab === \"income\"\n      ? _vm.__map(_vm.incomeList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.formatHighPrecisionAmount(item.total_amount)\n          return {\n            $orig: $orig,\n            m6: m6,\n          }\n        })\n      : null\n  var g0 =\n    _vm.isload && _vm.activeTab === \"income\" ? _vm.incomeList.length : null\n  var l1 =\n    _vm.isload && _vm.activeTab === \"change\"\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = _vm.t(\"消费券\")\n          var m8 = _vm.smartFormatAmount(item.after)\n          var m9 = _vm.smartFormatAmount(item.money)\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n            m9: m9,\n          }\n        })\n      : null\n  var g1 = _vm.isload && _vm.activeTab === \"change\" ? _vm.datalist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashcoupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashcoupon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<block v-if=\"isload\">\n\t\t\t<!-- 余额统计头部 -->\n\t\t\t<view class=\"header-stats\">\n\t\t\t\t<view class=\"stats-container\">\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<text class=\"stats-amount\">{{activeTab === 'income' ? formatHighPrecisionAmount(currentBalance) : smartFormatAmount(currentBalance)}}</text>\n\t\t\t\t\t\t<text class=\"stats-label\">可用{{t('消费券')}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<text class=\"stats-amount\">{{activeTab === 'income' ? formatHighPrecisionAmount(totalCount) : smartFormatAmount(totalCount)}}</text>\n\t\t\t\t\t\t<text class=\"stats-label\" v-if=\"activeTab === 'income'\">累计补贴</text>\n\t\t\t\t\t\t<text class=\"stats-label\" v-else>累计收入</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- Tab切换区域 -->\n\t\t\t<view class=\"tab-section\">\n\t\t\t\t<view class=\"tab-item\" :class=\"activeTab === 'income' ? 'active' : ''\" @tap=\"switchTab('income')\">\n\t\t\t\t\t<text class=\"tab-text\">收入明细</text>\n\t\t\t\t\t<view class=\"tab-underline\" v-if=\"activeTab === 'income'\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tab-item\" :class=\"activeTab === 'change' ? 'active' : ''\" @tap=\"switchTab('change')\">\n\t\t\t\t\t<text class=\"tab-text\">变动明细</text>\n\t\t\t\t\t<view class=\"tab-underline\" v-if=\"activeTab === 'change'\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 日期选择器 -->\n\t\t\t<view class=\"date-selector\" @tap=\"showDatePicker\">\n\t\t\t\t<text class=\"date-text\">{{currentDate}}</text>\n\t\t\t\t<image class=\"calendar-icon\" src=\"/static/img/calendar.png\" mode=\"aspectFit\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 表格头部 -->\n\t\t\t<view class=\"table-header\" v-if=\"activeTab === 'income'\">\n\t\t\t\t<text class=\"header-date\">结算日期</text>\n\t\t\t\t<text class=\"header-count\">参与订单数</text>\n\t\t\t\t<text class=\"header-amount\">收入{{t('消费券')}}</text>\n\t\t\t</view>\n\n\t\t\t<!-- 收入明细列表 -->\n\t\t\t<view class=\"table-content\" v-if=\"activeTab === 'income'\">\n\t\t\t\t<view v-for=\"(item, index) in incomeList\" :key=\"index\" class=\"table-row\">\n\t\t\t\t\t<text class=\"row-date\">{{item.settle_date}}</text>\n\t\t\t\t\t<text class=\"row-count\">{{item.order_count}}</text>\n\t\t\t\t\t<text class=\"row-amount\">{{formatHighPrecisionAmount(item.total_amount)}}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view v-if=\"incomeList.length === 0\" class=\"empty-state\">\n\t\t\t\t\t<text class=\"empty-text\">没有更多了</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 变动明细列表 -->\n\t\t\t<view class=\"change-list\" v-if=\"activeTab === 'change'\">\n\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"change-item\">\n\t\t\t\t\t<view class=\"change-info\">\n\t\t\t\t\t\t<text class=\"change-title\">{{item.remark}}</text>\n\t\t\t\t\t\t<text class=\"change-time\">{{item.createtime}}</text>\n\t\t\t\t\t\t<text class=\"change-balance\">变更后{{t('消费券')}}: {{smartFormatAmount(item.after)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"change-amount\" :class=\"item.money > 0 ? 'amount-positive' : 'amount-negative'\">\n\t\t\t\t\t\t<text class=\"amount-symbol\">{{item.money > 0 ? '+' : ''}}</text>\n\t\t\t\t\t\t<text class=\"amount-value\">{{smartFormatAmount(item.money)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view v-if=\"datalist.length === 0\" class=\"empty-state\">\n\t\t\t\t\t<text class=\"empty-text\">没有更多了</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</block>\n\t\t\n\t\t<!-- 日期选择器弹窗 -->\n\t\t<view class=\"date-picker-mask\" v-if=\"showDatePickerModal\" @tap=\"hideDatePicker\">\n\t\t\t<view class=\"date-picker-content\" @tap.stop>\n\t\t\t\t<view class=\"picker-header\">\n\t\t\t\t\t<text class=\"picker-cancel\" @tap=\"hideDatePicker\">取消</text>\n\t\t\t\t\t<text class=\"picker-title\">选择月份</text>\n\t\t\t\t\t<text class=\"picker-confirm\" @tap=\"confirmDatePicker\">确定</text>\n\t\t\t\t</view>\n\t\t\t\t<picker-view class=\"picker-view\" :value=\"pickerValue\" @change=\"onPickerChange\">\n\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t<view v-for=\"(year, index) in years\" :key=\"index\" class=\"picker-item\">{{year}}</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t<view v-for=\"(month, index) in months\" :key=\"index\" class=\"picker-item\">{{month}}</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<loading v-if=\"loading\"></loading>\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\n\texport default {\n\t\tinject: ['reload'], // 注入 reload 方法\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\topt: {},\n\t\t\t\tloading: false,\n\t\t\t\tisload: false,\n\t\t\t\t\n\t\t\t\t// Tab状态\n\t\t\t\tactiveTab: 'income', // 'income' 或 'change'\n\n\t\t\t\t// 余额数据\n\t\t\t\tdatalist: [],\n\t\t\t\tincomeList: [],\n\t\t\t\tpagenum: 1,\n\t\t\t\tnodata: false,\n\t\t\t\tnomore: false,\n\n\t\t\t\t// 统计数据\n\t\t\t\tcurrentBalance: '0.00', // 当前余额\n\t\t\t\ttotalCount: '0.00', // 累计收入\n\t\t\t\t\n\t\t\t\t// 日期选择器\n\t\t\t\tcurrentDate: '2025-07',\n\t\t\t\tshowDatePickerModal: false,\n\t\t\t\tpickerValue: [0, 6], // 年份索引，月份索引\n\t\t\t\tyears: [],\n\t\t\t\tmonths: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']\n\t\t\t};\n\t\t},\n\n\t\tonLoad: function(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.initCurrentDate();\n\t\t\tthis.initDatePicker();\n\t\t\tthis.getdata();\n\t\t},\n\t\t\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.getdata(true);\n\t\t},\n\t\t\n\t\tonReachBottom: function() {\n\t\t\tif (!this.nodata && !this.nomore) {\n\t\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\t\tthis.getdata(true);\n\t\t\t}\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\t// 初始化当前日期\n\t\t\tinitCurrentDate() {\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst year = now.getFullYear();\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0'); // getMonth()返回0-11，所以要+1\n\t\t\t\tthis.currentDate = `${year}-${month}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 初始化日期选择器数据\n\t\t\tinitDatePicker() {\n\t\t\t\tconst currentYear = new Date().getFullYear();\n\t\t\t\tconst currentMonth = new Date().getMonth(); // 0-11\n\t\t\t\t\n\t\t\t\tthis.years = [];\n\t\t\t\t// 生成最近5年的年份选项\n\t\t\t\tfor (let i = currentYear - 2; i <= currentYear + 2; i++) {\n\t\t\t\t\tthis.years.push(i.toString());\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置默认选中值，与当前日期同步\n\t\t\t\tconst yearIndex = this.years.indexOf(currentYear.toString());\n\t\t\t\tthis.pickerValue = [yearIndex, currentMonth]; // currentMonth已经是0-11，直接使用\n\t\t\t},\n\t\t\t\n\t\t\t// 切换Tab\n\t\t\tswitchTab(tab) {\n\t\t\t\tif (this.activeTab !== tab) {\n\t\t\t\t\tthis.activeTab = tab;\n\t\t\t\t\tthis.pagenum = 1;\n\t\t\t\t\tthis.getdata();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 显示日期选择器\n\t\t\tshowDatePicker() {\n\t\t\t\tthis.showDatePickerModal = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 隐藏日期选择器\n\t\t\thideDatePicker() {\n\t\t\t\tthis.showDatePickerModal = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 日期选择器值改变\n\t\t\tonPickerChange(e) {\n\t\t\t\tthis.pickerValue = e.detail.value;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认选择日期\n\t\t\tconfirmDatePicker() {\n\t\t\t\tconst yearIndex = this.pickerValue[0];\n\t\t\t\tconst monthIndex = this.pickerValue[1];\n\t\t\t\tconst selectedYear = this.years[yearIndex];\n\t\t\t\tconst selectedMonth = this.months[monthIndex]; // months数组是['01','02'...]，索引已正确\n\t\t\t\t\n\t\t\t\tthis.currentDate = `${selectedYear}-${selectedMonth}`;\n\t\t\t\tthis.hideDatePicker();\n\t\t\t\t\n\t\t\t\t// 重新获取数据\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.getdata();\n\t\t\t},\n\t\t\t\n\t\t\t// 获取数据\n\t\t\tgetdata: function(loadmore) {\n\t\t\t\tif (!loadmore) {\n\t\t\t\t\tthis.pagenum = 1;\n\t\t\t\t\tthis.datalist = [];\n\t\t\t\t\tthis.incomeList = [];\n\t\t\t\t}\n\n\t\t\t\tvar that = this;\n\t\t\t\tvar pagenum = that.pagenum;\n\n\t\t\t\tthat.nodata = false;\n\t\t\t\tthat.nomore = false;\n\t\t\t\tthat.loading = true;\n\n\t\t\t\t// 根据Tab类型调用不同接口\n\t\t\t\tif (that.activeTab === 'income') {\n\t\t\t\t\t// 收入明细：调用新的分红收入接口\n\t\t\t\t\tapp.post('ApiMoney/getIncomeDetail', {\n\t\t\t\t\t\tpagenum: pagenum,\n\t\t\t\t\t\tdate: that.currentDate // 传递选择的日期\n\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\tthat.loading = false;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (res.code == 1) {\n\t\t\t\t\t\t\tvar data = res.data;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\t\t\t// 设置页面标题\n\t\t\t\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\t\t\t\ttitle: that.t('消费券')\n\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\tthat.incomeList = data;\n\n\t\t\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 使用返回的统计数据\n\t\t\t\t\t\t\t\tif (res.stats) {\n\t\t\t\t\t\t\t\t\tthat.currentBalance = res.stats.current_balance;\n\t\t\t\t\t\t\t\t\tthat.totalCount = res.stats.total_income;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tthat.loaded();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tvar incomeList = that.incomeList;\n\t\t\t\t\t\t\t\t\tvar newlist = incomeList.concat(data);\n\t\t\t\t\t\t\t\t\tthat.incomeList = newlist;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.loaded();\n\t\t\t\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 变动明细：调用原有的moneylog接口\n\t\t\t\t\tvar st = 0; // 余额明细的st值为0\n\t\t\t\t\t\n\t\t\t\t\tapp.post('ApiMy/moneylog', {\n\t\t\t\t\t\tst: st,\n\t\t\t\t\t\tpagenum: pagenum,\n\t\t\t\t\t\tdate: that.currentDate // 传递选择的日期\n\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\tthat.loading = false;\n\t\t\t\t\t\tvar data = res.data;\n\n\t\t\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\t\t// 设置页面标题\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\t\t\ttitle: that.t('消费券')\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tthat.datalist = data;\n\n\t\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// 计算统计数据\n\t\t\t\t\t\t\tthat.calculateStats(data);\n\t\t\t\t\t\t\tthat.loaded();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tvar datalist = that.datalist;\n\t\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\n\t\t\t\t\t\t\t\tthat.datalist = newdata;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\n\t\t\t\n\t\t\t// 计算统计数据\n\t\t\tcalculateStats(data) {\n\t\t\t\tlet totalIncomeCount = 0; // 只计算正数收入的累计\n\n\t\t\t\t// 累计收入：只计算正数金额\n\t\t\t\tdata.forEach(item => {\n\t\t\t\t\tif (item.money > 0) {\n\t\t\t\t\t\ttotalIncomeCount += parseFloat(item.money);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// 当前余额：使用最新记录的after字段（最准确的当前余额）\n\t\t\t\tif (data.length > 0) {\n\t\t\t\t\tthis.currentBalance = parseFloat(data[0].after || 0).toFixed(2);\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentBalance = '0.00';\n\t\t\t\t}\n\n\t\t\t\tthis.totalCount = totalIncomeCount.toFixed(2); // 累计收入只显示正数总和\n\t\t\t},\n\t\t\t\n\t\t\t// 智能格式化金额显示：如果后两位是00则不显示\n\t\t\tformatAmount(amount) {\n\t\t\t\tconst num = parseFloat(amount);\n\t\t\t\t// 检查是否为整数或只有1-2位有效小数\n\t\t\t\tif (num % 1 === 0) {\n\t\t\t\t\t// 整数，显示2位小数\n\t\t\t\t\treturn num.toFixed(2);\n\t\t\t\t} else if ((num * 100) % 1 === 0) {\n\t\t\t\t\t// 2位小数，直接显示\n\t\t\t\t\treturn num.toFixed(2);\n\t\t\t\t} else {\n\t\t\t\t\t// 超过2位小数，显示所有小数位但去掉末尾的0\n\t\t\t\t\treturn parseFloat(num.toFixed(5)).toString();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 智能格式化金额：去掉末尾无意义的0，但至少保留2位小数\n\t\t\tsmartFormatAmount(amount) {\n\t\t\t\tif (!amount && amount !== 0) return '0.00';\n\t\t\t\t\n\t\t\t\tconst num = parseFloat(amount);\n\t\t\t\tif (isNaN(num)) return '0.00';\n\t\t\t\t\n\t\t\t\t// 首先格式化到5位小数\n\t\t\t\tlet formatted = num.toFixed(5);\n\t\t\t\t\n\t\t\t\t// 去掉末尾的0，但至少保留2位小数\n\t\t\t\t// 例如：0.45000 -> 0.45, 0.45600 -> 0.456, 1.00000 -> 1.00\n\t\t\t\t\n\t\t\t\t// 分离整数和小数部分\n\t\t\t\tconst parts = formatted.split('.');\n\t\t\t\tif (parts.length === 1) {\n\t\t\t\t\t// 没有小数部分，添加.00\n\t\t\t\t\treturn parts[0] + '.00';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tlet decimalPart = parts[1];\n\t\t\t\t// 从右边去掉0，但至少保留2位\n\t\t\t\twhile (decimalPart.length > 2 && decimalPart.charAt(decimalPart.length - 1) === '0') {\n\t\t\t\t\tdecimalPart = decimalPart.slice(0, -1);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn parts[0] + '.' + decimalPart;\n\t\t\t},\n\t\t\t\n\t\t\t// 高精度格式化金额：专用于收入明细，智能显示小数位\n\t\t\tformatHighPrecisionAmount(amount) {\n\t\t\t\tif (!amount && amount !== 0) return '0.00';\n\t\t\t\t\n\t\t\t\tconst num = parseFloat(amount);\n\t\t\t\tif (isNaN(num)) return '0.00';\n\t\t\t\t\n\t\t\t\t// 先格式化到5位小数\n\t\t\t\tlet formatted = num.toFixed(5);\n\t\t\t\t\n\t\t\t\t// 智能去掉末尾的0，但至少保留2位小数\n\t\t\t\t// 例如：6.23000 -> 6.23, 6.23400 -> 6.234, 6.00000 -> 6.00\n\t\t\t\tconst parts = formatted.split('.');\n\t\t\t\tif (parts.length === 1) {\n\t\t\t\t\t// 没有小数部分，添加.00\n\t\t\t\t\treturn parts[0] + '.00';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tlet decimalPart = parts[1];\n\t\t\t\t// 从右边去掉0，但至少保留2位\n\t\t\t\twhile (decimalPart.length > 2 && decimalPart.charAt(decimalPart.length - 1) === '0') {\n\t\t\t\t\tdecimalPart = decimalPart.slice(0, -1);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn parts[0] + '.' + decimalPart;\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t\tbackground: linear-gradient(180deg, #6C7CE7 0%, #F5F6FA 40%);\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 20rpx;\n\t}\n\n\t/* 头部余额统计区域 */\n\t.header-stats {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 60rpx 40rpx 40rpx;\n\t\tbackground: transparent;\n\t}\n\n\t.stats-container {\n\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx 40rpx;\n\t\tmargin: 0 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tbox-shadow: 0 4px 12px #f8f8f8;\n\t}\n\n\t.stats-item {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t}\n\n\t.stats-amount {\n\t\tdisplay: block;\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #6C7CE7;\n\t\tline-height: 1.2;\n\t}\n\n\t.stats-label {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t/* Tab切换区域 */\n\t.tab-section {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 0 40rpx 20rpx;\n\t}\n\n\t.tab-item {\n\t\tposition: relative;\n\t}\n\n\t.tab-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\tfont-weight: 500;\n\t}\n\n\t.tab-item.active .tab-text {\n\t\tcolor: #fff;\n\t\tfont-weight: bold;\n\t}\n\n\t.tab-underline {\n\t\tposition: absolute;\n\t\tbottom: -10rpx;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\twidth: 60rpx;\n\t\theight: 4rpx;\n\t\tbackground: #fff;\n\t\tborder-radius: 2rpx;\n\t}\n\n\t/* 日期选择器 */\n\t.date-selector {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: 0 40rpx 30rpx;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder-radius: 30rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tcursor: pointer;\n\t}\n\n\t.date-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #fff;\n\t\tmargin-right: 16rpx;\n\t}\n\n\t.calendar-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t/* 表格头部 */\n\t.table-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\tpadding: 20rpx 40rpx;\n\t\tmargin: 0 40rpx;\n\t\tborder-radius: 8rpx 8rpx 0 0;\n\t}\n\n\t.header-date,\n\t.header-count,\n\t.header-amount {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tfont-size: 26rpx;\n\t\tcolor: #fff;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 表格内容 */\n\t.table-content {\n\t\tbackground: #fff;\n\t\tmargin: 0 40rpx;\n\t\tborder-radius: 0 0 8rpx 8rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.table-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 24rpx 20rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.table-row:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.row-date,\n\t.row-count,\n\t.row-amount {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.row-amount {\n\t\tcolor: #6C7CE7;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 余额变动明细列表 */\n\t.change-list {\n\t\tpadding: 0 40rpx;\n\t}\n\n\t.change-item {\n\t\tbackground: #fff;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 16rpx;\n\t\tpadding: 24rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n\t}\n\n\t.change-info {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.change-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.change-time {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 6rpx;\n\t}\n\n\t.change-balance {\n\t\tfont-size: 22rpx;\n\t\tcolor: #666;\n\t}\n\n\t.change-amount {\n\t\tdisplay: flex;\n\t\talign-items: baseline;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.amount-symbol {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.amount-value {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.amount-positive {\n\t\tcolor: #52C41A;\n\t}\n\n\t.amount-negative {\n\t\tcolor: #FF4D4F;\n\t}\n\n\t/* 日期选择器弹窗 */\n\t.date-picker-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tz-index: 1000;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t}\n\n\t.date-picker-content {\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx 16rpx 0 0;\n\t\tpadding-bottom: 40rpx;\n\t}\n\n\t.picker-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.picker-cancel,\n\t.picker-confirm {\n\t\tfont-size: 28rpx;\n\t\tcolor: #6C7CE7;\n\t}\n\n\t.picker-title {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t}\n\n\t.picker-view {\n\t\theight: 400rpx;\n\t\twidth: 100%;\n\t}\n\n\t.picker-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t/* 空状态 */\n\t.empty-state {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 80rpx 40rpx;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t/* 响应式调整 */\n\t@media screen and (max-width: 375px) {\n\t\t.stats-container {\n\t\t\tpadding: 24rpx 30rpx;\n\t\t\tmargin: 0 10rpx;\n\t\t\twidth: 100%;\n\t\t}\n\t\t\n\t\t.stats-item {\n\t\t\tflex: 1;\n\t\t}\n\t\t\n\t\t.stats-amount {\n\t\t\tfont-size: 40rpx;\n\t\t}\n\t\t\n\t\t\n\t}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashcoupon.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashcoupon.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024611\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}