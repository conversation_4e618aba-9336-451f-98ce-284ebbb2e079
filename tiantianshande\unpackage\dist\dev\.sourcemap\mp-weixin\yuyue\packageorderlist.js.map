{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?34c7", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?4702", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?a3ad", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?8382", "uni-app:///yuyue/packageorderlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?8f23", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderlist.vue?e9d3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "themeColor", "tabs", "name", "status", "currentTab", "orderList", "page", "limit", "total", "loading", "nodata", "triggered", "$baseUrl", "onLoad", "console", "initialStatus", "uni", "title", "onReachBottom", "onPullDownRefresh", "methods", "switchTab", "getOrderList", "that", "app", "order", "payTime", "payTimeStamp", "validDays", "newExpiresTime", "currentTime", "String", "filteredData", "gotoDetail", "gotoPay", "payorderid", "orderid", "type", "success", "fail", "cancelOrder", "order_id", "deleteOrder", "applyRefund", "canUse", "canRefund", "getStatusColor", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,gxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwDzxB;EACAC;IACA;IACA;MACAC;MAAA;MACAC,OACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;MAAA;MACA;QAAAD;QAAAC;MAAA;MAAA;MACA;QAAAD;QAAAC;MAAA;MAAA,CACA;;MACAC;MACAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;MACA;MACA;MACAC;IACA;;IAEA;IACA;IACA;MACAC;IACA;;IAEA;IACA;MAAA;IAAA;IACA;IACA;IAEA;IACAC;MACAC;IACA;IAEAH;EACA;EACA;EACA;EACA;EACA;EACAI;IACA;MACA;IACA;IACA;IACA;EACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MACAH;MACA;IACA;EACA;EACAI;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;QACA;QACA;MACA;MACAC;MACA;QACAA;QACAA;QACAA;MACA;MAEA;MACA;QACAT;QACAS;QACA;QACA;MACA;;MAEA;MACA;MAEAC;QACAlB;QACAC;QACAJ;MACA;QACAoB;QACA;UACA;;UAEA;UACA;YACA;YACA;cACA;cACA;;cAEA;cACA,0BACAE,4BACAA;;cAEA;cACA;gBACAX;;gBAEA;gBACA;gBACA;gBACAW;gBAEAX;kBACAY;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;;cAEA;cACA;gBACA;gBACAL,6DACAM,2DACAA;cACA;;cAEA;cACA;gBACAN;gBACAX;cACA;gBACAW;cACA;gBACAA;cACA;YACA;;YAEA;YACA;cACAA;YACA;;YAEA;YACA;cACAA;YACA;YAEA;UACA;;UAEA;UACA;;UAEA;UACA;YACAO;cAAA,OACAP,uBACAA;YAAA,EACA;YACAX;UACA;UACA;UAAA,KACA;YACAkB;cAAA,OACAP,sBACAA,6BACAA;YAAA,EACA;YACAX;UACA;UAEA;YACAS;UACA;YACAA;UACA;UACA;UACA;YACAA;YACA;cACAA;YACA;UACA;YACAA;YACA;cACAA;YACA;UACA;UAEAT;QACA;UACAU;UACA;QACA;QACA;MACA;QACAD;QACA;QACA;QACAC;MACA;IACA;IACAS;MACA;MACA;MACAT;IACA;IACAU;MAAA;MACA;QACA;QACAV;QACA;MACA;MACA;MACAA;QACAW;QACAC;QACAC;QACAC;UACA;UACAd;UACA;QACA;QACAe;UACAf;QACA;MACA;IACA;IACAgB;MACA;MACA;MACAhB;QACAA;QACAA;UAAAiB;QAAA;UACAjB;UACA;YACAA;YACA;YACA;cACAD;YACA;cACAA;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACA;MACAlB;QACAA;QACAA;UAAAiB;QAAA;UACAjB;UACA;YACAA;YACAD;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACAnB;IACA;IACAoB;MACA;MACA;MACA,6BACAnB,6BACAA,+BACAA;IACA;IACAoB;MACA;MACA;MACA;MACA,6BACApB,+BACAA,gCACA;IACA;IACAqB;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnZA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,ikCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packageorderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packageorderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packageorderlist.vue?vue&type=template&id=16dc5016&\"\nvar renderjs\nimport script from \"./packageorderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./packageorderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packageorderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packageorderlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderlist.vue?vue&type=template&id=16dc5016&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.orderList, function (order, index) {\n          var $orig = _vm.__get_orig(order)\n          var m0 = _vm.getStatusColor(order.status)\n          var m1 = _vm.canUse(order)\n          var m2 = _vm.canRefund(order)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderlist.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- Tab切换 -->\n\t\t<view class=\"tabs\">\n\t\t\t<view v-for=\"(tab, index) in tabs\" :key=\"index\"\n\t\t\t\tclass=\"tab-item\" :class=\"{active: currentTab === index}\"\n\t\t\t\t:style=\"currentTab === index && themeColor ? 'color:' + themeColor + ';border-bottom-color:' + themeColor : ''\"\n\t\t\t\t@tap=\"switchTab(index)\">\n\t\t\t\t{{ tab.name }}\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单列表 -->\n\t\t<view class=\"order-list\">\n\t\t\t<view v-if=\"orderList.length > 0\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(order, index) in orderList\" :key=\"index\" @tap=\"gotoDetail(order.id)\">\n\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t<text class=\"order-time\">{{ order.createtime }}</text>\n\t\t\t\t\t\t<text class=\"order-status\" :style=\"{color: getStatusColor(order.status)}\">{{ order.status_text }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<image class=\"package-pic-list\" :src=\"order.package_pic\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"package-info-list\">\n\t\t\t\t\t\t\t<view class=\"package-name-list\">{{ order.package_name }}</view>\n\t\t\t\t\t\t\t<view class=\"package-services\">\n\t\t\t\t\t\t\t\t<text>总次数: {{ order.total_services }}</text>\n\t\t\t\t\t\t\t\t<text class=\"remain\">剩余: {{ order.remain_services }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"package-expire\" v-if=\"order.expires_time_format\">有效期至: {{ order.expires_time_format }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-footer\">\n\t\t\t\t\t\t<view class=\"total-price-list\">实付: <text :style=\"{color: themeColor}\">￥{{ order.total_price }}</text></view>\n\t\t\t\t\t\t<view class=\"item-actions\">\n\t\t\t\t\t\t\t<button v-if=\"order.status === 0\" class=\"action-btn plain\" @tap.stop=\"cancelOrder(order.id, index)\">取消订单</button>\n\t\t\t\t\t\t\t<button v-if=\"order.status === 0\" class=\"action-btn primary\" :style=\"{background: themeColor}\" @tap.stop=\"gotoPay(order.payorderid, order.id)\">去支付</button>\n\t\t\t\t\t\t\t<button v-if=\"canUse(order)\" class=\"action-btn primary\" :style=\"{background: themeColor}\" @tap.stop=\"gotoDetail(order.id)\">去使用</button>\n\t\t\t\t\t\t\t<button v-if=\"canRefund(order)\" class=\"action-btn plain\" @tap.stop=\"applyRefund(order.id)\">申请退款</button>\n\t\t\t\t\t\t\t<!-- <button v-if=\"order.status === 3\" class=\"action-btn plain disabled\">已完成</button>\n\t\t\t\t\t\t\t<button v-if=\"order.status === 4\" class=\"action-btn plain disabled\">退款中</button> -->\n\t\t\t\t\t\t\t<button v-if=\"order.status === 2 || order.status > 4\" class=\"action-btn plain\" @tap.stop=\"deleteOrder(order.id, index)\">删除</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"loading-tip\" v-if=\"loading\">加载中...</view>\n\t\t\t\t<view class=\"nodata-tip\" v-if=\"nodata\">没有更多订单了~</view>\n\t\t\t</view>\n\t\t\t<view class=\"empty-tip list-empty\" v-else-if=\"!loading\">\n\t\t\t\t<image src=\"/static/img/nodata.png\" class=\"empty-img\" mode=\"widthFix\"></image>\n\t\t\t\t<text>暂无相关订单</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\t// 避免直接引用可能未初始化的全局app对象\n\t\treturn {\n\t\t\tthemeColor: '', // 主题色，将在onLoad中正确初始化\n\t\t\ttabs: [\n\t\t\t\t{ name: '全部', status: -1 },\n\t\t\t\t{ name: '待支付', status: 0 },\n\t\t\t\t{ name: '可使用', status: 1 }, // 接口定义1为已支付，包含多种子状态，前端统称可使用\n\t\t\t\t{ name: '已用完', status: 100 }, // 自定义状态码，用于前端标识已用完的套餐\n\t\t\t\t{ name: '已失效', status: 2 } // 接口定义2为已取消/已失效/已退款等终结状态\n\t\t\t],\n\t\t\tcurrentTab: 0,\n\t\t\tstatus: -1, // 默认请求全部\n\t\t\torderList: [],\n\t\t\tpage: 1,\n\t\t\tlimit: 10,\n\t\t\ttotal: 0,\n\t\t\tloading: false,\n\t\t\tnodata: false,\n\t\t\ttriggered: false, // 下拉刷新状态\n\t\t\t$baseUrl: '' // 添加 $baseUrl 属性\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 初始化全局app对象\n\t\tconst app = getApp();\n\t\tif (app && app.globalData && app.globalData.config && app.globalData.config.t) {\n\t\t\tthis.themeColor = app.globalData.config.t('color1');\n\t\t} else {\n\t\t\t// 如果未能获取主题色，设置一个默认值\n\t\t\tthis.themeColor = '#2979ff';\n\t\t\tconsole.warn('2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_001] 无法获取主题色配置，使用默认值');\n\t\t}\n\t\t\n\t\t// 根据传入的 status 定位到对应的 tab\n\t\tlet initialStatus = -1;\n\t\tif (options && options.status !== undefined) {\n\t\t\tinitialStatus = parseInt(options.status);\n\t\t}\n\t\t\n\t\t// 使用已在data初始化的tabs数组\n\t\tconst initialTabIndex = this.tabs.findIndex(tab => tab.status === initialStatus);\n\t\tthis.currentTab = initialTabIndex >= 0 ? initialTabIndex : 0;\n\t\tthis.status = this.tabs[this.currentTab].status;\n\n\t\tthis.getOrderList(true);\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: '我的套餐订单'\n\t\t});\n\t\t\n\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_002] 组件初始化完成，当前状态:', this.status);\n\t},\n\t// onShow() {\n\t// \t// 如果需要每次进入都刷新\n\t// \tthis.getOrderList(true);\n\t// },\n\tonReachBottom() {\n\t\tif (this.loading || this.nodata) {\n\t\t\treturn;\n\t\t}\n\t\tthis.page++;\n\t\tthis.getOrderList();\n\t},\n\t// uni-app下拉刷新\n\tonPullDownRefresh(){\n\t\tif(this.triggered) return;\n\t\tthis.triggered = true;\n\t\tthis.page = 1;\n\t\tthis.nodata = false;\n\t\tthis.getOrderList(true, () => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t\tthis.triggered = false;\n\t\t});\n\t},\n\tmethods: {\n\t\tswitchTab(index) {\n\t\t\tif (this.currentTab === index) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.currentTab = index;\n\t\t\tthis.status = this.tabs[index].status;\n\t\t\tthis.page = 1;\n\t\t\tthis.nodata = false;\n\t\t\tthis.orderList = []; // 清空列表\n\t\t\tthis.getOrderList(true);\n\t\t},\n\t\tgetOrderList(reload = false, callback) {\n\t\t\tvar that = this;\n\t\t\tif (that.loading || (that.nodata && !reload)) {\n\t\t\t\tif (callback) callback();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.loading = true;\n\t\t\tif (reload) {\n\t\t\t\tthat.page = 1;\n\t\t\t\tthat.orderList = [];\n\t\t\t\tthat.nodata = false;\n\t\t\t}\n\n\t\t\tconst app = getApp();\n\t\t\tif (!app || !app.post) {\n\t\t\t\tconsole.error('2025-01-03 22:55:53,565-ERROR-[packageorderlist][getOrderList_001] app对象未初始化或post方法不存在');\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (callback) callback();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 针对\"已用完\"特殊状态，需要请求已支付的订单后再过滤\n\t\t\tconst requestStatus = that.status === 100 ? 1 : that.status;\n\t\t\t\n\t\t\tapp.post('ApiYuyuePackage/getUserPackages', {\n\t\t\t\tpage: that.page,\n\t\t\t\tlimit: that.limit,\n\t\t\t\tstatus: requestStatus\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tconst data = res.data || [];\n\t\t\t\t\t\n\t\t\t\t\t// 处理每条订单数据，修复过期时间计算问题\n\t\t\t\t\tconst processedData = data.map(order => {\n\t\t\t\t\t\t// 对已支付状态的订单(status=1)处理\n\t\t\t\t\t\tif (order.status === 1) {\n\t\t\t\t\t\t\t// 获取当前时间戳（秒）\n\t\t\t\t\t\t\tconst now = Math.floor(Date.now()/1000);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 检查expires_time是否明显错误（为0，或者小于now但订单状态是1，或者日期太早如1971年）\n\t\t\t\t\t\t\tconst isExpireTimeInvalid = \n\t\t\t\t\t\t\t\torder.expires_time === 0 || \n\t\t\t\t\t\t\t\t(order.expires_time < now && order.expires_time < 1672531200); // 2023-01-01的时间戳\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 支付后但expires_time无效的情况，说明后端未设置过期时间或计算错误\n\t\t\t\t\t\t\tif (isExpireTimeInvalid && order.pay_time) {\n\t\t\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_004] 订单' + order.id + '检测到无效的过期时间:', order.expires_time);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 从支付时间计算过期时间\n\t\t\t\t\t\t\t\tconst payTimeStamp = new Date(order.pay_time.replace(/-/g, '/')).getTime();\n\t\t\t\t\t\t\t\tconst validDays = order.valid_days || 365; // 默认365天\n\t\t\t\t\t\t\t\torder.expires_time = Math.floor(payTimeStamp/1000) + (validDays * 86400);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_005] 订单' + order.id + '自动计算新的过期时间:', {\n\t\t\t\t\t\t\t\t\tpayTime: order.pay_time, \n\t\t\t\t\t\t\t\t\tpayTimeStamp: Math.floor(payTimeStamp/1000),\n\t\t\t\t\t\t\t\t\tvalidDays: validDays,\n\t\t\t\t\t\t\t\t\tnewExpiresTime: order.expires_time,\n\t\t\t\t\t\t\t\t\tcurrentTime: now\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 格式化过期时间显示(独立于前面的逻辑，确保始终有格式化显示)\n\t\t\t\t\t\t\tif (order.expires_time > 0) {\n\t\t\t\t\t\t\t\tconst expireDate = new Date(order.expires_time * 1000);\n\t\t\t\t\t\t\t\torder.expires_time_format = expireDate.getFullYear() + '-' + \n\t\t\t\t\t\t\t\t\tString(expireDate.getMonth() + 1).padStart(2, '0') + '-' + \n\t\t\t\t\t\t\t\t\tString(expireDate.getDate()).padStart(2, '0');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 重新判断状态文本\n\t\t\t\t\t\t\tif (order.expires_time > 0 && now > order.expires_time) {\n\t\t\t\t\t\t\t\torder.status_text = '已过期';\n\t\t\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_006] 订单' + order.id + '已过期，当前时间:', now, '过期时间:', order.expires_time);\n\t\t\t\t\t\t\t} else if (order.remain_services <= 0) {\n\t\t\t\t\t\t\t\torder.status_text = '已用完';\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\torder.status_text = '可使用';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 修复图片URL\n\t\t\t\t\t\tif (order.package_pic && order.package_pic.indexOf('https://localhost') === 0) {\n\t\t\t\t\t\t\torder.package_pic = order.package_pic.replace('https://localhost', '');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 双重URL问题修复 (添加这一段以与详情页保持一致)\n\t\t\t\t\t\tif (order.package_pic && order.package_pic.indexOf('https://localhosthttps://') === 0) {\n\t\t\t\t\t\t\torder.package_pic = order.package_pic.replace('https://localhost', '');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn order;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 根据不同标签进行数据过滤\n\t\t\t\t\tlet filteredData = processedData;\n\t\t\t\t\t\n\t\t\t\t\t// \"已用完\"标签：只保留已用完的订单\n\t\t\t\t\tif (that.status === 100) {\n\t\t\t\t\t\tfilteredData = processedData.filter(order => \n\t\t\t\t\t\t\torder.status === 1 && \n\t\t\t\t\t\t\t(order.remain_services <= 0 || order.status_text === '已用完')\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_007] 过滤已用完套餐，原数量:', processedData.length, '过滤后数量:', filteredData.length);\n\t\t\t\t\t}\n\t\t\t\t\t// \"可使用\"标签：只保留真正可用的订单（剩余次数>0且未过期）\n\t\t\t\t\telse if (that.status === 1) {\n\t\t\t\t\t\tfilteredData = processedData.filter(order => \n\t\t\t\t\t\t\torder.status === 1 && \n\t\t\t\t\t\t\torder.remain_services > 0 && \n\t\t\t\t\t\t\torder.status_text === '可使用'\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_008] 过滤真正可使用套餐，原数量:', processedData.length, '过滤后数量:', filteredData.length);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (that.page === 1) {\n\t\t\t\t\t\tthat.orderList = filteredData;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.orderList = that.orderList.concat(filteredData);\n\t\t\t\t\t}\n\t\t\t\t\t// 对于需要前端过滤的状态（已用完、可使用），total应该是过滤后的总数\n\t\t\t\t\tif (that.status === 100 || that.status === 1) {\n\t\t\t\t\t\tthat.total = filteredData.length;\n\t\t\t\t\t\tif (filteredData.length < that.limit) {\n\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.total = res.total || 0;\n\t\t\t\t\t\tif (data.length < that.limit || that.orderList.length >= that.total) {\n\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_003] 订单列表数据处理完成', processedData);\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '获取订单列表失败');\n\t\t\t\t\tif (that.page > 1) that.page--;\n\t\t\t\t}\n\t\t\t\tif (callback) callback();\n\t\t\t}, function() {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (that.page > 1) that.page--;\n\t\t\t\tif (callback) callback();\n\t\t\t\tapp.error('请求失败');\n\t\t\t});\n\t\t},\n\t\tgotoDetail(orderId) {\n\t\t\tif (!orderId) return;\n\t\t\tconst app = getApp();\n\t\t\tapp.goto('/yuyue/packageorderdetail?order_id=' + orderId);\n\t\t},\n\t\tgotoPay(payorderid, orderid){\n\t\t\tif(!payorderid) {\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.error('支付单号不存在');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst app = getApp();\n\t\t\tapp.payorder({\n\t\t\t\tpayorderid: payorderid,\n\t\t\t\torderid: orderid,\n\t\t\t\ttype: 'yuyue_package',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\t// 支付成功，刷新当前列表\n\t\t\t\t\tapp.success('支付成功');\n\t\t\t\t\tthis.getOrderList(true);\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tapp.error('支付失败或取消');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tcancelOrder(orderId, index){\n\t\t\tvar that = this;\n\t\t\tconst app = getApp();\n\t\t\tapp.confirm('确定要取消该订单吗？', function(){\n\t\t\t\tapp.showLoading('处理中...');\n\t\t\t\tapp.post('ApiYuyuePackage/cancelOrder', {order_id: orderId}, function(res){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(res.status == 1){\n\t\t\t\t\t\tapp.success('取消成功');\n\t\t\t\t\t\t// 刷新列表或直接修改状态\n\t\t\t\t\t\tif (that.status === -1 || that.status === 0) {\n\t\t\t\t\t\t\tthat.getOrderList(true);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.orderList.splice(index, 1);\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(res.msg || '取消失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tdeleteOrder(orderId, index){\n\t\t\t// 注意：接口文档未提供删除接口，这里假设有 ApiYuyuePackage/deleteOrder\n\t\t\tvar that = this;\n\t\t\tconst app = getApp();\n\t\t\tapp.confirm('确定要删除该订单吗？删除后不可恢复。', function(){\n\t\t\t\tapp.showLoading('处理中...');\n\t\t\t\tapp.post('ApiYuyuePackage/deleteOrder', {order_id: orderId}, function(res){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(res.status == 1){\n\t\t\t\t\t\tapp.success('删除成功');\n\t\t\t\t\t\tthat.orderList.splice(index, 1);\n\t\t\t\t\t\tif(that.orderList.length === 0 && that.page > 1) that.page--; // 如果删空了且不在第一页，尝试回退页码\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(res.msg || '删除失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tapplyRefund(orderId){\n\t\t\t// 跳转到退款申请页或直接在此处理\n\t\t\t// 接口文档提供了获取退款金额和提交退款的接口\n\t\t\t// 为简化，这里直接跳转到详情页，在详情页处理退款\n\t\t\tthis.gotoDetail(orderId);\n\t\t\tconst app = getApp();\n\t\t\tapp.toast('请在订单详情页申请退款');\n\t\t},\n\t\tcanUse(order){\n\t\t\t// 必须是已支付状态(status=1)、有剩余次数、未过期\n\t\t\t// 注意：我们现在接收整个order对象作为参数，而不仅仅是status\n\t\t\treturn order.status === 1 \n\t\t\t\t&& order.remain_services > 0 \n\t\t\t\t&& order.status_text !== '已用完'\n\t\t\t\t&& order.status_text !== '已过期';\n\t\t},\n\t\tcanRefund(order){\n\t\t\t// 必须是已支付状态(status=1)\n\t\t\t// 并且状态文本不是 '已用完' 和 '已过期'\n\t\t\t// 增加对 order.refund_status 的判断，如果已经申请过退款，则不显示\n\t\t\treturn order.status === 1 \n\t\t\t\t&& order.status_text !== '已用完' \n\t\t\t\t&& order.status_text !== '已过期'\n\t\t\t\t&& (!order.refund_status || order.refund_status === 0); // 未申请退款或申请状态为0\n\t\t},\n\t\tgetStatusColor(status){\n\t\t\tswitch(status){\n\t\t\t\tcase 0: return '#FF9900'; // 待支付 - 橙色\n\t\t\t\tcase 1: return this.themeColor || '#2979ff'; // 可使用 - 主题色\n\t\t\t\tcase 2: return '#999999'; // 已取消/失效 - 灰色\n\t\t\t\tcase 3: return '#4CAF50'; // 已完成(如果后端有此状态) - 绿色\n\t\t\t\tcase 4: return '#FF9900'; // 退款中(如果后端有此状态) - 橙色\n\t\t\t\tcase 5: return '#999999'; // 已退款(如果后端有此状态) - 灰色\n\t\t\t\tdefault: return '#666666';\n\t\t\t}\n\t\t},\n\t\t// 格式化时间戳为可读时间\n\t\tformatTime(timestamp) {\n\t\t\tif (!timestamp) return '';\n\t\t\tconst date = new Date(timestamp * 1000);\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\n\t\t\tconst day = date.getDate().toString().padStart(2, '0');\n\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\n\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}`;\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* Tabs */\n.tabs {\n\tdisplay: flex;\n\tbackground-color: #fff;\n\theight: 80rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n\tposition: sticky;\n\ttop: 0; /* 吸顶效果 */\n\t/* #ifdef H5 */\n\ttop: var(--window-top); /* H5需要考虑顶部导航栏 */\n\t/* #endif */\n\tz-index: 99;\n}\n\n.tab-item {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tposition: relative;\n\tborder-bottom: 4rpx solid transparent; /* 底部边框占位 */\n\ttransition: color 0.3s, border-bottom-color 0.3s;\n}\n\n.tab-item.active {\n\tcolor: #333; /* 激活时文字颜色加深 */\n\tfont-weight: bold;\n\tborder-bottom-width: 4rpx;\n\tborder-bottom-style: solid;\n}\n\n/* Order List */\n.order-list {\n\tflex: 1;\n\tpadding: 20rpx;\n}\n\n.order-item {\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 25rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);\n}\n\n.item-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tpadding-bottom: 15rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.order-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.order-status {\n\tfont-size: 26rpx;\n\tfont-weight: bold;\n}\n\n.item-content {\n\tdisplay: flex;\n\tmargin-bottom: 20rpx;\n}\n\n.package-pic-list {\n\twidth: 140rpx;\n\theight: 140rpx;\n\tborder-radius: 8rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.package-info-list {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\toverflow: hidden;\n}\n\n.package-name-list {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.package-services {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-bottom: 10rpx;\n}\n.package-services .remain {\n\tmargin-left: 20rpx;\n\tcolor: #ff9900;\n\tfont-weight: bold;\n}\n\n.package-expire {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.item-footer {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n\tmargin-top: 15rpx;\n\tpadding-top: 15rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.total-price-list {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tmargin-bottom: 15rpx;\n}\n.total-price-list text {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n}\n\n.item-actions {\n\tdisplay: flex;\n\tgap: 15rpx; /* 按钮间距 */\n}\n\n.action-btn {\n\tpadding: 0 24rpx;\n\theight: 56rpx;\n\tline-height: 56rpx;\n\tfont-size: 24rpx;\n\tborder-radius: 28rpx;\n\tmargin: 0; /* 去除默认边距 */\n\tborder: 1rpx solid #ccc;\n\tbackground-color: #fff;\n\tcolor: #666;\n}\nbutton::after {\n    border: none;\n}\n\n.action-btn.primary {\n\tcolor: #fff;\n\tborder: none;\n}\n\n.action-btn.plain {\n\t/* 可自定义朴素按钮样式 */\n}\n.action-btn.disabled {\n    background-color: #f5f5f5;\n    color: #ccc;\n    border-color: #eee;\n}\n\n/* 加载和空状态 */\n.loading-tip, .nodata-tip {\n\ttext-align: center;\n\tcolor: #999;\n\tfont-size: 24rpx;\n\tpadding: 20rpx 0;\n}\n\n.empty-tip.list-empty {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding-top: 150rpx;\n\tflex: 1; /* 占据剩余空间 */\n}\n\n.empty-img {\n\twidth: 200rpx;\n\tmargin-bottom: 20rpx;\n}\n.empty-tip text {\n\tcolor: #999;\n\tfont-size: 26rpx;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041148\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}