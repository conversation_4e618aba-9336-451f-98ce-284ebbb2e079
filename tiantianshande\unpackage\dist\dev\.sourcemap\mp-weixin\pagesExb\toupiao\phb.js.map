{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?1130", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?9d06", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?4a62", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?680d", "uni-app:///pagesExb/toupiao/phb.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?fa93", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/phb.vue?8865"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "pagenum", "nomore", "nodata", "info", "nowtime", "djsday", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "title", "desc", "link", "pic", "interval", "getdjs", "getmore"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsC5wB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAjB;MAAA;QACAe;QACA;UACAA;UACA;YACAA;UACA;UAEAA;UACAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAA;YAAAG;YAAAC;YAAAC;YAAAC;UAAA;UAEAT;UACAU;YACAP;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;QACA;MACA;QACA;MACA;MACA;QACAR;QACAA;QACAA;QACAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAS;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/toupiao/phb.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/toupiao/phb.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./phb.vue?vue&type=template&id=2ad37ac6&\"\nvar renderjs\nimport script from \"./phb.vue?vue&type=script&lang=js&\"\nexport * from \"./phb.vue?vue&type=script&lang=js&\"\nimport style0 from \"./phb.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/toupiao/phb.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phb.vue?vue&type=template&id=2ad37ac6&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phb.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phb.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" :style=\"{background:info.color1}\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"banner\"><image :src=\"info.banner\" mode=\"widthFix\"></image></view>\r\n\t\t<view class=\"box1\">\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.joinnum}}</view><view class=\"t2\">参与人数</view></view>\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.helpnum}}</view><view class=\"t2\">累计投票</view></view>\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.readcount}}</view><view class=\"t2\">访问次数</view></view>\r\n\t\t</view>\r\n\t\t<view class=\"box2\">\r\n\t\t\t<block v-for=\"(item,index) in datalist\" :key=\"index\">\r\n\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'detail?id='+item.id\">\r\n\t\t\t\t<view class=\"f1\" :style=\"{color:info.color2}\">{{index+1}}</view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"img\"/>\r\n\t\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t\t<text class=\"x1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"x2\">编号：{{item.number}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\" :style=\"{color:info.color2}\">{{item.helpnum}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<view v-if=\"!nomore\" style=\"width:100%;text-align:center;height:40rpx;line-height:40rpx;color:#778899;font-size:26rpx;margin-top:30rpx\" @tap=\"getmore\">- 查看更多 -</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:20rpx\"></view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n  data() {\r\n    return {  \r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n      nodata: false,\r\n\r\n      info: {},\r\n\t\t\tnowtime:0,\r\n      djsday: '00',\r\n      djshour: '00',\r\n      djsmin: '00',\r\n      djssec: '00',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t}, \r\n  onUnload: function () {\r\n    clearInterval(interval);\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('ApiToupiao/phb', {id:that.opt.id,pagenum:pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = res.datalist;\r\n          if ((that.datalist).length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\r\n\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\t\t//uni.setNavigationBarTitle({\r\n\t\t\t\t\t//\ttitle: that.info.name\r\n\t\t\t\t\t//});\r\n\t\t\t\t\tvar title = that.info.sharetitle ? that.info.sharetitle : that.info.name;\r\n\t\t\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : that.info.pic;\r\n\t\t\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\t\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\t\t\t\tthat.loaded({title:title,desc:sharedesc,link:sharelink,pic:sharepic});\r\n\r\n\t\t\t\t\tclearInterval(interval);\r\n\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif ((res.datalist).length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(res.datalist);\r\n            that.datalist = newdata;\r\n          }\r\n\t\t\t\t}\r\n      });\r\n    },\r\n    getdjs: function () {\r\n      var that = this;\r\n      if (that.info.starttime * 1 > that.nowtime * 1) {\r\n        var totalsec = that.info.starttime * 1 - that.nowtime * 1;\r\n      } else {\r\n        var totalsec = that.info.endtime * 1 - that.nowtime * 1;\r\n      }\r\n      if (totalsec <= 0) {\r\n        that.djsday = '00';\r\n        that.djshour = '00';\r\n        that.djsmin = '00';\r\n        that.djssec = '00';\r\n      } else {\r\n        var date = Math.floor(totalsec / 86400);\r\n        var houer = Math.floor((totalsec - date * 86400) / 3600);\r\n        var min = Math.floor((totalsec - date * 86400 - houer * 3600) / 60);\r\n        var sec = totalsec - date * 86400 - houer * 3600 - min * 60;\r\n        var djsday = (date < 10 ? '0' : '') + date;\r\n        var djshour = (houer < 10 ? '0' : '') + houer;\r\n        var djsmin = (min < 10 ? '0' : '') + min;\r\n        var djssec = (sec < 10 ? '0' : '') + sec;\r\n        that.djsday = djsday;\r\n        that.djshour = djshour;\r\n        that.djsmin = djsmin;\r\n        that.djssec = djssec;\r\n      }\r\n    },\r\n\t\tgetmore:function(){\r\n\t\t\tthis.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.container{min-height:100vh;display:flex;flex-direction:column;}\r\n.banner{width:100%;}\r\n.banner image{width:100%;height:auto}\r\n\r\n.box1{width:94%;margin-left:3%;border-radius:12rpx;background:#fff;padding:60rpx 10rpx;display:flex;align-items:center;position:relative;z-index:12;margin-top:-160rpx}\r\n.box1 .item{flex:1;display:flex;flex-direction:column;align-items:center;}\r\n.box1 .item .t1{font-size:48rpx;font-weight:bold}\r\n.box1 .item .t2{font-size:24rpx;color:#778899;margin-top:10rpx}\r\n\r\n.box2{width:94%;margin-left:3%;border-radius:12rpx;background:#fff;padding:30rpx 10rpx;display:flex;flex-direction:column;align-items:center;position:relative;z-index:12;margin-top:20rpx}\r\n.box2 .item{width:100%;display:flex;align-items:center;padding:0 40rpx 0 0;}\r\n.box2 .item .f1{font-size:36rpx;font-weight:bold;width:100rpx;text-align:center}\r\n.box2 .item .right{flex:1;border-bottom:1px solid #EEEEEE;display:flex;padding:20rpx 0;}\r\n.box2 .item .f2{display:flex;align-items:center;}\r\n.box2 .item .f2 .img{width:80rpx;height:80rpx;border-radius:50%;margin-right:20rpx;flex-shrink:0}\r\n.box2 .item .f2 .t1{display:flex;flex-direction:column;}\r\n.box2 .item .f2 .t1 .x1{color:#222222;font-size:28rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.box2 .item .f2 .t1 .x2{color:#778899;font-size:24rpx}\r\n.box2 .item .f3{flex:1;font-size:36rpx;font-weight:bold;display:flex;align-items:center;justify-content:flex-end;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phb.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phb.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024403\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}