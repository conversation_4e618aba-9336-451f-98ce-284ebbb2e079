{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?a982", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?ca48", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?b6dc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?5adf", "uni-app:///pagesExt/equity_pool/ranking.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?2efd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/ranking.vue?88d7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "isload", "pre_url", "equity_value", "my_ranking", "equity_ranking", "pagenum", "nomore", "nodata", "my_mid", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "msg", "isok", "getMoreData", "formatNumber", "num"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyDhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;;MAEA;MACA;QACAA;MACA;MAEAC;QAAAT;MAAA;QACAQ;QACAE;QAEA;UACAF;UACAA;UAEA;YACAA;YACA;cACAA;YACA;;YAEA;YACA;cACAA;YACA;UACA;YACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UAEAA;QACA;UACAA;YACAG;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/equity_pool/ranking.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/equity_pool/ranking.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ranking.vue?vue&type=template&id=19626f8e&\"\nvar renderjs\nimport script from \"./ranking.vue?vue&type=script&lang=js&\"\nexport * from \"./ranking.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ranking.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/equity_pool/ranking.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ranking.vue?vue&type=template&id=19626f8e&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.equity_ranking, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m2 = _vm.formatNumber(item.total_num)\n        var m3 = _vm.formatNumber(item.equity_value)\n        return {\n          $orig: $orig,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var g0 = _vm.isload\n    ? !_vm.nomore && !_vm.nodata && _vm.equity_ranking.length > 0\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ranking.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ranking.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"header-info\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t<view class=\"equity-value\">\r\n\t\t\t\t\t<view class=\"label\">当前股权单价</view>\r\n\t\t\t\t\t<view class=\"value\">{{equity_value}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"my-ranking\" v-if=\"my_ranking > 0\">\r\n\t\t\t\t\t<view class=\"label\">我的排名</view>\r\n\t\t\t\t\t<view class=\"value\">第 {{my_ranking}} 名</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"my-ranking\" v-else>\r\n\t\t\t\t\t<view class=\"label\">我的排名</view>\r\n\t\t\t\t\t<view class=\"value\">暂未上榜</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"ranking-list\">\r\n\t\t\t\t<view class=\"list-header\">\r\n\t\t\t\t\t<view class=\"header-item rank\">排名</view>\r\n\t\t\t\t\t<view class=\"header-item user\">用户</view>\r\n\t\t\t\t\t<view class=\"header-item count\">持股数</view>\r\n\t\t\t\t\t<view class=\"header-item value\">价值</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"list-content\">\r\n\t\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in equity_ranking\" :key=\"index\" :class=\"{'highlight': item.mid == my_mid}\">\r\n\t\t\t\t\t\t<view class=\"item-rank\" :class=\"'top' + item.ranking\" v-if=\"item.ranking <= 3\">{{item.ranking}}</view>\r\n\t\t\t\t\t\t<view class=\"item-rank\" v-else>{{item.ranking}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"item-user\">\r\n\t\t\t\t\t\t\t<image class=\"user-avatar\" :src=\"item.headimg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"user-name\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"item-count\">{{formatNumber(item.total_num)}}</view>\r\n\t\t\t\t\t\t<view class=\"item-value\">{{formatNumber(item.equity_value)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"no-more\" v-if=\"nomore\">没有更多数据了</view>\r\n\t\t\t\t<view class=\"loading-more\" v-if=\"!nomore && !nodata && equity_ranking.length > 0\">\r\n\t\t\t\t\t<view class=\"loading-text\" @tap=\"getMoreData\">点击加载更多</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"no-data\" v-if=\"nodata\">\r\n\t\t\t\t\t<image class=\"no-data-img\" src=\"/static/img/nodata.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<view class=\"no-data-text\">暂无排行数据</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tequity_value: \"0.00\",  // 单位股权价值\r\n\t\t\tmy_ranking: 0,         // 我的排名，0表示未上榜\r\n\t\t\tequity_ranking: [],    // 排行榜数据\r\n\t\t\t\r\n\t\t\tpagenum: 1,            // 当前页码\r\n\t\t\tnomore: false,         // 是否没有更多数据\r\n\t\t\tnodata: false,         // 是否暂无数据\r\n\t\t\tmy_mid: 0              // 我的mid\r\n\t\t};\r\n\t},\r\n\tonLoad: function(opt) {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function() {\r\n\t\tthis.pagenum = 1;\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.nodata = false;\r\n\t\t\t\r\n\t\t\t// 重置页码时清空列表\r\n\t\t\tif (that.pagenum === 1) {\r\n\t\t\t\tthat.equity_ranking = [];\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiEquityPool/ranking', {pagenum: that.pagenum}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.equity_value = parseFloat(res.data.equity_value).toFixed(2);\r\n\t\t\t\t\tthat.my_ranking = res.data.my_ranking || 0;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (that.pagenum === 1) {\r\n\t\t\t\t\t\tthat.equity_ranking = res.data.equity_ranking;\r\n\t\t\t\t\t\tif (res.data.equity_ranking.length === 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 获取当前用户的mid\r\n\t\t\t\t\t\tif (app.globalData.member && app.globalData.member.mid) {\r\n\t\t\t\t\t\t\tthat.my_mid = app.globalData.member.mid;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 追加数据\r\n\t\t\t\t\t\tif (res.data.equity_ranking.length === 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.equity_ranking = that.equity_ranking.concat(res.data.equity_ranking);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.$refs.popmsg.show({\r\n\t\t\t\t\t\tmsg: res.msg || '获取数据失败，请重试',\r\n\t\t\t\t\t\tisok: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载更多数据\r\n\t\tgetMoreData: function() {\r\n\t\t\tif (!this.nomore && !this.loading) {\r\n\t\t\t\tthis.pagenum++;\r\n\t\t\t\tthis.getdata();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化数字，保留两位小数\r\n\t\tformatNumber: function(num) {\r\n\t\t\tif (typeof num !== 'number') {\r\n\t\t\t\tnum = parseFloat(num) || 0;\r\n\t\t\t}\r\n\t\t\treturn num.toFixed(2);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header-info {\r\n\tpadding: 40rpx 30rpx;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.equity-value, .my-ranking {\r\n\ttext-align: center;\r\n}\r\n\r\n.label {\r\n\tfont-size: 24rpx;\r\n\topacity: 0.8;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.value {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.ranking-list {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\tmargin-top: -20rpx;\r\n\tpadding: 30rpx 20rpx;\r\n\tmin-height: 80vh;\r\n}\r\n\r\n.list-header {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1px solid #eee;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.header-item {\r\n\ttext-align: center;\r\n}\r\n\r\n.header-item.rank {\r\n\tflex: 1;\r\n}\r\n\r\n.header-item.user {\r\n\tflex: 3;\r\n\ttext-align: left;\r\n}\r\n\r\n.header-item.count {\r\n\tflex: 2;\r\n}\r\n\r\n.header-item.value {\r\n\tflex: 2;\r\n}\r\n\r\n.list-content {\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.list-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.list-item:active {\r\n\tbackground-color: #f9f9f9;\r\n}\r\n\r\n.list-item.highlight {\r\n\tbackground-color: rgba(240, 80, 90, 0.05);\r\n}\r\n\r\n.item-rank {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.item-rank.top1 {\r\n\tcolor: #fff;\r\n\tbackground-color: #ff7043;\r\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 112, 67, 0.3);\r\n}\r\n\r\n.item-rank.top2 {\r\n\tcolor: #fff;\r\n\tbackground-color: #ff9800;\r\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 152, 0, 0.3);\r\n}\r\n\r\n.item-rank.top3 {\r\n\tcolor: #fff;\r\n\tbackground-color: #ffc107;\r\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 193, 7, 0.3);\r\n}\r\n\r\n.item-user {\r\n\tflex: 3;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.user-avatar {\r\n\twidth: 70rpx;\r\n\theight: 70rpx;\r\n\tborder-radius: 35rpx;\r\n\tmargin-right: 15rpx;\r\n\tborder: 2px solid #fff;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tmax-width: 150rpx;\r\n}\r\n\r\n.item-count {\r\n\tflex: 2;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.item-value {\r\n\tflex: 2;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #f0505a;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.no-more, .loading-more {\r\n\ttext-align: center;\r\n\tpadding: 30rpx 0;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.loading-text {\r\n\tdisplay: inline-block;\r\n\tpadding: 10rpx 30rpx;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.no-data {\r\n\tpadding: 80rpx 0;\r\n\ttext-align: center;\r\n}\r\n\r\n.no-data-img {\r\n\twidth: 200rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.no-data-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ranking.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ranking.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093061\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}