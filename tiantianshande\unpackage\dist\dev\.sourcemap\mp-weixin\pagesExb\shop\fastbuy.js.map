{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?a807", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?f5c8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?d062", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?6cb2", "uni-app:///pagesExb/shop/fastbuy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?c8b0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy.vue?53f6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "cartListShow", "buydialogShow", "harr", "datalist", "cartList", "proid", "totalprice", "currentActiveIndex", "animation", "scrollToViewId", "bid", "scrollState", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "list", "total", "clickRootItem", "setTimeout", "addcart", "ggid", "num", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "currentTarget", "dataset", "clearShopCartFn", "gopay", "prodata", "uni", "url", "fail", "gotoCatproductPage", "scroll", "countH", "buydialogChange", "handleClickMask", "loaded", "getmenuindex"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmGhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAP;MAAA;QACAM;QACAA;;QAEA;QACA;UACAA;UACA;QACA;UACAA;YAAAE;YAAAC;YAAAb;UAAA;UACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;UACA;UACAJ;QACA;QACAc;QAEA;UACAA;UACA;YACA;UACA;QACA;QACAA;QACAA;MACA;QACAA;QACA;QACAC;MACA;IACA;IACAG;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAN;MACAC;QAAAZ;QAAAkB;QAAAC;MAAA;QACAR;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAQ;MACA;MACAC;MACAA;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACAC;MACA;MACAZ;QAAAP;MAAA;QACAM;MACA;IACA;IACAc;MACA;MACA;MACA;QACAb;QACA;QACA;MACA;MACA;MACA;QACA;UACA;YACAc;YACA;UACA;QACA;;QACA;QACA;;QAEA;QACA;UACAC;YACAC;YACAC;cACA;cACA;cACAjB;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;QACA;QACAA;MACA;IACA;IACAkB;MACA;MACA;QACAlB;MACA;QACAA;MACA;IACA;IACAmB;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAxB;MACA;MACA;MACAgB;IACA;IACAS;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvSA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/shop/fastbuy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/shop/fastbuy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fastbuy.vue?vue&type=template&id=44c44357&\"\nvar renderjs\nimport script from \"./fastbuy.vue?vue&type=script&lang=js&\"\nexport * from \"./fastbuy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fastbuy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/shop/fastbuy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy.vue?vue&type=template&id=44c44357&\"", "var components\ntry {\n  components = {\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = index === _vm.currentActiveIndex ? _vm.t(\"color1\") : null\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (detail, index) {\n        var $orig = _vm.__get_orig(detail)\n        var l1 = _vm.__map(detail.prolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 =\n            item.price_type != 1 || item.sell_price > 0 ? _vm.t(\"color1\") : null\n          var m3 = !item.price_type ? _vm.t(\"color1rgb\") : null\n          var m4 = !item.price_type ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n        return {\n          $orig: $orig,\n          l1: l1,\n        }\n      })\n    : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m7 = _vm.isload && _vm.cartList.total > 0 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g0 = _vm.isload && _vm.cartListShow ? _vm.cartList.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l2: l2,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"view-show\">\r\n\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/search?bid='+bid\" class=\"search-container\">\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t<view class=\"search-text\">搜索感兴趣的商品</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" style=\"overflow:hidden;display:flex\" :style=\"{height:'calc(100% - '+(menuindex>-1?294:194)+'rpx)'}\">\r\n\t\t\t\t<scroll-view class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\r\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" :style=\"{color:index===currentActiveIndex?t('color1'):'#333'}\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t\t<view class=\"nav_right-content\">\r\n\t\t\t\t\t\t<scroll-view @scroll=\"scroll\" class=\"detail-list\" :scrollIntoView=\"scrollToViewId\" :scrollWithAnimation=\"animation\" scroll-y=\"true\" :show-scrollbar=\"false\">\r\n\t\t\t\t\t\t\t<view v-for=\"(detail, index) in datalist\" :key=\"index\" class=\"classification-detail-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"head\" :data-id=\"detail.id\" :id=\"'detail-' + detail.id\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{detail.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"show-all\" @tap=\"gotoCatproductPage\" :data-id=\"detail.id\">查看全部<text class=\"iconfont iconjiantou\"></text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in detail.prolist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/shopPackage/shop/product?id='+item.id\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p2\" v-if=\"item.price_type != 1 || item.sell_price > 0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-2\" v-if=\"item.limit_start>0\"><text style=\"overflow:hidden;\">{{item.limit_start}}件起售</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p4\" v-if=\"!item.price_type\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\"></buydialog>\r\n\t\t<view style=\"height:auto;position:relative\">\r\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t<view class=\"cart_ico\" :style=\"{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"handleClickMask\"><image class=\"img\" src=\"/static/img/cart.png\"/><view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"cartList.total>0\">{{cartList.total}}</view></view>\r\n\t\t\t\t<view class=\"text1\">合计</view>\r\n\t\t\t\t<view class=\"text2 flex1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx\">￥</text>{{cartList.totalprice}}</view>\r\n\t\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"gopay\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view v-if=\"cartListShow\" class=\"popup__container\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"min-height:400rpx;padding:0\">\r\n\t\t\t\t<view class=\"popup__title\" style=\"border-bottom:1px solid #EFEFEF\">\r\n\t\t\t\t\t<text class=\"popup__title-text\" style=\"color:#323232;font-weight:bold;font-size:32rpx\">购物车</text>\r\n\t\t\t\t\t<view class=\"popup__close flex-y-center\" @tap.stop=\"clearShopCartFn\" style=\"color:#999999;font-size:24rpx\"><image src=\"/static/img/del.png\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>清空</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\" style=\"padding:0\">\r\n\t\t\t\t\t<scroll-view scroll-y class=\"prolist\">\r\n\t\t\t\t\t\t<block v-for=\"(cart, index) in cartList.list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"proitem\">\r\n\t\t\t\t\t\t\t\t<image :src=\"cart.guige.pic?cart.guige.pic:cart.product.pic\" class=\"pic flex0\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"con\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cart.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"cart.guige.name!='默认规格'\">{{cart.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"color:#ff5555;margin-top:10rpx;font-size:28rpx\">￥{{cart.guige.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" src=\"/static/img/cart-minus.png\" @tap=\"addcart\" data-num=\"-1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"i\">{{cart.num}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" src=\"/static/img/cart-plus.png\" @tap=\"addcart\" data-num=\"1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"!cartList.list.length\">\r\n\t\t\t\t\t\t\t<text class=\"nopro\">暂时没有商品喔~</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tcartListShow:false,\r\n\t\t\tbuydialogShow:false,\r\n\t\t\tharr:[],\r\n      datalist: [],\r\n\t\t\tcartList:{},\r\n\t\t\tproid:'',\r\n\t\t\ttotalprice:'0.00',\r\n      currentActiveIndex: 0,\r\n      animation: true,\r\n      scrollToViewId: \"\",\r\n\t\t\tbid:'',\r\n\t\t\tscrollState:true\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid ? this.opt.bid  : '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiShop/fastbuy', {bid:bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.datalist = res.data;\r\n\t\t\t\t\r\n\t\t\t\t// 确保cartList正确初始化\r\n\t\t\t\tif (res.cartList) {\r\n\t\t\t\t\tthat.cartList = res.cartList;\r\n\t\t\t\t//\tconsole.log('2023-08-01 10:00:00-INFO-[fastbuy][getdata_003] 购物车数据:', res.cartList.total, '件商品，总价:', res.cartList.totalprice);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.cartList = {list: [], total: 0, totalprice: '0.00'};\r\n\t\t\t\t\t//console.log('2023-08-01 10:00:00-WARN-[fastbuy][getdata_004] 购物车数据为空，已初始化');\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//计算每个高度\r\n\t\t\t\tvar harr = [];\r\n\t\t\t\tvar clientwidth = uni.getSystemInfoSync().windowWidth;\r\n\t\t\t\tvar datalist = res.data;\r\n\t\t\t\t//console.log('2023-08-01 10:00:00-INFO-[fastbuy][getdata_005] 分类数量:', datalist.length);\r\n\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\r\n\t\t\t\t\tvar child = datalist[i].prolist;\r\n\t\t\t\t\tharr.push(Math.ceil(child.length) * 200 / 750 * clientwidth);\r\n\t\t\t\t}\r\n\t\t\t\tthat.harr = harr;\r\n\r\n\t\t\t\tif(that.opt.cid){\r\n\t\t\t\t\tthat.scrollToViewId = 'detail-' + that.opt.cid;\r\n\t\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\r\n\t\t\t\t\t\tif(datalist[i].id == that.opt.cid) that.currentActiveIndex = i;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.isload = true; // 确保页面显示\r\n\t\t\t\tthat.loaded();\r\n\t\t\t}, function(err) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t//console.log('2023-08-01 10:00:00-ERROR-[fastbuy][getdata_006] 获取数据失败:', JSON.stringify(err));\r\n\t\t\t\tapp.error('获取数据失败，请重试');\r\n\t\t\t});\r\n\t\t},\r\n    clickRootItem: function (t) {\r\n\t\t\tthis.scrollState=false;\r\n      var e = t.currentTarget.dataset;\r\n      this.scrollToViewId = 'detail-' + e.rootItemId;\r\n      this.currentActiveIndex = e.rootItemIndex;\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthis.scrollState=true;\r\n\t\t\t},500)\r\n    },\r\n\t\taddcart:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ks = that.ks;\r\n\t\t\tvar num = e.currentTarget.dataset.num;\r\n\t\t\tvar proid = e.currentTarget.dataset.proid;\r\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    //加入购物车弹窗后\r\n    afteraddcart: function (e) {\r\n     // console.log('2023-08-01 10:00:00-INFO-[fastbuy][afteraddcart_001] 加入购物车:', e);\r\n      e.hasoption = false;\r\n      e.num = e.num || 1; // 确保有数量参数\r\n      this.addcart({currentTarget:{dataset:e}});\r\n    },\r\n    clearShopCartFn: function () {\r\n      var that = this;\r\n      app.post(\"ApiShop/cartclear\", {bid:that.opt.bid}, function (res) {\r\n        that.getdata();\r\n      });\r\n    },\r\n    gopay: function () {\r\n      var cartList = this.cartList.list;\r\n    //  console.log('2023-08-01 10:00:00-INFO-[fastbuy][gopay_001] 点击去结算按钮，当前购物车:', JSON.stringify(cartList));\r\n      if (!cartList || cartList.length == 0) {\r\n        app.alert('请先添加商品到购物车');\r\n     //   console.log('2023-08-01 10:00:00-INFO-[fastbuy][gopay_002] 购物车为空');\r\n        return;\r\n      }\r\n      var prodata = [];\r\n      try {\r\n        for (var i = 0; i < cartList.length; i++) {\r\n          if (cartList[i].proid && cartList[i].ggid) {\r\n            prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);\r\n          //  console.log('2023-08-01 10:00:00-INFO-[fastbuy][gopay_003] 添加商品:', cartList[i].proid, cartList[i].ggid, cartList[i].num);\r\n          }\r\n        }\r\n        var url = 'buy?frompage=fastbuy&prodata=' + prodata.join('-');\r\n       // console.log('2023-08-01 10:00:00-INFO-[fastbuy][gopay_004] 跳转URL:', url);\r\n        \r\n        // 确保页面正确跳转\r\n        if (prodata.length > 0) {\r\n          uni.navigateTo({\r\n            url: '/shopPackage/shop/' + url,\r\n            fail: function(err) {\r\n           //   console.log('2023-08-01 10:00:00-ERROR-[fastbuy][gopay_005] 跳转失败:', JSON.stringify(err));\r\n              // 尝试使用app.goto方式\r\n              app.goto(url);\r\n            }\r\n          });\r\n        } else {\r\n          app.alert('购物车数据异常，请重新添加商品');\r\n        //  console.log('2023-08-01 10:00:00-ERROR-[fastbuy][gopay_006] 购物车数据异常');\r\n        }\r\n      } catch (e) {\r\n      //  console.log('2023-08-01 10:00:00-ERROR-[fastbuy][gopay_007] 处理异常:', e.message);\r\n        app.alert('处理异常，请稍后重试');\r\n      }\r\n    },\r\n    gotoCatproductPage: function (t) {\r\n      var e = t.currentTarget.dataset;\r\n\t\t\tif(this.bid){\r\n\t\t\t\tapp.goto('/shopPackage/shop/prolist?bid='+this.bid+'&cid2=' + e.id);\r\n\t\t\t}else{\r\n\t\t\t\tapp.goto('/shopPackage/shop/prolist?cid=' + e.id);\r\n\t\t\t}\r\n    },\r\n    scroll: function (e) {\r\n\t\tif(this.scrollState){\r\n\t\t\tvar scrollTop = e.detail.scrollTop;\r\n\t\t\tvar harr = this.harr;\r\n\t\t\tvar countH = 0;\r\n\t\t\tfor (var i = 0; i < harr.length; i++) {\r\n\t\t\t  if (scrollTop >= countH && scrollTop < countH + harr[i]) {\r\n\t\t\t    this.currentActiveIndex = i;\r\n\t\t\t    break;\r\n\t\t\t  }\r\n\t\t\t  countH += harr[i];\r\n\t\t\t}\r\n\t\t}\r\n    },\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\thandleClickMask:function(){\r\n\t\t\tthis.cartListShow = !this.cartListShow;\r\n\t\t},\r\n\t\tloaded: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 设置页面加载完成状态\r\n\t\t\tthat.isload = true;\r\n\t\t\t//console.log('2023-08-01 10:00:00-INFO-[fastbuy][loaded_001] 页面加载完成');\r\n\t\t\t// 停止下拉刷新\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t},\r\n\t\tgetmenuindex: function(e) {\r\n\t\t\tthis.menuindex = e || -1;\r\n\t\t\t//console.log('2023-08-01 10:00:00-INFO-[fastbuy][getmenuindex_001] 菜单索引:', this.menuindex);\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {position: relative;width: 100%;height: 100%;}\r\n.container{height:100%;overflow:hidden}\r\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\r\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\r\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\r\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\r\n\r\n.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#333333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\r\n.nav_left .nav_left_items.active{background: #fff;color:#333333;font-size:30rpx;font-weight:bold}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 20rpx 20rpx}\r\n.nav_right-content{background: #ffffff;padding:20rpx;height:100%;position:relative}\r\n.detail-list {height:100%;overflow:scroll}\r\n.classification-detail-item {width: 100%;overflow: visible;background:#fff}\r\n.classification-detail-item .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;}\r\n.classification-detail-item .head .txt {color:#222222;font-weight:bold;font-size:30rpx;}\r\n.classification-detail-item .head .show-all {font-size: 26rpx;color:#949494;display:flex;align-items:center}\r\n\r\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\r\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\r\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\r\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;font-weight:bold;}\r\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999;margin-right:10rpx}\r\n.product-itemlist .product-info .p3-2{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#777}\r\n.product-itemlist .product-info .p4{width:56rpx;height:56rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:32rpx;height:56rpx;line-height:56rpx}\r\n\r\n.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}\r\n.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}\r\n.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}\r\n.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}\r\n.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}\r\n.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}\r\n.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\r\n.prolist .proitem .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.prolist .proitem .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.prolist .proitem .addnum .img{width:24rpx;height:24rpx}\r\n.prolist .proitem .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\r\n.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}\r\n\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\r\n.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}\r\n.footer .cart_ico .img{width:36rpx;height:36rpx;}\r\n.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}\r\n.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}\r\n.footer .text2 {font-size: 32rpx;font-weight:bold}\r\n.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}\r\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115022997\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}