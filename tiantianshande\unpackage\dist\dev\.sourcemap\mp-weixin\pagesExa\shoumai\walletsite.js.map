{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?6648", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?2d15", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?44e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?5d5e", "uni-app:///pagesExa/shoumai/walletsite.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?bb98", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/walletsite.vue?a3ed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "corridorIndex", "corridorList", "corridorItem", "wallet_address", "pypwd", "receiving_code", "onLoad", "app", "_that", "setTimeout", "uni", "title", "onPullDownRefresh", "methods", "corridorChange", "upIdcardHead", "formSubmit", "corridor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmDnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;MACAC;MACA;QACAD;QACAE;UACAF;QACA;QACA;MACA;MAEAC;MACA;QACAA;UAAA;QAAA;QACAA;UAAA;QAAA;MACA;MAEA,oCACAA;MAEA;QACAA;MACA;QACAA;MACA;MAEAE;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAF;EACA;EACAG;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAR;QACA;UACAC;QACA;MACA;IACA;IACAQ;MACA;MAEA;MACA;MACA;;MAEA;MACA;QACAT;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEAA;MACAA;QACAU;QACAd;QACAE;QACAD;MACA;QACAG;QACA;UACAA;UACAE;YACAF;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7KA;AAAA;AAAA;AAAA;AAA87C,CAAgB,y4CAAG,EAAC,C;;;;;;;;;;;ACAl9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/shoumai/walletsite.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/shoumai/walletsite.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./walletsite.vue?vue&type=template&id=210838dc&scoped=true&\"\nvar renderjs\nimport script from \"./walletsite.vue?vue&type=script&lang=js&\"\nexport * from \"./walletsite.vue?vue&type=script&lang=js&\"\nimport style0 from \"./walletsite.vue?vue&type=style&index=0&id=210838dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"210838dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/shoumai/walletsite.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./walletsite.vue?vue&type=template&id=210838dc&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./walletsite.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./walletsite.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"container\">\r\n        <block v-if=\"isload\">\r\n            <form @submit=\"formSubmit\" @reset=\"formReset\">\r\n                <view class=\"auth\">\r\n                   <view class=\"text\">\r\n                        <text>設置說明:<br>1、請正確填寫錢包地址和上傳收款碼，這將用於收款和確認身份。<br>2、支持多種收款通道，請選擇您常用的收款方式。<br>3、保持收款碼清晰完整，確保買家能夠順利支付。<br>4、設置交易密碼用於保護您的資產安全，請妥善保管。</text>\r\n                    </view>\r\n                    <view class=\"infos\">\r\n                    \t<view class=\"list\">\r\n    \t\t\t\t\t\t<text>支付通道</text>\r\n    \t\t\t\t\t\t<picker  class=\"picker\" mode=\"selector\" name=\"corridor\" :value=\"corridorIndex\" :range=\"corridorList\" range-key=\"name\" @change=\"corridorChange\">\r\n    \t\t\t\t\t\t\t<view style=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\">{{corridorItem.name ? corridorItem.name : '請選擇通道'}}</view>\r\n    \t\t\t\t\t\t</picker>\r\n                            <text class=\"tips\">選擇您常用的收款通道，不同通道可能有不同的手續費</text>\r\n\t    \t\t\t\t</view>\r\n                        <view class=\"list\">\r\n                            <text>錢包地址</text>\r\n                            <input placeholder=\"請輸入收款錢包地址\" placeholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" border=\"surround\" name=\"wallet_address\" v-model=\"wallet_address\"></input>\r\n                            <text class=\"tips\">收款地址將顯示給買家，請確保地址正確</text>\r\n                        </view>\r\n                        <view class=\"list\">\r\n                            <text>收款二維碼</text>\r\n                            <view class=\"upload\" @click=\"upIdcardHead\">\r\n                                <image :src=\"receiving_code\"></image>\r\n                                <text>點擊上傳收款碼，確保清晰完整</text>\r\n                            </view>\r\n                            <text class=\"tips\">買家將通過此收款碼向您支付</text>\r\n                        </view>\r\n                        <view class=\"list\">\r\n                            <text>交易密碼</text>\r\n                            <input type=\"password\" placeholder=\"請輸入交易密碼（至少6位）\" placeholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" border=\"surround\" name=\"pypwd\" v-model=\"pypwd\"></input>\r\n                            <text class=\"tips\">用於保護您的交易安全，請設置復雜密碼</text>\r\n                        </view>\r\n                        <view class=\"button\" @click=\"formSubmit\">\r\n                            <text>提交保存</text>\r\n                        </view>\r\n                        <view class=\"info-text\">\r\n                            <text>提醒：設置錢包信息後，您才能進行資產售賣和購買操作。</text>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </form>\r\n        </block>\r\n        <loading v-if=\"loading\"></loading>\r\n        <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n        <popmsg ref=\"popmsg\"></popmsg>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tcorridorIndex: 0,\r\n\t\t\t\tcorridorList: [],\r\n\t\t\t\tcorridorItem: {},\r\n\t\t\t\twallet_address: '',\r\n\t\t\t\tpypwd: '',\r\n\t\t\t\treceiving_code: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.isload = true;\r\n\t\t\tconst _that = this\r\n\t\t\tthis.loading = true;\r\n\t\t\tapp.get('ApiShoumai/setWalletInfo', {}, function(data) {\r\n\t\t\t\t_that.loading = false;\r\n\t\t\t\tif(data.status == 2){\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tapp.goto(data.to_url);\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t_that.corridorList = data.data.corridorList || [];\r\n\t\t\t\tif(data.data.corridor > 0){\r\n\t\t\t\t\t_that.corridorItem = _that.corridorList.find(item => item.id === data.data.corridor) || {};\r\n\t\t\t\t\t_that.corridorIndex = _that.corridorList.findIndex(item => item.id === data.data.corridor);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif(data.data.wallet_address != '')\r\n\t\t\t\t\t_that.wallet_address = data.data.wallet_address;\r\n\r\n\t\t\t\tif(data.data.wallet_receiving_code == ''){\r\n\t\t\t\t\t_that.receiving_code = '/static/img/alipay.jpg';\r\n\t\t\t\t} else {\r\n\t\t\t\t\t_that.receiving_code = data.data.wallet_receiving_code;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '設置錢包信息'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.onLoad(this.opt);\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcorridorChange(e){\r\n\t\t\t\tthis.corridorIndex = e.target.value;\r\n\t\t\t\tthis.corridorItem = this.corridorList[e.target.value] || {};\r\n\t\t\t},\r\n\t\t\tupIdcardHead(){\r\n\t\t\t\tconst _that = this;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\tif(data && data.length > 0) {\r\n\t\t\t\t\t\t_that.receiving_code = data[0];\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1);\r\n\t\t\t},\r\n\t\t\tformSubmit: function() {\r\n\t\t\t\tconst _that = this;\r\n\r\n\t\t\t\tvar wallet_address = _that.wallet_address.trim();\r\n\t\t\t\tvar pypwd = _that.pypwd;\r\n\t\t\t\tvar receiving_code = _that.receiving_code;\r\n\r\n\t\t\t\t// 验证通道选择\r\n\t\t\t\tif(!_that.corridorItem || !_that.corridorItem.id){\r\n\t\t\t\t\tapp.alert('請選擇支付通道');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 验证交易密码\r\n\t\t\t\tif (pypwd == '') {\r\n\t\t\t\t\tapp.alert('請輸入交易密碼');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif(pypwd.length < 6){\r\n\t\t\t\t\tapp.alert('交易密碼最少為6位');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 验证钱包地址\r\n\t\t\t\tif (wallet_address == '') {\r\n\t\t\t\t\tapp.alert('請輸入錢包地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 验证收款码\r\n\t\t\t\tif(receiving_code == '/static/img/alipay.jpg'){\r\n\t\t\t\t\tapp.alert('請上傳收款二維碼');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiShoumai/setWalletInfo', {\r\n\t\t\t\t\tcorridor: _that.corridorItem.id,\r\n\t\t\t\t\twallet_address: wallet_address,\r\n\t\t\t\t\treceiving_code: receiving_code,\r\n\t\t\t\t\tpypwd: pypwd\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tapp.success(data.msg || '設置成功');\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tapp.goto(\"/pagesExa/shoumai/index\");\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(data.msg || '設置失敗');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.picker{\r\n\t\theight: 80rpx;\r\n\t\tbackground: #F5F5FB;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding-left: 32rpx;\r\n\t}\r\n\t.auth{\r\n\t\theight: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\t& text{\r\n\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\tcolor: #7C7597;\r\n\t\t}\r\n\t\t.text{\r\n\t\t\tline-height: 35px;\r\n\t\t\tpadding: 20px 30px;\r\n\t\t\theight: auto;\r\n\t\t\tdisplay: block;\r\n\t\t\tbackground: #f9f9fe;\r\n\t\t\tmargin: 20rpx 30rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tborder-left: 4px solid #533CD7;\r\n\t\t}\r\n\t\t.infos{\r\n\t\t\t.list{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tpadding: 40rpx 50rpx 0;\r\n\t\t\t\t>text{\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #160651;\r\n\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\tmargin-bottom: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t\t>input{\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\tpadding-left: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.tips {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\tfont-weight: normal;\r\n\t\t\t\t}\r\n\t\t\t\t.upload{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\tpadding: 40rpx 40rpx 20rpx 40rpx;\r\n\t\t\t\t\t& image{\r\n\t\t\t\t\t\twidth: 248rpx;\r\n\t\t\t\t\t\theight: 334rpx;\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t>text{\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.button{\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 0 82rpx 40rpx 82rpx;\r\n\t\t\tmargin-top: 60rpx;\r\n\t\t\t>text{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 84rpx;\r\n\t\t\t\tbackground: #533CD7;\r\n\t\t\t\tbox-shadow: 0rpx 6rpx 30rpx 0rpx rgba(83,60,215,0.4600);\r\n\t\t\t\tborder-radius: 43rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 84rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.info-text {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 0 50rpx 50rpx 50rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.form {\r\n\t\twidth: 94%;\r\n\t\tmargin: 20rpx 3%;\r\n\t\tborder-radius: 5px;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tpadding: 0 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\theight: 98rpx;\r\n\t\tline-height: 98rpx;\r\n\t}\r\n\r\n\t.form-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.form-item .label {\r\n\t\tcolor: #000;\r\n\t\twidth: 200rpx;\r\n\t}\r\n\r\n\t.form-item .input {\r\n\t\tflex: 1;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.set-btn {\r\n\t\twidth: 90%;\r\n\t\tmargin: 60rpx 5%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./walletsite.vue?vue&type=style&index=0&id=210838dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./walletsite.vue?vue&type=style&index=0&id=210838dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115072191\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}