{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?9286", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?15c9", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?7671", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?381a", "uni-app:///zhaopin/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?beaa", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/index.vue?73e2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "listFilter", "positionGuide", "regularItem", "tab", "onLoad", "onPullDownRefresh", "onReachBottom", "data", "opt", "loading", "listFilterVisitor", "townName", "tabList", "currentJobType", "loinTotal", "isSign", "pageNum", "pageSize", "isEnd", "jobItemType", "prevItemType", "homeList", "showFilter", "jobHomeList", "isSearchFixed", "isFixed", "isLoading", "firstLoaded", "fixedClass", "listGroupId", "pre_url", "classifications", "sortRules", "key", "value", "areas", "areaId", "townId", "areaName", "clearingForms", "userSex", "classList", "searchList", "name", "hot", "sourceType", "showGuide", "commandFilterData", "type_id", "areaIds", "sexRequire", "controlPop", "hostExistsJobIds", "clearWordChange", "collectImage", "collectContentId", "collectVisible", "newUserTemplate", "isCollectDialog", "universeParam", "refreshLocation", "firstPageJobNum", "waitRenderList", "noMore", "contentHeight", "jobList", "loadMoreStatus", "methods", "getCategoryList", "app", "that", "tabChange", "console", "index", "currentIndex", "oldJobItemType", "getModuleList", "params", "category_id", "page", "limit", "keyword", "partJobId", "title", "titleSimple", "salary", "companyName", "logo", "addressDetail", "distance", "jobLineType", "category", "entryCount", "companyType", "labelList", "serviceLabels", "labelId", "labelName", "labelStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "tags", "listStyle", "uni", "filterChange", "btFresh", "locationHandle", "getTypeList", "pid", "item", "classificationId", "secondClassifications", "child", "parentId", "id", "sort", "handleSwiperChange", "currentJobItemType", "calculateContentHeight", "query", "mounted", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6E9wB;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC,YACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC,QACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC,gBACA;QACAN;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAM;MACAC;MACAC,aACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC;MACAC;QACAC;QACAT;QACAU;QACAC;QACAlB;MACA;MACAmB;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACAC;QACA;UACA;UACA;YACApC;YACAC;UACA;UACAoC;YAAA;cACArC;cACAC;YACA;UAAA;;UAEA;UACAoC;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAE;MACA;MACAC;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;MAEA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IACA;IACAC;MACA;MACAN;;MAEA;MACA;QACAO;UACAC;QACA;MACA;MAEA;QACAC;QACAC;MAAA,GACAH;QACAI;MAAA,EACA;MAEAT;MAEAH;QACAC;QACA;UACA;UACA;YAAA;YAAA;cACAY;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;gBACA5D;gBACAC;cACA;cACA4D;gBACAC,gBACA;kBACAC;kBACAC;kBACAC;gBACA,EACA;gBACAC;kBAAA;oBAAAC;oBAAAC;kBAAA,OACAA;oBAAA;sBACAL;sBACAC;sBACAC;oBACA;kBAAA;gBAAA,EACA;cACA;cACAI;YACA;UAAA;;UAEA;UACA;YACAhC;UACA;YACAA;UACA;;UAEA;UACAA;UACAA;UAEAE;QACA;UACAH;QACA;QAEAkC;MACA;IACA;IACAC;MACA;MACA,6CACA;QACA1B;MAAA,EACA;MACA;MACA;IACA;IACA2B;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAtC;QAAAuC;MAAA;QACA;UACA;UACA;YAAA,uCACAC;cAAA;cACAC;cACAC;gBAAA,uCACAC;kBAAA;kBACAF;kBACAG;gBAAA;cAAA,CACA;YAAA;UAAA,CACA;;UAEA;UACA;YACAC;YACAN;YACAjE;YACAwE;YACAL;YACAC;UACA;UAEAzC;QACA;UACAD;QACA;MACA;IACA;IACA;IACA+C;MACA;MACA5C;QACAC;QACA4C;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACAC;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAtG;MACAqD;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACneA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,42CAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=d95dc244&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=d95dc244&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.firstLoaded\n    ? _vm.__map(_vm.tabList, function (tab, index) {\n        var $orig = _vm.__get_orig(tab)\n        var m0 = Number(_vm.tabList[_vm.jobItemType].key)\n        var g0 = _vm.homeList.length === 0 && !_vm.loading\n        return {\n          $orig: $orig,\n          m0: m0,\n          g0: g0,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e) {\n      return (_vm.listFilterVisitor = e.detail)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template> \r\n\t<view>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<page-meta :pageStyle=\"'overflow: ' + (listFilterVisitor ? 'hidden' : 'auto')\"></page-meta>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"select-job-area\" v-if=\"firstLoaded\">\r\n\t\t\t\t<view :class=\"'gradient-bg fixed white'\"\r\n\t\t\t\t\t:style=\"'top: 0px; z-index: 999;'\">\r\n\t\t\t\t\t<tab \r\n\t\t\t\t\t\tref=\"jobTab\"\r\n\t\t\t\t\t\t@tabChange=\"tabChange\" \r\n\t\t\t\t\t\t:tabList=\"tabList\"\r\n\t\t\t\t\t\t:current=\"jobItemType\"\r\n\t\t\t\t\t\t:style=\"'top: 0px; z-index: 999;'\">\r\n\t\t\t\t\t</tab>\r\n\t\t\t\t\t<view v-if=\"showFilter\">\r\n\t\t\t\t\t\t<list-filter\r\n\t\t\t\t\t\t\t:areas=\"areas\"\r\n\t\t\t\t\t\t\t@change=\"filterChange\"\r\n\t\t\t\t\t\t\t@visitorChange=\"(e) => listFilterVisitor = e.detail\"\r\n\t\t\t\t\t\t\tclass=\"filterRef\"\r\n\t\t\t\t\t\t\tclassName=\"index-filter\"\r\n\t\t\t\t\t\t\t:clearingList=\"clearingForms\"\r\n\t\t\t\t\t\t\t:controlPop=\"controlPop\"\r\n\t\t\t\t\t\t\t:jobtypeList=\"classifications\"\r\n\t\t\t\t\t\t\t:ptpId=\"tabList[jobItemType].key\"\r\n\t\t\t\t\t\t\t:sortRules=\"sortRules\"\r\n\t\t\t\t\t\t\t:typeIndex=\"jobItemType\"\r\n\t\t\t\t\t\t\t:userSex=\"userSex\"\r\n\t\t\t\t\t\t></list-filter>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<swiper class=\"swiper-container\" \r\n\t\t\t\t\t:current=\"jobItemType\" \r\n\t\t\t\t\t@change=\"handleSwiperChange\"\r\n\t\t\t\t\t:style=\"{ height: 'calc(100vh - ' + (showFilter ? '206rpx' : '106rpx') + ')' }\">\r\n\t\t\t\t\t<swiper-item v-for=\"(tab, index) in tabList\" :key=\"index\">\r\n\t\t\t\t\t\t<scroll-view \r\n\t\t\t\t\t\t\tscroll-y \r\n\t\t\t\t\t\t\t@scrolltolower=\"onReachBottom\"\r\n\t\t\t\t\t\t\t:class=\"'school-life-job-list ' + (isFixed && showFilter ? 'padding206' : isFixed && !showFilter ? 'padding106' : tabList[jobItemType].key !== '1' ? 'padding16' : '') + ' ' + (tabList[jobItemType].key === '4' ? 'padding0' : '')\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<position-guide \r\n\t\t\t\t\t\t\t\t@handleTapGuide=\"locationHandle\" \r\n\t\t\t\t\t\t\t\tv-if=\"tabList[jobItemType].key === '3' && showGuide\">\r\n\t\t\t\t\t\t\t</position-guide>\r\n\t\t\t\t\t\t\t<regular-item\r\n\t\t\t\t\t\t\t\t@btnFresh=\"btFresh\"\r\n\t\t\t\t\t\t\t\t:data=\"item\"\r\n\t\t\t\t\t\t\t\t:index=\"idx\"\r\n\t\t\t\t\t\t\t\t:listIndex=\"Number(tabList[jobItemType].key)\"\r\n\t\t\t\t\t\t\t\t:ptpId=\"'jfb2-dn2b-dn1b-718d-' + tabList[jobItemType].key\"\r\n\t\t\t\t\t\t\t\t:type=\"jobItemType\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, idx) in homeList\"\r\n\t\t\t\t\t\t\t\t:key=\"idx\"\r\n\t\t\t\t\t\t\t></regular-item>\r\n\t\t\t\t\t\t\t<view class=\"noneList\" v-if=\"homeList.length === 0 && !loading\">\r\n\t\t\t\t\t\t\t\t<image lazyLoad mode=\"widthFix\" :src=\"pre_url+'/static/img/wuzhaopin.png'\"></image>\r\n\t\t\t\t\t\t\t\t<text>此时内心是空荡荡的</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport listFilter from './components/newListFilter/index';\r\nimport positionGuide from './components/positionGuide/index';\r\nimport regularItem from './components/regularItem/index';\r\nimport tab from './components/tab/index';\r\n\r\nvar app = getApp();\r\nexport default {\r\n\tcomponents: {\r\n\t\tlistFilter,\r\n\t\tpositionGuide,\r\n\t\tregularItem,\r\n\t\ttab\r\n\t},\r\n\tonLoad(opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getCategoryList();\r\n\t\tthis.getTypeList();\r\n\t},\r\n\tonPullDownRefresh() {\r\n\t\tthis.pageNum = 1;\r\n\t\tthis.getModuleList(this.currentJobType);\r\n\t},\r\n\tonReachBottom() {\r\n\t\tif (!this.loading && !this.noMore) {\r\n\t\t\tthis.pageNum++;\r\n\t\t\tthis.getModuleList(this.currentJobType);\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tlistFilterVisitor: false,\r\n\t\t\ttownName: '茂名',\r\n\t\t\ttabList: [], // 移除静态数据，改为动态获取\r\n\t\t\tcurrentJobType: '', // 改为字符串类型\r\n\t\t\tloinTotal: 0,\r\n\t\t\tisSign: false,\r\n\t\t\tpageNum: 1,\r\n\t\t\tpageSize: 20,\r\n\t\t\tisEnd: false,\r\n\t\t\tjobItemType: 0,\r\n\t\t\tprevItemType: 0,\r\n\t\t\thomeList: [],\r\n\t\t\tshowFilter: true,\r\n\t\t\tjobHomeList: [],\r\n\t\t\tisSearchFixed: false,\r\n\t\t\tisFixed: false,\r\n\t\t\tisLoading: true,\r\n\t\t\tfirstLoaded: true,\r\n\t\t\tfixedClass: 'padding206',\r\n\t\t\tlistGroupId: 1012,\r\n\t\t\tpre_url: getApp().globalData.pre_url, // 添加pre_url变量\r\n\t\t\tclassifications: [], // 职位类型列表，格式：[{id, pid, name, sort, children}]\r\n\t\t\tsortRules: [\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '',\r\n\t\t\t\t\tvalue: '默认'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '2',\r\n\t\t\t\t\tvalue: '离我近'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '3',\r\n\t\t\t\t\tvalue: '最新'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tareas: [\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 0,\r\n\t\t\t\t\ttownId: 0,\r\n\t\t\t\t\tareaName: '不限'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 1806,\r\n\t\t\t\t\ttownId: 205,\r\n\t\t\t\t\tareaName: '茂南区'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 1809,\r\n\t\t\t\t\ttownId: 205,\r\n\t\t\t\t\tareaName: '高州市'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 1810,\r\n\t\t\t\t\ttownId: 205,\r\n\t\t\t\t\tareaName: '化州市'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 1811,\r\n\t\t\t\t\ttownId: 205,\r\n\t\t\t\t\tareaName: '信宜市'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tareaId: 2939,\r\n\t\t\t\t\ttownId: 205,\r\n\t\t\t\t\tareaName: '电白区'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tclearingForms: [\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '99',\r\n\t\t\t\t\tvalue: '不限'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '1',\r\n\t\t\t\t\tvalue: '日结'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '2',\r\n\t\t\t\t\tvalue: '周结'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '3',\r\n\t\t\t\t\tvalue: '月结'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '4',\r\n\t\t\t\t\tvalue: '完工结算'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: '0',\r\n\t\t\t\t\tvalue: '其他结算'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tuserSex: '',\r\n\t\t\tclassList: [],\r\n\t\t\tsearchList: [\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '服务员',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 1\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '超市零售',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 1\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '日结兼职',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '日结',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '寒假工',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '夜班',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '配音',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '奶茶',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '剪辑',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '发传单',\r\n\t\t\t\t\thot: 0,\r\n\t\t\t\t\tsourceType: 2\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tshowGuide: false,\r\n\t\t\tcommandFilterData: {\r\n\t\t\t\ttype_id: '',\r\n\t\t\t\tclearingForms: '',\r\n\t\t\t\tareaIds: '',\r\n\t\t\t\tsexRequire: '',\r\n\t\t\t\tsortRules: ''\r\n\t\t\t},\r\n\t\t\tcontrolPop: '',\r\n\t\t\thostExistsJobIds: '',\r\n\t\t\tclearWordChange: false,\r\n\t\t\tcollectImage: '',\r\n\t\t\tcollectContentId: '',\r\n\t\t\tcollectVisible: false,\r\n\t\t\tnewUserTemplate: '',\r\n\t\t\tisCollectDialog: false,\r\n\t\t\tuniverseParam: {},\r\n\t\t\trefreshLocation: 17,\r\n\t\t\tfirstPageJobNum: 10,\r\n\t\t\twaitRenderList: [],\r\n\t\t\tnoMore: false,\r\n\t\t\tcontentHeight: 0, // 新增：内容区域高度\r\n\t\t\tjobList: {}, // 修改为对象形式，存储各个tab的数据\r\n\t\t\tloadMoreStatus: {} // 修改为对象形式，存储各个tab的加载状态\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tgetCategoryList() {\r\n\t\t\tconst that = this;\r\n\t\t\tapp.get('apiZhaopin/getCategoryList', {}, function(res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t// 添加\"推荐\"选项\r\n\t\t\t\t\tconst recommendedOption = {\r\n\t\t\t\t\t\tkey: '0',\r\n\t\t\t\t\t\tvalue: '推荐'\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.tabList = [recommendedOption, ...res.data.map(item => ({\r\n\t\t\t\t\t\tkey: item.id.toString(),\r\n\t\t\t\t\t\tvalue: item.name\r\n\t\t\t\t\t}))];\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 默认选中\"推荐\"选项\r\n\t\t\t\t\tthat.currentJobType = recommendedOption.key;\r\n\t\t\t\t\tthat.getModuleList(recommendedOption.key);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.alert(res.msg || '获取分类列表失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttabChange(index) {\r\n\t\t\t// 添加日志\r\n\t\t\tconsole.log('tabChange triggered:', {\r\n\t\t\t\tindex,\r\n\t\t\t\tcurrentIndex: typeof index === 'object' ? index.detail.index : index,\r\n\t\t\t\toldJobItemType: this.jobItemType\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 如果传入的是事件对象，则从detail中获取index\r\n\t\t\tconst currentIndex = typeof index === 'object' ? index.detail.index : index;\r\n\t\t\t\r\n\t\t\tif (this.jobItemType === currentIndex) return;\r\n\t\t\t\r\n\t\t\t// 更新数据\r\n\t\t\tthis.jobItemType = currentIndex;\r\n\t\t\tthis.currentJobType = this.tabList[currentIndex].key;\r\n\t\t\tthis.pageNum = 1;\r\n\t\t\tthis.homeList = [];\r\n\t\t\tthis.getModuleList(this.currentJobType);\r\n\t\t\t\r\n\t\t\t// 强制更新组件\r\n\t\t\tthis.$forceUpdate();\r\n\t\t},\r\n\t\tgetModuleList: function(params) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\t// 如果传入的是字符串（categoryId），转换为对象格式\r\n\t\t\tif (typeof params === 'string') {\r\n\t\t\t\tparams = {\r\n\t\t\t\t\tcategory_id: params\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst requestParams = {\r\n\t\t\t\tpage: that.pageNum || 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\t...params,\r\n\t\t\t\tkeyword: that.searchKeyword || ''\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tconsole.log('请求参数:', requestParams);\r\n\t\t\t\r\n\t\t\tapp.get('apiZhaopin/getPositionList', requestParams, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1 && res.data.list) {\r\n\t\t\t\t\t// 处理返回的数据\r\n\t\t\t\t\tconst processedList = res.data.list.map(item => ({\r\n\t\t\t\t\t\tpartJobId: item.id,\r\n\t\t\t\t\t\ttitle: item.title,\r\n\t\t\t\t\t\ttitleSimple: item.title,\r\n\t\t\t\t\t\tsalary: item.salary,\r\n\t\t\t\t\t\tcompanyName: item.company?.name || '',\r\n\t\t\t\t\t\tlogo: item.company?.logo || 'https://qiniu-image.qtshe.com/company_logo_default.png',\r\n\t\t\t\t\t\taddressDetail: item.address || '',\r\n\t\t\t\t\t\tdistance: item.distance ? item.distance + 'km' : '',\r\n\t\t\t\t\t\tjobLineType: 1,\r\n\t\t\t\t\t\tcategory: 1,\r\n\t\t\t\t\t\tentryCount: item.views || 0,\r\n\t\t\t\t\t\tcompanyType: {\r\n\t\t\t\t\t\t\tkey: '1',\r\n\t\t\t\t\t\t\tvalue: '企业'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlabelList: {\r\n\t\t\t\t\t\t\tserviceLabels: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tlabelId: 61,\r\n\t\t\t\t\t\t\t\t\tlabelName: '企业认证',\r\n\t\t\t\t\t\t\t\t\tlabelStyle: '{\"id\":10,\"icon\":\"\",\"color\":\"#72AAFA\",\"borderColor\":\"#72AAFA\",\"background\":\"#FFFFFF\"}'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\tdescLabels: Object.entries(item.formatted_options || {}).map(([type, tags]) => \r\n\t\t\t\t\t\t\t\ttags.map(tag => ({\r\n\t\t\t\t\t\t\t\t\tlabelId: Math.random(),\r\n\t\t\t\t\t\t\t\t\tlabelName: tag,\r\n\t\t\t\t\t\t\t\t\tlabelStyle: '{\"id\":6,\"icon\":\"\",\"color\":\"#FA5555\",\"borderColor\":\"#FEEEEE\",\"background\":\"#FEEEEE\"}'\r\n\t\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\t).flat()\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlistStyle: 1\r\n\t\t\t\t\t}));\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新列表数据\r\n\t\t\t\t\tif (that.pageNum === 1) {\r\n\t\t\t\t\t\tthat.homeList = processedList;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.homeList = [...that.homeList, ...processedList];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新加载状态\r\n\t\t\t\t\tthat.noMore = processedList.length < 10;\r\n\t\t\t\t\tthat.firstLoaded = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('获取职位列表成功:', that.homeList);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.alert(res.msg || '获取职位列表失败');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tfilterChange(data) {\r\n\t\t\tthis.commandFilterData = data.detail;\r\n\t\t\tconst params = {\r\n\t\t\t\t...this.commandFilterData,\r\n\t\t\t\tcategory_id: this.tabList[this.jobItemType].key\r\n\t\t\t};\r\n\t\t\tthis.pageNum = 1;\r\n\t\t\tthis.getModuleList(params);\r\n\t\t},\r\n\t\tbtFresh() {\r\n\t\t\tthis.pageNum = 1;\r\n\t\t\tthis.getModuleList(this.tabList[this.jobItemType].key);\r\n\t\t},\r\n\t\tlocationHandle() {\r\n\t\t\tthis.showGuide = false;\r\n\t\t},\r\n\t\tgetTypeList(pid = -1) {\r\n\t\t\tconst that = this;\r\n\t\t\tapp.get('apiZhaopin/getTypeList', { pid }, function(res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t// 转换数据结构，保持原有字段不变，添加组件所需字段\r\n\t\t\t\t\tconst formattedData = res.data.map(item => ({\r\n\t\t\t\t\t\t...item, // 保留原有所有字段\r\n\t\t\t\t\t\tclassificationId: item.id,\r\n\t\t\t\t\t\tsecondClassifications: (item.children || []).map(child => ({\r\n\t\t\t\t\t\t\t...child, // 保留原有所有字段\r\n\t\t\t\t\t\t\tclassificationId: child.id,\r\n\t\t\t\t\t\t\tparentId: child.pid\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t}));\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 添加\"全部\"选项\r\n\t\t\t\t\tconst allOption = {\r\n\t\t\t\t\t\tid: 0,\r\n\t\t\t\t\t\tpid: 0,\r\n\t\t\t\t\t\tname: '全部',\r\n\t\t\t\t\t\tsort: 0,\r\n\t\t\t\t\t\tclassificationId: 0,\r\n\t\t\t\t\t\tsecondClassifications: []\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.classifications = [allOption, ...formattedData];\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.alert(res.msg || '获取职位类型失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 修改 handleSwiperChange 方法\r\n\t\thandleSwiperChange(e) {\r\n\t\t\tconst index = e.detail.current;\r\n\t\t\tconsole.log('handleSwiperChange:', {\r\n\t\t\t\tindex,\r\n\t\t\t\tcurrentJobItemType: this.jobItemType\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 避免重复触发\r\n\t\t\tif (this.jobItemType === index) return;\r\n\t\t\t\r\n\t\t\t// 直接调用 tabChange 方法\r\n\t\t\tthis.tabChange(index);\r\n\t\t},\r\n\t\t// 新增：计算内容区域高度\r\n\t\tcalculateContentHeight() {\r\n\t\t\tconst query = uni.createSelectorQuery().in(this)\r\n\t\t\tquery.select('.container').boundingClientRect(data => {\r\n\t\t\t\tconst windowHeight = uni.getSystemInfoSync().windowHeight\r\n\t\t\t\tthis.contentHeight = windowHeight - data.top\r\n\t\t\t}).exec()\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.calculateContentHeight()\r\n\t\tthis.tabChange(0)\r\n\t},\r\n\twatch: {\r\n\t\tjobItemType(newVal) {\r\n\t\t\tconsole.log('jobItemType changed:', newVal);\r\n\t\t\t// 强制更新组件\r\n\t\t\tthis.$forceUpdate();\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground-color: #f5f5f5;\r\n\toverflow: hidden;\r\n\t.select-job-area {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\t.school-life-job-list {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\toverflow-y: auto;\r\n\t\t\toverflow-x: hidden;\r\n\t\t\tpadding-bottom: 100rpx;\r\n\t\t\tpadding-top: 10rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t.noneList {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 400rpx;\r\n\t\t\t\t\theight: 400rpx;\r\n\t\t\t\t}\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.tab-content {\r\n\tflex: 1;\r\n\twidth: 100%;\r\n}\r\n\r\n.scroll-content {\r\n\theight: 100%;\r\n}\r\n\r\n.job-list {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.swiper-container {\r\n\twidth: 100%;\r\n\tbackground-color: #f5f5f5;\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115059800\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}