{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?a1db", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?fb1e", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?c252", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?fca0", "uni-app:///yuyue/packageorderdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?a2d1", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageorderdetail.vue?c368"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "themeColor", "themeColorRgb", "orderId", "orderDetail", "loading", "refunding", "refundReason", "refundAmount", "onLoad", "console", "app", "uni", "title", "icon", "setTimeout", "methods", "getOrderDetail", "that", "order_id", "payTime", "payTimeStamp", "validDays", "newExpiresTime", "currentTime", "String", "expiresTime", "remainServices", "statusText", "item", "service_id", "service_name", "service_pic", "buy_times", "remain_times", "fixImageUrl", "url", "canUseItem", "gotoUseService", "copyText", "gotoPay", "payorderid", "orderid", "type", "success", "fail", "cancelOrder", "deleteOrder", "canRefundDetail", "canDelete", "applyRefund", "closeRefundPopup", "confirmRefund", "done", "money", "reason"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,2BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AACsE;AACL;AACa;;;AAG9E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAuwB,CAAgB,kxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8H3xB;EACAC;IACA;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;MACAC;IACA;MACAA;IACA;IAEA;MACA;MACA;IACA;MACA;MACA;QACAC;UACAA;QACA;MACA;QACAD;QACAE;UACAC;UACAC;QACA;QACAC;UACAH;QACA;MACA;MACA;IACA;IAEAA;MACAC;IACA;EACA;EACA;EACA;EACA;EACA;EACAG;IACAC;MACA;MACAC;MAEA;MACA;QACAR;QACAQ;QACAN;UACAC;UACAC;QACA;QACA;MACA;MAEAH;QAAAQ;MAAA;QACAD;QACA;UACAR;;UAEA;UACA;;UAEA;UACA;YACA;YACA;;YAEA;YACA,0BACAV,2BACAA;;YAEA;YACA;cACAU;;cAEA;cACA;cACA;cACAV;cAEAU;gBACAU;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;;YAEA;YACA;cACA;cACAxB,4DACAyB,2DACAA,sDACAA,uDACAA,yDACAA;YACA;;YAEA;YACA;cACAzB;YACA;cACAA;YACA;cACAA;YACA;YAEAU;cACAgB;cACAF;cACAG;cACAC;YACA;UACA;;UAEA;UACA;YACA5B;UACA;;UAEA;UACA;YACAA;UACA;;UAEA;UACA;YACAA;cACA,uCACA6B;gBACA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cAAA;YAEA;UACA;UAEAxB;UACAQ;QACA;UACAP;YACAA;UACA;QACA;MACA;QACAO;QACAP;UACAA;QACA;MACA;IACA;IAEA;IACAwB;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;QACAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;QACA3B;QACA;MACA;;MAEA;MACA;QACA;QACAA;QACA;MACA;MAEA;;MAEA;MACAA;IACA;IAEA;IACA4B;MACA;MACA5B;IACA;IAEA;IACA6B;MAAA;MACA;MACA;QACA7B;QACA;MACA;MACAA;QACA8B;QACAC;QACAC;QACAC;UACAjC;UACA;QACA;;QACAkC;UACAlC;QACA;MACA;IACA;IACAmC;MACA;MACA;MACAnC;QACAA;QACAA;UAAAQ;QAAA;UACAR;UACA;YACAA;cACAO;YACA;UACA;YACAP;UACA;QACA;MACA;IACA;IACAoC;MACA;MACA;MACApC;QACAA;QACAA;UAAAQ;QAAA;UACAR;UACA;YACAA;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAqC;MACA;MACA;MACA,wBACA,0CACA,0CACA;IACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACAvC;QACA;MACA;MAEA;MACA;IACA;IACAwC;MACA;IACA;IACA;IACAC;MACA;;MAEA;MACAC;MAEA;MACA;MACAnC;MAEA;MACAP;;MAEA;MACAA;QACA+B;QACAY;QAAA;QACAC;MACA;QACA5C;QACAO;QACA;UACAP;YACAO;UACA;QACA;UACAP;QACA;MACA;QACAA;QACAO;QACAP;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7dA;AAAA;AAAA;AAAA;AAAulC,CAAgB,mkCAAG,EAAC,C;;;;;;;;;;;ACA3mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packageorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packageorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packageorderdetail.vue?vue&type=template&id=8711a36e&\"\nvar renderjs\nimport script from \"./packageorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./packageorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packageorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packageorderdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderdetail.vue?vue&type=template&id=8711a36e&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    !_vm.loading && _vm.orderDetail.id\n      ? _vm.orderDetail.items && _vm.orderDetail.items.length > 0\n      : null\n  var g1 =\n    !_vm.loading && _vm.orderDetail.id && g0\n      ? _vm.orderDetail.items.length\n      : null\n  var l0 =\n    !_vm.loading && _vm.orderDetail.id && g0\n      ? _vm.__map(_vm.orderDetail.items, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.fixImageUrl(item.service_pic || item.product_pic)\n          var m1 = _vm.canUseItem(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g2 =\n    !_vm.loading && _vm.orderDetail.id\n      ? _vm.orderDetail.use_records && _vm.orderDetail.use_records.length > 0\n      : null\n  var m2 =\n    !_vm.loading && _vm.orderDetail.id\n      ? _vm.canRefundDetail(\n          _vm.orderDetail.status,\n          _vm.orderDetail.refund_status\n        )\n      : null\n  var m3 =\n    !_vm.loading && _vm.orderDetail.id\n      ? _vm.canDelete(_vm.orderDetail.status)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderdetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\" v-if=\"!loading && orderDetail.id\">\n\t\t<!-- 订单状态与基本信息 -->\n\t\t<view class=\"status-section\" :style=\"{background: 'linear-gradient(90deg,' + themeColor + ' 0%,rgba(' + themeColorRgb + ',0.8) 100%)'}\">\n\t\t\t<view class=\"status-text\">{{ orderDetail.status_text }}</view>\n\t\t\t<view class=\"status-desc\" v-if=\"orderDetail.status === 0\">请在指定时间内完成支付</view>\n\t\t\t<view class=\"status-desc\" v-if=\"orderDetail.status === 1 && orderDetail.expires_time_format\">有效期至: {{ orderDetail.expires_time_format }}</view>\n\t\t\t<!-- 根据订单状态显示不同提示 -->\n\t\t\t<view class=\"status-desc\" v-if=\"orderDetail.status === 1 && orderDetail.status_text === '已过期'\">此套餐已过期，无法使用</view>\n\t\t\t<view class=\"status-desc\" v-if=\"orderDetail.status === 1 && !orderDetail.expires_time_format && orderDetail.status_text !== '已过期'\">\n\t\t\t\t套餐有效期: {{ orderDetail.valid_days || 365 }}天\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 套餐包含项目 -->\n\t\t<view class=\"items-card\" v-if=\"orderDetail.items && orderDetail.items.length > 0\">\n\t\t\t<view class=\"card-title\">套餐包含服务 (共 {{ orderDetail.items.length }} 项)</view>\n\t\t\t<view class=\"service-item-detail\" v-for=\"(item, index) in orderDetail.items\" :key=\"index\">\n\t\t\t\t<image class=\"service-pic-detail\" :src=\"fixImageUrl(item.service_pic || item.product_pic)\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"service-info-detail\">\n\t\t\t\t\t<view class=\"service-name-detail\">{{ item.service_name || item.product_name }}</view>\n\t\t\t\t\t<view class=\"service-times-detail\">\n\t\t\t\t\t\t<text>总次数: {{ item.buy_times || item.total_num }}</text>\n\t\t\t\t\t\t<text class=\"remain-detail\" :style=\"(item.remain_times || item.remain_num) <= 0 ? 'color:#ccc' : 'color:#ff9900'\">剩余: {{ item.remain_times || item.remain_num }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<button v-if=\"canUseItem(item)\" class=\"use-btn\" :style=\"{background: themeColor}\" size=\"mini\" @tap=\"gotoUseService(item.service_id || item.product_id)\">去使用</button>\n\t\t\t\t<button v-else-if=\"orderDetail.status === 1 && (item.remain_times || item.remain_num) <= 0\" class=\"use-btn disabled\" size=\"mini\">已用完</button>\n\t\t\t\t<button v-else-if=\"orderDetail.status === 1 && orderDetail.status_text === '已过期'\" class=\"use-btn disabled\" size=\"mini\">已过期</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单信息 -->\n\t\t<view class=\"info-card\">\n\t\t\t<view class=\"card-title\">订单信息</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">订单编号:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.ordernum || orderDetail.id }}</text>\n\t\t\t\t<button class=\"copy-btn\" size=\"mini\" @tap=\"copyText(orderDetail.ordernum || orderDetail.id)\">复制</button>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">下单时间:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.createtime }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.pay_time\">\n\t\t\t\t<text class=\"label\">支付时间:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.pay_time }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">联系方式:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.linkman }} {{ orderDetail.tel }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">支付金额:</text>\n\t\t\t\t<text class=\"value\" :style=\"{color: themeColor}\">￥{{ orderDetail.total_price }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.remark\">\n\t\t\t\t<text class=\"label\">订单备注:</text>\n\t\t\t\t<text class=\"value remark-value\">{{ orderDetail.remark }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 使用记录 -->\n\t\t<view class=\"records-card\" v-if=\"orderDetail.use_records && orderDetail.use_records.length > 0\">\n\t\t\t<view class=\"card-title\">使用记录</view>\n\t\t\t<view class=\"record-item\" v-for=\"(record, index) in orderDetail.use_records\" :key=\"index\">\n\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t<view class=\"record-service-name\">{{ record.product_name || '未知服务' }}</view>\n\t\t\t\t\t<view class=\"record-time\">{{ record.use_time }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"record-status\">已使用</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 退款信息 -->\n\t\t<view class=\"refund-card\" v-if=\"orderDetail.refund_status && orderDetail.refund_status > 0\">\n\t\t\t<view class=\"card-title\">退款信息</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">退款状态:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.refund_status_text }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.refund_reason\">\n\t\t\t\t<text class=\"label\">退款原因:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.refund_reason }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.refund_time\">\n\t\t\t\t<text class=\"label\">申请时间:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.refund_time }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.refund_check_time\">\n\t\t\t\t<text class=\"label\">审核时间:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.refund_check_time }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.refund_check_reason\">\n\t\t\t\t<text class=\"label\">审核备注:</text>\n\t\t\t\t<text class=\"value\">{{ orderDetail.refund_check_reason }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" v-if=\"orderDetail.refund_money\">\n\t\t\t\t<text class=\"label\">退款金额:</text>\n\t\t\t\t<text class=\"value\" :style=\"{color: themeColor}\">￥{{ orderDetail.refund_money }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"bottom-actions\">\n\t\t\t<button v-if=\"orderDetail.status === 0\" class=\"action-button plain\" @tap=\"cancelOrder(orderDetail.id)\">取消订单</button>\n\t\t\t<button v-if=\"orderDetail.status === 0\" class=\"action-button primary\" :style=\"{background: themeColor}\" @tap=\"gotoPay(orderDetail.payorderid, orderDetail.id)\">立即支付</button>\n\t\t\t<button v-if=\"canRefundDetail(orderDetail.status, orderDetail.refund_status)\" class=\"action-button plain\" @tap=\"applyRefund(orderDetail.id, orderDetail.total_price)\">申请退款</button>\n\t\t\t<button v-if=\"canDelete(orderDetail.status)\" class=\"action-button plain\" @tap=\"deleteOrder(orderDetail.id)\">删除订单</button>\n\t\t</view>\n\n\t\t<!-- 退款原因输入弹窗 -->\n\t\t<uni-popup ref=\"refundPopup\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"申请退款\" placeholder=\"请输入退款原因(选填)\" :before-close=\"true\" @confirm=\"confirmRefund\" @close=\"closeRefundPopup\"></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t</view>\n\t<view class=\"loading-container\" v-else-if=\"loading\">\n\t\t<text>加载中...</text>\n\t</view>\n\t<view class=\"empty-container\" v-else>\n\t\t<text>未找到订单信息</text>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\t// 避免直接引用可能未初始化的全局app对象\n\t\treturn {\n\t\t\tthemeColor: '#2979ff', // 主题色默认值，将在onLoad中正确初始化\n\t\t\tthemeColorRgb: '41,121,255', // 主题色RGB默认值\n\t\t\torderId: null,\n\t\t\torderDetail: {},\n\t\t\tloading: true,\n\t\t\trefunding: false, // 防止重复提交退款\n\t\t\trefundReason: '', // 存储退款原因\n\t\t\trefundAmount: 0 // 存储退款金额\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 初始化全局app对象和主题色\n\t\tconst app = getApp();\n\t\tif (app && app.globalData && app.globalData.config && app.globalData.config.t) {\n\t\t\tthis.themeColor = app.globalData.config.t('color1');\n\t\t\tthis.themeColorRgb = app.globalData.config.t('color1rgb');\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_001] 主题色初始化成功');\n\t\t} else {\n\t\t\tconsole.warn('2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_002] 无法获取主题色配置，使用默认值');\n\t\t}\n\t\t\n\t\tif (options && options.order_id) {\n\t\t\tthis.orderId = options.order_id;\n\t\t\tthis.getOrderDetail();\n\t\t} else {\n\t\t\tconst app = getApp();\n\t\t\tif (app && app.error && app.goback) {\n\t\t\t\tapp.error('缺少订单ID', function() {\n\t\t\t\t\tapp.goback();\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconsole.error('2025-01-03 22:55:53,565-ERROR-[packageorderdetail][onLoad_003] app对象未初始化');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '缺少订单ID',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t\tthis.loading = false;\n\t\t}\n\t\t\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: '订单详情'\n\t\t});\n\t},\n\t// onShow(){\n\t// \t// 如果需要返回刷新\n\t// \tif(this.orderId) this.getOrderDetail();\n\t// },\n\tmethods: {\n\t\tgetOrderDetail() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\tconst app = getApp();\n\t\t\tif (!app || !app.post) {\n\t\t\t\tconsole.error('2025-01-03 22:55:53,565-ERROR-[packageorderdetail][getOrderDetail_001] app对象未初始化或post方法不存在');\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取订单详情失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tapp.post('ApiYuyuePackage/getPackageOrderDetail', { order_id: that.orderId }, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1 && res.data) {\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_002] 获取订单数据成功', res.data);\n\t\t\t\t\t\n\t\t\t\t\t// 处理数据\n\t\t\t\t\tlet data = res.data;\n\t\t\t\t\t\n\t\t\t\t\t// 修复过期时间计算问题\n\t\t\t\t\tif (data.status === 1) {\n\t\t\t\t\t\t// 获取当前时间戳（秒）\n\t\t\t\t\t\tconst now = Math.floor(Date.now()/1000);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查expires_time是否明显错误（为0，或者小于now但订单状态是1，或者日期太早如1971年）\n\t\t\t\t\t\tconst isExpireTimeInvalid = \n\t\t\t\t\t\t\tdata.expires_time === 0 || \n\t\t\t\t\t\t\t(data.expires_time < now && data.expires_time < 1672531200); // 2023-01-01的时间戳\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 支付后但expires_time无效的情况，说明后端未设置过期时间或计算错误，使用pay_time+valid_days计算\n\t\t\t\t\t\tif (isExpireTimeInvalid && data.pay_time) {\n\t\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_003] 检测到无效的过期时间:', data.expires_time);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 从支付时间计算过期时间\n\t\t\t\t\t\t\tconst payTimeStamp = new Date(data.pay_time.replace(/-/g, '/')).getTime();\n\t\t\t\t\t\t\tconst validDays = data.valid_days || 365; // 默认365天\n\t\t\t\t\t\t\tdata.expires_time = Math.floor(payTimeStamp/1000) + (validDays * 86400);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_004] 自动计算新的过期时间:', {\n\t\t\t\t\t\t\t\tpayTime: data.pay_time, \n\t\t\t\t\t\t\t\tpayTimeStamp: Math.floor(payTimeStamp/1000),\n\t\t\t\t\t\t\t\tvalidDays: validDays,\n\t\t\t\t\t\t\t\tnewExpiresTime: data.expires_time,\n\t\t\t\t\t\t\t\tcurrentTime: now\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 格式化过期时间显示(独立于前面的逻辑，确保始终有格式化显示)\n\t\t\t\t\t\tif (data.expires_time > 0) {\n\t\t\t\t\t\t\tconst expireDate = new Date(data.expires_time * 1000);\n\t\t\t\t\t\t\tdata.expires_time_format = expireDate.getFullYear() + '-' + \n\t\t\t\t\t\t\t\tString(expireDate.getMonth() + 1).padStart(2, '0') + '-' + \n\t\t\t\t\t\t\t\tString(expireDate.getDate()).padStart(2, '0') + ' ' +\n\t\t\t\t\t\t\t\tString(expireDate.getHours()).padStart(2, '0') + ':' +\n\t\t\t\t\t\t\t\tString(expireDate.getMinutes()).padStart(2, '0') + ':' +\n\t\t\t\t\t\t\t\tString(expireDate.getSeconds()).padStart(2, '0');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 重新判断状态文本\n\t\t\t\t\t\tif (data.expires_time > 0 && now > data.expires_time) {\n\t\t\t\t\t\t\tdata.status_text = '已过期';\n\t\t\t\t\t\t} else if (data.remain_services <= 0) {\n\t\t\t\t\t\t\tdata.status_text = '已用完';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdata.status_text = '可使用';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_005] 订单状态判断:', {\n\t\t\t\t\t\t\texpiresTime: data.expires_time,\n\t\t\t\t\t\t\tcurrentTime: now,\n\t\t\t\t\t\t\tremainServices: data.remain_services,\n\t\t\t\t\t\t\tstatusText: data.status_text\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 修复可能的图片路径问题\n\t\t\t\t\tif (data.package_pic && data.package_pic.indexOf('https://localhost') === 0) {\n\t\t\t\t\t\tdata.package_pic = data.package_pic.replace('https://localhost', '');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 双重URL问题修复\n\t\t\t\t\tif (data.package_pic && data.package_pic.indexOf('https://localhosthttps://') === 0) {\n\t\t\t\t\t\tdata.package_pic = data.package_pic.replace('https://localhost', '');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理服务项的字段名称映射\n\t\t\t\t\tif (data.items && data.items.length > 0) {\n\t\t\t\t\t\tdata.items = data.items.map(item => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t\t// 将后端返回的字段名映射到组件中使用的字段名\n\t\t\t\t\t\t\t\tservice_id: item.product_id,\n\t\t\t\t\t\t\t\tservice_name: item.product_name || '未命名服务',\n\t\t\t\t\t\t\t\tservice_pic: that.fixImageUrl(item.product_pic),\n\t\t\t\t\t\t\t\tbuy_times: item.total_num || 0,\n\t\t\t\t\t\t\t\tremain_times: item.remain_num || 0\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_006] 数据处理完成', data);\n\t\t\t\t\tthat.orderDetail = data;\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '获取订单详情失败', function() {\n\t\t\t\t\t\tapp.goback();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, function() {\n\t\t\t\tthat.loading = false;\n\t\t\t\tapp.error('请求失败', function() {\n\t\t\t\t\tapp.goback();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 修复图片URL\n\t\tfixImageUrl(url) {\n\t\t\tif (!url) return '/static/img/goods-default.png';\n\t\t\t\n\t\t\t// 修复双重URL问题\n\t\t\tif (url.indexOf('https://localhosthttps://') === 0) {\n\t\t\t\turl = url.replace('https://localhost', '');\n\t\t\t}\n\t\t\t\n\t\t\t// 修复localhost前缀\n\t\t\tif (url.indexOf('https://localhost') === 0) {\n\t\t\t\turl = url.replace('https://localhost', '');\n\t\t\t}\n\t\t\t\n\t\t\treturn url;\n\t\t},\n\n\t\t// 判断服务项是否可用\n\t\tcanUseItem(item){\n\t\t\t// 订单状态为可使用(1) 且 服务项剩余次数>0\n\t\t\t// 以及确认状态文本不是\"已过期\"\n\t\t\tconst notExpired = this.orderDetail.status_text !== '已过期';\n\t\t\treturn this.orderDetail.status === 1 && item.remain_times > 0 && notExpired;\n\t\t},\n\n\t\t// 去使用服务\n\t\tgotoUseService(productId) {\n\t\t\tvar that = this;\n\t\t\t// 检查服务是否还有剩余次数\n\t\t\tconst serviceItem = that.orderDetail.items.find(item => item.service_id === productId);\n\t\t\tif(!serviceItem || serviceItem.remain_times <= 0){\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.error('该服务已无剩余次数');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果订单已过期，不允许使用\n\t\t\tif(that.orderDetail.status_text === '已过期'){\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.error('套餐已过期，无法使用');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst app = getApp();\n\t\t\t\n\t\t\t// 跳转到套餐预约页面\n\t\t\tapp.goto('/yuyue/packageappoint?package_order_id=' + that.orderId + '&product_id=' + productId);\n\t\t},\n\n\t\t// 复制文本\n\t\tcopyText(text) {\n\t\t\tconst app = getApp();\n\t\t\tapp.copy(text);\n\t\t},\n\n\t\t// 支付、取消、删除逻辑 (与列表页类似)\n\t\tgotoPay(payorderid, orderid){\n\t\t\tconst app = getApp();\n\t\t\tif(!payorderid) {\n\t\t\t\tapp.error('支付单号不存在');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tapp.payorder({\n\t\t\t\tpayorderid: payorderid,\n\t\t\t\torderid: orderid,\n\t\t\t\ttype: 'yuyue_package',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tapp.success('支付成功');\n\t\t\t\t\tthis.getOrderDetail(); // 刷新详情\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tapp.error('支付失败或取消');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tcancelOrder(orderId){\n\t\t\tvar that = this;\n\t\t\tconst app = getApp();\n\t\t\tapp.confirm('确定要取消该订单吗？', function(){\n\t\t\t\tapp.showLoading('处理中...');\n\t\t\t\tapp.post('ApiYuyuePackage/cancelOrder', {order_id: orderId}, function(res){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(res.status == 1){\n\t\t\t\t\t\tapp.success('取消成功', function(){\n\t\t\t\t\t\t\tthat.getOrderDetail(); // 刷新详情\n\t\t\t\t\t\t});\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(res.msg || '取消失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tdeleteOrder(orderId){\n\t\t\tvar that = this;\n\t\t\tconst app = getApp();\n\t\t\tapp.confirm('确定要删除该订单吗？删除后不可恢复。', function(){\n\t\t\t\tapp.showLoading('处理中...');\n\t\t\t\tapp.post('ApiYuyuePackage/deleteOrder', {order_id: orderId}, function(res){\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(res.status == 1){\n\t\t\t\t\t\tapp.success('删除成功', function(){\n\t\t\t\t\t\t\tapp.goback(); // 删除成功后返回上一页\n\t\t\t\t\t\t});\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(res.msg || '删除失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\n\t\t// 判断是否可申请退款 (业务逻辑复杂，具体条件需后端确认)\n\t\tcanRefundDetail(status, refund_status){\n\t\t\t// 修改判断逻辑，增加对状态文本的判断\n\t\t\t// 已支付(1)，未发起过退款(refund_status为0或null)，且不是已过期状态，且不是已用完状态\n\t\t\treturn status === 1 \n\t\t\t\t&& (!refund_status || refund_status === 0) \n\t\t\t\t&& this.orderDetail.status_text !== '已过期'\n\t\t\t\t&& this.orderDetail.status_text !== '已用完'; // 新增判断条件\n\t\t},\n\t\t// 判断是否可删除 (通常是已取消、已失效、已退款等终态)\n\t\tcanDelete(status){\n\t\t\t// 增加对status_text的判断，如果是\"已过期\"也可以删除\n\t\t\treturn status === 2 || status === 5 || this.orderDetail.status_text === '已过期'; \n\t\t},\n\n\t\t// 申请退款\n\t\tapplyRefund(orderId, amount) {\n\t\t\t// 检查是否已过期\n\t\t\tif(this.orderDetail.status_text === '已过期'){\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.error('套餐已过期，无法申请退款');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.refundAmount = amount; // 存储需要退款的金额\n\t\t\tthis.$refs.refundPopup.open();\n\t\t},\n\t\tcloseRefundPopup(){\n\t\t\tthis.$refs.refundPopup.close();\n\t\t},\n\t\t// 确认提交退款申请\n\t\tconfirmRefund(done, value){\n\t\t\tthis.refundReason = value || '用户申请退款'; // 获取输入的退款原因\n\t\t\t\n\t\t\t// 关闭输入框\n\t\t\tdone();\n\n\t\t\tvar that = this;\n\t\t\tif(that.refunding) return;\n\t\t\tthat.refunding = true;\n\t\t\t\n\t\t\tconst app = getApp();\n\t\t\tapp.showLoading('提交中...');\n\n\t\t\t// 调用POST接口提交退款申请\n\t\t\tapp.post('ApiYuyuePackage/refund', {\n\t\t\t\torderid: that.orderId,\n\t\t\t\tmoney: that.refundAmount, // 使用之前获取或存储的金额\n\t\t\t\treason: that.refundReason\n\t\t\t}, function(postRes){\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tthat.refunding = false;\n\t\t\t\tif(postRes.status == 1){\n\t\t\t\t\tapp.success('退款申请已提交', function(){\n\t\t\t\t\t\tthat.getOrderDetail(); // 刷新订单详情\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(postRes.msg || '提交失败');\n\t\t\t\t}\n\t\t\t}, function(){\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tthat.refunding = false;\n\t\t\t\tapp.error('请求失败');\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tpadding-bottom: 140rpx; /* 底部操作栏高度 */\n}\n\n/* 状态区域 */\n.status-section {\n\tpadding: 30rpx;\n\tborder-radius: 16rpx;\n\tmargin-bottom: 20rpx;\n\tcolor: #fff;\n}\n\n.status-text {\n\tfont-size: 34rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n}\n\n.status-desc {\n\tfont-size: 24rpx;\n\topacity: 0.9;\n}\n\n/* 信息卡片 */\n.info-card, .items-card, .records-card, .refund-card {\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 25rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.card-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 25rpx;\n\tpadding-bottom: 15rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tmargin-bottom: 10rpx;\n}\n.info-item:last-child{\n\tmargin-bottom: 0;\n}\n\n.info-item .label {\n\twidth: 150rpx;\n\tflex-shrink: 0;\n\tcolor: #999;\n}\n\n.info-item .value {\n\tflex: 1;\n\tcolor: #333;\n}\n.info-item .remark-value{\n\twhite-space: pre-wrap; /* 保留换行 */\n}\n\n.copy-btn {\n\tmargin-left: 15rpx;\n\tpadding: 0 10rpx;\n\tfont-size: 20rpx;\n\theight: 36rpx;\n\tline-height: 36rpx;\n\tbackground-color: #f0f0f0;\n\tcolor: #666;\n\tborder: none;\n}\nbutton::after {\n    border: none;\n}\n\n/* 服务项目 */\n.service-item-detail {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx dashed #eee;\n}\n.service-item-detail:last-child {\n\tborder-bottom: none;\n}\n\n.service-pic-detail {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 8rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.service-info-detail {\n\tflex: 1;\n\toverflow: hidden;\n}\n\n.service-name-detail {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.service-times-detail {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n.service-times-detail .remain-detail {\n\tmargin-left: 20rpx;\n\tfont-weight: bold;\n}\n\n.use-btn {\n\tpadding: 0 20rpx;\n\theight: 50rpx;\n\tline-height: 50rpx;\n\tfont-size: 24rpx;\n\tcolor: #fff;\n\tborder-radius: 25rpx;\n\tmargin-left: 20rpx;\n\tborder: none;\n\tflex-shrink: 0;\n}\n.use-btn.disabled{\n\tbackground-color: #ccc !important;\n\tcolor: #fff;\n}\n\n/* 使用记录 */\n.record-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15rpx 0;\n\tborder-bottom: 1rpx dashed #eee;\n}\n.record-item:last-child {\n\tborder-bottom: none;\n}\n\n.record-info {\n\t/* flex: 1; */\n}\n\n.record-service-name {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tmargin-bottom: 5rpx;\n}\n\n.record-time {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.record-status {\n\tfont-size: 24rpx;\n\tcolor: #4CAF50; /* 绿色表示已使用 */\n\tflex-shrink: 0;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground-color: #fff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n\tpadding: 0 20rpx;\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n\tbox-sizing: border-box;\n\tpadding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */\n\theight: calc(100rpx + env(safe-area-inset-bottom));\n\tgap: 15rpx; /* 按钮间距 */\n}\n\n.action-button {\n\tpadding: 0 30rpx;\n\theight: 64rpx;\n\tline-height: 64rpx;\n\tfont-size: 26rpx;\n\tborder-radius: 32rpx;\n\tmargin: 0;\n\tborder: 1rpx solid #ccc;\n\tbackground-color: #fff;\n\tcolor: #666;\n}\n.action-button.primary {\n\tcolor: #fff;\n\tborder: none;\n}\n\n\n/* 加载和空状态 */\n.loading-container, .empty-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmin-height: 80vh;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115040993\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}