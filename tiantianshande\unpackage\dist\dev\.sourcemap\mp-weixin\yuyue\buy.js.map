{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?d753", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?9c4c", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?3284", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?d0a3", "uni-app:///yuyue/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?7c27", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy.vue?1517"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "test", "address", "totalprice", "couponvisible", "bid", "nowbid", "needaddress", "linkman", "tel", "userinfo", "latitude", "longitude", "allbuydata", "alltotalprice", "type11visible", "type11key", "regiondata", "items", "editorFormdata", "sindex", "prodata", "yydate", "yyset", "issubmit", "isdate", "timeDialogShow", "datelist", "daydate", "curTopIndex", "index", "day", "days", "dates", "num", "timeindex", "startTime", "selectDates", "timelist", "proid", "order_flow_mode", "onLoad", "console", "onPullDownRefresh", "methods", "getdata", "app", "worker_id", "that", "chooseCoupon", "coupon_money", "showCouponList", "handleClickMask", "calculatePrice", "topay", "formdata", "newformdata", "buydata", "couponrid", "frompage", "addressid", "remark", "fwtype", "editorChooseImage", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "selectFwtype", "inputLinkman", "inputTel", "chooseTime", "date", "switchTopTab", "switchDateTab", "selectDate", "hidetimeDialog", "gotopeople"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmP5wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;MACA;MACAC;IACA;IAEA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MAEAC;QACAzB;QACA0B;MACA;QACAC;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;QAEA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACApC;QACAA;QACAA;QACAA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAqC;QACA;QACArC;QACAA;QACAA;QACAA;QACA;QACA;MACA;MACA;IACA;IACAsC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAvC;MACA;MAEAkC;MACA;MACA;MACAlC;MACAkC;MACAA;IACA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAZ;MACAA;MACAA;MACAA;;MAEA;MACA;;MAEA;MACA;QACAI;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MAEA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;YACAA;YACA;UACA;UACA;YACAS;UACA;UACAC;QACA;QACAC;UACApD;UACAgB;UACAqC;UACAH;QACA;MACA;MAEA;MACA;MAEAT;MACAA;QACAa;QACAF;QACAG;QACApD;QACAC;QACAoD;QACAvC;QACAyB;QACAe;QACAtB;MACA;QACAM;QACA;UACAE;UACAF;QACA;UACA;YACAA;cACAA;YACA;UACA;YACAA;UACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACA;MACAjB;QACA3B;QACA;QACA6B;QACAA;MACA;IACA;IAAAgB;MACA;MACA;MACA;MACA;MACA;MACA;MACA7C;MACA;MACA6B;MACAA;IACA;IACA;IACAiB;MACA;MACA;MACAjB;IACA;IACAkB;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACApB;MACAA;MACAA;MACA;MACAA;MACAA;MACAF;QAAAuB;QAAA9B;MAAA;QACAS;QACAA;MACA;IACA;IACAsB;MACA;MACA;MACA;MACA;MACAtB;MACAA;MACA;MACA;MACA;MACA;MACAA;MAEAF;QAAAuB;QAAA9B;MAAA;QACAS;QACAA;MACA;IAEA;IACAuB;MACA;MACA;MACA;MACA;MACA;QACAzB;QAAA;MACA;MACAE;MACAA;MACA;MACAA;MACA;QAAAA;MAAA;MACAA;IACA;IACAwB;MACA;MACA;QACAxB;MACA;MACA;QACAF;QAAA;MACA;MACA;MACAE;MACA;IACA;IACAyB;MACA;IACA;IACAC;MACA;MACA;MACA;QACA5B;QACA;MACA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjnBA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=3f5b7206&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=3f5b7206&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var m0 =\n          buydata.fwpeople == 1 && _vm.order_flow_mode !== 1\n            ? _vm.isEmpty(buydata.fw)\n            : null\n        var m1 = buydata.leveldk_money > 0 ? _vm.t(\"会员\") : null\n        var m2 = _vm.yyset.iscoupon == 1 ? _vm.t(\"优惠券\") : null\n        var m3 =\n          _vm.yyset.iscoupon == 1 && buydata.couponCount > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m4 =\n          _vm.yyset.iscoupon == 1 && !(buydata.couponCount > 0)\n            ? _vm.t(\"优惠券\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var m5 = _vm.isload && !_vm.issubmit ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && !_vm.issubmit ? _vm.t(\"color1rgb\") : null\n  var m7 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l1 = _vm.timeDialogShow\n    ? _vm.__map(_vm.datelist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m8 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 = _vm.timeDialogShow ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        l1: l1,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view v-if=\"sindex==1\" class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\"\r\n\t\t\t\t\t:data-url=\"'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=1'\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url + '/static/img/address.png'\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\r\n\t\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"f2 flex1\">请选择您的地点</view>\r\n\t\t\t\t\t<image :src=\"pre_url + '/static/img/arrowright.png'\" class=\"f3\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\t\t\t\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t\t服务信息\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\" class=\"item flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + item.product.id\">\r\n\t\t\t\t\t\t\t\t\t<image v-if=\"item.guige.pic\" :src=\"item.guige.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t<image v-else :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text><text\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"padding-left:20rpx\"> × {{item.num}}</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"body_item\">\r\n\t\t\t\t\t\t\t<view class=\"body_title flex flex-bt\">服务方式<text class=\"body_text\">请选择服务方式</text></view>\r\n\t\t\t\t\t\t\t<view class=\"body_content\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, idx2) in fwtypelist\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"body_tag\" :class=\"item.key==sindex?'body_active':''\"\r\n\t\t\t\t\t\t\t\t\t\t@tap=\"selectFwtype\" :data-index=\"item.key\">{{item.name}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"body_item\" v-if=\"order_flow_mode !== 1\">\r\n\t\t\t\t\t\t\t<view class=\"body_title flex flex-bt\">\r\n\t\t\t\t\t\t\t\t<text>预约时间</text>\r\n\t\t\t\t\t\t\t\t<view class=\"body_data\" v-if=\"isdate\" @tap=\"chooseTime\">\r\n\t\t\t\t\t\t\t\t\t{{yydate?yydate:'请选择预约时间'}}\r\n\t\t\t\t\t\t\t\t\t<image class=\"body_detail\" :src=\"pre_url + '/static/img/arrowright.png'\" style=\"width:26rpx;height:26rpx;\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"body_data\" v-else>{{yydate}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"body_item\" v-if=\"buydata.fwpeople==1 && order_flow_mode !== 1\">\r\n\t\t\t\t\t\t\t<view class=\"body_title flex flex-bt\">\r\n\t\t\t\t\t\t\t\t<text>服务人员</text>\r\n\t\t\t\t\t\t\t\t<view class=\"body_data\" @tap=\"gotopeople\"\r\n\t\t\t\t\t\t\t\t\t:data-url=\"'selectpeople?prodata='+prodata+'&yydate='+yydate+'&sindex='+sindex+'&linkman='+linkman+'&tel='+tel\"> \r\n\t\t\t\t\t\t\t\t\t{{!isEmpty(buydata.fw)?buydata.fw.realname:'请选择人员'}}\r\n\t\t\t\t\t\t\t\t\t<image class=\"body_detail\" :src=\"pre_url + '/static/img/arrowright.png'\" style=\"width:26rpx;height:26rpx;\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent2\">\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.leveldk_money>0\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">-¥{{buydata.leveldk_money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"yyset.iscoupon==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"buydata.couponCount > 0\" class=\"f2\" @tap=\"showCouponList\" :data-bid=\"buydata.bid\">\r\n\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{background:t('color1')}\">{{buydata.couponrid!=0?buydata.couponList[buydata.couponkey].couponname:buydata.couponCount+'张可用'}}</text><text\r\n\t\t\t\t\t\t\t\t\tclass=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">服务价格</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.product_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">应付定金</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.sell_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.coupontype==3\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">计次卡</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\" style=\"color: red;\">-{{buydata.product_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in buydata.formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+buydata.bid+'_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+buydata.bid+'_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+buydata.bid+'_'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+buydata.bid+'_'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0\"> {{item.val2[buydata.editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"buydata.editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"buydata.editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"buydata.editorFormdata[idx]\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-bid=\"buydata.bid\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<view style=\"width: 100%; height:110rpx;\"></view>\r\n\t\t\t\t<view class=\"footer flex\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{alltotalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button v-if=\"issubmit\" class=\"op\" style=\"background: #999;\" >\r\n\t\t\t\t\t\t确认提交</button>\r\n\t\t\t\t\t<button v-else class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t\t\t确认提交</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url + '/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<couponlist :couponlist=\"allbuydata[bid].couponList\" :choosecoupon=\"true\"\r\n\t\t\t\t\t\t\t:selectedrid=\"allbuydata[bid].couponrid\" :bid=\"bid\" @chooseCoupon=\"chooseCoupon\">\r\n\t\t\t\t\t\t</couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t\r\n\t\t\r\n\t\t<view v-if=\"timeDialogShow\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidetimeDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择时间</text>\r\n\t\t\t\t\t<image :src=\"pre_url + '/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\**********=\"hidetimeDialog\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"order-tab\">\r\n\t\t\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t\t\t<block v-for=\"(item, index) in datelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"datetext\">{{item.weeks}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"datetext2\">{{item.date}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"after\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex daydate\">\r\n\t\t\t\t\t<block v-for=\"(item,index2) in timelist\" :key=\"index2\">\r\n\t\t\t\t\t\t<view :class=\"'date ' + ((timeindex==index2 && item.status==1) ? 'on' : '') + (item.status==0 ?'hui' : '') \"  \r\n\t\t\t\t\t\t\t@tap=\"switchDateTab\" :data-index2=\"index2\" :data-status=\"item.status\" :data-time=\"item.timeint\"> \r\n\t\t\t\t\t\t\t{{item.time}}\r\n\t\t\t\t\t\t</view>\t\t\t\t\t\t\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<button class=\"tobuy on\" :style=\"{backgroundColor:t('color1')}\" @tap=\"selectDate\">确 定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\ttest:'test',\r\n\t\t\t\taddress: [],\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tnowbid: 0,\r\n\t\t\t\tneedaddress: 1,\r\n\t\t\t\tlinkman: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tuserinfo: {},\r\n\t\t\t\tlatitude: \"\",\r\n\t\t\t\tlongitude: \"\",\r\n\t\t\t\tallbuydata: {},\r\n\t\t\t\talltotalprice: \"\",\r\n\t\t\t\ttype11visible: false,\r\n\t\t\t\ttype11key: -1,\r\n\t\t\t\tregiondata: '',\r\n\t\t\t\titems: [],\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\tsindex:'',\r\n\t\t\t\tprodata:'',\r\n\t\t\t\tyydate:'',\r\n\t\t\t\tyyset:'',\r\n\t\t\t\tissubmit:false,\r\n\t\t\t\tisdate:false,\r\n\t\t\t\ttimeDialogShow: false,\r\n\t\t\t\tdatelist:[],\r\n\t\t\t\tdaydate:[],\r\n\t\t\t\tcurTopIndex: 0,\r\n\t\t\t\tindex:0,\r\n\t\t\t\tday: -1,\r\n\t\t\t\tdays:'请选择服务时间',\r\n\t\t\t\tdates:'',\r\n\t\t\t\tnum:0,\r\n\t\t\t\ttimeindex:-1,\r\n\t\t\t\tstartTime:0,\r\n\t\t\t\tselectDates:'',\r\n\t\t\t\ttimelist:[],\r\n\t\t\t\tproid:'',\r\n\t\t\t\torder_flow_mode: 0,\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = opt;\r\n\t\t\tvar opts = app.getopts(opt);\r\n\t\t\tthis.opt.prodata = opts.prodata;\r\n\t\t\tthis.opt.worker_id = opts.worker_id ? opts.worker_id : 0;\r\n\t\t\tthis.opt.yydate = opts.yydate;\r\n\t\t\tthis.opt.frompage = opts.frompage;\r\n\t\t\tthis.yydate = opts.yydate;\r\n\t\t\t\r\n\t\t\t// 获取URL参数中的流程模式\r\n\t\t\tthis.opt.order_flow_mode = opts.order_flow_mode;\r\n\t\t\tif (this.opt.order_flow_mode) {\r\n\t\t\t\tthis.order_flow_mode = parseInt(this.opt.order_flow_mode);\r\n\t\t\t\tconsole.log(\"从URL获取流程模式:\", this.order_flow_mode);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.prodata = this.opt.prodata;\r\n\t\t\tthis.sindex = opt.sindex;\r\n\t\t\tthis.linkman = opt.linkman;\r\n\t\t\tthis.tel = opt.tel;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.get('ApiYuyue/buy', {\r\n\t\t\t\t\tprodata: that.opt.prodata,\r\n\t\t\t\t\tworker_id:that.opt.worker_id,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t\tif(!that.linkman ){\r\n\t\t\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!that.tel ){\r\n\t\t\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\t\tthat.yyset = res.yyset;\r\n\t\t\t\t\tthat.allbuydata = res.allbuydata;\r\n\t\t\t\t\tthat.fwtypelist = res.fwtypelist;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保URL参数的优先级高于接口返回值\r\n\t\t\t\t\tif (!that.opt.order_flow_mode) {\r\n\t\t\t\t\t\tthat.order_flow_mode = res.order_flow_mode || 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(!that.sindex ){\r\n\t\t\t\t\t\tthat.sindex = that.fwtypelist[0].key;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.isdate = res.isdate\r\n\t\t\t\t\tthat.datelist = res.datelist;\r\n\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseCoupon: function(e) {\r\n\t\t\t\tvar allbuydata = this.allbuydata;\r\n\t\t\t\tvar bid = e.bid;\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t\tvar couponkey = e.key;\r\n\t\t\t\tif (couponrid == allbuydata[bid].couponrid) {\r\n\t\t\t\t\tallbuydata[bid].couponkey = 0;\r\n\t\t\t\t\tallbuydata[bid].couponrid = 0;\r\n\t\t\t\t\tallbuydata[bid].coupontype = 1;\r\n\t\t\t\t\tallbuydata[bid].coupon_money = 0;\r\n\t\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar couponList = allbuydata[bid].couponList;\r\n\t\t\t\t\tvar coupon_money = couponList[couponkey]['money'];\r\n\t\t\t\t\tvar coupontype = couponList[couponkey]['type'];\r\n\t\t\t\t\tif(coupontype == 10){\r\n\t\t\t\t\t\tcoupon_money = allbuydata[bid].sell_price * (100 - couponList[couponkey]['discount']) * 0.01;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tallbuydata[bid].couponkey = couponkey;\r\n\t\t\t\t\tallbuydata[bid].couponrid = couponrid;\r\n\t\t\t\t\tallbuydata[bid].coupontype = coupontype;\r\n\t\t\t\t\tallbuydata[bid].coupon_money = coupon_money;\r\n\t\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tshowCouponList: function(e) {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar address = that.address;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar alltotalprice = 0;\r\n\t\t\t\tvar needaddress = 0;\r\n\t\t\t\tfor (var k in allbuydata) {\r\n\t\t\t\t\tvar product_price = parseFloat(allbuydata[k].sell_price);\r\n\t\t\t\t\tvar coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 \r\n\t\t\t\t  if(allbuydata[k].coupontype==3) coupon_money =  product_price\r\n\t\t\t\t\tvar totalprice = product_price - coupon_money ;\r\n\t\t\t\t\tif (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\t\t\t\t\t\r\n\t\t\t\t\talltotalprice += totalprice;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.needaddress = needaddress;\r\n\t\t\t\tvar oldalltotalprice = alltotalprice;\r\n\t\t\t\tif (alltotalprice < 0) alltotalprice = 0;\r\n\t\t\t\talltotalprice = alltotalprice.toFixed(2);\r\n\t\t\t\tthat.alltotalprice = alltotalprice;\r\n\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t},\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar addressid = this.address.id;\r\n\t\t\t\tvar linkman = this.linkman;\r\n\t\t\t\tvar tel = this.tel;\r\n\t\t\t\tvar worker_id = that.opt.worker_id;\r\n\t\t\t\tvar frompage = that.opt.frompage ? that.opt.frompage : '';\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\t\r\n\t\t\t\t// 添加日志输出\r\n\t\t\t\tconsole.log(\"buy.vue中的流程模式:\", that.order_flow_mode, typeof that.order_flow_mode);\r\n\t\t\t\tconsole.log(\"URL参数中的流程模式:\", that.opt.order_flow_mode);\r\n\t\t\t\tconsole.log(\"服务人员ID:\", worker_id);\r\n\t\t\t\tconsole.log(\"预约时间:\", that.yydate);\r\n\t\t\t\t\r\n\t\t\t\t// 确保流程模式是数字类型\r\n\t\t\t\tvar flowMode = parseInt(that.order_flow_mode || that.opt.order_flow_mode || 0);\r\n\t\t\t\t\r\n\t\t\t\t// 根据流程模式判断必填项\r\n\t\t\t\tif (that.sindex == 0) {\r\n\t\t\t\t\tapp.error('请选择服务方式');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.sindex==2 && addressid == undefined) {\r\n\t\t\t\t\tapp.error('请选择地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.sindex==1 && (!linkman || !tel)) {\r\n\t\t\t\t\tapp.error('请填写联系人及联系电话');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 传统模式下需要选择服务人员和预约时间\r\n\t\t\t\tif (flowMode !== 1) {\r\n\t\t\t\t\tif (worker_id == 0) {\r\n\t\t\t\t\t\tapp.error('请选择服务人员');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!that.yydate) {\r\n\t\t\t\t\t\tapp.error('请选择预约时间');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar buydata = [];\r\n\t\t\t\tfor (var i in allbuydata) {\r\n\t\t\t\t\tvar formdata_fields = allbuydata[i].formdata;\r\n\t\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\t\tvar newformdata = {};\r\n\t\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\r\n\t\t\t\t\t\tvar thisfield = 'form'+allbuydata[i].bid + '_' + j;\r\n\t\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\r\n\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\r\n\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tnewformdata['form'+j] = formdata[thisfield];\r\n\t\t\t\t\t}\t\t\t\r\n\t\t\t\t\tbuydata.push({\r\n\t\t\t\t\t\tbid: allbuydata[i].bid,\r\n\t\t\t\t\t\tprodata: allbuydata[i].prodatastr,\r\n\t\t\t\t\t\tcouponrid: allbuydata[i].couponrid,\r\n\t\t\t\t\t\tformdata:newformdata\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tvar remark = this.remark;\r\n\t\t\t\tvar yydate = that.yydate;\r\n\t\t\t\t\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiYuyue/createOrder', {\r\n\t\t\t\t\tfrompage: frompage,\r\n\t\t\t\t\tbuydata: buydata,\r\n\t\t\t\t\taddressid: addressid,\r\n\t\t\t\t\tlinkman: linkman,\r\n\t\t\t\t\ttel: tel,\r\n\t\t\t\t\tremark:remark,\r\n\t\t\t\t\tyydate:that.yydate,\r\n\t\t\t\t\tworker_id:worker_id,\r\n\t\t\t\t\tfwtype:that.sindex,\r\n\t\t\t\t\torder_flow_mode: flowMode\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif(res.status==1 && res.payorderid){\r\n\t\t\t\t\t\tthat.\tissubmit = true\t\r\n\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t}else if(res.status==1 && !res.payorderid){\r\n\t\t\t\t\t\tif(that.yyset.yuyue_success){\r\n\t\t\t\t\t\t\tapp.alert(that.yyset.yuyue_success,function(){\r\n\t\t\t\t\t\t\t\tapp.goto('/yuyue/orderlist');\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.goto('/yuyue/orderlist');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},editorBindPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},\r\n\t\t\t//选择服务方式\r\n\t\t\tselectFwtype: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tthat.sindex = index\r\n\t\t\t},\r\n\t\t\tinputLinkman: function (e) {\r\n\t\t\t\tthis.linkman = e.detail.value\r\n\t\t\t},\r\n\t\t\tinputTel: function (e) {\r\n\t\t\t\tthis.tel = e.detail.value\r\n\t\t\t},\r\n\t\t\t//选择时间\r\n\t\t\tchooseTime: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar prodata = that.prodata.split(',');\r\n\t\t\t\tthat.proid = prodata[0]\r\n\t\t\t\tthat.timeDialogShow = true;\r\n\t\t\t\tthat.timeIndex = -1;\r\n\t\t\t\tvar curTopIndex = that.datelist[0];\r\n\t\t\t\tthat.nowdate = that.datelist[that.curTopIndex].year+that.datelist[that.curTopIndex].date;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiYuyue/isgetTime', { date:that.nowdate,proid:that.proid}, function (res) {\r\n\t\t\t\t  that.loading = false;\r\n\t\t\t\t  that.timelist = res.data;\r\n\t\t\t\t})\r\n\t\t\t},\t\r\n\t\t\tswitchTopTab: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t  var id = e.currentTarget.dataset.id;\r\n\t\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\t  this.curTopIndex = index;\r\n\t\t\t  that.days = that.datelist[index].year+that.datelist[index].date\r\n\t\t\t  that.nowdate = that.datelist[index].nowdate\r\n\t\t\t   // if(!that.dates ){ that.dates = that.daydate[0] }\r\n\t\t\t  this.curIndex = -1;\r\n\t\t\t  this.curIndex2 = -1;\r\n\t\t\t  //检测预约时间是否可预约\r\n\t\t\t  that.loading = true;\r\n\r\n\t\t\t  app.get('ApiYuyue/isgetTime', { date: that.days,proid:that.proid}, function (res) {\r\n\t\t\t\t  that.loading = false;\r\n\t\t\t\t  that.timelist = res.data;\r\n\t\t\t  })\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tswitchDateTab: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t  var index2 = parseInt(e.currentTarget.dataset.index2);\r\n\t\t\t  var timeint = e.currentTarget.dataset.time\r\n\t\t\t  var status = e.currentTarget.dataset.status\r\n\t\t\t  if(status==0){\r\n\t\t\t\t\tapp.error('此时间不可选择');return;\t\r\n\t\t\t  }\r\n\t\t\t  that.timeint = timeint\r\n\t\t\t  that.timeindex = index2\r\n\t\t\t\t//console.log(that.timelist);\r\n\t\t\t  that.starttime1 = that.timelist[index2].time\r\n\t\t\t  if(!that.days || that.days=='请选择服务时间'){ that.days = that.datelist[0].year + that.datelist[0].date }\r\n\t\t\t  that.selectDates = that.starttime1;\r\n\t\t\t},\r\n\t\t\tselectDate:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(that.timeindex >= 0 && that.timelist[that.timeindex].status==0){\r\n\t\t\t\t\t\tthat.starttime1='';\r\n\t\t\t\t}\r\n\t\t\t\tif(!that.starttime1){\r\n\t\t\t\t\tapp.error('请选择预约时间');return;\r\n\t\t\t\t}\r\n\t\t\t\t var yydate = that.days+' '+that.selectDates\r\n\t\t\t\t that.yydate = yydate\r\n\t\t\t\t this.timeDialogShow = false;\r\n\t\t\t},\r\n\t\t\thidetimeDialog: function() {\r\n\t\t\t\tthis.timeDialogShow = false;\r\n\t\t\t},\r\n\t\t\tgotopeople:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\t// 传统模式下需要先选择预约时间\r\n\t\t\t\tif (that.order_flow_mode !== 1 && !that.yydate){\r\n\t\t\t\t\tapp.error('请先选择预约时间');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t} \r\n\t\t\t\tapp.goto('selectpeople?prodata='+that.prodata+'&yydate='+that.yydate+'&sindex='+that.sindex+'&linkman='+that.linkman+'&tel='+that.tel);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}\r\n.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}\r\n.address-add .f1 {margin-right: 20rpx}\r\n.address-add .f1 .img {width: 66rpx;height: 66rpx;}\r\n.address-add .f2 {color: #666;}\r\n.address-add .f3 {width: 26rpx;height: 26rpx;}\r\n.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;\r\n    text-align: right;}\r\n.linkitem .f1 {width: 160rpx;color: #111111}\r\n.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}\r\n.buydata {width: 94%;margin: 0 3%;margin-bottom: 20rpx;}\r\n.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}\r\n.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}\r\n.bcontent {width: 100%;padding: 0 20rpx;background: #fff;border-radius: 20rpx;}\r\n.bcontent2 {width: 100%;padding: 0 20rpx; margin-top: 30rpx;background: #fff;border-radius: 20rpx;}\r\n.product {width: 100%;border-bottom: 1px solid #f4f4f4}\r\n.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}\r\n.product .item:last-child {border: none}\r\n.product .info {padding-left: 20rpx;}\r\n.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.product .info .f2 {color: #999999;font-size: 24rpx}\r\n.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n.product image {width: 140rpx;height: 140rpx}\r\n.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}\r\n.freight .f1 {color: #333;margin-bottom: 10rpx}\r\n.freight .f2 {color: #111111;text-align: right;flex: 1}\r\n.freight .f3 {width: 24rpx;height: 28rpx;}\r\n.freighttips {color: red;font-size: 24rpx;}\r\n.freight-ul {width: 100%;display: flex;}\r\n.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}\r\n\r\n.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}\r\n.price .f1 {color: #333}\r\n.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.price .f3 {width: 24rpx;height: 24rpx;}\r\n.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}\r\n.scoredk .f1 {color: #333333}\r\n.scoredk .f2 {color: #999999;text-align: right;flex: 1}\r\n.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}\r\n.remark .f1 {color: #333;width: 200rpx}\r\n.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 20rpx;display: flex;align-items: center;z-index: 8}\r\n.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}\r\n.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}\r\n.storeitem .panel .f1 {color: #333}\r\n.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}\r\n.storeitem .radio-item:last-child {border: 0}\r\n.storeitem .radio-item .f1 {color: #666;flex: 1}\r\n.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}\r\n.storeitem .radio .radio-img {width: 100%;height: 100%}\r\n.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\r\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\r\n.cuxiao-desc {width: 100%}\r\n.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}\r\n.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.cuxiao-item .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}\r\n.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}\r\n.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}\r\n.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}\r\n.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}\r\n.memberitem .t1{color:#333;font-weight:bold}\r\n.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.memberitem .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.checkMem{ display: inline-block; }\r\n.checkMem p{ height: 30px; width: 100%; display: inline-block; }\r\n.placeholder{  font-size: 26rpx;line-height: 80rpx;}\r\n.selected-item span{ font-size: 26rpx !important;}\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n\r\n/*时间范围*/\r\n.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}\r\n.order-tab{ }\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;}\r\n.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}\r\n.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}\r\n.order-tab2 .on .after{display:block}\r\n.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx; }\r\n.daydate .date{ width:20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}\r\n.daydate .on{ background:red; color:#fff;}\r\n.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}\r\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}\r\n\r\n/* 从planWrite.vue引入的样式 */\r\n.body_item {\r\n\tpadding: 20px 0;\r\n\tborder-bottom: 1px solid #f6f6f6;\r\n}\r\n\r\n.body_item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.body_title {\r\n\tfont-size: 28rpx;\r\n\tfont-family: PingFang SC;\r\n\tfont-weight: bold;\r\n\tcolor: #323232;\r\n}\r\n\r\n.body_text {\r\n\tfont-size: 24rpx;\r\n\tfont-family: PingFang SC;\r\n\tfont-weight: 500;\r\n\tcolor: #999999;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.body_content {\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.body_tag {\r\n\tpadding: 0 20rpx;\r\n\theight: 54rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: #F4F4F4;\r\n\tborder-radius: 27rpx;\r\n\tmargin: 20rpx 10rpx 0 0;\r\n\tfont-size: 22rpx;\r\n\tborder: 1px solid rgba(0, 0, 0, 0);\r\n\tfont-family: PingFang SC;\r\n}\r\n\r\n.body_active {\r\n\tbackground: rgba(252, 67, 67, 0.1200);\r\n\tborder: 1px solid #FC4343;\r\n\tcolor: #FC4343;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.body_data {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: normal;\r\n\tfont-family: PingFang SC;\r\n\tfont-weight: 500;\r\n\tcolor: #686868;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.body_detail {\r\n\theight: 35rpx;\r\n\twidth: 35rpx;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.flex-bt {\r\n\tjustify-content: space-between;\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045113\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}