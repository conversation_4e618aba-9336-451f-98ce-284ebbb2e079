{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?41e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?c2b6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?2f26", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?d040", "uni-app:///pagesExt/cityagent/income.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?773c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/income.vue?3b9f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "nodata", "nomore", "st", "page", "limit", "datalist", "statistics", "total_income", "today_income", "month_income", "incomeTypeIndex", "incomeTypeList", "key", "name", "startDate", "endDate", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "params", "app", "uni", "title", "changetab", "onIncomeTypeChange", "onStartDateChange", "onEndDateChange", "getIncomeTypeName", "getWithdrawTypeName", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqI/wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC,iBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MAEA;QACAC;QACAA;QACAA;MACA;MAEA;QACAlB;QACAC;MACA;;MAEA;MACA;QACA;UACAkB;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MAEA;MAEAC;QACAF;QACAG;QAEA;UACAD;UACA;QACA;;QAEA;QACAC;UACAC;QACA;;QAEA;QACA;UACAJ;QACA;;QAEA;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;QAEAA;MACA;IACA;IAEA;IACAK;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;MAAA,8CACA,oDACA,oDACA,oDACA,gBACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClUA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/income.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/income.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./income.vue?vue&type=template&id=3a8cc352&\"\nvar renderjs\nimport script from \"./income.vue?vue&type=script&lang=js&\"\nexport * from \"./income.vue?vue&type=script&lang=js&\"\nimport style0 from \"./income.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/income.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./income.vue?vue&type=template&id=3a8cc352&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getIncomeTypeName(item.income_type)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.st == 1\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.getWithdrawTypeName(item.withdraw_type)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./income.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./income.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- 顶部统计卡片 -->\r\n\t\t<view class=\"stats-card\">\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">¥{{statistics.total_income}}</text>\r\n\t\t\t\t<text class=\"stats-label\">总收益</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">¥{{statistics.today_income}}</text>\r\n\t\t\t\t<text class=\"stats-label\">今日收益</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t<text class=\"stats-number\">¥{{statistics.month_income}}</text>\r\n\t\t\t\t<text class=\"stats-label\">本月收益</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 标签页 -->\r\n\t\t<dd-tab :itemdata=\"['收益明细','转换记录']\" :itemst=\"['0','1']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\r\n\t\t\r\n\t\t<!-- 筛选条件 -->\r\n\t\t<view class=\"filter-container\" v-if=\"st == 0\">\r\n\t\t\t<view class=\"filter-row\">\r\n\t\t\t\t<picker @change=\"onIncomeTypeChange\" :value=\"incomeTypeIndex\" :range=\"incomeTypeList\" range-key=\"name\">\r\n\t\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t\t<text>{{incomeTypeList[incomeTypeIndex].name}}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"filter-row\">\r\n\t\t\t\t<picker mode=\"date\" @change=\"onStartDateChange\" :value=\"startDate\">\r\n\t\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t\t<text>开始日期：{{startDate || '请选择'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t\t<picker mode=\"date\" @change=\"onEndDateChange\" :value=\"endDate\">\r\n\t\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t\t<text>结束日期：{{endDate || '请选择'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 收益明细 -->\r\n\t\t\t<block v-if=\"st == 0\">\r\n\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"income-item\">\r\n\t\t\t\t\t<view class=\"item-header\">\r\n\t\t\t\t\t\t<text class=\"item-title\">{{item.description || '订单佣金'}}</text>\r\n\t\t\t\t\t\t<text class=\"item-amount\" :class=\"item.amount > 0 ? 'positive' : 'negative'\">\r\n\t\t\t\t\t\t\t{{item.amount > 0 ? '+' : ''}}¥{{item.amount}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">收益类型：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{getIncomeTypeName(item.income_type)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\" v-if=\"item.order_sn\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">订单号：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{item.order_sn}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\" v-if=\"item.order_amount > 0\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">订单金额：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">¥{{item.order_amount}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">佣金比例：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{item.commission_rate}}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">获得时间：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{item.createtime_format}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\r\n\t\t\t<!-- 转换记录 -->\r\n\t\t\t<block v-if=\"st == 1\">\r\n\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"withdraw-item\">\r\n\t\t\t\t\t<view class=\"item-header\">\r\n\t\t\t\t\t\t<text class=\"item-title\">转换申请</text>\r\n\t\t\t\t\t\t<text class=\"item-amount\">¥{{item.amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">转换方式：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{getWithdrawTypeName(item.withdraw_type)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">手续费：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">¥{{item.fee}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">实际到账：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">¥{{item.actual_amount}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">申请时间：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{item.createtime_format}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-info\" v-if=\"item.remark\">\r\n\t\t\t\t\t\t\t<text class=\"info-label\">备注：</text>\r\n\t\t\t\t\t\t\t<text class=\"info-value\">{{item.remark}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-status\">\r\n\t\t\t\t\t\t<text class=\"status-text\" :style=\"{color: item.status_color}\">{{item.status_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!-- 空状态 -->\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t\r\n\t<!-- 底部导航 -->\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\r\n\t<!-- 消息提示 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tnodata: false,\r\n\t\t\tnomore: false,\r\n\t\t\tst: '0', // 当前标签页：0-收益明细，1-转换记录\r\n\t\t\tpage: 1,\r\n\t\t\tlimit: 20,\r\n\t\t\tdatalist: [],\r\n\t\t\tstatistics: {\r\n\t\t\t\ttotal_income: '0.00',\r\n\t\t\t\ttoday_income: '0.00',\r\n\t\t\t\tmonth_income: '0.00'\r\n\t\t\t},\r\n\t\t\t// 收益类型筛选\r\n\t\t\tincomeTypeIndex: 0,\r\n\t\t\tincomeTypeList: [\r\n\t\t\t\t{key: 'all', name: '全部类型'},\r\n\t\t\t\t{key: '1', name: '订单佣金'},\r\n\t\t\t\t{key: '2', name: '推荐奖励'},\r\n\t\t\t\t{key: '3', name: '平台奖励'},\r\n\t\t\t\t{key: '4', name: '其他收益'}\r\n\t\t\t],\r\n\t\t\t// 时间筛选\r\n\t\t\tstartDate: '',\r\n\t\t\tendDate: ''\r\n\t\t};\r\n\t},\r\n\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.st = opt.st || '0';\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.page = 1;\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tonReachBottom: function () {\r\n\t\tif (!this.nomore) {\r\n\t\t\tthis.page++;\r\n\t\t\tthis.getdata(true);\r\n\t\t}\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 获取数据\r\n\t\tgetdata: function (isLoadMore = false) {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tif (!isLoadMore) {\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar params = {\r\n\t\t\t\tpage: that.page,\r\n\t\t\t\tlimit: that.limit\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 收益明细参数\r\n\t\t\tif (that.st == '0') {\r\n\t\t\t\tif (that.incomeTypeIndex > 0) {\r\n\t\t\t\t\tparams.income_type = that.incomeTypeList[that.incomeTypeIndex].key;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.startDate) {\r\n\t\t\t\t\tparams.start_time = that.startDate;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.endDate) {\r\n\t\t\t\t\tparams.end_time = that.endDate;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar apiUrl = that.st == '0' ? 'ApiCityAgent/getIncomeList' : 'ApiCityAgent/getWithdrawList';\r\n\t\t\t\r\n\t\t\tapp.get(apiUrl, params, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置导航标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.st == '0' ? '收益明细' : '转换记录'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 更新统计数据\r\n\t\t\t\tif (res.statistics) {\r\n\t\t\t\t\tthat.statistics = res.statistics;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新列表数据\r\n\t\t\t\tif (isLoadMore) {\r\n\t\t\t\t\tthat.datalist = that.datalist.concat(res.list);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.datalist = res.list;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否还有更多数据\r\n\t\t\t\tif (res.list.length < that.limit) {\r\n\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否为空\r\n\t\t\t\tif (that.datalist.length === 0) {\r\n\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换标签页\r\n\t\tchangetab: function (index) {\r\n\t\t\tthis.st = index;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.datalist = [];\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 收益类型选择\r\n\t\tonIncomeTypeChange: function (e) {\r\n\t\t\tthis.incomeTypeIndex = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 开始日期选择\r\n\t\tonStartDateChange: function (e) {\r\n\t\t\tthis.startDate = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 结束日期选择\r\n\t\tonEndDateChange: function (e) {\r\n\t\t\tthis.endDate = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 获取收益类型名称\r\n\t\tgetIncomeTypeName: function (type) {\r\n\t\t\t// 如果是字符串类型且不是纯数字，直接返回\r\n\t\t\tif (typeof type === 'string' && isNaN(type)) {\r\n\t\t\t\treturn type;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 数字类型映射\r\n\t\t\tconst types = {\r\n\t\t\t\t1: '订单佣金',\r\n\t\t\t\t2: '推荐奖励', \r\n\t\t\t\t3: '平台奖励',\r\n\t\t\t\t4: '其他收益',\r\n\t\t\t\t'1': '订单佣金',\r\n\t\t\t\t'2': '推荐奖励',\r\n\t\t\t\t'3': '平台奖励',\r\n\t\t\t\t'4': '其他收益'\r\n\t\t\t};\r\n\t\t\treturn types[type] || type || '未知';\r\n\t\t},\r\n\t\t\r\n\t\t// 获取转换方式名称\r\n\t\tgetWithdrawTypeName: function (type) {\r\n\t\t\tconst types = {\r\n\t\t\t\t'weixin': '微信钱包',\r\n\t\t\t\t'alipay': '支付宝',\r\n\t\t\t\t'bank': '银行卡',\r\n\t\t\t\t'commission': '余额转换'\r\n\t\t\t};\r\n\t\t\treturn types[type] || '未知';\r\n\t\t},\r\n\t\t\r\n\t\t// 数据加载完成\r\n\t\tloaded: function () {\r\n\t\t\tthis.isload = true;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-card {\r\n\tdisplay: flex;\r\n\tbackground: white;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.stats-number {\r\n\tdisplay: block;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 筛选条件样式 */\r\n.filter-container {\r\n\tbackground: white;\r\n\tmargin: 0 20rpx 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-row {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.filter-row:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.filter-item {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tpadding: 0 20rpx;\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 10rpx;\r\n\tmargin-right: 20rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.filter-item:last-child {\r\n\tmargin-right: 0;\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content {\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n/* 收益明细样式 */\r\n.income-item {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.item-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.item-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.item-amount {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.item-amount.positive {\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n.item-amount.negative {\r\n\tcolor: #F44336;\r\n}\r\n\r\n.item-content {\r\n\t\r\n}\r\n\r\n.item-info {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.item-info:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n\twidth: 160rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.info-value {\r\n\tflex: 1;\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 转换记录样式 */\r\n.withdraw-item {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.item-status {\r\n\ttext-align: right;\r\n\tmargin-top: 20rpx;\r\n\tpadding-top: 20rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.status-text {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./income.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./income.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103595\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}