{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?a207", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?9b73", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?81cb", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?8ee8", "uni-app:///yuyue/jdorderdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?d794", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderdetail.vue?d1c9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "orderinfo", "prolist", "worker", "binfo", "psorder", "order", "yuyue_sign", "nowtime2", "formdata", "showPunchModal", "punchId", "punchSt", "punch<PERSON>itle", "punchPhotoType", "punchLocationInfo", "punchPhotos", "isLocating", "team_workers", "computed", "canSubmitPunch", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "methods", "getdata", "app", "id", "setTimeout", "that", "console", "interval2", "getdjs", "<PERSON><PERSON><PERSON><PERSON>", "uni", "title", "content", "showCancel", "confirmText", "setst", "openPunchModal", "photoType", "closePunchModal", "getPunchLocation", "longitude", "latitude", "icon", "duration", "cancelText", "success", "selectPunchPhoto", "remove<PERSON>unch<PERSON><PERSON><PERSON>", "previewPunchPhoto", "urls", "current", "submitPunchData", "st", "params", "mask", "da<PERSON><PERSON>", "itemList", "name", "address", "scale", "call", "phoneNumber", "previewImage", "formatDistance", "distance", "directEndOrder", "submitEndOrder", "goToOrderList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+StxB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QAAAC;MAAA;QACA;UACAD;UACAE;YACAF;UACA;QAEA;QACAG;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEAC;;QAEA;QACA;UACAD;QACA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;QAEA;UACAP;UACAS;YACAF;YACAA;UACA;QACA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;QACAH;MACA;QACA;QACA;QACA;QACA;MACA;MACAA;IACA;IACAI;MACA;MACA;MACAP;QACAA;QACAA;UAAAC;QAAA;UACAD;UACA;YACAA;YACAE;cACA;cACA;gBACA;gBACAM;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cACA;cACAT;YACA;UACA;YACAH;YACAE;cACAF;YACA;UAEA;QAEA;MACA;IACA;IACAa;MACA;MACA;MACA;;MAEA;MACAV;IACA;IAEA;IACAW;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACAL;QACAM;MACA;QACAN;QACAM;MACA;QACAN;QACAM;MACA;;MAEA;MACAZ;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAA;IACA;IAEA;IACAa;MACA;IACA;IAEA;IACAC;MACA;MAEAd;MAEAH;QACAI;QAEAD;QACAA;UACAe;UACAC;QACA;;QAEA;QACAX;UACAC;UACAW;UACAC;QACA;MACA;QACAjB;QAEAD;;QAEA;QACAK;UACAC;UACAC;UACAE;UACAU;UACAC;YACA;cACApB;YACA;UACA;QACA;MACA;IACA;IAEA;IACAqB;MACA;MAEApB;;MAEA;MACAJ;QACAI;;QAEA;QACAD;;QAEA;QACAK;UACAC;UACAW;UACAC;QACA;MACA;IACA;IAEA;IACAI;MACA;MACArB;MAEA;QACA;QACAA;MACA;IACA;IAEA;IACAsB;MACA;MACA;QACAlB;UACAmB;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACArB;UACAC;UACAW;UACAC;QACA;QACA;MACA;MAEAjB;;MAEA;MACA;QACAH;QACA6B;QACAZ;QACAC;MACA;;MAEA;MACA;QACAY;MACA;QACAA;MACA;MAEA3B;;MAEA;MACAI;QACAC;QACAuB;MACA;;MAEA;MACAhC;QACAQ;QACAJ;QAEA;UACA;UACAD;;UAEA;UACAH;;UAEA;UACA;UACA;UAEA;YACAS;YACAC;UACA;YACAD;YACAC;UACA;YACAD;YACAC;UACA;;UAEA;UACA;YACAA;UACA;;UAEA;UACAF;YACAC;YACAC;YACAC;YACAY;cACA;cACArB;gBACAC;cACA;YACA;UACA;QACA;UACA;UACAH;QACA;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;MACAzB;QACA0B;QACAX;UACA;YACA;cACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;cACA;YACA;YACAnB;YACAA;YACAA;YACAI;cACAW;cACAD;cACAiB;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA9B;QACA+B;MACA;IACA;IACAC;MACA;MACA;MACA;MACAhC;QACAmB;QACAC;MACA;IACA;IACAa;MACA;QACA;MACA;MAEAC;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA3C;QACAA;;QAEA;QACAA;UACA;UACAG;QACA;UACA;UACAC;UACAD;QACA;MACA;IACA;IACA;IACAyC;MACA;MACA;MACA;QACA3C;QACA6B;MACA;;MAEA;MACA;QACAC;QACAA;MACA;MAEA3B;;MAEA;MACAJ;QACAA;QACAI;QAEA;UACA;UACAJ;;UAEA;UACAQ;YACAC;YACAC;YACAC;YACAY;cACA;cACArB;gBACAC;cACA;YACA;UACA;QACA;UACAH;QACA;MACA;IACA;IACA;IACA6C;MACAzC;MACAJ;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/xBA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/jdorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/jdorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jdorderdetail.vue?vue&type=template&id=76fe41a5&\"\nvar renderjs\nimport script from \"./jdorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./jdorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jdorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/jdorderdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderdetail.vue?vue&type=template&id=76fe41a5&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.psorder.status != 0\n      ? _vm.dateFormat(_vm.psorder.starttime)\n      : null\n  var m1 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.daodiantime\n      ? _vm.dateFormat(_vm.psorder.daodiantime)\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.psorder.status != 0 &&\n    _vm.psorder.arrival_distance &&\n    _vm.psorder.status >= 2\n      ? _vm.formatDistance(_vm.psorder.arrival_distance)\n      : null\n  var m3 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.sign_time\n      ? _vm.dateFormat(_vm.psorder.sign_time)\n      : null\n  var m4 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.endtime\n      ? _vm.dateFormat(_vm.psorder.endtime)\n      : null\n  var g0 = _vm.isload ? _vm.formdata && _vm.formdata.length > 0 : null\n  var g1 = _vm.isload\n    ? _vm.team_workers &&\n      _vm.team_workers.is_multi_worker &&\n      _vm.team_workers.other_workers.length > 0\n    : null\n  var m5 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.createtime) : null\n  var m6 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.paytime) : null\n  var g2 = _vm.showPunchModal ? _vm.punchPhotos.length : null\n  var g3 = _vm.showPunchModal ? _vm.punchPhotos.length : null\n  var g4 = _vm.showPunchModal ? _vm.punchPhotos.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        g0: g0,\n        g1: g1,\n        m5: m5,\n        m6: m6,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\n\t\t<map v-if=\"psorder.status!=4\" class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\n\t\t\tid:0,\n\t\t\tlatitude:binfo.latitude,\n\t\t\tlongitude:binfo.longitude,\n\t\t\ticonPath: '/static/peisong/marker_business.png',\n\t\t\twidth:'44',\n\t\t\theight:'54'\n\t\t},{\n\t\t\tid:1,\n\t\t\tlatitude:orderinfo.latitude,\n\t\t\tlongitude:orderinfo.longitude,\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\n\t\t\twidth:'44',\n\t\t\theight:'54'\n\t\t},{\n\t\t\tid:2,\n\t\t\tlatitude:worker.latitude,\n\t\t\tlongitude:worker.longitude,\n\t\t\ticonPath: '/static/peisong/marker_qishou.png',\n\t\t\twidth:'44',\n\t\t\theight:'54'\n\t\t}]\"></map>\n\t\t<map v-else class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\n\t\t\tid:0,\n\t\t\tlatitude:binfo.latitude,\n\t\t\tlongitude:binfo.longitude,\n\t\t\ticonPath: '/static/peisong/marker_business.png',\n\t\t\twidth:'44',\n\t\t\theight:'54'\n\t\t},{\n\t\t\tid:0,\n\t\t\tlatitude:orderinfo.latitude,\n\t\t\tlongitude:orderinfo.longitude,\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\n\t\t\twidth:'44',\n\t\t\theight:'54'\n\t\t}]\"></map>\n\n\t\t<view class=\"order-box\">\n\t\t\t<view class=\"head\">\n\t\t\t\t<view v-if=\"psorder.fwtype==1\">\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==3\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已完成</view>\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/> {{orderinfo.yydate}}<text class=\"t1\">预计上门时间</text> </view>\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==2\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text class=\"t1\" style=\"margin-left:10rpx\">服务中</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else-if=\"psorder.fwtype==2\">\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==3\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已完成</view>\n\t\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.status==1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>期望上门时间<text class=\"t1\">{{orderinfo.yydate}}</text> </view>\n\t\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.status==2\"  ><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已到达，服务中</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"f2\"><text class=\"t1\">{{psorder.ticheng}}</text>元</view>\n\t\t\t</view>\n\t\t\t<view class=\"content\" style=\"border-bottom:0\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t<view class=\"t1\"><text class=\"x1\">{{psorder.juli}}</text><text class=\"x2\">{{psorder.juli_unit}}</text></view>\n\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">{{psorder.juli2}}</text><text class=\"x2\">{{psorder.juli2_unit}}</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t<view class=\"t1\">{{binfo.name}}</view>\n\t\t\t\t\t<view class=\"t2\">{{binfo.address}}</view>\n\t\t\t\t\t<view class=\"t3\">{{orderinfo.address}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{orderinfo.area}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"box-title\">商品清单({{orderinfo.procount}})</view>\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"item\">\n\t\t\t\t<text class=\"t1 flex1\">{{item.name}} {{item.ggname}}</text>\n\t\t\t\t<text class=\"t2 flex0\">￥{{item.sell_price}} ×{{item.num}} </text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"psorder.status!=0\">\n\t\t\t<view class=\"box-title\">服务信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">用户姓名</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.linkman}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">用户电话</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.tel}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">预约时间</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.yydate}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">接单时间</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.starttime)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"psorder.daodiantime\">\n\t\t\t\t<text class=\"t1\">{{yuyue_sign?'出发时间':'到店时间'}}</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.daodiantime)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"psorder.arrival_distance && psorder.status>=2\">\n\t\t\t\t<text class=\"t1\">到达距离</text>\n\t\t\t\t<text class=\"t2\">{{formatDistance(psorder.arrival_distance)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"psorder.sign_time\">\n\t\t\t\t<text class=\"t1\">开始时间</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.sign_time)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"psorder.endtime\">\n\t\t\t\t<text class=\"t1\">完成时间</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.endtime)}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 自定义表单数据展示区域 -->\n\t\t<view class=\"orderinfo\" v-if=\"formdata && formdata.length > 0\">\n\t\t\t<view class=\"box-title\">自定义表单信息</view>\n\t\t\t<view v-for=\"(item, idx) in formdata\" :key=\"idx\" class=\"item\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<text class=\"t2\" :class=\"{'form-input': item[2] === 'input', 'form-textarea': item[2] === 'textarea', 'form-select': item[2] === 'select'}\">{{item[1] || '未填写'}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 到达照片展示区域 -->\n\t\t<view class=\"orderinfo\" v-if=\"psorder.arrival_photo && psorder.status>=2\">\n\t\t\t<view class=\"box-title\">到达现场照片</view>\n\t\t\t<view class=\"photo-area\">\n\t\t\t\t<image v-for=\"(item, index) in psorder.arrival_photo_array\" :key=\"index\" \n\t\t\t\t\t\t:src=\"item\" mode=\"widthFix\" class=\"service-photo\" @tap=\"previewImage\" :data-urls=\"psorder.arrival_photo_array\" :data-current=\"item\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 完成照片展示区域 -->\n\t\t<view class=\"orderinfo\" v-if=\"psorder.complete_photo && psorder.status>=3\">\n\t\t\t<view class=\"box-title\">服务完成照片</view>\n\t\t\t<view class=\"photo-area\">\n\t\t\t\t<image v-for=\"(item, index) in psorder.complete_photo_array\" :key=\"index\" \n\t\t\t\t\t\t:src=\"item\" mode=\"widthFix\" class=\"service-photo\" @tap=\"previewImage\" :data-urls=\"psorder.complete_photo_array\" :data-current=\"item\"/>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 服务团队信息展示区域（多技师功能） -->\n\t\t<view class=\"orderinfo\" v-if=\"team_workers && team_workers.is_multi_worker && team_workers.other_workers.length > 0\">\n\t\t\t<view class=\"box-title\">服务团队（共{{team_workers.total_count}}人）</view>\n\t\t\t\n\t\t\t<!-- 当前技师信息 -->\n\t\t\t<view class=\"team-member current\" v-if=\"team_workers.current_worker\">\n\t\t\t\t<view class=\"member-header\">\n\t\t\t\t\t<image class=\"member-avatar\" :src=\"team_workers.current_worker.headimg || '/static/img/default_avatar.png'\"></image>\n\t\t\t\t\t<view class=\"member-info\">\n\t\t\t\t\t\t<view class=\"member-name\">{{team_workers.current_worker.realname}}（我）</view>\n\t\t\t\t\t\t<view class=\"member-role\" :class=\"{'leader': team_workers.current_worker.worker_role == 1}\">\n\t\t\t\t\t\t\t{{team_workers.current_worker.role_text}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"member-status current-status\">当前技师</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 其他技师信息 -->\n\t\t\t<view class=\"team-member\" v-for=\"(member, index) in team_workers.other_workers\" :key=\"index\">\n\t\t\t\t<view class=\"member-header\">\n\t\t\t\t\t<image class=\"member-avatar\" :src=\"member.headimg || '/static/img/default_avatar.png'\"></image>\n\t\t\t\t\t<view class=\"member-info\">\n\t\t\t\t\t\t<view class=\"member-name\">{{member.realname}}</view>\n\t\t\t\t\t\t<view class=\"member-role\" :class=\"{'leader': member.worker_role == 1}\">\n\t\t\t\t\t\t\t{{member.role_text}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"member-contact\" @tap=\"call\" :data-tel=\"member.tel\">\n\t\t\t\t\t\t<text class=\"contact-icon\">📞</text>\n\t\t\t\t\t\t<text class=\"contact-text\">联系</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"team-tip\">\n\t\t\t\t<text class=\"tip-text\">💡 您可以联系其他技师协调服务进度</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"box-title\">订单信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{orderinfo.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.createtime)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.paytime)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{orderinfo.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.totalprice}}</text>\n\t\t\t</view>\n\t\t\t<!-- <view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2 red\">{{orderinfo.message ? orderinfo.message : '无'}}</text>\n\t\t\t</view> -->\n\t\t\t<view class=\"item\" v-if=\"order.remark\">\n\t\t\t\t<text class=\"t1\">后台备注</text>\n\t\t\t\t<text class=\"t2\" style=\"color: #FF6F30;\">{{order.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view style=\"width:100%;height:120rpx\"></view>\n\t\t<view class=\"bottom\" v-if=\"psorder.status!=4\">\n\t\t\t<view class=\"f1\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"orderinfo.tel\"><image src=\"/static/peisong/tel1.png\" class=\"img\"/>联系顾客</view>\n\t\t\t<view class=\"f2\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"binfo.tel\"><image src=\"/static/peisong/tel2.png\" class=\"img\"/>联系商家</view>\n\t\t\t\n\t\t\t<view class=\"btn1\" @tap=\"qiangdan\" :data-id=\"psorder.id\" v-if=\"psorder.status==0 && psorder.isqd==1\">立即抢单</view>\n\t\t\t<view class=\"btn1\" \tstyle=\"background: #BCBFC7;\" v-if=\"psorder.status==0 && !psorder.isqd\">{{psorder.djs}}后可抢单</view>\n\t\t\t\n\t\t\t\n\t\t\t<block v-if=\"psorder.fwtype==1\">\n\t\t\t\t<view class=\"btn1\" @tap=\"openPunchModal\" :data-id=\"psorder.id\" data-st=\"2\" v-if=\"psorder.status==1\">顾客已到店</view>\n\t\t\t\t<view class=\"btn1\" @tap=\"openPunchModal\" :data-id=\"psorder.id\" data-st=\"3\" v-if=\"psorder.status==2\">我已完成</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"psorder.fwtype==2\">\n\t\t\t\t<view class=\"btn1\" @tap=\"openPunchModal\" :data-id=\"psorder.id\" data-st=\"2\" v-if=\"psorder.status==1\">我已到达</view>\n\t\t\t\t<view class=\"btn1\" @tap=\"openPunchModal\" :data-id=\"psorder.id\" data-st=\"3\" v-if=\"psorder.status==2\">服务已完成</view>\n\t\t\t\t<view class=\"btn1\" @tap=\"goToOrderList\" v-if=\"psorder.status==3\">返回列表</view>\n\t\t\t</block>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t\n\t<!-- 打卡弹窗 -->\n\t<view class=\"punch-modal\" v-if=\"showPunchModal\">\n\t\t<view class=\"punch-content\">\n\t\t\t<view class=\"punch-header\">\n\t\t\t\t<text class=\"punch-title\">{{punchTitle}}</text>\n\t\t\t\t<view class=\"close-btn\" @tap=\"closePunchModal\">×</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 位置信息区域 -->\n\t\t\t<view class=\"location-section\">\n\t\t\t\t<view class=\"section-title\"><text class=\"icon\">📍</text> 位置信息</view>\n\t\t\t\t<view class=\"location-content\" v-if=\"punchLocationInfo\">\n\t\t\t\t\t<view class=\"location-status success\">\n\t\t\t\t\t\t<text class=\"status-text\">已获取位置信息</text>\n\t\t\t\t\t\t<text class=\"location-detail\">经度: {{punchLocationInfo.longitude}}</text>\n\t\t\t\t\t\t<text class=\"location-detail\">纬度: {{punchLocationInfo.latitude}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"location-content\" v-else>\n\t\t\t\t\t<view class=\"location-status\" :class=\"{'loading': isLocating}\">\n\t\t\t\t\t\t<text class=\"status-text\">{{isLocating ? '获取位置中...' : '点击获取位置'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"get-location-btn\" @tap=\"getPunchLocation\" v-if=\"!isLocating && !punchLocationInfo\">\n\t\t\t\t\t\t获取位置信息\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 照片上传区域 -->\n\t\t\t<view class=\"photo-section\">\n\t\t\t\t<view class=\"section-title\"><text class=\"icon\">📷</text> {{punchPhotoType}} ({{punchPhotos.length}}/9)</view>\n\t\t\t\t<view class=\"photo-content\">\n\t\t\t\t\t<!-- 已选择的照片列表 -->\n\t\t\t\t\t<view class=\"photo-list\" v-if=\"punchPhotos.length > 0\">\n\t\t\t\t\t\t<view class=\"photo-item\" v-for=\"(photo, index) in punchPhotos\" :key=\"index\">\n\t\t\t\t\t\t\t<image :src=\"photo\" class=\"preview-image\" mode=\"aspectFill\" @tap=\"previewPunchPhoto\" :data-url=\"photo\"></image>\n\t\t\t\t\t\t\t<view class=\"remove-icon\" @tap=\"removePunchPhoto\" :data-index=\"index\">×</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 上传按钮（当照片数量小于9时显示） -->\n\t\t\t\t\t<view class=\"photo-placeholder\" @tap=\"selectPunchPhoto\" v-if=\"punchPhotos.length < 9\">\n\t\t\t\t\t\t<text class=\"placeholder-icon\">+</text>\n\t\t\t\t\t\t<text class=\"placeholder-text\">点击上传照片</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 操作按钮 -->\n\t\t\t<view class=\"punch-actions\">\n\t\t\t\t<view class=\"cancel-btn\" @tap=\"closePunchModal\">取消</view>\n\t\t\t\t<view class=\"submit-btn\" @tap=\"submitPunchData\" :class=\"{'disabled': !canSubmitPunch}\">\n\t\t\t\t\t确认提交\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval2 = null;\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\t\n      orderinfo: {},\n      prolist: [],\n\t\t\tworker:{},\n      binfo: {},\n      psorder: {},\n\t\t\torder: {},\n\t\t\tyuyue_sign:false,\n\t\t\tnowtime2:'',\n\t\t\tformdata: [], // 自定义表单数据\n\t\t\t\n\t\t\t// 打卡弹窗相关\n\t\t\tshowPunchModal: false,\n\t\t\tpunchId: null,\n\t\t\tpunchSt: null,\n\t\t\tpunchTitle: '',\n\t\t\tpunchPhotoType: '',\n\t\t\tpunchLocationInfo: null,\n\t\t\tpunchPhotos: [], \n\t\t\tisLocating: false,\n\t\t\tteam_workers: null\n    };\n  },\n  computed: {\n    // 是否可以提交打卡\n    canSubmitPunch: function() {\n      return this.punchLocationInfo && this.punchPhotos.length > 0;\n    }\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonUnload:function(){\n\t\tclearInterval(this.interval2);\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tapp.get('ApiYuyueWorker/orderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tif(res.status==0){\n\t\t\t\t\tapp.alert(res.msg)\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t  app.goto('dating');\n\t\t\t\t\t}, 1000);\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\tthat.orderinfo = res.orderinfo;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.binfo = res.binfo;\n\t\t\t\tthat.psorder = res.psorder;\n\t\t\t\tthat.worker = res.worker;\n\t\t\t\tthat.order = res.order || {}; // 获取完整的订单信息\n\t\t\t\tthat.yuyue_sign = res.yuyue_sign\n\t\t\t\tthat.nowtime2 = res.nowtime\n\t\t\t\tthat.formdata = res.formdata || []; // 获取自定义表单数据\n\t\t\t\t\n\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][getdata_001]获取到自定义表单数据:\", JSON.stringify(that.formdata));\n\t\t\t\t\n\t\t\t\t// 处理照片数组\n\t\t\t\tif (that.psorder.arrival_photo && !that.psorder.arrival_photo_array) {\n\t\t\t\t\tthat.psorder.arrival_photo_array = [that.psorder.arrival_photo];\n\t\t\t\t}\n\t\t\t\tif (that.psorder.complete_photo && !that.psorder.complete_photo_array) {\n\t\t\t\t\tthat.psorder.complete_photo_array = [that.psorder.complete_photo];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保距离值是数字类型\n\t\t\t\tif (that.psorder.arrival_distance) {\n\t\t\t\t\tthat.psorder.arrival_distance = parseFloat(that.psorder.arrival_distance);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif(res.isdelayed){\n\t\t\t\t\tclearInterval(interval2);\n\t\t\t\t\tinterval2 = setInterval(function () {\n\t\t\t\t\t\tthat.nowtime2 = that.nowtime2 + 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.team_workers = res.team_workers;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tgetdjs: function () {\n\t\t\t\tvar that = this;\n\t\t\t\tvar nowtime = that.nowtime2;\n\t\t\t\tvar psorder = that.psorder\n\t\t\t\tvar totalsec = psorder.createtime * 1 + psorder.delayedtime * 60 - nowtime * 1;\n\t\t\t\tif (totalsec <= 0) {\n\t\t\t\t\tthat.psorder.isqd = true;\n\t\t\t\t} else {\n\t\t\t\t\tvar houer = Math.floor(totalsec / 3600);\n\t\t\t\t\tvar min = Math.floor((totalsec - houer * 3600) / 60);\n\t\t\t\t\tvar sec = totalsec - houer * 3600 - min * 60;\n\t\t\t\t\tvar djs =  (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n\t\t\t\t}\n\t\t\t\tthat.psorder.djs = djs;\n\t\t\t},\n    qiangdan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要接单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiYuyueWorker/qiangdan', {id: id}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status==1){\n\t\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n              // 如果返回了预约时间信息，可以在这里更新显示\n              if (data.appointment_time) {\n                // 使用更明确的弹窗显示预约时间\n                uni.showModal({\n                  title: '接单成功',\n                  content: '客户预约上门时间：' + data.appointment_time + '\\n请按时到达客户位置',\n                  showCancel: false,\n                  confirmText: '我知道了'\n                });\n              }\n\t\t\t\t\t\t  that.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t  app.error(data.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goto('dating');\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\n        });\n      });\n    },\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      \n      // 通过打开弹窗代替直接处理\n      that.openPunchModal(e);\n    },\n    \n    // 打开打卡弹窗\n    openPunchModal: function(e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      \n      // 设置打卡类型和标题\n      var title = '';\n      var photoType = '';\n      \n      if(st == 2){\n        title = '到达打卡';\n        photoType = '到达现场照片';\n      } else if(st == 3) {\n        title = '完成打卡';\n        photoType = '服务完成照片';\n      } else if(st == 4) {\n        title = '结束订单';\n        photoType = '订单结束确认照片';\n      }\n      \n      // 重置打卡状态\n      that.punchId = id;\n      that.punchSt = st;\n      that.punchTitle = title;\n      that.punchPhotoType = photoType;\n      that.punchLocationInfo = null;\n      that.punchPhotos = []; \n      that.showPunchModal = true;\n      \n      // 自动开始获取位置\n      that.getPunchLocation();\n    },\n    \n    // 关闭打卡弹窗\n    closePunchModal: function() {\n      this.showPunchModal = false;\n    },\n    \n    // 获取打卡位置\n    getPunchLocation: function() {\n      var that = this;\n      \n      that.isLocating = true;\n      \n      app.getLocation(function(locRes) {\n        console.log(\"2023-09-26 16:39:25-INFO-[jdorderdetail][getPunchLocation_001]位置获取成功:\", JSON.stringify(locRes));\n        \n        that.isLocating = false;\n        that.punchLocationInfo = {\n          longitude: locRes.longitude,\n          latitude: locRes.latitude\n        };\n        \n        // 显示位置获取成功提示\n        uni.showToast({\n          title: '位置获取成功',\n          icon: 'success',\n          duration: 1500\n        });\n      }, function(err) {\n        console.log(\"2023-09-26 16:39:25-ERROR-[jdorderdetail][getPunchLocation_002]位置获取失败:\", JSON.stringify(err));\n        \n        that.isLocating = false;\n        \n        // 提示重试\n        uni.showModal({\n          title: '位置获取失败',\n          content: '请检查是否授予定位权限，并重试',\n          confirmText: '重试',\n          cancelText: '取消',\n          success: function(res) {\n            if(res.confirm) {\n              that.getPunchLocation();\n            }\n          }\n        });\n      });\n    },\n    \n    // 选择打卡照片\n    selectPunchPhoto: function() {\n      var that = this;\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][selectPunchPhoto_001]开始选择照片\");\n      \n      // 使用系统标准的chooseImage方法\n      app.chooseImage(function(imageUrls) {\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][selectPunchPhoto_002]照片上传成功:\", imageUrls);\n        \n        // 将上传成功的图片URL添加到照片列表\n        that.punchPhotos = that.punchPhotos.concat(imageUrls);\n        \n        // 提示上传成功\n        uni.showToast({\n          title: '照片上传成功',\n          icon: 'success',\n          duration: 1500\n        });\n      }, 9 - that.punchPhotos.length);\n    },\n    \n    // 移除指定照片\n    removePunchPhoto: function(e) {\n        var index = e.currentTarget.dataset.index;\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][removePunchPhoto_001]移除照片，索引:\", index);\n        \n        if (index >= 0 && index < this.punchPhotos.length) {\n          this.punchPhotos.splice(index, 1);\n          console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][removePunchPhoto_002]照片移除成功，剩余数量:\", this.punchPhotos.length);\n        }\n    },\n    \n    // 预览选择的照片\n    previewPunchPhoto: function(e) {\n      var currentUrl = e.currentTarget.dataset.url;\n      if(this.punchPhotos && this.punchPhotos.length > 0) {\n        uni.previewImage({\n          urls: this.punchPhotos,\n          current: currentUrl\n        });\n      }\n    },\n    \n    // 提交打卡数据\n    submitPunchData: function() {\n      var that = this;\n      \n      if(!that.canSubmitPunch) {\n        uni.showToast({\n          title: '请先获取位置并上传照片',\n          icon: 'none',\n          duration: 2000\n        });\n        return;\n      }\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_001]准备提交打卡数据\");\n      \n      // 准备参数\n      var params = {\n        id: that.punchId,\n        st: that.punchSt,\n        longitude: that.punchLocationInfo.longitude,\n        latitude: that.punchLocationInfo.latitude\n      };\n      \n      // 设置照片参数\n      if(that.punchSt == 2) {\n        params.arrival_photo = that.punchPhotos.join(',');\n      } else {\n        params.complete_photo = that.punchPhotos.join(',');\n      }\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_002]提交参数:\", JSON.stringify(params));\n      \n      // 显示提交中\n      uni.showLoading({\n        title: '提交中...',\n        mask: true\n      });\n      \n      // 提交数据\n      app.post('ApiYuyueWorker/setst', params, function(data) {\n        uni.hideLoading();\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_003]提交响应:\", JSON.stringify(data));\n        \n        if(data.status === 1) {\n          // 成功，关闭弹窗并刷新数据\n          that.closePunchModal();\n          \n          // 提示成功\n          app.success(data.msg);\n          \n          // 显示详细信息\n          var title = '';\n          var content = '';\n          \n          if(that.punchSt == 2) {\n            title = '到达打卡成功';\n            content = data.msg || '您已成功打卡，请开始服务';\n          } else if(that.punchSt == 3) {\n            title = '完成打卡成功';\n            content = data.msg || '服务已完成，感谢您的工作';\n          } else if(that.punchSt == 4) {\n            title = '订单已结束';\n            content = data.msg || '订单已完成结算，感谢您的工作';\n          }\n          \n          // 如果有距离信息，显示距离\n          if(data.distance && that.punchSt == 2) {\n            content = '您距离客户位置: ' + that.formatDistance(data.distance) + '\\n' + content;\n          }\n          \n          // 显示成功弹窗\n          uni.showModal({\n            title: title,\n            content: content,\n            showCancel: false,\n            success: function() {\n              // 刷新数据\n              setTimeout(function() {\n                that.getdata();\n              }, 500);\n            }\n          });\n        } else {\n          // 失败提示\n          app.alert(data.msg || '提交失败');\n        }\n      });\n    },\n\t\tdaohang:function(e){\n\t\t\tvar that = this;\n\t\t\tvar datainfo = that.psorder;\n\t\t\tvar binfo = that.binfo\n\t\t\tvar orderinfo = that.orderinfo\n\t\t\tuni.showActionSheet({\n        itemList: ['导航到商家', '导航到用户'],\n        success: function (res) {\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tif (res.tapIndex == 0) {\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude\n\t\t\t\t\t\t\tvar name = binfo.name\n\t\t\t\t\t\t\tvar address = binfo.address\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\n\t\t\t\t\t\t\tvar name = orderinfo.address\n\t\t\t\t\t\t\tvar address = orderinfo.address\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(longitude);\n\t\t\t\t\t\tconsole.log(latitude);\n\t\t\t\t\t\tconsole.log(address);\n\t\t\t\t\t\tuni.openLocation({\n\t\t\t\t\t\t latitude:parseFloat(latitude),\n\t\t\t\t\t\t longitude:parseFloat(longitude),\n\t\t\t\t\t\t name:name,\n\t\t\t\t\t\t address:address,\n\t\t\t\t\t\t scale: 13\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tcall:function(e){\n\t\t\tvar tel = e.currentTarget.dataset.tel;\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: tel\n\t\t\t});\n\t\t},\n\t\tpreviewImage: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar urls = e.currentTarget.dataset.urls;\n\t\t\tvar current = e.currentTarget.dataset.current;\n\t\t\tuni.previewImage({\n\t\t\t\turls: urls,\n\t\t\t\tcurrent: current\n\t\t\t});\n\t\t},\n\t\tformatDistance: function (distance) {\n\t\t\tif (!distance && distance !== 0) {\n\t\t\t\treturn '未知';\n\t\t\t}\n\t\t\t\n\t\t\tdistance = parseFloat(distance);\n\t\t\tif (isNaN(distance)) {\n\t\t\t\treturn '未知';\n\t\t\t}\n\t\t\t\n\t\t\tif (distance >= 1000) {\n\t\t\t\treturn (distance / 1000).toFixed(2) + '公里';\n\t\t\t} else {\n\t\t\t\treturn parseInt(distance) + '米';\n\t\t\t}\n\t\t},\n\t\tdirectEndOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要结束订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\t\n\t\t\t\t// 获取当前位置作为可选参数，如果获取失败也继续提交\n\t\t\t\tapp.getLocation(function(locRes) {\n\t\t\t\t\t// 成功获取位置后提交\n\t\t\t\t\tthat.submitEndOrder(id, locRes.longitude, locRes.latitude);\n\t\t\t\t}, function(err) {\n\t\t\t\t\t// 位置获取失败，仍然提交但不带位置信息\n\t\t\t\t\tconsole.log(\"2023-09-26 16:39:25-INFO-[jdorderdetail][directEndOrder_001]位置获取失败，继续提交\", JSON.stringify(err));\n\t\t\t\t\tthat.submitEndOrder(id);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 提交结束订单\n\t\tsubmitEndOrder: function(id, longitude, latitude) {\n\t\t\tvar that = this;\n\t\t\t// 准备参数\n\t\t\tvar params = {\n\t\t\t\tid: id,\n\t\t\t\tst: 4\n\t\t\t};\n\t\t\t\n\t\t\t// 如果有位置信息，添加到参数中\n\t\t\tif (longitude && latitude) {\n\t\t\t\tparams.longitude = longitude;\n\t\t\t\tparams.latitude = latitude;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log(\"2023-09-26 16:39:25-INFO-[jdorderdetail][submitEndOrder_001]提交参数:\", JSON.stringify(params));\n\t\t\t\n\t\t\t// 提交数据\n\t\t\tapp.post('ApiYuyueWorker/setst', params, function(data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tconsole.log(\"2023-09-26 16:39:25-INFO-[jdorderdetail][submitEndOrder_002]提交响应:\", JSON.stringify(data));\n\t\t\t\t\n\t\t\t\tif(data.status===1){\n\t\t\t\t\t// 提示成功\n\t\t\t\t\tapp.success(data.msg || '订单已完成结算');\n\t\t\t\t\t\n\t\t\t\t\t// 显示详细信息\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '订单已结束',\n\t\t\t\t\t\tcontent: data.msg || '订单已完成结算，感谢您的工作',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t// 刷新数据\n\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t\t}, 500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tapp.error(data.msg || '提交失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 返回订单列表\n\t\tgoToOrderList: function() {\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderdetail][goToOrderList_001]用户点击返回列表\");\n\t\t\tapp.goto('/yuyue/jdorderlist');\n\t\t}\n  }\n}\n</script>\n<style>\n\n.map{width:100%;height:500rpx;overflow:hidden}\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#222222}\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}\n.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}\n.order-box .head .f2{color:#FF6F30}\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\n\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\n\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\n.order-box .content .f2{}\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\n\n.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}\n.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}\n.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}\n.orderinfo .item .t1{width:200rpx;color:#161616}\n.orderinfo .item .t2{flex:1;text-align:right;color:#222222}\n.orderinfo .item .red{color:red}\n\n/* 自定义表单数据样式 */\n.orderinfo .item .form-input {\n  color: #333;\n  font-weight: normal;\n}\n\n.orderinfo .item .form-textarea {\n  color: #333;\n  font-weight: normal;\n  word-break: break-all;\n  white-space: pre-wrap;\n}\n\n.orderinfo .item .form-select {\n  color: #06A051;\n  font-weight: bold;\n}\n\n.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}\n.bottom .f1 .img{width:44rpx;height:44rpx}\n.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}\n.bottom .f2 .img{width:44rpx;height:44rpx}\n.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}\n\n/* 照片展示区域样式 */\n.photo-area {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx 0;\n}\n.service-photo {\n  width: 180rpx;\n  margin-right: 10rpx;\n  margin-bottom: 10rpx;\n  border-radius: 8rpx;\n}\n\n/* 打卡弹窗样式 */\n.punch-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n}\n\n.punch-content {\n  background-color: #fff;\n  padding: 40rpx;\n  border-radius: 20rpx;\n  width: 85%;\n  max-width: 650rpx;\n  max-height: 80vh;\n  overflow-y: auto;\n  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);\n}\n\n.punch-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.punch-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.close-btn {\n  font-size: 48rpx;\n  height: 48rpx;\n  line-height: 40rpx;\n  width: 48rpx;\n  text-align: center;\n  color: #999;\n  cursor: pointer;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  color: #333;\n}\n\n.section-title .icon {\n  margin-right: 10rpx;\n  font-size: 32rpx;\n}\n\n.location-section, .photo-section {\n  margin-bottom: 30rpx;\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 16rpx;\n}\n\n.location-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.location-status {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 20rpx;\n  padding: 16rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n}\n\n.location-status.success {\n  color: #06A051;\n  border-left: 8rpx solid #06A051;\n}\n\n.location-status.loading {\n  color: #FF6F30;\n  border-left: 8rpx solid #FF6F30;\n}\n\n.status-text {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.location-detail {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.get-location-btn {\n  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);\n  color: #fff;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  box-shadow: 0 4rpx 8rpx rgba(3, 178, 105, 0.2);\n}\n\n.photo-content {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.photo-list {\n    display: flex;\n    flex-wrap: wrap;\n    width: 100%;\n    margin-bottom: 20rpx;\n}\n\n.photo-item {\n    position: relative;\n    width: 31%;\n    padding-bottom: 31%;\n    margin-right: 2%;\n    margin-bottom: 10rpx;\n}\n\n.photo-item:nth-child(3n) {\n    margin-right: 0;\n}\n\n.preview-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  background-color: #fff;\n}\n\n.remove-icon {\n    position: absolute;\n    top: -10rpx;\n    right: -10rpx;\n    width: 36rpx;\n    height: 36rpx;\n    background-color: rgba(0, 0, 0, 0.6);\n    color: white;\n    border-radius: 50%;\n    text-align: center;\n    line-height: 32rpx;\n    font-size: 28rpx;\n    font-weight: bold;\n    z-index: 10;\n}\n\n.photo-actions {\n  display: flex;\n  justify-content: center;\n  width: 100%;\n  margin-top: 10rpx;\n}\n\n.remove-btn {\n  background-color: #FF6F30;\n  color: #fff;\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  box-shadow: 0 4rpx 8rpx rgba(255, 111, 48, 0.2);\n}\n\n.photo-placeholder {\n  width: 31%;\n  padding-bottom: 31%;\n  position: relative;\n  background-color: #fff;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  border: 2rpx dashed #ccc;\n  border-radius: 12rpx;\n}\n\n.photo-placeholder > * {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    text-align: center;\n}\n\n.placeholder-icon {\n  font-size: 60rpx;\n  color: #ccc;\n  line-height: 1;\n  margin-bottom: 10rpx;\n  transform: translate(-50%, -70%);\n}\n\n.placeholder-text {\n  font-size: 24rpx;\n  color: #999;\n  transform: translate(-50%, 30%);\n}\n\n.punch-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 40rpx;\n}\n\n.cancel-btn, .submit-btn {\n  flex: 1;\n  padding: 20rpx 0;\n  border-radius: 50rpx;\n  text-align: center;\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.cancel-btn {\n  background-color: #f0f0f0;\n  color: #666;\n  margin-right: 20rpx;\n}\n\n.submit-btn {\n  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);\n  color: #fff;\n}\n\n.submit-btn.disabled {\n  background: linear-gradient(-90deg, #ccc 0%, #999 100%);\n  color: #fff;\n  opacity: 0.8;\n}\n\n/* 动画 */\n@keyframes rotating {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.loading .status-text:before {\n  content: \"\";\n  display: inline-block;\n  width: 24rpx;\n  height: 24rpx;\n  border: 3rpx solid #FF6F30;\n  border-top-color: transparent;\n  border-radius: 50%;\n  margin-right: 10rpx;\n  animation: rotating 1s linear infinite;\n}\n\n/* 服务团队样式 */\n.team-member {\n\tmargin-bottom: 15rpx;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n}\n\n.team-member.current {\n\tbackground: #f0f8ff;\n\tborder: 1px solid #e1f3ff;\n}\n\n.member-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15rpx;\n}\n\n.member-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tmargin-right: 15rpx;\n}\n\n.member-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.member-name {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.member-role {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tpadding: 4rpx 10rpx;\n\tbackground: #f5f5f5;\n\tborder-radius: 10rpx;\n\tdisplay: inline-block;\n\twidth: fit-content;\n}\n\n.member-role.leader {\n\tbackground: #e8f4ff;\n\tcolor: #007aff;\n}\n\n.current-status {\n\tfont-size: 24rpx;\n\tcolor: #007aff;\n\tpadding: 8rpx 15rpx;\n\tbackground: #e8f4ff;\n\tborder-radius: 15rpx;\n}\n\n.member-contact {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 10rpx 15rpx;\n\tbackground: #4CAF50;\n\tborder-radius: 20rpx;\n\tcolor: white;\n\tfont-size: 24rpx;\n}\n\n.contact-icon {\n\tfont-size: 28rpx;\n\tmargin-bottom: 2rpx;\n}\n\n.contact-text {\n\tfont-size: 22rpx;\n}\n\n.team-tip {\n\tmargin-top: 15rpx;\n\tpadding: 15rpx;\n\tbackground: #fff8e1;\n\tborder-radius: 8rpx;\n\tborder-left: 4rpx solid #ffc107;\n}\n\n.tip-text {\n\tfont-size: 24rpx;\n\tcolor: #ff8f00;\n\tline-height: 1.5;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115043566\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}