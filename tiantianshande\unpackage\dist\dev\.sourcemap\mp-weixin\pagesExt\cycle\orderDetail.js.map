{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?fd40", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?1168", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?48ab", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?c3c4", "uni-app:///pagesExt/cycle/orderDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?8640", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?8b72", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?3a51", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderDetail.vue?f5cb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "opt", "pre_url", "detail", "djs", "shopset", "storeinfo", "onLoad", "onShow", "methods", "getdata", "app", "id", "that", "interval", "toPlanDetail", "toclose", "orderid", "setTimeout", "todel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACa;AACwB;;;AAG/F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8KpxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;QAAAC;MAAA;QACAC;QACAA;QACAA;QACA;UACAC;YACAD;YACAA;UACA;QACA;QACAF;QACAE;MACA;IACA;IACAE;MACA;MACA;MACA;QACA;MACA;QACAJ;MACA;IACA;IACAK;MACA;MACA;MACAL;QACAA;QACAA;UAAAM;QAAA;UACAN;UACAA;UACAO;YACAL;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAR;QACAA;QACAA;UAAAM;QAAA;UACAN;UACAA;UACAO;YACAP;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1PA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAwmC,CAAgB,olCAAG,EAAC,C;;;;;;;;;;;ACA5nC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/orderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/orderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=fe824c5c&scoped=true&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDetail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./orderDetail.vue?vue&type=style&index=1&id=fe824c5c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fe824c5c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/orderDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=template&id=fe824c5c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"isload\">\n\t\t\n\t\t<view class=\"banner\" v-if=\"detail.refund_status ==1 || detail.refund_status ==1\">\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_banner.png'\" class=\"banner_img\" alt=\"\" />\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_icon.png'\" class=\"banner_icon\" alt=\"\" />\n\t\t\t<view class=\"banner_data\" v-if=\"detail.refund_status==1\">\n\t\t\t\t<view class=\"banner_title\">退款中</view>\n\t\t\t\t<view class=\"banner_text\" >\n\t\t\t\t\t等待平台审核中\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.refund_status==2\">\n\t\t\t\t<view class=\"banner_title\">退款成功</view>\n\t\t\t\t<view class=\"banner_text\" >\n\t\t\t\t\t审核通过，已退款\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"banner\" v-else>\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_banner.png'\" class=\"banner_img\" alt=\"\" />\n\t\t\t<img :src=\"pre_url+'/static/img/week/week_icon.png'\" class=\"banner_icon\" alt=\"\" />\n\t\t\t<view class=\"banner_data\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"banner_title\">\n\t\t\t\t\t待支付\n\t\t\t\t</view>\n\t\t\t<!-- \t<view class=\"banner_text\" v-if=\"detail.status == 0\">\n\t\t\t\t\t请尽快完成支付\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==1\">\n\t\t\t\t<view class=\"banner_title\" >\n\t\t\t\t\t<text v-if=\"detail.freight_type ==1\">待取货</text>\n\t\t\t\t\t<text v-else>待发货</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"banner_text\" >\n\t\t\t\t\t<view class=\"t1\">{{detail.paytype=='货到付款' ? '已选择'+codtxt : '已成功付款'}}\n\t\t\t\t\t\t<text v-if=\"detail.freight_type!=1\">，我们会尽快为您配送</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==2\">\n\t\t\t\t<view class=\"banner_title\"  v-if=\"detail.freight_type ==1\">已取货</view>\n\t\t\t\t<view class=\"banner_title\"  v-else>已发货</view>\n\t\t\t\t<view class=\"banner_text\" v-if=\"detail.status == 0\">\n\t\t\t\t\t具体发货信息请查看每期订单\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==3\">\n\t\t\t\t<view class=\"banner_title\">已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"banner_data\" \tv-if=\"detail.status==4\">\n\t\t\t\t<view class=\"banner_title\">已关闭</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"body\">\n\t\t\t<view class=\"address\">\n\t\t\t\t\n\t\t\t\t<view class=\"img\">\n\t\t\t\t\t<image src=\"/static/img/address3.png\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info\">\n\t\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openMendian\" :data-storeinfo=\"storeinfo\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}} </text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t\t<!-- <view class=\"body_title\">\n\t\t\t\t服务信息\n\t\t\t</view> -->\n\t\t\t<view class=\"body_module\">\n\t\t\t\t<img :src=\"detail.propic\"\n\t\t\t\t\tclass=\"body_img\" alt=\"\" />\n\t\t\t\t<view class=\"body_data\">\n\t\t\t\t\t<view class=\"body_name\">\n\t\t\t\t\t\t{{detail.proname}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"body_text\">\n\t\t\t\t\t\t{{detail.ggname}} | {{detail.pspl}}<text v-if=\"detail.every_day\">,{{detail.every_day}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"body_price flex flex-bt flex-y-bottom\">\n\t\t\t\t\t\t<text>￥{{detail.sell_price}}</text>\n\t\t\t\t\t\t<!-- <text class=\"body_num\">x{{detail.num}}</text> -->\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>订单号</text>\n\t\t\t\t<text>{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>下单时间</text>\n\t\t\t\t<text>{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>配送期数</text>\n\t\t\t\t<text>共 {{detail.qsnum}} 期</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>每期数量</text>\n\t\t\t\t<text>共 {{detail.num}} 件</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>配送计划</text>\n\t\t\t\t<view class=\"body_time\" v-if=\"detail.status ==4\">\n\t\t\t\t\t<text>{{detail.start_date}}起<text style=\"margin-left: 10rpx;\" v-if=\"detail.every_day\">{{detail.every_day}}</text><text style=\"margin-left: 10rpx;\" v-else>{{detail.pspl}}</text></text>\n\t\t\t\t\t<!-- <img :src=\"pre_url+'/static/img/week/week_detail.png'\" alt=\"\" /> -->\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"body_time\" @tap=\"toPlanDetail\" :data-url=\"'/pagesExt/cycle/planList?id=' + detail.id\">\n\t\t\t\t\t<text>{{detail.start_date}}起<text style=\"margin-left: 10rpx;\" v-if=\"detail.every_day\">{{detail.every_day}}</text><text style=\"margin-left: 10rpx;\" v-else>{{detail.pspl}}</text></text>\n\t\t\t\t\t<img :src=\"pre_url+'/static/img/week/week_detail.png'\" alt=\"\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>配送方式</text>\n\t\t\t\t<text>{{detail.freight_text}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>商品总金额</text>\n\t\t\t\t<text class=\"body_color\">￥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\"  v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text>会员折扣</text>\n\t\t\t\t<text class=\"body_color\">-￥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\" v-if=\"detail.coupon_money > 0\">\n\t\t\t\t<text>优惠券抵扣</text>\n\t\t\t\t<text class=\"body_color\">-￥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\">\n\t\t\t\t<text>实付金额</text>\n\t\t\t\t<text class=\"body_color\">￥{{detail.totalprice}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\" v-if=\"detail.status==4\">\n\t\t\t\t<text>订单状态</text>\n\t\t\t\t<text>已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"body_list\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 body_color\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t\t\n\t\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\n\t\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n\t\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"opt\" v-if=\"detail.refund_status!=1\">\n\t\t\t<view class=\"opt_module\">\n\t\t\t\t<view class=\"opt_btn\" @tap=\"toclose\" :data-id=\"detail.id\" v-if=\"detail.status == 0\">关闭订单</view>\n\t\t\t\t<view class=\"opt_btn\" style=\"    background: #FF9900;color: #fff;border: none;\" v-if=\"detail.status == 0\" @tap=\"goto\" :data-url=\"'/pages/pay/pay?id=' + detail.payorderid\">去支付</view>\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status == 4\" @tap=\"todel\" :data-id=\"detail.id\" >删除订单</view>\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status==3 && detail.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">去评价</view>\n\t\t\t\t<view class=\"opt_btn\" v-if=\"detail.status==3 && detail.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?orderid=' + detail.id\">查看评价</view>\n\t\t\t\t<view style=\"width: 100%;\" class=\"flex flex-x-bottom\" v-if=\" (detail.status == 1 ||  detail.status ==2 )   && (detail.refund_status==3 ||detail.refund_status==0 )\">\n\t\t\t\t\t<view class=\"opt_btn\"  @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + detail.id\">申请退款</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisload: false,\n\t\t\t\topt:{},\n\t\t\t\tpre_url: app.globalData.pre_url,\n\t\t\t\tdetail:{},\n\t\t\t\tdjs: '',\n\t\t\t\tshopset:{},\n\t\t\t\tstoreinfo:[]\n\t\t\t}\n\t\t},\n\t\tonLoad(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.getdata();\n\t\t},\n\t\tonShow() {\n\t\t\tthis.getdata();\n\t\t},\n\t\tmethods: {\n\t\t\tgetdata: function () {\n\t\t\t\tvar that = this;\n\t\t\t\tapp.showLoading();\n\t\t\t\tapp.get('ApiCycle/orderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\t\tthat.detail = res.detail;\n\t\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t});\n\t\t\t},\n\t\t\ttoPlanDetail(e){\n\t\t\t\tvar that = this;\n\t\t\t\t var url = e.currentTarget.dataset.url;\n\t\t\t\tif(that.detail.status==0){\n\t\t\t\t\treturn;\n\t\t\t\t}else{\n\t\t\t\t\tapp.goto(url);\n\t\t\t\t}\n\t\t\t},\n\t\t\ttoclose: function (e) {\n\t\t\t  var that = this;\n\t\t\t  var orderid = e.currentTarget.dataset.id;\n\t\t\t  app.confirm('确定要取消该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t    app.post('ApiCycle/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t      app.success(data.msg);\n\t\t\t      setTimeout(function () {\n\t\t\t        that.getdata();\n\t\t\t      }, 1000);\n\t\t\t    });\n\t\t\t  });\n\t\t\t},\n\t\t\ttodel: function (e) {\n\t\t\t  var that = this;\n\t\t\t  var orderid = e.currentTarget.dataset.id;\n\t\t\t  app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\t\t\tapp.showLoading('删除中');\n\t\t\t    app.post('ApiCycle/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t      app.success(data.msg);\n\t\t\t      setTimeout(function () {\n\t\t\t        app.goback(true);\n\t\t\t      }, 1000);\n\t\t\t    });\n\t\t\t  });\n\t\t\t},\n\t\t}\n\t}\n</script>\n<style>\n\tpage {\n\t\tbackground: #F6F6F6;\n\t}\n</style>\n<style scoped>\n\t.banner {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t.banner_img {\n\t\twidth: 100%;\n\t\tdisplay: block;\n\t}\n\n\t.banner_icon {\n\t\tposition: absolute;\n\t\tright: 70rpx;\n\t\ttop: 30rpx;\n\t\theight: 124rpx;\n\t\twidth: 124rpx;\n\t}\n\n\t.banner_data {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tpadding: 60rpx 60rpx 0 60rpx;\n\t}\n\n\t.banner_title {\n\t\tfont-size: 40rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #FFFFFF;\n\t}\n\n\t.banner_text {\n\t\tfont-size: 26rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: rgba(255, 255, 255, 0.6);\n\t\tmargin-top: 28rpx;\n\t}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999} \n\n\t.body {\n\t\tposition: relative;\n\t\twidth: 690rpx;\n\t\tbox-sizing: border-box;\n\t\tpadding: 30rpx 30rpx 0 30rpx;\n\t\tmargin: -235rpx auto 0 auto;\n\t\tbackground: #fff;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.body_title {\n\t\tfont-size: 30rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #111111;\n\t}\n\n\t.body_module {\n\t\tpadding: 45rpx 0 20rpx 0;\n\t\tdisplay: flex;\n\t}\n\n\t.body_data {\n\t\tflex: 1;\n\t}\n\n\t.body_img {\n\t\twidth: 172rpx;\n\t\theight: 172rpx;\n\t\tborder-radius: 10rpx;\n\t\tmargin-right: 30rpx;\n\t\tflex-shrink: 0;\n\t}\n\n\t.body_name {\n\t\tfont-size: 28rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #323232;\n\t}\n\n\t.body_text {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #999999;\n\t\tmargin-top: 15rpx;\n\t}\n\n\t.body_price {\n\t\tfont-size: 32rpx;\n\t\tfont-family: Arial;\n\t\tfont-weight: bold;\n\t\tcolor: #FD4A46;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.body_tag {\n\t\tfont-size: 20rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #FD4A46;\n\t}\n\n\t.body_num {\n\t\tfont-size: 28rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #222222;\n\t}\n\n\t.body_list {\n\t\tposition: relative;\n\t\twidth: 630rpx;\n\t\theight: 88rpx;\n\t\tmargin: 0 auto;\n\t\tfont-size: 26rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #222222;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tborder-bottom: 1px solid #f7f7f7;\n\t}\n\n\t.body_list:last-child {\n\t\tborder-bottom: 0;\n\t}\n\n\t.body_time {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.body_time img {\n\t\theight: 35rpx;\n\t\twidth: 35rpx;\n\t\tmargin-left: 15rpx;\n\t}\n\n\t.body_color {\n\t\tcolor: #FF5347;\n\t}\n\n\t.opt {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 105rpx;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.opt_module {\n\t\tposition: fixed;\n\t\theight: 105rpx;\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\tbottom: 0;\n\t\tpadding: 0 40rpx;\n\t\tbox-sizing: border-box;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end;\n\t\tbox-shadow: 0px 0px 18px 0px rgba(132, 132, 132, 0.3200);\n\t}\n\n\t.opt_btn {\n\t\twidth: 160rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tbackground: #fff;\n\t\ttext-align: center;\n\t\tline-height: 60rpx;\n\t\tmargin-left: 10rpx;\n\t\tborder: 1px solid #cdcdcd\n\t}\n.orderinfo{width:100%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 0;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;flex-shrink:0;font-size: 26rpx}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .t3{ margin-top: 3rpx;}\n.orderinfo .item .red{color:red}\n\n/* \t.opt_btn:last-child {\n\t\tcolor: #fff;\n\t\tbackground: #FD4A46;\n\t} */\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098400\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=1&id=fe824c5c&scoped=true&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=1&id=fe824c5c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098334\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}