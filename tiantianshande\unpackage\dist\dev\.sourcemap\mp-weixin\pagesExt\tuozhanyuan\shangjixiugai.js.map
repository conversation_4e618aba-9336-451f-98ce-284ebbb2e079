{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?c0d4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?e479", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?1eb4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?c8ea", "uni-app:///pagesExt/tuozhanyuan/shangjixiugai.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?f4f4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjixiugai.vue?6743"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "id", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "rateArr", "rateIndex", "selectedRate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yixiangduIndex", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "yixiang<PERSON><PERSON><PERSON><PERSON>", "sourceChange", "withdrawMethodChange", "locationSelect", "success", "fail", "subform", "console", "info", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "pics", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8HtxB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IAAA,uDACA,8DACA,sDACA,yDACA,mDACA,iDACA,sDACA,sDACA,wDACA,wDACA,iDACA,iDACA,qDACA,sDACA,oDACA,4DACA,oFACA,kEACA;EAEA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAnB;MAAA;QACAkB;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;;QAEA;QACA;QACA;UAAA;QAAA;QACAH;QACAA;;QAEA;QACA;QACA;UAAA;QAAA;QACAA;QACAA;;QAEA;QACA;QACA;UAAA;QAAA;QACAA;QACAA;;QAEA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;MACA;IACA;IAEAI;MACA;IACA;IACAC;MACA;MACAL;MACAA;IACA;;IACAM;MACA;MACAN;MACAA;IACA;;IAEAO;MACA;MACA;IACA;IACAC;MACA;MACAN;QACAO;UACAT;UACAA;UACAA;QACA;;QACAU;UACAT;QACA;MACA;IACA;IAEAU;MACA;MACA;MAEA;QACAV;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MACA;MACAW;MACAA;MACAA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;MACA;MACA;QACAA;MACA;QACAA;MACA;;MAEAA;MACA;QACAA;MACA;MACAA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAZ;MACAA;QAAAY;MAAA;QACAZ;QACAA;QACA;UACAa;YACAb;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAjB;QACA;UACAkB;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAD;QACAnB;MACA;QACA;QACAmB;QACAnB;MACA;QACA;QACAmB;QACAnB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvXA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/shangjixiugai.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/shangjixiugai.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shangjixiugai.vue?vue&type=template&id=77b6eaa4&\"\nvar renderjs\nimport script from \"./shangjixiugai.vue?vue&type=script&lang=js&\"\nexport * from \"./shangjixiugai.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shangjixiugai.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/shangjixiugai.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjixiugai.vue?vue&type=template&id=77b6eaa4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.subordinates.length : null\n  var g1 = _vm.isload ? _vm.pics.length : null\n  var g2 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjixiugai.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjixiugai.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==2\">审核不通过：{{info.reason}}，请修改后再提交</view>\r\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==0\">您已提交申请，请等待审核</view>\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>联系人姓名<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"linkman\" :value=\"info.contact_name\" placeholder=\"请填写姓名\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>联系人电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"linktel\" :value=\"info.phone\" placeholder=\"请填写手机号码\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t<view class=\"apply_item\" v-if=\"subordinates.length > 0\">\r\n\t\t\t  <view>选择商机来源<text style=\"color:red\"> *</text></view>\r\n\t\t\t  <view>\r\n\t\t\t    <picker @change=\"sourceChange\" :value=\"sourceIndex\" :range=\"sourceArr\">\r\n\t\t\t      <view class=\"picker\">{{sourceArr[sourceIndex]}}</view>\r\n\t\t\t    </picker>\r\n\t\t\t  </view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>商家名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.company_name\" placeholder=\"请输入商家名称\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>商家备注<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.remark\" placeholder=\"请输入商家描述\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>主营类目<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\">\r\n\t\t\t\t\t\t\t<view class=\"picker\">{{cateArr[cindex]}}</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"apply_item\">\r\n\t\t\t\t\t<view>餐饮类目<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"cuisine_type\" :value=\"info.cuisine_type\" placeholder=\"类目不确定可以填\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>商家单价<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"price\" :value=\"info.price\" placeholder=\"类目不确定可以填\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t <view class=\"apply_item\">\r\n\t\t\t\t\t<view>意向度<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<picker @change=\"yixiangduChange\" :value=\"yixiangduIndex\" :range=\"yixiangduArr\">\r\n\t\t\t\t\t  <view class=\"picker\">{{ yixiangduArr[yixiangduIndex] }}</view>\r\n\t\t\t\t\t</picker>\r\n\r\n\t\t\t\t </view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>店铺坐标<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\" :value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t    <view>店铺地址<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t    <view class=\"flex-y-center\">\r\n\t\t\t\t        <input type=\"text\" name=\"address\" v-model=\"address\" placeholder=\"请输入商家详细地址\"></input>\r\n\t\t\t\t    </view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\r\n\t\t\t<!-- \t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>联系电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"apply_item\" style=\"line-height:50rpx\"><textarea name=\"sample_feedback\" placeholder=\"请输入商家样品反馈\" :value=\"info.sample_feedback\"></textarea></view>\r\n\t\t\t</view>\r\n\t\t<!-- \t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家主图<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家照片(3-4张)<text style=\"color:red\"> </text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>证明材料<text style=\"color:red\"> </text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in zhengming\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"zhengming\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"zhengming\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"zhengming\" :value=\"zhengming.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t\t -->\r\n\t      \r\n\t\t\t<view style=\"padding:30rpx 0\"><button  form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n\t   id: null,\r\n      loading: false,\r\n      isload: false,\r\n      menuindex: -1,\r\n      pre_url: app.globalData.pre_url,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      cateArr: [],\r\n      cindex: 0,\r\n\t  rateArr: [], // 商户费率名称数组\r\n\t  rateIndex: 0, // 当前选中的费率索引\r\n\t  selectedRate: null, // 当前选中的费率对象\r\n\t  yixiangduArr: [], // 商户费率名称数组\r\n\tyixiangduIndex: 0, // 当前选中的费率索引\r\n\t   yixiangduArr: [], // 意向度数组\r\n\t  selectedyixiangdu: null, // 当前选中的费率对象\r\n      isagree: false,\r\n      showxieyi: false,\r\n      pic: [],\r\n      pics: [],\r\n      zhengming: [],\r\n\t   sourceArr: [],  // 存储商机来源名称数组\r\n\t        sourceIndex: 0,  // 当前选中的商机来源索引\r\n\t        subordinates: [],  // 存储完整的商机来源数据\r\n      info: {},\r\n      bset: {},\r\n      latitude: '',\r\n      longitude: '',\r\n      address: '',\r\n      withdrawMethods: ['支付宝', '银行卡', '三方支付'],\r\n      withdrawMethodIndex: 0,\r\n      selectedWithdrawMethod: '支付宝'\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n\t this.id = this.opt.id;\r\n    this.getdata();\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  methods: {\r\n   getdata: function () {\r\n     var that = this;\r\n     that.loading = true;\r\n     app.get('ApiTuozhancrm/applyxiugai', { id: that.id }, function (res) {\r\n       that.loading = false;\r\n       if (res.status == 2) {\r\n         app.alert(res.msg, function () {\r\n           app.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');\r\n         });\r\n         return;\r\n       }\r\n       uni.setNavigationBarTitle({\r\n         title: res.title\r\n       });\r\n       \r\n       // 处理分类列表\r\n       var clist = res.clist;\r\n       var cateArr = clist.map(item => item.name);\r\n       that.clist = clist;\r\n       that.cateArr = cateArr;\r\n   \r\n       // 处理意向度数据\r\n       var yixiangdu = res.yixiangdu || [];\r\n       var yixiangduArr = yixiangdu.map(item => item.name);\r\n       that.yixiangdu = yixiangdu;\r\n       that.yixiangduArr = yixiangduArr;\r\n   \r\n       // 处理商机来源数据，将对象转换为数组\r\n       var subordinates = Object.values(res.subordinates || {});\r\n       var sourceArr = subordinates.map(item => item.realname || item.nickname || '无名');\r\n       that.subordinates = subordinates;\r\n       that.sourceArr = sourceArr;\r\n   \r\n       // 初始化其他数据\r\n       that.info = res.info || {};\r\n       that.address = that.info.address || '';\r\n       that.latitude = that.info.latitude || '';\r\n       that.longitude = that.info.longitude || '';\r\n       that.pic = that.info.pic ? [that.info.pic] : [];\r\n       that.pics = that.info.pics ? that.info.pics.split(',') : [];\r\n       that.zhengming = that.info.zhengming ? that.info.zhengming.split(',') : [];\r\n       that.bset = res.bset || {};\r\n   \r\n       that.loaded();\r\n     });\r\n   },\r\n\r\n    cateChange: function (e) {\r\n      this.cindex = e.detail.value;\r\n    },\r\n\t yixiangduChange: function (e) {\r\n\t     var that = this; // 引用组件的上下文\r\n\t     that.yixiangduIndex = e.detail.value;\r\n\t     that.selectedyixiangdu = that.yixiangdu[that.yixiangduIndex]; // 使用 that\r\n\t },\r\nsourceChange: function (e) {\r\n  var that = this; // 引用组件的上下文\r\n  that.sourceIndex = e.detail.value;  // 更新 sourceIndex 为用户选择的值\r\n  that.selectedSubordinate = that.subordinates[that.sourceIndex];  // 从 subordinates 中获取对应的商机来源\r\n},\r\n\r\n    withdrawMethodChange: function (e) {\r\n      this.withdrawMethodIndex = e.detail.value;\r\n      this.selectedWithdrawMethod = this.withdrawMethods[this.withdrawMethodIndex];\r\n    },\r\n    locationSelect: function () {\r\n        var that = this;\r\n        uni.chooseLocation({\r\n            success: function (res) {\r\n                that.latitude = res.latitude;\r\n                that.longitude = res.longitude;\r\n                that.address = res.name; // 更新地址为选择的地理位置名称\r\n            },\r\n            fail: function () {\r\n                app.error('无法获取当前位置，请手动填写店铺地址');\r\n            }\r\n        });\r\n    },\r\n\r\n    subform: function (e) {\r\n      var that = this;\r\n      var info = e.detail.value;\r\n     \r\n\t  if (info.linkman == '') {\r\n\t    app.error('请填写联系人姓名');\r\n\t    return false;\r\n\t  }\r\n\t  if (info.linktel == '') {\r\n\t    app.error('请填写联系人电话');\r\n\t    return false;\r\n\t  }\r\n\t\r\n\t  if (info.name == '') {\r\n\t    app.error('请填写商家名称');\r\n\t    return false;\r\n\t  }\r\n\t\r\n\t  if (info.address == '') {\r\n\t    app.error('请填写店铺地址');\r\n\t    return false;\r\n\t  }\r\n\t    // 打印调试信息，检查 sourceIndex 和 subordinates 是否有效\r\n\t    console.log('sourceIndex:', that.sourceIndex);\r\n\t    console.log('subordinates:', that.subordinates);\r\n\t    console.log('selected subordinate:', that.subordinates[that.sourceIndex]);\r\n\r\n\t  // if (info.pics == '') {\r\n\t  //   app.error('请上传商家照片');\r\n\t  //   return false;\r\n\t  // }\r\n\t  // 检查 subordinates 和 sourceIndex 是否有效\r\n\t    // if (!that.subordinates || !that.subordinates[that.sourceIndex]) {\r\n\t    //   app.error('请选择有效的商机来源');\r\n\t    //   return false;\r\n\t    // }\r\n\t      // 设置表单信息\r\n\t      info.address = that.address;  // 使用手动输入的地址\r\n\t      info.latitude = that.latitude || ''; // 如果没有选择坐标，则留空\r\n\t      info.longitude = that.longitude || ''; // 如果没有选择坐标，则留空\r\n\t   // 设置商机来源（下级成员ID）\r\n\t     // 设置商机来源（下级成员ID）\r\n\t     if (that.subordinates && that.subordinates.length > 0) {\r\n\t       info.source = that.subordinates[that.sourceIndex].id;\r\n\t     } else {\r\n\t       info.source = ''; // 或根据后端需求设置为 null 或者不设置该字段\r\n\t     }\r\n\r\n\t  info.cid = that.clist[that.cindex].id;\r\n\t  if (that.info && that.info.id) {\r\n\t    info.id = that.info.id;\r\n\t  }\r\n  info.yixiangduid = that.yixiangdu[that.yixiangduIndex].id;\r\n\t  if (that.info && that.info.id) {\r\n\t    info.yixiangduid = that.info.yixiangduid;\r\n\t  }\r\n if (that.id) {\r\n        info.id = that.id;\r\n    }\r\n\t\t\t// if (!info.rate_id) {\r\n\t\t\t//  app.error('请选择商户费率');\r\n\t\t // return false;\r\n\t\t // }\r\n      // 继续现有的表单验证和提交逻辑\r\n      // 您的现有验证逻辑...\r\n      app.showLoading('提交中');\r\n      app.post(\"ApiTuozhancrm/applyxiugai\", { info: info }, function (res) {\r\n        app.showLoading(false);\r\n        app.error(res.msg);\r\n        if (res.status == 1) {\r\n          setTimeout(function () {\r\n            app.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    isagreeChange: function (e) {\r\n      var val = e.detail.value;\r\n      this.isagree = val.length > 0;\r\n    },\r\n    showxieyiFun: function () {\r\n      this.showxieyi = true;\r\n    },\r\n    hidexieyi: function () {\r\n      this.showxieyi = false;\r\n      this.isagree = true;\r\n    },\r\n    uploadimg: function (e) {\r\n      var that = this;\r\n      var field = e.currentTarget.dataset.field;\r\n      var pics = that[field];\r\n      if (!pics) pics = [];\r\n      app.chooseImage(function (urls) {\r\n        for (var i = 0; i < urls.length; i++) {\r\n          pics.push(urls[i]);\r\n        }\r\n        if (field == 'pic') that.pic = pics;\r\n        if (field == 'pics') that.pics = pics;\r\n        if (field == 'zhengming') that.zhengming = pics;\r\n      }, 1);\r\n    },\r\n    removeimg: function (e) {\r\n      var that = this;\r\n      var index = e.currentTarget.dataset.index;\r\n      var field = e.currentTarget.dataset.field;\r\n      if (field == 'pic') {\r\n        var pics = that.pic;\r\n        pics.splice(index, 1);\r\n        that.pic = pics;\r\n      } else if (field == 'pics') {\r\n        var pics = that.pics;\r\n        pics.splice(index, 1);\r\n        that.pics = pics;\r\n      } else if (field == 'zhengming') {\r\n        var pics = that.zhengming;\r\n        pics.splice(index, 1);\r\n        that.zhengming = pics;\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.apply_title { background: #fff}\r\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n\r\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.apply_box .apply_item:last-child{ border:none}\r\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.apply_item input::placeholder{ color:#999999}\r\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\r\n.layui-imgbox-close image{width:100%;height:100%}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjixiugai.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjixiugai.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103392\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}