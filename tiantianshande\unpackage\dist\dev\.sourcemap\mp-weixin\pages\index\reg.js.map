{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?2958", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?7687", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?ca3b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?fc70", "uni-app:///pages/index/reg.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?3f87", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/index/reg.vue?5ecd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "platform2", "platform", "platformname", "platformimg", "logintype", "logintype_1", "logintype_2", "logintype_3", "logo", "name", "xystatus", "xyname", "xycontent", "xyname2", "xycontent2", "needsms", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "isagree", "smsdjs", "tel", "<PERSON><PERSON>", "frompage", "wxloginclick", "login_bind", "login_setnickname", "reg_invite_code", "reg_invite_code_text", "reg_invite_code_type", "parent", "xlevelarr", "has_custom", "show_custom_field", "regiondata", "editorFormdata", "test", "formfields", "custom_formdata", "items", "formvaldata", "submitDisabled", "tmplids", "default_headimg", "headimg", "nickname", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "pid", "xlevel", "uni", "url", "method", "header", "success", "formSubmit", "formdata", "pwd", "smscode", "yqcode", "postdata", "setTimeout", "getPhoneNumber", "console", "iv", "encryptedData", "code", "setnicknameregister", "nosetnicknameregister", "bindregister", "nobindregister", "register", "weixinlogin", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showxieyiFun2", "hidexieyi2", "telinput", "uploadHeadimg", "count", "sizeType", "sourceType", "filePath", "fail", "onChooseAvatar", "time", "clearInterval", "onchange", "setfield", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "check<PERSON>ust<PERSON><PERSON><PERSON><PERSON><PERSON>s", "subdata", "editorChooseImage", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4R5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IAEA;IACA;IACA;IACA;;IAEA;EACA;EAEAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACAC;UAAA;QACA;QACAD;QACAA;QACAA;QAWAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;;QAEA;QACA;UACAA;UACAA;UACAA;UACAI;YACAC;YACA5D;YACA6D;YACAC;cAAA;YAAA;YACAC;cACAR;YACA;UACA;QACA;QACAA;MACA;IACA;IACAS;MACA;MACA;MACA;QACAR;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;QACAS;MACA;MACA;QAAAP;QAAAhC;QAAAwC;QAAAC;QAAAV;QAAAW;MAAA;MACA;MACA;QACA;QACA;QACA;UACA;QACA;QACAC;QACAA;MACA;MAEA;QACAb;QACA;MACA;MACAA;MACAA;QACAA;QACA;UACAA;UACA;YACAD;UACA;UACAA;YACAe;cACA;gBACAd;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAe;MACA;MACA;QACAf;QAAA;MACA;MACA7D;QAAAoE;UACAS;UACA;UACA;UACAhB;YAAAiB;YAAAC;YAAAC;YAAAlB;YAAAC;UAAA;YACA;cACAF;cACAc;gBACAd;cACA;YACA;cACAA;YACA;YACA;UACA;QACA;MAAA;IACA;IACAoB;MACA;MACA;MACA;MACA;QACApB;QACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAqB;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAtB;QACA;MACA;MACA;QACAA;QACA;MACA;MACAD;IACA;IACAwB;MACA;IACA;IACAC;MACA;MACA;MACA;QACApB;MACA;QACAA;MACA;QACAA;MACA;MACAJ;QAAAP;QAAAC;QAAAxB;QAAAyC;QAAAV;QAAAC;MAAA;QACA;UACAF;UACAc;YACAd;UACA;QACA;UACAA;QACA;QACA;MACA;IACA;IACAyB;MACA;MACA;QACA1B;QACAA;QACA;MACA;MACAA;MACAC;QACA;UACAA;UACAc;YACAd;UACA;QACA;UACAD;UACAA;UACAA;QACA;UACAA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA0B;MACA;MACA;QACA;MACA;QACA;MACA;MACAV;IACA;IACAW;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA7B;QACA8B;QACAC;QACAC;QACA5B;UACA;UACA;UACAP;UACAG;YACAC;YACAgC;YACA7E;YACAgD;cACAS;cACAhB;cACA;cACA;gBACAD;cACA;gBACAC;cACA;YACA;YACAqC;cACArC;cACAA;YACA;UACA;QACA;QACAqC;QAAA;MAEA;IACA;IACAC;MACAtB;MACA;MACAhB;MACAG;QACAC;QACAgC;QACA7E;QACAgD;UACAP;UACA;UACA;YACAD;UACA;YACAC;UACA;QACA;QACAqC;UACArC;UACAA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAZ;MACA;MACA;QACAC;QACAD;QACA;MACA;MACA;QACAC;QACAD;QACA;MACA;MACAC;QAAA9B;MAAA;QACA;UACA8B;QACA;MACA;MACA;MACA;QACAuC;QACA;UACAxC;UACAA;UACAyC;QACA;UACAzC;QACA;MACA;IACA;IACA;IACA0C;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;MACA3D;MACAe;MACA;MACA;MACA;IACA;IACA6C;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAC;QACA;QACA;UACA7C;UAAA;QACA;QACA;UACA;YACA6C;UACA;YACAA;UACA;QACA;QACA;UACAA;QACA;QACA;UACA;YAAA;YACA;cACA7C;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;QACA;QACAS;MACA;MACA;IACA;IACAqC;MACA;MACA;MACA;MACA;MACA;MACA9C;QACAhB;QACAgC;QACAjB;QACAA;QAEA;QACAA;MAEA;IACA;IACAgD;MACA;MACA;MACA;MACA;MACA;MACA;MACA/D;MACAe;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACl0BA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/reg.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/reg.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./reg.vue?vue&type=template&id=18f2162e&\"\nvar renderjs\nimport script from \"./reg.vue?vue&type=script&lang=js&\"\nexport * from \"./reg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./reg.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/reg.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./reg.vue?vue&type=template&id=18f2162e&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    !_vm.show_custom_field &&\n    _vm.needsms\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.show_custom_field &&\n    _vm.needsms\n      ? _vm.t(\"color1\")\n      : null\n  var l1 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.show_custom_field &&\n    _vm.show_custom_field\n      ? _vm.__map(_vm.formfields.content, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var l0 =\n            item.key == \"checkbox\"\n              ? _vm.__map(item.val2, function (item1, idx1) {\n                  var $orig = _vm.__get_orig(item1)\n                  var m2 =\n                    _vm.custom_formdata[\"form\" + idx] &&\n                    _vm.inArray(item1, _vm.custom_formdata[\"form\" + idx])\n                  return {\n                    $orig: $orig,\n                    m2: m2,\n                  }\n                })\n              : null\n          return {\n            $orig: $orig,\n            l0: l0,\n          }\n        })\n      : null\n  var m3 =\n    _vm.isload && _vm.logintype != 4 && _vm.logintype != 5 && _vm.xystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.logintype != 4 &&\n    _vm.logintype != 5 &&\n    _vm.xystatus == 1 &&\n    _vm.xyname2\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.logintype != 4 && _vm.logintype != 5\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && _vm.logintype != 4 && _vm.logintype != 5\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m7 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.logintype == 4 ? _vm.t(\"color1rgb\") : null\n  var m9 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.logintype == 5 ? _vm.t(\"color1rgb\") : null\n  var m11 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1\") : null\n  var m12 = _vm.isload && _vm.showxieyi ? _vm.t(\"color1rgb\") : null\n  var m13 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1\") : null\n  var m14 = _vm.isload && _vm.showxieyi2 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        l1: l1,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./reg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./reg.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<block v-if=\"logintype!=4 && logintype!=5\">\n\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\n\t\t\t<view class=\"title\">注册账号</view>\n\t\t\t<view class=\"regform\">\n\t\t\t\t<!-- 系统注册S -->\n\t\t\t\t<block v-if=\"!show_custom_field\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" class=\"img\"/>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" v-if=\"needsms\">\n\t\t\t\t\t\t<image src=\"/static/img/reg-code.png\" class=\"img\"/>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\n\t\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<image src=\"/static/img/reg-pwd.png\" class=\"img\"/>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"6-16位字母数字组合密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"pwd\" value=\"\" :password=\"true\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<image src=\"/static/img/reg-pwd.png\" class=\"img\"/>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"再次输入登录密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"repwd\" value=\"\" :password=\"true\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" v-if=\"reg_invite_code && !parent\">\n\t\t\t\t\t\t<image src=\"/static/img/reg-yqcode.png\" class=\"img\"/>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :placeholder=\"'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"yqcode\" value=\"\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" v-if=\"reg_invite_code && parent\" style=\"color:#666;\">\n\t\t\t\t\t\t<block v-if=\"reg_invite_code_type == 0 \">\n\t\t\t\t\t\t邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}} \n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t邀请码：{{parent.yqcode}} \n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" v-if=\"xlevelarr\" style=\"color:#666;\">\n\t\t\t\t\t\t<block>\n\t\t\t\t\t\t注册等级：{{xlevelarr.name}} \n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<!-- 系统注册E -->\n\t\t\t\t<!-- 自定义注册S -->\n\t\t\t\t<block v-if=\"show_custom_field\">\n\t\t\t\t\t<view class=\"dp-form-item\">\n\t\t\t\t\t\t<view class=\"label\">手机号<text style=\"color:red\"> * </text></view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dp-form-item\" v-if=\"needsms\">\n\t\t\t\t\t\t<text class=\"label\">验证码</text>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\n\t\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dp-form-item\">\n\t\t\t\t\t\t<view class=\"label\">密码<text style=\"color:red\"> * </text></view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"6-16位字母数字组合密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"pwd\" value=\"\" :password=\"true\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dp-form-item\">\n\t\t\t\t\t\t<view class=\"label\">确认密码</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"再次输入登录密码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"repwd\" value=\"\" :password=\"true\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dp-form-item\" v-if=\"reg_invite_code && !parent\">\n\t\t\t\t\t\t<text class=\"label\">邀请码</text>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :placeholder=\"'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"yqcode\" value=\"\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"dp-form-item\" v-if=\"reg_invite_code && parent\" style=\"color:#666;\">\n\t\t\t\t\t\t<block v-if=\"reg_invite_code_type == 0 \">\n\t\t\t\t\t\t邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}} \n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t邀请码：{{parent.yqcode}} \n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"custom_field\" v-if=\"show_custom_field\">\n\t\t\t\t\t\t<view :class=\"'dp-form-item'\" v-for=\"(item,idx) in formfields.content\"  :key=\"idx\">\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\n\t\t\t\t\t\t\t\t<text v-if=\"item.val5\" style=\"margin-right:10rpx\">{{item.val5}}</text>\n\t\t\t\t\t\t\t\t<input :type=\"(item.val4==1 || item.val4==2) ? 'digit' : 'text'\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" :value=\"custom_formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"  :value=\"custom_formdata['form'+idx]\" @input=\"setfield\" :data-formidx=\"'form'+idx\"/>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\n\t\t\t\t\t\t\t\t<radio-group class=\"flex\" :name=\"'form'+idx\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\" style=\"transform: scale(0.8);\" :checked=\"custom_formdata['form'+idx] && custom_formdata['form'+idx]==item1 ? true : false\"/>{{item1}}\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"flex\" style=\"flex-wrap:wrap\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" style=\"transform: scale(0.8);\" :value=\"item1\" :checked=\"custom_formdata['form'+idx] && inArray(item1,custom_formdata['form'+idx]) ? true : false\"/>{{item1}}\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\n\t\t\t\t\t\t\t\t\t<view v-else style=\"color: #b2b5be;\">请选择</view>\n\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" :value=\"custom_formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t\t\t<view v-else style=\"color: #b2b5be;\">请选择</view>\n\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" :value=\"custom_formdata['form'+idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t\t\t<view v-else style=\"color: #b2b5be;\">请选择</view>\n\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\n\t\t\t\t\t\t\t<block v-if=\"item.key=='region'\">\n\t\t\t\t\t\t\t\t<uni-data-picker :localdata=\"items\" popup-title=\"请选择省市区\" :placeholder=\"custom_formdata['form'+idx] || '请选择省市区'\" @change=\"onchange\" :data-formidx=\"'form'+idx\"></uni-data-picker>\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"regiondata ? regiondata : custom_formdata['form'+idx]\"/>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"editorFormdata[idx]\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image src=\"/static/img/ico-del.png\" class=\"image\"></image></view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\" :data-idx=\"idx\"/></view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view v-else class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\n\t\t\t\t</block>\n\t\t\t\t<!-- 自定义注册E -->\n\t\t\t\t<view class=\"xieyi-item\" v-if=\"xystatus==1\">\n\t\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox class=\"checkbox\" value=\"1\" :checked=\"isagree\"/>阅读并同意</label></checkbox-group>\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun\">{{xyname}}</text>\n\t\t\t\t\t<text @tap=\"showxieyiFun\" v-if=\"xyname2\">和</text>\n\t\t\t\t\t<text :style=\"{color:t('color1')}\" @tap=\"showxieyiFun2\" v-if=\"xyname2\">{{xyname2}}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<button class=\"form-btn\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" form-type=\"submit\">注册</button>\n\t\t\t</view>\n\t\t\t</form>\n\t\t\t<view class=\"tologin\" @tap=\"goto\" data-url=\"login\" data-opentype=\"redirect\">已有账号? 前去登录</view>\n\t\t\t\n\t\t\t<block v-if=\"logintype_2 || logintype_3\">\n\t\t\t\t<view class=\"othertip\">\n\t\t\t\t\t<view class=\"othertip-line\"></view>\n\t\t\t\t\t<view class=\"othertip-text\">\n\t\t\t\t\t\t<text class=\"txt\">其他方式登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"othertip-line\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"othertype\">\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_3\" @tap=\"weixinlogin\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/login-'+platformimg+'.png'\"/>\n\t\t\t\t\t\t<text class=\"txt\">{{platformname}}登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"othertype-item\" v-if=\"logintype_2\" @tap=\"goto\" data-url=\"login?logintype=2\" data-opentype=\"redirect\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/reg-tellogin.png'\"/>\n\t\t\t\t\t\t<text class=\"txt\">手机号登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</block>\n\t\t<!-- 绑定手机号 -->\n\t\t<block v-if=\"logintype==4\">\n\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t<view class=\"authlogin\">\n\t\t\t\t\t<view class=\"authlogin-logo\"><image :src=\"logo\" style=\"width:100%;height:100%\"/></view>\n\t\t\t\t\t<view class=\"authlogin-name\">授权登录{{name}}</view>\n\t\t\t\t\t<view class=\"authlogin-parent\" v-if=\"reg_invite_code && parent\" style=\"margin-top:30rpx;text-align:center;color:#666;\">\n\t\t\t\t\t\t<block v-if=\"reg_invite_code_type == 0\">\n\t\t\t\t\t\t\t邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;vertical-align:middle;\"></image> {{parent.nickname}}\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t邀请码：{{parent.yqcode}}\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"authlogin-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">{{platformname}}授权绑定手机号</button>\n\t\t\t\t\t<button class=\"authlogin-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\n\t\t\t\t</view>\n\t\t\t<!-- #endif -->\n\t\t\t<!--  #ifndef MP-WEIXIN -->\n\t\t\t\t<form @submit=\"bindregister\" @reset=\"formReset\">\n\t\t\t\t\t<view class=\"title\">绑定手机号</view>\n\t\t\t\t\t<view class=\"regform\">\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"reg_invite_code && parent\" style=\"color:#666;\">\n\t\t\t\t\t\t\t<block v-if=\"reg_invite_code_type == 0\">\n\t\t\t\t\t\t\t\t邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}}\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t邀请码：{{parent.yqcode}}\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" class=\"img\"/>\n\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"tel\" value=\"\" @input=\"telinput\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t<image src=\"/static/img/reg-code.png\" class=\"img\"/>\n\t\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入验证码\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" name=\"smscode\" value=\"\"/>\n\t\t\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"smscode\">{{smsdjs||'获取验证码'}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\n\t\t\t\t\t\t<button class=\"form-btn2\" @tap=\"nobindregister\" v-if=\"login_bind==1\">暂不绑定</button>\n\t\t\t\t\t</view>\n\t\t\t\t</form>\n\t\t\t<!-- #endif -->\n\t\t</block>\n\n\t\t\n\t\t<!-- 设置头像昵称 -->\n\t\t<block v-if=\"logintype==5\">\n\t\t\t<form @submit=\"setnicknameregister\" @reset=\"formReset\">\n\t\t\t\t<view class=\"title\">请设置头像昵称</view>\n\t\t\t\t<view class=\"regform\">\n\t\t\t\t\t<!--  #ifdef MP-WEIXIN -->\n\t\t\t\t\t<view class=\"form-item\" v-if=\"reg_invite_code && parent\" style=\"color:#666;margin-bottom:20rpx;\">\n\t\t\t\t\t\t<block v-if=\"reg_invite_code_type == 0\">\n\t\t\t\t\t\t\t邀请人：<image :src=\"parent.headimg\" style=\"width: 80rpx; height: 80rpx;border-radius: 50%;\"></image> {{parent.nickname}}\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t邀请码：{{parent.yqcode}}\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n\t\t\t\t\t\t<view class=\"flex1\">头像</view>\n\t\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" style=\"width:100rpx;height:100rpx;\">\n\t\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100%;height:100%;border-radius:50%\"></image>\n\t\t\t\t\t\t</button> \n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\n\t\t\t\t\t\t<input type=\"nickname\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t<!--  #ifndef MP-WEIXIN -->\n\t\t\t\t\t<view class=\"form-item\" style=\"height:120rpx;line-height:120rpx\">\n\t\t\t\t\t\t<view class=\"flex1\">头像</view>\n\t\t\t\t\t\t<image :src=\"headimg || default_headimg\" style=\"width:100rpx;height:100rpx;border-radius:50%\" @tap=\"uploadHeadimg\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"flex1\">昵称</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入昵称\" name=\"nickname\" value=\"\" placeholder-style=\"font-size:30rpx;color:#B2B5BE\" style=\"text-align:right\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t<button class=\"form-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</button>\n\t\t\t\t\t<button class=\"form-btn2\" @tap=\"nosetnicknameregister\" v-if=\"login_setnickname==1\">暂不设置</button>\n\t\t\t\t</view>\n\t\t\t</form>\n\t\t</block>\n\n\t\t<view v-if=\"showxieyi\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"xycontent\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi\">已阅读并同意</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"showxieyi2\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"xycontent2\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidexieyi2\">已阅读并同意</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tplatform2:app.globalData.platform2,\n\t\t\t\n\t\t\tplatform:'',\n\t\t\tplatformname:'',\n\t\t\tplatformimg:'weixin',\n\t\t\tlogintype:0,\n\t\t\tlogintype_1:true,\n\t\t\tlogintype_2:false,\n\t\t\tlogintype_3:false,\n\t\t\tlogo:'',\n\t\t\tname:'',\n\t\t\txystatus:0,\n\t\t\txyname:'',\n\t\t\txycontent:'',\n\t\t\txyname2:'',\n\t\t\txycontent2:'',\n\t\t\tneedsms:false,\n\t\t\tshowxieyi:false,\n\t\t\tshowxieyi2:false,\n\t\t\tisagree:false,\n      smsdjs: '',\n\t\t\ttel:'',\n      hqing: 0,\n\t\t\tfrompage:'/pages/my/usercenter',\n\t\t\twxloginclick:false,\n\t\t\tlogin_bind:0,\n\t\t\tlogin_setnickname:0,\n      reg_invite_code:0,\n      reg_invite_code_text:'',\n\t\t\treg_invite_code_type:0,\n      parent:{},\n\t        xlevelarr:{},\n\t\t\t//自定义表单Start\n\t\t\thas_custom:0,\n\t\t\tshow_custom_field:false,\n\t\t\tregiondata:'',\n\t\t\teditorFormdata:{},\n\t\t\ttest:'',\n\t\t\tformfields:[],\n\t\t\tcustom_formdata:[],\n\t\t\titems: [],\n\t\t\tformvaldata:{},\n\t\t\tsubmitDisabled:false,\n\t\t\t//自定义表单End\n\t\t\ttmplids:[],\n\t\t\tdefault_headimg:app.globalData.pre_url + '/static/img/touxiang.png',\n\t\t\theadimg:'',\n\t\t\tnickname:'',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\t\n\t\tif(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);\n\t\tif(this.opt.logintype) this.logintype = this.opt.logintype;\n\t\tif(this.opt.login_bind) this.login_bind = this.opt.login_bind;\n\t\t   // 如果 opt 中有 xlevel，则保存到全局变量中\n\t\t \n\t\tthis.getdata();\n  },\n   \n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiIndex/reg', {pid:app.globalData.pid,xlevel:app.globalData.xlevel}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.alert(res.msg);return;\n\t\t\t\t}\n\t\t\t\tthat.logintype_2 = res.logintype_2;\n\t\t\t\tthat.logintype_3 = res.logintype_3;\n\t\t\t\tthat.logintype_3 = res.logintype_3;\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif(that.platform2 == 'ios'){\n\t\t\t\t\tif (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {\n\t\t\t\t\t\t\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.logintype_3 = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\tthat.xystatus = res.xystatus;\n\t\t\t\tthat.xyname = res.xyname;\n\t\t\t\tthat.xycontent = res.xycontent;\n\t\t\t\tthat.xyname2 = res.xyname2;\n\t\t\t\tthat.xycontent2 = res.xycontent2;\n\t\t\t\tthat.logo = res.logo;\n\t\t\t\tthat.name = res.name;\n\t\t\t\tthat.needsms = res.needsms;\n\t\t\t\tthat.platform = res.platform;\n        that.reg_invite_code = res.reg_invite_code;\n        that.reg_invite_code_text = res.reg_invite_code_text;\n        that.reg_invite_code_type = res.reg_invite_code_type;\n        that.parent = res.parent;\n\t\tthat.xlevelarr = res.xlevelarr;\n\t\t\t\tif(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){\n\t\t\t\t\tthat.platformname = '微信';\n\t\t\t\t\tthat.platformimg = 'weixin';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'toutiao'){\n\t\t\t\t\tthat.platformname = '头条';\n\t\t\t\t\tthat.platformimg = 'toutiao';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'alipay'){\n\t\t\t\t\tthat.platformname = '支付宝';\n\t\t\t\t\tthat.platformimg = 'alipay';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'qq'){\n\t\t\t\t\tthat.platformname = 'QQ';\n\t\t\t\t\tthat.platformimg = 'qq';\n\t\t\t\t}\n\t\t\t\tif(that.platform == 'baidu'){\n\t\t\t\t\tthat.platformname = '百度';\n\t\t\t\t\tthat.platformimg = 'baidu';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t//自定义表单\n\t\t\t\tif(res.has_custom){\n\t\t\t\t\tthat.formfields = res.custom_form_field;\n\t\t\t\t\tthat.has_custom = res.has_custom\n\t\t\t\t\tthat.show_custom_field = true\n\t\t\t\t\tuni.request({\n\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\n\t\t\t\t\t\tdata: {},\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\theader: { 'content-type': 'application/json' },\n\t\t\t\t\t\tsuccess: function(res2) {\n\t\t\t\t\t\t\tthat.items = res2.data\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    formSubmit: function (e) {\n\t\t\tvar that = this;\n      var formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n      if (formdata.pwd == '') {\n        app.alert('请输入密码');\n        return;\n      }\n      if (formdata.pwd.length < 6) {\n        app.alert('新密码不小于6位');\n        return;\n      }\n      if (formdata.repwd == '') {\n        app.alert('请再次输入新密码');\n        return;\n      }\n      if (formdata.pwd != formdata.repwd) {\n        app.alert('两次密码不一致');\n        return;\n      }\n\t\t\tif(that.needsms){\n\t\t\t\tif (formdata.smscode == '') {\n\t\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tformdata.smscode = '';\n\t\t\t}\n\t\t\tvar postdata = {xlevel:app.globalData.xlevel,tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode,pid:app.globalData.pid,yqcode:formdata.yqcode,xlevel:app.globalData.xlevel}\n\t\t\t//如果有自定义表单则验证表单内容\n\t\t\tif(that.show_custom_field){\n\t\t\t\tvar customformdata = {};\n\t\t\t\tvar customData = that.checkCustomFormFields();\n\t\t\t\tif(!customData){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tpostdata['customformdata'] = customData\n\t\t\t\tpostdata['customformid'] = that.formfields.id\n\t\t\t}\n\t\t\t\n      if (that.xystatus == 1 && !that.isagree) {\n        app.error('请先阅读并同意用户注册协议');\n        return false;\n      }\n\t\t\tapp.showLoading('提交中');\n      app.post(\"ApiIndex/regsub\", postdata, function (data) {\n\t\t\t\tapp.showLoading(false);\n        if (data.status == 1) {\n          app.success(data.msg);\n\t\t\t\t\tif(data.tmplids){\n\t\t\t\t\t\tthat.tmplids = data.tmplids;\n\t\t\t\t\t}\n\t\t\t\t\tthat.subscribeMessage(function () {\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tif(that.opt.fromapp==1 && data.toappurl){\n\t\t\t\t\t\t\t\tapp.goto(data.toappurl,'redirect');\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tapp.goto('/pages/my/usercenter','redirect');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n        } else {\n          app.error(data.msg);\n        }\n      });\n    },\n\t\tgetPhoneNumber: function (e) {\n\t\t\tvar that = this\n\t\t\tif(e.detail.errMsg == \"getPhoneNumber:fail user deny\"){\n\t\t\t\tapp.error('请同意授权获取手机号');return;\n\t\t\t}\n\t\t\twx.login({success (res1){\n\t\t\t\tconsole.log(res1);\n\t\t\t\tvar code = res1.code;\n\t\t\t\t//用户允许授权\n\t\t\t\tapp.post('ApiIndex/wxRegister',{ iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\n\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t})\n\t\t\t}});\n\t\t},\n\t\tsetnicknameregister:function(e){\n\t\t\t//console.log(e);\n\t\t\t//return;\n\t\t\tthis.nickname = e.detail.value.nickname;\n\t\t\tif(this.nickname == '' || this.headimg == ''){\n\t\t\t\tapp.alert('请设置头像和昵称');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t}else{\n\t\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t\t}\n\t\t},\n\t\tnosetnicknameregister:function(){\n\t\t\tthis.nickname = '';\n\t\t\tthis.headimg = '';\n\t\t\tif(this.login_bind!=0){\n\t\t\t\tthis.logintype = 4;\n\t\t\t}else{\n\t\t\t\tthis.register('','','','');\n\t\t\t}\n\t\t},\n\t\tbindregister:function(e){\n\t\t\tvar that = this;\n\t\t\tvar formdata = e.detail.value;\n      if (formdata.tel == ''){\n        app.alert('请输入手机号');\n        return;\n      }\n\t\t\tif (formdata.smscode == '') {\n\t\t\t\tapp.alert('请输入短信验证码');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);\n\t\t},\n\t\tnobindregister:function(){\n\t\t\tthis.register(this.headimg,this.nickname,'','');\n\t\t},\n\t\tregister:function(headimg,nickname,tel,smscode){\n\t\t\tvar that = this;\n\t\t\tvar url = '';\n\t\t\tif(that.platform == 'app') {\n\t\t\t\turl = 'ApiIndex/appwxRegister';\n\t\t\t} else if(that.platform=='mp' || that.platform=='h5') {\n\t\t\t\turl = 'ApiIndex/shouquanRegister';\n\t\t\t} else {\n\t\t\t\turl = 'ApiIndex/'+that.platform+'Register';\n\t\t\t}\n\t\t\tapp.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){\n\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res2.msg);\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t});\n\t\t},\n\t\tweixinlogin:function(){\n\t\t\tvar that = this;\n\t\t\tif (that.xystatus == 1 && !that.isagree) {\n\t\t\t\tthat.showxieyi = true;\n\t\t\t\tthat.wxloginclick = true;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.wxloginclick = false;\n\t\t\tapp.authlogin(function(res){\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto(that.frompage,'redirect');\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else if (res.status == 3) {\n\t\t\t\t\tthat.logintype = 5;\n\t\t\t\t\tthat.login_setnickname = res.login_setnickname\n\t\t\t\t\tthat.login_bind = res.login_bind\n\t\t\t\t} else if (res.status == 2) {\n\t\t\t\t\tthat.logintype = 4;\n\t\t\t\t\tthat.login_bind = res.login_bind\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    isagreeChange: function (e) {\n      var val = e.detail.value;\n      if (val.length > 0) {\n        this.isagree = true;\n      } else {\n        this.isagree = false;\n      }\n      console.log(this.isagree);\n    },\n    showxieyiFun: function () {\n      this.showxieyi = true;\n    },\n    hidexieyi: function () {\n      this.showxieyi = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n    },\n    showxieyiFun2: function () {\n      this.showxieyi2 = true;\n    },\n    hidexieyi2: function () {\n      this.showxieyi2 = false;\n\t\t\tthis.isagree = true;\n\t\t\tif(this.wxloginclick){\n\t\t\t\tthis.weixinlogin();\n\t\t\t}\n\t\t\tif(this.iosloginclick){\n\t\t\t\tthis.ioslogin();\n\t\t\t}\n    },\n    telinput: function (e) {\n      this.tel = e.detail.value\n    },\n\t\tuploadHeadimg:function(){\n\t\t\tvar that = this;\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\n\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\n\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\t\t\tfilePath: tempFilePath,\n\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: function(res) { //alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tonChooseAvatar:function(e){\n\t\t\tconsole.log(e)\n\t\t\tvar that = this;\n\t\t\tapp.showLoading('上传中');\n\t\t\tuni.uploadFile({\n\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',\n\t\t\t\tfilePath: e.detail.avatarUrl,\n\t\t\t\tname: 'file',\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\tthat.headimg = data.url;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: function(res) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    smscode: function () {\n      var that = this;\n      if (that.hqing == 1) return;\n      that.hqing = 1;\n      var tel = that.tel;\n      if (tel == '') {\n        app.alert('请输入手机号码');\n        that.hqing = 0;\n        return false;\n      }\n      if (!/^1[3456789]\\d{9}$/.test(tel)) {\n        app.alert(\"手机号码有误，请重填\");\n        that.hqing = 0;\n        return false;\n      }\n      app.post(\"ApiIndex/sendsms\", {tel: tel}, function (data) {\n        if (data.status != 1) {\n          app.alert(data.msg);\n        } \n      });\n      var time = 120;\n      var interval1 = setInterval(function () {\n        time--;\n        if (time < 0) {\n          that.smsdjs = '重新获取';\n          that.hqing = 0;\n          clearInterval(interval1);\n        } else if (time >= 0) {\n          that.smsdjs = time + '秒';\n        }\n      }, 1000);\n    },\n\t\t//自定义表单\n\t\tonchange(e) {\n\t\t  const value = e.detail.value\n\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;\n\t\t},\n\t\tsetfield:function(e){\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tvar value = e.detail.value;\n\t\t\tthis.formvaldata[field] = value;\n\t\t},\n\t\teditorBindPickerChange:function(e){\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar val = e.detail.value;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\t\n\t\t\tif(!editorFormdata) editorFormdata = {};\n\t\t\teditorFormdata[idx] = val;\n\t\t\tthat.editorFormdata = editorFormdata\n\t\t\tthis.test = Math.random();\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tthis.formvaldata[field] = val;\n\t\t},\n\t\tcheckCustomFormFields:function(e){\n\t\t\tvar that = this;\n\t\t\tvar subdata = this.formvaldata;\n\t\t\tvar formcontent = that.formfields.content;\n\t\t\tvar formid = that.formfields.id;\n\t\t\tvar formdata = new Array();\n\t\t\tfor (var i = 0; i < formcontent.length;i++){\n\t\t\t\t//console.log(subdata['form' + i]);\n\t\t\t\tif (formcontent[i].key == 'region') {\n\t\t\t\t\t\tsubdata['form' + i] = that.regiondata;\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){\n\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 必填');return false;\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key =='switch'){\n\t\t\t\t\t\tif (subdata['form' + i]==false){\n\t\t\t\t\t\t\t\tsubdata['form' + i] = '否'\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tsubdata['form' + i] = '是'\n\t\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key == 'selector') {\n\t\t\t\t\t\tsubdata['form' + i] = formcontent[i].val2[subdata['form' + i]]\n\t\t\t\t}\n\t\t\t\tif (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){\n\t\t\t\t\tif(formcontent[i].val4 == '2'){ //手机号\n\t\t\t\t\t\tif (!/^1[3456789]\\d{9}$/.test(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(formcontent[i].val4 == '3'){ //身份证号\n\t\t\t\t\t\tif (!/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(formcontent[i].val4 == '4'){ //邮箱\n\t\t\t\t\t\tif (!/^(.+)@(.+)$/.test(subdata['form' + i])) {\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tformdata.push(subdata['form' + i])\n\t\t\t}\n\t\t\treturn subdata;\n\t\t},\n\t\teditorChooseImage: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\tapp.chooseImage(function(data){\n\t\t\t\teditorFormdata[idx] = data[0];\n\t\t\t\tconsole.log(editorFormdata)\n\t\t\t\tthat.editorFormdata = editorFormdata\n\t\t\t\tthat.test = Math.random();\n\t\t\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\t\tthat.formvaldata[field] = data[0];\n\t\t\n\t\t\t})\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar field = e.currentTarget.dataset.formidx;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\teditorFormdata[idx] = '';\n\t\t\tthat.editorFormdata = editorFormdata\n\t\t\tthat.test = Math.random();\n\t\t\tthat.formvaldata[field] = '';\n\t\t},\n  }\n};\n</script>\n\n<style>\npage{background:#ffffff}\n.container{width:100%;}\n.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}\n.regform{ width:100%;padding:0 50rpx;border-radius:5px;background: #FFF;}\n.regform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx}\n.regform .form-item:last-child{border:0}\n.regform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}\n.regform .form-item .input{flex:1;color: #000;}\n.regform .form-item .code{font-size:30rpx}\n.regform .xieyi-item{display:flex;align-items:center;margin-top:50rpx}\n.regform .xieyi-item{font-size:24rpx;color:#B2B5BE}\n.regform .xieyi-item .checkbox{transform: scale(0.6);}\n.regform .form-btn{margin-top:20rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}\n.regform .form-btn2{width:100%;height:80rpx;line-height:80rpx;background:#EEEEEE;border-radius:40rpx;color:#A9A9A9;margin-top:30rpx}\n.tologin{color:#737785;font-size:26rpx;display:flex;width:100%;padding:0 80rpx;margin-top:30rpx}\n\n.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:60rpx;}\n.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}\n.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\n.othertip-text .txt{color:#A3A3A3;font-size:22rpx}\n\n.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}\n.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}\n.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}\n.othertype-item .txt{color:#A3A3A3;font-size:24rpx}\n\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\n\n\n.authlogin{display:flex;flex-direction:column;align-items:center}\n.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}\n.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}\n.authlogin-parent{margin-top:30rpx;text-align:center;color:#666;}\n.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}\n.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}\n\n\n/* 自定义字段显示 */\n.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:16rpx 0px;display:flex;align-items: center;}\n.dp-form-item:last-child{border:0}\n.dp-form-item .label{line-height: 40rpx;width:140rpx;margin-right: 10px;flex-shrink:0;text-align: right;color: #666666;}\n.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}\n.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.dp-form-item .radio2{display:flex;align-items:center;}\n.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.dp-form-item .layui-form-switch{}\n.dp-form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}\n\n.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;flex-direction:column;align-items: flex-start;}\n.dp-form-item2:last-child{border:0}\n.dp-form-item2 .label{height:70rpx;line-height: 70rpx;width:100%;margin-right: 10px;}\n.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}\n.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}\n.dp-form-item2 .radio2{display:flex;align-items:center;}\n.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}\n.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.dp-form-item2 .layui-form-switch{}\n.dp-form-item2 .picker{height: 70rpx;line-height:70rpx;flex:1;width:100%;}\n.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}\n\n.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\n.dp-form-imgbox-close .image{width:100%;height:100%}\n.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.dp-form-imgbox-img>.image{max-width:100%;}\n.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./reg.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./reg.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115021632\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}