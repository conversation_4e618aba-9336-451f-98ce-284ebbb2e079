{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?0d46", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?01d6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?e0cd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?b75a", "uni-app:///pagesExt/maidan/maidanlog.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?0af9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidan/maidanlog.vue?5013"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "field", "order", "status", "datalist", "pagenum", "nodata", "nomore", "keyword", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "app", "that", "uni", "filterClick", "sortClick", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0ElxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAZ;QACAG;QACAL;QACAF;QACAC;MACA;QACAgB;QACAC;QACA;QACA;UACAD;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACAF;MACAA;MACAA;MACAA;IACA;IACAG;MACA;MACA;MACAH;MACAA;MACAA;IACA;IACAI;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/maidan/maidanlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/maidan/maidanlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./maidanlog.vue?vue&type=template&id=6344fe69&\"\nvar renderjs\nimport script from \"./maidanlog.vue?vue&type=script&lang=js&\"\nexport * from \"./maidanlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./maidanlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/maidan/maidanlog.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maidanlog.vue?vue&type=template&id=6344fe69&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.status == \"\" ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.status == \"1\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.status == \"0\" ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && _vm.field == \"money\" ? _vm.t(\"color1\") : null\n  var m4 =\n    _vm.isload && _vm.field == \"money\" && _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.field == \"money\" && _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m6 = item.status == 1 ? _vm.t(\"color1\") : null\n        var m7 = item.paidui_jiang && parseFloat(item.paidui_jiang) > 0\n        var m8 = m7 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maidanlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maidanlog.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 搜索容器 -->\n\t\t<view class=\"search-container\">\n\t\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索订单号、商家名称\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" confirm-type=\"search\" @confirm=\"searchConfirm\"></input>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 筛选导航栏 -->\n\t\t\t<view class=\"filter-navbar\">\n\t\t\t\t<view @tap=\"filterClick\" class=\"filter-item\" :style=\"status==''?'color:'+t('color1'):''\" data-status=\"\">全部</view>\n\t\t\t\t<view @tap=\"filterClick\" class=\"filter-item\" :style=\"status=='1'?'color:'+t('color1'):''\" data-status=\"1\">已完成</view>\n\t\t\t\t<view @tap=\"filterClick\" class=\"filter-item\" :style=\"status=='0'?'color:'+t('color1'):''\" data-status=\"0\">待处理</view>\n\t\t\t\t<view @tap=\"sortClick\" class=\"filter-item\" data-field=\"money\" :data-order=\"order=='asc'?'desc':'asc'\">\n\t\t\t\t\t<text :style=\"field=='money'?'color:'+t('color1'):''\">金额排序</text>\n\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='money'&&order=='asc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='money'&&order=='desc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单列表 -->\n\t\t<view class=\"order-list\" id=\"datalist\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t<view class=\"order-item\" @tap=\"goto\" :data-url=\"'maidandetail?id=' + item.id\">\n\t\t\t\t<!-- 订单头部信息 -->\n\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t<view class=\"order-time\">{{item.paytime}}</view>\n\t\t\t\t\t<view class=\"order-status\" :style=\"{color:item.status==1?t('color1'):'#999'}\">\n\t\t\t\t\t\t{{item.status==1?'已完成':'待处理'}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 订单主体信息 -->\n\t\t\t\t<view class=\"order-body\">\n\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t<view class=\"order-title\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"order-detail\">\n\t\t\t\t\t\t\t<text class=\"detail-item\">订单号：{{item.ordernum}}</text>\n\t\t\t\t\t\t\t<text class=\"detail-item\">支付方式：{{item.paytype}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 奖励信息 -->\n\t\t\t\t\t\t<view class=\"reward-info\" v-if=\"item.paidui_jiang && parseFloat(item.paidui_jiang) > 0\">\n\t\t\t\t\t\t\t<text class=\"reward-label\">排队奖励：</text>\n\t\t\t\t\t\t\t<text class=\"reward-amount\" :style=\"{color:t('color1')}\">+￥{{item.paidui_jiang}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-amount\">\n\t\t\t\t\t\t<text class=\"amount-text\">-￥{{item.money}}</text>\n\t\t\t\t\t\t<text class=\"amount-label\">支付金额</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 订单底部信息 -->\n\t\t\t\t<!-- <view class=\"order-footer\" v-if=\"item.tuozhanfei && parseFloat(item.tuozhanfei) > 0\">\n\t\t\t\t\t<text class=\"fee-info\">拓展费：￥{{item.tuozhanfei}}</text>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n\t\t\tpre_url:app.globalData.pre_url,\n      field: 'id',\n\t\t\torder:'desc',\n      status: '',\n      datalist: [],\n      pagenum: 1,\n\t\t\tnodata:false,\n      nomore: false,\n\t\t\tkeyword:'',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n\t\t\tthis.nodata = false;\n\t\t\tthis.nomore = false;\n\t\t\tthis.loading = true;\n      app.post('ApiMaidan/maidanlog', {\n\t\t\t\tpagenum: pagenum,\n\t\t\t\tkeyword:that.keyword,\n\t\t\t\tstatus:that.status,\n\t\t\t\tfield:that.field,\n\t\t\t\torder:that.order\n\t\t\t}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n        var data = res.data;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    filterClick: function (e) {\n      var that = this;\n      var status = e.currentTarget.dataset.status;\n      that.status = status;\n      that.pagenum = 1;\n      that.datalist = [];\n      that.getdata();\n    },\n    sortClick: function (e) {\n      var that = this;\n      var t = e.currentTarget.dataset;\n      that.field = t.field;\n      that.order = t.order;\n      that.getdata();\n    },\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n\t\t\tthis.pagenum = 1;\n\t\t\tthis.datalist = [];\n      this.getdata(false);\n\t\t}\n  }\n};\n</script>\n<style>\n/* 搜索容器样式 */\n.search-container {\n\tposition: fixed;\n\twidth: 100%;\n\tbackground: #fff;\n\tz-index: 9;\n\ttop: var(--window-top);\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.topsearch {\n\twidth: 100%;\n\tpadding: 16rpx 20rpx;\n}\n\n.topsearch .f1 {\n\theight: 60rpx;\n\tborder-radius: 30rpx;\n\tborder: 0;\n\tbackground-color: #f7f7f7;\n\tflex: 1;\n}\n\n.topsearch .f1 .img {\n\twidth: 24rpx;\n\theight: 24rpx;\n\tmargin-left: 10px;\n}\n\n.topsearch .f1 input {\n\theight: 100%;\n\tflex: 1;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 筛选导航栏样式 */\n.filter-navbar {\n\tdisplay: flex;\n\ttext-align: center;\n\talign-items: center;\n\tpadding: 5rpx 0;\n\tborder-top: 1rpx solid #f5f5f5;\n}\n\n.filter-item {\n\tflex: 1;\n\theight: 70rpx;\n\tline-height: 70rpx;\n\tposition: relative;\n\tfont-size: 26rpx;\n\tfont-weight: bold;\n\tcolor: #323232;\n\tpadding: 0 5rpx;\n}\n\n.filter-item .iconshangla {\n\tposition: absolute;\n\ttop: -4rpx;\n\tpadding: 0 6rpx;\n\tfont-size: 20rpx;\n\tcolor: #7D7D7D;\n}\n\n.filter-item .icondaoxu {\n\tposition: absolute;\n\ttop: 8rpx;\n\tpadding: 0 6rpx;\n\tfont-size: 20rpx;\n\tcolor: #7D7D7D;\n}\n\n/* 订单列表样式 */\n.order-list {\n\twidth: 100%;\n\tmargin-top: 200rpx;\n\tpadding: 0 24rpx;\n}\n\n.order-item {\n\twidth: 100%;\n\tbackground: #fff;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\n}\n\n/* 订单头部样式 */\n.order-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 24rpx 16rpx;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.order-time {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.order-status {\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n}\n\n/* 订单主体样式 */\n.order-body {\n\tdisplay: flex;\n\tpadding: 20rpx 24rpx;\n\talign-items: flex-start;\n}\n\n.order-info {\n\tflex: 1;\n\tmargin-right: 20rpx;\n}\n\n.order-title {\n\tfont-size: 30rpx;\n\tcolor: #222;\n\tfont-weight: bold;\n\tline-height: 42rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.order-detail {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8rpx;\n}\n\n.detail-item {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 34rpx;\n}\n\n.reward-info {\n\tmargin-top: 12rpx;\n\tpadding: 8rpx 12rpx;\n\tbackground: rgba(255,107,107,0.1);\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.reward-label {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.reward-amount {\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n\tmargin-left: 8rpx;\n}\n\n.order-amount {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n\ttext-align: right;\n}\n\n.amount-text {\n\tfont-size: 32rpx;\n\tcolor: #ff4757;\n\tfont-weight: bold;\n\tline-height: 44rpx;\n}\n\n.amount-label {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tmargin-top: 4rpx;\n}\n\n/* 订单底部样式 */\n.order-footer {\n\tpadding: 12rpx 24rpx 20rpx;\n\tborder-top: 1rpx solid #f5f5f5;\n}\n\n.fee-info {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maidanlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maidanlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102829\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}