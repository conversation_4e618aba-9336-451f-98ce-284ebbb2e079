{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?a987", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?e6db", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?0ad8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?7878", "uni-app:///pagesExt/cityagent/merchant.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?630e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/merchant.vue?42fd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "nodata", "nomore", "page", "limit", "keyword", "datalist", "statistics", "total_merchants", "active_merchants", "today_new_merchants", "total_orders", "total_amount", "month_amount", "statusIndex", "statusList", "key", "name", "typeIndex", "typeList", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getMerchantTypes", "app", "that", "res", "getdata", "params", "uni", "title", "onSearch", "onStatusChange", "onTypeChange", "callMerchant", "content", "success", "phoneNumber", "fail", "viewLocation", "longitude", "latitude", "scale", "goto", "gotoBusinessManage", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgJjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACAC,WACA;QAAAH;QAAAC;MAAA;IAEA;EACA;EAEAG;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACA;UACA;UACAC;YAAAV;YAAAC;UAAA;UACAU;YACAD;cACAV;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MAAA;MACA;MAEA;QACAF;QACAA;QACAA;MACA;MAEA;QACAvB;QACAC;MACA;;MAEA;MACA;QACAyB;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEAJ;QACAC;QACAI;QAEA;UACAL;UACA;QACA;;QAEA;QACAK;UACAC;QACA;;QAEA;QACA;UACAL;QACA;;QAEA;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;QAEAA;MACA;IACA;IAEA;IACAM;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAV;QACA;MACA;MAEAK;QACAC;QACAK;QACAC;UACA;YACAP;cACAQ;cACAC;gBACAd;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAe;MACA;MACA;MACA;MAEA;QACAf;QACA;MACA;MAEAK;QACAW;QACAC;QACAzB;QACA0B;QACAJ;UACAd;QACA;MACA;IACA;IAEA;IACAmB;MACA;MACA;QACAnB;MACA;IACA;IAEA;IACAoB;MACA;MACA;QACA;QACApB;MACA;IACA;IAEA;IACAqB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvXA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/merchant.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/merchant.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./merchant.vue?vue&type=template&id=c78db89e&\"\nvar renderjs\nimport script from \"./merchant.vue?vue&type=script&lang=js&\"\nexport * from \"./merchant.vue?vue&type=script&lang=js&\"\nimport style0 from \"./merchant.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/merchant.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant.vue?vue&type=template&id=c78db89e&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- 顶部统计卡片 -->\r\n\t\t<view class=\"stats-container\">\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{statistics.total_merchants}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">总商户数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{statistics.active_merchants}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">活跃商户</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{statistics.today_new_merchants}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">今日新增</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"amount-card\">\r\n\t\t\t\t<view class=\"amount-item\">\r\n\t\t\t\t\t<text class=\"amount-label\">总交易额</text>\r\n\t\t\t\t\t<text class=\"amount-value\">¥{{statistics.total_amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"amount-item\">\r\n\t\t\t\t\t<text class=\"amount-label\">本月交易额</text>\r\n\t\t\t\t\t<text class=\"amount-value\">¥{{statistics.month_amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 筛选条件 -->\r\n\t\t<view class=\"filter-container\">\r\n\t\t\t<view class=\"filter-row\">\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\tclass=\"search-input\" \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tv-model=\"keyword\" \r\n\t\t\t\t\t\tplaceholder=\"搜索商户名称\" \r\n\t\t\t\t\t\t@confirm=\"onSearch\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<text class=\"iconfont iconsousuo search-icon\" @tap=\"onSearch\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"filter-row\">\r\n\t\t\t\t<picker @change=\"onStatusChange\" :value=\"statusIndex\" :range=\"statusList\" range-key=\"name\">\r\n\t\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t\t<text>{{statusList[statusIndex].name}}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t\t<picker @change=\"onTypeChange\" :value=\"typeIndex\" :range=\"typeList\" range-key=\"name\">\r\n\t\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t\t<text>{{typeList[typeIndex].name}}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商户列表 -->\r\n\t\t<view class=\"merchant-list\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"merchant-item\" \r\n\t\t\t\tv-for=\"(item, index) in datalist\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@tap=\"goto\" \r\n\t\t\t\t:data-url=\"'merchant_detail?merchant_id=' + item.id\"\r\n\t\t\t>\r\n\t\t\t\t<!-- 商户头部信息 -->\r\n\t\t\t\t<view class=\"merchant-header\">\r\n\t\t\t\t\t<view class=\"merchant-avatar\">\r\n\t\t\t\t\t\t<image :src=\"item.logo || '/static/img/default_merchant.png'\" class=\"avatar-img\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"merchant-info\">\r\n\t\t\t\t\t\t<view class=\"merchant-name\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"merchant-category\">{{item.category_name || '未分类'}}</view>\r\n\t\t\t\t\t\t<view class=\"merchant-address\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont iconweizhi\"></text>\r\n\t\t\t\t\t\t\t<text class=\"address-text\">{{item.address || '暂无地址'}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"merchant-status\">\r\n\t\t\t\t\t\t<text class=\"status-badge\" :style=\"{background: item.status_color}\">\r\n\t\t\t\t\t\t\t{{item.status_text}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 商户统计信息 -->\r\n\t\t\t\t<view class=\"merchant-stats\">\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-label\">订单数</text>\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{item.order_count}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-label\">交易额</text>\r\n\t\t\t\t\t\t<text class=\"stat-value\">¥{{item.order_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-label\">商品数</text>\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{item.product_count}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-label\">余额</text>\r\n\t\t\t\t\t\t<text class=\"stat-value\">¥{{item.money}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 操作按钮 -->\r\n\t\t\t\t<view class=\"merchant-actions\">\r\n\t\t\t\t\t<view class=\"action-btn\" @tap.stop=\"callMerchant\" :data-phone=\"item.phone\">\r\n\t\t\t\t\t\t<text class=\"iconfont icondianhua\"></text>\r\n\t\t\t\t\t\t<text>电话</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"action-btn\" @tap.stop=\"gotoBusinessManage\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t<text class=\"iconfont iconguanli\"></text>\r\n\t\t\t\t\t\t<text>详情</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"action-btn\" @tap.stop=\"viewLocation\" :data-longitude=\"item.longitude\" :data-latitude=\"item.latitude\" :data-name=\"item.name\">\r\n\t\t\t\t\t\t<text class=\"iconfont iconweizhi\"></text>\r\n\t\t\t\t\t\t<text>位置</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!-- 空状态 -->\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t\r\n\t<!-- 底部导航 -->\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\r\n\t<!-- 消息提示 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tnodata: false,\r\n\t\t\tnomore: false,\r\n\t\t\tpage: 1,\r\n\t\t\tlimit: 20,\r\n\t\t\tkeyword: '',\r\n\t\t\tdatalist: [],\r\n\t\t\tstatistics: {\r\n\t\t\t\ttotal_merchants: 0,\r\n\t\t\t\tactive_merchants: 0,\r\n\t\t\t\ttoday_new_merchants: 0,\r\n\t\t\t\ttotal_orders: 0,\r\n\t\t\t\ttotal_amount: '0.00',\r\n\t\t\t\tmonth_amount: '0.00'\r\n\t\t\t},\r\n\t\t\t// 状态筛选\r\n\t\t\tstatusIndex: 0,\r\n\t\t\tstatusList: [\r\n\t\t\t\t{key: '', name: '全部状态'},\r\n\t\t\t\t{key: '1', name: '正常营业'},\r\n\t\t\t\t{key: '0', name: '待审核'},\r\n\t\t\t\t{key: '2', name: '已暂停'},\r\n\t\t\t\t{key: '3', name: '已禁用'}\r\n\t\t\t],\r\n\t\t\t// 类型筛选\r\n\t\t\ttypeIndex: 0,\r\n\t\t\ttypeList: [\r\n\t\t\t\t{key: '', name: '全部类型'}\r\n\t\t\t]\r\n\t\t};\r\n\t},\r\n\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getMerchantTypes();\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.page = 1;\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n\tonReachBottom: function () {\r\n\t\tif (!this.nomore) {\r\n\t\t\tthis.page++;\r\n\t\t\tthis.getdata(true);\r\n\t\t}\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 获取商户类型\r\n\t\tgetMerchantTypes: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.get('ApiCityAgent/getMerchantTypes', {}, function (res) {\r\n\t\t\t\tif (res.status == 1 && res.types) {\r\n\t\t\t\t\t// 添加默认选项\r\n\t\t\t\t\tthat.typeList = [{key: '', name: '全部类型'}];\r\n\t\t\t\t\tres.types.forEach(function(type) {\r\n\t\t\t\t\t\tthat.typeList.push({\r\n\t\t\t\t\t\t\tkey: type.id.toString(),\r\n\t\t\t\t\t\t\tname: type.name\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取数据\r\n\t\tgetdata: function (isLoadMore = false) {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tif (!isLoadMore) {\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar params = {\r\n\t\t\t\tpage: that.page,\r\n\t\t\t\tlimit: that.limit\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 添加筛选条件\r\n\t\t\tif (that.statusIndex > 0) {\r\n\t\t\t\tparams.status = that.statusList[that.statusIndex].key;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (that.typeIndex > 0) {\r\n\t\t\t\tparams.merchant_type = that.typeList[that.typeIndex].key;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (that.keyword) {\r\n\t\t\t\tparams.keyword = that.keyword;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiCityAgent/getMerchantList', params, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置导航标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '商户管理'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 更新统计数据\r\n\t\t\t\tif (res.statistics) {\r\n\t\t\t\t\tthat.statistics = res.statistics;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新列表数据\r\n\t\t\t\tif (isLoadMore) {\r\n\t\t\t\t\tthat.datalist = that.datalist.concat(res.list);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.datalist = res.list;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否还有更多数据\r\n\t\t\t\tif (res.list.length < that.limit) {\r\n\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否为空\r\n\t\t\t\tif (that.datalist.length === 0) {\r\n\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 搜索\r\n\t\tonSearch: function () {\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 状态筛选\r\n\t\tonStatusChange: function (e) {\r\n\t\t\tthis.statusIndex = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 类型筛选\r\n\t\tonTypeChange: function (e) {\r\n\t\t\tthis.typeIndex = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\t// 拨打电话\r\n\t\tcallMerchant: function (e) {\r\n\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\tif (!phone) {\r\n\t\t\t\tapp.error('该商户未设置联系电话');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '拨打电话',\r\n\t\t\t\tcontent: '确认拨打电话：' + phone + '？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\t\t\tapp.error('拨打电话失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查看位置\r\n\t\tviewLocation: function (e) {\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\tvar name = e.currentTarget.dataset.name;\r\n\t\t\t\r\n\t\t\tif (!longitude || !latitude) {\r\n\t\t\t\tapp.error('该商户未设置位置信息');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tlongitude: longitude,\r\n\t\t\t\tlatitude: latitude,\r\n\t\t\t\tname: name,\r\n\t\t\t\tscale: 18,\r\n\t\t\t\tfail: function() {\r\n\t\t\t\t\tapp.error('打开地图失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 页面跳转\r\n\t\tgoto: function (e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tif (url) {\r\n\t\t\t\tapp.goto('/pagesExt/cityagent/' + url);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到商户后台管理\r\n\t\tgotoBusinessManage: function (e) {\r\n\t\t\tvar merchantId = e.currentTarget.dataset.id;\r\n\t\t\tif (merchantId) {\r\n\t\t\t\t// 跳转到商户后台管理页面\r\n\t\t\t\tapp.goto('/pagesExt/business/index?id=' + merchantId);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 数据加载完成\r\n\t\tloaded: function () {\r\n\t\t\tthis.isload = true;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-container {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.stats-card {\r\n\tdisplay: flex;\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stats-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.stats-number {\r\n\tdisplay: block;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.amount-card {\r\n\tdisplay: flex;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tcolor: white;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.amount-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.amount-label {\r\n\tdisplay: block;\r\n\tfont-size: 24rpx;\r\n\topacity: 0.9;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.amount-value {\r\n\tdisplay: block;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 筛选条件样式 */\r\n.filter-container {\r\n\tbackground: white;\r\n\tmargin: 0 20rpx 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-row {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.filter-row:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.search-box {\r\n\tflex: 1;\r\n\tposition: relative;\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 25rpx;\r\n\tpadding: 0 20rpx;\r\n\theight: 70rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.search-input {\r\n\tflex: 1;\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.search-icon {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.filter-item {\r\n\tflex: 1;\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tpadding: 0 20rpx;\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 10rpx;\r\n\tmargin-right: 20rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.filter-item:last-child {\r\n\tmargin-right: 0;\r\n}\r\n\r\n/* 商户列表样式 */\r\n.merchant-list {\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n.merchant-item {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 商户头部信息 */\r\n.merchant-header {\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.merchant-avatar {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tborder-radius: 15rpx;\r\n\toverflow: hidden;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.avatar-img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.merchant-info {\r\n\tflex: 1;\r\n}\r\n\r\n.merchant-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.merchant-category {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.merchant-address {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.merchant-address .iconfont {\r\n\tfont-size: 24rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.address-text {\r\n\tflex: 1;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.merchant-status {\r\n\t\r\n}\r\n\r\n.status-badge {\r\n\tfont-size: 22rpx;\r\n\tcolor: white;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n/* 商户统计信息 */\r\n.merchant-stats {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx 0;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.stat-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n}\r\n\r\n.stat-label {\r\n\tdisplay: block;\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stat-value {\r\n\tdisplay: block;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 操作按钮 */\r\n.merchant-actions {\r\n\tdisplay: flex;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx 0;\r\n\tmargin-right: 20rpx;\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 15rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.action-btn:last-child {\r\n\tmargin-right: 0;\r\n}\r\n\r\n.action-btn .iconfont {\r\n\tfont-size: 32rpx;\r\n\tmargin-bottom: 10rpx;\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n.action-btn:active {\r\n\tbackground: #e8f5e8;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./merchant.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103611\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}