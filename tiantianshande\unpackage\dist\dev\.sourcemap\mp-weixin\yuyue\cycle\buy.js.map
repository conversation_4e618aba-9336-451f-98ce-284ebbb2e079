{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?6184", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?2130", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?a898", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?0c27", "uni-app:///yuyue/cycle/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?4a9d", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/buy.vue?8040"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "couponlist", "data", "opt", "loading", "isload", "menuindex", "submitDisabled", "productInfo", "address", "priceInfo", "product_total_price", "total_price", "coupon_money", "memberInfo", "couponList", "couponSelected", "couponDiscount", "totalPrice", "startDate", "minStartDate", "couponVisible", "selectedPaytype", "onLoad", "onShow", "uni", "methods", "getdata", "app", "that", "product_id", "tomorrow", "calculatePrice", "finalPrice", "bindStartDateChange", "formatDate", "showCouponList", "hideCouponList", "chooseCoupon", "paytypeChange", "submitOrder", "address_id", "linkman", "tel", "longitude", "latitude", "start_date", "remark", "coupon_id", "platform", "paytype"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyI5wB;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eAEA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;;MAEAC;;MAEA;MACA;MACA;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACAC;MACA;IACA;EACA;;EACAC;IACAC;MACA;MACA;MACA;QACAC;UAAAA;QAAA;QACA;MACA;MAEAC;MACAD;QAAAE;MAAA;QACAD;QACA;UACAD;YAAAA;UAAA;UACA;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;QACA;QACAE;QACAF;QACAA;;QAEAA;QACAA;MACA;IACA;IAEA;IACAG;MACA;MACA;MACA;QACA;QACA;QACA;MAAA;;MAGA;MACA;;MAEA;MACA;QACAf;QACAgB;QACA;MACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAA;QACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACAZ;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAE;QACAW;QACAC;QAAA;QACAC;QAAA;QACAlC;QAAA;QACAmC;QACAC;QACAC;QACAC;QACAC;QAAA;QACA;QACAC;QACAC;MACA;;MAEArB;MACAD;MAEAA;QACAA;QACAC;QACA;UACAD;UACA;QACA;QACA;QACAA;;QAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvVA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/cycle/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/cycle/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=2de573dd&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/cycle/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=2de573dd&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var g0 = _vm.isload ? _vm.couponList.length : null\n  var m1 = _vm.isload && g0 > 0 ? _vm.t(\"color1\") : null\n  var g1 =\n    _vm.isload && g0 > 0 && !_vm.couponSelected.id\n      ? _vm.couponList.length\n      : null\n  var m2 = _vm.isload && !(g0 > 0) ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m5 = _vm.isload && _vm.couponVisible ? _vm.t(\"优惠券\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        g1: g1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"submitOrder\">\r\n\t\t\t<!-- 地址选择 -->\r\n\t\t\t<view class=\"address-add flex-y-center\" @tap=\"goto\" data-url=\"/pages/address/address?fromPage=buy&type=1\"> <!-- type=1 for potentially needing tongcheng -->\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/address.png\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\r\n\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}}</view>\r\n\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"f2 flex1\">请选择服务地址</view>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 订单信息 -->\r\n\t\t\t<view class=\"buydata\">\r\n\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t<!-- 商品信息 -->\r\n\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"productInfo.pic\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{productInfo.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">共{{productInfo.total_period}}期</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f3\">￥{{productInfo.sell_price}}<text style=\"font-size: 24rpx;color:#999\">/期</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 价格明细 -->\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<text class=\"f1\">商品总价</text>\r\n\t\t\t\t\t\t<text class=\"f2\">￥{{priceInfo.product_total_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"price\" v-if=\"priceInfo.leveldk_money > 0\"> -->\r\n\t\t\t\t\t\t<!-- <text class=\"f1\">{{t('会员')}}折扣</text> -->\r\n\t\t\t\t\t\t<!-- <text class=\"f2\" style=\"color:#ff5043\">-￥{{priceInfo.leveldk_money}}</text> -->\r\n\t\t\t\t\t<!-- </view> -->\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t<view v-if=\"couponList.length > 0\" class=\"f2\" @tap=\"showCouponList\">\r\n\t\t\t\t\t\t\t<text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{couponSelected.id ? couponSelected.name : couponList.length+'张可用'}}</text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"price\" v-if=\"couponSelected.id\">\r\n\t\t\t\t\t\t<text class=\"f1\">优惠金额</text>\r\n\t\t\t\t\t\t<text class=\"f2\" style=\"color:#ff5043\">-￥{{couponDiscount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 期望开始日期 -->\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<text class=\"f1\">期望开始日期</text>\r\n\t\t\t\t\t\t<picker mode=\"date\" :value=\"startDate\" :start=\"minStartDate\" @change=\"bindStartDateChange\">\r\n\t\t\t\t\t\t\t<view class=\"f2\">{{startDate || '请选择'}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 备注 -->\r\n\t\t\t\t\t<view class=\"remark\">\r\n\t\t\t\t\t\t<text class=\"f1\">备注</text>\r\n\t\t\t\t\t\t<input class=\"f2\" name=\"remark\" placeholder=\"选填, 请输入备注信息\" placeholder-style=\"color:#ccc\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 积分抵扣 (如果需要) -->\r\n\t\t\t<!-- \r\n\t\t\t<view class=\"scoredk flex\" v-if=\"memberInfo.score > 0 && sysset.scoredk > 0\">\r\n\t\t\t\t<checkbox-group @change=\"useScoreChange\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view>{{memberInfo.score}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{scoreDeductMoney}}</text> 元</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t\t<checkbox value=\"1\" :checked=\"useScore\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</checkbox-group>\r\n\t\t\t</view>\r\n\t\t\t-->\r\n\r\n\t\t\t<!-- 在这里添加支付方式选择的 UI 区域 -->\r\n\t\t\t<!-- 例如： -->\r\n\t\t\t<!--\r\n\t\t\t<view class=\"payment-method\">\r\n\t\t\t\t<view class=\"title\">支付方式</view>\r\n\t\t\t\t<radio-group @change=\"paytypeChange\">\r\n\t\t\t\t\t<label class=\"flex-y-center\">\r\n\t\t\t\t\t\t<radio value=\"1\" :checked=\"selectedPaytype == 1\"></radio> 微信支付\r\n\t\t\t\t\t</label>\r\n\t\t\t\t\t<label class=\"flex-y-center\">\r\n\t\t\t\t\t\t<radio value=\"3\" :checked=\"selectedPaytype == 3\"></radio> 余额支付\r\n\t\t\t\t\t</label>\r\n\t\t\t\t\t... 其他支付方式 ...\r\n\t\t\t\t</radio-group>\r\n\t\t\t</view>\r\n\t\t\t-->\r\n\r\n\t\t\t<view style=\"width: 100%; height: 120rpx;\"></view>\r\n\r\n\t\t\t<!-- 底部栏 -->\r\n\t\t\t<view class=\"footer flex\" :class=\"menuindex > -1 ? 'tabbarbot' : 'notabbarbot'\">\r\n\t\t\t\t<view class=\"text1 flex1\">合计：\r\n\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx;color:#FF5043\">￥{{totalPrice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" :disabled=\"submitDisabled\">提交订单</button>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\r\n\t\t<!-- 优惠券弹出层 -->\r\n\t\t<view v-if=\"couponVisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideCouponList\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" @tap.stop=\"hideCouponList\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponSelected.id\" @chooseCoupon=\"chooseCoupon\" :bid=\"productInfo.bid\"></couponlist> \r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nimport couponlist from '@/components/couponlist/couponlist.vue'; // 引入优惠券组件\r\n\r\nexport default {\r\n\tcomponents: { couponlist },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: true,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex: -1,\r\n\t\t\tsubmitDisabled: false,\r\n\r\n\t\t\tproductInfo: {}, // 商品信息\r\n\t\t\taddress: {},     // 地址信息\r\n\t\t\tpriceInfo: { product_total_price: '0.00', total_price: '0.00', coupon_money: '0.00' }, // 价格信息\r\n\t\t\tmemberInfo: {},  // 会员信息\r\n\t\t\tcouponList: [],  // 优惠券列表\r\n\t\t\tcouponSelected: {}, // 已选优惠券\r\n\t\t\tcouponDiscount: '0.00', // 优惠券抵扣金额\r\n\t\t\ttotalPrice: '0.00', // 最终总价\r\n\r\n\t\t\tstartDate: '',     // 期望开始日期\r\n\t\t\tminStartDate: '',  // 最小可选开始日期 (明天)\r\n\t\t\t\r\n\t\t\tcouponVisible: false, // 优惠券弹窗\r\n\r\n\t\t\tselectedPaytype: 1 // 默认支付方式为1 (例如微信支付)，需要根据实际情况调整\r\n\t\t\t\r\n\t\t\t// 积分相关 (如果需要)\r\n\t\t\t// useScore: false,\r\n\t\t\t// scoreDeductMoney: '0.00'\r\n\t\t};\r\n\t},\r\n\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShow: function() {\r\n\t\t// 如果从地址页面返回，尝试获取选择的地址\r\n\t\tvar chosenAddress = uni.getStorageSync('choosedAddress');\r\n\t\tif (chosenAddress) {\r\n\t\t\tthis.address = chosenAddress;\r\n\t\t\tuni.removeStorageSync('choosedAddress');\r\n\t\t\tthis.calculatePrice(); // 地址变化可能影响运费（如果未来支持）\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar productId = that.opt.product_id;\r\n\t\t\tif (!productId) {\r\n\t\t\t\tapp.alert('缺少服务ID', function() { app.goback(); });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiPeriodicService/buy', { product_id: productId }, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg, function() { app.goback(); });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.productInfo = res.data.product;\r\n\t\t\t\tthat.address = res.data.address || {}; // 使用接口返回的默认地址\r\n\t\t\t\tthat.priceInfo = res.data.price_info;\r\n\t\t\t\tthat.memberInfo = res.data.member_info;\r\n\t\t\t\tthat.couponList = res.data.coupon_list || [];\r\n\t\t\t\t\r\n\t\t\t\t// 设置最小可选开始日期为明天\r\n\t\t\t\tvar today = new Date();\r\n\t\t\t\tvar tomorrow = new Date(today);\r\n\t\t\t\ttomorrow.setDate(tomorrow.getDate() + 1);\r\n\t\t\t\tthat.minStartDate = that.formatDate(tomorrow);\r\n\t\t\t\tthat.startDate = that.minStartDate; // 默认选中明天\r\n\t\t\t\t\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 计算总价\r\n\t\tcalculatePrice: function() {\r\n\t\t\tvar basePrice = parseFloat(this.priceInfo.product_total_price) || 0;\r\n\t\t\tvar couponDiscount = 0;\r\n\t\t\tif (this.couponSelected.id) {\r\n\t\t\t\t// 根据优惠券类型计算抵扣金额 (这里简化处理，直接使用后台计算好的total_price)\r\n\t\t\t\t// 实际可能需要前端根据券类型和金额计算\r\n\t\t\t\t// couponDiscount = parseFloat(this.couponSelected.money) || 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 暂时从 priceInfo 直接获取总价，如果选择优惠券再调整\r\n\t\t\tvar finalPrice = parseFloat(this.priceInfo.total_price) || basePrice;\r\n\t\t\t\r\n\t\t\t// 如果选择了优惠券，重新计算总价\r\n\t\t\tif (this.couponSelected.id) {\r\n\t\t\t\tcouponDiscount = parseFloat(this.couponSelected.money) || 0;\r\n\t\t\t\tfinalPrice = basePrice - couponDiscount;\r\n\t\t\t\tthis.couponDiscount = couponDiscount.toFixed(2);\r\n\t\t\t} else {\r\n\t\t\t\tthis.couponDiscount = '0.00';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 积分抵扣逻辑 (如果需要)\r\n\t\t\t// if (this.useScore) {\r\n\t\t\t// \t finalPrice -= parseFloat(this.scoreDeductMoney);\r\n\t\t\t// }\r\n\r\n\t\t\tif (finalPrice < 0) finalPrice = 0;\r\n\t\t\tthis.totalPrice = finalPrice.toFixed(2);\r\n\t\t},\r\n\r\n\t\t// 选择开始日期\r\n\t\tbindStartDateChange: function(e) {\r\n\t\t\tthis.startDate = e.detail.value;\r\n\t\t},\r\n\r\n\t\t// 格式化日期 YYYY-MM-DD\r\n\t\tformatDate: function(date) {\r\n\t\t\tconst year = date.getFullYear();\r\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t},\r\n\r\n\t\t// 显示优惠券列表\r\n\t\tshowCouponList: function() {\r\n\t\t\tif (this.couponList.length > 0) {\r\n\t\t\t\tthis.couponVisible = true;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 隐藏优惠券列表\r\n\t\thideCouponList: function() {\r\n\t\t\tthis.couponVisible = false;\r\n\t\t},\r\n\t\t// 选择优惠券\r\n\t\tchooseCoupon: function(e) {\r\n\t\t\tif (this.couponSelected.id === e.rid) { // 取消选择\r\n\t\t\t\tthis.couponSelected = {};\r\n\t\t\t} else {\r\n\t\t\t\tthis.couponSelected = this.couponList[e.key];\r\n\t\t\t}\r\n\t\t\tthis.hideCouponList();\r\n\t\t\tthis.calculatePrice();\r\n\t\t},\r\n\t\t\r\n\t\t// 积分抵扣开关 (如果需要)\r\n\t\t// useScoreChange: function(e) {\r\n\t\t// \t this.useScore = e.detail.value.length > 0;\r\n\t\t// \t this.calculatePrice();\r\n\t\t// },\r\n\r\n\t\t// 添加支付方式切换方法\r\n\t\tpaytypeChange: function(e) {\r\n\t\t\tthis.selectedPaytype = parseInt(e.detail.value);\r\n\t\t},\r\n\r\n\t\t// 提交订单\r\n\t\tsubmitOrder: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar formData = e.detail.value;\r\n\r\n\t\t\tif (!that.address || !that.address.id) {\r\n\t\t\t\tapp.error('请选择服务地址');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (!that.startDate) {\r\n\t\t\t\tapp.error('请选择期望开始日期');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar postData = {\r\n\t\t\t\tproduct_id: that.productInfo.id,\r\n\t\t\t\taddress_id: that.address.id,\r\n\t\t\t\tlinkman: that.address.name, // 从地址对象获取\r\n\t\t\t\ttel: that.address.tel,     // 从地址对象获取\r\n\t\t\t\taddress: that.address.province + that.address.city + that.address.area + that.address.address, // 拼接完整地址\r\n\t\t\t\tlongitude: that.address.longitude || '',\r\n\t\t\t\tlatitude: that.address.latitude || '',\r\n\t\t\t\tstart_date: that.startDate,\r\n\t\t\t\tremark: formData.remark || '',\r\n\t\t\t\tcoupon_id: that.couponSelected.id || 0, // 传递优惠券ID\r\n\t\t\t\t// usescore: that.useScore ? 1 : 0, // 如果使用积分抵扣\r\n\t\t\t\tplatform: app.globalData.platform, \r\n\t\t\t\tpaytype: that.selectedPaytype // 添加选择的支付方式\r\n\t\t\t};\r\n\r\n\t\t\tthat.submitDisabled = true;\r\n\t\t\tapp.showLoading('提交中...');\r\n\r\n\t\t\tapp.post('ApiPeriodicService/createOrder', postData, function(res) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tthat.submitDisabled = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 下单成功，跳转支付页面\r\n\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\r\n\t\t\t\t// If payment needs params (e.g., WeChat JSAPI), access res.payparams\r\n\t\t\t\t// Example: let payParams = res.payparams;\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n/* Reuse styles from tiantianshande/pagesExt/cycle/buy.vue and adapt */\r\n.container { background-color: #f8f8f8; padding-bottom: 120rpx; /* Footer height */ }\r\n\r\n.address-add { width: 94%; margin: 20rpx 3%; background: #fff; border-radius: 10rpx; padding: 20rpx 3%; min-height: 100rpx; display: flex; align-items: center; }\r\n.address-add .f1 { margin-right: 20rpx; }\r\n.address-add .f1 .img { width: 40rpx; height: 40rpx; }\r\n.address-add .f2 { color: #666; font-size: 28rpx; }\r\n.address-add .f2[v-if=\"address.id\"] view:first-child { font-weight: bold; color: #111; font-size: 30rpx; margin-bottom: 4rpx; }\r\n.address-add .f2[v-if=\"address.id\"] view:last-child { font-size: 24rpx; color: #666; }\r\n.address-add .f3 { width: 26rpx; height: 26rpx; margin-left: 10rpx; }\r\n\r\n.buydata { width: 94%; margin: 20rpx 3%; background: #fff; border-radius: 10rpx; overflow: hidden; }\r\n.bcontent { width: 100%; padding: 0 20rpx; }\r\n\r\n.product { width: 100%; border-bottom: 1px solid #f4f4f4; padding: 20rpx 0; }\r\n.product .item { width: 100%; display: flex; background: #fff; }\r\n.product .info { padding-left: 20rpx; }\r\n.product .info .f1 { color: #222222; font-weight: bold; font-size: 28rpx; line-height: 1.4; margin-bottom: 10rpx; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; }\r\n.product .info .f2 { color: #999999; font-size: 24rpx; margin-bottom: 10rpx; }\r\n.product .info .f3 { color: #FF4C4C; font-size: 28rpx; font-weight: bold; }\r\n.product .img { width: 140rpx; height: 140rpx; border-radius: 8rpx; flex-shrink: 0; }\r\n\r\n.price { width: 100%; padding: 20rpx 0; background: #fff; display: flex; align-items: center; border-bottom: 1px solid #f4f4f4; }\r\n.price:last-child { border-bottom: none; }\r\n.price .f1 { color: #333; font-size: 28rpx; }\r\n.price .f2 { color: #111; font-weight: bold; text-align: right; flex: 1; font-size: 28rpx; display: flex; align-items: center; justify-content: flex-end; }\r\n.price .f2 .iconfont { margin-left: 10rpx; font-size: 24rpx; }\r\n.price picker .f2 { color: #111; font-weight: bold; }\r\n\r\n.remark { width: 100%; padding: 20rpx 0; background: #fff; display: flex; align-items: center; }\r\n.remark .f1 { color: #333; width: 200rpx; font-size: 28rpx; }\r\n.remark .f2 { border: none; height: 70rpx; padding-left: 10rpx; text-align: right; flex: 1; font-size: 28rpx; }\r\n\r\n.scoredk { width: 94%; margin: 20rpx 3%; border-radius: 10rpx; padding: 24rpx 20rpx; background: #fff; display: flex; align-items: center; }\r\n.scoredk .f1 { color: #333333; font-size: 28rpx; }\r\n.scoredk .f2 { color: #999999; text-align: right; flex: 1; font-size: 28rpx; }\r\n\r\n.footer { width: 100%; background: #fff; position: fixed; left: 0px; bottom: 0px; padding: 10rpx 2%; display: flex; align-items: center; z-index: 8; border-top: 1px solid #eee; }\r\n.footer .text1 { height: 100rpx; line-height: 100rpx; color: #2a2a2a; font-size: 28rpx; }\r\n.footer .op { width: 240rpx; height: 80rpx; line-height: 80rpx; color: #fff; text-align: center; font-size: 30rpx; border-radius: 40rpx; }\r\n.footer .op[disabled] { background-color: #ccc !important; background-image: none !important; }\r\n\r\n/* Popups */\r\n.popup__container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 100; }\r\n.popup__overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); }\r\n.popup__modal { position: absolute; left: 0; right: 0; bottom: 0; background-color: #fff; border-top-left-radius: 20rpx; border-top-right-radius: 20rpx; padding-bottom: env(safe-area-inset-bottom); max-height: 70%; overflow-y: auto; }\r\n.popup__title { display: flex; align-items: center; justify-content: space-between; padding: 30rpx; border-bottom: 1px solid #eee; }\r\n.popup__title-text { font-size: 32rpx; font-weight: bold; color: #333; }\r\n.popup__close { width: 36rpx; height: 36rpx; padding: 10rpx; }\r\n.popup__content { padding: 0; max-height: 60vh; /* Limit coupon list height */ }\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041129\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}