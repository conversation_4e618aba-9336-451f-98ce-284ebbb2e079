{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?250a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?ae68", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?f3eb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?803e", "uni-app:///pagesExb/toupiao/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?3100", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/toupiao/index.vue?2830"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "aid", "session_id", "<PERSON><PERSON>a", "randt", "nowjoinid", "smscode", "smsdjs", "smstel", "<PERSON><PERSON>", "datalist", "pagenum", "nomore", "nodata", "info", "nowtime", "djsday", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "dj<PERSON><PERSON>", "keyword", "onLoad", "onPullDownRefresh", "onReachBottom", "onUnload", "clearInterval", "onShareAppMessage", "title", "desc", "link", "pic", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "id", "uni", "interval", "getdjs", "searchConfirm", "reget<PERSON><PERSON>a", "<PERSON><PERSON><PERSON><PERSON>", "setsmscode", "setsmstel", "sendsmscode", "tel", "time", "closeCaptcha", "closeSmscode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyF9wB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;MAAAC;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;MAAAJ;MAAAC;MAAAC;MAAAC;IAAA;IACA;IACA;MACAH;MACAK;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAA3B;QAAAS;MAAA;QACAgB;QAEA;UACAA;UACA;YACAA;UACA;UACAA;UACAA;UACAG;YACAZ;UACA;UACA;UACA;UACA;UACA;UACAS;YAAAT;YAAAC;YAAAC;YAAAC;UAAA;UAEAL;UACAe;YACAJ;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;QACA;MACA;QACA;MACA;MACA;QACAL;QACAA;QACAA;QACAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAM;MACA;MACA;MACAN;MACAA;IACA;IACAO;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAX;MACA;MACA;QACAC;QACAD;QACA;MACA;MACA;QACAC;QACAD;QACA;MACA;MACAC;QAAAW;MAAA;QACA;UACAX;UAAA;QACA;MACA;MACA;MACA;QACAY;QACA;UACAb;UACAA;UACAX;QACA;UACAW;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;MACA;MACAf;MACAA;QAAAC;QAAAnC;QAAAK;QAAAF;MAAA;QACA+B;QACA;UACAA;UACA;UACA;YACA;cACA3B;cAAA;YACA;UACA;UACA0B;UACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3UA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/toupiao/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/toupiao/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2629c5a4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/toupiao/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2629c5a4&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.info.help_check == 2 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" :style=\"{background:info.color1}\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"banner\"><image :src=\"info.banner\" mode=\"widthFix\"></image></view>\r\n\t\t<view class=\"box1\">\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.joinnum}}</view><view class=\"t2\">参与人数</view></view>\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.helpnum}}</view><view class=\"t2\">累计投票</view></view>\r\n\t\t\t<view class=\"item\"><view class=\"t1\" :style=\"{color:info.color2}\">{{info.readcount}}</view><view class=\"t2\">访问次数</view></view>\r\n\t\t</view>\r\n\t\t<view class=\"box2\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<image src=\"/static/img/clock.png\" style=\"width:32rpx;height:32rpx;margin-right:6rpx\"/>\r\n\t\t\t\t<text>{{ info.starttime > nowtime ? '距活动开始还有' : '距活动结束还剩'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f2\" :style=\"{color:info.color2}\">\r\n\t\t\t\t<text class=\"t1\">{{djsday}}</text><text class=\"t2\">天</text><text class=\"t1\">{{djshour}}</text><text class=\"t2\">小时</text><text class=\"t1\">{{djsmin}}</text><text class=\"t2\">分钟</text><text class=\"t1\">{{djssec}}</text><text class=\"t2\">秒</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t<view class=\"topsearch-f1 flex-y-center\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索编号或选手名称\" placeholder-style=\"font-size:24rpx;color:#778899\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"box3\">\r\n\t\t\t<block v-for=\"(item,index) in datalist\" :key=\"index\">\r\n\r\n\t\t\t<view v-if=\"info.listtype==0\" class=\"item\" @tap=\"goto\" :data-url=\"'detail?id='+item.id\">\r\n\t\t\t\t<view class=\"pic\"><image :src=\"item.pic\" class=\"img\" mode=\"widthFix\"/></view>\r\n\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"no\">NO: {{item.number}}</view>\r\n\t\t\t\t<view class=\"helpnum\">{{item.helpnum}}票</view>\r\n\t\t\t\t<view class=\"tou\" :style=\"{background:'linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)'}\" @tap.stop=\"toupiao\" :data-id=\"item.id\" data-type=\"0\">{{info.helptext}}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"info.listtype==1\" class=\"itemlist\" @tap=\"goto\" :data-url=\"'detail?id='+item.id\">\r\n\t\t\t\t<view class=\"pic\"><image :src=\"item.pic\" class=\"img\" mode=\"widthFix\"/></view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"no\">NO: {{item.number}}</view>\r\n\t\t\t\t\t<view class=\"helpnum\">{{item.helpnum}}票</view>\r\n\t\t\t\t\t<view class=\"tou\" :style=\"{background:'linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)'}\" @tap.stop=\"toupiao\" :data-id=\"item.id\" data-type=\"0\">{{info.helptext}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<view style=\"background:#fff;width:94%;margin:0 3%;border-radius:12rpx\" v-if=\"nodata\"><nodata></nodata></view>\r\n\t\t<view style=\"width:100%;height:60rpx\"></view>\r\n\r\n\t\t\r\n\t\t<uni-popup id=\"dialogCaptcha\" ref=\"dialogCaptcha\" type=\"dialog\" v-if=\"info.help_check == 1\">\r\n\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t<view class=\"hxqrbox1\">\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入验证码\" @input=\"setcaptcha\"/>\r\n\t\t\t\t\t<image @tap=\"regetcaptcha\" :src=\"pre_url+'/?s=/ApiIndex/captcha&aid='+aid+'&session_id='+session_id+'&t='+randt\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tou2\" :style=\"{background:'linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)'}\" @tap=\"toupiao\" :data-id=\"nowjoinid\" data-type=\"1\">确 定</view>\r\n\t\t\t\t<view class=\"close\" @tap=\"closeCaptcha\">\r\n\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t<uni-popup id=\"dialogSmscode\" ref=\"dialogSmscode\" type=\"dialog\" v-if=\"info.help_check == 2\">\r\n\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t<view class=\"hxqrbox1\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-tel.png\" style=\"width:44rpx;height:44rpx;margin-right:30rpx\"/>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入手机号\" @input=\"setsmstel\"/>\r\n\t\t\t\t\t<view class=\"code\" :style=\"{color:t('color1')}\" @tap=\"sendsmscode\" style=\"font-size:30rpx;width:160rpx;text-align:center\">{{smsdjs||'获取验证码'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"hxqrbox1\" style=\"margin-top:40rpx\">\r\n\t\t\t\t\t<image src=\"/static/img/reg-code.png\" style=\"width:44rpx;height:44rpx;margin-right:30rpx\"/>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"请输入短信验证码\" @input=\"setsmscode\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tou2\" :style=\"{background:'linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)'}\" @tap=\"toupiao\" :data-id=\"nowjoinid\" data-type=\"1\">确 定</view>\r\n\t\t\t\t<view class=\"close\" @tap=\"closeSmscode\">\r\n\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n  data() {\r\n    return {  \r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\taid:app.globalData.aid,\r\n\t\t\tsession_id:app.globalData.session_id,\r\n\t\t\tcaptcha:'',\r\n\t\t\trandt:'',\r\n\t\t\tnowjoinid:0,\r\n\t\t\tsmscode:'',\r\n      smsdjs: '',\r\n\t\t\tsmstel:'',\r\n      hqing: 0,\r\n\t\t\t\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n      nodata: false,\r\n\t\t\t\r\n      info: {},\r\n\t\t\tnowtime:0,\r\n      djsday: '00',\r\n      djshour: '00',\r\n      djsmin: '00',\r\n      djssec: '00',\r\n\t\t\tkeyword:'',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t}, \r\n  onReachBottom: function () {\r\n\t\treturn;\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  onUnload: function (loadmore) {\r\n    clearInterval(interval);\r\n  },\r\n  onShareAppMessage: function () {\r\n\t\tvar that = this;\r\n\t\tvar title = that.info.sharetitle ? that.info.sharetitle : that.info.name;\r\n\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : that.info.banner;\r\n\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\treturn this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic});\r\n  },\r\n\tonShareTimeline:function(){\r\n\t\tvar that = this;\r\n\t\tvar title = that.info.sharetitle ? that.info.sharetitle : that.info.name;\r\n\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : that.info.banner;\r\n\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\tvar sharewxdata = this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      app.post('ApiToupiao/index', {id:that.opt.id,pagenum:pagenum,keyword:that.keyword}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = res.datalist;\r\n          if ((that.datalist).length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: that.info.name\r\n\t\t\t\t\t});\r\n\t\t\t\t\tvar title = that.info.sharetitle ? that.info.sharetitle : that.info.name;\r\n\t\t\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : that.info.banner;\r\n\t\t\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\t\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\t\t\t\tthat.loaded({title:title,desc:sharedesc,link:sharelink,pic:sharepic});\r\n\r\n\t\t\t\t\tclearInterval(interval);\r\n\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t}, 1000);\r\n        }else{\r\n          if ((res.datalist).length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(res.datalist);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    getdjs: function () {\r\n      var that = this;\r\n      if (that.info.starttime * 1 > that.nowtime * 1) {\r\n        var totalsec = that.info.starttime * 1 - that.nowtime * 1;\r\n      } else {\r\n        var totalsec = that.info.endtime * 1 - that.nowtime * 1;\r\n      }\r\n      if (totalsec <= 0) {\r\n        that.djsday = '00';\r\n        that.djshour = '00';\r\n        that.djsmin = '00';\r\n        that.djssec = '00';\r\n      } else {\r\n        var date = Math.floor(totalsec / 86400);\r\n        var houer = Math.floor((totalsec - date * 86400) / 3600);\r\n        var min = Math.floor((totalsec - date * 86400 - houer * 3600) / 60);\r\n        var sec = totalsec - date * 86400 - houer * 3600 - min * 60;\r\n        var djsday = (date < 10 ? '0' : '') + date;\r\n        var djshour = (houer < 10 ? '0' : '') + houer;\r\n        var djsmin = (min < 10 ? '0' : '') + min;\r\n        var djssec = (sec < 10 ? '0' : '') + sec;\r\n        that.djsday = djsday;\r\n        that.djshour = djshour;\r\n        that.djsmin = djsmin;\r\n        that.djssec = djssec;\r\n      }\r\n    },\r\n\t\tsearchConfirm:function(e){\r\n\t\t\tvar that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword;\r\n      that.getdata();\r\n\t\t},\r\n\t\tregetcaptcha:function(){\r\n\t\t\tthis.randt = this.randt+'1';\r\n\t\t},\r\n\t\tsetcaptcha:function(e){\r\n\t\t\tthis.captcha = e.detail.value;\r\n\t\t},\r\n\t\tsetsmscode:function(e){\r\n\t\t\tthis.smscode = e.detail.value;\r\n\t\t},\r\n\t\tsetsmstel:function(e){\r\n\t\t\tthis.smstel = e.detail.value;\r\n\t\t},\r\n    sendsmscode: function () {\r\n      var that = this;\r\n      if (that.hqing == 1) return;\r\n      that.hqing = 1;\r\n      var smstel = that.smstel;\r\n      if (smstel == '') {\r\n        app.alert('请输入手机号码');\r\n        that.hqing = 0;\r\n        return false;\r\n      }\r\n      if (!/^1[3456789]\\d{9}$/.test(smstel)) {\r\n        app.alert(\"手机号码有误，请重填\");\r\n        that.hqing = 0;\r\n        return false;\r\n      }\r\n      app.post(\"ApiIndex/sendsms\", {tel: smstel}, function (data) {\r\n        if (data.status != 1) {\r\n          app.alert(data.msg);return;\r\n        }\r\n      });\r\n      var time = 120;\r\n      var interval1 = setInterval(function () {\r\n        time--;\r\n        if (time < 0) {\r\n          that.smsdjs = '重新获取';\r\n          that.hqing = 0;\r\n          clearInterval(interval1);\r\n        } else if (time >= 0) {\r\n          that.smsdjs = time + '秒';\r\n        }\r\n      }, 1000);\r\n    },\r\n\t\tcloseCaptcha:function(){\r\n\t\t\tthis.$refs.dialogCaptcha.close();\r\n\t\t},\r\n\t\tcloseSmscode:function(){\r\n\t\t\tthis.$refs.dialogSmscode.close();\r\n\t\t},\r\n\t\ttoupiao:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar type = e.currentTarget.dataset.type;\r\n\t\t\tif(type == 0 && that.info.help_check == 1){\r\n\t\t\t\tthis.nowjoinid = id;\r\n\t\t\t\tthis.$refs.dialogCaptcha.open();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(type == 0 && that.info.help_check == 2){\r\n\t\t\t\tthis.nowjoinid = id;\r\n\t\t\t\tthis.$refs.dialogSmscode.open();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tapp.showLoading('投票中');\r\n\t\t\tapp.post('ApiToupiao/toupiao',{id:id,captcha:that.captcha,smstel:that.smstel,smscode:that.smscode},function(res){\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\tfor(var i in datalist){\r\n\t\t\t\t\t\tif(datalist[i].id == id){\r\n\t\t\t\t\t\t\tdatalist[i].helpnum++;break;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.datalist = datalist;\r\n\t\t\t\t\tthat.info.helpnum++\r\n\t\t\t\t\tif(type == 1 && that.info.help_check == 1){\r\n\t\t\t\t\t\tthat.$refs.dialogCaptcha.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(type == 1 && that.info.help_check == 2){\r\n\t\t\t\t\t\tthat.$refs.dialogSmscode.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n\r\n.banner{width:100%;}\r\n.banner image{width:100%;height:auto}\r\n\r\n.box1{width:94%;margin-left:3%;border-radius:12rpx;background:#fff;padding:60rpx 10rpx;display:flex;align-items:center;position:relative;z-index:12;margin-top:-160rpx}\r\n.box1 .item{flex:1;display:flex;flex-direction:column;align-items:center;}\r\n.box1 .item .t1{font-size:48rpx;font-weight:bold}\r\n.box1 .item .t2{font-size:24rpx;color:#778899;margin-top:10rpx}\r\n.box2{width:94%;margin-left:3%;border-radius:12rpx;background:#fff;padding:20rpx 10rpx;display:flex;flex-direction:column;align-items:center;margin-top:20rpx}\r\n.box2 .f1{display:flex;align-items:center;color:#222222}\r\n.box2 .f2{display:flex;align-items:flex-end;color:#222222;margin-top:20rpx;font-size:24rpx}\r\n.box2 .f2 .t1{font-size:48rpx;font-weight:bold;padding:0 12rpx}\r\n.box2 .f2 .t2{color:#222222;height:48rpx;line-height:48rpx}\r\n\r\n.topsearch{width:94%;margin:40rpx 3% 10rpx 3%;}\r\n.topsearch .topsearch-f1{height:80rpx;border-radius:10rpx;border:0;background-color:#f5f5f6;flex:1}\r\n.topsearch .topsearch-f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .topsearch-f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.box3{width:96%;margin-left:2%;display:flex;flex-wrap:wrap;margin-top:20rpx;}\r\n.box3 .item{width:48%;margin:8rpx 1%;background:#fff;border-radius:12rpx;overflow:hidden;position:relative;padding-bottom:24rpx}\r\n.box3 .item .pic {width: 100%;height:0;overflow:hidden;background: #f7f7f8;padding-bottom: 100%;position: relative;margin-bottom:10rpx}\r\n.box3 .item .pic .img{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.box3 .item .name{padding:10rpx 10rpx;color:#222222;font-size:28rpx;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.box3 .item .no{color:#778899;font-size:24rpx;padding:4rpx 10rpx;}\r\n.box3 .item .helpnum{color:#778899;font-size:24rpx;padding:4rpx 10rpx;}\r\n.box3 .item .tou{position:absolute;bottom:30rpx;right:20rpx;border-radius:24rpx;font-size:22rpx;color:#fff;height:48rpx;line-height:48rpx;padding:0 20rpx}\r\n\r\n.box3 .itemlist{width:98%;margin:8rpx 1%;background:#fff;border-radius:12rpx;overflow:hidden;position:relative;padding:24rpx;display:flex}\r\n.box3 .itemlist .pic {width:180rpx;height:180rpx;overflow:hidden;background: #f7f7f8;position: relative;}\r\n.box3 .itemlist .pic .img{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.box3 .itemlist .right{padding-left:20rpx;flex:1}\r\n.box3 .itemlist .name{margin:10rpx 10rpx;color:#222222;font-size:28rpx;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.box3 .itemlist .no{color:#778899;font-size:24rpx;padding:4rpx 10rpx;}\r\n.box3 .itemlist .helpnum{color:#778899;font-size:24rpx;padding:4rpx 10rpx;}\r\n.box3 .itemlist .tou{position:absolute;bottom:30rpx;right:20rpx;border-radius:24rpx;font-size:22rpx;color:#fff;height:48rpx;line-height:48rpx;padding:0 20rpx}\r\n\r\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.hxqrbox .hxqrbox1{display:flex;align-items:center}\r\n.hxqrbox .hxqrbox1 input{width:270rpx;}\r\n.hxqrbox .hxqrbox1 image{width:210rpx;height:70rpx}\r\n.hxqrbox  .tou2{border-radius:24rpx;font-size:32rpx;color:#fff;height:80rpx;line-height:80rpx;padding:0 20rpx;text-align:center;margin-top:60rpx}\r\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024339\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}