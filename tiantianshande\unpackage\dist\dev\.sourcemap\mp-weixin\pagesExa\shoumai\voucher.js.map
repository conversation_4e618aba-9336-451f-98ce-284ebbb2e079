{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?0342", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?04c2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?e58e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?580d", "uni-app:///pagesExa/shoumai/voucher.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?647a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/shoumai/voucher.vue?b9a2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "ratio", "selectedItem", "id", "pre_url", "content_pic", "address_text", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "postVoucher", "uploadimg", "pics", "copyAddressText", "uni", "success", "title"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgEhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA,iBACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAT;MAAA;QACAQ;QACA;UACAC;QACA;QACAD;QACAA;MACA;IACA;IACAE;MACA;MACA;MACA;QACAD;QACA;MACA;MAEAA;MACAA;QAAAT;QAAAE;MAAA;QACAO;QACA;UACAA;QACA;UACAA;UACA;UACAA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACAF;QACA;UACAG;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAWAC;QACAlB;QACAmB;UACAD;YACAE;UACA;QACA;MACA;IAEA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC9JA;AAAA;AAAA;AAAA;AAAomC,CAAgB,glCAAG,EAAC,C;;;;;;;;;;;ACAxnC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/shoumai/voucher.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/shoumai/voucher.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./voucher.vue?vue&type=template&id=145fb142&scoped=true&\"\nvar renderjs\nimport script from \"./voucher.vue?vue&type=script&lang=js&\"\nexport * from \"./voucher.vue?vue&type=script&lang=js&\"\nimport style0 from \"./voucher.vue?vue&type=style&index=0&id=145fb142&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"145fb142\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/shoumai/voucher.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./voucher.vue?vue&type=template&id=145fb142&scoped=true&\"", "var components\ntry {\n  components = {\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = parseFloat(_vm.selectedItem.ratio * _vm.selectedItem.commission)\n  var g0 = _vm.content_pic.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./voucher.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./voucher.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"container\">\r\n        <view class=\"content\">\r\n            <view class=\"info-item\">\r\n                <view class=\"t1\">售賣人：</view>\r\n                <view class=\"t2\">{{ selectedItem.nickname }}</view>\r\n            </view>\r\n            <view class=\"info-item\" @tap=\"copyAddressText\">\r\n                <view class=\"t1\">通道：</view>\r\n                <view class=\"t2\">{{ selectedItem.corridor }}</view>\r\n            </view>\r\n            <view class=\"info-item\" @tap=\"copyAddressText\">\r\n                <view class=\"t1\">收款地址：</view>\r\n                <view class=\"t2\">{{ selectedItem.wallet_address }}</view>\r\n            </view>\r\n            <view class=\"info-item\" style=\"height:136rpx;line-height:136rpx\" :data-url=\"selectedItem.wallet_receiving_code\" @tap=\"previewImage\" >\r\n                <view class=\"t1\" style=\"flex:1;\">收款二維碼：</view>\r\n                <image :src=\"selectedItem.wallet_receiving_code\" style=\"width:88rpx;height:88rpx;\"></image>\r\n                <!-- <image class=\"t3\" src=\"/static/img/search_ico.png\"/> -->\r\n            </view>\r\n        </view>\r\n        <view class=\"content\">\r\n            <view class=\"info-item\">\r\n                <view class=\"t1\">創建時間：</view>\r\n                <view class=\"t2\">{{ selectedItem.createtime }}</view>\r\n            </view>\r\n            <view class=\"info-item\">\r\n                <view class=\"t1\">買入數量：</view>\r\n                <view class=\"t2\">{{ selectedItem.commission }}</view>\r\n            </view>\r\n        </view>\r\n        <view class=\"content\">\r\n          <view class=\"info-item\">\r\n            <h3 style=\"margin-top: 8rpx;\">應付：{{ parseFloat(selectedItem.ratio * selectedItem.commission) }} 元</h3>\r\n          </view>\r\n            <view class=\"info-item\" style=\"height: auto;\">\r\n                <view class=\"form-item4 flex-col\" style=\"margin-top: 10px;\">\r\n                    <view class=\"label\">\r\n                        <h4>上傳支付憑證</h4>\r\n                    </view>\r\n                    <view id=\"content_picpreview\" class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n                        <view v-for=\"(item, index) in content_pic\" :key=\"index\" class=\"layui-imgbox\">\r\n                            <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"content_pic\">\r\n                                <image src=\"/static/img/ico-del.png\"></image>\r\n                            </view>\r\n                            <view class=\"layui-imgbox-img\">\r\n                                <image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"content_pic\" v-if=\"content_pic.length<2\"></view>\r\n                    </view>\r\n                    <text class=\"info\" style=\"color:#9e9e9e\">請務必確認上傳支付憑證的有效性與真實性，支付金額必須與訂單應付金額一致。</text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        <view style=\"width:94%;margin: 2rpx 3%;\">\r\n          <button class=\"buy-button\" @click=\"postVoucher(selectedItem)\">提交</button>\r\n        </view>\r\n\r\n        <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n    data() {\r\n        return {\r\n            opt: {},\r\n            ratio: 1,\r\n            // \r\n            selectedItem: [],\r\n            id: '',\r\n            pre_url:app.globalData.pre_url,\r\n            content_pic: [],\r\n\t\t\t//\r\n\t\t\taddress_text: '',\r\n        }\r\n    },\r\n    onLoad: function(opt) {\r\n        this.opt = app.getopts(opt);\r\n        if (this.opt.id)\r\n            this.id = opt.id\r\n        this.getdata();\r\n    },\r\n    onPullDownRefresh: function() {\r\n        this.getdata();\r\n    },\r\n    methods: {\r\n        getdata: function(loadmore) {\r\n            var that = this;\r\n            that.loading = true;\r\n            app.get('ApiShoumai/postVoucher', { id: that.id }, function(res) {\r\n                that.loading = false;\r\n                if(res.status == 0){\r\n                  app.goback()\r\n                }\r\n                that.selectedItem = res.data\r\n\t\t\t\tthat.address_text = res.data.wallet_address\r\n            });\r\n        },\r\n        postVoucher(item){\r\n          var that = this;\r\n          var content_pic = that.content_pic;\r\n          if(content_pic.length <= 0){\r\n            app.alert('請上傳支付憑證');\r\n            return;\r\n          }\r\n\r\n          app.showLoading('处理中');\r\n          app.post('ApiShoumai/postVoucher', {id: item.id, content_pic: content_pic.join(',')}, function (data) {\r\n            app.showLoading(false);\r\n            if (data.status == 0) {\r\n                app.error(data.msg);\r\n            } else {\r\n                app.success(data.msg);\r\n                // that.content_pic = [];\r\n                app.goto(\"/pages/shoumai/index\")\r\n            }\r\n          });  \r\n        },\r\n        uploadimg:function(e){\r\n          var that = this;\r\n          var field= e.currentTarget.dataset.field\r\n          var pics = that[field]\r\n          if(!pics) pics = [];\r\n          app.chooseImage(function(urls){\r\n            for(var i=0;i<urls.length;i++){\r\n              pics.push(urls[i]);\r\n            }\r\n            if(field == 'pic') that.pic = pics;\r\n            if(field == 'pics') that.pics = pics;\r\n            if(field == 'zhengming') that.zhengming = pics;\r\n          },1)\r\n        },\r\n\t\tcopyAddressText(){\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.$copyText(this.address_text).then(\r\n\t\t\t\tres => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '复制成功',\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t)\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef H5\r\n\t\t\tuni.setClipboardData({\r\n\t\t\t\tdata: this.address_text,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '复制成功',\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t}\r\n        \r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}\r\n.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:80rpx;line-height:80rpx}\r\n.info-item:last-child{border:none}\r\n.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:80rpx;line-height:80rpx}\r\n.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.layui-imgbox-close image{width:100%;height:100%}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n.info {\r\n  margin-bottom: 0.75rem;\r\n  text-align: left;\r\n  line-height: 1.5;\r\n  color: #333;\r\n}\r\n\r\n.input-field {\r\n  box-sizing: border-box; /* 确保padding不会增加输入框的总宽高 */\r\n  width: 100%; /* 宽度为100%，根据父容器调整 */\r\n  padding: 0px 15px; /* 适度的内边距，确保文本和边框之间有空间 */\r\n  margin-top: 1rem; /* 与上方元素的间距 */\r\n  margin-bottom: 1rem; /* 与上方元素的间距 */\r\n  border: 1px solid #0587f0; /* 边框 */\r\n  border-radius: 4px; /* 圆角边框 */\r\n  font-size: 1.3rem; /* 文本大小 */\r\n  line-height: 1.5; /* 行高 */\r\n  color: #333; /* 文本颜色 */\r\n}\r\n\r\n.input-field::placeholder {\r\n  color: #888; /* 占位符颜色 */\r\n}\r\n\r\n.input-field:focus {\r\n  border-color: #405d85; /* 聚焦时的边框颜色 */\r\n  outline: none; /* 移除聚焦时的默认轮廓线 */\r\n}\r\n\r\n\r\n.buy-button {\r\n  width: 100%;\r\n  padding: 0rem 0;\r\n  margin-top: 1.5rem;\r\n  background-color: #0587f0;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 1.125rem;\r\n  transition: background-color 0.2s ease;\r\n}\r\n\r\n.buy-button:hover {\r\n  background-color: #405d85;\r\n}\r\n\r\n.buy-button:active {\r\n  transform: scale(0.98); /* 按钮点击时的小幅缩放 */\r\n}\r\n\r\n\r\n/* 容器样式 */\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 0 2px 5px rgba(0,0,0,0.1);\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  height: 100vh;\r\n}\r\n\r\n/* 分段控制器样式 */\r\n.segment-control {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 50px;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./voucher.vue?vue&type=style&index=0&id=145fb142&scoped=true&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./voucher.vue?vue&type=style&index=0&id=145fb142&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061080\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}