{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?4ec4", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?a99c", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?f4ad", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?be86", "uni-app:///yuyue/dating.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?c9fe", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/dating.vue?a150"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "interval1", "timestamp", "nowtime", "showform", "nowtime2", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "uni", "scrollTop", "duration", "getdata", "that", "app", "keyword", "interval2", "getdjs", "updatemylocation", "thisdata", "longitude", "latitude", "t", "getdistance", "juli", "unit", "<PERSON><PERSON><PERSON><PERSON>", "id", "setTimeout", "title", "content", "showCancel", "confirmText", "da<PERSON><PERSON>", "itemList", "success", "name", "address", "scale", "console", "fail", "searchConfirm", "receiveMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyF/wB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;IACAA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAC;QAAAtB;QAAAE;QAAAqB;MAAA;QACA;UACAD;UACA;QACA;QACA;QACA;UACAD;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;UACAA;UACAT;UACAS;YACAA;YACAA;UACA;UAEA;YACAT;YACAY;cACAH;cACAA;YACA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEAI;MACA;MACA;MACA;QACA;QACA;QACA;UACAJ;QACA;UACA;UACA;UACA;UACA;QACA;QACAA;MACA;IAEA;IACAK;MACA;MACAJ;QACA;QACA;QACA;QACA;UACA;UACA;UACAK;UACAA;UACAA;UACA1B;QACA;QACAoB;QACAA;QACAC;UAAAM;UAAAC;UAAAC;QAAA;UACA;QAAA,CACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACAD;MACA;QAAAA;QAAAC;MAAA;IACA;IACAC;MACA;MACA;MACA;MACAZ;QACAA;QACAA;UAAAa;QAAA;UACAb;UACAA;UACAc;YACA;cACAnB;gBACAoB;gBACAC;gBACAC;gBACAC;cACA;YACA;YACAnB;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACAxB;QACAyB;QACAC;UACA;YACA;cACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;cACA;YACA;YACA1B;cACAY;cACAD;cACAgB;cACAC;cACAC;cACAH;gBACAI;cACA;cACAC;gBACAD;cACA;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA5B;MACAA;IACA;IACA6B;MACA;MACA;QACA7B;MACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/TA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/dating.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/dating.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./dating.vue?vue&type=template&id=60fac11b&\"\nvar renderjs\nimport script from \"./dating.vue?vue&type=script&lang=js&\"\nexport * from \"./dating.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dating.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/dating.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dating.vue?vue&type=template&id=60fac11b&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.leftminute > 0 ? _vm.t(\"color1\") : null\n        var m1 = !(item.leftminute > 0) ? _vm.t(\"color1\") : null\n        var m2 = !!item.isqd ? _vm.t(\"color1\") : null\n        var m3 = !!item.isqd ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n        }\n      })\n    : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dating.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dating.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\" :style=\"{backgroundColor: '#f7f8fa'}\">\n\t<block v-if=\"isload\">\n\t\t<view>\n\t\t\t<view class=\"search-container\" :style=\"{boxShadow: '0 2rpx 10rpx rgba(0,0,0,0.05)'}\">\n\t\t\t\t<view class=\"search-box\" :style=\"{backgroundColor: '#f0f2f5'}\">\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t\t<input class=\"search-text\" placeholder=\"搜索商家\" placeholder-style=\"color:#aaa;font-size:24rpx\" @confirm=\"searchConfirm\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"item.id\">\n\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'jdorderdetail?id=' + item.id\" :style=\"{boxShadow: '0 2rpx 12rpx rgba(0,0,0,0.03)'}\">\n\t\t\t\t<view class=\"head\">\t\n\t\t\t\t\t<view class=\"f1\" v-if=\"item.leftminute>0\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text class=\"t1\" :style=\"{color: t('color1')}\">{{item.leftminute}}分钟内</text> 送达</view>\n\t\t\t\t\t<view class=\"f1\" v-else><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>预约时间：<text :style=\"{color: t('color1')}\">{{item.orderinfo.yydate}}</text></view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"f2\" :style=\"{color: '#ff7a45'}\"><text class=\"t1\">{{item.ticheng}}</text>元</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<view class=\"t1\"><text class=\"x1\" :style=\"{color: '#ff7a45'}\">{{item.juli}}</text><text class=\"x2\">{{item.juli_unit}}</text></view>\n\t\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1\" :style=\"{color: '#ff7a45'}\">{{item.juli2}}</text><text class=\"x2\">{{item.juli2_unit}}</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<view class=\"t1\">{{item.binfo.name}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{item.binfo.address}}</view>\n\t\t\t\t\t\t<view class=\"t3\">{{item.orderinfo.address}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{item.orderinfo.area}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\" :data-index=\"index\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"op\">\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view v-if=\"!item.isqd\" class=\"btn1\" style=\"background: #BCBFC7; box-shadow: none;\">{{item.djs}}后可抢单</view>\n\t\t\t\t\t<view class=\"btn1\" v-else @tap.stop=\"qiangdan\" :data-id=\"item.id\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">抢单</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t \n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\t\t\n\t\t<view class=\"tabbar\">\n\t\t\t<view class=\"tabbar-bot\"></view>\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\n\t\t\t\t<view @tap=\"goto\" data-url=\"dating\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/home2.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text active\" :style=\"{color: t('color1')}\">大厅</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/order.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">订单</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist?st=3\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/orderwc.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">已完成</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"showform\" @tap=\"goto\" data-url=\"formlog\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/dangan.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">档案</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"my\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/my.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">我的</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<view style=\"display:none\">{{timestamp}}</view>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval2 = null;\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n      nodata: false,\n\t\t\tinterval1:null,\n\t\t\ttimestamp:'',\n\t\t\tnowtime:'',\n\t\t\tshowform:0,\n\t\t\tnowtime2:''\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonUnload:function(){\n\t\tclearInterval(this.interval1);\n\t\tclearInterval(this.interval2);\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiYuyueWorker/dating', {st: st,pagenum: pagenum,keyword:keyword}, function (res) {\n\t\t\t\tif(res.status==0){\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.nowtime = res.nowtime\n\t\t\t\t\tthat.nowtime2 = res.nowtime\n\t\t\t\t\tthat.showform = res.showform;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n\t\t\t\t\tthat.updatemylocation(false);\n\t\t\t\t\tclearInterval(that.interval1);\n\t\t\t\t\tthat.interval1 = setInterval(function(){\n\t\t\t\t\t\tthat.updatemylocation(true);\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 10;\n\t\t\t\t\t},10000)\n\t\t\t\t\t\n\t\t\t\t\tif(res.isdelayed){\n\t\t\t\t\t\tclearInterval(interval2);\n\t\t\t\t\t\tinterval2 = setInterval(function () {\n\t\t\t\t\t\t\tthat.nowtime2 = that.nowtime2 + 1;\n\t\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n\t\t\n\t\tgetdjs: function () {\n\t\t  var that = this;\n\t\t  var nowtime = that.nowtime2;\n\t\t  for (var i in that.datalist) {\n\t\t    var thisteam = that.datalist[i];\n\t\t    var totalsec = thisteam.createtime * 1 + thisteam.delayedtime * 60 - nowtime * 1;\n\t\t    if (totalsec <= 0) {\n\t\t      that.datalist[i].isqd = true;\n\t\t    } else {\n\t\t      var houer = Math.floor(totalsec / 3600);\n\t\t      var min = Math.floor((totalsec - houer * 3600) / 60);\n\t\t      var sec = totalsec - houer * 3600 - min * 60;\n\t\t      var djs =  (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n\t\t    }\n\t\t\t\tthat.datalist[i].djs = djs;\n\t\t  }\n\t\n\t\t},\n\t\tupdatemylocation:function(needload){\n\t\t\tvar that = this;\n\t\t\tapp.getLocation(function(res){\n\t\t\t\tvar longitude = res.longitude;\n\t\t\t\tvar latitude = res.latitude;\n\t\t\t\tvar datalist = that.datalist;\n\t\t\t\tfor(var i in datalist){\n\t\t\t\t\tvar thisdata = datalist[i];\n\t\t\t\t\tvar rs = that.getdistance(thisdata.longitude2,thisdata.latitude2,longitude,latitude,1);\n\t\t\t\t\tthisdata.juli2 = rs.juli;\n\t\t\t\t\tthisdata.juli2_unit = rs.unit;\n\t\t\t\t\tthisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);\n\t\t\t\t\tdatalist[i] = thisdata;\n\t\t\t\t}\n\t\t\t\tthat.datalist = datalist;\n\t\t\t\tthat.timestamp = parseInt((new Date().getTime())/1000);\n\t\t\t\tapp.get('ApiYuyueWorker/updatemylocation',{longitude:longitude,latitude:latitude,t:that.timestamp},function(){\n\t\t\t\t\t//if(needload) that.getdata();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tgetdistance: function (lng1, lat1, lng2, lat2) {\n\t\t\tif(!lat1 || !lng1 || !lat2 || !lng2) return '';\n\t\t\tvar rad1 = lat1 * Math.PI / 180.0;\n\t\t\tvar rad2 = lat2 * Math.PI / 180.0;\n\t\t\tvar a = rad1 - rad2;\n\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n\t\t\tvar r = 6378137;\n\t\t\tvar juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));\n\t\t\tvar unit = 'm';\n\t\t\tif(juli> 1000){\n\t\t\t\tjuli = juli/1000;\n\t\t\t\tunit = 'km';\n\t\t\t}\n\t\t\tjuli = juli.toFixed(1);\n\t\t\treturn {juli:juli,unit:unit}\n\t\t},\n    qiangdan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      app.confirm('确定要接单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiYuyueWorker/qiangdan', {id: id}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            if (data.appointment_time) {\n              uni.showModal({\n                title: '接单成功',\n                content: '客户预约上门时间：' + data.appointment_time + '\\n请按时到达客户位置',\n                showCancel: false,\n                confirmText: '我知道了'\n              });\n            }\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n\t\tdaohang:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar datainfo = that.datalist[index];\n\t\t\tuni.showActionSheet({\n        itemList: ['导航到商家', '导航到用户'],\n        success: function (res) {\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tif (res.tapIndex == 0) {\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude\n\t\t\t\t\t\t\tvar name = datainfo.binfo.name\n\t\t\t\t\t\t\tvar address = datainfo.binfo.address\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\n\t\t\t\t\t\t\tvar name = datainfo.orderinfo.address\n\t\t\t\t\t\t\tvar address = datainfo.orderinfo.address\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.openLocation({\n\t\t\t\t\t\t\tlatitude:parseFloat(latitude),\n\t\t\t\t\t\t\tlongitude:parseFloat(longitude),\n\t\t\t\t\t\t\tname:name,\n\t\t\t\t\t\t\taddress:address,\n\t\t\t\t\t\t\tscale: 13,\n\t\t\t\t\t\t\tsuccess: function () {\n                console.log('success');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(res){\n\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.getdata();\n    },\n    receiveMessage: function (data) {\n\t\t\tvar that = this;\n\t\t\tif(data.type == 'peisong' || data.type == 'peisong_jiedan') {\n\t\t\t\tthat.getdata();\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\n  }\n};\n</script>\n<style>\n@import \"./common.css\";\n.container{ width:100%;display:flex;flex-direction:column}\n.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f0f0f0}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}\n\n.order-box{ width: 94%;margin:20rpx 3%;padding:16rpx 3%; background: #fff;border-radius:16rpx;box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);transition: all 0.3s ease;}\n.order-box:active {transform: scale(0.98);}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f0f0f0 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#222222}\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:10rpx}\n.order-box .head .f1 .t1{margin-right:10rpx}\n.order-box .head .f2{color:#ff7a45}\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\n\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f0f0f0;position:relative}\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1 .x1{font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\n\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t3 .x1{font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\n.order-box .content .f2{flex:1;padding:0 20rpx}\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t2{font-size:24rpx;color:#666666;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\n\n.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op .btn1{width:100%;height:88rpx;line-height:88rpx;color:#fff;border-radius:50rpx;text-align:center;font-size:32rpx;box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);transition: all 0.3s ease;}\n.order-box .op .btn1:active {transform: scale(0.98);box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);}\n\n/* 改进tabbar样式 */\n.tabbar-text.active {\n  font-weight: bold;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dating.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./dating.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115051825\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}