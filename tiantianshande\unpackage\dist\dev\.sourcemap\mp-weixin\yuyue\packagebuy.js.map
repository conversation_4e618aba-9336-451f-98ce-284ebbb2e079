{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?5954", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?cfbc", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?7efb", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?a98a", "uni-app:///yuyue/packagebuy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?d3f6", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packagebuy.vue?2952"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "packageId", "orderData", "packageInfo", "businessInfo", "couponList", "couponCount", "linkman", "tel", "remark", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "submitting", "computed", "coupon_money", "leveldk_money", "package_total_price", "actual_price", "price", "onLoad", "app", "uni", "title", "methods", "getBuyData", "that", "package_id", "console", "id", "name", "pic", "sell_price", "product_price", "chooseCoupon", "closeCouponPopup", "selectCoupon", "submitOrder", "buydata", "bid", "prodatastr", "couponrid", "num"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuGnxB;AAAA,eACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACAC;QACAA;MACA;MACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAL;QAAAM;MAAA;QACAC;QACAF;QACA;QACA;UACAA;UACA;UACAA;UACAA;UACA;;UAEA;UACA;UACAE;UAEAF;UACAA;;UAEA;UACA;UACAA;UACAE;;UAEA;UACA;YACA;YACAA;YACA;cACA;cACAA;cAEAF;gBACAG;gBACAC;gBACAC;gBACA;gBACAC;gBACAC;cACA;;cAEA;cACA;gBACAL;gBACAF;cACA;cACAE;YACA;cACAA;cACAP;YACA;UACA;YACAO;YACAP;UACA;;UAEA;UACAK;YAAAG;UAAA;UACAD;QAEA;UACAA;UACAP;YACA;UAAA,CACA;QACA;MACA;QAAA;QACAK;QACAE;QACAP;UACA;QAAA,CACA;MACA;IACA;IAEA;IACAa;MACA;QACA;MACA;QACAb;MACA;IACA;IACA;IACAc;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAhB;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;MACAK;MACAL;;MAEA;MACAO;MACA;;MAEA;MACA;MACAA;MACA;MACA;QACAU;UACAC;UACAC;UAAA;UACAC;UACA;UACA;UACAC;UACAT;QACA;MACA;QACAZ;QACAK;QACAL;QACA;MACA;;MAEA;MACA;QACA;QACAiB;QACA9B;QACAC;QACAC;QACA;MACA;;MAEA;MACAkB;MACA;;MAEAP;QACAA;QACAK;QACA;UACA;UACA;UACA;YACA;YACAE;YACAF;YACAL;;YAEA;YACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;YACA;YACAA;cACAA;YACA;UACA;QACA;UACAA;QACA;MACA;QACAA;QACAK;QACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnWA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packagebuy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packagebuy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packagebuy.vue?vue&type=template&id=092933a0&\"\nvar renderjs\nimport script from \"./packagebuy.vue?vue&type=script&lang=js&\"\nexport * from \"./packagebuy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packagebuy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packagebuy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagebuy.vue?vue&type=template&id=092933a0&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.loading && _vm.packageInfo.id ? _vm.t(\"color1\") : null\n  var g0 = !_vm.loading && _vm.packageInfo.id ? _vm.couponList.length : null\n  var m1 =\n    !_vm.loading &&\n    _vm.packageInfo.id &&\n    _vm.orderData.userinfo &&\n    _vm.orderData.userinfo.leveldk_money > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    !_vm.loading && _vm.packageInfo.id && _vm.coupon_money > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 = !_vm.loading && _vm.packageInfo.id ? _vm.t(\"color1\") : null\n  var m4 = !_vm.loading && _vm.packageInfo.id ? _vm.t(\"color1\") : null\n  var m5 = !_vm.loading && _vm.packageInfo.id ? _vm.t(\"color1\") : null\n  var l0 =\n    !_vm.loading && _vm.packageInfo.id\n      ? _vm.__map(_vm.couponList, function (coupon, __i0__) {\n          var $orig = _vm.__get_orig(coupon)\n          var m6 =\n            _vm.selectedCoupon &&\n            _vm.selectedCoupon.couponrid === coupon.couponrid\n              ? _vm.t(\"color1\")\n              : null\n          var m7 =\n            _vm.selectedCoupon &&\n            _vm.selectedCoupon.couponrid === coupon.couponrid\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n          }\n        })\n      : null\n  var m8 =\n    !_vm.loading && _vm.packageInfo.id && !_vm.selectedCoupon\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    !_vm.loading && _vm.packageInfo.id && !_vm.selectedCoupon\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagebuy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagebuy.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\" v-if=\"!loading && packageInfo.id\">\n\t\t<!-- 购买的套餐信息 -->\n\t\t<view class=\"package-card\">\n\t\t\t<image class=\"package-pic\" :src=\"packageInfo.pic\" mode=\"aspectFill\"></image>\n\t\t\t<view class=\"package-details\">\n\t\t\t\t<view class=\"package-name\">{{ packageInfo.name }}</view>\n\t\t\t\t<view class=\"package-price-buy\" :style=\"{color: t('color1')}\">￥{{ packageInfo.sell_price }}</view>\n\t\t\t\t<!-- 可添加规格/数量等 -->\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 联系方式 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">联系人</text>\n\t\t\t\t<input class=\"input\" v-model=\"linkman\" placeholder=\"请填写联系人姓名\" />\n\t\t\t</view>\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">联系电话</text>\n\t\t\t\t<input class=\"input\" type=\"number\" v-model=\"tel\" placeholder=\"请填写联系电话\" />\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 优惠与金额 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"form-item arrow\" @tap=\"chooseCoupon\" v-if=\"couponList.length > 0\">\n\t\t\t\t<text class=\"label\">优惠券</text>\n\t\t\t\t<view class=\"value coupon-value\" :class=\"{placeholder: !selectedCoupon}\">\n\t\t\t\t\t{{ selectedCoupon ? '已选 ' + selectedCoupon.name : couponCount + ' 张可用' }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"form-item\" v-if=\"orderData.userinfo && orderData.userinfo.leveldk_money > 0\">\n\t\t\t\t<text class=\"label\">会员折扣</text>\n\t\t\t\t<view class=\"value discount-value\" :style=\"{color: t('color1')}\">-￥{{ orderData.userinfo.leveldk_money }}</view>\n\t\t\t</view>\n\t\t\t<view class=\"form-item\" v-if=\"coupon_money > 0\">\n\t\t\t\t<text class=\"label\">优惠券抵扣</text>\n\t\t\t\t<view class=\"value discount-value\" :style=\"{color: t('color1')}\">-￥{{ coupon_money }}</view>\n\t\t\t</view>\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">商品金额</text>\n\t\t\t\t<view class=\"value\">￥{{ packageInfo.product_price || packageInfo.sell_price }}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 备注 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"form-item remark-item\">\n\t\t\t\t<text class=\"label\">订单备注</text>\n\t\t\t\t<textarea class=\"textarea\" v-model=\"remark\" placeholder=\"选填，请输入备注信息\"></textarea>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部提交栏 -->\n\t\t<view class=\"bottom-bar-buy\">\n\t\t\t<view class=\"total-price-area\">\n\t\t\t\t<text>合计：</text>\n\t\t\t\t<text class=\"price-symbol-buy\" :style=\"{color: t('color1')}\">￥</text>\n\t\t\t\t<text class=\"total-price-value\" :style=\"{color: t('color1')}\">{{ actual_price }}</text>\n\t\t\t</view>\n\t\t\t<button class=\"submit-button\" :style=\"{background: t('color1')}\" @tap=\"submitOrder\">提交订单</button>\n\t\t</view>\n\n\t\t<!-- 优惠券选择弹窗 (示例，可能需要替换为项目中已有的组件) -->\n\t\t<uni-popup ref=\"couponPopup\" type=\"bottom\">\n\t\t\t<view class=\"coupon-popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text>选择优惠券</text>\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeCouponPopup\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view scroll-y class=\"coupon-scroll\">\n\t\t\t\t\t<view class=\"coupon-item-popup\" v-for=\"coupon in couponList\" :key=\"coupon.couponrid\"\n\t\t\t\t\t\t@tap=\"selectCoupon(coupon)\" :class=\"{selected: selectedCoupon && selectedCoupon.couponrid === coupon.couponrid}\">\n\t\t\t\t\t\t<view class=\"coupon-info\">\n\t\t\t\t\t\t\t<view class=\"coupon-name\">{{ coupon.name }}</view>\n\t\t\t\t\t\t\t<view class=\"coupon-desc\">{{ coupon.desc }}</view>\n\t\t\t\t\t\t\t<view class=\"coupon-expire\">有效期至 {{ coupon.expiredate }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"coupon-radio\" :style=\"selectedCoupon && selectedCoupon.couponrid === coupon.couponrid ? 'border-color:' + t('color1') + ';background:' + t('color1') : ''\">\n\t\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"selectedCoupon && selectedCoupon.couponrid === coupon.couponrid\">✓</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"coupon-item-popup no-use\" @tap=\"selectCoupon(null)\" :class=\"{selected: !selectedCoupon}\">\n\t\t\t\t\t\t不使用优惠券\n\t\t\t\t\t\t<view class=\"coupon-radio\" :style=\"!selectedCoupon ? 'border-color:' + t('color1') + ';background:' + t('color1') : ''\">\n\t\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"!selectedCoupon\">✓</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t</view>\n\t<view class=\"loading-container\" v-else-if=\"loading\">\n\t\t<text>加载中...</text>\n\t</view>\n\t<view class=\"empty-container\" v-else>\n\t\t<text>加载订单信息失败</text>\n\t</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n\tcomponents: { // 注册uni-popup组件\n\t\t// 如果项目未使用easycom，需要在此显式注册\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tpackageId: null,\n\t\t\torderData: {},       // 接口返回的原始数据\n\t\t\tpackageInfo: {},     // 提取的套餐信息\n\t\t\tbusinessInfo: {},    // 提取的商家信息\n\t\t\tcouponList: [],      // 可用优惠券列表\n\t\t\tcouponCount: 0,      // 可用优惠券数量\n\t\t\tlinkman: '',\n\t\t\ttel: '',\n\t\t\tremark: '',\n\t\t\tselectedCoupon: null, // 选中的优惠券对象 {couponrid, name, desc, money, ...}\n\t\t\tloading: true,\n\t\t\tsubmitting: false // 防止重复提交\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 优惠券抵扣金额\n\t\tcoupon_money() {\n\t\t\treturn this.selectedCoupon ? parseFloat(this.selectedCoupon.money || 0) : 0;\n\t\t},\n\t\t// 会员折扣金额\n\t\tleveldk_money() {\n\t\t\treturn this.orderData.userinfo ? parseFloat(this.orderData.userinfo.leveldk_money || 0) : 0;\n\t\t},\n\t\t// 套餐原始总价 (可能是product_price或sell_price，根据业务定)\n\t\tpackage_total_price() {\n\t\t\treturn parseFloat(this.packageInfo.product_price || this.packageInfo.sell_price || 0);\n\t\t},\n\t\t// 最终实际支付金额\n\t\tactual_price() {\n\t\t\tlet price = this.package_total_price - this.leveldk_money - this.coupon_money;\n\t\t\tprice = price < 0 ? 0 : price; // 价格不能小于0\n\t\t\treturn price.toFixed(2); // 保留两位小数\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.package_id) {\n\t\t\tthis.packageId = options.package_id;\n\t\t\tthis.getBuyData();\n\t\t} else {\n\t\t\tapp.error('缺少套餐ID', function() {\n\t\t\t\tapp.goback();\n\t\t\t});\n\t\t\tthis.loading = false;\n\t\t}\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: '确认订单'\n\t\t});\n\t},\n\tmethods: {\n\t\tgetBuyData() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiYuyuePackage/buy', { package_id: that.packageId }, function(res) {\n\t\t\t\tconsole.log('[getBuyData] ApiYuyuePackage/buy 返回: ', JSON.stringify(res)); // 添加日志\n\t\t\t\tthat.loading = false;\n\t\t\t\t// 根据新的 API 响应调整数据提取逻辑\n\t\t\t\tif (res.status == 1 && res.allbuydata && Array.isArray(res.allbuydata) && res.allbuydata.length > 0) {\n\t\t\t\t\tthat.orderData = res; // 存储完整响应，以防他处需要\n\t\t\t\t\t// 提取默认联系方式和地址\n\t\t\t\t\tthat.linkman = res.linkman || '';\n\t\t\t\t\tthat.tel = res.tel || '';\n\t\t\t\t\t// that.address = res.address || {}; // 如果需要显示地址\n\n\t\t\t\t\t// 处理 allbuydata 数组 (假设只处理第一个元素，即一次只买一个套餐包)\n\t\t\t\t\tconst buyItem = res.allbuydata[0];\n\t\t\t\t\tconsole.log('[getBuyData] 处理 buyItem: ', JSON.stringify(buyItem));\n\n\t\t\t\t\tthat.couponList = buyItem.couponList || [];\n\t\t\t\t\tthat.couponCount = buyItem.couponCount || 0;\n\n\t\t\t\t\t// 确保 userinfo 对象存在，并存储会员折扣信息\n\t\t\t\t\tif (!that.orderData.userinfo) that.orderData.userinfo = {};\n\t\t\t\t\tthat.orderData.userinfo.leveldk_money = buyItem.leveldk_money || 0;\n\t\t\t\t\tconsole.log('[getBuyData] 更新 userinfo: ', JSON.stringify(that.orderData.userinfo));\n\n\t\t\t\t\t// 从 prodata 中提取套餐信息\n\t\t\t\t\tif (buyItem.prodata && Array.isArray(buyItem.prodata) && buyItem.prodata.length > 0) {\n\t\t\t\t\t\tconst productInfo = buyItem.prodata[0];\n\t\t\t\t\t\tconsole.log('[getBuyData] 处理 productInfo: ', JSON.stringify(productInfo));\n\t\t\t\t\t\tif (productInfo.package) {\n\t\t\t\t\t\t\tconst packageFromServer = productInfo.package;\n\t\t\t\t\t\t\tconsole.log('[getBuyData] 提取 packageFromServer: ', JSON.stringify(packageFromServer));\n\n\t\t\t\t\t\t\tthat.packageInfo = {\n\t\t\t\t\t\t\t\tid: packageFromServer.id,\n\t\t\t\t\t\t\t\tname: packageFromServer.name,\n\t\t\t\t\t\t\t\tpic: packageFromServer.pic,\n\t\t\t\t\t\t\t\t// 优先使用 buyItem 中的价格，然后是 package 中的\n\t\t\t\t\t\t\t\tsell_price: buyItem.sell_price !== undefined ? buyItem.sell_price : packageFromServer.sell_price,\n\t\t\t\t\t\t\t\tproduct_price: buyItem.product_price !== undefined ? buyItem.product_price : (packageFromServer.product_price || packageFromServer.sell_price)\n\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t// 清理图片 URL\n\t\t\t\t\t\t\tif (that.packageInfo.pic && that.packageInfo.pic.startsWith('https://localhost')) {\n\t\t\t\t\t\t\t\tconsole.log('[getBuyData] 清理套餐图片前缀');\n\t\t\t\t\t\t\t\tthat.packageInfo.pic = that.packageInfo.pic.substring('https://localhost'.length);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.log('[getBuyData] 构建的 packageInfo: ', JSON.stringify(that.packageInfo));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.error('[getBuyData] buyItem.prodata[0] 中缺少 package 对象');\n\t\t\t\t\t\t\tapp.error('加载购买信息失败[pke]');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('[getBuyData] buyItem.prodata 结构错误或为空');\n\t\t\t\t\t\tapp.error('加载购买信息失败[pdi]');\n\t\t\t\t\t}\n\n\t\t\t\t\t// 存储商家ID，用于提交订单 (商家名称和logo可能需要从packageInfo或单独接口获取)\n\t\t\t\t\tthat.businessInfo = { id: buyItem.bid };\n\t\t\t\t\tconsole.log('[getBuyData] 存储 businessInfo: ', JSON.stringify(that.businessInfo));\n\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('[getBuyData] 接口状态错误或 allbuydata 结构不符: ', res);\n\t\t\t\t\tapp.error(res.msg || '加载订单信息失败', function() {\n\t\t\t\t\t\t// app.goback(); // 暂时不返回，让用户看到错误\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, function(err) { // 添加错误回调日志\n\t\t\t\tthat.loading = false;\n\t\t\t\tconsole.error('[getBuyData] ApiYuyuePackage/buy 请求失败: ', err);\n\t\t\t\tapp.error('请求失败', function() {\n\t\t\t\t\t// app.goback();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\n\t\t// 打开优惠券弹窗\n\t\tchooseCoupon() {\n\t\t\tif (this.couponList.length > 0) {\n\t\t\t\tthis.$refs.couponPopup.open();\n\t\t\t} else {\n\t\t\t\tapp.toast('暂无可用优惠券');\n\t\t\t}\n\t\t},\n\t\t// 关闭优惠券弹窗\n\t\tcloseCouponPopup() {\n\t\t\tthis.$refs.couponPopup.close();\n\t\t},\n\t\t// 选择优惠券\n\t\tselectCoupon(coupon) {\n\t\t\tthis.selectedCoupon = coupon;\n\t\t\tthis.closeCouponPopup();\n\t\t},\n\n\t\t// 提交订单\n\t\tsubmitOrder() {\n\t\t\tvar that = this;\n\t\t\tif (!that.linkman) {\n\t\t\t\tapp.error('请填写联系人');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!that.tel || !/^1[3-9]\\d{9}$/.test(that.tel)) {\n\t\t\t\tapp.error('请填写正确的手机号');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (that.submitting) return; // 防止重复提交\n\t\t\tthat.submitting = true;\n\t\t\tapp.showLoading('提交中...');\n\n\t\t\t//-- 调试日志 --\n\t\t\tconsole.log('[submitOrder] 当前 packageInfo:', JSON.stringify(that.packageInfo));\n\t\t\t//--------------\n\n\t\t\t// 构建buydata数组\n\t\t\tlet buydata = [];\n\t\t\tconsole.log('[submitOrder] 检查条件: businessInfo.id=', that.businessInfo.id, 'packageId=', that.packageId); // 添加日志\n\t\t\t// 修改判断条件，允许 businessInfo.id 为 0\n\t\t\tif (typeof that.businessInfo.id === 'number' && that.packageId) {\n\t\t\t\tbuydata.push({\n\t\t\t\t\tbid: that.businessInfo.id,\n\t\t\t\t\tprodatastr: that.packageId, // 套餐ID，接口文档用prodatastr\n\t\t\t\t\tcouponrid: that.selectedCoupon ? that.selectedCoupon.couponrid : 0,\n\t\t\t\t\t// remark: that.remark, // 接口文档中 remark 在外层\n\t\t\t\t\t// 数量可能需要，默认为1\n\t\t\t\t\tnum: 1,\n\t\t\t\t\tproduct_price: that.packageInfo.product_price // 添加后端需要的 product_price\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tthat.submitting = false;\n\t\t\t\tapp.error('订单信息不完整');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 准备提交参数\n\t\t\tconst params = {\n\t\t\t\t// buydata: JSON.stringify(buydata), // 接口可能需要JSON字符串 - 改为直接传递数组\n\t\t\t\tbuydata: buydata,\n\t\t\t\tlinkman: that.linkman,\n\t\t\t\ttel: that.tel,\n\t\t\t\tremark: that.remark\n\t\t\t\t// 可能还需要 platform, scene 等通用参数，app.post内部处理\n\t\t\t};\n\n\t\t\t//-- 调试日志 --\n\t\t\tconsole.log('[submitOrder] 最终提交参数 params:', JSON.stringify(params));\n\t\t\t//--------------\n\n\t\t\tapp.post('ApiYuyuePackage/createOrder', params, function(res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tthat.submitting = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tconst order_id = res.order_id;\n\t\t\t\t\t// 判断是否需要支付\n\t\t\t\t\tif (that.actual_price > 0 && res.payorderid) {\n\t\t\t\t\t\t// 修改此处：参考buy.vue的支付方式，直接跳转到支付页面\n\t\t\t\t\t\tconsole.log('[submitOrder] 跳转支付页面, payorderid=', res.payorderid); // 添加日志\n\t\t\t\t\t\tthat.submitting = true; // 设置提交状态，防止重复点击\n\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 注释掉原来的支付方式\n\t\t\t\t\t\t/*\n\t\t\t\t\t\tapp.payorder({\n\t\t\t\t\t\t\tpayorderid: res.payorderid,\n\t\t\t\t\t\t\torderid: order_id, // 支付成功后跳转可能需要\n\t\t\t\t\t\t\ttype: 'yuyue_package', // 定义一个类型用于支付成功后的回调或跳转判断\n\t\t\t\t\t\t\tsuccess: function(){\n\t\t\t\t\t\t\t\tapp.success('支付成功', function(){\n\t\t\t\t\t\t\t\t\tapp.goto('/pages/my/packageorderlist'); // 跳转到套餐订单列表\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(){\n\t\t\t\t\t\t\t\tapp.error('支付失败', function(){\n\t\t\t\t\t\t\t\t\tapp.goto('/pages/my/packageorderlist?status=0'); // 跳转到待支付列表\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t*/\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 0元订单，直接成功\n\t\t\t\t\t\tapp.success('下单成功', function() {\n\t\t\t\t\t\t\tapp.goto('/pages/my/packageorderlist');\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '创建订单失败');\n\t\t\t\t}\n\t\t\t}, function() {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tthat.submitting = false;\n\t\t\t\tapp.error('请求失败');\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tpadding-bottom: 140rpx; /* 留出底部空间 */\n}\n\n/* 套餐卡片 */\n.package-card {\n\tdisplay: flex;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.package-pic {\n\twidth: 160rpx;\n\theight: 160rpx;\n\tborder-radius: 8rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.package-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n}\n\n.package-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\t/* 最多显示两行 */\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n\tmargin-bottom: 10rpx;\n}\n\n.package-price-buy {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n/* 表单区域 */\n.form-section {\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 0 25rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.form-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmin-height: 100rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.form-item:last-child {\n\tborder-bottom: none;\n}\n\n.form-item.arrow {\n\tposition: relative;\n\tpadding-right: 30rpx;\n}\n\n.form-item.arrow::after {\n\tcontent: '';\n\tposition: absolute;\n\tright: 0;\n\ttop: 50%;\n\ttransform: translateY(-50%) rotate(45deg);\n\twidth: 14rpx;\n\theight: 14rpx;\n\tborder-top: 2rpx solid #ccc;\n\tborder-right: 2rpx solid #ccc;\n}\n\n.label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\twidth: 150rpx; /* 固定标签宽度 */\n\tflex-shrink: 0;\n}\n\n.input {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\ttext-align: right;\n\theight: 100%; /* 确保input高度撑满form-item */\n}\n\n.value {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\ttext-align: right;\n}\n\n.coupon-value {\n\tcolor: #ff9900;\n}\n.coupon-value.placeholder {\n\tcolor: #999;\n}\n\n.discount-value {\n\tfont-weight: bold;\n}\n\n.remark-item {\n\talign-items: flex-start; /* 标签和文本域顶部对齐 */\n\tpadding: 20rpx 0;\n\tmin-height: 150rpx;\n}\n.remark-item .label {\n\tpadding-top: 10rpx; /* 微调标签位置 */\n}\n\n.textarea {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\theight: 120rpx;\n\tpadding: 10rpx;\n\tbox-sizing: border-box;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 8rpx;\n\twidth: auto; /* 覆盖uni-app默认宽度 */\n}\n\n/* 底部提交栏 */\n.bottom-bar-buy {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground-color: #fff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0 20rpx 0 30rpx;\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n\tbox-sizing: border-box;\n\tpadding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */\n\theight: calc(100rpx + env(safe-area-inset-bottom));\n}\n\n.total-price-area {\n\tfont-size: 26rpx;\n\tcolor: #333;\n}\n\n.price-symbol-buy {\n\tfont-size: 28rpx;\n\tmargin-left: 8rpx;\n\tfont-weight: bold;\n}\n\n.total-price-value {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n}\n\n.submit-button {\n\theight: 72rpx;\n\tline-height: 72rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n\tpadding: 0 50rpx;\n\tborder-radius: 36rpx;\n\ttext-align: center;\n\tborder: none;\n\toutline: none;\n\tmargin: 0;\n}\nbutton::after {\n\tborder: none;\n}\n\n/* 优惠券弹窗 */\n.coupon-popup-content {\n\tbackground-color: #fff;\n\tborder-top-left-radius: 20rpx;\n\tborder-top-right-radius: 20rpx;\n\tpadding: 20rpx 0;\n\theight: 60vh; /* 弹窗高度 */\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.popup-header {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\ttext-align: center;\n\tpadding-bottom: 20rpx;\n\tposition: relative;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.close-icon {\n\tposition: absolute;\n\tright: 30rpx;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\tfont-size: 36rpx;\n\tcolor: #999;\n}\n\n.coupon-scroll {\n\tflex: 1;\n\toverflow-y: auto;\n\tpadding: 20rpx 30rpx;\n}\n\n.coupon-item-popup {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 25rpx 0;\n\tborder-bottom: 1rpx dashed #eee;\n}\n.coupon-item-popup:last-of-type {\n    border-bottom: none;\n}\n.coupon-item-popup.no-use {\n    font-size: 28rpx;\n    color: #666;\n}\n\n.coupon-info {\n\tflex: 1;\n\tmargin-right: 20rpx;\n}\n\n.coupon-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tfont-weight: bold;\n}\n\n.coupon-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 8rpx;\n}\n\n.coupon-expire {\n\tfont-size: 22rpx;\n\tcolor: #ccc;\n}\n\n.coupon-radio {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder-radius: 50%;\n\tborder: 2rpx solid #ccc;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tflex-shrink: 0;\n}\n\n.coupon-radio .check-icon {\n\tcolor: #fff;\n\tfont-size: 24rpx;\n}\n\n/* 加载和空状态 */\n.loading-container, .empty-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmin-height: 80vh;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagebuy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packagebuy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041075\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}