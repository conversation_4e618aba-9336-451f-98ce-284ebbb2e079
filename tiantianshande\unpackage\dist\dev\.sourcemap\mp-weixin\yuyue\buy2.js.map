{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?a04b", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?4201", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?7977", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?ddec", "uni-app:///yuyue/buy2.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?ccaa", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/buy2.vue?f66f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "test", "address", "totalprice", "couponvisible", "bid", "nowbid", "needaddress", "linkman", "tel", "userinfo", "latitude", "longitude", "allbuydata", "alltotalprice", "type11visible", "type11key", "regiondata", "items", "editorFormdata", "sindex", "prodata", "yydate", "master", "onLoad", "onPullDownRefresh", "methods", "getdata", "app", "that", "showCouponList", "handleClickMask", "topay", "frompage", "addressid", "remark", "editorChooseImage", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "selectFwtype", "inputLinkman", "inputTel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2G7wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAP;MACA;QACA;UAAAO;QAAA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACAA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACAA;MACAA;QACAK;QACAC;QACA1B;QACAC;QACA0B;QACAb;QACAD;MACA;QACAO;QACA;UACAA;UACA;QACA;UACAA;UACA;QAEA;QACA;MAEA;IACA;;IACAQ;MACA;MACA;MACA;MACA;MACA;MACAR;QACAT;QACA;QACAU;QACAA;MACA;IACA;IAAAQ;MACA;MACA;MACA;MACA;MACA;MACA;MACAlB;MACA;MACAU;MACAA;IACA;IACA;IACAS;MACA;MACA;MACAT;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnQA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/buy2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/buy2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy2.vue?vue&type=template&id=a7ab0fc8&\"\nvar renderjs\nimport script from \"./buy2.vue?vue&type=script&lang=js&\"\nexport * from \"./buy2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/buy2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy2.vue?vue&type=template&id=a7ab0fc8&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy2.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view v-if=\"sindex==1\" class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\"\r\n\t\t\t\t\t:data-url=\"'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=1'\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/address.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\r\n\t\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"f2 flex1\">请选择您的地点</view>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buydata\">\t\t\t\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t\t服务信息\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product2?id=' + allbuydata.skillId\">\r\n\t\t\t\t\t\t\t\t\t<image v-if=\"allbuydata.pic\" :src=\"allbuydata.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t<image v-else :src=\"allbuydata.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{allbuydata.name}}</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{allbuydata.price}}{{allbuydata.unit}}</text>\r\n\t\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{allbuydata.num}}</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent2\">\r\n\t\t\t\t\t\t<view class=\"btitle2\">\r\n\t\t\t\t\t\t\t服务方式\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\r\n\t\t\t\t\t\t\t<view class=\"price\" >\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">预约时间</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" >{{yydate}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"price\" >\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">服务人员</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" >{{master.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"price\" >\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">服务类型</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" >{{allbuydata.serviceType}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent2\">\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">服务价格</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{allbuydata.price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\" >\r\n\t\t\t\t\t\t\t<text class=\"f1\">路程费用</text>\r\n\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t<text style=\"color:red; \">{{master.juli}}km </text> \r\n\t\t\t\t\t\t\t\t<text> ¥{{allbuydata.freight_price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\" >\r\n\t\t\t\t\t\t\t<view class=\"label\">顾客备注<text> </text></view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<input name=\"remark\" class='input' placeholder=\"请输入备注\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<view style=\"width: 100%; height:110rpx;\"></view>\r\n\t\t\t\t<view class=\"footer flex\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t\t确认提交</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\ttest:'test',\r\n\t\t\t\taddress: [],\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tnowbid: 0,\r\n\t\t\t\tneedaddress: 1,\r\n\t\t\t\tlinkman: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tuserinfo: {},\r\n\t\t\t\tlatitude: \"\",\r\n\t\t\t\tlongitude: \"\",\r\n\t\t\t\tallbuydata: {},\r\n\t\t\t\talltotalprice: \"\",\r\n\t\t\t\ttype11visible: false,\r\n\t\t\t\ttype11key: -1,\r\n\t\t\t\tregiondata: '',\r\n\t\t\t\titems: [],\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\tsindex:0,\r\n\t\t\t\tprodata:'',\r\n\t\t\t\tyydate:'',\r\n\t\t\t\tmaster:[]\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.yydate = opt.yydate\r\n\t\t\tthis.prodata = opt.prodata;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\t\t\t\r\n\t\t\t\tapp.get('ApiYuyue2/buy', {\r\n\t\t\t\t\tprodata: that.opt.prodata,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif(res.status==0){\t\tapp.error('技能获取失败'); }\r\n\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\t\tthat.allbuydata = res.allbuydata;\r\n\t\t\t\t\tthat.totalprice = res.totalprice;\r\n\t\t\t\t\tthat.rdata = res.rdata;\r\n\t\t\t\t\tthat.master = res.master;\r\n\t\t\t\t\r\n\t\t\t\t\t//\tthat.calculatePrice();\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowCouponList: function(e) {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t},\r\n\t\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar addressid = this.address.id;\r\n\t\t\t\tvar linkman = this.linkman;\r\n\t\t\t\tvar tel = this.tel;\r\n\t\t\t\tvar frompage = that.opt.frompage ? that.opt.frompage : '';\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tif(addressid == undefined) {\r\n\t\t\t\t\tapp.error('请选择地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(!linkman || !tel) {\r\n\t\t\t\t\tapp.error('请填写联系人及联系电话');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar remark =  formdata.remark;\r\n\t\t\t\tvar yydate = that.yydate;\r\n\t\t\t\t//console.log(buydata);return;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiYuyue2/createOrder', {\r\n\t\t\t\t\tfrompage: frompage,\r\n\t\t\t\t\taddressid: addressid,\r\n\t\t\t\t\tlinkman: linkman,\r\n\t\t\t\t\ttel: tel,\r\n\t\t\t\t\tremark:remark,\r\n\t\t\t\t\tyydate:that.yydate,\r\n\t\t\t\t\tprodata:that.prodata,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t\t//that.showsuccess(res.data.msg);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//app.error('订单编号：' +res.payorderid);\r\n\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},editorBindPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},\r\n\t\t\t//选择服务方式\r\n\t\t\tselectFwtype: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tthat.sindex = index\r\n\t\t\t},\r\n\t\t\tinputLinkman: function (e) {\r\n\t\t\t\tthis.linkman = e.detail.value\r\n\t\t\t},\r\n\t\t\tinputTel: function (e) {\r\n\t\t\t\tthis.tel = e.detail.value\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}\r\n.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}\r\n.address-add .f1 {margin-right: 20rpx}\r\n.address-add .f1 .img {width: 66rpx;height: 66rpx;}\r\n.address-add .f2 {color: #666;}\r\n.address-add .f3 {width: 26rpx;height: 26rpx;}\r\n.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;\r\n    text-align: right;}\r\n.linkitem .f1 {width: 160rpx;color: #111111}\r\n.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}\r\n.buydata {width: 94%;margin: 0 3%;margin-bottom: 20rpx;}\r\n.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}\r\n.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}\r\n.btitle2 {width: 100%; padding-top:20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}\r\n.btitle2 .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}\r\n\r\n.bcontent {width: 100%;padding: 0 20rpx;background: #fff;border-radius: 20rpx;}\r\n.bcontent2 {width: 100%;padding: 0 30rpx; margin-top: 30rpx;background: #fff;border-radius: 20rpx;}\r\n.product {width: 100%;border-bottom: 1px solid #f4f4f4}\r\n.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}\r\n.product .item:last-child {border: none}\r\n.product .info {padding-left: 20rpx;}\r\n.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.product .info .f2 {color: #999999;font-size: 24rpx}\r\n.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n.product image {width: 140rpx;height: 140rpx}\r\n.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}\r\n.freight .f1 {color: #333;margin-bottom: 10rpx}\r\n.freight .f2 {color: #111111;text-align: right;flex: 1}\r\n.freight .f3 {width: 24rpx;height: 28rpx;}\r\n.freighttips {color: red;font-size: 24rpx;}\r\n.freight-ul {width: 100%;display: flex;}\r\n.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}\r\n\r\n.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}\r\n.price .f1 {color: #333}\r\n.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.price .f3 {width: 24rpx;height: 24rpx;}\r\n.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}\r\n.scoredk .f1 {color: #333333}\r\n.scoredk .f2 {color: #999999;text-align: right;flex: 1}\r\n.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}\r\n.remark .f1 {color: #333;width: 200rpx}\r\n.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 20rpx;display: flex;align-items: center;z-index: 8}\r\n.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}\r\n.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}\r\n.storeitem .panel .f1 {color: #333}\r\n.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}\r\n.storeitem .radio-item:last-child {border: 0}\r\n.storeitem .radio-item .f1 {color: #666;flex: 1}\r\n.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}\r\n.storeitem .radio .radio-img {width: 100%;height: 100%}\r\n.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\r\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\r\n.cuxiao-desc {width: 100%}\r\n.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}\r\n.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.cuxiao-item .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}\r\n.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}\r\n.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}\r\n.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}\r\n.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}\r\n.memberitem .t1{color:#333;font-weight:bold}\r\n.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.memberitem .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.checkMem{ display: inline-block; }\r\n.checkMem p{ height: 30px; width: 100%; display: inline-block; }\r\n.placeholder{  font-size: 26rpx;line-height: 80rpx;}\r\n.selected-item span{ font-size: 26rpx !important;}\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045089\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}