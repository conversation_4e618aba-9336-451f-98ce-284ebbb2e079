{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?4287", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?7ead", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?c846", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?8724", "uni-app:///pagesExa/ranking-reward/rules.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?a4a8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/rules.vue?d21f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "rulesList", "loading", "onLoad", "onPullDownRefresh", "uni", "methods", "getRulesList", "console", "app", "that", "title", "icon", "callback", "goToRulePreview", "url", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoC9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MAEA;MACA;MAEAC;QACAC;QAEA;UACAF;UACAE;QACA;UACAF;UACAH;YACAM;YACAC;UACA;QACA;QAEA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACAN;MACAH;QACAU;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,42CAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/ranking-reward/rules.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/ranking-reward/rules.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rules.vue?vue&type=template&id=94fd4116&\"\nvar renderjs\nimport script from \"./rules.vue?vue&type=script&lang=js&\"\nexport * from \"./rules.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rules.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/ranking-reward/rules.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rules.vue?vue&type=template&id=94fd4116&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.rulesList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rules.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rules.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 规则列表 -->\r\n\t\t<view class=\"rules-container\">\r\n\t\t\t<view class=\"no-data\" v-if=\"rulesList.length === 0\">\r\n\t\t\t\t<text>暂无可用奖励规则</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"rule-card\" v-for=\"(item, index) in rulesList\" :key=\"index\" @click=\"goToRulePreview(item.id)\">\r\n\t\t\t\t<view class=\"rule-header\">\r\n\t\t\t\t\t<view class=\"rule-name\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"rule-tag\">{{item.reward_mode_name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-content\">\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">排名类型：</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{item.rank_type_name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">奖励比例：</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{item.total_reward_rate}}%</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">奖励排名：</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">前{{item.reward_top_num}}名</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-footer\">\r\n\t\t\t\t\t<view class=\"rule-btn\">查看详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\trulesList: [],\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getRulesList();\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.getRulesList(() => {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取规则列表\r\n\t\t\tgetRulesList(callback) {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/rules][getRulesList_001] 开始获取排名奖励规则列表`);\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getRankingRules', {}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/rules][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);\r\n\t\t\t\t\t\tthat.rulesList = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/rules][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取规则列表失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (callback && typeof callback === 'function') {\r\n\t\t\t\t\t\tcallback();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到规则预览页\r\n\t\t\tgoToRulePreview(ruleId) {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/rules][goToRulePreview_001] 跳转到规则预览页，规则ID：${ruleId}`);\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pagesExa/ranking-reward/preview?rule_id=${ruleId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间，用于日志\r\n\t\t\tformatTime() {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\tconst seconds = date.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\tconst milliseconds = date.getMilliseconds().toString().padStart(3, '0');\r\n\t\t\t\t\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n// 规则列表\r\n.rule-card {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n\toverflow: hidden;\r\n}\r\n\r\n.rule-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n}\r\n\r\n.rule-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.rule-tag {\r\n\tfont-size: 24rpx;\r\n\tcolor: #FFFFFF;\r\n\tbackground-color: #FF6600;\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.rule-content {\r\n\tpadding: 20rpx 30rpx;\r\n}\r\n\r\n.rule-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.rule-item:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.item-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\twidth: 160rpx;\r\n}\r\n\r\n.item-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.rule-footer {\r\n\tpadding: 20rpx 30rpx 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.rule-btn {\r\n\tbackground-color: #FF6600;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 28rpx;\r\n\tpadding: 16rpx 0;\r\n\tborder-radius: 40rpx;\r\n\ttext-align: center;\r\n\twidth: 300rpx;\r\n}\r\n\r\n// 无数据提示\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 100rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rules.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./rules.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115084327\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}