{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?0380", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?dda5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?64bf", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?7b72", "uni-app:///pagesExa/my/set.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?d4b7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/set.vue?dd41"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "userinfo", "member_auto_addlogin", "member_auto_reg", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "console", "uploadHeadimg", "headimg", "delaccount", "isAutoLoginEnabled", "logout", "goBack", "uni", "delta", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsI5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;;QAEA;QACAA;QACAA;;QAEA;QACAE;QACAA;QACAA;QAEAF;MACA;IACA;IACAG;MACA;MACAF;QACA;QACAD;QACAC;UAAAG;QAAA;MACA;IACA;IACAC;MACAJ;QACAA;QACAA;UACAA;UACA;YACAA;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAK;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACAN;QACA;MACA;MAEAD;MACAC;QACAA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAO;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrPA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/set.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/set.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./set.vue?vue&type=template&id=9f046f34&\"\nvar renderjs\nimport script from \"./set.vue?vue&type=script&lang=js&\"\nexport * from \"./set.vue?vue&type=script&lang=js&\"\nimport style0 from \"./set.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/set.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./set.vue?vue&type=template&id=9f046f34&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload\n    ? !_vm.isAutoLoginEnabled() && _vm.member_auto_addlogin != 1\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./set.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./set.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"content\">\n\t\t\t<view class=\"info-item\" style=\"height:136rpx;line-height:136rpx\">\n\t\t\t\t<view class=\"t1\" style=\"flex:1;\">头像</view>\n\t\t\t\t<image :src=\"userinfo.headimg\" style=\"width:88rpx;height:88rpx;\" @tap=\"uploadHeadimg\"/>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setnickname\">\n\t\t\t\t<view class=\"t1\">昵称</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.nickname}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content\">\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setidcard\">\n\t\t\t\t<view class=\"t1\">身份证认证</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.realname}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setrealname\">\n\t\t\t\t<view class=\"t1\">姓名</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.realname}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"settel\">\n\t\t\t\t<view class=\"t1\">手机号</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.tel}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setsex\">\n\t\t\t\t<text class=\"t1\">性别</text>\n\t\t\t\t<text class=\"t2\" v-if=\"userinfo.sex==1\">男</text>\n\t\t\t\t<text class=\"t2\" v-else-if=\"userinfo.sex==2\">女</text>\n\t\t\t\t<text class=\"t2\" v-else>未知</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setbirthday\">\n\t\t\t\t<text class=\"t1\">生日</text>\n\t\t\t\t<text class=\"t2\">{{userinfo.birthday}}</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"seteducation\">\n\t\t\t\t<text class=\"t1\">学历</text>\n\t\t\t\t<text class=\"t2\">{{userinfo.education || '未设置'}}</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setmaritalstatus\">\n\t\t\t\t<text class=\"t1\">情感状态</text>\n\t\t\t\t<text class=\"t2\">{{userinfo.marital_status || '未设置'}}</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setprofession\">\n\t\t\t\t<text class=\"t1\">职业</text>\n\t\t\t\t<text class=\"t2\">{{userinfo.profession || '未设置'}}</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"editaddress\">\n\t\t\t\t<view class=\"t1\">地理位置</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.full_address}} </view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t <view class=\"content\">\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setweixin\">\n\t\t\t\t<view class=\"t1\">微信号</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.weixin}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setaliaccount\">\n\t\t\t\t<view class=\"t1\">支付宝账号</view>\n\t\t\t\t<view class=\"t2\">{{userinfo.aliaccount}}</view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"setbankinfo\">\n\t\t\t\t<text class=\"t1\">银行卡</text>\n\t\t\t\t<text class=\"t2\">{{userinfo.bankname ? '已设置' : ''}}</text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"/pages/address/address\">\n\t\t\t\t<view class=\"t1\">收货地址</view>\n\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content\" v-if=\"userinfo.haspwd==1\">\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"/pagesExa/my/setpwd\">\n\t\t\t\t<view class=\"t1\">修改密码</view>\n\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content\">\n\t\t\t<view class=\"info-item\" @tap=\"goto\" data-url=\"/pagesB/login/login\">\n\t\t\t\t<view class=\"t1\">切换账号</view>\n\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"content\" v-if=\"!isAutoLoginEnabled() && member_auto_addlogin != 1\">\n\t\t\t<view class=\"info-item\" @tap=\"logout\">\n\t\t\t\t<view class=\"t1\">退出登录</view>\n\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #ifdef APP-PLUS -->\n\t\t<view class=\"content\" v-if=\"!isAutoLoginEnabled() && member_auto_addlogin != 1\">\n\t\t\t<view class=\"info-item\" @tap=\"delaccount\">\n\t\t\t\t<view class=\"t1\">注销账号</view>\n\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/arrowright.png\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t <!-- 底部返回按钮 -->\n\t\t    <view class=\"content\">\n\t\t      <view class=\"back-button\" @tap=\"goBack\">\n\t\t        <text class=\"t1\">返回</text>\n\t\t      </view>\n\t\t    </view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tuserinfo:{},\n\t\t\tmember_auto_addlogin: 0, // 会员自动添加登录：1=开启，0=关闭\n\t\t\tmember_auto_reg: 0, // 游客自动注册会员：1=开启，0=关闭\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiMy/set', {}, function (data) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.userinfo = data.userinfo;\n\t\t\t\t\n\t\t\t\t// 获取系统设置，检查是否开启了免登录和自动注册\n\t\t\t\tthat.member_auto_addlogin = data.member_auto_addlogin || 0;\n\t\t\t\tthat.member_auto_reg = data.member_auto_reg || 0;\n\t\t\t\t\n\t\t\t\t// 添加调试信息\n\t\t\t\tconsole.log('退出登录调试 - member_auto_addlogin:', that.member_auto_addlogin);\n\t\t\t\tconsole.log('退出登录调试 - member_auto_reg:', that.member_auto_reg);\n\t\t\t\tconsole.log('退出登录调试 - isAutoLoginEnabled:', that.isAutoLoginEnabled());\n\t\t\t\t\n\t\t\t\tthat.isload = true;\n\t\t\t});\n\t\t},\n\t\tuploadHeadimg:function(){\n\t\t\tvar that = this;\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tvar headimg = urls[0];\n\t\t\t\tthat.userinfo.headimg = headimg;\n\t\t\t\tapp.post('ApiMy/setfield',{headimg:headimg});\n\t\t\t},1)\n\t\t},\n\t\tdelaccount:function(){\n\t\t\tapp.confirm('注销账号后该账号下的所有数据都将删除并且无法恢复，确定要注销吗？',function(){\n\t\t\t\tapp.showLoading('注销中');\n\t\t\t\tapp.get('ApiMy/delaccount', {}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status == 1){\n\t\t\t\t\t\tapp.alert(data.msg,function(){\n\t\t\t\t\t\t\tapp.goto('/pages/index/index');\n\t\t\t\t\t\t});\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\t// 判断是否启用了自动登录（免登录）\n\t\tisAutoLoginEnabled: function() {\n\t\t\treturn this.member_auto_addlogin == 1;\n\t\t},\n\t\t\n\t\tlogout:function(){\n\t\t\tvar that = this;\n\t\t\t\n\t\t\t// 如果启用了免登录，显示提示\n\t\t\tif(this.isAutoLoginEnabled()) {\n\t\t\t\tapp.alert('系统已开启免登录功能，退出登录后将自动重新登录');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiIndex/logout', {}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif(data.status == 0){\n\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t  // 返回功能\n\t\t    goBack: function () {\n\t\t      uni.navigateBack({\n\t\t        delta: 1\n\t\t      });\n\t\t    },\n\t\t// 添加goto导航方法\n\t\tgoto: function(e) {\n\t\t\tvar url = '';\n\t\t\tif(typeof e === 'object') {\n\t\t\t\turl = e.currentTarget.dataset.url;\n\t\t\t} else {\n\t\t\t\turl = e;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否是pagesExa/my/目录下的页面，如果是相对路径则补全前缀\n\t\t\tif(url && !url.startsWith('/') && !url.includes('://')) {\n\t\t\t\turl = '/pagesExa/my/' + url;\n\t\t\t}\n\t\t\t\n\t\t\tapp.goto(url);\n\t\t}\n  }\n};\n</script>\n<style>\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}\n.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}\n.info-item:last-child{border:none}\n.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}\n.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}\n\n\n/* 返回按钮样式 */\n.back-button {\n  width: 100%;\n  background: #b60000;\n  color: #fff;\n  text-align: center;\n  height: 96rpx;\n  line-height: 96rpx;\n  border-radius: 50px;\n  margin-top: 20rpx;\n}\n\n.back-button .t1 {\n  font-size: 30rpx;\n  color: #fff;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./set.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./set.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115060369\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}