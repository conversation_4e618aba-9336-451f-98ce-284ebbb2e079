{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?88aa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?a501", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?6c37", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?0112", "uni-app:///pagesExt/tuozhanyuan/tuozhanteam.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?7d97", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/tuozhanteam.vue?5247"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "userlevel", "userinfo", "textset", "levelList", "keyword", "to<PERSON>d", "tomoney", "toscore", "nodata", "nomore", "dialogShow", "tempMid", "tempLevelid", "tempLevelsort", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "uni", "title", "changetab", "scrollTop", "duration", "givemoneyshow", "givescoreshow", "givemoney", "id", "money", "givescore", "score", "searchChange", "searchConfirm", "showDialog", "changeLevel", "mid", "levelId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmFpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAvB;QAAAE;QAAAK;MAAA;QACAe;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAE;YACAC;UACA;UACAH;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACAF;QACAG;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAP;MACAA;IACA;IACAQ;MACA;MACA;MACAR;MACAA;IACA;IACAS;MACA;MACA;MACAR;MACAA;QAAAS;QAAAC;MAAA;QACAV;QACA;UACAA;QACA;UACAA;UACAD;UACAA;QACA;MACA;IACA;IACAY;MACA;MACA;MACAX;MACAA;QAAAS;QAAAG;MAAA;QACAZ;QACA;UACAA;QACA;UACAA;UACAD;UACAA;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;MACA;MACAf;MACAA;IACA;IACAgB;MACA;MACAhB;MACAA;MACAA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACAhB;QACAA;QACAA;UAAAiB;UAAAC;QAAA;UACAlB;UACA;YACAA;UACA;YACAA;YACAD;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/tuozhanteam.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/tuozhanteam.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tuozhanteam.vue?vue&type=template&id=961ac6b6&\"\nvar renderjs\nimport script from \"./tuozhanteam.vue?vue&type=script&lang=js&\"\nexport * from \"./tuozhanteam.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tuozhanteam.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/tuozhanteam.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhanteam.vue?vue&type=template&id=961ac6b6&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.userlevel && _vm.userlevel.can_agent == 2\n      ? _vm.t(\"一级\")\n      : null\n  var m1 =\n    _vm.isload && _vm.userlevel && _vm.userlevel.can_agent == 2\n      ? _vm.t(\"二级\")\n      : null\n  var m2 =\n    _vm.isload && _vm.userlevel && _vm.userlevel.can_agent == 3\n      ? _vm.t(\"一级\")\n      : null\n  var m3 =\n    _vm.isload && _vm.userlevel && _vm.userlevel.can_agent == 3\n      ? _vm.t(\"二级\")\n      : null\n  var m4 =\n    _vm.isload && _vm.userlevel && _vm.userlevel.can_agent == 3\n      ? _vm.t(\"三级\")\n      : null\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m5 =\n            _vm.userlevel && _vm.userlevel.team_givescore == 1\n              ? _vm.t(\"额度\")\n              : null\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.dialogShow\n      ? _vm.__map(_vm.levelList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 =\n            item.id != _vm.tempLevelid && item.sort > _vm.tempLevelsort\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m6: m6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhanteam.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhanteam.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\n\t\t<!-- <dd-tab :itemdata=\"[t('一级')+'('+userinfo.myteamCount1+')',t('二级')+'('+userinfo.myteamCount2+')']\" :itemst=\"['1','2','3']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"userlevel && userlevel.can_agent==2\"></dd-tab>\n\t\t<dd-tab :itemdata=\"[t('一级')+'('+userinfo.myteamCount1+')',t('二级')+'('+userinfo.myteamCount2+')',t('三级')+'('+userinfo.myteamCount3+')']\" :itemst=\"['1','2','3']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"userlevel && userlevel.can_agent==3\"></dd-tab>\n\t\t -->\r\n\t\t\r\n\t\t<dd-tab :itemdata=\"[t('一级'),t('二级')]\" :itemst=\"['1','2','3']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"userlevel && userlevel.can_agent==2\"></dd-tab>\r\n\t\t<dd-tab :itemdata=\"[t('一级'),t('二级'),t('三级')]\" :itemst=\"['1','2','3']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"userlevel && userlevel.can_agent==3\"></dd-tab>\r\n\t\t\r\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入昵称/姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\n\t\t\t</view>\n\t\t</view>\r\n\t\t<view  class=\"x1\" @tap=\"givescoreshow\"  >添加成员</view>\n\t\t<view class=\"content\" v-if=\"datalist && datalist.length>0\">\n\t\t\t<view class=\"label\">\n\t\t\t\t<!-- <text class=\"t1\">成员信息 (有效人数 {{userinfo.teamnum || 0}} 人)</text> -->\r\n\t\t\t\t<text class=\"t1\">成员信息 </text>\n\t\t\t\t<text class=\"t2\">TA的额度</text>\n\t\t\t</view>\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"item\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<image :src=\"item.headimg\"></image>\n\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t<text class=\"x1\">{{item.nickname}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\">{{item.createtime}}</text>\n\t\t\t\t\t\t\t<text class=\"x2\">等级：事业部</text>\n\t\t\t\t\t\t\t<text class=\"x2\" v-if=\"item.tel\">手机号：{{item.tel}}</text>\r\n\t\t\t\t\t\t<!-- \t<text class=\"x2\">下级人数：{{item.downcount}}</text>\r\n\t\t\t\t\t\t\t<text class=\"x2\">剩余额度：需要代码</text> -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<text class=\"t1\">+0</text>\n\t\t\t\t\t\t<text class='t2'>{{item.downcount}}个商家</text>\n\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t<!-- \t<view v-if=\"userlevel && userlevel.team_givemoney==1\" class=\"x1\" @tap=\"givemoneyshow\" :data-id=\"item.id\">转{{t('余额')}}</view>\n\t\t\t\t\t\t\t<view v-if=\"userlevel && userlevel.team_givescore==1\" class=\"x1\" @tap=\"givescoreshow\" :data-id=\"item.id\" >转{{t('积分')}}</view> -->\r\n\t\t\t\t\t\t\t<view v-if=\"userlevel && userlevel.team_givescore==1\" class=\"x1\" @tap=\"givescoreshow\" :data-id=\"item.id\" >充{{t('额度')}}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"userlevel \" class=\"x1\" @tap=\"showDialog\" :data-id=\"item.id\" :data-levelid=\"item.levelid\" :data-levelsort=\"item.levelsort\">设为招商经理</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<uni-popup id=\"dialogmoneyInput\" ref=\"dialogmoneyInput\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"转账金额\" value=\"\" placeholder=\"请输入转账金额\" @confirm=\"givemoney\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"dialogscoreInput\" ref=\"dialogscoreInput\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"转账数量\" value=\"\" placeholder=\"请输入转账数量\" @confirm=\"givescore\"></uni-popup-dialog>\n\t\t</uni-popup>\r\n\t\t\r\n\t\t\r\n\t\t<view v-if=\"dialogShow\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"showDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">升级</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"showDialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sheet-item\" v-for=\"(item, index) in levelList\" :key=\"index\">\r\n\t\t\t\t\t\t<text class=\"item-text flex-item\">{{item.name}}</text>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view><view @tap=\"changeLevel\" :data-id=\"item.id\" :data-name=\"item.name\" v-if=\"item.id != tempLevelid && item.sort > tempLevelsort\" :style=\"{'color':t('color1')}\">选择</view><view v-else style=\"color: #ccc;\">选择</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      st: 1,\n      datalist: [],\n      pagenum: 1,\n\t\t\tuserlevel:{},\n\t\t\tuserinfo:{},\n\t\t\ttextset:{},\r\n\t\t\tlevelList:{},\n\t\t\tkeyword:'',\n\t\t\ttomid:'',\n\t\t\ttomoney:0,\n\t\t\ttoscore:0,\n      nodata: false,\n      nomore: false,\r\n\t\t\tdialogShow: false,\r\n\t\t\ttempMid: '',\r\n\t\t\ttempLevelid: '',\r\n\t\t\ttempLevelsort: '',\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1\n      this.getdata(true);\n    }\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n      app.post('ApiAgent/team', {st: st,pagenum: pagenum,keyword:keyword}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\t\tthat.userlevel = res.userlevel;\n\t\t\t\t\tthat.textset = app.globalData.textset;\n          that.datalist = data;\r\n\t\t\t\t\tthat.levelList = res.levelList;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('我的团队')\n\t\t\t\t\t});\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    changetab: function (st) {\n\t\t\tthis.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n\t\tgivemoneyshow:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tthat.tomid = id;\n\t\t\tthat.$refs.dialogmoneyInput.open();\n\t\t},\n\t\tgivescoreshow:function(e){\n\t\t\tvar that = this;\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tthat.tomid = id;\n\t\t\tthat.$refs.dialogscoreInput.open();\n\t\t},\n\t\tgivemoney:function(done, money){\n\t\t\tvar that = this;\n\t\t\tvar id = that.tomid;\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAgent/givemoney', {id:id,money:money}, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 0) {\n          app.error(res.msg);\n        } else {\n          app.success(res.msg);\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tthat.$refs.dialogmoneyInput.close();\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tgivescore:function(done, score){\n\t\t\tvar that = this;\n\t\t\tvar id = that.tomid;\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAgent/givescore', {id:id,score:score}, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif (res.status == 0) {\n          app.error(res.msg);\n        } else {\n          app.success(res.msg);\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tthat.$refs.dialogscoreInput.close();\n\t\t\t\t}\n\t\t\t})\n\t\t},\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword;\n      that.getdata();\n    },\r\n\t\tshowDialog:function(e){\r\n\t\t\tlet that = this;\r\n\t\t\tthat.tempMid = e.currentTarget.dataset.id;\r\n\t\t\tthat.tempLevelid = e.currentTarget.dataset.levelid;\r\n\t\t\tthat.tempLevelsort = e.currentTarget.dataset.levelsort;\r\n\t\t\tthis.dialogShow = !this.dialogShow\r\n\t\t},\r\n\t\tchangeLevel: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar mid = that.tempMid;\r\n\t\t\tvar levelId = e.currentTarget.dataset.id;\r\n\t\t\tvar levelName = e.currentTarget.dataset.name;\r\n\t\t\tapp.confirm('确定要升级为'+levelName+'吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t  app.post('ApiAgent/levelUp', {mid: mid,levelId:levelId}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t  app.error(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.dialogShow = false;\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}\r\n\t\t\t  });\r\n\t\t\t});\n    },\n  }\n};\r\n</script>\r\n<style>\n\n.topsearch{width:94%;margin:16rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n\r\n.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}\r\n.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}\r\n.content .label .t1{flex:1}\r\n.content .label .t2{ width:300rpx;text-align:right}\r\n\r\n.content .item{width: 100%;padding: 32rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}\r\n.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}\r\n.content .item .f1{display:flex;flex:1;align-items:center;}\r\n.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}\r\n.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}\r\n.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}\r\n\r\n.content .item .f2{display:flex;flex-direction:column;width:200rpx;text-align:right;border-left:1px solid #eee}\r\n.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}\r\n.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}\r\n.content .item .f2 .t3{ display:flex;justify-content:space-around;margin-top:10rpx; flex-wrap: wrap;}\n.content .item .f2 .t3 .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-top: 10rpx;}\r\n\r\n\t.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}\r\n\t.sheet-item .item-img {width: 44rpx;height: 44rpx;}\r\n\t.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}\r\n\t.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}\r\n\t.man-btn {\n\t\tline-height: 100rpx;\n\t\ttext-align: center;\n\t\tbackground: #FFFFFF;\n\t\tfont-size: 30rpx;\n\t\tcolor: #FF4015;\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhanteam.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tuozhanteam.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103509\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}