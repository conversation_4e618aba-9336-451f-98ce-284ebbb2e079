{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?d4b6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?fa83", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?1d35", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?77cc", "uni-app:///pagesExa/tuanzhangadmin/index/setinfo.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?a1f8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/setinfo.vue?40a8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "pic", "pics", "info", "latitude", "longitude", "address", "start_hours", "end_hours", "start_hours2", "end_hours2", "start_hours3", "end_hours3", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "locationSelect", "success", "bindStartHoursChange", "bindEndHoursChange", "bindStartHours2Change", "bindEndHours2Change", "bindStartHours3Change", "bindEndHours3Change", "subform", "setTimeout", "isagreeChange", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsGhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;QACA;QACA;QACA;UACArB;QACA;QACA;QACA;UACAG;QACA;UACAA;QACA;QACAe;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;MACAH;QACAI;UACAN;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IAEAO;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAZ;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACAf;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MAEAe;MACAA;QAAAf;MAAA;QACAe;QACA;UACAA;UACAa;YACAb;UACA;QACA;UACAA;QACA;MACA;IACA;IACAc;MACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAlB;QACA;UACAhB;QACA;QACA;QACA;QACA;MACA;IACA;IACAmC;MACA;MACA;MACA;MACA;QACA;QACAnC;QACAe;MACA;QACA;QACAf;QACAe;MACA;QACA;QACAf;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzTA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/index/setinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/index/setinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setinfo.vue?vue&type=template&id=37f04cbc&\"\nvar renderjs\nimport script from \"./setinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./setinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setinfo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/index/setinfo.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinfo.vue?vue&type=template&id=37f04cbc&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pic.length : null\n  var g1 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g2 = _vm.isload ? _vm.pics.length : null\n  var g3 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinfo.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>商家名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" :disabled=\"true\" placeholder=\"请输入商家名称\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>商家描述<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\" placeholder=\"请输入商家描述\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"apply_item\">\r\n\t\t\t\t\t<view>店铺坐标<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\" :value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>店铺地址<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"address\" :value=\"address\" placeholder=\"请输入商家详细地址\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>客服电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\" style=\"line-height:50rpx\"><textarea name=\"content\" placeholder=\"请输入商家简介\" :value=\"info.content\"></textarea></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家主图<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"logo\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家照片(3-5张)<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t<!-- \t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>营业时间<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t\t<view class=\"flex-col\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours\" @change=\"bindStartHoursChange\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours\" @change=\"bindEndHoursChange\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours2\" @change=\"bindStartHours2Change\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours2}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours2\" @change=\"bindEndHours2Change\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours2}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<picker mode=\"time\" :value=\"start_hours3\" @change=\"bindStartHours3Change\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{start_hours3}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t\t <view style=\"padding:0 30rpx;color:#222;\">到</view>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours3\" @change=\"bindEndHours3Change\">\r\n\t\t\t\t\t\t\t <view class=\"picker\">{{end_hours3}}</view>\r\n\t\t\t\t\t\t </picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t<text>店铺状态<text style=\"color:red\"> *</text></text>\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"is_open\" @change=\"bindStatusChange\">\r\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.is_open==1?true:false\"></radio> 开启</label> \r\n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"info.is_open==0?true:false\"></radio> 关闭</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view style=\"padding:30rpx 0\"><button form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">确 定</button></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      cateArr: [],\r\n      cindex: 0,\r\n\t\t\tpic:[],\r\n\t\t\tpics:[],\r\n      info: {},\r\n      latitude: '',\r\n      longitude: '',\r\n      address:'',\r\n\t\t\tstart_hours:'00:00',\r\n\t\t\tend_hours:'00:00',\r\n\t\t\tstart_hours2:'00:00',\r\n\t\t\tend_hours2:'00:00',\r\n\t\t\tstart_hours3:'00:00',\r\n\t\t\tend_hours3:'00:00',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApituanzhangAdminIndex/setinfo', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\tapp.alert(res.msg, function () {\r\n\t\t\t\t\t\tapp.goto('/admin/index/index', 'redirect');\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.title\r\n\t\t\t\t});\r\n\t\t\t\tvar clist = res.clist;\r\n\t\t\t\tvar cateArr = [];\r\n\t\t\t\tfor (var i in clist) {\r\n\t\t\t\t\tcateArr.push(clist[i].name);\r\n\t\t\t\t}\r\n\t\t\t\tvar pics = res.info ? res.info.pics : '';\r\n\t\t\t\tif (pics) {\r\n\t\t\t\t\tpics = pics.split(',');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tpics = [];\r\n\t\t\t\t}\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tthat.address = res.info.address;\r\n\t\t\t\tthat.latitude = res.info.latitude;\r\n\t\t\t\tthat.longitude = res.info.longitude;\r\n\t\t\t\tthat.cateArr = cateArr;\r\n\t\t\t\tthat.pic = res.info.logo ? [res.info.logo] : [];\r\n\t\t\t\tthat.start_hours = res.info.start_hours ? res.info.start_hours : '00:00';\r\n\t\t\t\tthat.end_hours = res.info.end_hours ? res.info.end_hours : '00:00';\r\n\t\t\t\tthat.start_hours2 = res.info.start_hours2 ? res.info.start_hours2 : '00:00';\r\n\t\t\t\tthat.end_hours2 = res.info.end_hours2 ? res.info.end_hours2 : '00:00';\r\n\t\t\t\tthat.start_hours3 = res.info.start_hours3 ? res.info.start_hours3 : '00:00';\r\n\t\t\t\tthat.end_hours3 = res.info.end_hours3 ? res.info.end_hours3 : '00:00';\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    cateChange: function (e) {\r\n      this.cindex = e.detail.value;\r\n    },\r\n    locationSelect: function () {\r\n      var that = this;\r\n      uni.chooseLocation({\r\n        success: function (res) {\r\n          that.info.address = res.name;\r\n\t\t\t\t\tthat.info.latitude = res.latitude;\r\n          that.info.longitude = res.longitude;\r\n          that.address = res.name;\r\n          that.latitude = res.latitude;\r\n          that.longitude = res.longitude;\r\n        }\r\n      });\r\n    },\r\n\t\t\r\n\t\tbindStartHoursChange:function(e){\r\n\t\t\tthis.start_hours = e.target.value\r\n\t\t},\r\n\t\tbindEndHoursChange:function(e){\r\n\t\t\tthis.end_hours = e.target.value\r\n\t\t},\r\n\t\t\r\n\t\tbindStartHours2Change:function(e){\r\n\t\t\tthis.start_hours2 = e.target.value\r\n\t\t},\r\n\t\tbindEndHours2Change:function(e){\r\n\t\t\tthis.end_hours2 = e.target.value\r\n\t\t},\r\n\t\t\r\n\t\tbindStartHours3Change:function(e){\r\n\t\t\tthis.start_hours3 = e.target.value\r\n\t\t},\r\n\t\tbindEndHours3Change:function(e){\r\n\t\t\tthis.end_hours3 = e.target.value\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var info = e.detail.value;\r\n      if (info.tel == '') {\r\n        app.error('请填写客服电话');\r\n        return false;\r\n      }\r\n      // if (info.zuobiao == '') {\r\n      //   app.error('请选择店铺坐标');\r\n      //   return false;\r\n      // }\r\n      // if (info.address == '') {\r\n      //   app.error('请填写店铺地址');\r\n      //   return false;\r\n      // }\r\n      if (info.pic == '') {\r\n        app.error('请上传商家主图');\r\n        return false;\r\n      }\r\n      if (info.pics == '') {\r\n        app.error('请上传商家照片');\r\n        return false;\r\n      }\r\n      info.address = that.address;\r\n      info.latitude = that.latitude;\r\n      info.longitude = that.longitude;\r\n\t\t\tinfo.start_hours = that.start_hours ? that.start_hours : '00:00';\r\n\t\t\tinfo.end_hours = that.end_hours ? that.end_hours : '00:00';\r\n\t\t\tinfo.start_hours2 = that.start_hours2 ? that.start_hours2 : '00:00';\r\n\t\t\tinfo.end_hours2 = that.end_hours2 ? that.end_hours2 : '00:00';\r\n\t\t\tinfo.start_hours3 = that.start_hours3 ? that.start_hours3 : '00:00';\r\n\t\t\tinfo.end_hours3 = that.end_hours3 ? that.end_hours3 : '00:00';\r\n\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post(\"ApituanzhangAdminIndex/setinfo\", {info: info}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (res.status == 1) {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('index');\r\n          }, 1000);\r\n        } else {\r\n          app.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n    isagreeChange: function (e) {\r\n      console.log(e.detail.value);\r\n      var val = e.detail.value;\r\n      if (val.length > 0) {\r\n        this.isagree = true;\r\n      } else {\r\n        this.isagree = false;\r\n      }\r\n    },\r\n    showxieyiFun: function () {\r\n      this.showxieyi = true;\r\n    },\r\n    hidexieyi: function () {\r\n      this.showxieyi = false;\r\n\t\t\tthis.isagree = true;\r\n    },\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t\tif(field == 'zhengming') that.zhengming = pics;\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'pic'){\r\n\t\t\t\tvar pics = that.pic\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pic = pics;\r\n\t\t\t}else if(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}else if(field == 'zhengming'){\r\n\t\t\t\tvar pics = that.zhengming\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.zhengming = pics;\r\n\t\t\t}\r\n\t\t},\r\n  }\r\n}\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.apply_title { background: #fff}\r\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n\r\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.apply_box .apply_item:last-child{ border:none}\r\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.apply_item input::placeholder{ color:#999999}\r\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\r\n.layui-imgbox-close image{width:100%;height:100%}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinfo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinfo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115055533\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}