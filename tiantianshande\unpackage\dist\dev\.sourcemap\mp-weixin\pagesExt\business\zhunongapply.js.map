{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?210c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?e7ef", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?e9e9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?eef6", "uni-app:///pagesExt/business/zhunongapply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?f424", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/zhunongapply.vue?02db"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "rateArr", "rateIndex", "selectedRate", "isagree", "<PERSON><PERSON><PERSON><PERSON>", "pic", "pics", "zhengming", "info", "bset", "latitude", "longitude", "address", "withdrawMethods", "withdrawMethodIndex", "selectedWithdrawMethod", "conditions", "hasGroup", "hasOnlineShop", "hasOfflineShop", "groupPics", "licensePics", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "rateChange", "withdrawMethodChange", "locationSelect", "success", "subform", "selectedConditions", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "goBack", "delta", "removeimg", "conditionChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuLrxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;QACA;QACA;QACA;UACA/B;QACA;QACA;QACA;QACA;QACA;UACAE;QACA;QACA0B;QACAA;QAEA;QACA;UACApB;QACA;UACAA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACAmB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;UACAA;YACAT;YACAC;YACAC;UACA;QACA;;QAEA;QACAO;QACAA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAL;QACAM;UACAR;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAS;MACA;MACA;;MAEA;MACA;QACA3B;MACA;;MAEA;MACA;QACAmB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;;MAIA;MACAnB;MACAA;MACAA;;MAEA;MACAA;MACA;QACAmB;QACA;MACA;;MAEA;MACAnB;MACA;QACAA;QACAA;MACA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;;MAEA;MACA;QACAmB;QACA;MACA;;MAEA;MACA;MACA;QACA;UACAA;UACA;QACA;QACAS;QACA5B;MACA;MAEA;QACA;UACAmB;UACA;QACA;QACAS;MACA;MAEA;QACA;UACAT;UACA;QACA;QACA;UACAA;UACA;QACA;QACAS;QACA5B;MACA;MAGAA;;MAEA;MACAmB;MACAA;QAAAnB;MAAA;QACAmB;QACA;UACAA;UACAU;YACA;cACAV;YACA;cACAA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEAW;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAd;QACA;UACArB;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAoC;MACAd;QACAe;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAtC;QACAoB;MACA;QACA;QACApB;QACAoB;MACA;QACA;UACA;UACApB;UACAoB;QACA;UACA;UACApB;UACAoB;QACA;UACA;UACApB;UACAoB;QACA;MACA;IACA;IACAmB;MACA;MACA;QACA5B;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7hBA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/zhunongapply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/zhunongapply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./zhunongapply.vue?vue&type=template&id=5ab1f278&\"\nvar renderjs\nimport script from \"./zhunongapply.vue?vue&type=script&lang=js&\"\nexport * from \"./zhunongapply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./zhunongapply.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/zhunongapply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhunongapply.vue?vue&type=template&id=5ab1f278&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pic.length : null\n  var g1 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g2 = _vm.isload ? _vm.pics.length : null\n  var g3 = _vm.isload ? _vm.pics.join(\",\") : null\n  var g4 = _vm.isload ? _vm.zhengming.join(\",\") : null\n  var m0 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhunongapply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhunongapply.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n      <block v-if=\"isload\">\r\n          <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==2\">\r\n              <parse :content=\"bset.verify_reject || '审核未通过：'\"/>{{info.reason}}，请修改后重新提交\r\n          </view>\r\n          <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==0\">\r\n              <parse :content=\"bset.verify_notice || '您的入驻申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'\"/>\r\n          </view>\r\n          <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==1\">\r\n              <parse :content=\"bset.verify_success || '恭喜您审核通过！'\"/>\r\n          </view>\r\n          <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n              <parse :content=\"bset.verify_normal || '温馨提示：审核通过后需缴纳保证金可完成入驻'\"/>\r\n          </view>\r\n  \r\n          <form @submit=\"subform\">\r\n              <view class=\"apply_box\">\r\n                  \r\n                  <view class=\"apply_item\">\r\n                      <view>联系人姓名<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"linkman\" :value=\"info.linkman\" placeholder=\"请填写姓名\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <view>联系人电话<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"linktel\" :value=\"info.linktel\" placeholder=\"请填写手机号码\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <view>联系人微信<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"wxid\" :value=\"info.wxid\" placeholder=\"请填写微信号\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\" v-if=\"info.zhanshi == 1\">\r\n                      <view>拓展人邀请码</view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"tuozhanid\" :value=\"info.tuozhanid\" placeholder=\"请填写拓展员邀请码\"></input></view>\r\n                  </view>\r\n              </view>\r\n              \r\n              <view class=\"apply_box\">\r\n                  <view class=\"apply_item\">\r\n                      <view>商家名称<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请输入商家名称\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <view>商家描述<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\" placeholder=\"请输入商家描述\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <view>主营类目<text style=\"color:red\"> *</text></view>\r\n                      <view>\r\n                          <picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\">\r\n                              <view class=\"picker\">{{cateArr[cindex]}}</view>\r\n                          </picker>\r\n                      </view>\r\n                  </view>\r\n                    <!-- 费率选择 -->\r\n   \r\n              <view class=\"apply_item\">\r\n                <view>商户费率<text style=\"color:red\"> *</text></view>\r\n                <view>\r\n                  <picker @change=\"rateChange\" :value=\"rateIndex\" :range=\"rateArr\">\r\n                    <view class=\"picker\">{{ rateArr[rateIndex] }}</view>\r\n                  </picker>\r\n                </view>\r\n              </view>\r\n  \r\n                  <view class=\"apply_item\">\r\n                      <view>店铺坐标<text style=\"color:red\"> </text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\" :value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <view>店铺地址<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"address\" :value=\"address\" placeholder=\"请输入商家详细地址\"></input></view>\r\n                  </view>\r\n                  <!-- 在店铺地址选项后面添加 -->\r\n<view class=\"apply_item\">\r\n  <view>生产地址</view>\r\n  <view class=\"flex-y-center\">\r\n      <input type=\"text\" name=\"production_address\" :value=\"info.production_address\" placeholder=\"请输入生产地址(选填)\"></input>\r\n  </view>\r\n</view>\r\n\r\n<view class=\"apply_item\">\r\n  <view>实体店地址</view>\r\n  <view class=\"flex-y-center\">\r\n      <input type=\"text\" name=\"shop_address\" :value=\"info.shop_address\" placeholder=\"请输入实体店地址(选填)\"></input>\r\n  </view>\r\n</view>\r\n\r\n<view class=\"apply_item\">\r\n  <view>线上链接</view>\r\n  <view class=\"flex-y-center\">\r\n      <input type=\"text\" name=\"shop_url\" :value=\"info.shop_url\" placeholder=\"请输入线上店铺链接(选填)\"></input>\r\n  </view>\r\n</view>\r\n                  <input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\r\n                  <input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\r\n                  <!-- <view class=\"apply_item\">\r\n                      <view>客服电话<text style=\"color:red\"> *</text></view>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\r\n                  </view> -->\r\n                  <view class=\"apply_item\" style=\"line-height:50rpx\"><textarea name=\"content\" placeholder=\"请输入商家简介\" :value=\"info.content\"></textarea></view>\r\n              </view>\r\n              <view class=\"apply_box\">\r\n                  <view class=\"apply_item\" style=\"border-bottom:0\"><text>商家头像<text style=\"color:red\"> *</text></text></view>\r\n                  <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                      <view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n                          <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                          <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                      </view>\r\n                      <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n                  </view>\r\n                  <input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n              </view>\r\n              <view class=\"apply_box\">\r\n                  <view class=\"apply_item\" style=\"border-bottom:0\"><text>主要产品图片(3-5张)<text style=\"color:red\"> *</text></text></view>\r\n                  <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                      <view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n                          <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                          <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                      </view>\r\n                      <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n                  </view>\r\n                  <input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n              </view>\r\n              <view class=\"apply_box\">\r\n                  <view class=\"apply_item\" style=\"border-bottom:0\"><text>商家身份证正反面，营业执照，及相关资质<text style=\"color:red\"> *</text></text></view>\r\n                  <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                      <view v-for=\"(item, index) in zhengming\" :key=\"index\" class=\"layui-imgbox\">\r\n                          <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"zhengming\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                          <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                      </view>\r\n                      <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"zhengming\"></view>\r\n                  </view>\r\n                  <input type=\"text\" hidden=\"true\" name=\"zhengming\" :value=\"zhengming.join(',')\" maxlength=\"-1\"></input>\r\n              </view>\r\n              \r\n            \r\n            <!-- 新增提现方式部分 -->\r\n            \r\n              <view class=\"apply_box\">\r\n                  <view class=\"apply_item\">\r\n                      <text>登录用户名<text style=\"color:red\"> *</text></text>\r\n                      <view class=\"flex-y-center\"><input type=\"text\" name=\"un\" :value=\"info.un\" placeholder=\"请填写登录账号\" autocomplete=\"off\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <text>登录密码<text style=\"color:red\"> *</text></text>\r\n                      <view class=\"flex-y-center\"><input type=\"password\" name=\"pwd\" :value=\"info.pwd\" placeholder=\"请填写登录密码\" autocomplete=\"off\"></input></view>\r\n                  </view>\r\n                  <view class=\"apply_item\">\r\n                      <text>确认密码<text style=\"color:red\"> *</text></text>\r\n                      <view class=\"flex-y-center\"><input type=\"password\" name=\"repwd\" :value=\"info.repwd\" placeholder=\"请再次填写密码\"></input></view>\r\n                  </view>\r\n              </view>\r\n              <block v-if=\"bset.xieyi_show==1\">\r\n              <view class=\"flex-y-center\" style=\"margin-left:20rpx;color:#999\" v-if=\"!info.id || info.status==2\">\r\n                  <checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox value=\"1\" :checked=\"isagree\"></checkbox>阅读并同意</label></checkbox-group>\r\n                  <text style=\"color:#666\" @tap=\"showxieyiFun\">《商户入驻协议》</text>\r\n              </view>\r\n              </block>\r\n              <view style=\"padding:30rpx 0\"><button v-if=\"!info.id || info.status==2\" form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\r\n  </view>\r\n          </form>\r\n          <!-- 底部返回按钮 -->\r\n             <view class=\"content\">\r\n               <view class=\"back-button\" @tap=\"goBack\">\r\n                 <text class=\"t1\">返回</text>\r\n               </view>\r\n             </view>\r\n          <view id=\"xieyi\" :hidden=\"!showxieyi\" style=\"width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)\">\r\n              <view style=\"width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px\">\r\n                  <view style=\"overflow:scroll;height:100%;\">\r\n                      <parse :content=\"bset.xieyi\"/>\r\n                  </view>\r\n                  <view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center\" @tap=\"hidexieyi\">已阅读并同意</view>\r\n              </view>\r\n          </view>\r\n      </block>\r\n      <loading v-if=\"loading\"></loading>\r\n      <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n      <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n  </template>\r\n  <script>\r\n  var app = getApp();\r\n  \r\n  export default {\r\n    data() {\r\n      return {\r\n        opt: {},\r\n        loading: false,\r\n        isload: false,\r\n        menuindex: -1,\r\n        pre_url: app.globalData.pre_url,\r\n        datalist: [],\r\n        pagenum: 1,\r\n        cateArr: [],\r\n        cindex: 0,\r\n        rateArr: [], // 商户费率名称数组\r\n        rateIndex: 0, // 当前选中的费率索引\r\n        selectedRate: null, // 当前选中的费率对象\r\n        isagree: false,\r\n        showxieyi: false,\r\n        pic: [],\r\n        pics: [],\r\n        zhengming: [],\r\n        info: {},\r\n        bset: {},\r\n        latitude: '',\r\n        longitude: '',\r\n        address: '',\r\n        withdrawMethods: ['支付宝', '银行卡'],\r\n        withdrawMethodIndex: 1,\r\n        selectedWithdrawMethod: '银行卡',\r\n        conditions: {\r\n          hasGroup: false,\r\n          hasOnlineShop: false,\r\n          hasOfflineShop: false\r\n        },\r\n        groupPics: [],\r\n        licensePics: [],\r\n      };\r\n    },\r\n  \r\n    onLoad: function (opt) {\r\n      this.opt = app.getopts(opt);\r\n      this.getdata();\r\n    },\r\n    onPullDownRefresh: function () {\r\n      this.getdata();\r\n    },\r\n    methods: {\r\n      getdata: function () {\r\n        var that = this;\r\n        that.loading = true;\r\n        app.get('ApiBusiness/apply', {}, function (res) {\r\n          that.loading = false;\r\n          if (res.status == 2) {\r\n            app.alert(res.msg, function () {\r\n              app.goto('/admin/index/index', 'redirect');\r\n            });\r\n            return;\r\n          }\r\n          uni.setNavigationBarTitle({\r\n            title: res.title\r\n          });\r\n          var clist = res.clist;\r\n          var cateArr = [];\r\n          for (var i in clist) {\r\n            cateArr.push(clist[i].name);\r\n          }\r\n           // 处理费率数据，模仿分类的处理方式\r\n                  var feilv = res.feilv || [];\r\n                  var rateArr = [];\r\n                  for (var i in feilv) {\r\n                    rateArr.push(feilv[i].name);\r\n                  }\r\n                  that.feilv = feilv;\r\n                  that.rateArr = rateArr;\r\n                  \r\n          var pics = res.info ? res.info.pics : '';\r\n          if (pics) {\r\n            pics = pics.split(',');\r\n          } else {\r\n            pics = [];\r\n          }\r\n          var zhengming = res.info ? res.info.zhengming : '';\r\n          if (zhengming) {\r\n            zhengming = zhengming.split(',');\r\n          } else {\r\n            zhengming = [];\r\n          }\r\n          that.clist = res.clist;\r\n          that.bset = res.bset;\r\n          that.info = res.info;\r\n          that.address = res.info.address;\r\n          that.latitude = res.info.latitude;\r\n          that.longitude = res.info.longitude;\r\n          that.cateArr = cateArr;\r\n          that.pic = res.info.logo ? [res.info.logo] : [];\r\n          that.pics = pics;\r\n          that.zhengming = zhengming;\r\n          that.loaded();\r\n          \r\n          // 初始化条件相关数据\r\n          if(res.info && res.info.conditions) {\r\n            const conditions = !empty(res.info.conditions) ? res.info.conditions.split(',') : [];\r\n            that.conditions = {\r\n              hasGroup: conditions.includes('1'),\r\n              hasOnlineShop: conditions.includes('2'),\r\n              hasOfflineShop: conditions.includes('3')\r\n            };\r\n          }\r\n          \r\n          // 初始化图片数据\r\n          that.groupPics = res.info.group_pics ? res.info.group_pics.split(',') : [];\r\n          that.licensePics = res.info.license_pics ? res.info.license_pics.split(',') : [];\r\n        });\r\n      },\r\n      cateChange: function (e) {\r\n        this.cindex = e.detail.value;\r\n      },\r\n        // 费率选择事件处理\r\n          rateChange: function (e) {\r\n            this.rateIndex = e.detail.value;\r\n            this.selectedRate = this.feilv[this.rateIndex]; // 更新选中的费率对象\r\n          },\r\n      withdrawMethodChange: function (e) {\r\n        const index = e.detail.value;\r\n        this.withdrawMethodIndex = index;\r\n        this.selectedWithdrawMethod = this.withdrawMethods[index];\r\n      },\r\n      locationSelect: function () {\r\n        var that = this;\r\n        uni.chooseLocation({\r\n          success: function (res) {\r\n            that.info.address = res.name;\r\n            that.info.latitude = res.latitude;\r\n            that.info.longitude = res.longitude;\r\n            that.address = res.name;\r\n            that.latitude = res.latitude;\r\n            that.longitude = res.longitude;\r\n          }\r\n        });\r\n      },\r\n      subform: function (e) {\r\n        var that = this;\r\n        var info = e.detail.value;\r\n      \r\n        // 如果存在 info.id，说明是修改操作\r\n        if(that.info && that.info.id) {\r\n            info.id = that.info.id;  // 添加 id 字段\r\n        }\r\n      \r\n        // 验证所有必填项\r\n        if (info.linkman == '') {\r\n          app.error('请填写联系人姓名');\r\n          return false;\r\n        }\r\n        if (info.linktel == '') {\r\n          app.error('请填写联系人电话');\r\n          return false;\r\n        }\r\n        if (info.tel == '') {\r\n          app.error('请填写客服电话');\r\n          return false;\r\n        }\r\n        if (info.name == '') {\r\n          app.error('请填写商家名称');\r\n          return false;\r\n        }\r\n        if (info.address == '') {\r\n          app.error('请填写店铺地址');\r\n          return false;\r\n        }\r\n        if (info.pic == '') {\r\n          app.error('请上传商家头像');\r\n          return false;\r\n        }\r\n        if (info.pics == '') {\r\n          app.error('请上传商家照片');\r\n          return false;\r\n        }\r\n        if (info.zhengming == '') {\r\n          app.error('请上传证明材料');\r\n          return false;\r\n        }\r\n        if (info.un == '') {\r\n          app.error('请填写登录账号');\r\n          return false;\r\n        }\r\n        if (info.pwd == '') {\r\n          app.error('请填写登录密码');\r\n          return false;\r\n        }\r\n        if (info.pwd.length < 6) {\r\n          app.error('密码不能小于6位');\r\n          return false;\r\n        }\r\n        if (info.repwd != info.pwd) {\r\n          app.error('两次输入的密码不一致');\r\n          return false;\r\n        }\r\n      \r\n       \r\n      \r\n        // 赋值地址、纬度和经度\r\n        info.address = that.address;\r\n        info.latitude = that.latitude;\r\n        info.longitude = that.longitude;\r\n      \r\n        // 确保商户费率已选择并赋值\r\n        info.rate_id = that.feilv[that.rateIndex] ? that.feilv[that.rateIndex].id : null;\r\n        if (!info.rate_id) {\r\n          app.error('请选择商户费率');\r\n          return false;\r\n        }\r\n      \r\n        // 将提现方式和相关字段添加到 info 对象\r\n        info.withdrawMethod = that.selectedWithdrawMethod;\r\n        if (that.selectedWithdrawMethod === '支付宝') {\r\n          info.alipayName = that.info.alipayName;\r\n          info.alipayAccount = that.info.alipayAccount;\r\n        } else if (that.selectedWithdrawMethod === '银行卡') {\r\n          info.bankcarduser = that.info.bankcarduser;\r\n          info.bankCardAccount = that.info.bankCardAccount;\r\n          info.bankName = that.info.bankName;\r\n        } else if (that.selectedWithdrawMethod === '三方支付') {\r\n          info.jialianMerchantNumber = that.info.jialianMerchantNumber;\r\n          info.jialianTerminalNumber = that.info.jialianTerminalNumber;\r\n        }\r\n      \r\n        // 检查是否同意协议（如果需要）\r\n        if (that.bset.xieyi_show == 1 && !that.isagree) {\r\n          app.error('请先阅读并同意商户入驻协议');\r\n          return false;\r\n        }\r\n      \r\n        // 处理申请条件\r\n        const selectedConditions = [];\r\n        if(that.conditions.hasGroup) {\r\n          if(!that.groupPics.length) {\r\n            app.error('请上传微信群截图');\r\n            return false;\r\n          }\r\n          selectedConditions.push('1');\r\n          info.group_pics = that.groupPics.join(',');\r\n        }\r\n        \r\n        if(that.conditions.hasOnlineShop) {\r\n          if(!info.shopUrl) {\r\n            app.error('请填写线上店铺链接');\r\n            return false;\r\n          }\r\n          selectedConditions.push('2');\r\n        }\r\n        \r\n        if(that.conditions.hasOfflineShop) {\r\n          if(!info.shopAddress) {\r\n            app.error('请填写线下店铺地址');\r\n            return false;\r\n          }\r\n          if(!that.licensePics.length) {\r\n            app.error('请上传营业执照');\r\n            return false;\r\n          }\r\n          selectedConditions.push('3');\r\n          info.license_pics = that.licensePics.join(',');\r\n        }\r\n\r\n\r\n        info.conditions = selectedConditions.join(',');\r\n      \r\n        // 提交表单数据到服务器\r\n        app.showLoading('提交中');\r\n        app.post(\"ApiBusiness/apply\", { info: info }, function (res) {\r\n          app.showLoading(false);\r\n          if (res.status == 1) {\r\n            app.success(res.msg);\r\n            setTimeout(function () {\r\n              if (res.after_register_url) {\r\n                app.goto(res.after_register_url);\r\n              } else {\r\n                app.goto(app.globalData.indexurl);\r\n              }\r\n            }, 1500);\r\n          } else {\r\n            app.error(res.msg);\r\n          }\r\n        });\r\n      },\r\n  \r\n      isagreeChange: function (e) {\r\n        var val = e.detail.value;\r\n        this.isagree = val.length > 0;\r\n      },\r\n      showxieyiFun: function () {\r\n        this.showxieyi = true;\r\n      },\r\n      hidexieyi: function () {\r\n        this.showxieyi = false;\r\n        this.isagree = true;\r\n      },\r\n      uploadimg: function (e) {\r\n        var that = this;\r\n        var field = e.currentTarget.dataset.field;\r\n        var pics = that[field];\r\n        if(!pics) pics = [];\r\n        app.chooseImage(function(urls) {\r\n          for(var i=0; i<urls.length; i++) {\r\n            pics.push(urls[i]);\r\n          }\r\n          if(field == 'pic') that.pic = pics;\r\n          if(field == 'pics') that.pics = pics;\r\n          if(field == 'zhengming') that.zhengming = pics;\r\n          if(field == 'groupPics') that.groupPics = pics;\r\n          if(field == 'licensePics') that.licensePics = pics;\r\n        }, 1);\r\n      },\r\n      // 返回功能\r\n        goBack: function () {\r\n          uni.navigateBack({\r\n            delta: 1\r\n          });\r\n        },\r\n      removeimg: function (e) {\r\n        var that = this;\r\n        var index = e.currentTarget.dataset.index;\r\n        var field = e.currentTarget.dataset.field;\r\n        if(field == 'groupPics') {\r\n          var pics = that.groupPics;\r\n          pics.splice(index, 1);\r\n          that.groupPics = pics;\r\n        } else if(field == 'licensePics') {\r\n          var pics = that.licensePics;\r\n          pics.splice(index, 1);\r\n          that.licensePics = pics;\r\n        } else {\r\n          if (field == 'pic') {\r\n            var pics = that.pic;\r\n            pics.splice(index, 1);\r\n            that.pic = pics;\r\n          } else if (field == 'pics') {\r\n            var pics = that.pics;\r\n            pics.splice(index, 1);\r\n            that.pics = pics;\r\n          } else if (field == 'zhengming') {\r\n            var pics = that.zhengming;\r\n            pics.splice(index, 1);\r\n            that.zhengming = pics;\r\n          }\r\n        }\r\n      },\r\n      conditionChange(e) {\r\n        const values = e.detail.value;\r\n        this.conditions = {\r\n          hasGroup: values.includes('1'),\r\n          hasOnlineShop: values.includes('2'),\r\n          hasOfflineShop: values.includes('3')\r\n        };\r\n      },\r\n    }\r\n  };\r\n  </script>\r\n  \r\n  <style>\r\n  radio{transform: scale(0.6);}\r\n  checkbox{transform: scale(0.6);}\r\n  .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n  .apply_title { background: #fff}\r\n  .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n  .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n  \r\n  .apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n  .apply_box .apply_item:last-child{ border:none}\r\n  .apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n  .apply_item input::placeholder{ color:#999999}\r\n  .apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n  .apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n  .apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n  .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n  \r\n  .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n  .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\r\n  .layui-imgbox-close image{width:100%;height:100%}\r\n  .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n  .layui-imgbox-img>image{max-width:100%;}\r\n  .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n  .uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n  \r\n  /* 返回按钮样式 */\r\n  .back-button {\r\n    width: 90%;\r\n    background: #b60000;\r\n    color: #fff;\r\n    text-align: center;\r\n    height: 96rpx;\r\n    line-height: 96rpx;\r\n    border-radius: 50px;\r\n    margin-top: 0rpx;\r\n    margin: auto;\r\n  }\r\n  \r\n  .back-button .t1 {\r\n    font-size: 30rpx;\r\n    color: #fff;\r\n  }\r\n  \r\n  .condition-item {\r\n    padding: 20rpx 0;\r\n    border-bottom: 1px solid #eee;\r\n  }\r\n  \r\n  .condition-item:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .group-proof {\r\n    margin-top: 20rpx;\r\n    padding-left: 40rpx;\r\n  }\r\n  \r\n  .placeholder-style {\r\n    color: #999999;\r\n  }\r\n  </style>\r\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhunongapply.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./zhunongapply.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098382\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}