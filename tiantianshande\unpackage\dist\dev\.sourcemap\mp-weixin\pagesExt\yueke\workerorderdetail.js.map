{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?9a36", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?26bc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?ea40", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?6b3e", "uni-app:///pagesExt/yueke/workerorderdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?dda9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/yueke/workerorderdetail.vue?d333"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "workerinfo", "storeinfo", "lefttime", "selectExpressShow", "express_content", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "todel", "orderid", "setTimeout", "toclose", "orderCollect", "showhxqr", "closeHxqr", "openLocation", "uni", "latitude", "longitude", "name", "scale", "openMendian", "logistics", "hideSelectExpressDialog"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACa;;;AAG7E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAswB,CAAgB,ixBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuL1xB;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA,oCACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;MACA;MACAJ;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACAP;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACAjB;IACA;IACAkB;MACA;MACA;MACAlB;IACA;IACAmB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAAslC,CAAgB,kkCAAG,EAAC,C;;;;;;;;;;;ACA1mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/yueke/workerorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/yueke/workerorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./workerorderdetail.vue?vue&type=template&id=071d6571&\"\nvar renderjs\nimport script from \"./workerorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./workerorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./workerorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/yueke/workerorderdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workerorderdetail.vue?vue&type=template&id=071d6571&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workerorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workerorderdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"ordertop flex\" :style=\"'background:url('+pre_url+'/static/img/orderbg.png);background-size:100%'\">\r\n\t\t\t<view class=\"f1 \" v-if=\"detail.status==0\">\r\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\r\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\r\n\t\t\t\t<view class=\"t2\">订单已付款</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\r\n\t\t\t\t<view class=\"t1\">订单已完成</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\r\n\t\t\t\t<view class=\"t1\">订单已取消</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderx\"><image :src=\"pre_url+'/static/img/orderx.png'\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo orderinfotop\">\r\n\t\t\t<view class=\"title\">订单信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">预约时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.yy_time}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\r\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\r\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\">\r\n\t\t\t<image :src=\"detail.binfo.logo\" style=\"width:36rpx;height:36rpx;\"></image>\r\n\t\t\t<view class=\"flex1\" decode=\"true\" space=\"true\" style=\"padding-left:16rpx\">{{detail.binfo.name}}</view>\r\n\t\t</view> -->\r\n\t\t<view class=\"product\">\r\n\t\t\t<view class=\"title\">课程信息</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + detail.proid\">\r\n\t\t\t\t\t<image :src=\"detail.propic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<text class=\"t1\">{{detail.proname}}</text>\r\n\t\t\t\t\t<view class=\"t2 flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t<text>教练：{{workerinfo.realname}} {{workerinfo.dengji||''}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{detail.product_price}}</text></view>\r\n\t\t\t\t\t<!-- <view class=\"t4 flex flex-x-bottom\">\r\n\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && prolist.iscomment==0\" @tap.stop=\"goto\" :data-url=\"'comment?oid=' + prolist.id\">去评价</view>\r\n\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && prolist.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?oid=' + prolist.id\">查看评价</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单人</text>\r\n\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">应付金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\r\n\t\t\t\t<text class=\"t1\">满减活动</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\r\n\t\t\t\t<text class=\"t1\">服务费</text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\r\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">服务中</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已完成</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\r\n\t\t\t\t<text class=\"\" v-if=\"detail.refundCount\" style=\"margin-left: 8rpx;\">有退款({{detail.refundCount}})</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\r\n\t\t\t\t<text class=\"t1\">尾款</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.balance_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\r\n\t\t\t\t<text class=\"t1\">尾款状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==1\">已支付</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==0\">未支付</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\r\n\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.paytypeid\">\r\n\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\r\n\t\t\t\t<text class=\"t1\">派单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.addmoney>0\">\r\n\t\t\t\t<text class=\"t1\">补差价</text>\r\n\t\t\t\t<text class=\"t2 red\">￥{{detail.addmoney}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\r\n\t\t\t\t<text class=\"t1\">完成时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"title\">顾客信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">姓名</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.linkman}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">手机号</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.tel}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      prodata: '',\r\n      djs: '',\r\n      iscommentdp: \"\",\r\n      detail: \"\",\r\n\t\t\tworkerinfo:{},\r\n      storeinfo: \"\",\r\n      lefttime: \"\",\r\n\t\t\tselectExpressShow:false,\r\n\t\t\texpress_content:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onUnload: function () {\r\n    clearInterval(interval);\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYueke/workerorderdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.iscommentdp = res.iscommentdp,\r\n\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\tthat.workerinfo = res.workerinfo;\r\n\t\t\t\tthat.storeinfo = res.storeinfo;\r\n\t\t\t\tthat.lefttime = res.lefttime;\r\n\t\t\t\tthat.payorder = res.payorder;\r\n\t\t\t\tif (res.lefttime > 0) {\r\n\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\r\n\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    getdjs: function () {\r\n      var that = this;\r\n      var totalsec = that.lefttime;\r\n\r\n      if (totalsec <= 0) {\r\n        that.djs = '00时00分00秒';\r\n      } else {\r\n        var houer = Math.floor(totalsec / 3600);\r\n        var min = Math.floor((totalsec - houer * 3600) / 60);\r\n        var sec = totalsec - houer * 3600 - min * 60;\r\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n        that.djs = djs;\r\n      }\r\n    },\r\n    todel: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('删除中');\r\n        app.post('ApiYueke/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            app.goback(true);\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    toclose: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiYueke/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    orderCollect: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定已完成服务吗?', function () {\r\n\t\t\t\tapp.showLoading('确认中');\r\n        app.post('ApiYueke/orderCollect', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\t\tshowhxqr:function(){\r\n\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t},\r\n\t\tcloseHxqr:function(){\r\n\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t},\r\n\t\topenLocation:function(e){\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\tvar address = e.currentTarget.dataset.address;\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t\t});\r\n\t\t},\r\n\t\topenMendian: function(e) {\r\n\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\r\n\t\t\tapp.goto('/shopPackage/shop/mendian?id=' + storeinfo.id);\r\n\t\t},\r\n\t\tlogistics:function(e){\r\n\t\t\tvar express_com = e.currentTarget.dataset.express_com\r\n\t\t\tvar express_no = e.currentTarget.dataset.express_no\r\n\t\t\tapp.goto('/yuyue/logistics?express_no=' + express_no);\r\n\t\t},\r\n\t\thideSelectExpressDialog:function(){\r\n\t\t\tthis.$refs.dialogSelectExpress.close();\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\t.text-min { font-size: 24rpx; color: #999;}\r\n.ordertop{width:100%;height:452rpx;padding:50rpx 0 0 70rpx; justify-content: space-between;}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:40rpx;height:60rpx;line-height:60rpx;}\r\n.ordertop .f1 .t2{font-size:26rpx; margin-top: 20rpx;}\r\n\r\n.container .orderinfotop{ position: relative; margin-top: -200rpx;}\r\n\r\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\r\n.address .img{width:40rpx}\r\n.address image{width:40rpx; height:40rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n.address .info .t2{font-size:24rpx;color:#999}\r\n\r\n.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content image{ width: 140rpx; height: 140rpx;}\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n\r\n.product .content .detail .t1{font-size:26rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .content .detail .t2{color: #999;font-size: 26rpx;margin-top: 10rpx;}\r\n.product .content .detail .t3{display:flex;color: #ff4246;margin-top: 10rpx;}\r\n.product .content .detail .t4{margin-top: 10rpx;}\r\n\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .title,.product .title{ font-weight: bold; font-size: 30rpx; line-height: 60rpx; margin-bottom: 15rpx;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .t3{ margin-top: 3rpx;}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n\r\n.btn1{margin-left:20rpx;min-width:160rpx;padding: 0 20rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\r\n.btn3{font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n\r\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\r\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.hxqrbox .img{width:400rpx;height:400rpx}\r\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.orderx image{ width:124rpx ; height: 124rpx; margin-right: 60rpx;}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workerorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workerorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115094748\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}