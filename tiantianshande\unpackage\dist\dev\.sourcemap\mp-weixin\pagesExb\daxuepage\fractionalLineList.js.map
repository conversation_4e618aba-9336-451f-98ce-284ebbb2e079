{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?9ec5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?a7c9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?1e3d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?b45b", "uni-app:///pagesExb/daxuepage/fractionalLineList.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?dd55", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/fractionalLineList.vue?969c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "HMFilterDropdown", "data", "opt", "loading", "menuindex", "pre_url", "field", "order", "oldcid", "<PERSON>ecid", "longitude", "latitude", "clist", "datalist", "originalDatalist", "pagenum", "keyword", "type", "score", "subject_choice", "school_type", "nomore", "nodata", "types", "showfilter", "showtype", "buydialogShow", "proid", "defaultSelected", "type_id", "school_nature", "enrollment_type", "city", "onLoad", "console", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "app", "that", "getDataList", "uni", "showDrawer", "closeDrawer", "change", "cateClick", "filterConfirm", "filterReset", "filterClick", "changetab", "search", "sortClick", "name", "scale", "phoneNumber", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,2BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AACsE;AACL;AACa;;;AAG9E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAuwB,CAAgB,kxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyF3xB;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAL;QACA;QACA;QACAM;QACAA;QACAA;MACA,GACA;QACAA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAD;MACAA;MACAA;MACAD;QACAxB;QACAE;QACAC;QACAC;QACAC;QACAJ;MACA;QACAkB;QAEAM;QACAE;QACA;QACA;UACAF;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACA;QACAA;UAAA;QAAA;MAEA;IACA;IACA;IACAG;MACAT;MACA;IACA;IACA;IACAU;MACA;IACA;IACA;IACAC;MACAX;MACA;IACA;IACAY;MACA;MACA;MACAN;IACA;IACAO;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAV;MACAA;MACAA;MACAA;IACA;IACAW;MACA;MACA;MACAX;MACAA;MACAA;MACAA;IACA;IACAY;MACA;MACA;MACAZ;MACAA;MACAA;IACA;EAAA,0DACA;IACA;IACA;IACAA;EACA,4DACA;IACA;IACA;IACA;IACA;IACAE;MACA/B;MACAD;MACA2C;MACAC;IACA;EACA,qDACA;IACA;IACAZ;MACAa;MACAC;IACA;EACA,+DACA;IACA;MACA;IACA;IACA;IACAtB;EACA,uDACA;IACAA;IACA;MAAA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;MAAA;MACA;MACA;IACA;IAEA;MAAA;MACA;MACA;IACA;IAEA;MAAA;MACA;MACA;IACA;IAEA;MAAA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA,0DAEA;IACA;IACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtXA;AAAA;AAAA;AAAA;AAAulC,CAAgB,mkCAAG,EAAC,C;;;;;;;;;;;ACA3mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/daxuepage/fractionalLineList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/daxuepage/fractionalLineList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fractionalLineList.vue?vue&type=template&id=d0b3ab0c&\"\nvar renderjs\nimport script from \"./fractionalLineList.vue?vue&type=script&lang=js&\"\nexport * from \"./fractionalLineList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fractionalLineList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/daxuepage/fractionalLineList.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fractionalLineList.vue?vue&type=template&id=d0b3ab0c&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fractionalLineList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fractionalLineList.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block>\r\n\t\t\t<view class=\"search-container\">\r\n\t\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索你感兴趣的大学\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"font-size:24rpx;color:#C2C2C2\" confirm-type=\"search\"\r\n\t\t\t\t\t\t\t@confirm=\"search\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-navbar\">\r\n\t\t\t\t\t<!-- <HM-filterDropdown :menuTop=\"88\" :filterData=\"filterData\" :defaultSelected=\"defaultSelected\"\r\n\t\t\t\t\t\t:updateMenuName=\"true\" @confirm=\"confirm\" dataFormat=\"Object\">\r\n\t\t\t\t\t\t<template slot=\"body\" style=\"width: 100%\">\r\n\t\t\t\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\"\r\n\t\t\t\t\t\t\t\************=\"showDrawer('showRight')\">\r\n\t\t\t\t\t\t\t\t筛选\r\n\t\t\t\t\t\t\t\t<text :class=\"'iconfont iconshaixuan ' + (showfilter?'active':'')\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t</HM-filterDropdown> -->\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n<!-- 添加描述文字 -->\r\n\t\t\t\r\n\t\t\t<view class=\"ind_business\">\r\n\t\t\t\t<view class=\"ind_buslist\" id=\"datalist\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pagesExa/daxuepage/index?id=' + item.id\">\r\n\t\t\t\t\t\t\t<view class=\"ind_busbox flex1 flex-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"ind_buspic flex0\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.logo\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"bus_title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- 标签显示 -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"tags-container\">\r\n\t\t\t\t\t\t\t\t\t\t<!-- 大学类型标签 -->\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(type, index) in item.type_names\" :key=\"'type_' + index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tag-item\">{{type}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 学校性质标签 -->\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(nature, index) in item.school_nature\" :key=\"'nature_' + index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tag-item\">{{nature}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 招生类型标签 -->\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(enrollType, index) in item.enrollment_type\"\r\n\t\t\t\t\t\t\t\t\t\t\t:key=\"'enroll_' + index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tag-item\">{{enrollType}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 大学标签 -->\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(tag, index) in item.biaoqian_names\" :key=\"'tag_' + index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"tag-item\">{{tag}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"bus_sales\">收藏：{{item.sales}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"bus_address\" v-if=\"item.address\" @tap.stop=\"openLocation\"\r\n\t\t\t\t\t\t\t\t\t\t:data-latitude=\"item.latitude\" :data-longitude=\"item.longitude\"\r\n\t\t\t\t\t\t\t\t\t\t:data-company=\"item.name\" :data-address=\"item.address\">\r\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/b_addr.png\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"width:26rpx;height:26rpx;margin-right:10rpx\" />\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1\">{{item.address}}</text><text class=\"x2\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"result-description\">\r\n\t\t\t\t\t\t<text>结果仅供参考</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @addcart=\"addcart\" @buydialogChange=\"buydialogChange\"\r\n\t\t\t\t:menuindex=\"menuindex\"></buydialog>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport HMFilterDropdown from './components/HM-filterDropdown/HM-filterDropdown.vue'\r\n\timport data from './common/data.js'; //筛选菜单数据\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tHMFilterDropdown\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tfield: 'juli',\r\n\t\t\t\torder: 'asc',\r\n\t\t\t\toldcid: \"\",\r\n\t\t\t\tcatchecid: \"\",\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tclist: [],\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\toriginalDatalist: [], // 原始数据\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\ttype: '',\r\n\t\t\t\tscore:'',\r\n\t\t\t\tsubject_choice:'',\r\n\t\t\t\tschool_type:'',\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tnodata: false,\r\n\t\t\t\ttypes: \"\",\r\n\t\t\t\tshowfilter: \"\",\r\n\t\t\t\tshowtype: 0,\r\n\t\t\t\tbuydialogShow: false,\r\n\t\t\t\tproid: 0,\r\n\t\t\t\tdefaultSelected: [],\r\n\t\t\t\ttype_id: '', // 学校类型id\r\n\t\t\t\tschool_nature: '', // 学校性质\r\n\t\t\t\tenrollment_type: '', // 招生类型\r\n\t\t\t\tcity: '' // 选择的城市\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tconsole.log('this.opt',this.opt)\r\n\t\t\t// this.oldcid = this.opt.cid;\r\n\t\t\t// this.catchecid = this.opt.cid;\r\n\t\t\tthis.type = this.opt.type;\r\n\t\t\tthis.score = this.opt.score;\r\n\t\t\tthis.subject_choice = this.opt.subject_choice;\r\n\t\t\tthis.school_type = this.opt.school_type;\r\n\t\t\tif (this.opt.keyword) {\r\n\t\t\t\tthis.keyword = this.opt.keyword;\r\n\t\t\t}\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getDataList(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// that.loading = true;\r\n\t\t\t\t// app.get('ApiDaxue/blist', {}, function(res) {\r\n\t\t\t\t// \tthat.loading = false;\r\n\t\t\t\t// \tthat.clist = res.clist;\r\n\t\t\t\t// \tthat.filterData[1].submenu[3].submenu = res.leixinglist.map(item => {\r\n\t\t\t\t// \t\treturn {\r\n\t\t\t\t// \t\t\tname: item.name,\r\n\t\t\t\t// \t\t\tvalue: item.id\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \tthat.showtype = res.showtype || 0;\r\n\t\t\t\t// \tthat.loaded();\r\n\t\t\t\t// });\r\n\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\tconsole.log('res',res)\r\n\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\t\t\tthat.getDataList();\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfunction() {\r\n\t\t\t\t\t\tthat.getDataList();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetDataList: function(loadmore) {\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tvar latitude = that.latitude;\r\n\t\t\t\tvar longitude = that.longitude;\r\n\t\t\t\tvar keyword = that.keyword;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tapp.post('ApiDaxue/fenshuxianchaxun', {\r\n\t\t\t\t\tpagenum: pagenum,\r\n\t\t\t\t\ttype: that.type,\r\n\t\t\t\t\tscore:that.score,\r\n\t\t\t\t\tsubject_choice:that.subject_choice,\r\n\t\t\t\t\tschool_type:that.school_type,\r\n\t\t\t\t\tkeyword: keyword\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tconsole.log('res',res)\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\tthat.originalDatalist = JSON.parse(JSON.stringify(data));\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 在这里根据 sort 字段进行排序，降序排列\r\n\t\t\t\t\t\t\t\t\t\tthat.datalist.sort((a, b) => b.sort - a.sort);\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 打开窗口\r\n\t\t\tshowDrawer(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tthis.$refs[e].open()\r\n\t\t\t},\r\n\t\t\t// 关闭窗口\r\n\t\t\tcloseDrawer(e) {\r\n\t\t\t\tthis.$refs[e].close()\r\n\t\t\t},\r\n\t\t\t// 抽屉状态发生变化触发\r\n\t\t\tchange(e, type) {\r\n\t\t\t\tconsole.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));\r\n\t\t\t\tthis[type] = e\r\n\t\t\t},\r\n\t\t\tcateClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cid = e.currentTarget.dataset.cid;\r\n\t\t\t\tthat.catchecid = cid\r\n\t\t\t},\r\n\t\t\tfilterConfirm() {\r\n\t\t\t\tthis.cid = this.catchecid;\r\n\t\t\t\tthis.gid = this.catchegid;\r\n\t\t\t\tthis.getDataList();\r\n\t\t\t\tthis.$refs['showRight'].close()\r\n\t\t\t},\r\n\t\t\tfilterReset() {\r\n\t\t\t\tthis.catchecid = this.oldcid;\r\n\t\t\t\tthis.catchegid = '';\r\n\t\t\t},\r\n\t\t\tfilterClick: function() {\r\n\t\t\t\tthis.showfilter = !this.showfilter\r\n\t\t\t},\r\n\t\t\tchangetab: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cid = e.currentTarget.dataset.cid;\r\n\t\t\t\tthat.cid = cid\r\n\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\tthat.datalist = [];\r\n\t\t\t\tthat.getDataList();\r\n\t\t\t},\r\n\t\t\tsearch: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar keyword = e.detail.value;\r\n\t\t\t\tthat.keyword = keyword;\r\n\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\tthat.datalist = [];\r\n\t\t\t\tthat.getDataList();\r\n\t\t\t},\r\n\t\t\tsortClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar t = e.currentTarget.dataset;\r\n\t\t\t\tthat.field = t.field;\r\n\t\t\t\tthat.order = t.order;\r\n\t\t\t\tthat.getDataList();\r\n\t\t\t},\r\n\t\t\tfilterClick: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar types = e.currentTarget.dataset.types;\r\n\t\t\t\tthat.types = types;\r\n\t\t\t},\r\n\t\t\topenLocation: function(e) {\r\n\t\t\t\t//console.log(e)\r\n\t\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tname: address,\r\n\t\t\t\t\tscale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tphone: function(e) {\r\n\t\t\t\tvar phone = e.currentTarget.dataset.phone;\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\tfail: function() {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbuydialogChange: function(e) {\r\n\t\t\t\tif (!this.buydialogShow) {\r\n\t\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t\tconsole.log(this.buydialogShow);\r\n\t\t\t},\r\n\t\t\tconfirm: function(e) {\r\n\t\t\t\tconsole.log('e', e)\r\n\t\t\t\tif (e.value[2][0].length !== 0) { // 综合排序\r\n\t\t\t\t\tif (e.value[2][0][0] == '离我最近') {\r\n\t\t\t\t\t\tthis.field = 'comment_score';\r\n\t\t\t\t\t\tthis.order = 'desc';\r\n\t\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (e.value[1][0].length !== 0) { // 获取学校性质\r\n\t\t\t\t\tthis.school_nature = e.value[1][0][0]\r\n\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (e.value[1][2].length !== 0) { // 获取招生类型\r\n\t\t\t\t\tthis.enrollment_type = e.value[1][2][0]\r\n\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (e.value[1][3].length !== 0) { // 获取学校类型\r\n\t\t\t\t\tthis.type_id = e.value[1][3][0]\r\n\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (e.value[0].length !== 0) { // 城市筛选\r\n\t\t\t\t\tthis.city = e.value[0][1]\r\n\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 判断筛选是否都是空数据,如果为空则清空数据重新请求\r\n\t\t\t\tif (this.isAllEmpty(e.value)) {\r\n\t\t\t\t\tthis.field = '';\r\n\t\t\t\t\tthis.order = '';\r\n\t\t\t\t\tthis.school_nature = ''\r\n\t\t\t\t\tthis.type_id = ''\r\n\t\t\t\t\tthis.enrollment_type = ''\r\n\t\t\t\t\tthis.getDataList();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 递归查询数组是否都是空的\r\n\t\t\tisAllEmpty: function(array) {\r\n\t\t\t\t// 遍历数组的每个元素\r\n\t\t\t\tfor (let i = 0; i < array.length; i++) {\r\n\t\t\t\t\tconst element = array[i];\r\n\t\t\t\t\t// 如果元素是数组，递归检查\r\n\t\t\t\t\tif (Array.isArray(element)) {\r\n\t\t\t\t\t\tif (!this.isAllEmpty(element)) {\r\n\t\t\t\t\t\t\treturn false; // 如果找到非空的数组，返回false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (element !== null && element !== undefined && element !== '全部城市') {\r\n\t\t\t\t\t\t// 如果元素不是null或undefined，说明有非空元素，返回false\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn true; // 所有元素都为空，返回true\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.search-container {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 9;\r\n\t\ttop: var(--window-top)\r\n\t}\r\n\r\n\t.topsearch {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 20rpx;\r\n\t}\r\n\r\n\t.topsearch .f1 {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 0;\r\n\t\tbackground-color: #f7f7f7;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.topsearch .f1 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 10px\r\n\t}\r\n\r\n\t.topsearch .f1 input {\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.topsearch .search-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: #5a5a5a;\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-left: 20rpx\r\n\t}\r\n\r\n\t.search-navbar {\r\n\t\tdisplay: flex;\r\n\t\ttext-align: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 5rpx 0\r\n\t}\r\n\r\n\t.search-navbar-item {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 13px;\r\n\t\tcolor: #757575;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: color .2s linear;\r\n\r\n\r\n\t\t/* flex: 1;\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tposition: relative;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #323232 */\r\n\t}\r\n\r\n\t.search-navbar-item .iconshangla {\r\n\t\tposition: absolute;\r\n\t\ttop: -4rpx;\r\n\t\tpadding: 0 6rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #7D7D7D\r\n\t}\r\n\r\n\t.search-navbar-item .icondaoxu {\r\n\t\tposition: absolute;\r\n\t\ttop: 8rpx;\r\n\t\tpadding: 0 6rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #7D7D7D\r\n\t}\r\n\r\n\t.search-navbar-item .iconshaixuan {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #7d7d7d\r\n\t}\r\n\r\n\t.filter-scroll-view {\r\n\t\tmargin-top: var(--window-top)\r\n\t}\r\n\r\n\t.search-filter {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\ttext-align: left;\r\n\t\twidth: 100%;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.filter-content-title {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tmargin-bottom: 10rpx\r\n\t}\r\n\r\n\t.filter-title {\r\n\t\tcolor: #BBBBBB;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #F8F8F8;\r\n\t\tpadding: 60rpx 0 30rpx 20rpx;\r\n\t}\r\n\r\n\t.search-filter-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t}\r\n\r\n\t.search-filter-content .filter-item {\r\n\t\tbackground: #F4F4F4;\r\n\t\tborder-radius: 28rpx;\r\n\t\tcolor: #2B2B2B;\r\n\t\tfont-weight: bold;\r\n\t\tmargin: 10rpx 10rpx;\r\n\t\tmin-width: 140rpx;\r\n\t\theight: 56rpx;\r\n\t\tline-height: 56rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 30rpx\r\n\t}\r\n\r\n\t.search-filter-content .close {\r\n\t\ttext-align: right;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff4544;\r\n\t\twidth: 100%;\r\n\t\tpadding-right: 20rpx\r\n\t}\r\n\r\n\t.search-filter button .icon {\r\n\t\tmargin-top: 6rpx;\r\n\t\theight: 54rpx;\r\n\t}\r\n\r\n\t.search-filter-btn {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 30rpx 30rpx;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.search-filter-btn .btn {\r\n\t\twidth: 240rpx;\r\n\t\theight: 66rpx;\r\n\t\tline-height: 66rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 33rpx;\r\n\t\tcolor: #2B2B2B;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.search-filter-btn .btn2 {\r\n\t\twidth: 240rpx;\r\n\t\theight: 66rpx;\r\n\t\tline-height: 66rpx;\r\n\t\tborder-radius: 33rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.ind_business {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 140rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tpadding: 0 24rpx\r\n\t}\r\n\r\n\t.ind_business .ind_busbox {\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.ind_business .ind_buspic {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tmargin-right: 28rpx;\r\n\t}\r\n\r\n\t.ind_business .ind_buspic image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 8rpx;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\r\n\t.ind_business .bus_title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #222;\r\n\t\tfont-weight: bold;\r\n\t\tline-height: 46rpx\r\n\t}\r\n\r\n\t.ind_business .bus_score {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #FC5648;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.ind_business .bus_score .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.ind_business .bus_score .txt {\r\n\t\tmargin-left: 20rpx\r\n\t}\r\n\r\n\t.ind_business .indsale_box {\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.ind_business .bus_sales {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tright: 28rpx\r\n\t}\r\n\r\n\t.ind_business .bus_address {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 22rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-top: 6rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.ind_business .bus_address .x2 {\r\n\t\tpadding-left: 20rpx\r\n\t}\r\n\r\n\t.ind_business .prolist {\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-top: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.ind_business .prolist .product {\r\n\t\twidth: 108rpx;\r\n\t\theight: 160rpx;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: inline-flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-right: 24rpx\r\n\t}\r\n\r\n\t.ind_business .prolist .product .f1 {\r\n\t\twidth: 108rpx;\r\n\t\theight: 108rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tbackground: #f6f6f6\r\n\t}\r\n\r\n\t.ind_business .prolist .product .f2 {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #FC5648;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 4rpx\r\n\t}\r\n\r\n\t.ind_business .prolist2 {\r\n\t\tmargin-top: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product {\r\n\t\twidth: 118rpx;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: inline-flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-right: 10rpx;\r\n\t\tposition: relative;\r\n\t\tmin-height: 200rpx;\r\n\t\tpadding-bottom: 20rpx\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f1 {\r\n\t\twidth: 118rpx;\r\n\t\theight: 118rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tbackground: #f6f6f6\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f2 {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #FC5648;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 4rpx;\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f3 {\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #aaa;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f4 {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #888;\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f5 {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: absolute;\r\n\t\tdisplay: relative;\r\n\t\ttop: 140rpx;\r\n\t\tright: 0rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f5 .icon_gouwuche {\r\n\t\tfont-size: 28rpx;\r\n\t\theight: 48rpx;\r\n\t\tline-height: 48rpx\r\n\t}\r\n\r\n\t.ind_business .prolist2 .product .f5 .img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.tags-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-top: 10rpx;\r\n\t\t/* 标签容器与标题的间距 */\r\n\t}\r\n\r\n\t.tag-item {\r\n\t\tbackground: rgba(173, 216, 230, 0.3);\r\n\t\t/* 淡蓝色透明背景 */\r\n\t\tcolor: #007BFF;\r\n\t\t/* 标签文字颜色 */\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\t/* 标签内边距 */\r\n\t\tborder-radius: 12rpx;\r\n\t\t/* 标签圆角 */\r\n\t\tmargin-right: 8rpx;\r\n\t\t/* 标签之间的水平间距 */\r\n\t\tmargin-bottom: 8rpx;\r\n\t\t/* 标签之间的垂直间距 */\r\n\t\tfont-size: 24rpx;\r\n\t\t/* 标签文字大小 */\r\n\t\twhite-space: nowrap;\r\n\t\t/* 防止标签文字换行 */\r\n\t}\r\n\t/* 添加一个新的样式用于描述文字 */\r\n\t\t.result-description {\r\n\t\t\tmargin-top: 100rpx; /* 调整到搜索框下方 */\r\n\t\t\t\r\n\t\t\tfont-size: 16rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\ttext-align: center;\r\n\t\t\tbackground-color: #f7f7f7;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fractionalLineList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fractionalLineList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027309\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}