{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?3a5e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?63c5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?9151", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?1e4e", "uni-app:///pagesExa/tuandui/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?a8df", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/detail.vue?5ab1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "activityId", "activityInfo", "rewardRules", "userRewardInfo", "onLoad", "onPullDownRefresh", "onShow", "onShareAppMessage", "title", "desc", "pic", "callback", "methods", "getdata", "console", "app", "that", "targetActivity", "uni", "getUserRewardInfo", "activity_id", "refreshData", "gotoRecords", "url", "claimReward"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoL/wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;QACA;MAAA;IAEA;EACA;EACAC;IACAC;MACA;MAEA;QACAC;QACAC;QACA;MACA;MAEAD;MACAE;;MAEA;MACAD;QACAD;QACA;UACA;UACA;UACA;YACA;cACAG;cACA;YACA;UACA;UAEA;YACAD;YACAA;YAEAE;cACAV;YACA;;YAEA;YACAQ;UACA;YACAA;YACAF;YACAC;UACA;QACA;UACAC;UACAF;UACAC;QACA;MACA;IACA;IAEAI;MACA;MAEAL;MAEAC;QAAAK;MAAA;QACAJ;QACAA;QAEAF;QAEA;UACAE;UACAF;QACA;UACA;UACAA;UACAE;QACA;QAEAE;MACA;IACA;IAEAG;MACAP;MACA;MACA;IACA;IAEAQ;MACAJ;QACAK;MACA;IACA;IAEAC;MACA;MACA;MAEAV;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzTA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuandui/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuandui/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=067304c6&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuandui/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=067304c6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<text class=\"title\" v-if=\"activityInfo.title\">{{activityInfo.title}}</text>\r\n\t\t\t\t<view class=\"activity-meta\">\r\n\t\t\t\t\t<text class=\"meta-item\">{{activityInfo.performance_name || (activityInfo.performance_type == 1 ? '团队金额' : '团队数量')}}</text>\r\n\t\t\t\t\t<text class=\"meta-item\">{{activityInfo.reward_name || (activityInfo.reward_type == 1 ? '比例奖励' : '固定金额')}}</text>\r\n\t\t\t\t\t<text class=\"meta-item algorithm-tag\" :class=\"activityInfo.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'\">\r\n\t\t\t\t\t\t{{activityInfo.algorithm_name || (activityInfo.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法')}}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"activity-description\">\r\n\t\t\t\t\t<view class=\"desc-item\" v-if=\"activityInfo.time_range\">\r\n\t\t\t\t\t\t<text class=\"desc-label\">活动时间：</text>\r\n\t\t\t\t\t\t<text class=\"desc-value\">{{activityInfo.time_range.start_date}} ~ {{activityInfo.time_range.end_date}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\" v-if=\"activityInfo.min_amount\">\r\n\t\t\t\t\t\t<text class=\"desc-label\">起始业绩：</text>\r\n\t\t\t\t\t\t<text class=\"desc-value\">{{activityInfo.min_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\" v-if=\"activityInfo.deduct_contribution_text\">\r\n\t\t\t\t\t\t<text class=\"desc-label\">贡献值扣除：</text>\r\n\t\t\t\t\t\t<text class=\"desc-value\">{{activityInfo.deduct_contribution_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\" v-if=\"activityInfo.description\">\r\n\t\t\t\t\t\t<text class=\"desc-label\">活动说明：</text>\r\n\t\t\t\t\t\t<text class=\"desc-value\">{{activityInfo.description}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc-item\">\r\n\t\t\t\t\t\t<text class=\"desc-label\">创建时间：</text>\r\n\t\t\t\t\t\t<text class=\"desc-value\">{{activityInfo.createtime}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 奖励规则详情 -->\r\n\t\t\t<view class=\"rules-box\">\r\n\t\t\t\t<view class=\"rules-title\">奖励规则</view>\r\n\t\t\t\t<view class=\"rules-content\">\r\n\t\t\t\t\t<block v-for=\"(rule, index) in rewardRules\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t\t\t<view class=\"rule-level\">等级 {{index + 1}}</view>\r\n\t\t\t\t\t\t\t<view class=\"rule-details\">\r\n\t\t\t\t\t\t\t\t<view class=\"rule-achievement\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"rule-label\">目标业绩：</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"rule-value\">{{rule.achievement}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"rule-reward\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"rule-label\">奖励：</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"rule-value reward-highlight\">\r\n\t\t\t\t\t\t\t\t\t\t{{activityInfo.reward_type == 1 ? rule.reward_value + '%' : '¥' + rule.reward_value}}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rule-calculation\" v-if=\"activityInfo.reward_type == 1\">\r\n\t\t\t\t\t\t\t\t按业绩的 {{rule.reward_value}}% 发放奖励\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rule-calculation\" v-else>\r\n\t\t\t\t\t\t\t\t固定奖励 ¥{{rule.reward_value}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"algorithm-note\" v-if=\"activityInfo.algorithm_type === 'layered_reduction'\">\r\n\t\t\t\t\t\t\t\t<text class=\"note-text\">注：使用分层递减算法，实际奖励 = 理论奖励 - 直接下级理论奖励</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 适用条件 -->\r\n\t\t\t<view class=\"conditions-box\">\r\n\t\t\t\t<view class=\"conditions-title\">适用条件</view>\r\n\t\t\t\t<view class=\"conditions-content\">\r\n\t\t\t\t\t<view class=\"condition-item\" v-if=\"activityInfo.level_names\">\r\n\t\t\t\t\t\t<view class=\"condition-label\">适用等级：</view>\r\n\t\t\t\t\t\t<view class=\"condition-value\">{{activityInfo.level_names}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"condition-item\" v-if=\"activityInfo.product_names\">\r\n\t\t\t\t\t\t<view class=\"condition-label\">适用商品：</view>\r\n\t\t\t\t\t\t<view class=\"condition-value\">{{activityInfo.product_names}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"condition-item\">\r\n\t\t\t\t\t\t<view class=\"condition-label\">统计类型：</view>\r\n\t\t\t\t\t\t<view class=\"condition-value\">{{activityInfo.statistics_type == 1 ? '已付款订单' : activityInfo.statistics_type == 2 ? '已收货订单' : '全部订单'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 用户状态 -->\r\n\t\t\t<view class=\"user-status-box\" v-if=\"userRewardInfo && userRewardInfo.user_performance\">\r\n\t\t\t\t<view class=\"status-title\">我的状态</view>\r\n\t\t\t\t<view class=\"status-content\">\r\n\t\t\t\t\t<view class=\"current-performance\">\r\n\t\t\t\t\t\t<view class=\"perf-label\">当前团队业绩</view>\r\n\t\t\t\t\t\t<view class=\"perf-value\">{{userRewardInfo.user_performance.team_performance}}{{activityInfo.performance_type == 1 ? '元' : '件'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"team-info\" v-if=\"userRewardInfo.team_structure\">\r\n\t\t\t\t\t\t<view class=\"team-count\">团队成员：{{userRewardInfo.team_structure.total_members}}人</view>\r\n\t\t\t\t\t\t<view class=\"team-levels\">团队层级：{{userRewardInfo.team_structure.max_level}}级</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"reward-progress\">\r\n\t\t\t\t\t\t<view class=\"progress-label\">奖励进度</view>\r\n\t\t\t\t\t\t<view class=\"progress-details\">\r\n\t\t\t\t\t\t\t<view class=\"progress-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"progress-name\">理论奖励</text>\r\n\t\t\t\t\t\t\t\t<text class=\"progress-amount\">¥{{userRewardInfo.user_performance.theoretical_reward || 0}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"progress-item\" v-if=\"activityInfo.algorithm_type === 'layered_reduction'\">\r\n\t\t\t\t\t\t\t\t<text class=\"progress-name\">实际奖励</text>\r\n\t\t\t\t\t\t\t\t<text class=\"progress-amount success\">¥{{userRewardInfo.user_performance.actual_reward || 0}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"calculation-explanation\" v-if=\"userRewardInfo.user_performance.calculation_detail\">\r\n\t\t\t\t\t\t\t<text class=\"explanation-text\">{{userRewardInfo.user_performance.calculation_detail}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 奖励详情 -->\r\n\t\t\t<view class=\"reward-details-box\" v-if=\"userRewardInfo && userRewardInfo.reward_details\">\r\n\t\t\t\t<view class=\"details-title\">奖励详情</view>\r\n\t\t\t\t<view class=\"details-content\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in userRewardInfo.reward_details\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<view class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t<view class=\"achievement-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"achievement-label\">等级 {{item.achievement_level}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"achievement-target\">目标：{{item.achievement_target}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"reward-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"reward-value\">{{item.reward_value}}{{activityInfo.reward_type == 1 ? '%' : '元'}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-amounts\">\r\n\t\t\t\t\t\t\t\t<view class=\"amount-item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"amount-label\">理论奖励：</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"amount-value theoretical\">¥{{item.theoretical_reward_amount}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"amount-item\" v-if=\"activityInfo.algorithm_type === 'layered_reduction' && item.actual_reward_amount !== item.theoretical_reward_amount\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"amount-label\">实际奖励：</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"amount-value actual\">¥{{item.actual_reward_amount}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-status\">\r\n\t\t\t\t\t\t\t\t<view class=\"status-badge\" :class=\"item.is_achieved ? (item.is_paid ? 'status-paid' : (item.is_claimed ? 'status-claimed' : 'status-achieved')) : 'status-not-achieved'\">\r\n\t\t\t\t\t\t\t\t\t{{item.is_achieved ? (item.is_paid ? '已发放' : (item.is_claimed ? '已领取' : '已达成')) : '未达成'}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"claim-action\" v-if=\"item.is_achieved && !item.is_claimed && !item.is_paid\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"claim-btn\" @tap=\"claimReward\" :data-activity=\"item.activity_id\" :data-level=\"item.achievement_level\">立即领取</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"calculation-desc\" v-if=\"item.calculation\">\r\n\t\t\t\t\t\t\t\t{{item.calculation}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 操作按钮 -->\r\n\t\t\t<view class=\"action-box\">\r\n\t\t\t\t<view class=\"action-btn primary\" @tap=\"refreshData\">\r\n\t\t\t\t\t刷新数据\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn secondary\" @tap=\"gotoRecords\">\r\n\t\t\t\t\t查看记录\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tactivityId: 0,\r\n\t\t\tactivityInfo: {},\r\n\t\t\trewardRules: [],\r\n\t\t\tuserRewardInfo: null\r\n\t\t};\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.activityId = opt.id || 0;\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShow: function() {\r\n\t\t// 页面显示时刷新用户奖励信息\r\n\t\tif (this.isload && this.activityId > 0) {\r\n\t\t\tthis.getUserRewardInfo();\r\n\t\t}\r\n\t},\r\n\tonShareAppMessage: function() {\r\n\t\tvar that = this;\r\n\t\treturn this._sharewx({\r\n\t\t\ttitle: this.activityInfo.title,\r\n\t\t\tdesc: '团队业绩奖励活动',\r\n\t\t\tpic: '/static/img/tuandui-share.png',\r\n\t\t\tcallback: function() {\r\n\t\t\t\t// 分享回调\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tif (!that.activityId) {\r\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_001] 活动ID不能为空');\r\n\t\t\t\tapp.alert('活动ID不能为空');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_002] 获取活动详情, ID:', that.activityId);\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\t// 先获取活动列表，从中找到指定活动的信息\r\n\t\t\tapp.get('ApiTuandui/getActivityList', {}, function(res) {\r\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_003] 活动列表响应:', res);\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t// 从活动列表中找到指定活动\r\n\t\t\t\t\tvar targetActivity = null;\r\n\t\t\t\t\tfor (var i = 0; i < res.data.length; i++) {\r\n\t\t\t\t\t\tif (res.data[i].id == that.activityId) {\r\n\t\t\t\t\t\t\ttargetActivity = res.data[i];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (targetActivity) {\r\n\t\t\t\t\t\tthat.activityInfo = targetActivity;\r\n\t\t\t\t\t\tthat.rewardRules = targetActivity.reward_rules || [];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\ttitle: targetActivity.title || '活动详情'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 获取用户奖励信息\r\n\t\t\t\t\t\tthat.getUserRewardInfo();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_004] 未找到指定活动');\r\n\t\t\t\t\t\tapp.alert('活动不存在');\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_005] 获取活动列表失败:', res.msg);\r\n\t\t\t\t\tapp.alert(res.msg || '获取活动信息失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tgetUserRewardInfo: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_001] 获取用户奖励信息, 活动ID:', that.activityId);\r\n\t\t\t\r\n\t\t\tapp.get('ApiTuandui/getUserRewardInfo', {activity_id: that.activityId}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_002] 用户奖励信息响应:', res);\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.userRewardInfo = res.data;\r\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_003] 设置用户奖励信息成功');\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 用户信息获取失败不影响页面显示\r\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-WARN-[detail.vue][getUserRewardInfo_004] 获取用户奖励信息失败：', res.msg);\r\n\t\t\t\t\tthat.userRewardInfo = null;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\trefreshData: function() {\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][refreshData_001] 刷新数据');\r\n\t\t\tthis.getActivityInfo();\r\n\t\t\tthis.getUserRewardInfo();\r\n\t\t},\r\n\t\t\r\n\t\tgotoRecords: function() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pagesExa/tuandui/records?activity_id=' + this.activityId\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tclaimReward: function(e) {\r\n\t\t\tvar activityId = e.currentTarget.dataset.activity;\r\n\t\t\tvar level = e.currentTarget.dataset.level;\r\n\t\t\t\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[detail.vue][claimReward_001] 领取奖励:', activityId, level);\r\n\t\t\t\r\n\t\t\t// TODO: 实现领取奖励逻辑\r\n\t\t\tthis.$refs.popmsg.show('提示', '领取奖励功能待实现');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.header .title {\r\n\tfont-size: 40rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tline-height: 1.4;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.activity-meta {\r\n\tdisplay: flex;\r\n\tgap: 15rpx;\r\n\tmargin-bottom: 25rpx;\r\n}\r\n\r\n.meta-item {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tbackground-color: #f8f9fa;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.activity-description {\r\n\tborder-top: 1px solid #f0f0f0;\r\n\tpadding-top: 20rpx;\r\n}\r\n\r\n.desc-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.desc-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\twidth: 160rpx;\r\n}\r\n\r\n.desc-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tflex: 1;\r\n}\r\n\r\n/* 奖励规则样式 */\r\n.rules-box, .conditions-box, .user-status-box, .reward-details-box {\r\n\tbackground-color: #fff;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.rules-title, .conditions-title, .status-title, .details-title {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tpadding: 20rpx 30rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.rules-content, .conditions-content, .status-content, .details-content {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.rule-item {\r\n\tborder: 1px solid #e9ecef;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 25rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbackground-color: #fafafa;\r\n}\r\n\r\n.rule-level {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.rule-details {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.rule-achievement, .rule-reward {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.rule-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.rule-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.reward-highlight {\r\n\tcolor: #FA5151 !important;\r\n}\r\n\r\n.rule-calculation {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-style: italic;\r\n}\r\n\r\n/* 适用条件样式 */\r\n.condition-item {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.condition-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.condition-value {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tline-height: 1.5;\r\n}\r\n\r\n/* 用户状态样式 */\r\n.current-performance {\r\n\ttext-align: center;\r\n\tmargin-bottom: 25rpx;\r\n}\r\n\r\n.perf-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.perf-value {\r\n\tfont-size: 48rpx;\r\n\tcolor: #FA5151;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.team-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 25rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.reward-progress {\r\n\tborder-top: 1px solid #f0f0f0;\r\n\tpadding-top: 20rpx;\r\n}\r\n\r\n.progress-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.progress-details {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.progress-item {\r\n\ttext-align: center;\r\n\tflex: 1;\r\n}\r\n\r\n.progress-name {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.progress-amount {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.progress-amount.success {\r\n\tcolor: #28a745;\r\n}\r\n\r\n.progress-amount.pending {\r\n\tcolor: #ffc107;\r\n}\r\n\r\n/* 奖励详情样式 */\r\n.detail-item {\r\n\tborder: 1px solid #e9ecef;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 25rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.detail-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.achievement-info, .reward-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.achievement-label, .achievement-target {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 5rpx;\r\n}\r\n\r\n.reward-value {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\ttext-align: right;\r\n}\r\n\r\n.reward-amount {\r\n\tfont-size: 32rpx;\r\n\tcolor: #FA5151;\r\n\tfont-weight: bold;\r\n\ttext-align: right;\r\n}\r\n\r\n.detail-status {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.status-badge {\r\n\tfont-size: 24rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.status-unachieved {\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #999;\r\n}\r\n\r\n.status-paid {\r\n\tbackground-color: #d4edda;\r\n\tcolor: #155724;\r\n}\r\n\r\n.status-pending {\r\n\tbackground-color: #fff3cd;\r\n\tcolor: #856404;\r\n}\r\n\r\n.status-claimable {\r\n\tbackground-color: #d1ecf1;\r\n\tcolor: #0c5460;\r\n}\r\n\r\n.claim-btn {\r\n\tbackground-color: #FA5151;\r\n\tcolor: #fff;\r\n\tpadding: 12rpx 24rpx;\r\n\tborder-radius: 20rpx;\r\n\tfont-size: 24rpx;\r\n}\r\n\r\n.calculation-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-style: italic;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-box {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 100rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n.action-btn.primary {\r\n\tbackground-color: #FA5151;\r\n\tcolor: #fff;\r\n}\r\n\r\n.action-btn.secondary {\r\n\tbackground-color: #f8f9fa;\r\n\tcolor: #333;\r\n\tborder: 1px solid #e9ecef;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065320\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}