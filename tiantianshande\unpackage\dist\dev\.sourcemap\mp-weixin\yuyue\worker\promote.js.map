{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?8c06", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?60b1", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?670f", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?ea41", "uni-app:///yuyue/worker/promote.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?ac12", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/worker/promote.vue?93a9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "loading", "uniLoadMore", "dpTabbar", "popmsg", "data", "opt", "isload", "dataList", "pagenum", "pernum", "total", "loadingType", "showPosterPopup", "posterImageUrl", "onLoad", "onReachBottom", "onPullDownRefresh", "setTimeout", "uni", "methods", "getdata", "app", "that", "generatePoster", "title", "proid", "closePoster<PERSON><PERSON><PERSON>", "previewPoster", "urls", "savePoster", "url", "success", "filePath", "icon", "fail", "content", "showCancel", "stopPrevent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuEhxB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAN;MACAO;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;QAAAb;QAAAC;MAAA;QACAa;QACA;UACA;UACA,eACAA,0BAEAA;UACAA;UACA;YACAA;UACA;YACAA;UACA;QACA;UACA;UACAA;QACA;MACA;IACA;IACAC;MACAL;QAAAM;MAAA;MACA;MACAH;QAAAI;MAAA;QACAP;QACA;UACAI;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAK;MACA;MACA;IACA;IACAC;MACA;QACAT;UACAU;QACA;MACA;IACA;IACAC;MACA;MACAX;QAAAM;MAAA;MACA;MACA;MACAN;QACAY;QACAC;UACA;YACA;YACAb;cACAc;cACAD;gBACAb;gBACAA;kBACAM;kBACAS;gBACA;cACA;cACAC;gBACAhB;gBACA;kBACAA;oBACAM;oBACAW;oBACAC;kBACA;gBACA;kBACAlB;oBACAM;oBACAS;kBACA;gBACA;cACA;YACA;UACA;YACAf;YACAA;cACAM;cACAS;YACA;UACA;QACA;QACAC;UACAhB;UACAA;YACAM;YACAS;UACA;QACA;MACA;IACA;IACAI;MACA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/worker/promote.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/worker/promote.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./promote.vue?vue&type=template&id=a04ddf42&\"\nvar renderjs\nimport script from \"./promote.vue?vue&type=script&lang=js&\"\nexport * from \"./promote.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promote.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/worker/promote.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promote.vue?vue&type=template&id=a04ddf42&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g0 =\n    _vm.isload && !_vm.loading ? _vm.dataList && _vm.dataList.length > 0 : null\n  var l0 =\n    _vm.isload && !_vm.loading && g0\n      ? _vm.__map(_vm.dataList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.t(\"color1\")\n          var g1 = String(item.price || 0).split(\".\")\n          var g2 = String(item.price || 0).split(\".\")[1] || \"00\"\n          var m3 = _vm.t(\"color1\")\n          var m4 = _vm.t(\"color1rgb\")\n          return {\n            $orig: $orig,\n            m2: m2,\n            g1: g1,\n            g2: g2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var m5 = _vm.isload && _vm.showPosterPopup ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.showPosterPopup ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.showPosterPopup ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promote.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promote.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"isload\">\n\t\t<view class=\"container\">\n\t\t\t<view class=\"header\" :style=\"{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\n\t\t\t\t<text class=\"title\">推广产品</text>\n\t\t\t\t<text class=\"subtitle\">为顾客提供优质服务，提升您的收入</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"content\">\n\t\t\t\t<block v-if=\"!loading\">\n\t\t\t\t\t<view class=\"product-list\" v-if=\"dataList && dataList.length > 0\">\n\t\t\t\t\t\t<view class=\"product-item\" v-for=\"(item, index) in dataList\" :key=\"item.id\">\n\t\t\t\t\t\t\t<image class=\"product-image\" :src=\"item.pic\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t\t<view class=\"product-name\">{{ item.name }}</view>\n\t\t\t\t\t\t\t\t<view class=\"product-price\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\n\t\t\t\t\t\t\t\t\t<text class=\"price-integer\">{{ String(item.price || 0).split('.')[0] }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"price-decimal\">.{{ (String(item.price || 0).split('.')[1] || '00') }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"product-sales\">已售 {{ item.sales || 0 }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"poster-button-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"poster-button\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"generatePoster(item.id)\">\n\t\t\t\t\t\t\t\t\t<text class=\"button-text\">生成海报</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-load-more :status=\"loadingType\"></uni-load-more>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"no-data\" v-else>\n\t\t\t\t\t\t<image class=\"no-data-image\" src=\"/static/img/no-data.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"no-data-text\">暂无可推广的产品</text>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<loading v-else></loading>\n\t\t\t</view>\n\n\t\t\t<!-- 海报展示弹窗 -->\n\t\t\t<view class=\"poster-popup\" v-if=\"showPosterPopup\" @touchmove.stop.prevent=\"stopPrevent\">\n\t\t\t\t<view class=\"popup-mask\" @tap=\"closePosterPopup\"></view>\n\t\t\t\t<view class=\"poster-content\">\n\t\t\t\t\t<view class=\"poster-header\">\n\t\t\t\t\t\t<text class=\"poster-title\">产品推广海报</text>\n\t\t\t\t\t\t<view class=\"close-icon\" @tap=\"closePosterPopup\">×</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<image class=\"poster-image-preview\" :src=\"posterImageUrl\" mode=\"widthFix\" @tap=\"previewPoster\"></image>\n\t\t\t\t\t<view class=\"poster-tip\">\n\t\t\t\t\t\t<view class=\"tip-icon\" :style=\"{color:t('color1')}\">i</view>\n\t\t\t\t\t\t<text class=\"tip-text\">长按图片保存或分享给好友</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t\t<view class=\"save-button\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"savePoster\">\n\t\t\t\t\t\t\t<text>保存到相册</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"close-button\" @tap=\"closePosterPopup\">\n\t\t\t\t\t\t\t<text>关闭</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 添加通用组件 -->\n\t\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t\t</view>\n\t</view>\n\t<loading v-else></loading>\n</template>\n\n<script>\nconst app = getApp();\nimport loading from '@/components/loading/loading';\nimport uniLoadMore from '@/components/uni-load-more/uni-load-more';\nimport dpTabbar from '@/components/dp-tabbar/dp-tabbar.vue';\nimport popmsg from '@/components/popmsg/popmsg.vue';\n\nexport default {\n\tcomponents: {\n\t\tloading,\n\t\tuniLoadMore,\n\t\tdpTabbar,\n\t\tpopmsg\n\t},\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tisload: false,\n\t\t\tloading: true,\n\t\t\tdataList: [],\n\t\t\tpagenum: 1,\n\t\t\tpernum: 20,\n\t\t\ttotal: 0,\n\t\t\tloadingType: 'more',\n\t\t\tshowPosterPopup: false,\n\t\t\tposterImageUrl: ''\n\t\t};\n\t},\n\tonLoad: function (options) {\n\t\tthis.opt = options;\n\t\tthis.loaded();\n\t\tthis.getdata(true);\n\t},\n\tonReachBottom: function () {\n\t\tif (this.loadingType == 'noMore') return;\n\t\tthis.pagenum++;\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function() {\n\t\tthis.getdata(true);\n\t\tsetTimeout(function() {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}, 1000);\n\t},\n\tmethods: {\n\t\tgetdata: function (isRefresh) {\n\t\t\tif (isRefresh) {\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.dataList = [];\n\t\t\t\tthis.loadingType = 'more';\n\t\t\t}\n\t\t\tthis.loading = true;\n\t\t\tthis.loadingType = 'loading';\n\t\t\tvar that = this;\n\t\t\tapp.post('/ApiYuyueWorker/getPromotionalProducts', { pagenum: this.pagenum, pernum: this.pernum }, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tvar data = res.data.list;\n\t\t\t\t\tif (isRefresh)\n\t\t\t\t\t\tthat.dataList = data;\n\t\t\t\t\telse\n\t\t\t\t\t\tthat.dataList = that.dataList.concat(data);\n\t\t\t\t\tthat.total = res.data.count;\n\t\t\t\t\tif (that.pagenum * that.pernum >= that.total) {\n\t\t\t\t\t\tthat.loadingType = 'noMore';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.loadingType = 'more';\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif(that.isload) app.alert(res.msg);\n\t\t\t\t\tthat.loadingType = 'noMore';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgeneratePoster: function (productId) {\n\t\t\tuni.showLoading({ title: '海报生成中...' });\n\t\t\tvar that = this;\n\t\t\tapp.post('/ApiYuyueWorker/getWorkerPoster', { proid: productId }, function (res) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tif (res.status == 1 && res.poster) {\n\t\t\t\t\tthat.posterImageUrl = res.poster;\n\t\t\t\t\tthat.showPosterPopup = true;\n\t\t\t\t} else {\n\t\t\t\t\tapp.alert(res.msg || '海报生成失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tclosePosterPopup: function () {\n\t\t\tthis.showPosterPopup = false;\n\t\t\tthis.posterImageUrl = '';\n\t\t},\n\t\tpreviewPoster: function () {\n\t\t\tif (this.posterImageUrl) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: [this.posterImageUrl]\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tsavePoster: function() {\n\t\t\tif (!this.posterImageUrl) return;\n\t\t\tuni.showLoading({ title: '保存中...' });\n\t\t\tvar that = this;\n\t\t\t// 下载海报到本地\n\t\t\tuni.downloadFile({\n\t\t\t\turl: this.posterImageUrl,\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t// 保存图片到相册\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tif (err.errMsg.indexOf('auth deny') >= 0) {\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: '需要您授权保存图片到相册',\n\t\t\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片下载失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: function() {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片下载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n        stopPrevent: function() {\n            // 阻止背景滚动\n        }\n\t}\n};\n</script>\n\n<style>\n.container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f7fa;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.header {\n\tpadding: 40rpx 30rpx;\n\tbox-sizing: border-box;\n\tcolor: #fff;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.title {\n\tfont-size: 40rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.subtitle {\n\tfont-size: 26rpx;\n\topacity: 0.8;\n\tdisplay: block;\n}\n\n.content {\n\tflex: 1;\n\tpadding: 20rpx;\n}\n\n.product-list {\n\twidth: 100%;\n}\n\n.product-item {\n\tdisplay: flex;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tmargin-bottom: 20rpx;\n\tpadding: 20rpx;\n\talign-items: center;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.product-image {\n\twidth: 160rpx;\n\theight: 160rpx;\n\tborder-radius: 12rpx;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.product-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\tmin-width: 0; /* 防止内容撑开 */\n\theight: 160rpx;\n\tpadding: 10rpx 0;\n}\n\n.product-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.product-price {\n\tfont-size: 28rpx;\n\tdisplay: flex;\n\talign-items: baseline;\n}\n\n.price-symbol {\n\tfont-size: 24rpx;\n}\n\n.price-integer {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tmargin: 0 2rpx;\n}\n\n.price-decimal {\n\tfont-size: 24rpx;\n}\n\n.product-sales {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.poster-button-wrapper {\n\tmargin-left: 20rpx;\n}\n\n.poster-button {\n\tpadding: 14rpx 20rpx;\n\tborder-radius: 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);\n}\n\n.button-text {\n\tcolor: #fff;\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n}\n\n.no-data {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding-top: 100rpx;\n}\n\n.no-data-image {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.no-data-text {\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n/* 海报弹窗样式 */\n.poster-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.popup-mask {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.7);\n}\n\n.poster-content {\n\twidth: 650rpx;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tposition: relative;\n\tz-index: 1000;\n\toverflow: hidden;\n\tbox-shadow: 0 10rpx 30rpx rgba(0,0,0,0.2);\n\tanimation: popup-in 0.3s ease-out;\n}\n\n@keyframes popup-in {\n\tfrom {\n\t\ttransform: scale(0.8);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t}\n}\n\n.poster-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.poster-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.close-icon {\n\tfont-size: 40rpx;\n\tline-height: 40rpx;\n\twidth: 40rpx;\n\theight: 40rpx;\n\ttext-align: center;\n\tcolor: #999;\n}\n\n.poster-image-preview {\n\twidth: 100%;\n\tpadding: 30rpx;\n\tbox-sizing: border-box;\n}\n\n.poster-tip {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 0 30rpx 30rpx;\n}\n\n.tip-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tborder-radius: 50%;\n\tfont-size: 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder: 2rpx solid;\n\tmargin-right: 10rpx;\n}\n\n.tip-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tborder-top: 1rpx solid #f0f0f0;\n}\n\n.save-button, .close-button {\n\tflex: 1;\n\tpadding: 25rpx 0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.save-button {\n\tcolor: #fff;\n\tfont-size: 28rpx;\n}\n\n.close-button {\n\tbackground-color: #f7f7f7;\n\tcolor: #666;\n\tfont-size: 28rpx;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promote.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promote.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115046783\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}