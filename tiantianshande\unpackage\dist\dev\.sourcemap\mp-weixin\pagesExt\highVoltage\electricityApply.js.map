{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?da4a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?1d3c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?773a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?d85e", "uni-app:///pagesExt/highVoltage/electricityApply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?b560", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/highVoltage/electricityApply.vue?5048"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formdata", "formData", "delta", "id", "onLoad", "methods", "getDetail", "app", "_this", "uni", "back", "commitSend", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACc;;;AAG7E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,gxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BzxB;AAAA,eACA;EACAC;IACA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;EACA;EACAC;IACAC;MACA;AACA;AACA;MACA;MACAC,SACA,kCACA;QACAJ;MACA,GACA;QAAA;QACAK;QACAC;MACA,EACA;IACA;IACAC;MACA;AACA;AACA;MACAD;IACA;IACAE;MACA;AACA;AACA;MACA;MACAJ;QAAA;QACAE;UACAG;UACAC;QACA;QACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAA46C,CAAgB,u3CAAG,EAAC,C;;;;;;;;;;;ACAh8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/highVoltage/electricityApply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/highVoltage/electricityApply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./electricityApply.vue?vue&type=template&id=4e400944&\"\nvar renderjs\nimport script from \"./electricityApply.vue?vue&type=script&lang=js&\"\nexport * from \"./electricityApply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./electricityApply.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/highVoltage/electricityApply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./electricityApply.vue?vue&type=template&id=4e400944&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./electricityApply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./electricityApply.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"header\" style=\"height: 260rpx\">\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"title-box\">\r\n\t\t\t\t\t<view class=\"title\">高压办电</view>\r\n\t\t\t\t\t<view>帮代办、少跑腿</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image src=\"@/static/highVoltage/icon1.png\" class=\"logo\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"form\">\r\n\t\t\t<view class=\"form-item box\" v-for=\"(value, key) in formdata\" :key=\"key\">\r\n\t\t\t\t<view class=\"title\">{{ value[0] }}</view>\r\n\t\t\t\t<view class=\"value\">{{ value[1] }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item box\">\r\n\t\t\t\t<view class=\"title\">施工单位</view>\r\n\t\t\t\t<input class=\"input\" placeholder=\"请输入\" v-model=\"formData.shigongdanwei\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item box\">\r\n\t\t\t\t<view class=\"title\">报验内容</view>\r\n\t\t\t\t<textarea class=\"textarea input\" v-model=\"formData.baoyanneirong\" placeholder=\"请输入\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @click=\"commitSend\">申请送电</view>\r\n\t\t<view class=\"btn back-i\" @click=\"back\">返回</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tformdata: [],\r\n\t\t\t// 申请送电表单\r\n\t\t\tformData: {},\r\n\t\t\tdelta: 1,\r\n\t\t\tid: null\r\n\t\t};\r\n\t},\r\n\tonLoad({ id }) {\r\n\t\tthis.id = id;\r\n\t\tthis.getDetail();\r\n\t},\r\n\tmethods: {\r\n\t\tgetDetail() {\r\n\t\t\t/**\r\n\t\t\t * @method  获取详情\r\n\t\t\t */\r\n\t\t\tconst _this = this;\r\n\t\t\tapp.post(\r\n\t\t\t\t'ApiShenqingbandian/orderdetail',\r\n\t\t\t\t{\r\n\t\t\t\t\tid: this.id\r\n\t\t\t\t},\r\n\t\t\t\t({ detail }) => {\r\n\t\t\t\t\t_this.formdata = detail.formdata;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t);\r\n\t\t},\r\n\t\tback() {\r\n\t\t\t/**\r\n\t\t\t * @method 返回上一页\r\n\t\t\t */\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\tcommitSend() {\r\n\t\t\t/**\r\n\t\t\t * @method 申请送电\r\n\t\t\t */\r\n\t\t\tconst _this = this;\r\n\t\t\tapp.post('ApiShenqingbandian/orderCollect', this.formData, ({ msg }) => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t_this.getDetail();\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import 'styled.scss';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./electricityApply.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./electricityApply.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115099200\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}