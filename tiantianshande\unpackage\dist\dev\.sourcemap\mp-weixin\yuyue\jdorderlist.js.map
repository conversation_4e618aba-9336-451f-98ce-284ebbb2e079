{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?21cf", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?6405", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?da73", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?49a2", "uni-app:///yuyue/jdorderlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?8b03", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/jdorderlist.vue?e684"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "keyword", "interval1", "timestamp", "showform", "showtabbar", "showaddmoney", "showmodal", "addprice", "showpaycodes", "paycode", "add<PERSON><PERSON><PERSON><PERSON>", "showPunchModal", "punchId", "punchSt", "punch<PERSON>itle", "punchPhotoType", "punchLocationInfo", "punchPhotos", "isLocating", "statusType", "computed", "canSubmitPunch", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "uni", "scrollTop", "duration", "getdata", "console", "that", "mid", "app", "updatemylocation", "thisdata", "longitude", "latitude", "t", "getdistance", "juli", "unit", "setst", "openPunchModal", "title", "photoType", "closePunchModal", "getPunchLocation", "icon", "content", "confirmText", "cancelText", "success", "selectPunchPhoto", "reselectPunchPhoto", "remove<PERSON>unch<PERSON><PERSON><PERSON>", "previewPunchPhoto", "urls", "current", "submitPunchData", "id", "params", "mask", "showCancel", "setTimeout", "formatDistance", "distance", "searchConfirm", "addmoney", "cancel", "<PERSON><PERSON><PERSON>", "addconfirm", "price", "addmoneyPayorderid", "showpaycode", "update", "da<PERSON><PERSON>", "itemList", "name", "address", "scale", "fail", "switchStatusType", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5XA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4PpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;IAEA;MACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACAC;MAEA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MAEA;QACAvC;QACAE;QACAG;QACAmC;QACAhB;MACA;MAEAc;MAEAG;QACAH;QAEA;UACAA;UACAG;UACA;QACA;QACA;QACA;UACAH;UACAC;UACAA;UACAA;UACAA;UACA;YACAA;YACAD;UACA;UACAC;UACAA;UACAV;UACAU;YACAA;YACAA;UACA;QACA;UACAD;UACA;YACAC;YACAD;UACA;YACA;YACA;YACAC;YACAD;UACA;QACA;MACA;IACA;IACAI;MACA;MACAD;QACA;QACA;QACA;QACAH;QACA;UACA;UACA;UACAK;UACAA;UACAA;UACA1C;QACA;QACAsC;QACAA;QACAE;UAAAG;UAAAC;UAAAC;QAAA;UACA;QAAA,CACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACAD;MACA;QAAAA;QAAAC;MAAA;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACAX;IACA;IAEA;IACAY;MACA;MACA;MACA;MAEAb;;MAEA;MACA;MACA;MAEA;QACA;UACAc;UACAC;QACA;UACAD;UACAC;QACA;MACA;QACAD;QACAC;MACA;;MAEA;MACAd;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAA;IACA;IAEA;IACAe;MACA;IACA;IAEA;IACAC;MACA;MAEAhB;MAEAD;MAEAG;QACAH;QAEAC;QACAA;UACAK;UACAC;QACA;;QAEA;QACAX;UACAkB;UACAI;UACApB;QACA;MACA;QACAE;QAEAC;;QAEA;QACAL;UACAkB;UACAK;UACAC;UACAC;UACAC;YACA;cACArB;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsB;MACA;MAEAvB;;MAEA;MACAG;QACAH;;QAEA;QACAC;;QAEA;QACAL;UACAkB;UACAI;UACApB;QACA;MACA;IACA;IAEA;IACA0B;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAzB;MAEA;QACA;QACAA;MACA;IACA;IAEA;IACA0B;MACA;MACA;QACA9B;UACA+B;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACAjC;UACAkB;UACAI;UACApB;QACA;QACA;MACA;MAEAE;;MAEA;MACA;QACA8B;QACApE;QACA4C;QACAC;MACA;;MAEA;MACA;QACAwB;MACA;QACAA;MACA;MAEA/B;;MAEA;MACAJ;QACAkB;QACAkB;MACA;;MAEA;MACA7B;QACAP;QACAI;QAEA;UACA;UACAC;;UAEA;UACAE;;UAEA;UACA;UACA;UAEA;YACA;cACAW;cACAK;YACA;cACAL;cACAK;YACA;UACA;YACAL;YACAK;UACA;;UAEA;UACA;YACAA;UACA;;UAEA;UACAvB;YACAkB;YACAK;YACAc;YACAX;cACA;cACAY;gBACAjC;cACA;YACA;UACA;QACA;UACA;UACAE;QACA;MACA;IACA;IAEA;IACAgC;MACA;QACA;MACA;MAEAC;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACApC;MACAA;IACA;IACAqC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAvC;IACA;IACAwC;MACA;MACA;QACAtC;QACA;MACA;MACAA;QAAA2B;QAAAY;QAAAC;MAAA;QACAxC;QACAA;QACA;UACAF;UACAA;QACA;MACA;IACA;IACA2C;MACA;MACA;MACA;MACA3C;MACA;MACA;MACA;MACA;MACA;IACA;IACA4C;MACA;MACA;MACA;MACA;MACA5C;IAEA;IACA6C;MACA;MACA;MACA;MACAlD;QACAmD;QACAzB;UACA;YACA;cACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;cACA;YACA;YACA1B;cACAW;cACAD;cACA0C;cACAC;cACAC;cACA5B;gBACAtB;cACA;cACAmD;gBACAnD;cACA;YACA;UACA;QACA;MACA;IACA;IACAoD;MACApD;MAEA;QACAA;QACA;MACA;;MAEA;MAEAA;;MAEA;MACA;MACA;QACAqD;MACA;MACA;QACAA;MACA;MAEArD;;MAEA;MACA;MACA;MACA;QACAJ;UACAyD;QACA;MACA;;MAEA;MACAzD;QACAC;QACAC;MACA;;MAEA;MACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrzBA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/jdorderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/jdorderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jdorderlist.vue?vue&type=template&id=4b888a72&\"\nvar renderjs\nimport script from \"./jdorderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./jdorderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jdorderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/jdorderlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderlist.vue?vue&type=template&id=4b888a72&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.statusType === \"waiting\" ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.statusType === \"waiting\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.statusType === \"serving\" ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload && _vm.statusType === \"serving\" ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload && _vm.statusType === \"completed\" ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload && _vm.statusType === \"completed\" ? _vm.t(\"color1\") : null\n  var m6 =\n    _vm.isload && _vm.statusType === \"transferred\" ? _vm.t(\"color1\") : null\n  var m7 =\n    _vm.isload && _vm.statusType === \"transferred\" ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.statusType === \"all\" ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload && _vm.statusType === \"all\" ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m10 = item.fwtype == 1 && item.status == 3 ? _vm.t(\"color1\") : null\n        var m11 = item.fwtype == 1 && item.status == 1 ? _vm.t(\"color1\") : null\n        var m12 = item.fwtype == 1 && item.status == 2 ? _vm.t(\"color1\") : null\n        var m13 =\n          !(item.fwtype == 1) && item.fwtype == 2 && item.status == 3\n            ? _vm.t(\"color1\")\n            : null\n        var m14 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !(item.status == 3) &&\n          item.status == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m15 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !(item.status == 3) &&\n          !(item.status == 1) &&\n          item.status == 2 &&\n          _vm.showaddmoney &&\n          !item.sign_status\n            ? _vm.t(\"color1\")\n            : null\n        var m16 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !(item.status == 3) &&\n          !(item.status == 1) &&\n          item.status == 2 &&\n          _vm.showaddmoney &&\n          !!item.sign_status\n            ? _vm.t(\"color1\")\n            : null\n        var m17 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !(item.status == 3) &&\n          !(item.status == 1) &&\n          item.status == 2 &&\n          !_vm.showaddmoney\n            ? _vm.t(\"color1\")\n            : null\n        var m18 = item.fwtype == 1 && item.status == 1 ? _vm.t(\"color1\") : null\n        var m19 = item.fwtype == 1 && item.status == 2 ? _vm.t(\"color1\") : null\n        var m20 = item.fwtype == 1 && item.status == 3 ? _vm.t(\"color1\") : null\n        var m21 = item.fwtype == 1 && item.status == 1 ? _vm.t(\"color1\") : null\n        var m22 =\n          item.fwtype == 1 && item.status == 1 ? _vm.t(\"color1rgb\") : null\n        var m23 = item.fwtype == 1 && item.status == 2 ? _vm.t(\"color1\") : null\n        var m24 =\n          item.fwtype == 1 && item.status == 2 ? _vm.t(\"color1rgb\") : null\n        var m25 =\n          !(item.fwtype == 1) && item.fwtype == 2 && item.status == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m26 =\n          !(item.fwtype == 1) && item.fwtype == 2 && item.status == 2\n            ? _vm.t(\"color1\")\n            : null\n        var m27 =\n          !(item.fwtype == 1) && item.fwtype == 2 && item.status == 3\n            ? _vm.t(\"color1\")\n            : null\n        var m28 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.sign_status == 1 &&\n          item.status == 2 &&\n          item.addprice <= 0\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m29 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.sign_status == 1 &&\n          item.status == 2 &&\n          item.addprice <= 0\n            ? _vm.t(\"color1\")\n            : null\n        var m30 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.sign_status == 1 &&\n          item.status == 2 &&\n          item.addprice <= 0\n            ? _vm.t(\"color1\")\n            : null\n        var m31 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.status == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m32 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.status == 1\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m33 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.addprice > 0\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m34 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.addprice > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m35 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.addprice > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m36 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          !item.sign_status &&\n          item.status == 2\n            ? _vm.t(\"color1\")\n            : null\n        var m37 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          !item.sign_status &&\n          item.status == 2\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m38 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.sign_status == 1 &&\n          item.status == 2\n            ? _vm.t(\"color1\")\n            : null\n        var m39 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          _vm.showaddmoney &&\n          item.sign_status == 1 &&\n          item.status == 2\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m40 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !_vm.showaddmoney &&\n          item.status == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m41 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !_vm.showaddmoney &&\n          item.status == 1\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m42 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !_vm.showaddmoney &&\n          item.status == 2\n            ? _vm.t(\"color1\")\n            : null\n        var m43 =\n          !(item.fwtype == 1) &&\n          item.fwtype == 2 &&\n          !_vm.showaddmoney &&\n          item.status == 2\n            ? _vm.t(\"color1rgb\")\n            : null\n        return {\n          $orig: $orig,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n          m16: m16,\n          m17: m17,\n          m18: m18,\n          m19: m19,\n          m20: m20,\n          m21: m21,\n          m22: m22,\n          m23: m23,\n          m24: m24,\n          m25: m25,\n          m26: m26,\n          m27: m27,\n          m28: m28,\n          m29: m29,\n          m30: m30,\n          m31: m31,\n          m32: m32,\n          m33: m33,\n          m34: m34,\n          m35: m35,\n          m36: m36,\n          m37: m37,\n          m38: m38,\n          m39: m39,\n          m40: m40,\n          m41: m41,\n          m42: m42,\n          m43: m43,\n        }\n      })\n    : null\n  var m44 = _vm.isload && _vm.showtabbar && _vm.st != 3 ? _vm.t(\"color1\") : null\n  var m45 = _vm.isload && _vm.showtabbar && _vm.st == 3 ? _vm.t(\"color1\") : null\n  var m46 = _vm.isload && _vm.showmodal ? _vm.t(\"color1\") : null\n  var m47 = _vm.isload && _vm.showmodal ? _vm.t(\"color1rgb\") : null\n  var m48 =\n    _vm.isload && _vm.showpaycodes && _vm.addmoneystatus == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m49 =\n    _vm.isload && _vm.showpaycodes && _vm.addmoneystatus == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m50 =\n    _vm.isload && _vm.showpaycodes && _vm.addmoneystatus == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m51 = _vm.isload && _vm.showPunchModal ? _vm.t(\"color1\") : null\n  var m52 =\n    _vm.isload && _vm.showPunchModal && _vm.punchLocationInfo\n      ? _vm.t(\"color1\")\n      : null\n  var m53 =\n    _vm.isload && _vm.showPunchModal && _vm.punchLocationInfo\n      ? _vm.t(\"color1\")\n      : null\n  var m54 =\n    _vm.isload &&\n    _vm.showPunchModal &&\n    !_vm.punchLocationInfo &&\n    !_vm.isLocating &&\n    !_vm.punchLocationInfo\n      ? _vm.t(\"color1\")\n      : null\n  var m55 =\n    _vm.isload &&\n    _vm.showPunchModal &&\n    !_vm.punchLocationInfo &&\n    !_vm.isLocating &&\n    !_vm.punchLocationInfo\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g0 = _vm.isload && _vm.showPunchModal ? _vm.punchPhotos.length : null\n  var g1 = _vm.isload && _vm.showPunchModal ? _vm.punchPhotos.length : null\n  var g2 = _vm.isload && _vm.showPunchModal ? _vm.punchPhotos.length : null\n  var m56 =\n    _vm.isload && _vm.showPunchModal && _vm.canSubmitPunch\n      ? _vm.t(\"color1\")\n      : null\n  var m57 =\n    _vm.isload && _vm.showPunchModal && _vm.canSubmitPunch\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        l0: l0,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m56: m56,\n        m57: m57,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderlist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\" :style=\"{backgroundColor: '#f7f8fa'}\">\n\t<block v-if=\"isload\">\n\t\t<view>\n\t\t\t<!-- 添加状态切换栏 -->\n\t\t\t<view class=\"status-tabs\" :style=\"{backgroundColor: '#fff', boxShadow: '0 2rpx 10rpx rgba(0,0,0,0.05)'}\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ active: statusType === 'waiting' }\" \n\t\t\t\t\t@tap=\"switchStatusType('waiting')\"\n\t\t\t\t\t:style=\"statusType === 'waiting' ? {color: t('color1'), borderBottomColor: t('color1')} : null\"\n\t\t\t\t>待服务</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ active: statusType === 'serving' }\" \n\t\t\t\t\t@tap=\"switchStatusType('serving')\"\n\t\t\t\t\t:style=\"statusType === 'serving' ? {color: t('color1'), borderBottomColor: t('color1')} : null\"\n\t\t\t\t>服务中</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ active: statusType === 'completed' }\" \n\t\t\t\t\t@tap=\"switchStatusType('completed')\"\n\t\t\t\t\t:style=\"statusType === 'completed' ? {color: t('color1'), borderBottomColor: t('color1')} : null\"\n\t\t\t\t>已完成</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ active: statusType === 'transferred' }\" \n\t\t\t\t\t@tap=\"switchStatusType('transferred')\"\n\t\t\t\t\t:style=\"statusType === 'transferred' ? {color: t('color1'), borderBottomColor: t('color1')} : null\"\n\t\t\t\t>被转派</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ active: statusType === 'all' }\" \n\t\t\t\t\t@tap=\"switchStatusType('all')\"\n\t\t\t\t\t:style=\"statusType === 'all' ? {color: t('color1'), borderBottomColor: t('color1')} : null\"\n\t\t\t\t>全部</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-container\" :style=\"{boxShadow: '0 2rpx 10rpx rgba(0,0,0,0.05)'}\">\n\t\t\t\t<view class=\"search-box\" :style=\"{backgroundColor: '#f0f2f5'}\">\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t\t<input class=\"search-text\" placeholder=\"搜索商家\" placeholder-style=\"color:#aaa;font-size:24rpx\" @confirm=\"searchConfirm\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"item.id\">\n\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'jdorderdetail?id=' + item.id\" :style=\"{boxShadow: '0 2rpx 12rpx rgba(0,0,0,0.03)'}\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"status-container\">\n\t\t\t\t\t\t<view v-if=\"item.fwtype==1\">\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==3\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: t('color1')}\">已完成</text></view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text class=\"t1\" :style=\"{color: t('color1')}\">等待客户上门</text> </view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==2\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text class=\"t1\" :style=\"{color: t('color1')}\">服务中</text></view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==-1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: '#ff4d4f'}\">已取消</text></view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==-2\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: '#f57c00'}\">已改派</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.fwtype==2\">\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==3\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: t('color1')}\">已完成</text></view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-else-if=\"item.status==1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>期望上门时间<text class=\"t1\" :style=\"{color: t('color1')}\">{{item.orderinfo.yydate}}</text> </view>\n\t\t\t\t\t\t\t<block v-else-if=\"item.status==2\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"showaddmoney\">\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"!item.sign_status\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: t('color1')}\">已到达，等待服务</text>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t<block v-else=\"!item.sign_status\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: t('color1')}\">已到达，正在服务</text>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-else><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: t('color1')}\">已到达，服务中</text></view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==-1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: '#ff4d4f'}\">已取消</text></view>\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==-2\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text :style=\"{color: '#f57c00'}\">已改派</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-num\">订单号: {{item.ordernum}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"f2\" :style=\"{color: '#ff7a45'}\"><text class=\"t1\">{{item.ticheng}}</text>元</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<view class=\"t1\"><text class=\"x1\" :style=\"{color: '#ff7a45'}\">{{item.juli}}</text><text class=\"x2\">{{item.juli_unit}}</text></view>\n\t\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1\" :style=\"{color: '#ff7a45'}\">{{item.juli2}}</text><text class=\"x2\">{{item.juli2_unit}}</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<view class=\"t1\">{{item.binfo.name}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{item.binfo.address}}</view>\n\t\t\t\t\t\t<view class=\"t3\">{{item.orderinfo.address}}</view>\n\t\t\t\t\t\t<view class=\"t2\">{{item.orderinfo.area}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\" :data-index=\"index\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"item.fwtype==1\" class=\"op\">\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==1\" :style=\"{color: t('color1')}\">已接单，待顾客上门</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==2\" :style=\"{color: t('color1')}\">顾客已到达</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==3\" :style=\"{color: t('color1')}\">已完成</view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">顾客已到达</view>\n\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\" v-if=\"item.status==2\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">我已完成</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else-if=\"item.fwtype==2\" class=\"op\">\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==1\" :style=\"{color: t('color1')}\">已接单，等待上门</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==2\" :style=\"{color: t('color1')}\">已到达，共用时{{item.useminute}}分钟</view>\n\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==3\" :style=\"{color: t('color1')}\">已完成</view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\n\t\t\t\t\t<block v-if=\"showaddmoney\">\n\t\t\t\t\t\t\t<view class=\"btn1 btn2\" @tap.stop=\"addmoney\" v-if=\"item.sign_status==1 && item.status==2 && item.addprice<=0\" :data-id=\"item.id\" :style=\"{backgroundColor: 'rgba('+t('color1rgb')+',0.1)', color: t('color1'), borderColor: t('color1')}\">补差价</view>\n\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">出发</view>\n\t\t\t\t\t\t\t<view class=\"btn1 btn2\" @tap.stop=\"showpaycode\" v-if=\"item.addprice>0\" :data-id=\"item.id\" :data-key=\"index\" :style=\"{backgroundColor: 'rgba('+t('color1rgb')+',0.1)', color: t('color1'), borderColor: t('color1')}\">查看补余款</view>\n\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"5\" v-if=\"!item.sign_status && item.status==2\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">开始服务</view>\n\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\" v-if=\"item.sign_status==1 && item.status==2\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">服务完成</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">我已到达</view>\n\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\" v-if=\"item.status==2\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">我已完成</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\t\t\n\t\t<view class=\"tabbar\" v-if=\"showtabbar\">\n\t\t\t<view class=\"tabbar-bot\"></view>\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\n\t\t\t\t<view @tap=\"goto\" data-url=\"dating\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/home.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">大厅</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\" :class=\"st!=3?'active':''\" :style=\"st!=3? {color: t('color1')} : null\">订单</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist?st=3\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\" :class=\"st==3?'active':''\" :style=\"st==3? {color: t('color1')} : null\">已完成</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"showform\" @tap=\"goto\" data-url=\"formlog\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/dangan.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">档案</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"my\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/my.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">我的</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"modal\" v-if=\"showmodal\">\n\t\t\t<view class=\"addmoney\" :style=\"{borderRadius: '16rpx', boxShadow: '0 8rpx 20rpx rgba(0,0,0,0.1)'}\">\n\t\t\t\t\t<view class=\"title\" :style=\"{borderBottom: '1rpx solid #f0f0f0'}\">{{addprice>0?'修改':'创建'}}补余款</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<label class=\"label\">金额：</label><input type=\"text\" @input=\"bindMoney\" name=\"blance_price\" :value=\"addprice\" placeholder=\"请输入补余款金额\"  placeholder-style=\"font-size:24rpx\"/>元\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn\"><button class=\"btn-cancel\" @tap=\"cancel\" :style=\"{borderRadius: '50rpx'}\">取消</button><button class=\"confirm\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx'\"  @tap.stop=\"addconfirm\">确定</button></view>\n\t\t\t</view>\n\t\t</view>\t\n\t\t\n\t\t<view class=\"modal\" v-if=\"showpaycodes\">\n\t\t\t<view class=\"addmoney\" :style=\"{borderRadius: '16rpx', boxShadow: '0 8rpx 20rpx rgba(0,0,0,0.1)'}\">\n\t\t\t\t\t<view class=\"title\" :style=\"{borderBottom: '1rpx solid #f0f0f0'}\">查看补余款</view>\n\t\t\t\t\t<view class=\"item\" >\n\t\t\t\t\t\t<label>金额：</label><text class=\"price\">{{addprice}}</text>元\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\" style=\"padding-top: 0;\">\n\t\t\t\t\t\t<label>支付状态：</label> <text class=\"t2\" v-if=\"addmoneystatus==1\" :style=\"{color: t('color1')}\"> 已支付</text> \n\t\t\t\t\t\t<text class=\"t2\" v-if=\"addmoneystatus==0\" style=\"color:#ff4d4f;\"> 待支付</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qrcode\"><image :src=\"paycode\"></image></view>\n\t\t\t\t\t<view class=\"btn\"><button class=\"btn-cancel\" @tap=\"cancel\" :style=\"{borderRadius: '50rpx'}\">关闭</button> \n\t\t\t\t\t\n\t\t\t\t\t<button class=\"btn-update\" v-if=\"addmoneystatus==0\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx'\"  @tap=\"update\" :data-key=\"index\" :data-id=\"id\"  >修改差价</button></view>\n\t\t\t</view>\n\t\t</view>\t\n\t\t\n\t\t<!-- 添加打卡弹窗 -->\n\t\t<view class=\"punch-modal\" v-if=\"showPunchModal\">\n\t\t\t<view class=\"punch-content\" :style=\"{borderRadius: '16rpx', boxShadow: '0 10rpx 30rpx rgba(0,0,0,0.15)'}\">\n\t\t\t\t<view class=\"punch-header\">\n\t\t\t\t\t<text class=\"punch-title\" :style=\"{color: t('color1')}\">{{punchTitle}}</text>\n\t\t\t\t\t<view class=\"close-btn\" @tap=\"closePunchModal\">×</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 位置信息区域 -->\n\t\t\t\t<view class=\"location-section\" :style=\"{backgroundColor: '#f8f8fa'}\">\n\t\t\t\t\t<view class=\"section-title\"><text class=\"icon\">📍</text> 位置信息</view>\n\t\t\t\t\t<view class=\"location-content\" v-if=\"punchLocationInfo\">\n\t\t\t\t\t\t<view class=\"location-status success\" :style=\"{borderLeftColor: t('color1')}\">\n\t\t\t\t\t\t\t<text class=\"status-text\" :style=\"{color: t('color1')}\">已获取位置信息</text>\n\t\t\t\t\t\t\t<text class=\"location-detail\">经度: {{punchLocationInfo.longitude}}</text>\n\t\t\t\t\t\t\t<text class=\"location-detail\">纬度: {{punchLocationInfo.latitude}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"location-content\" v-else>\n\t\t\t\t\t\t<view class=\"location-status\" :class=\"{'loading': isLocating}\">\n\t\t\t\t\t\t\t<text class=\"status-text\">{{isLocating ? '获取位置中...' : '点击获取位置'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"get-location-btn\" @tap=\"getPunchLocation\" v-if=\"!isLocating && !punchLocationInfo\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">\n\t\t\t\t\t\t\t获取位置信息\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 照片上传区域 -->\n\t\t\t\t<view class=\"photo-section\" :style=\"{backgroundColor: '#f8f8fa'}\">\n\t\t\t\t\t<view class=\"section-title\"><text class=\"icon\">📷</text> {{punchPhotoType}} ({{punchPhotos.length}}/9)</view>\n\t\t\t\t\t<view class=\"photo-content\">\n\t\t\t\t\t\t<!-- 已选择的照片列表 -->\n\t\t\t\t\t\t<view class=\"photo-list\" v-if=\"punchPhotos.length > 0\">\n\t\t\t\t\t\t\t<view class=\"photo-item\" v-for=\"(photo, index) in punchPhotos\" :key=\"index\">\n\t\t\t\t\t\t\t\t<image :src=\"photo\" class=\"preview-image\" mode=\"aspectFill\" @tap=\"previewPunchPhoto\" :data-url=\"photo\"></image>\n\t\t\t\t\t\t\t\t<view class=\"remove-icon\" @tap=\"removePunchPhoto\" :data-index=\"index\">×</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 上传按钮（当照片数量小于9时显示） -->\n\t\t\t\t\t\t<view class=\"photo-placeholder\" @tap=\"selectPunchPhoto\" v-if=\"punchPhotos.length < 9\">\n\t\t\t\t\t\t\t<text class=\"placeholder-icon\">+</text>\n\t\t\t\t\t\t\t<text class=\"placeholder-text\">点击上传照片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"punch-actions\">\n\t\t\t\t\t<view class=\"cancel-btn\" @tap=\"closePunchModal\" :style=\"{borderRadius: '50rpx'}\">取消</view>\n\t\t\t\t\t<view class=\"submit-btn\" @tap=\"submitPunchData\" :class=\"{'disabled': !canSubmitPunch}\" :style=\"canSubmitPunch ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx' : 'border-radius:50rpx'\">\n\t\t\t\t\t\t确认提交\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<view style=\"display:none\">{{timestamp}}</view>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\n      st: '11',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n      nodata: false,\n\t\t\tkeyword:'',\n\t\t\tinterval1:null,\n\t\t\ttimestamp:'',\n\t\t\tshowform:0,\n\t\t\tshowtabbar:false,\n\t\t\tshowaddmoney:false,\n\t\t\tshowmodal:false,\n\t\t\taddprice:0,\n\t\t\tshowpaycodes:false,\n\t\t\tpaycode:'',\n\t\t\taddmoneystatus:0,\n\t\t\t\n\t\t\t// 打卡弹窗相关\n\t\t\tshowPunchModal: false,\n\t\t\tpunchId: null,\n\t\t\tpunchSt: null,\n\t\t\tpunchTitle: '',\n\t\t\tpunchPhotoType: '',\n\t\t\tpunchLocationInfo: null,\n\t\t\tpunchPhotos: [],\n\t\t\tisLocating: false,\n\t\t\tstatusType: 'waiting'\n    };\n  },\n  computed: {\n    // 是否可以提交打卡\n    canSubmitPunch: function() {\n      return this.punchLocationInfo && this.punchPhotos.length > 0;\n    }\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || '11';\n\t\t\n\t\t// 初始化状态类型\n\t\tthis.statusType = this.opt.statusType || 'waiting';\n\t\t\n\t\tif(this.opt.mid){\n\t\t\tthis.showtabbar = false;\n\t\t}else{\n\t\t\tthis.showtabbar = true;\n\t\t}\n\t\tthis.getdata();\n  },\n\tonUnload:function(){\n\t\tclearInterval(this.interval1);\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    getdata: function (loadmore) {\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_001]开始获取数据，loadmore:\", loadmore, \"当前状态类型:\", this.statusType);\n\t\t\t\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\t\n\t\t\tvar requestParams = {\n\t\t\t\tst: st,\n\t\t\t\tpagenum: pagenum,\n\t\t\t\tkeyword: keyword,\n\t\t\t\tmid: this.opt.mid,\n\t\t\t\tstatusType: this.statusType\n\t\t\t};\n\t\t\t\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_002]请求参数:\", JSON.stringify(requestParams));\n\t\t\t\n      app.post('ApiYuyueWorker/orderlist', requestParams, function (res) {\n\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_003]接口响应状态:\", res.status, \"数据长度:\", res.datalist ? res.datalist.length : 0);\n\t\t\t\t\n\t\t\t\tif(res.status==0){\n\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-ERROR-[jdorderlist][getdata_004]接口返回错误:\", res.msg);\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_005]首页数据加载，数据条数:\", data.length);\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.nowtime = res.nowtime\n\t\t\t\t\tthat.showform = res.showform;\n\t\t\t\t\tthat.showaddmoney = res.addmoney\n          if (data.length == 0) {\n            that.nodata = true;\n\t\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_006]无数据显示\");\n          }\n\t\t\t\t\tthat.loaded();\n\t\t\t\t\tthat.updatemylocation();\n\t\t\t\t\tclearInterval(that.interval1);\n\t\t\t\t\tthat.interval1 = setInterval(function(){\n\t\t\t\t\t\tthat.updatemylocation(true);\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 10;\n\t\t\t\t\t},10000)\n        }else{\n\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_007]分页数据加载，数据条数:\", data.length);\n          if (data.length == 0) {\n            that.nomore = true;\n\t\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_008]没有更多数据\");\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n\t\t\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_009]数据合并完成，总条数:\", newdata.length);\n          }\n        }\n      });\n    },\n\t\tupdatemylocation:function(){\n\t\t\tvar that = this;\n\t\t\tapp.getLocation(function(res){\n\t\t\t\tvar longitude = res.longitude;\n\t\t\t\tvar latitude = res.latitude;\n\t\t\t\tvar datalist = that.datalist;\n\t\t\t\tconsole.log(datalist);\n\t\t\t\tfor(var i in datalist){\n\t\t\t\t\tvar thisdata = datalist[i];\n\t\t\t\t\tvar rs = that.getdistance(thisdata.longitude2,thisdata.latitude2,longitude,latitude,1);\n\t\t\t\t\tthisdata.juli2 = rs.juli;\n\t\t\t\t\tthisdata.juli2_unit = rs.unit;\n\t\t\t\t\tthisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);\n\t\t\t\t\tdatalist[i] = thisdata;\n\t\t\t\t}\n\t\t\t\tthat.datalist = datalist;\n\t\t\t\tthat.timestamp = parseInt((new Date().getTime())/1000);\n\t\t\t\tapp.get('ApiYuyueWorker/updatemylocation',{longitude:longitude,latitude:latitude,t:that.timestamp},function(){\n\t\t\t\t\t//if(needload) that.getdata();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tgetdistance: function (lng1, lat1, lng2, lat2) {\n\t\t\tif(!lat1 || !lng1 || !lat2 || !lng2) return '';\n\t\t\tvar rad1 = lat1 * Math.PI / 180.0;\n\t\t\tvar rad2 = lat2 * Math.PI / 180.0;\n\t\t\tvar a = rad1 - rad2;\n\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n\t\t\tvar r = 6378137;\n\t\t\tvar juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));\n\t\t\tvar unit = 'm';\n\t\t\tif(juli> 1000){\n\t\t\t\tjuli = juli/1000;\n\t\t\t\tunit = 'km';\n\t\t\t}\n\t\t\tjuli = juli.toFixed(1);\n\t\t\treturn {juli:juli,unit:unit}\n\t\t},\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      \n      // 通过打开弹窗代替直接处理\n      that.openPunchModal(e);\n    },\n    \n    // 打开打卡弹窗\n    openPunchModal: function(e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][openPunchModal_001]打开打卡弹窗，id:\", id, \"状态:\", st);\n      \n      // 设置打卡类型和标题\n      var title = '';\n      var photoType = '';\n      \n      if(st == 2){\n        if(that.showaddmoney){\n          title = '出发打卡';\n          photoType = '出发前照片';\n        } else {\n          title = '到达打卡';\n          photoType = '到达现场照片';\n        }\n      } else if(st == 3 || st == 5) {\n        title = '完成打卡';\n        photoType = '服务完成照片';\n      }\n      \n      // 重置打卡状态\n      that.punchId = id;\n      that.punchSt = st;\n      that.punchTitle = title;\n      that.punchPhotoType = photoType;\n      that.punchLocationInfo = null;\n      that.punchPhotos = [];\n      that.showPunchModal = true;\n      \n      // 自动开始获取位置\n      that.getPunchLocation();\n    },\n    \n    // 关闭打卡弹窗\n    closePunchModal: function() {\n      this.showPunchModal = false;\n    },\n    \n    // 获取打卡位置\n    getPunchLocation: function() {\n      var that = this;\n      \n      that.isLocating = true;\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_001]开始获取位置\");\n      \n      app.getLocation(function(locRes) {\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_002]位置获取成功:\", JSON.stringify(locRes));\n        \n        that.isLocating = false;\n        that.punchLocationInfo = {\n          longitude: locRes.longitude,\n          latitude: locRes.latitude\n        };\n        \n        // 显示位置获取成功提示\n        uni.showToast({\n          title: '位置获取成功',\n          icon: 'success',\n          duration: 1500\n        });\n      }, function(err) {\n        console.log(\"2025-01-03 22:55:53,565-ERROR-[jdorderlist][getPunchLocation_003]位置获取失败:\", JSON.stringify(err));\n        \n        that.isLocating = false;\n        \n        // 提示重试\n        uni.showModal({\n          title: '位置获取失败',\n          content: '请检查是否授予定位权限，并重试',\n          confirmText: '重试',\n          cancelText: '取消',\n          success: function(res) {\n            if(res.confirm) {\n              that.getPunchLocation();\n            }\n          }\n        });\n      });\n    },\n    \n    // 选择打卡照片\n    selectPunchPhoto: function() {\n      var that = this;\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_001]开始选择照片\");\n      \n      // 使用系统标准的chooseImage方法\n      app.chooseImage(function(imageUrls) {\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_002]照片上传成功:\", imageUrls);\n        \n        // 将上传成功的图片URL添加到照片列表\n        that.punchPhotos = that.punchPhotos.concat(imageUrls);\n        \n        // 提示上传成功\n        uni.showToast({\n          title: '照片上传成功',\n          icon: 'success',\n          duration: 1500\n        });\n      }, 9 - that.punchPhotos.length);\n    },\n    \n    // 重新选择照片\n    reselectPunchPhoto: function(e) {\n      var index = e.currentTarget.dataset.index;\n      this.punchPhotos.splice(index, 1);\n    },\n    \n    // 移除指定照片\n    removePunchPhoto: function(e) {\n      var index = e.currentTarget.dataset.index;\n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_001]移除照片，索引:\", index);\n      \n      if (index >= 0 && index < this.punchPhotos.length) {\n        this.punchPhotos.splice(index, 1);\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_002]照片移除成功，剩余数量:\", this.punchPhotos.length);\n      }\n    },\n    \n    // 预览选择的照片\n    previewPunchPhoto: function(e) {\n      var currentUrl = e.currentTarget.dataset.url;\n      if(this.punchPhotos && this.punchPhotos.length > 0) {\n        uni.previewImage({\n          urls: this.punchPhotos,\n          current: currentUrl\n        });\n      }\n    },\n    \n    // 提交打卡数据\n    submitPunchData: function() {\n      var that = this;\n      \n      if(!that.canSubmitPunch) {\n        uni.showToast({\n          title: '请先获取位置并上传照片',\n          icon: 'none',\n          duration: 2000\n        });\n        return;\n      }\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_001]准备提交打卡数据\");\n      \n      // 准备参数\n      var params = {\n        id: that.punchId,\n        st: that.punchSt,\n        longitude: that.punchLocationInfo.longitude,\n        latitude: that.punchLocationInfo.latitude\n      };\n      \n      // 设置照片参数\n      if(that.punchSt == 2) {\n        params.arrival_photo = that.punchPhotos.join(',');\n      } else {\n        params.complete_photo = that.punchPhotos.join(',');\n      }\n      \n      console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_002]提交参数:\", JSON.stringify(params));\n      \n      // 显示提交中\n      uni.showLoading({\n        title: '提交中...',\n        mask: true\n      });\n      \n      // 提交数据\n      app.post('ApiYuyueWorker/setst', params, function(data) {\n        uni.hideLoading();\n        console.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_003]提交响应:\", JSON.stringify(data));\n        \n        if(data.status === 1) {\n          // 成功，关闭弹窗并刷新数据\n          that.closePunchModal();\n          \n          // 提示成功\n          app.success(data.msg);\n          \n          // 显示详细信息\n          var title = '';\n          var content = '';\n          \n          if(that.punchSt == 2) {\n            if(that.showaddmoney) {\n              title = '出发打卡成功';\n              content = '您已成功打卡出发，请尽快前往客户位置';\n            } else {\n              title = '到达打卡成功';\n              content = '您已成功打卡到达，请开始服务';\n            }\n          } else {\n            title = '完成打卡成功';\n            content = '服务已完成，感谢您的工作';\n          }\n          \n          // 如果有距离信息，显示距离\n          if(data.distance && that.punchSt == 2) {\n            content = '您距离客户位置: ' + that.formatDistance(data.distance) + '\\n' + content;\n          }\n          \n          // 显示成功弹窗\n          uni.showModal({\n            title: title,\n            content: content,\n            showCancel: false,\n            success: function() {\n              // 刷新数据\n              setTimeout(function() {\n                that.getdata();\n              }, 500);\n            }\n          });\n        } else {\n          // 失败提示\n          app.alert(data.msg || '提交失败');\n        }\n      });\n    },\n    \n    // 格式化距离显示\n    formatDistance: function (distance) {\n      if (!distance && distance !== 0) {\n        return '未知';\n      }\n      \n      distance = parseFloat(distance);\n      if (isNaN(distance)) {\n        return '未知';\n      }\n      \n      if (distance >= 1000) {\n        return (distance / 1000).toFixed(2) + '公里';\n      } else {\n        return parseInt(distance) + '米';\n      }\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.getdata();\n    },\n\t\taddmoney:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=true\n\t\t\tthis.id= e.currentTarget.dataset.id\n\t\t},\n\t\tcancel:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=false\n\t\t\tthis.showpaycodes=false\n\t\t},\n\t\tbindMoney:function(e){\n\t\t\tvar that=this\n\t\t\tthat.addprice = e.detail.value\n\t\t},\n\t\taddconfirm:function(e){\n\t\t\tvar that = this\n\t\t\tif(!that.addprice){\n\t\t\t\tapp.error('请输入金额');\n\t\t\t\treturn;\n\t\t\t} \n\t\t\tapp.post('ApiYuyueWorker/addmoney', {id:that.id,price:that.addprice,addmoneyPayorderid:that.addmoneyPayorderid}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t\tif(data.payorderid){\n\t\t\t\t\t\tthat.showmodal=false\n\t\t\t\t\t\tthat.getdata()\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tshowpaycode:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showpaycodes=true\n\t\t\tvar index= e.currentTarget.dataset.key\n\t\t\tthat.index = index\n\t\t\tthis.addprice = \tthat.datalist[index].addprice\n\t\t\tthis.paycode = \tthat.datalist[index].paycode\n\t\t\tthis.addmoneystatus = \tthat.datalist[index].addmoneystatus\n\t\t\tthis.addmoneyPayorderid = \tthat.datalist[index].addmoneyPayorderid\n\t\t\tthis.id= e.currentTarget.dataset.id\n\t\t},\n\t\tupdate:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=true\n\t\t\tthis.showpaycodes=false\n\t\t\tvar index= e.currentTarget.dataset.key\n\t\t\tthat.addprice = that.datalist[index].addprice\n\t\t\t\n\t\t},\n\t\tdaohang:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar datainfo = that.datalist[index];\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['导航到商家', '导航到用户'],\n\t\t\t\tsuccess: function (res) {\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tif (res.tapIndex == 0) {\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude\n\t\t\t\t\t\t\tvar name = datainfo.binfo.name\n\t\t\t\t\t\t\tvar address = datainfo.binfo.address\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\n\t\t\t\t\t\t\tvar name = datainfo.orderinfo.address\n\t\t\t\t\t\t\tvar address = datainfo.orderinfo.address\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.openLocation({\n\t\t\t\t\t\t\tlatitude:parseFloat(latitude),\n\t\t\t\t\t\t\tlongitude:parseFloat(longitude),\n\t\t\t\t\t\t\tname:name,\n\t\t\t\t\t\t\taddress:address,\n\t\t\t\t\t\t\tscale: 13,\n\t\t\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\t\t\tconsole.log('success');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(res){\n\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tswitchStatusType: function(type) {\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_001]状态切换，原状态:\", this.statusType, \"新状态:\", type);\n\t\t\t\n\t\t\tif (this.statusType === type) {\n\t\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_002]状态相同，跳过切换\");\n\t\t\t\treturn; // 避免重复切换\n\t\t\t}\n\t\t\t\n\t\t\tthis.statusType = type;\n\t\t\t\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_003]开始切换到状态:\", type);\n\t\t\t\n\t\t\t// 更新URL参数，不刷新页面\n\t\t\tvar url = 'jdorderlist?st=' + this.st;\n\t\t\tif (type !== 'all') {\n\t\t\t\turl += '&statusType=' + type;\n\t\t\t}\n\t\t\tif (this.opt.mid) {\n\t\t\t\turl += '&mid=' + this.opt.mid;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_004]构建URL:\", url);\n\t\t\t\n\t\t\t// 使用history记录，不刷新页面\n\t\t\tvar pages = getCurrentPages();\n\t\t\tvar page = pages[pages.length - 1];\n\t\t\tif (page && page.route && page.route.indexOf('jdorderlist') > -1) {\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 重置页面\n\t\t\tuni.pageScrollTo({\n\t\t\t\tscrollTop: 0,\n\t\t\t\tduration: 0\n\t\t\t});\n\t\t\t\n\t\t\t// 获取数据\n\t\t\tconsole.log(\"2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_005]开始获取数据\");\n\t\t\tthis.getdata();\n\t\t}\n  }\n};\n</script>\n<style>\n@import \"./common.css\";\n.container{ width:100%;display:flex;flex-direction:column}\n.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f0f0f0}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}\n\n.order-box{ width: 94%;margin:20rpx 3%;padding:16rpx 3%; background: #fff;border-radius:16rpx;box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);transition: all 0.3s ease;}\n.order-box:active {transform: scale(0.98);}\n.order-box .head{ display:flex;justify-content:space-between;width:100%; border-bottom: 1px #f0f0f0 solid; padding: 10rpx 0; overflow: hidden; color: #999;}\n.order-box .head .status-container{display:flex;flex-direction:column;align-items:flex-start;}\n.order-box .head .f1{display:flex;align-items:center;color:#222222;height:50rpx;line-height:50rpx;}\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:10rpx}\n.order-box .head .f1 .t1{margin-right:10rpx}\n.order-box .head .f2{color:#ff7a45;height:100%;display:flex;align-items:center;}\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\n\n.order-box .head .order-num {\n  font-size: 22rpx;\n  color: #999;\n  margin-top: 6rpx;\n  line-height: 30rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 350rpx;\n}\n\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f0f0f0;position:relative}\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1 .x1{font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\n\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t3 .x1{font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\n.order-box .content .f2{flex:1;padding:0 20rpx}\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t2{font-size:24rpx;color:#666666;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\n\n.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op .t1{font-weight:bold}\n.order-box .op .btn1{height:70rpx;line-height:70rpx;color:#fff;border-radius:50rpx;text-align:center;font-size:28rpx; padding: 0 30rpx; font-size: 26rpx;box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);}\n.order-box .op .btn2{ margin-right: 20rpx; font-size: 26rpx; border-radius:50rpx; border: 1rpx solid;}\n\n.modal{ position: fixed; width: 100%; height: 100%; bottom: 0; background: rgba(0,0,0,0.5); z-index: 100; display: flex; justify-content: center;}\n.modal .addmoney{ width: 100%; background: #fff; width: 80%; position: absolute; top: 30%; border-radius: 10rpx; }\n.modal .title{ height: 88rpx; line-height: 88rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx; }\n.modal .item{ display: flex; padding: 30rpx; align-items: center;}\n.modal .item input{ width: 200rpx; height: 60rpx; background: #f8f8fa; border-radius: 8rpx; padding: 0 16rpx;}\n.modal .item label{ width:200rpx; text-align: right; font-weight: bold;}\n.modal .item .t2{ font-weight: bold;}\n.modal .btn{ display: flex; margin: 30rpx 20rpx; justify-content: space-between;}\n.modal .btn .btn-cancel{ background-color: #F2F2F2; width: 180rpx; border-radius: 10rpx;}\n.modal .btn .confirm{ width: 180rpx; border-radius: 10rpx; color: #fff;}\n.modal .btn .btn-update{ width: 180rpx; border-radius: 10rpx; color: #fff; }\n.modal .addmoney .price{ color: #ff4d4f; font-size: 32rpx; font-weight: bold;}\n.modal .qrcode{ display: flex; align-items: center;}\n.modal .qrcode image{width: 300rpx; height: 300rpx; margin: auto; box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05); border-radius: 8rpx;}\n\n/* 打卡弹窗样式 */\n.punch-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n}\n\n.punch-content {\n  background-color: #fff;\n  padding: 40rpx;\n  border-radius: 20rpx;\n  width: 85%;\n  max-width: 650rpx;\n  max-height: 80vh;\n  overflow-y: auto;\n  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);\n}\n\n.punch-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.punch-title {\n  font-size: 36rpx;\n  font-weight: bold;\n}\n\n.close-btn {\n  font-size: 48rpx;\n  height: 48rpx;\n  line-height: 40rpx;\n  width: 48rpx;\n  text-align: center;\n  color: #999;\n  cursor: pointer;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  color: #333;\n}\n\n.section-title .icon {\n  margin-right: 10rpx;\n  font-size: 32rpx;\n}\n\n.location-section, .photo-section {\n  margin-bottom: 30rpx;\n  padding: 20rpx;\n  border-radius: 16rpx;\n}\n\n.location-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.location-status {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 20rpx;\n  padding: 16rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n}\n\n.location-status.success {\n  border-left: 8rpx solid;\n}\n\n.location-status.loading {\n  color: #ff7a45;\n  border-left: 8rpx solid #ff7a45;\n}\n\n.status-text {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.location-detail {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.get-location-btn {\n  color: #fff;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.photo-content {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.photo-list {\n    display: flex;\n    flex-wrap: wrap;\n    width: 100%;\n    margin-bottom: 20rpx;\n}\n\n.photo-item {\n    position: relative;\n    width: 31%;\n    padding-bottom: 31%;\n    margin-right: 2%;\n    margin-bottom: 10rpx;\n}\n\n.photo-item:nth-child(3n) {\n    margin-right: 0;\n}\n\n.preview-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  background-color: #fff;\n}\n\n.remove-icon {\n    position: absolute;\n    top: -10rpx;\n    right: -10rpx;\n    width: 36rpx;\n    height: 36rpx;\n    background-color: rgba(0, 0, 0, 0.6);\n    color: white;\n    border-radius: 50%;\n    text-align: center;\n    line-height: 32rpx;\n    font-size: 28rpx;\n    font-weight: bold;\n    z-index: 10;\n}\n\n.photo-actions {\n  display: flex;\n  justify-content: center;\n  width: 100%;\n  margin-top: 10rpx;\n}\n\n.reselect-btn {\n  background-color: #FF6F30;\n  color: #fff;\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  box-shadow: 0 4rpx 8rpx rgba(255, 111, 48, 0.2);\n}\n\n.photo-placeholder {\n  width: 31%;\n  padding-bottom: 31%;\n  position: relative;\n  background-color: #fff;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  border: 2rpx dashed #ccc;\n  border-radius: 12rpx;\n}\n\n.photo-placeholder > * {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    text-align: center;\n}\n\n.placeholder-icon {\n  font-size: 60rpx;\n  color: #ccc;\n  line-height: 1;\n  margin-bottom: 10rpx;\n  transform: translate(-50%, -70%);\n}\n\n.placeholder-text {\n  font-size: 24rpx;\n  color: #999;\n  transform: translate(-50%, 30%);\n}\n\n.punch-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 40rpx;\n}\n\n.cancel-btn, .submit-btn {\n  flex: 1;\n  padding: 20rpx 0;\n  text-align: center;\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.cancel-btn {\n  background-color: #f0f0f0;\n  color: #666;\n  margin-right: 20rpx;\n}\n\n.submit-btn {\n  color: #fff;\n}\n\n.submit-btn.disabled {\n  background: linear-gradient(-90deg, #ccc 0%, #999 100%);\n  color: #fff;\n  opacity: 0.8;\n}\n\n/* 动画 */\n@keyframes rotating {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.loading .status-text:before {\n  content: \"\";\n  display: inline-block;\n  width: 24rpx;\n  height: 24rpx;\n  border: 3rpx solid #ff7a45;\n  border-top-color: transparent;\n  border-radius: 50%;\n  margin-right: 10rpx;\n  animation: rotating 1s linear infinite;\n}\n\n/* 改进tabbar样式 */\n.tabbar-text.active {\n  font-weight: bold;\n}\n\n/* 状态切换栏样式 */\n.status-tabs {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 80rpx;\n  width: 100%;\n  padding: 0 10rpx;\n  box-sizing: border-box;\n  border-bottom: 1rpx solid #f0f0f0;\n  overflow-x: auto;\n  white-space: nowrap;\n}\n\n.tab-item {\n  position: relative;\n  height: 80rpx;\n  line-height: 80rpx;\n  font-size: 26rpx;\n  padding: 0 12rpx;\n  color: #666;\n  border-bottom: 4rpx solid transparent;\n  transition: all 0.3s;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  min-width: 70rpx;\n  text-align: center;\n}\n\n.tab-item.active {\n  font-weight: bold;\n  font-size: 28rpx;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jdorderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115053882\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}