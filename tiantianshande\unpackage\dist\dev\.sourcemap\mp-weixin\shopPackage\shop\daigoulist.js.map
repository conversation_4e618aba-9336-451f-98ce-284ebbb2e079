{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?74f2", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?1bc2", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?acc5", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?616e", "uni-app:///shopPackage/shop/daigoulist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?7dc7", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/daigoulist.vue?43e8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "canrefund", "express_content", "selectExpressShow", "hexiao_qr", "keyword", "is_more", "cart_info", "loop_point", "loop_length", "type", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "add_cart", "app", "uni", "url", "that", "more_one", "obj", "proInfo", "more_one2", "confirm", "title", "content", "success", "close", "getdata", "changetab", "scrollTop", "duration", "toclose", "orderid", "setTimeout", "todel", "orderCollect", "logistics", "console", "express_oglist", "hideSelectExpressDialog", "showhxqr", "closeHxqr", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8KnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IAEA;EAEA;EAEAC;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC,kCACA;MACA;MACA;MACA;MACAC,4BACA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,GACA,eACA;QACA,qBACA;UAEA,6CACA;YACAC;cACAC;YACA;YAEA;UACA;YACAC;YACAA;UACA;QAEA;MACA,EACA;IAEA;IACAC,+BACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MAEA,yCACA;QACA;QACAC;QACAA;QACAA;QACAC;MACA;MAGA;MACA;MACA;MACA;MACA;MACA;IAIA;IACAC,iCACA;MACA;MACA;;MAGAP,6BACA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,GACA,eACA;QACA,qBACA;UACAC;YACAC;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA,EACA;IAEA;IACAM;MACA;MACA;MACA;MACA;MAEA,2BACA;QACAP;UACAQ;UACAC;UACAC;YACAR;UACA;QACA;MACA;MACA;MACAF;QACAC;MACA;MACA;IACA;IAEAU,wBACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAV;MACAA;MACAA;MACAH;QAAAxB;QAAAE;QAAAQ;QAAAK;MAAA;QACAY;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;QAEA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACAA;MACA;IACA;IAEAW;MACA;MACAb;QACAc;QACAC;MACA;MACA;MACAb;IACA;IACAc;MACA;MACA;MACAjB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACApB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACArB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;QACAvB;MACA;QACAjB;QACA;UACA;YACA;YACAwC;YACA;YACA;cACA;gBACAC;cACA;YACA;YACAzC;UACA;QACA;QACA;QACAwC;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3dA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/daigoulist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/daigoulist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./daigoulist.vue?vue&type=template&id=2d194a1b&\"\nvar renderjs\nimport script from \"./daigoulist.vue?vue&type=script&lang=js&\"\nexport * from \"./daigoulist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./daigoulist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/daigoulist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./daigoulist.vue?vue&type=template&id=2d194a1b&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = [1, 2, 3].includes(item.status) && item.invoice\n        var m0 =\n          item.status == 0 && item.paytypeid != 5 ? _vm.t(\"color1\") : null\n        var m1 =\n          item.status == 0 && item.paytypeid == 5 && item.transfer_check == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m2 =\n          item.status == 0 && item.paytypeid == 5 && !(item.transfer_check == 1)\n            ? _vm.t(\"color1\")\n            : null\n        var m3 =\n          item.status == 2 &&\n          item.balance_pay_status == 0 &&\n          item.balance_price > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m4 =\n          item.status == 2 &&\n          item.paytypeid != \"4\" &&\n          (item.balance_pay_status == 1 || item.balance_price == 0)\n            ? _vm.t(\"color1\")\n            : null\n        var m5 =\n          item.bid > 0 && item.status == 3 && item.iscommentdp == 0\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./daigoulist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./daigoulist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t\r\n\t<block v-if=\"isload\">\r\n\t\t<view style=\"width:100%;height:80rpx;text-align: center;line-height: 80rpx;\">\r\n\t\t\t<view v-if=\"type ==1\" class=\"search-text\" style=\"height: 80rpx;line-height: 80rpx;font-size: 35rpx;\">给他人的代选</view>\r\n\t\t\t<view v-if=\"type ==2\" class=\"search-text\" style=\"height: 80rpx;line-height: 80rpx;font-size: 35rpx;\">我的代选</view>\r\n\t\t</view> \r\n\t\t<dd-tab style=\"margin-top: 75rpx;\" :itemdata=\"['全部','待付款','待发货','待收货','已完成']\" :itemst=\"['all','0','1','2','3']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\r\n\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t<!-- <view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<!--  #endif -->\r\n\t\t<view class=\"order-content\" style=\"margin-top: 85rpx;\">\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/daikebuy?id=' + item.id\">\r\n\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t<!-- <view class=\"f1\" v-if=\"item.bid!=0\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.bid\"><image src=\"/static/img/ico-shop.png\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t<view class=\"f1\" v-else><image :src=\"item.binfo.logo\" class=\"logo-row\"></image> {{item.binfo.name}}</view> -->\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type!=1\" class=\"st1\">待发货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type==1\" class=\"st1\">待提货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">待收货</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<block v-for=\"(item2, idx) in item.product\" :key=\"idx\">\r\n\t\t\t\t\t\t<view class=\"content\" :style=\"'margin-bottom:20rpx'\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item2.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.num}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t<!-- <text class=\"x1 flex1\">￥{{item2.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.num}}</text> -->\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view  class=\"bottom\"><text class=\"t2\">{{item.typenamename}}</text></view>\r\n\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <span v-if=\"item.balance_price > 0 && item.balance_pay_status == 0\"  style=\"display: block; float: right;\">尾款：￥{{item.balance_price}}</span></text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red;padding-left:6rpx\">退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red;padding-left:6rpx\">已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red;padding-left:6rpx\">退款申请已驳回</text>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bottom\" v-if=\"item.tips!=''\">\r\n\t\t\t\t\t\t<text style=\"color:red\">{{item.tips}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"([1,2,3]).includes(item.status) && item.invoice\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'invoice?type=shop&orderid=' + item.id\">发票</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/daikebuy?id=' + item.id\" class=\"btn2\">详情</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"item.status==0\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\r\n\t\t\t\t\t\t\t<view class=\"btn1\" v-if=\"item.paytypeid!=5\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.payorderid\">去付款</view>\r\n                            <block v-if=\"item.paytypeid==5\">\r\n                                <view class=\"btn1\" v-if=\"item.transfer_check == 1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/transfer?id=' + item.payorderid\">付款凭证</view>\r\n                                <view class=\"btn1\" v-else :style=\"{background:t('color1')}\">\r\n                                    <text v-if=\"item.transfer_check == 0\">转账待审核</text>\r\n                                    <text v-if=\"item.transfer_check == -1\">转账已驳回</text>\r\n                                </view>\r\n                            </block>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"item.status==1\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.paytypeid!='4'\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundSelect?orderid=' + item.id + '&price=' + item.totalprice\" v-if=\"canrefund==1 && item.refundnum < item.procount\">退款</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"item.status==2\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.paytypeid!='4'\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundSelect?orderid=' + item.id + '&price=' + item.totalprice\" v-if=\"canrefund==1 && item.refundnum < item.procount\">退款</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.freight_type!=3 && item.freight_type!=4\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" v-if=\"item.express_type =='express_wx'\" @tap.stop=\"logistics\" :data-index=\"index\">订单跟踪</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" v-else @tap.stop=\"logistics\" :data-index=\"index\">查看物流</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"item.balance_pay_status == 0 && item.balance_price > 0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.balance_pay_orderid\">支付尾款</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.paytypeid!='4' && (item.balance_pay_status==1 || item.balance_price==0)\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"orderCollect\" :data-id=\"item.id\">确认收货</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"(item.status==1 || item.status==2) && item.freight_type==1\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"showhxqr\" :data-hexiao_qr=\"item.hexiao_qr\">核销码</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<view v-if=\"item.refundCount\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundlist?orderid='+ item.id\">查看退款</view>\r\n\t\t\t\t\t\t<block v-if=\"item.status==3 || item.status==4\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"item.bid>0 && item.status==3\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.iscommentdp==0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/order/commentdp?orderid=' + item.id\">评价店铺</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\r\n\t\t\r\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t<image :src=\"hexiao_qr\" @tap=\"previewImage\" :data-url=\"hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\r\n\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\r\n\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\r\n\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\r\n\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\r\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\r\n\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\r\n\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t</block>\r\n\t<!-- <loading v-if=\"loading\"></loading> -->\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\r\n\t<uni-popup ref=\"more_one\" type=\"center\" >\r\n\t\t<uni-popup-dialog mode=\"input\" message=\"成功消息\" :duration=\"2000\" valueType=\"number\" :before-close=\"true\"  @close=\"close\" @confirm=\"confirm\"></uni-popup-dialog>\r\n\t\t<!-- <view class=\"uni-popup-dialog\">\r\n\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t<text>test</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t<input type=\"number\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t <view></view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t</uni-popup>\r\n</view>\r\n</template>\r\n\r\n<script> \r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      st: 'all',\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n      codtxt: \"\",\r\n\t\t\tcanrefund:1,\r\n\t\t\texpress_content:'',\r\n\t\t\tselectExpressShow:false,\r\n\t\t\thexiao_qr:'',\r\n\t\t\tkeyword:'',\r\n\t\t\t  is_more:0,\r\n\t\t\t \r\n\t\t\t cart_info:[],\r\n\t\t\t loop_point:0 ,\r\n\t\t\t loop_length:0,\r\n\t\t\t type:1\r\n\t\t\t \r\n    };\r\n\t\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.st){\r\n\t\t\tthis.st = this.opt.st;\r\n\t\t}\r\n\t\tthis.type = opt['type'];\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\t// this.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n\tonNavigationBarSearchInputConfirmed:function(e){\r\n\t\tthis.searchConfirm({detail:{value:e.text}});\r\n\t},\r\n  methods: {\r\n\tadd_cart(data)\r\n\t{\r\n\t\t//let index = i;\r\n\t\t//let data = datas[index];\r\n\t\tlet that = this;\r\n\t\tapp.post('ApiShop/addcart',\r\n\t\t{'ggid':data['ggid'],'num':data['num'],'proid':data['id'],'tid':0,'glass_record_id':0},\r\n\t\t\t\t   function(res)\r\n\t\t\t\t   {\r\n\t\t\t\t\t   if(res.status== 1)\r\n\t\t\t\t\t   {\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t   if(that.loop_point== that.loop_length-1)\r\n\t\t\t\t\t\t   {\r\n\t\t\t\t\t\t\t   uni.redirectTo({\r\n\t\t\t\t\t\t\t   \t  url:'/shopPackage/shop/cart'\r\n\t\t\t\t\t\t\t   });\r\n\t\t\t\t\t\t\t   \r\n\t\t\t\t\t\t\t   return ;\r\n\t\t\t\t\t\t   }else{\r\n\t\t\t\t\t\t\t   that.loop_point++;\r\n\t\t\t\t\t\t\t   that.add_cart(that.cart_info[that.loop_point]);\r\n\t\t\t\t\t\t   }\r\n\t\t\t\t\t\t  \r\n\t\t\t\t\t   }\r\n\t\t\t\t   }\r\n\t\t);\r\n\t\t\r\n\t},\r\n\tmore_one:function(e)\r\n\t{\r\n\t\t// console.log(e);\r\n\t\tlet data = e.target.dataset;\r\n\t\t// let url = \"/activity/luckycollage/buy\"+\"?proid=\"+data['id']+\"&num=\"+data['num']+\"&buytype=\"+data['buytype']+\"&ggid=\"+data['ggid'];\r\n\t\t\r\n\t\tlet proInfo = [];\r\n\t\tlet o_index = data['key'];\r\n\t\tlet order= this.datalist[o_index];\r\n\t\t\r\n\t\tlet prolist  = order['prolist'];\r\n\t\t\r\n\t\tfor(let i=0;i<prolist.length;i++)\r\n\t\t{\r\n\t\t\tlet obj={};\r\n\t\t    obj['id'] = prolist[i]['proid'];\r\n\t\t\tobj['num'] = prolist[i]['num'];\r\n\t\t\tobj['ggid'] = prolist[i]['ggid'];\r\n\t\t\tproInfo.push(obj);\r\n\t\t}\r\n\t\t\r\n\r\n\t\tthis.loop_length = proInfo.length;\r\n\t\tthis.loop_point = 0;\r\n\t\tthis.cart_info = proInfo;\r\n\t\t// console.log(proInfo);\r\n\t\t// return ;\r\n\t\tthis.add_cart(proInfo[0]);\r\n\t\t\r\n\t\t\r\n\t\t\t   \r\n\t},\r\n\tmore_one2:function(e)\r\n\t{\r\n\t\tlet data = e.target.dataset;\r\n\t\t// let url = \"/activity/luckycollage/buy\"+\"?proid=\"+data['id']+\"&num=\"+data['num']+\"&buytype=\"+data['buytype']+\"&ggid=\"+data['ggid'];\r\n\t\t\r\n\t\t\r\n\t\tapp.post('/ApiShop/addcart',\r\n\t\t{'ggid':data['ggid'],'num':data['num'],'proid':data['id'],'tid':0,'glass_record_id':0},\r\n\t\t\t\t   function(res)\r\n\t\t\t\t   {\r\n\t\t\t\t\t   if(res.status== 1)\r\n\t\t\t\t\t   {\r\n\t\t\t\t\t\t   uni.redirectTo({\r\n\t\t\t\t\t\t   \t  url:'/shopPackage/shop/cart'\r\n\t\t\t\t\t\t   });\r\n\t\t\t\t\t\t   \r\n\t\t\t\t\t\t   // if(that.loop_point== that.loop_length-1)\r\n\t\t\t\t\t\t   // {\r\n\t\t\t\t\t\t\t  //  return ;\r\n\t\t\t\t\t\t   // }else{\r\n\t\t\t\t\t\t\t  //  that.add_cart(cart_info[that.loop_point]);\r\n\t\t\t\t\t\t   // }\r\n\t\t\t\t\t\t   // that.loop_point++;\r\n\t\t\t\t\t   }\r\n\t\t\t\t   }\r\n\t\t);\r\n\t\t\r\n\t},\r\n\tconfirm(value1,value2) {\r\n\t\tlet data = this.more_data;\r\n\t\t//console.log(value2);\r\n\t\tlet num = value2;\r\n\t\tlet that = this;\r\n\t\t\r\n\t\tif(num==\"\"||num==0)\r\n\t\t{\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle:\"提示\",\r\n\t\t\t\tcontent:\"请输入数量\",\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tthat.$refs.more_one.close();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t\tlet url = \"/shopPackage/shop/buy?\"+\"prodata=\"+data['id']+\",\"+data['ggid']+\",\"+num;\r\n\t\tuni.redirectTo({\r\n\t\t\turl:url,\r\n\t\t})\r\n\t\tthis.$refs.more_one.close();\r\n\t}\r\n\t,\r\n\tclose()\r\n\t{\r\n\t\t this.$refs.more_one.close();\r\n\t},\t  \r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiDaigou/daigoulist', {st: st,pagenum: pagenum,keyword:that.keyword,type:this.type}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.codtxt = res.codtxt;\r\n\t\t\t\t\tthat.canrefund = res.canrefund;\r\n\t\t\t\t\tthat.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n\t\t\tthat.loaded();\r\n      });\r\n    },\r\n       \r\n    changetab: function (st) {\r\n      this.st = st;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n\t  that.is_more = res['is_more'];\r\n    },\r\n    toclose: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiDaigou/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    todel: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('删除中');\r\n        app.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    orderCollect: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要收货吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\t\tlogistics:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar orderinfo = this.datalist[index];\r\n\t\t\tvar express_com = orderinfo.express_com\r\n\t\t\tvar express_no = orderinfo.express_no\r\n\t\t\tvar express_content = orderinfo.express_content\r\n\t\t\tvar express_type = orderinfo.express_type\r\n\t\t\tvar prolist = orderinfo.prolist\r\n\t\t\tconsole.log(express_content)\r\n\t\t\tif(!express_content){\r\n\t\t\t\tapp.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\r\n\t\t\t}else{\r\n\t\t\t\texpress_content = JSON.parse(express_content);\r\n\t\t\t\tfor(var i in express_content){\r\n\t\t\t\t\tif(express_content[i].express_ogids){\r\n\t\t\t\t\t\tvar express_ogids = (express_content[i].express_ogids).split(',');\r\n\t\t\t\t\t\tconsole.log(express_ogids);\r\n\t\t\t\t\t\tvar express_oglist = [];\r\n\t\t\t\t\t\tfor(var j in prolist){\r\n\t\t\t\t\t\t\tif(app.inArray(prolist[j].id+'',express_ogids)){\r\n\t\t\t\t\t\t\t\texpress_oglist.push(prolist[j]);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\texpress_content[i].express_oglist = express_oglist;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.express_content = express_content;\r\n\t\t\t\tconsole.log(express_content);\r\n\t\t\t\tthis.$refs.dialogSelectExpress.open();\r\n\t\t\t}\r\n\t\t},\r\n\t\thideSelectExpressDialog:function(){\r\n\t\t\tthis.$refs.dialogSelectExpress.close();\r\n\t\t},\r\n\t\tshowhxqr:function(e){\r\n\t\t\tthis.hexiao_qr = e.currentTarget.dataset.hexiao_qr\r\n\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t},\r\n\t\tcloseHxqr:function(){\r\n\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t},\r\n\t\tsearchConfirm:function(e){\r\n\t\t\tthis.keyword = e.detail.value;\r\n      this.getdata(false);\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ width:100%;}\r\n.topsearch{width:94%;margin:10rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n.order-content{display:flex;flex-direction:column}\r\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#333}\r\n.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}\r\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\r\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\r\n.order-box .content:last-child{ border-bottom: 0; }\r\n.order-box .content image{ width: 140rpx; height: 140rpx;}\r\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.order-box .content .detail .x1{ flex:1}\r\n.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\r\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\r\n.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\r\n.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\r\n\r\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.hxqrbox .img{width:400rpx;height:400rpx}\r\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./daigoulist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./daigoulist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115007638\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}