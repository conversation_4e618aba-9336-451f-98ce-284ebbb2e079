{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?a3f2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?7a8d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?949e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?c43e", "uni-app:///pagesExb/filem/filemlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?339f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/filem/filemlist.vue?0413"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "datalist", "pagenum", "clist", "cnamelist", "cidlist", "cid", "bid", "set", "filetype", "showprivate", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "uni", "getcategory", "changetab", "searchConfirm", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuClxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IAEA;MACA;IACA;IAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEAC;;MAEA;MACAC;QACAf;QACAI;QACAC;QACAP;QACAS;QACAC;MACA;QACAM;QAEA;UACA;UACA;YACAA;UACA;UAEA;UACA;YACAA;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UAEAA;UACAE;QACA;UACAD;UACAD;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MAEAF;QAAAV;MAAA;QACA;UACA;UACAS;;UAEA;UACA;UACA;;UAEA;UACAZ;UACAC;;UAEA;UACA;YACAD;YACAC;;YAEA;YACA;cACA;gBACAD;gBACAC;cACA;YACA;UACA;UAEAW;UACAA;QACA;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAJ;UACAK;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtNA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/filem/filemlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/filem/filemlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./filemlist.vue?vue&type=template&id=340d151b&\"\nvar renderjs\nimport script from \"./filemlist.vue?vue&type=script&lang=js&\"\nexport * from \"./filemlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./filemlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/filem/filemlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./filemlist.vue?vue&type=template&id=340d151b&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.clist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./filemlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./filemlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索文件\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<dd-tab :itemdata=\"cnamelist\" :itemst=\"cidlist\" :st=\"cid\" :isfixed=\"false\" @changetab=\"changetab\" v-if=\"clist.length>0\"></dd-tab>\r\n\t\t\r\n\t\t<view class=\"filem_list\">\r\n\t\t\t<!-- 文件列表项 -->\r\n\t\t\t<view class=\"filem-item\" v-for=\"(item, index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExa/filem/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"filem-icon\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"'/static/img/filetypes/' + (item.filetype_icon || 'file') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"filem-info\">\r\n\t\t\t\t\t<view class=\"p1\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"p2\" v-if=\"item.subname\">{{item.subname}}</view>\r\n\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t<text class=\"filesize\">{{item.filesize_text}}</text>\r\n\t\t\t\t\t\t<text class=\"filedate\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text class=\"fileview\" v-if=\"item.showviewcount\">查看: {{item.viewcount}}</text>\r\n\t\t\t\t\t\t<text class=\"filedown\" v-if=\"item.showdownloads\">下载: {{item.downloads}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\topt: {},\r\n\t\tloading: false,\r\n\t\tisload: false,\r\n\t\tmenuindex: -1,\r\n\r\n\t\tnodata: false,\r\n\t\tnomore: false,\r\n\t\tkeyword: '',\r\n\t\tdatalist: [],\r\n\t\tpagenum: 1,\r\n\t\tclist: [],\r\n\t\tcnamelist: [],\r\n\t\tcidlist: [],\r\n\t\tcid: 0,\r\n\t\tbid: 0,\r\n\t\tset: '',\r\n\t\tfiletype: '',\r\n\t\tshowprivate: 0,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.cid = this.opt.cid || 0;\t\r\n\t\tthis.bid = this.opt.bid || 0;\r\n\t\tthis.filetype = this.opt.filetype || '';\r\n\t\tthis.showprivate = this.opt.showprivate || 0;\r\n\t\t\r\n\t\tif (this.opt.keyword) {\r\n\t\t\tthis.keyword = this.opt.keyword;\r\n\t\t}\r\n\t\t\r\n\t\tthis.getdata();\r\n\t\tthis.getcategory();\r\n  },\r\n  onPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n  },\r\n  onReachBottom: function () {\r\n\t\tif (!this.nomore && !this.nodata) {\r\n\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\tthis.getdata(true);\r\n\t\t}\r\n  },\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\tif(!loadmore){\r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.datalist = [];\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.nomore = false;\r\n\t\t}\r\n\t\t\r\n\t\tvar that = this;\r\n\t\tvar pagenum = that.pagenum;\r\n\t\tvar cid = that.cid;\r\n\t\tvar bid = that.bid;\r\n\t\tvar keyword = that.keyword;\r\n\t\tvar filetype = that.filetype;\r\n\t\tvar showprivate = that.showprivate;\r\n\t\t\r\n\t\tthat.loading = true;\r\n\t\t\r\n\t\t// 调用文件列表接口\r\n\t\tapp.post('ApiFilem/getfilemlist', {\r\n\t\t\tpagenum: pagenum, \r\n\t\t\tcid: cid, \r\n\t\t\tbid: bid, \r\n\t\t\tkeyword: keyword,\r\n\t\t\tfiletype: filetype,\r\n\t\t\tshowprivate: showprivate\r\n\t\t}, function (res) {\r\n\t\t\tthat.loading = false;\r\n\t\t\t\r\n\t\t\tif (res.status == 1) {\r\n\t\t\t\t// 获取设置信息\r\n\t\t\t\tif (res.set) {\r\n\t\t\t\t\tthat.set = res.set;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tvar newdata = res.data || [];\r\n\t\t\t\tif (newdata.length > 0) {\r\n\t\t\t\t\tthat.datalist = that.datalist.concat(newdata);\r\n\t\t\t\t\tif (newdata.length < 10) {\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t} else {\r\n\t\t\t\tapp.alert(res.msg || '获取数据失败');\r\n\t\t\t\tthat.nodata = true;\r\n\t\t\t}\r\n\t\t});\r\n    },\r\n\t\r\n\t// 获取文件分类\r\n\tgetcategory: function() {\r\n\t\tvar that = this;\r\n\t\tvar bid = that.bid;\r\n\t\t\r\n\t\tapp.post('ApiFilem/getcategory', {bid: bid}, function (res) {\r\n\t\t\tif (res.status == 1) {\r\n\t\t\t\tvar clist = res.data || [];\r\n\t\t\t\tthat.clist = clist;\r\n\t\t\t\t\r\n\t\t\t\t// 处理分类列表\r\n\t\t\t\tvar cnamelist = [];\r\n\t\t\t\tvar cidlist = [];\r\n\t\t\t\t\r\n\t\t\t\t// 添加\"全部\"选项\r\n\t\t\t\tcnamelist.push('全部');\r\n\t\t\t\tcidlist.push(0);\r\n\t\t\t\t\r\n\t\t\t\t// 遍历分类\r\n\t\t\t\tfor (var i=0; i<clist.length; i++) {\r\n\t\t\t\t\tcnamelist.push(clist[i].name);\r\n\t\t\t\t\tcidlist.push(clist[i].id);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理子分类\r\n\t\t\t\t\tif (clist[i].child && clist[i].child.length > 0) {\r\n\t\t\t\t\t\tfor (var j=0; j<clist[i].child.length; j++) {\r\n\t\t\t\t\t\t\tcnamelist.push('- ' + clist[i].child[j].name);\r\n\t\t\t\t\t\t\tcidlist.push(clist[i].child[j].id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.cnamelist = cnamelist;\r\n\t\t\t\tthat.cidlist = cidlist;\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\t\r\n\t// 切换分类\r\n\tchangetab: function(e) {\r\n\t\tthis.cid = e.currentTarget.dataset.st;\r\n\t\tthis.pagenum = 1;\r\n\t\tthis.datalist = [];\r\n\t\tthis.nodata = false;\r\n\t\tthis.nomore = false;\r\n\t\tthis.getdata();\r\n\t},\r\n\t\r\n\t// 搜索确认\r\n\tsearchConfirm: function(e) {\r\n\t\tthis.keyword = e.detail.value;\r\n\t\tthis.pagenum = 1;\r\n\t\tthis.datalist = [];\r\n\t\tthis.nodata = false;\r\n\t\tthis.nomore = false;\r\n\t\tthis.getdata();\r\n\t},\r\n\t\r\n\t// 页面跳转\r\n\tgoto: function(e) {\r\n\t\tvar url = e.currentTarget.dataset.url;\r\n\t\tif (url) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.topsearch{width:100%;height:80rpx;padding:0 24rpx;background:#fff;border-bottom:1px solid #eee;}\r\n.topsearch .f1{width:100%;height:60rpx;background:#f5f5f5;padding:0 20rpx;border-radius:30rpx;}\r\n.topsearch .img{width:28rpx;height:28rpx;margin-right:6rpx;}\r\n.topsearch input{width:100%;height:60rpx;font-size:26rpx;}\r\n\r\n.filem_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}\r\n.filem_list .filem-item {width:100%;display:flex;position:relative;margin-bottom:16rpx;background:#fff;border-radius:12rpx;padding:20rpx;overflow:hidden;}\r\n.filem_list .filem-item .filem-icon {width:80rpx;height:80rpx;flex-shrink:0;display:flex;align-items:center;justify-content:center;}\r\n.filem_list .filem-item .filem-icon .image {width:64rpx;height:64rpx;}\r\n.filem_list .filem-item .filem-info {flex:1;margin-left:20rpx;overflow:hidden;}\r\n.filem_list .filem-item .filem-info .p1 {font-size:28rpx;font-weight:bold;color:#333;line-height:40rpx;margin-bottom:6rpx;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;}\r\n.filem_list .filem-item .filem-info .p2 {font-size:24rpx;color:#666;line-height:32rpx;margin-bottom:6rpx;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}\r\n.filem_list .filem-item .filem-info .p3 {font-size:22rpx;color:#999;display:flex;flex-wrap:wrap;}\r\n.filem_list .filem-item .filem-info .p3 .filesize {margin-right:20rpx;}\r\n.filem_list .filem-item .filem-info .p3 .filedate {margin-right:20rpx;}\r\n.filem_list .filem-item .filem-info .p3 .fileview {margin-right:20rpx;}\r\n.filem_list .filem-item .filem-info .p3 .filedown {margin-right:20rpx;}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./filemlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./filemlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115027946\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}