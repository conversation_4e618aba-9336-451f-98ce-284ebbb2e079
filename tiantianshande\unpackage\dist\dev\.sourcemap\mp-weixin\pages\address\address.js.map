{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?8494", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?9879", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?d373", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?4014", "uni-app:///pages/address/address.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?c493", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/address.vue?6b6e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "type", "keyword", "nodata", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "url", "set<PERSON><PERSON>ult", "addressid", "del", "console", "searchChange", "searchConfirm", "getweixinaddress", "success", "name", "tel", "area", "address"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsChxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAC;QAAAR;QAAAC;MAAA;QACAM;QACA;QACA;UACAE;YACAC;UACA;QACA;UACAH;UACAA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACAH;QAAAI;MAAA;QACA;UACAJ;QACA;UACAD;QACA;MACA;IACA;IACAM;MACA;MACA;MACAC;MACAN;QACAA;UAAAI;QAAA;UACAJ;UACAD;QACA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;MACA;MACAT;MACAA;IACA;IACAU;MACA;MACA5B;QACA6B;UACAV;UACAA;YAAAR;YAAAY;YAAAO;YAAAC;YAAAC;YAAAC;UAAA;YACAd;YACA;cACAA;cACA;YACA;YACAD;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./address.vue?vue&type=template&id=db675620&\"\nvar renderjs\nimport script from \"./address.vue?vue&type=script&lang=js&\"\nexport * from \"./address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./address.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/address.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=template&id=db675620&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.isdefault ? _vm.t(\"color1\") : null\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content\" @tap.stop=\"setdefault\" :data-id=\"item.id\">\n\t\t\t<view class=\"f1\">\n\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t<text class=\"t2\">{{item.tel}}</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"item.company\">{{item.company}}</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image class=\"t3\" src=\"/static/img/edit.png\" @tap.stop=\"goto\" :data-url=\"'/pages/address/addressadd?id=' + item.id + '&type=' + (item.latitude>0 ? '1' : '0')\">\n\t\t\t</view>\n\t\t\t<view class=\"f2\">{{item.area}} {{item.address}}</view>\n\t\t\t<view class=\"f3\">\n\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t<view class=\"radio\" :style=\"item.isdefault ? 'border:0;background:'+t('color1') : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t<view class=\"mrtxt\">{{item.isdefault ? '默认地址' : '设为默认'}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t<view class=\"del\" :style=\"{color:t('color1')}\" @tap.stop=\"del\" :data-id=\"item.id\">删除</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t<view style=\"height:140rpx\"></view>\n\t\t<view class=\"btn-add\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot3'\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"goto\" :data-url=\"'/pages/address/addressadd?type=' + type\"><image src=\"/static/img/add.png\" style=\"width:28rpx;height:28rpx;margin-right:6rpx\"/>添加地址</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      datalist: [],\n      type: \"\",\n\t\t\tkeyword:'',\n\t\t\tnodata:false,\n    }\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.type = this.opt.type || '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tapp.get('ApiAddress/address', {type: that.opt.type,keyword:that.keyword}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar datalist = res.data;\n\t\t\t\tif (datalist.length == 0 && that.keyword == ''){\n\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\turl: '/pages/address/addressadd?type=' + (that.opt.type || 0)\n\t\t\t\t\t});\n\t\t\t\t}else if(datalist.length == 0){\n\t\t\t\t\tthat.datalist = datalist;\n\t\t\t\t\tthat.nodata = true;\n\t\t\t\t}else{\n\t\t\t\t\tthat.datalist = datalist;\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    //选择收货地址\n    setdefault: function (e) {\n      var that = this;\n      var fromPage = this.opt.fromPage;\n      var addressId = e.currentTarget.dataset.id;\n      app.post('ApiAddress/setdefault', {addressid: addressId}, function (data) {\n        if (fromPage) {\n          app.goback(true);\n        } else {\n          that.getdata();\n        }\n      });\n    },\n    del: function (e) {\n      var that = this;\n      var addressId = e.currentTarget.dataset.id;\n      console.log(addressId);\n      app.confirm('确定要删除此地址吗?', function () {\n        app.post(\"ApiAddress/del\", {addressid: addressId}, function (res) {\n          app.success(res.msg);\n          that.getdata();\n        });\n      });\n    },\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword;\n      that.getdata();\n    },\n\t\tgetweixinaddress:function(){\n      var that = this;\n\t\t\twx.chooseAddress({\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tapp.showLoading('提交中');\n\t\t\t\t\tapp.post('ApiAddress/addressadd', {type: that.type,addressid: '',name: res.userName,tel: res.telNumber,area: res.provinceName+','+res.cityName+','+res.countyName,address: res.detailInfo}, function (res) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\tapp.success('添加成功');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t})\n\t\t}\n  }\n};\r\n</script>\r\n<style>\n.topsearch{width:94%;margin:16rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx 40rpx;}\n.content .f1{height:96rpx;line-height:96rpx;display:flex;align-items:center}\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:30rpx}\n.content .f1 .t2{color:#999999;font-size:28rpx;margin-left:10rpx}\n.content .f1 .t3{width:28rpx;height:28rpx}\n.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;border-bottom:1px solid #F2F2F2}\n.content .f3{height:96rpx;display:flex;align-items:center}\n.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\n.content .radio .radio-img{width:100%;height:100%}\n.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\n.content .del{font-size:24rpx}\n\r\n.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:0;margin-bottom:20rpx;}\n.container .btn-add2{width:43%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:5%;bottom:0;margin-bottom:20rpx;}\n.container .btn-add3{width:43%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;right:5%;bottom:0;margin-bottom:20rpx;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115014079\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}