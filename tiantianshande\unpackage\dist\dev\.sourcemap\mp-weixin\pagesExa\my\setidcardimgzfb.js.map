{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?a94a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?f7fa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?c33a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?c6ce", "uni-app:///pagesExa/my/setidcardimgzfb.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?61a8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimgzfb.vue?c08b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "realname", "idcard", "textset", "haspwd", "idcard_front", "idcard_back", "showposter", "orderNumber", "cert_url", "cert_type", "onLoad", "onPullDownRefresh", "methods", "getInfo", "app", "that", "uqrcode", "canvasId", "componentInstance", "text", "size", "margin", "backgroundColor", "foregroundColor", "fileType", "errorCorrectLevel", "uni", "content", "showCancel", "success", "baocun", "console", "urls", "current", "fail", "renzheng", "log_id", "posterDialogClose", "upIdcardHead", "upIdcardBack", "formSubmit", "name", "card_id", "type", "img_front", "img_reverse"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAowB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiGxxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;;IAGA;IAEA;;IAEA;IACA;;IAEA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;;IAGA;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;EAAA,CACA;EACAC;IAEAC;MACA;MAEAC;QACA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACAA;YACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;;UAEA;UACA;YACAC;cACAC;cACAC;cACAC;gBACAH;cACA;YACA;UACA;QACA;UACAZ;QACA;MACA;IACA;IAEAgB;MAEAJ;QACAT;QACAY;UACAE;UAEAL;YACAM;YACAC;YAAA;YACAJ;cACAE;cACA;cACA;cACA;YACA;UACA;;UAGA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAG;UACAH;QACA;MACA;IAEA;IAGAI;MAEA;QACArB;QACA;MACA;MACAA;QACAsB;MACA;QAEAV;UACAC;UACAC;UACAC;YACAH;UACA;QACA;MAEA;IAEA;IAEAW;MACA;IAEA;IAEAC;MAEA;MACAxB;QACA;QACAC;MACA;IACA;IAEAwB;MACA;MACAzB;QACA;QACAC;MACA;IACA;IAEAyB;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA1B;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEAA;QACA2B;QACAC;QACAC;QACAC;QACAC;MACA;QACA;UACA9B;UACAA;UACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;;UAGA;UACA;UACA;UACA;QAEA;UACAX;QACA;MAEA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAAm8C,CAAgB,84CAAG,EAAC,C;;;;;;;;;;;ACAv9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/setidcardimgzfb.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/setidcardimgzfb.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setidcardimgzfb.vue?vue&type=template&id=1f3ba238&scoped=true&\"\nvar renderjs\nimport script from \"./setidcardimgzfb.vue?vue&type=script&lang=js&\"\nexport * from \"./setidcardimgzfb.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setidcardimgzfb.vue?vue&type=style&index=0&id=1f3ba238&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1f3ba238\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/setidcardimgzfb.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimgzfb.vue?vue&type=template&id=1f3ba238&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimgzfb.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimgzfb.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t\t\t\t<view class=\"auth\">\r\n\t\t\t\t\t\t<view class=\"infos\">\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>您的姓名</text>\r\n\t\t\t\t\t\t\t\t<input placeholder=\"请输入内容\" \r\n\t\t\t\t\t\t\t\t\tplaceholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" \r\n\t\t\t\t\t\t\t\t\tborder=\"surround\" \r\n\t\t\t\t\t\t\t\t\tname=\"realname\"\r\n\t\t\t\t\t\t\t\t\tv-model=\"realname\"\r\n\t\t\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>您的身份证</text>\r\n\t\t\t\t\t\t\t\t<input placeholder=\"请输入身份证号码\"\r\n\t\t\t\t\t\t\t\t\tplaceholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" \r\n\t\t\t\t\t\t\t\t\tborder=\"surround\" \r\n\t\t\t\t\t\t\t\t\tname=\"idcard\" v-model=\"idcard\"\r\n\t\t\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>上传身份证头像面</text>\r\n\t\t\t\t\t\t\t\t<view class=\"upload\" @click=\"upIdcardHead\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<image :src=\"idcard_front\"></image>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- \t<text>请保证身份证完整，身份证号清晰</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>上传身份证背面</text>\r\n\t\t\t\t\t\t\t\t<view class=\"upload\" @click=\"upIdcardBack\">\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<image :src=\"idcard_back\"></image>\r\n\t\t\t\t\t\t\t\t<!-- \t<text>请保证身份证完整，身份证号清晰</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"button\" @click=\"formSubmit\">\r\n\t\t\t\t\t\t\t\t<text>提交认证信息</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t<text>根据监管要求身份证照片仅用于实名认证</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n<!-- \t\t\t\t<view class=\"form\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入姓名\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#BBBBBB;font-size:28rpx\" name=\"realname\"\r\n\t\t\t\t\t\t\tv-model=\"realname\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入身份证号码\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#BBBBBB;font-size:28rpx\" name=\"idcard\" v-model=\"idcard\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"set-btn\" form-type=\"submit\"\r\n\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保\r\n\t\t\t\t\t存</button> -->\r\n\t\t\t</form>\r\n\t\t\t\r\n\r\n\t\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t\t<view class=\"main\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/close.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content\" @click=\"baocun\">\r\n\t\t\t\t\t\t<canvas canvas-id=\"qrcode\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips\">\r\n\t\t\t\t\t\t<text>请使用{{cert_type === 'ZHIMACREDIT' ? '支付宝' : '微信'}}扫码进行认证</text>\r\n\t\t\t\t\t\t<text class=\"sub-tips\">{{cert_type === 'ZHIMACREDIT' ? '请保存二维码打开支付宝扫码认证' : '请用另一台手机微信进行人脸验证'}}</text>\r\n\t\t\t\t\t\t<text class=\"sub-tips\">认证成功后打开此页面点击<text class=\"red\">我已认证</text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn-s\" @click=\"baocun\">长按识别</view>\r\n\t\t\t\t\t<view class=\"btn-s\" @click=\"renzheng\">我已认证</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</block>\r\n\t\t\r\n\t\t\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\timport uqrcode from './uqrcode.js'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\trealname: '',\r\n\t\t\t\tidcard: '',\r\n\t\t\t\ttextset: {},\r\n\t\t\t\thaspwd: 0,\r\n\t\t\t\tidcard_front : '../../static/img/upload.png',\r\n\t\t\t\tidcard_back : '../../static/img/upload.png',\r\n\t\t\t\t\r\n\t\t\t\tshowposter: false,\r\n\t\t\t\torderNumber : '',\r\n\t\t\t\tcert_url: '',\r\n\t\t\t\tcert_type: ''\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\t// this.opt = app.getopts(opt);\r\n\t\t\t\r\n\r\n\t\t\tthis.loaded();\r\n\t\t\t\r\n\t\t\tthis.getInfo();\r\n\t\t\t\r\n\t\t\t// const _that = this\r\n\t\t\t// app.get('ApiMy/set', {}, function(data) {\r\n\t\t\t\t\r\n\t\t\t// \tconsole.log(data)\r\n\t\t\t\t\r\n\t\t\t// \tconst user = data.userinfo\r\n\t\t\t// \t_that.realname = user.realname\r\n\t\t\t// \t_that.idcard = user.usercard || ''\r\n\r\n\t\t\t// \tif(user.is_eid_verify){\r\n\t\t\t// \t\tapp.goto('/pages/my/seteid');\r\n\t\t\t// \t}\r\n\t\t\t\t\r\n               \r\n\t\t\t// });\r\n\t\t\t// setTimeout(function() {\r\n\t\t\t// \t// \tapp.goback(true);\r\n\t\t\t// \tapp.goto('/pages/my/seteid');\r\n\t\t\t// }, 2000);\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\t//this.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\tgetInfo(){\r\n\t\t\t\tlet that = this \r\n\r\n\t\t\t\tapp.post('ApiFace/info', {}, function(res) {\r\n\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t   that.realname = res.data.name;\r\n\t\t\t\t\t   that.idcard = res.data.id_number;\r\n\t\t\t\t\t   that.idcard_front = res.data.img_front;\r\n\t\t\t\t\t   that.idcard_back = res.data.img_reverse; \r\n\t\t\t\t\t   that.orderNumber = res.data.order_num;    \r\n\t\t\t\t\t   that.cert_type = res.data.cert_type;\r\n\t\t\t\t\t   \r\n\t\t\t\t\t   // 如果有订单号，直接显示二维码弹窗\r\n\t\t\t\t\t   if(that.orderNumber) {\r\n\t\t\t\t\t\t   that.showposter = true;\r\n\t\t\t\t\t\t   uqrcode.make({\r\n\t\t\t\t\t\t\t   canvasId: 'qrcode',\r\n\t\t\t\t\t\t\t   componentInstance: that,\r\n\t\t\t\t\t\t\t   text: res.data.cert_url,\r\n\t\t\t\t\t\t\t   size: 250,\r\n\t\t\t\t\t\t\t   margin: 0,\r\n\t\t\t\t\t\t\t   backgroundColor: '#ffffff',\r\n\t\t\t\t\t\t\t   foregroundColor: '#000000',\r\n\t\t\t\t\t\t\t   fileType: 'jpg',\r\n\t\t\t\t\t\t\t   errorCorrectLevel: uqrcode.errorCorrectLevel.H,\r\n\t\t\t\t\t\t   });\r\n\t\t\t\t\t   }\r\n\t\t\t\t\t   \r\n\t\t\t\t\t   // 只在完成认证时显示提示\r\n\t\t\t\t\t   if(res.data.status == 1){\r\n\t\t\t\t\t\t   uni.showModal({\r\n\t\t\t\t\t\t\t   content:'您已成功认证',\r\n\t\t\t\t\t\t\t   showCancel:false,\r\n\t\t\t\t\t\t\t   success(){\r\n\t\t\t\t\t\t\t\t   uni.navigateBack();\r\n\t\t\t\t\t\t\t   }\r\n\t\t\t\t\t\t   })    \r\n\t\t\t\t\t   }\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tbaocun() {\r\n\t\t\t\r\n\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t uni.previewImage({\r\n\t\t\t\t\t\t\t\turls: [res.tempFilePath],\r\n\t\t\t\t\t\t\t\tcurrent: 0, //点击图片传过来的下标\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('预览图片成功');\r\n\t\t\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t\t\t// \ttitle: '预览图片成功'\r\n\t\t\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\r\n\t\t\t\t\t\t// uni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t// \tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t// \tsuccess() {\r\n\t\t\t\t\t\t// \t\tuni.showToast({\r\n\t\t\t\t\t\t// \t\t\ttitle: '图片保存成功'\r\n\t\t\t\t\t\t// \t\t});\r\n\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t// \tfail() {\r\n\t\t\t\t\t\t// \t\tconsole.log('保存失败：', err);\r\n\t\t\t\t\t\t// \t\t// 处理用户拒绝权限的情况\r\n\t\t\t\t\t\t// \t\tif (err.errMsg.indexOf('authorize') !== -1) {\r\n\t\t\t\t\t\t// \t\t\tuni.showModal({\r\n\t\t\t\t\t\t// \t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t// \t\t\t\tcontent: '需要授权保存到相册',\r\n\t\t\t\t\t\t// \t\t\t\tsuccess: function(modalRes) {\r\n\t\t\t\t\t\t// \t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t// \t\t\t\t\t\tuni.openSetting();\r\n\t\t\t\t\t\t// \t\t\t\t\t}\r\n\t\t\t\t\t\t// \t\t\t\t}\r\n\t\t\t\t\t\t// \t\t\t});\r\n\t\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\trenzheng() {\r\n\t\t\t\r\n\t\t\t\tif (this.orderNumber == '') {\r\n\t\t\t\t\tapp.alert('请先提交认证信息');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tapp.post('ApiFace/query', {\r\n\t\t\t\t\tlog_id: this.orderNumber,\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\t\r\n\t\t\t\t    uni.showModal({\r\n\t\t\t\t\t\t   content:data.msg,\r\n\t\t\t\t\t\t   showCancel:false,\r\n\t\t\t\t\t\t   success(){\r\n\t\t\t\t\t\t\t   uni.navigateBack();\r\n\t\t\t\t\t\t   }\r\n\t\t\t\t\t})\t\r\n\t\t\t\r\n\t\t\t\t});\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tposterDialogClose(){\r\n\t\t\t\tthis.showposter = false\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tupIdcardHead(){\r\n\t\t\t\t\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\t//console.log(data)\r\n\t\t\t\t\tthat.idcard_front = data[0]\r\n\t\t\t\t})\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tupIdcardBack(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\t//console.log(data)\r\n\t\t\t\t\tthat.idcard_back = data[0]\r\n\t\t\t\t})\t\r\n\t\t\t},\r\n\r\n\t\t\tformSubmit: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tvar realname = this.realname\r\n\t\t\t\tvar idcard = this.idcard\r\n\t\t\t\tvar idcard_front = this.idcard_front;\r\n\t\t\t\tvar idcard_back = this.idcard_back;\r\n\t\t\t\tvar is_eid_verify = this.is_eid_verify;\r\n\t\t\t\t\r\n\t\t\t\tif (realname == '') {\r\n\t\t\t\t\tapp.alert('请输入姓名');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (idcard == '') {\r\n\t\t\t\t\t\tapp.alert('请输入身份证号码');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif(idcard_front == '../../static/img/upload.png'){\r\n\t\t\t\t\tapp.alert('请上传身份证正面');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif(idcard_back== '../../static/img/upload.png'){\r\n\t\t\t\t\tapp.alert('请上传身份证背面');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiFace/detect', {\r\n\t\t\t\t\tname: realname,\r\n\t\t\t\t\tcard_id: idcard,\r\n\t\t\t\t\ttype: 'ZHIMACREDIT',\r\n\t\t\t\t\timg_front: idcard_front,\r\n\t\t\t\t\timg_reverse: idcard_back\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tthat.orderNumber = data.data.orderNumber;\r\n\t\t\t\t\t\tthat.showposter = true\r\n\t\t\t\t\t\tuqrcode.make({\r\n\t\t\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\t\t\tcomponentInstance: that,\r\n\t\t\t\t\t\t\ttext: data.data.originalUrl,\r\n\t\t\t\t\t\t\tsize: 250,\r\n\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\t\t\t\tforegroundColor: '#000000',\r\n\t\t\t\t\t\t\tfileType: 'jpg',\r\n\t\t\t\t\t\t\terrorCorrectLevel: uqrcode.errorCorrectLevel.H,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t\t\t// \turl: '../../pages/index/webView?url='+data.data.originalUrl\r\n\t\t\t\t\t\t// }) \r\n\t\t\t\t// \t\tapp.showLoading('提交中');\r\n\t\t\t\t// \t\tapp.post(\"ApiMy/setfield2\", {\r\n\t\t\t\t// \t\t\trealname: realname,\r\n\t\t\t\t// \t\t\tidcard: idcard,\r\n\t\t\t\t// \t\t\tidcard_back : idcard_back,\r\n\t\t\t\t// \t\t\tidcard_front : idcard_front\r\n\t\t\t\t// \t\t}, function(data) {\r\n\t\t\t\t// \t\t\tapp.showLoading(false);\r\n\t\t\t\t// \t\t\tif (data.status == 1) {\r\n\t\t\t\t// \t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t// \t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t// \t\t\t\t\t\t   app.goto('/pages/my/seteid');\r\n\t\t\t\t// \t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t// \t\t\t} else {\r\n\t\t\t\t// \t\t\t\tapp.error(data.msg);\r\n\t\t\t\t// \t\t\t}\r\n\t\t\t\t// \t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t}\t\r\n\t\t\t\t \r\n\t\t\t\t});\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t\r\n\t\t.auth{\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t& text{\r\n\t\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\t\tcolor: #7C7597;\r\n\t\t\t}\r\n\t\t\t.infos{\r\n\t\t\t\t.list{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tpadding: 60rpx 78rpx 0;\r\n\t\t\t\t\t>text{\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #160651;\r\n\t\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\t\tmargin-bottom: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t>input{\r\n\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tpadding-left: 32rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.upload{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tpadding: 62rpx 54rpx 30rpx 54rpx;\r\n\t\t\t\t\t\t& image{\r\n\t\t\t\t\t\t\twidth: 248rpx;\r\n\t\t\t\t\t\t\theight: 134rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>text{\r\n\t\t\t\t\t\t\tmargin-top: 38rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.button{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding: 0 82rpx;\r\n\t\t\t\tmargin-top: 78rpx;\r\n\t\t\t\t>text{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 84rpx;\r\n\t\t\t\t\tbackground: #533CD7;\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 30rpx 0rpx rgba(83,60,215,0.4600);\r\n\t\t\t\t\tborder-radius: 43rpx;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 84rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.text{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t.form {\r\n\t\twidth: 94%;\r\n\t\tmargin: 20rpx 3%;\r\n\t\tborder-radius: 5px;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tpadding: 0 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\theight: 98rpx;\r\n\t\tline-height: 98rpx;\r\n\t}\r\n\r\n\t.form-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.form-item .label {\r\n\t\tcolor: #000;\r\n\t\twidth: 200rpx;\r\n\t}\r\n\r\n\t.form-item .input {\r\n\t\tflex: 1;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.set-btn {\r\n\t\twidth: 90%;\r\n\t\tmargin: 60rpx 5%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t\r\n\t\r\n\t.posterDialog {\r\n\t\tposition: fixed;\r\n\t\tz-index: 9;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0.8);\r\n\t\ttop: var(--window-top);\r\n\t\tleft: 0\r\n\t}\r\n\t\r\n\t.posterDialog .main {\r\n\t\twidth: 74%;\r\n\t\tmargin: 100px 13% 15px 13%;\r\n\t\tbackground: #fff;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding-bottom: 10px;\r\n\t}\r\n\t\r\n\t.posterDialog .close {\r\n\t\tposition: absolute;\r\n\t\tpadding: 20rpx;\r\n\t\ttop: 0;\r\n\t\tright: 0\r\n\t}\r\n\t\r\n\t.posterDialog .close .img {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.posterDialog .content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 70rpx 20rpx 10rpx 20rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 30rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\t\r\n\t.posterDialog .content canvas {\r\n\t\twidth: 500rpx;\r\n\t\theight: 500rpx;\r\n\t}\r\n\t\r\n\t.posterDialog .tips {\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 20rpx 20rpx 20rpx;\r\n\t\ttext-align: center;\r\n\t\t\r\n\t\ttext {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.sub-tips {\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-size: 24rpx;\r\n\r\n\t\t\t.red {\r\n\t\t\t\tcolor: #ff0000;\r\n\t\t\t\tdisplay: inline;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t\r\n\t.btn-s {\r\n\t\ttext-align: center;\r\n\t\tpadding: 10px;\r\n\t\tbackground: #533CD7;\r\n\t\tmargin: 10px 20px;\r\n\t\tborder-radius: 20px;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimgzfb.vue?vue&type=style&index=0&id=1f3ba238&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimgzfb.vue?vue&type=style&index=0&id=1f3ba238&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115070729\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}