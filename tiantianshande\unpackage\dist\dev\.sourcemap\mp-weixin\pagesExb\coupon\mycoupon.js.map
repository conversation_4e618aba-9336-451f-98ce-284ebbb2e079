{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?2dc7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?c750", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?47e2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?cf2e", "uni-app:///pagesExb/coupon/mycoupon.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?db28", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/mycoupon.vue?412b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "givecheckbox", "checkednum", "test", "shareTitle", "sharePic", "shareDesc", "shareLink", "onLoad", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "console", "title", "pic", "desc", "link", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "bid", "uni", "changetab", "scrollTop", "duration", "changeradio", "ids", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiEjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;IACAA;IACA;MAAAC;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAJ;MAAAC;MAAAC;MAAAC;IAAA;IACA;IACAJ;IACAA;IACA;MACAC;MACAK;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAA3B;QAAAE;QAAA0B;MAAA;QACAF;QACAG;UACAZ;QACA;QACA;QACA;UACAS;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACAD;QACAE;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAhC;QACAyB;MACA;QACAzB;QACAyB;MACA;MACAA;MACAA;MACAV;MACA;MACA;QACA;UACAkB;QACA;MACA;MACAA;MACAR;MACAA;MACAA;MACA;QACAA;MACA;QACAA;MACA;MACAV;MACAU;QAAAT;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACAe;MACAR;MACA;IACA;IACAS;MACA;MACAV;MACAG;QACAQ;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YAEAX;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/coupon/mycoupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/coupon/mycoupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mycoupon.vue?vue&type=template&id=0d78ef76&\"\nvar renderjs\nimport script from \"./mycoupon.vue?vue&type=script&lang=js&\"\nexport * from \"./mycoupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mycoupon.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/coupon/mycoupon.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=template&id=0d78ef76&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 =\n          !item.from_mid &&\n          (item.isgive == 1 || item.isgive == 2) &&\n          item.checked\n            ? _vm.t(\"color1\")\n            : null\n        var m1 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m2 = item.type == 10 ? _vm.t(\"color1\") : null\n        var m3 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m4 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m5 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m6 = item.type == 5 ? _vm.t(\"color1\") : null\n        var m7 =\n          item.type == 1 || item.type == 4 || item.type == 5\n            ? _vm.t(\"color1\")\n            : null\n        var m8 = item.type == 1 ? _vm.t(\"color1rgb\") : null\n        var m9 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m10 = item.type == 2 ? _vm.t(\"color1rgb\") : null\n        var m11 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m12 = item.type == 3 ? _vm.t(\"color1rgb\") : null\n        var m13 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m14 = item.type == 4 ? _vm.t(\"color1rgb\") : null\n        var m15 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m16 = item.type == 5 ? _vm.t(\"color1rgb\") : null\n        var m17 = item.type == 5 ? _vm.t(\"color1\") : null\n        var m18 =\n          !item.from_mid && (item.isgive == 1 || item.isgive == 2)\n            ? _vm.t(\"color2rgb\")\n            : null\n        var m19 =\n          !item.from_mid && (item.isgive == 1 || item.isgive == 2)\n            ? _vm.t(\"color2\")\n            : null\n        var m20 =\n          item.isgive != 2 && _vm.st == 0 && (item.type == 1 || item.type == 10)\n            ? _vm.inArray(item.fwtype, [0, 1, 2])\n            : null\n        var m21 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          (item.type == 1 || item.type == 10) &&\n          m20\n            ? _vm.t(\"color1\")\n            : null\n        var m22 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          (item.type == 1 || item.type == 10) &&\n          m20\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m23 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          (item.type == 1 || item.type == 10) &&\n          item.fwtype == 4\n            ? _vm.t(\"color1\")\n            : null\n        var m24 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          (item.type == 1 || item.type == 10) &&\n          item.fwtype == 4\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m25 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          !(item.type == 1 || item.type == 10)\n            ? _vm.t(\"color1\")\n            : null\n        var m26 =\n          item.isgive != 2 &&\n          _vm.st == 0 &&\n          !(item.type == 1 || item.type == 10)\n            ? _vm.t(\"color1rgb\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n          m16: m16,\n          m17: m17,\n          m18: m18,\n          m19: m19,\n          m20: m20,\n          m21: m21,\n          m22: m22,\n          m23: m23,\n          m24: m24,\n          m25: m25,\n          m26: m26,\n        }\n      })\n    : null\n  var m27 = _vm.isload && _vm.checkednum > 0 ? _vm.getplatform() : null\n  var m28 =\n    _vm.isload && _vm.checkednum > 0 && m27 == \"app\" ? _vm.t(\"color2\") : null\n  var m29 =\n    _vm.isload && _vm.checkednum > 0 && m27 == \"app\" ? _vm.t(\"color2rgb\") : null\n  var m30 =\n    _vm.isload && _vm.checkednum > 0 && !(m27 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m31 =\n    _vm.isload && _vm.checkednum > 0 && !(m27 == \"app\") && m30 == \"mp\"\n      ? _vm.t(\"color2\")\n      : null\n  var m32 =\n    _vm.isload && _vm.checkednum > 0 && !(m27 == \"app\") && m30 == \"mp\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m33 =\n    _vm.isload && _vm.checkednum > 0 && !(m27 == \"app\") && !(m30 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m34 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m27 == \"app\") &&\n    !(m30 == \"mp\") &&\n    m33 == \"h5\"\n      ? _vm.t(\"color2\")\n      : null\n  var m35 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m27 == \"app\") &&\n    !(m30 == \"mp\") &&\n    m33 == \"h5\"\n      ? _vm.t(\"color2rgb\")\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m27 == \"app\") &&\n    !(m30 == \"mp\") &&\n    !(m33 == \"h5\")\n      ? _vm.t(\"color2\")\n      : null\n  var m37 =\n    _vm.isload &&\n    _vm.checkednum > 0 &&\n    !(m27 == \"app\") &&\n    !(m30 == \"mp\") &&\n    !(m33 == \"h5\")\n      ? _vm.t(\"color2rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<dd-tab :itemdata=\"['未使用','已使用','已过期']\" :itemst=\"['0','1','2']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\r\n\r\n\t\t<view class=\"coupon-list\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"coupon\" @tap.stop=\"goto\" :data-url=\"'coupondetail?rid=' + item.id\" :style=\"!item.from_mid && (item.isgive == 1 || item.isgive == 2)?'padding-left:40rpx':''\">\r\n\t\t\t\t<view class=\"radiobox\" @tap.stop=\"changeradio\" :data-index=\"index\"><view class=\"radio\" :style=\"item.checked ? 'background:'+t('color1')+';border:0' : ''\" v-if=\"!item.from_mid && (item.isgive == 1 || item.isgive == 2)\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view></view>\r\n\t\t\t\t<view class=\"pt_left\">\r\n\t\t\t\t\t<view class=\"pt_left-content\">\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==1\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==10\"><text class=\"t1\">{{item.discount/10}}</text><text class=\"t0\">折</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==2\">礼品券</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==3\"><text class=\"t1\">{{item.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==4\">抵运费</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==5\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f2\" :style=\"{color:t('color1')}\" v-if=\"item.type==1 || item.type==4 || item.type==5\">\r\n\t\t\t\t\t\t\t<text v-if=\"item.minprice>0\">满{{item.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"pt_right\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.couponname}}</view>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==1\">代金券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==2\">礼品券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==3\">计次券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==4\">运费抵扣券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==5\">餐饮券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"!item.from_mid && (item.isgive == 1 || item.isgive == 2)\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">可赠送</text>\r\n\t\t\t\t\t\t<view class=\"t4\" v-if=\"item.bid>0\">适用商家：{{item.bname}}</view>\r\n\t\t\t\t\t\t<view class=\"t3\" :style=\"item.bid>0?'margin-top:0':'margin-top:10rpx'\">有效期至 {{item.endtime}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"item.isgive!=2 && st==0\">\r\n\t\t\t\t\t\t<block v-if=\"item.type==1 || item.type==10\">\r\n\t\t\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"inArray(item.fwtype,[0,1,2])\" @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/prolist?cpid='+item.couponid+(item.bid?'&bid='+item.bid:'')\">去使用</button>\r\n\t\t\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-if=\"item.fwtype == 4\" @tap.stop=\"goto\" :data-url=\"'/yuyue/prolist?cpid='+item.couponid+(item.bid?'&bid='+item.bid:'')\">去使用</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"goto\" :data-url=\"'coupondetail?rid=' + item.id\">去使用</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<image class=\"sygq\" v-if=\"st==1\" :src=\"pre_url+'/static/img/ysy.png'\"></image>\r\n\t\t\t\t\t<image class=\"sygq\" v-if=\"st==2\" :src=\"pre_url+'/static/img/ygq.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<view class=\"giveopbox\" v-if=\"checkednum > 0\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot3'\">\r\n\t\t\t<view class=\"btn-give\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\r\n\t\t\t<view class=\"btn-give\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\r\n\t\t\t<view class=\"btn-give\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">转赠好友({{checkednum}}张)</view>\r\n\t\t\t<button class=\"btn-give\" open-type=\"share\" v-else :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\" >转赠好友({{checkednum}}张)</button>\r\n\t\t</view>\r\n\t\t<view style=\"display:none\">{{test}}</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n      st: 0,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n\t\t\tgivecheckbox:false,\r\n\t\t\tcheckednum:0,\r\n\t\t\ttest:'',\r\n\t\t\tshareTitle:'',\r\n\t\t\tsharePic:'',\r\n\t\t\tshareDesc:'',\r\n\t\t\tshareLink:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.st = this.opt.st || 0;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n\tonShareAppMessage:function(){\r\n\t\tconsole.log('this.shareLink');\r\n\t\tconsole.log(this.shareLink);\r\n\t\treturn this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n      var bid = that.opt && (that.opt.bid || that.opt.bid === '0') ? that.opt.bid : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.nodata = false;\r\n      app.post('ApiCoupon/mycoupon', {st: st,pagenum: pagenum,bid: bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '我的' + that.t('优惠券')\r\n\t\t\t\t});\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.checkednum = 0;\r\n\t\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\t\tthat.givecheckbox = res.givecheckbox;\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    changetab: function (st) {\r\n      this.st = st;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n    changeradio: function (e) {\r\n      var that = this;\r\n      var index = e.currentTarget.dataset.index;\r\n\t\t\tvar datalist = that.datalist;\r\n      var checked = datalist[index].checked;\r\n\t\t\tif(checked){\r\n\t\t\t\tdatalist[index].checked = false;\r\n\t\t\t\tthat.checkednum--;\r\n\t\t\t}else{\r\n\t\t\t\tdatalist[index].checked = true;\r\n\t\t\t\tthat.checkednum++;\r\n\t\t\t}\r\n\t\t\tthat.datalist = datalist;\r\n\t\t\tthat.test = Math.random();\r\n\t\t\tconsole.log(that.checkednum);\r\n\t\t\tvar ids = [];\r\n\t\t\tfor(var i in datalist){\r\n\t\t\t\tif(datalist[i].checked){\r\n\t\t\t\t\tids.push(datalist[i].id);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tids = ids.join(',');\r\n\t\t\tthat.shareTitle = '送你'+that.checkednum+'张'+that.t('优惠券');\r\n\t\t\tthat.shareDesc = '点击前往查看领取';\r\n\t\t\tthat.sharePic = app.globalData.initdata.logo;\r\n\t\t\tif(app.globalData.platform == 'h5' || app.globalData.platform == 'mp' || app.globalData.platform == 'app'){\r\n\t\t\t\tthat.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/coupon/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;\r\n\t\t\t}else{\r\n\t\t\t\tthat.shareLink = '/pages/coupon/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;\r\n\t\t\t}\r\n\t\t\tconsole.log(that.shareLink);\r\n\t\t\tthat.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n\t\t    itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t    success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.shareTitle;\r\n\t\t\t\t\t\tsharedata.summary = that.shareDesc;\r\n\t\t\t\t\t\tsharedata.href = that.shareLink;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.sharePic;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\r\n.coupon-list{width:100%;padding:20rpx}\r\n.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden;align-items:center;position:relative;background: #fff;}\r\n.coupon .pt_left{background: #fff;min-height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}\r\n.coupon .pt_left .t0{padding-right:0;}\r\n.coupon .pt_left .t1{font-size:60rpx;}\r\n.coupon .pt_left .t2{padding-left:10rpx;}\r\n.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}\r\n.coupon .pt_right{background: #fff;width:70%;display:flex;min-height:200rpx;text-align: left;padding:20rpx 20rpx;position:relative}\r\n.coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}\r\n.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}\r\n.coupon .pt_right .f1 .t2{height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx; margin-right: 16rpx;}\r\n.coupon .pt_right .f1 .t2:last-child {margin-right: 0;}\r\n.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-28rpx;border-radius:28rpx;width:160rpx;height:56rpx;line-height:56rpx;color:#fff}\r\n.coupon .pt_right .sygq{position:absolute;right:30rpx;top:50%;margin-top:-50rpx;width:100rpx;height:100rpx;}\r\n\r\n.coupon .pt_left.bg3{background:#ffffff;color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t1{color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t3{color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t4{color:#999999!important}\r\n\r\n.coupon .radiobox{position:absolute;left:0;padding:20rpx}\r\n.coupon .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;}\r\n.coupon .radio .radio-img{width:100%;height:100%}\r\n.giveopbox{position:fixed;bottom:0;left:0;width:100%;}\r\n.btn-give{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mycoupon.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024499\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}