{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?e15a", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?7494", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?9aea", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?44a6", "uni-app:///shopPackage/shop/cart.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?d746", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/cart.vue?000e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "indexurl", "cartlist", "tjdatalist", "totalprice", "selectedcount", "allchecked", "xixie", "xixie_cartlist", "onLoad", "onShow", "onPullDownRefresh", "methods", "getdata", "that", "app", "bid", "calculate", "ids", "changeradio", "changeradio2", "isallchecked", "changeradioAll", "cartdelete", "id", "type", "setTimeout", "cartdeleteb", "toOrder", "tmpprostr", "prodata", "xixie_prodata", "gwcplus", "num", "gwcminus", "gwcinput", "addcart"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoH7wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACAA;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACAC;YACA;YACAd;YACAC;UACA;QACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAa;cACA;cACAd;cACAC;YACA;UACA;QACA;MACA;MACAS;MACAA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACAX;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAM;MACA;QACA;QACA;QACA;UACAZ;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAY;MACA;MACAA;IACA;IAEAM;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACAlB;QACA;UACAA;QACA;QACA;QACA;UACA;YACAmB;UACA;QACA;QACA;UACAnB;QACA;UACAA;QACA;QACAY;MACA;QACA;QACA;QACA;UACAN;QACA;UACAA;QACA;QACA;QACA;UACA;YACAa;UACA;QACA;QACA;UACAb;QACA;UACAA;QACA;QACAM;MACA;MAEAA;IACA;IACAQ;MACA;MACA;MACA;MACA;QACApB;QACA;UACAA;QACA;MACA;MACAY;MAEA;MACA;QACA;QACA;UACAN;UACA;YACAA;UACA;QACA;QACAM;MACA;MAEAA;MACAA;IACA;IACAS;MACA;MACA;MACA;MACAR;QACAA;UAAAS;UAAAC;QAAA;UACAV;UACA;YACAW;cACAZ;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MAEAZ;QACAA;UAAAC;UAAAS;QAAA;UACAV;UACAW;YACAZ;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;YACA;YACA;YACA;cACAC;YACA;YACAC;UACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;YACA;cACA;cACAC;YACA;UACA;QACA;QACA;QACA;QACA;UACAhB;UACA;QACA;QACA;UACAe;QACA;MACA;MACA;QACAf;QACA;MACA;MACA;QACAA;MACA;QACAA;MACA;IACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MAEA;MACA;QACA;QACA;UACAjB;UACA;QACA;QACA;QACAb;QACA;MACA;QACA;QACA;UACAa;UACA;QACA;QACA;QACAP;QACA;MACA;MAEA;MACA;MACAO;QAAAS;QAAAS;QAAAR;MAAA;QACA;UACA;QAAA,CACA;UACAV;UACA;YACAb;UACA;YACAM;UACA;QACA;MACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;UACAnB;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;QACAb;QACA;QACA;MACA;QACA;QACAM;QACA;QACA;MACA;MAEA;MACAO;QAAAS;QAAAS;QAAAR;MAAA;QACA;UACA;QAAA,CACA;UACAV;UACA;YACAb;UACA;YACAM;UACA;QACA;MACA;IACA;IACA;IACA2B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACApB;QACA;MACA;MACA;QACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;QACAb;QACA;QACA;MACA;QACA;QACA;UACAa;UACA;QACA;QACA;QACAP;QACA;QACA;MACA;MACA;MACAO;QAAAS;QAAAS;QAAAR;MAAA;QACA;UACA;QAAA,CACA;UACAV;QACA;MACA;IACA;IACAqB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvhBA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/cart.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/cart.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cart.vue?vue&type=template&id=aa1dc3c8&\"\nvar renderjs\nimport script from \"./cart.vue?vue&type=script&lang=js&\"\nexport * from \"./cart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cart.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/cart.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart.vue?vue&type=template&id=aa1dc3c8&\"", "var components\ntry {\n  components = {\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload\n    ? _vm.xixie && _vm.xixie_cartlist && _vm.xixie_cartlist.length > 0\n    : null\n  var l1 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.xixie_cartlist, function (itemx, indexx) {\n          var $orig = _vm.__get_orig(itemx)\n          var m0 = itemx.checked ? _vm.t(\"color1\") : null\n          var g1 = itemx.prolist.length\n          var m2 = _vm.t(\"color1\")\n          var l0 = _vm.__map(itemx.prolist, function (itemx2, indexx2) {\n            var $orig = _vm.__get_orig(itemx2)\n            var m1 = itemx2.checked ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m1: m1,\n            }\n          })\n          return {\n            $orig: $orig,\n            m0: m0,\n            g1: g1,\n            m2: m2,\n            l0: l0,\n          }\n        })\n      : null\n  var g2 = _vm.isload ? _vm.cartlist.length : null\n  var l3 =\n    _vm.isload && g2 > 0\n      ? _vm.__map(_vm.cartlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = item.checked ? _vm.t(\"color1\") : null\n          var g3 = item.prolist.length\n          var l2 = _vm.__map(item.prolist, function (item2, index2) {\n            var $orig = _vm.__get_orig(item2)\n            var m4 = item2.checked ? _vm.t(\"color1\") : null\n            var m5 =\n              item2.guige && item2.guige.sell_price ? _vm.t(\"color1\") : null\n            var m6 =\n              !(item2.guige && item2.guige.sell_price) &&\n              item2.product.is_yh == 0 &&\n              item2.product.is_newcustom == 1\n                ? _vm.t(\"color1\")\n                : null\n            var m7 =\n              !(item2.guige && item2.guige.sell_price) &&\n              !(item2.product.is_yh == 0 && item2.product.is_newcustom == 1)\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m4: m4,\n              m5: m5,\n              m6: m6,\n              m7: m7,\n            }\n          })\n          return {\n            $orig: $orig,\n            m3: m3,\n            g3: g3,\n            l2: l2,\n          }\n        })\n      : null\n  var g4 = _vm.isload ? !_vm.xixie_cartlist && _vm.cartlist.length <= 0 : null\n  var m8 = _vm.isload && g4 ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload && g4 ? _vm.t(\"color1rgb\") : null\n  var g5 = _vm.tjdatalist.length\n  var g6 = _vm.cartlist.length > 0 || (_vm.xixie && _vm.xixie_cartlist)\n  var m10 = g6 && _vm.allchecked ? _vm.t(\"color1\") : null\n  var m11 = g6 ? _vm.t(\"color1\") : null\n  var m12 = g6 ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g2: g2,\n        l3: l3,\n        g4: g4,\n        m8: m8,\n        m9: m9,\n        g5: g5,\n        g6: g6,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<block v-if=\"xixie && xixie_cartlist && xixie_cartlist.length>0\">\r\n\t\t\t\t<view class=\"cartmain\">\r\n\t\t\t\t\t<block v-for=\"(itemx, indexx) in xixie_cartlist\" :key=\"itemx.bid\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t\t\t<view :data-type='2' @tap.stop=\"changeradio\" :data-index=\"indexx\" class=\"radio\" :style=\"itemx.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"btitle-name\" :data-type='2' @tap=\"goto\" :data-url=\"'/xixie/index?id=' + itemx.business.id\">{{itemx.business.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"> </view>\r\n\t\t\t\t\t\t\t\t<view class=\"btitle-del\" :data-type='2' @tap=\"cartdeleteb\" :data-bid=\"itemx.bid\"><image class=\"img\" src=\"/static/img/del.png\"/><text style=\"margin-left:10rpx\">删除</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\" v-for=\"(itemx2,indexx2) in itemx.prolist\" :key=\"indexx2\">\r\n\t\t\t\t\t\t\t\t<view :data-type='2' @tap.stop=\"changeradio2\" :data-index=\"indexx\" :data-index2=\"indexx2\" class=\"radio\" :style=\"itemx2.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"proinfo\" :style=\"(itemx.prolist).length == indexx2+1 ? 'border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"itemx2.product.pic\" class=\"img\"/>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detail\" style=\"height: 190rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"title\"><text>{{itemx2.product.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price\" :style=\"{color:t('color1')}\"><text style=\"font-size:24rpx\">￥</text>{{itemx2.product.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"minus\" :data-type='2' @tap.stop=\"gwcminus\" :data-index=\"indexx\" :data-index2=\"indexx2\" :data-cartid=\"itemx2.id\" :data-num=\"itemx2.num\" :data-buymax=\"itemx2.product.buymax\"><image class=\"img\" src=\"/static/img/cart-minus.png\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<input class=\"input\" :data-type='2' @tap.stop=\"\" type=\"number\" :value=\"itemx2.num\" @blur=\"gwcinput\" :data-index=\"indexx\" :data-index2=\"indexx2\" :data-cartid=\"itemx2.id\" :data-num=\"itemx2.num\"  :data-buymax=\"itemx2.product.buymax\"></input>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" :data-type='2' @tap.stop=\"gwcplus\" :data-index=\"indexx\" :data-index2=\"indexx2\" :data-num=\"itemx2.num\" :data-cartid=\"itemx2.id\" :data-buymax=\"itemx2.product.buymax\"><image class=\"img\" src=\"/static/img/cart-plus.png\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"prodel\" :data-type='2' @tap.stop=\"cartdelete\" :data-cartid=\"itemx2.id\" style=\"top:106rpx\"><image class=\"prodel-img\" src=\"/static/img/del.png\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"cartlist.length>0\">\r\n\t\t\t\t<view class=\"cartmain\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in cartlist\" :key=\"item.bid\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"changeradio\" :data-index=\"index\" class=\"radio\" :style=\"item.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"btitle-name\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.business.id\">{{item.business.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"> </view>\r\n\t\t\t\t\t\t\t\t<view class=\"btitle-del\" @tap=\"cartdeleteb\" :data-bid=\"item.bid\"><image class=\"img\" src=\"/static/img/del.png\"/><text style=\"margin-left:10rpx\">删除</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\" v-for=\"(item2,index2) in item.prolist\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.product.id\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"changeradio2\" :data-index=\"index\" :data-index2=\"index2\" class=\"radio\" :style=\"item2.checked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"proinfo\" :style=\"(item.prolist).length == index2+1 ? 'border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.product.pic\" class=\"img\"/>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"title\"><text>{{item2.product.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"desc\"><text>{{item2.guige.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price\" v-if=\"item2.guige && item2.guige.sell_price\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item2.guige.sell_price}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price\" v-else-if=\"item2.product.is_yh == 0 && item2.product.is_newcustom == 1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item2.product.yh_price}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"price\" v-else :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">￥</text>{{item2.product.sell_price}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap.stop=\"gwcminus\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"><image class=\"img\" src=\"/static/img/cart-minus.png\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<input class=\"input\" @tap.stop=\"\" type=\"number\" :value=\"item2.num\" @blur=\"gwcinput\" :data-max=\"item2.guige.store_nums\" :data-index=\"index\" :data-index2=\"index2\" :data-cartid=\"item2.id\" :data-num=\"item2.num\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"></input>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap.stop=\"gwcplus\" :data-index=\"index\" :data-index2=\"index2\" :data-max=\"item2.guige.store_nums\" :data-num=\"item2.num\" :data-cartid=\"item2.id\" :data-limit_start=\"item2.product.limit_start\" :data-limit_start_guige=\"item2.guige.limit_start\"><image class=\"img\" src=\"/static/img/cart-plus.png\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"prodel\" @tap.stop=\"cartdelete\" :data-cartid=\"item2.id\"><image class=\"prodel-img\" src=\"/static/img/del.png\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t<block v-if=\"!xixie_cartlist && cartlist.length<=0\">\r\n\t\t\t\t<view class=\"data-empty\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/cartnull.png'\" class=\"data-empty-img\" style=\"width:120rpx;height:120rpx\"/>\r\n\t\t\t\t\t<view class=\"data-empty-text\" style=\"margin-top:20rpx;font-size:24rpx\">购物车空空如也~</view>\r\n\t\t\t\t\t<view style=\"width:400rpx;border:0;height:80rpx;line-height:80rpx;margin:40rpx auto;border-radius:6rpx;color:#fff\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"indexurl\" data-opentype=\"reLaunch\">去选购</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</block>\r\n\t\t<view v-if=\"tjdatalist.length > 0\">\r\n\t\t\t<view class=\"xihuan\">\r\n\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t<view class=\"xihuan-text\">\r\n\t\t\t\t\t<image src=\"/static/img/xihuan.png\" class=\"img\"/>\r\n\t\t\t\t\t<text class=\"txt\">为您推荐</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"prolist\">\r\n\t\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\" :menuindex=\"menuindex\"></dp-product-item>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<block v-if=\"cartlist.length>0 ||(xixie && xixie_cartlist)\">\r\n\t\t<view style=\"height:auto;position:relative\">\r\n\t\t\t<view style=\"width:100%;height:110rpx\"></view>\r\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t<view @tap.stop=\"changeradioAll\" class=\"radio\" :style=\"allchecked ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t<view @tap.stop=\"changeradioAll\" class=\"text0\">全选（{{selectedcount}}）</view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view class=\"text1\">合计：</view>\r\n\t\t\t\t<view class=\"text2\"><text style=\"font-size:20rpx\">￥</text>{{totalprice}}</view>\r\n\t\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"toOrder\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t</block>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n\t</template>\r\n\t\r\n\t<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t  data() {\r\n\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex:-1,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\r\n\t\t\t\t// indexurl:app.globalData.indexurl,\r\n\t\t\t\tindexurl:app.globalData.indexurl,\r\n\t\t\t\tcartlist:[],\r\n\t\t\t\ttjdatalist:[],\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tselectedcount: 0,\r\n\t\t\t\tallchecked:true,\r\n\t\t\t\t\r\n\t\t\t\txixie:false,\r\n\t\t\t\txixie_cartlist:''\r\n\t\t};\r\n\t  },\r\n\t  \r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t  },\r\n\t\tonShow:function(){\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t  methods: {\r\n\t\tgetdata: function (){\r\n\t\t\tvar that = this;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tif(bid){\r\n\t\t\t\tthat.indexurl = 'pagesExt/business/index?id='+bid;\r\n\t\t\t}\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiShop/cart', {bid:bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.cartlist = res.cartlist;\r\n\t\t\t\tthat.tjdatalist = res.tjdatalist;\r\n\t\t\t\tif(res.xixie ){\r\n\t\t\t\t\tthat.xixie          = res.xixie;\r\n\t\t\t\t\tthat.xixie_cartlist = res.xixie_cartlist;\r\n\t\t\t\t}\r\n\t\t\t\tthat.calculate();\r\n\t\t\t\tthat.loaded();\r\n\t\t  });\r\n\t\t},\r\n\t\tcalculate: function () {\r\n\t\t  var that = this;\r\n\t\t  var cartlist = that.cartlist;\r\n\t\t  var ids = [];\r\n\t\t  var totalprice = 0.00;\r\n\t\t  var selectedcount = 0;\r\n\t\t\tfor(var i in cartlist){\r\n\t\t\t\tfor(var j in cartlist[i].prolist){\r\n\t\t\t\t\tif(cartlist[i].prolist[j].checked){\r\n\t\t\t\t\t\tids.push(cartlist[i].prolist[j].id);\r\n\t\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\r\n\t\t\t\t\t\ttotalprice += thispro.guige.sell_price * thispro.num;\r\n\t\t\t\t\t\tselectedcount += thispro.num;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar xixie = that.xixie;\r\n\t\t\tvar xixie_cartlist = that.xixie_cartlist;\r\n\t\t\tif(xixie && xixie_cartlist){\r\n\t\t\t\tfor(var xi in xixie_cartlist){\r\n\t\t\t\t\tfor(var xj in xixie_cartlist[xi].prolist){\r\n\t\t\t\t\t\tif(xixie_cartlist[xi].prolist[xj].checked){\r\n\t\t\t\t\t\t\tids.push(xixie_cartlist[xi].prolist[xj].id);\r\n\t\t\t\t\t\t\tvar thispro = xixie_cartlist[xi].prolist[xj];\r\n\t\t\t\t\t\t\ttotalprice += thispro.product.sell_price * thispro.num;\r\n\t\t\t\t\t\t\tselectedcount += thispro.num;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t  that.totalprice = totalprice.toFixed(2);\r\n\t\t  that.selectedcount = selectedcount;\r\n\t\t},\r\n\t\tchangeradio: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar xixie = that.xixie;\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\tif(type == 2){\r\n\t\t\t\tvar xixie_cartlist = that.xixie_cartlist;\r\n\t\t\t\tvar xixie_checked = xixie_cartlist[index].checked;\r\n\t\t\t\tif(xixie_checked){\r\n\t\t\t\t\txixie_cartlist[index].checked = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\txixie_cartlist[index].checked = true;\r\n\t\t\t\t}\r\n\t\t\t\tfor(var i in xixie_cartlist[index].prolist){\r\n\t\t\t\t\txixie_cartlist[index].prolist[i].checked = xixie_cartlist[index].checked;\r\n\t\t\t\t}\r\n\t\t\t\tthat.xixie_cartlist = xixie_cartlist;\r\n\t\t\t}else{\r\n\t\t\t\tvar cartlist = that.cartlist;\r\n\t\t\t\tvar checked = cartlist[index].checked;\r\n\t\t\t\tif(checked){\r\n\t\t\t\t\tcartlist[index].checked = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tcartlist[index].checked = true;\r\n\t\t\t\t}\r\n\t\t\t\tfor(var i in cartlist[index].prolist){\r\n\t\t\t\t\tcartlist[index].prolist[i].checked = cartlist[index].checked;\r\n\t\t\t\t}\r\n\t\t\t\tthat.cartlist = cartlist;\r\n\t\t\t}\r\n\t\t\tthat.calculate();\r\n\t\t},\r\n\t\t\t\r\n\t\tchangeradio2: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar index2 = e.currentTarget.dataset.index2;\r\n\t\t\tif(!type){\r\n\t\t\t\tvar cartlist = that.cartlist;\r\n\t\t\t\tvar checked = cartlist[index].prolist[index2].checked;\r\n\t\t\t\tif(checked){\r\n\t\t\t\t\tcartlist[index].prolist[index2].checked = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tcartlist[index].prolist[index2].checked = true;\r\n\t\t\t\t}\r\n\t\t\t\tvar isallchecked = true;\r\n\t\t\t\tfor(var i in cartlist[index].prolist){\r\n\t\t\t\t\tif(cartlist[index].prolist[i].checked == false){\r\n\t\t\t\t\t\tisallchecked = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(isallchecked){\r\n\t\t\t\t\tcartlist[index].checked = true;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tcartlist[index].checked = false;\r\n\t\t\t\t}\r\n\t\t\t\tthat.cartlist = cartlist;\r\n\t\t\t}else{\r\n\t\t\t\tvar xixie_cartlist = that.xixie_cartlist;\r\n\t\t\t\tvar checked = xixie_cartlist[index].prolist[index2].checked;\r\n\t\t\t\tif(checked){\r\n\t\t\t\t\txixie_cartlist[index].prolist[index2].checked = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\txixie_cartlist[index].prolist[index2].checked = true;\r\n\t\t\t\t}\r\n\t\t\t\tvar isallchecked = true;\r\n\t\t\t\tfor(var i in xixie_cartlist[index].prolist){\r\n\t\t\t\t\tif(xixie_cartlist[index].prolist[i].checked == false){\r\n\t\t\t\t\t\tisallchecked = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(isallchecked){\r\n\t\t\t\t\txixie_cartlist[index].checked = true;\r\n\t\t\t\t}else{\r\n\t\t\t\t\txixie_cartlist[index].checked = false;\r\n\t\t\t\t}\r\n\t\t\t\tthat.xixie_cartlist = xixie_cartlist;\r\n\t\t\t}\r\n\t\t\t\t\r\n\t\t\tthat.calculate();\r\n\t\t},\r\n\t\t\tchangeradioAll:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cartlist = that.cartlist;\r\n\t\t\t\tvar allchecked = that.allchecked\r\n\t\t\t\tfor(var i in cartlist){\r\n\t\t\t\t\tcartlist[i].checked = allchecked ? false : true;\r\n\t\t\t\t\tfor(var j in cartlist[i].prolist){\r\n\t\t\t\t\t\tcartlist[i].prolist[j].checked = allchecked ? false : true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.cartlist = cartlist;\r\n\t\t\t\t\r\n\t\t\t\tvar xixie = that.xixie;\r\n\t\t\t\tif(xixie){\r\n\t\t\t\t\tvar xixie_cartlist = that.xixie_cartlist;\r\n\t\t\t\t\tfor(var i in xixie_cartlist){\r\n\t\t\t\t\t\txixie_cartlist[i].checked = allchecked ? false : true;\r\n\t\t\t\t\t\tfor(var j in xixie_cartlist[i].prolist){\r\n\t\t\t\t\t\t\txixie_cartlist[i].prolist[j].checked = allchecked ? false : true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.xixie_cartlist = xixie_cartlist;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.allchecked = allchecked ? false : true;\r\n\t\t\t\tthat.calculate();\r\n\t\t\t},\r\n\t\t\tcartdelete: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = e.currentTarget.dataset.cartid;\r\n\t\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\t\tapp.confirm('确定要从购物车移除吗?', function () {\r\n\t\t\t\t\tapp.post('ApiShop/cartdelete', {id: id,type:type}, function (data) {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tif(data.status == 1){\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t  that.getdata();\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcartdeleteb:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid   = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\r\n\t\t\t\tapp.confirm('确定要从购物车移除吗?', function () {\r\n\t\t\t\t\tapp.post('ApiShop/cartdelete', {bid: bid,type:type}, function (data) {\r\n\t\t\t\t\t\tapp.success('操作成功');\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\ttoOrder: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar cartlist = that.cartlist;\r\n\t\t\tvar ids = [];\r\n\t\t\tvar prodata = [];\r\n\t\t\tfor(var i in cartlist){\r\n\t\t\t\tfor(var j in cartlist[i].prolist){\r\n\t\t\t\t\tif(cartlist[i].prolist[j].checked){\r\n\t\t\t\t\t\t// /\r\n\t\t\t\t\t\tvar thispro = cartlist[i].prolist[j];\r\n\t\t\t\t\t\t\t\t\t\t\tvar tmpprostr = thispro.product.id + ',' + thispro.guige.id + ',' + thispro.num+ ',' + thispro.tid+ ',' + thispro.dhtid;\r\n\t\t\t\t\t\tif(thispro.glassrecord){\r\n\t\t\t\t\t\t\t\t\t\t\t\ttmpprostr += ',' + thispro.glassrecord.id\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tprodata.push(tmpprostr);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar xixie = that.xixie;\r\n\t\t\tif(xixie){\r\n\t\t\t\tvar xixie_cartlist = that.xixie_cartlist;\r\n\t\t\t\tvar xixie_prodata = [];\r\n\t\t\t\tfor(var i in xixie_cartlist){\r\n\t\t\t\t\tfor(var j in xixie_cartlist[i].prolist){\r\n\t\t\t\t\t\tif(xixie_cartlist[i].prolist[j].checked){\r\n\t\t\t\t\t\t\tvar thispro = xixie_cartlist[i].prolist[j];\r\n\t\t\t\t\t\t\txixie_prodata.push(thispro.product.id + ','  + thispro.num);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar len = prodata.length;\r\n\t\t\t\tvar xixie_len = xixie_prodata.length;\r\n\t\t\t\tif(len>0 &&  xixie_len>0){\r\n\t\t\t\t\tapp.alert('洗鞋商品只能单独去结算，不能与其他商城产品一起去结算');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(xixie_len>0){\r\n\t\t\t\t\tprodata = xixie_prodata;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (prodata == undefined || prodata.length == 0) {\r\n\t\t\t\tapp.error('请先选择产品');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(xixie && xixie_len>0){\r\n\t\t\t\tapp.goto('/xixie/buy?&prodata=' + prodata.join('-'));\r\n\t\t\t}else{\r\n\t\t\t\tapp.goto('buy?&prodata=' + prodata.join('-'));\r\n\t\t\t}\r\n\t\t},\r\n\t\t//加\r\n\t\tgwcplus: function (e) {\r\n\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\tvar index  = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\r\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\r\n\t\t\t\r\n\t\t\tvar num = parseInt(e.currentTarget.dataset.num);\r\n\t\t\tif(!type){\r\n\t\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t\t\tif (num >= maxnum) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar cartlist = this.cartlist;\r\n\t\t\t\tcartlist[index].prolist[index2].num++;\r\n\t\t\t\tthis.cartlist = cartlist\r\n\t\t\t}else{\r\n\t\t\t\tvar buymax = parseInt(e.currentTarget.dataset.buymax);\r\n\t\t\t\tif (buymax>0 && num > buymax) {\r\n\t\t\t\t\tapp.alert('每人限购'+buymax);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar xixie_cartlist = this.xixie_cartlist;\r\n\t\t\t\txixie_cartlist[index].prolist[index2].num++;\r\n\t\t\t\tthis.xixie_cartlist = xixie_cartlist\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.calculate();\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post('ApiShop/cartChangenum', {id: cartid,num: num + 1,type:type}, function (data){\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t //that.getdata();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\tif(!type){\r\n\t\t\t\t\t\tcartlist[index].prolist[index2].num--;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\txixie_cartlist[index].prolist[index2].num--;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//减\r\n\t\tgwcminus: function (e) {\r\n\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\r\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\r\n\t\t\t\r\n\t\t\tvar num = parseInt(e.currentTarget.dataset.num);\r\n\t\t\tif (num == 1) return;\r\n\t\t\tif(!type){\r\n\t\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t\t\tvar limit_start = parseInt(e.currentTarget.dataset.limit_start);\r\n\t\t\t\tvar limit_start_guige = parseInt(e.currentTarget.dataset.limit_start_guige);\r\n\t\t\t\tif(limit_start_guige > 0 && num <= limit_start_guige){\r\n\t\t\t\t\tapp.error('该商品规格'+limit_start_guige+'件起售');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(limit_start > 0 && num <= limit_start){\r\n\t\t\t\t\tapp.error('该商品'+limit_start+'件起售');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar cartlist = this.cartlist;\r\n\t\t\t\tcartlist[index].prolist[index2].num--;\r\n\t\t\t\tthis.cartlist = cartlist\r\n\t\t\t\tthis.calculate();\r\n\t\t\t}else{\r\n\t\t\t\tvar xixie_cartlist = this.xixie_cartlist;\r\n\t\t\t\txixie_cartlist[index].prolist[index2].num--;\r\n\t\t\t\tthis.xixie_cartlist = xixie_cartlist\r\n\t\t\t\tthis.calculate();\r\n\t\t\t}\r\n\t\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post('ApiShop/cartChangenum', {id: cartid,num: num - 1,type:type}, function (data) {\r\n\t\t\tif (data.status == 1) {\r\n\t\t\t\t//that.getdata();\r\n\t\t\t} else {\r\n\t\t\t\tapp.error(data.msg);\r\n\t\t\t\tif(!type){\r\n\t\t\t\t\tcartlist[index].prolist[index2].num++;\r\n\t\t\t\t}else{\r\n\t\t\t\t\txixie_cartlist[index].prolist[index2].num++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t  });\r\n\t\t},\r\n\t\t//输入\r\n\t\tgwcinput: function (e) {\r\n\t\t\tvar type  = e.currentTarget.dataset.type?e.currentTarget.dataset.type:'';\r\n\t\t\tvar index = parseInt(e.currentTarget.dataset.index);\r\n\t\t\tvar index2 = parseInt(e.currentTarget.dataset.index2);\r\n\t\t\tvar maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t\tvar cartid = e.currentTarget.dataset.cartid;\r\n\t\t\tvar num = e.currentTarget.dataset.num;\r\n\t\t\tvar newnum = parseInt(e.detail.value);\r\n\t\t\tif (num == newnum) return;\r\n\t\t\tif (newnum < 1) {\r\n\t\t\t\tapp.error('最小数量为1');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(!type){\r\n\t\t\t\tvar limit_start = parseInt(e.currentTarget.dataset.limit_start);\r\n\t\t\t\tvar limit_start_guige = parseInt(e.currentTarget.dataset.limit_start_guige);\r\n\t\t\t\tif(limit_start_guige > 0 && newnum < limit_start_guige){\r\n\t\t\t\t\tapp.error('该商品规格'+limit_start_guige+'件起售');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(limit_start > 0 && newnum < limit_start){\r\n\t\t\t\t\tapp.error('该商品'+limit_start+'件起售');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (newnum > maxnum) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar cartlist = this.cartlist;\r\n\t\t\t\tcartlist[index].prolist[index2].num = newnum;\r\n\t\t\t\tthis.cartlist = cartlist\r\n\t\t\t\tthis.calculate();\r\n\t\t\t}else{\r\n\t\t\t\tvar buymax = parseInt(e.currentTarget.dataset.buymax);\r\n\t\t\t\tif (buymax>0 && num > buymax) {\r\n\t\t\t\t\tapp.alert('每人限购'+buymax);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar xixie_cartlist = this.xixie_cartlist;\r\n\t\t\t\txixie_cartlist[index].prolist[index2].num = newnum;\r\n\t\t\t\tthis.xixie_cartlist = xixie_cartlist\r\n\t\t\t\tthis.calculate();\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post('ApiShop/cartChangenum', {id: cartid,num: newnum,type:type}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t//that.getdata();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\taddcart:function(){\r\n\t\t\tthis.getdata();\r\n\t\t}\r\n\t  }\r\n\t};\r\n\t</script>\r\n\t<style>\r\n\t.container{height:100%}\r\n\t.cartmain .item {width: 94%;margin:20rpx 3%;background: #fff;border-radius:20rpx;padding:30rpx 3%;}\r\n\t.cartmain .item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n\t.cartmain .item .radio .radio-img{width:100%;height:100%}\r\n\t\r\n\t.cartmain .item .btitle{width:100%;display:flex;align-items:center;margin-bottom:30rpx}\r\n\t.cartmain .item .btitle-name{color:#222222;font-weight:bold;font-size:28rpx;}\r\n\t.cartmain .item .btitle-del{display:flex;align-items:center;color:#999999;font-size:24rpx;}\r\n\t.cartmain .item .btitle-del .img{width:24rpx;height:24rpx}\r\n\t\r\n\t.cartmain .item .content {width:100%;position: relative;display:flex;align-items:center;}\r\n\t.cartmain .item .content .proinfo{flex:1;display:flex;padding:20rpx 0;border-bottom:1px solid #f2f2f2}\r\n\t.cartmain .item .content .proinfo .img {width: 176rpx;height: 176rpx;}\r\n\t.cartmain .item .content .detail {flex:1;margin-left:20rpx;height: 176rpx;position: relative;}\r\n\t.cartmain .item .content .detail .title {color: #222222;font-weight:bold;font-size:28rpx;line-height:34rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:68rpx}\r\n\t.cartmain .item .content .detail .desc {margin-top:16rpx;height: 30rpx;line-height: 30rpx;color: #999;overflow: hidden;font-size: 20rpx;}\r\n\t.cartmain .item .content .prodel{width:24rpx;height:24rpx;position:absolute;top:90rpx;right:0}\r\n\t.cartmain .item .content .prodel-img{width:100%;height:100%}\r\n\t.cartmain .item .content .price{margin-top:10rpx;height:60rpx;line-height:60rpx;font-size:32rpx;font-weight:bold;display:flex;align-items:center}\r\n\t.cartmain .item .content .addnum {position: absolute;right: 0;bottom:0rpx;font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n\t.cartmain .item .content .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.cartmain .item .content .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.cartmain .item .content .addnum .img{width:24rpx;height:24rpx}\r\n\t.cartmain .item .content .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\r\n\t.cartmain .item .content .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx;margin: 0 15rpx;}\r\n\t\r\n\t.cartmain .item .bottom{width: 94%;margin: 0 3%;border-top: 1px #e5e5e5 solid;padding: 10rpx 0px;overflow: hidden;color: #ccc;display:flex;align-items:center;justify-content:flex-end}\r\n\t.cartmain .item .bottom .f1{display:flex;align-items:center;color:#333}\r\n\t.cartmain .item .bottom .f1 image{width:40rpx;height:40rpx;border-radius:4px;margin-right:4px}\r\n\t.cartmain .item .bottom .op {border: 1px #ff4246 solid;border-radius: 10rpx;color: #ff4246;padding: 0 10rpx;height: 46rpx;line-height: 46rpx;margin-left: 10rpx;}\r\n\t\r\n\t.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\r\n\t.footer .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\r\n\t.footer .radio .radio-img{width:100%;height:100%}\r\n\t.footer .text0{color:#666666;font-size:24rpx;}\r\n\t.footer .text1 {height: 110rpx;line-height: 110rpx;color:#444;font-weight:bold;font-size:24rpx;margin-right:10rpx}\r\n\t.footer .text2 {color: #F64D00;font-size: 36rpx;font-weight:bold}\r\n\t.footer .op{width: 216rpx;height: 80rpx;line-height:80rpx;border-radius: 6rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center;margin-left:30rpx}\r\n\t\r\n\t.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:12rpx 160rpx}\r\n\t.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\r\n\t.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\r\n\t.xihuan-text .txt{color:#111;font-size:30rpx}\r\n\t.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\r\n\t\r\n\t.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\r\n\t\r\n\t.data-empty {width: 100%; text-align: center; padding-top:100rpx;padding-bottom:100rpx}\r\n\t.data-empty-img{ width: 300rpx; height: 300rpx; display: inline-block; }\r\n\t.data-empty-text{ display: block; text-align: center; color: #999999; font-size:32rpx; width: 100%; margin-top: 30rpx; } \r\n\t</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115007786\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}