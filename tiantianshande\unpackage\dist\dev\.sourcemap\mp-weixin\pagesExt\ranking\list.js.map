{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?c133", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?8b15", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?4c35", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?2973", "uni-app:///pagesExt/ranking/list.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?db3d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/ranking/list.vue?dde8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "nomore", "rankType", "page", "limit", "rankList", "productList", "monthList", "productIndex", "monthIndex", "currentMonth", "settings", "computed", "getProductName", "getMonthName", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getRankingSetting", "app", "that", "getProductList", "id", "name", "getMonthList", "getRankingList", "type", "month", "params", "uni", "changeRankType", "onProductChange", "onMonthChange", "goToProduct", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqF7wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;QACA;UACAC;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAF;QACA;UACA;UACAC;YACAE;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAL;QACA;UACAC;UACAA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;QACA;MACA;MAEAL;MACA;QACAM;QACAxB;QACAC;QACAwB;MACA;;MAEA;MACA;QACAC;MACA;MAEAT;QACAC;QACAS;QAEA;UACA;YACAT;UACA;YACAA;UACA;UACAA;QACA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAU;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAJ;QACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAAg6C,CAAgB,22CAAG,EAAC,C;;;;;;;;;;;ACAp7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/ranking/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/ranking/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=2289c2cb&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/ranking/list.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=2289c2cb&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<!-- 排行榜类型选择 -->\r\n\t\t\t<view class=\"tab-box\">\r\n\t\t\t\t<view class=\"tab-item\" :class=\"{'active': rankType === 1}\" @tap=\"changeRankType(1)\">成员排行</view>\r\n\t\t\t\t<view class=\"tab-item\" :class=\"{'active': rankType === 2}\" @tap=\"changeRankType(2)\">进货排行</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 筛选条件 -->\r\n\t\t\t<view class=\"filter-box\">\r\n\t\t\t\t<!-- 成员排行筛选 -->\r\n\t\t\t\t<view v-if=\"rankType === 1\" class=\"filter-row\">\r\n\t\t\t\t\t<picker :value=\"productIndex\" :range=\"productList\" range-key=\"name\" @change=\"onProductChange\">\r\n\t\t\t\t\t\t<view class=\"picker-item\">\r\n\t\t\t\t\t\t\t<text>{{getProductName}}</text>\r\n\t\t\t\t\t\t\t<text class=\"icon-arrow\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<picker :value=\"monthIndex\" :range=\"monthList\" range-key=\"name\" @change=\"onMonthChange\">\r\n\t\t\t\t\t\t<view class=\"picker-item\">\r\n\t\t\t\t\t\t\t<text>{{getMonthName}}</text>\r\n\t\t\t\t\t\t\t<text class=\"icon-arrow\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 进货排行筛选 -->\r\n\t\t\t\t<view v-if=\"rankType === 2\" class=\"filter-row\">\r\n\t\t\t\t\t<picker :value=\"monthIndex\" :range=\"monthList\" range-key=\"name\" @change=\"onMonthChange\">\r\n\t\t\t\t\t\t<view class=\"picker-item\">\r\n\t\t\t\t\t\t\t<text>{{getMonthName}}</text>\r\n\t\t\t\t\t\t\t<text class=\"icon-arrow\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 排行榜列表 -->\r\n\t\t\t<view class=\"rank-list\">\r\n\t\t\t\t<block v-for=\"(item, index) in rankList\" :key=\"item.id\">\r\n\t\t\t\t\t<!-- 成员排行显示 -->\r\n\t\t\t\t\t<view class=\"rank-item\" v-if=\"rankType === 1\">\r\n\t\t\t\t\t\t<view class=\"avatar-box\" :class=\"'rank-' + (index + 1)\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t<view class=\"name\">{{item.nickname || '匿名用户'}}</view>\r\n\t\t\t\t\t\t\t<view class=\"level\">{{item.level_name || '-'}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data\">\r\n\t\t\t\t\t\t\t<view class=\"num\">{{item.total_num || 0}}</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"amount\">￥{{item.total_amount || 0}}</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 进货排行显示 -->\r\n\t\t\t\t\t<view class=\"rank-item\" v-if=\"rankType === 2\">\r\n\t\t\t\t\t\t<view class=\"rank-num\" :class=\"'rank-' + (index + 1)\">\r\n\t\t\t\t\t\t\t<text class=\"num\">{{index + 1}}</text>\r\n\t\t\t\t\t\t\t<view class=\"crown\" v-if=\"index < 3\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t<view class=\"name product-link\" @tap=\"goToProduct(item.id)\">{{item.name || '-'}}</view>\r\n\t\t\t\t\t\t\t<view class=\"shop-name\">{{item.shop_name || '-'}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data\">\r\n\t\t\t\t\t\t\t<view class=\"num\">销量：{{item.total_num || 0}}</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"amount\">￥{{item.total_amount || 0}}</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 加载更多 -->\r\n\t\t\t<view class=\"loading-more\" v-if=\"loading\">加载中...</view>\r\n\t\t\t<view class=\"no-more\" v-if=\"nomore\">没有更多数据了</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisload: false,\r\n\t\t\tloading: true,\r\n\t\t\tnomore: false,\r\n\t\t\t\r\n\t\t\trankType: 1, // 1成员排行 2进货排行\r\n\t\t\tpage: 1,\r\n\t\t\tlimit: 10,\r\n\t\t\t\r\n\t\t\trankList: [],\r\n\t\t\tproductList: [],\r\n\t\t\tmonthList: [],\r\n\t\t\tproductIndex: 0,\r\n\t\t\tmonthIndex: 0,\r\n\t\t\tcurrentMonth: '', // 当前选中的月份值\r\n\t\t\t\r\n\t\t\tsettings: null\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tgetProductName() {\r\n\t\t\tif (this.productList.length > 0 && this.productList[this.productIndex]) {\r\n\t\t\t\treturn this.productList[this.productIndex].name;\r\n\t\t\t}\r\n\t\t\treturn '全部商品';\r\n\t\t},\r\n\t\tgetMonthName() {\r\n\t\t\tif (this.monthList.length > 0 && this.monthList[this.monthIndex]) {\r\n\t\t\t\treturn this.monthList[this.monthIndex].name;\r\n\t\t\t}\r\n\t\t\treturn '选择月份';\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.getMonthList(); // 先获取月份列表\r\n\t\tthis.getRankingSetting();\r\n\t\tthis.getProductList();\r\n\t},\r\n\tonPullDownRefresh() {\r\n\t\tthis.page = 1;\r\n\t\tthis.rankList = [];\r\n\t\tthis.getRankingList();\r\n\t},\r\n\tonReachBottom() {\r\n\t\tif(!this.nomore && !this.loading) {\r\n\t\t\tthis.page++;\r\n\t\t\tthis.getRankingList();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取排行榜设置\r\n\t\tgetRankingSetting() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.get('ApiJietikaohe/getRankingSetting', {}, function(res) {\r\n\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\tthat.settings = res.data;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取商品列表\r\n\t\tgetProductList() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.get('ApiJietikaohe/getProductList', {}, function(res) {\r\n\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\t// 添加全部商品选项\r\n\t\t\t\t\tthat.productList = [{\r\n\t\t\t\t\t\tid: 0,\r\n\t\t\t\t\t\tname: '全部商品'\r\n\t\t\t\t\t}].concat(res.data || []);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取月份列表\r\n\t\tgetMonthList() {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.get('ApiJietikaohe/getMonthList', {}, function(res) {\r\n\t\t\t\tif(res.code == 1 && res.data && res.data.length > 0) {\r\n\t\t\t\t\tthat.monthList = res.data;\r\n\t\t\t\t\tthat.monthIndex = 0; // 默认选中第一个月份\r\n\t\t\t\t\tthat.currentMonth = res.data[0].value;\r\n\t\t\t\t\tthat.getRankingList(); // 获取第一个月份的数据\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取排行榜数据\r\n\t\tgetRankingList() {\r\n\t\t\tvar that = this;\r\n\t\t\tif(!that.currentMonth) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.loading = true;\r\n\t\t\tvar params = {\r\n\t\t\t\ttype: that.rankType,\r\n\t\t\t\tpage: that.page,\r\n\t\t\t\tlimit: that.limit,\r\n\t\t\t\tmonth: that.currentMonth\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 成员排行 - 只有当选择了具体商品时才传product_id\r\n\t\t\tif(that.rankType == 1 && that.productList[that.productIndex] && that.productList[that.productIndex].id !== 0) {\r\n\t\t\t\tparams.product_id = that.productList[that.productIndex].id;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiJietikaohe/getRankingList', params, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif(res.code == 1) {\r\n\t\t\t\t\tif(that.page == 1) {\r\n\t\t\t\t\t\tthat.rankList = res.data.list || [];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.rankList = that.rankList.concat(res.data.list || []);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.nomore = that.rankList.length >= (res.data.total || 0);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.rankList = [];\r\n\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换排行类型\r\n\t\tchangeRankType(type) {\r\n\t\t\tif(this.rankType === type) return;\r\n\t\t\tthis.rankType = type;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.rankList = [];\r\n\t\t\tthis.getRankingList();\r\n\t\t},\r\n\t\t\r\n\t\t// 选择商品\r\n\t\tonProductChange(e) {\r\n\t\t\tthis.productIndex = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.rankList = [];\r\n\t\t\tthis.getRankingList();\r\n\t\t},\r\n\t\t\r\n\t\t// 选择月份\r\n\t\tonMonthChange(e) {\r\n\t\t\tthis.monthIndex = e.detail.value;\r\n\t\t\tthis.currentMonth = this.monthList[this.monthIndex].value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.rankList = [];\r\n\t\t\tthis.getRankingList();\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到产品详情页\r\n\t\tgoToProduct(id) {\r\n\t\t\tif(!id) return;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/shopPackage/shop/product?id=' + id\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tpadding: 30rpx 20rpx;\r\n\tbackground: #F8F9FD;\r\n\tmin-height: 100vh;\r\n\t\r\n\t.tab-box {\r\n\t\tdisplay: flex;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 16rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\theight: 200%;\r\n\t\t\tbackground: linear-gradient(180deg, rgba(250, 81, 81, 0.08) 0%, rgba(250, 81, 81, 0) 100%);\r\n\t\t\topacity: 0.5;\r\n\t\t\tpointer-events: none;\r\n\t\t}\r\n\t\t\r\n\t\t.tab-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 24rpx 0;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t&.active {\r\n\t\t\t\tcolor: #FA5151;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\ttransform: scale(1.02);\r\n\t\t\t\t\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.filter-box {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 24rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.filter-row {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: 24rpx;\r\n\t\t\t\r\n\t\t\t.picker-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 76rpx;\r\n\t\t\t\tbackground: #F8F9FD;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tpadding: 0 24rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\tbackground: #F0F2F5;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.icon-arrow {\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\twidth: 0;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-left: 10rpx solid transparent;\r\n\t\t\t\t\tborder-right: 10rpx solid transparent;\r\n\t\t\t\t\tborder-top: 10rpx solid #999;\r\n\t\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\t\ttransition: transform 0.3s ease;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.rank-list {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 8rpx 24rpx;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.rank-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 24rpx 16rpx;\r\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.04);\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: rgba(250, 81, 81, 0.02);\r\n\t\t\t\ttransform: translateX(4rpx);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.avatar-box {\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tpadding: 4rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&.rank-1 {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\r\n\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.rank-2 {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);\r\n\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.rank-3 {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);\r\n\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.avatar {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tpadding: 0 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.product-link {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tpadding-bottom: 4rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 2rpx;\r\n\t\t\t\t\t\t\tbackground: currentColor;\r\n\t\t\t\t\t\t\ttransform: scaleX(0);\r\n\t\t\t\t\t\t\ttransition: transform 0.3s ease;\r\n\t\t\t\t\t\t\ttransform-origin: right;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:hover:after {\r\n\t\t\t\t\t\t\ttransform: scaleX(1);\r\n\t\t\t\t\t\t\ttransform-origin: left;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.rank-1 {\r\n\t\t\t\t\t\tcolor: #FFD700;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.rank-2 {\r\n\t\t\t\t\t\tcolor: #C0C0C0;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.rank-3 {\r\n\t\t\t\t\t\tcolor: #CD7F32;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.level,\r\n\t\t\t\t.shop-name {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\twidth: 8rpx;\r\n\t\t\t\t\t\theight: 8rpx;\r\n\t\t\t\t\t\tbackground: #FA5151;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\topacity: 0.5;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.data {\r\n\t\t\t\ttext-align: right;\r\n\t\t\t\tpadding-left: 24rpx;\r\n\t\t\t\t\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);\r\n\t\t\t\t\t-webkit-background-clip: text;\r\n\t\t\t\t\tcolor: transparent;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.loading-more,\r\n\t.no-more {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tpadding: 32rpx 0;\r\n\t\tletter-spacing: 2rpx;\r\n\t\t\r\n\t\t&::before,\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tdisplay: inline-block;\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground: linear-gradient(90deg, transparent, #ddd);\r\n\t\t\tmargin: 0 20rpx;\r\n\t\t\tvertical-align: middle;\r\n\t\t}\r\n\t\t\r\n\t\t&::after {\r\n\t\t\tbackground: linear-gradient(90deg, #ddd, transparent);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.rank-num {\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\tline-height: 64rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-right: 24rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: #F8F9FD;\r\n\t\tcolor: #999;\r\n\t\tposition: relative;\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t.num {\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 2;\r\n\t\t}\r\n\t\t\r\n\t\t.crown {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: -16rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 32rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tbackground: url('data:image/svg+xml;utf8,<svg t=\"1709799047439\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4286\"><path d=\"M910.8 405.8L814 492.9 755.3 333c-2.4-6.5-7.6-11.6-14.1-13.7-6.5-2.2-13.7-1.1-19.3 2.9L548.1 450 430.1 161.5c-3.2-7.9-10.9-13.1-19.5-13.1s-16.2 5.2-19.5 13.1L273.3 450l-174-127.9c-5.7-4.1-12.8-5.1-19.3-2.9-6.5 2.2-11.7 7.2-14.1 13.7L7.2 492.9l-96.8-87.1c-5.4-4.9-13-6.3-19.8-3.8-6.7 2.6-11.6 8.5-12.8 15.6-19.2 113.6 6.1 230.4 69.9 321.2 63.8 90.8 159.4 153.8 268.9 177.2 20.8 4.4 42 7.5 63.1 9.2 17.7 1.4 35.5 2.1 53.2 2.1 17.8 0 35.6-0.7 53.3-2.1 21.2-1.7 42.3-4.8 63.1-9.2 109.5-23.4 205.1-86.4 268.9-177.2 63.8-90.8 89.1-207.6 69.9-321.2-1.2-7.1-6.1-13.1-12.8-15.6-6.8-2.5-14.4-1.1-19.8 3.8z\" fill=\"%23FFD700\" p-id=\"4287\"></path></svg>') no-repeat center/contain;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\t\t\r\n\t\t&.rank-1 {\r\n\t\t\tbackground: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\r\n\t\t\tcolor: #fff;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);\r\n\t\t\ttransform: scale(1.1);\r\n\t\t\t\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -4rpx;\r\n\t\t\t\tleft: -4rpx;\r\n\t\t\t\tright: -4rpx;\r\n\t\t\t\tbottom: -4rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.2) 100%);\r\n\t\t\t\tz-index: 0;\r\n\t\t\t\tanimation: pulse 1.5s ease-in-out infinite;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.rank-2 {\r\n\t\t\tbackground: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);\r\n\t\t\tcolor: #fff;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);\r\n\t\t\ttransform: scale(1.05);\r\n\t\t}\r\n\t\t\r\n\t\t&.rank-3 {\r\n\t\t\tbackground: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);\r\n\t\t\tcolor: #fff;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);\r\n\t\t\ttransform: scale(1.02);\r\n\t\t}\r\n\t}\r\n\t\r\n\t@keyframes pulse {\r\n\t\t0% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: scale(1.5);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info {\r\n\t\tflex: 1;\r\n\t\tpadding: 0 16rpx;\r\n\t\t\r\n\t\t.name {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\t\r\n\t\t\t&.product-link {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding-bottom: 4rpx;\r\n\t\t\t\t\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 2rpx;\r\n\t\t\t\t\tbackground: currentColor;\r\n\t\t\t\t\ttransform: scaleX(0);\r\n\t\t\t\t\ttransition: transform 0.3s ease;\r\n\t\t\t\t\ttransform-origin: right;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:hover:after {\r\n\t\t\t\t\ttransform: scaleX(1);\r\n\t\t\t\t\ttransform-origin: left;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.rank-1 {\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.rank-2 {\r\n\t\t\t\tcolor: #C0C0C0;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.rank-3 {\r\n\t\t\t\tcolor: #CD7F32;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115104556\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}