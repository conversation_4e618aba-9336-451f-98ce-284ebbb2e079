{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?3f75", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?b50b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?04c8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?b700", "uni-app:///pagesExa/tuanzhangadmin/index/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?72b6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/index/index.vue?576f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "set", "uinfo", "count0", "count1", "count2", "count3", "count4", "seckill<PERSON>ount", "collageCount", "kanjia<PERSON>ount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreshopCount", "maidanCount", "productCount", "yuyueorderCount", "cycleCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formlogCount", "auth_data", "showshoporder", "showbusinessqr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showcollageorder", "showkanjiaorder", "showseckillorder", "showscoreshoporder", "showluckycollageorder", "showtuangouorder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showmaidanlog", "showformlog", "showworkorder", "showworkadd", "workorderCount", "showrecharge", "wxtmplset", "searchmember", "showCycleorder", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "switchchange", "console", "field", "value", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON>", "needResult", "scanType", "success", "uni", "addsubnum"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqK9wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAE;MACAC;MACA;MACA;MACAF;QACAG;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAL;QAAA;MACA;QACA;QACAM;UAAA;UACAA;YACAC;YAAA;YACAC;YAAA;YACAC;cACA;cACA;cACAT;cACA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;QACAU;UACAD;YACAP;YACA;YACA;YACAF;UACA;QACA;MACA;IACA;IACAW;MACA;MACAZ;MACAG;MACAH;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClUA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0f06cd04&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/index/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0f06cd04&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.auth_data.member ? _vm.t(\"会员\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t<view class=\"banner\" :style=\"{background:'url('+pre_url+'/static/img/topbg.png)',backgroundSize:'100%'}\">\r\n\t\t\t<image :src=\"set.logo\" background-size=\"cover\" @tap=\"goto\" :data-url=\"uinfo.bid==0 ? '/pages/index/index' : '/pagesExt/business/index?id='+uinfo.bid\"/>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t <text class=\"nickname\">{{set.name}}</text>\r\n\t\t\t\t <text>{{uinfo.un}}</text>\r\n\t\t\t\t <view  class=\"recharge\" v-if=\"showrecharge\" @tap=\"goto\" data-url=\"recharge\">充值</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"set\" @tap=\"saoyisao\">\r\n\t\t\t\t<image src=\"/static/img/ico-scan.png\"></image>\r\n\t\t\t</view>\r\n\t</view>\r\n\t<view class=\"contentdata\">\r\n\t\t<!-- <block v-if=\"auth_data.order\">\r\n\t\t<view class=\"order\"  v-if=\"showshoporder\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<text class=\"f1\">商城订单</text>\r\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"../order/shoporder\"><text>查看全部订单</text><image src=\"/static/img/arrowright.png\"></image></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=0\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/order1.png'\"></image>\r\n\t\t\t\t\t\t<text class=\"t3\">待付款({{count0}})</text>\r\n\t\t\t\t </view>\r\n\t\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=1\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/order2.png'\"></image>\r\n\t\t\t\t\t\t<text class=\"t3\">待发货({{count1}})</text>\r\n\t\t\t\t </view>\r\n\t\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"../order/shoporder?st=2\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/order3.png'\"></image>\r\n\t\t\t\t\t\t<text class=\"t3\">待收货({{count2}})</text>\r\n\t\t\t\t </view>\r\n\t\t\t\t <view class=\"item\" @tap=\"goto\" data-url=\"../order/shopRefundOrder\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/order4.png'\"></image>\r\n\t\t\t\t\t\t<text class=\"t3\">退款/售后({{count4}})</text>\r\n\t\t\t\t </view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t</block> -->\r\n\t\t\r\n\t\t\r\n\t\t<block v-if=\"auth_data.product\">\r\n\t\t<view class=\"list\">\r\n\t\t\t\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"../index/mytuanlist\">\r\n\t\t\t\t<view class=\"f2\">我的团</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"/activity/commission/myteam\">\r\n\t\t\t\t<view class=\"f2\">团队列表</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/activity/commission/downorder\">\r\n\t\t\t\t<view class=\"f2\">我的订单</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view  class=\"item\" @tap=\"goto\" data-url=\"/activity/commission/poster\">\r\n\t\t\t\t<view class=\"f2\">推广码</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"../index/mtuanlist\">\r\n\t\t\t\t<view class=\"f2\">推荐团购</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t</block>\r\n\r\n\t\t<block v-if=\"auth_data.product\">\r\n\t\t<view class=\"list\">\r\n\t\t<!-- \t<view class=\"item\" @tap=\"goto\" data-url=\"../product/index\">\r\n\t\t\t\t<view class=\"f2\">商品库</view>\r\n\t\t\t\t<text class=\"f3\">{{productCount}}</text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <view class=\"item\" @tap=\"goto\" data-url=\"../product/edit\">\r\n\t\t\t\t<view class=\"f2\">添加商品</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t</block>\r\n\t\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\r\n\t\t\r\n\t\t<view class=\"list\">\r\n\t\t\t<view v-if=\"showbusinessqr\" class=\"item\" @tap=\"goto\" data-url=\"businessqr\">\r\n\t\t\t\t<view class=\"f2\">推广码</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"showbusinessqr\" class=\"item\" @tap=\"goto\" data-url=\"businessqr\">\r\n\t\t\t\t<view class=\"f2\">推广码</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"../workorder/add\" v-if=\"showworkadd\" >\r\n\t\t\t\t<view class=\"f2\">工单提交</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setinfo\" v-if=\"uinfo.bid>0\">\r\n\t\t\t\t<view class=\"f2\">店铺设置</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"setpwd\">\r\n\t\t\t\t<view class=\"f2\">修改密码</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"../index/login\">\r\n\t\t\t\t<view class=\"f2\">切换账号</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pages/my/usercenter\">\r\n\t\t\t\t<view class=\"f2\">返回个人中心</view>\r\n\t\t\t\t<text class=\"f3\"></text>\r\n\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f4\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view class=\"tabbar\">\r\n\t\t<view class=\"tabbar-bot\"></view>\r\n\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\r\n\t\t\t<view @tap=\"goto\" data-url=\"../member/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.member\">\r\n\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/member.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tabbar-text\">{{t('会员')}}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view @tap=\"goto\" data-url=\"../finance/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\" v-if=\"auth_data.finance\">\r\n\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/finance.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tabbar-text\">财务</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap=\"goto\" data-url=\"../index/index\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n\t\t\t\t<view class=\"tabbar-image-box\">\r\n\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/admin/my2.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tabbar-text active\">我的</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t</block>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tset:{},\r\n\t\t\tuinfo:{},\r\n      count0: 0,\r\n      count1: 0,\r\n      count2: 0,\r\n      count3: 0,\r\n      count4: 0,\r\n      seckillCount: 0,\r\n      collageCount: 0,\r\n      kanjiaCount: 0,\r\n\t\t\ttuangouCount:0,\r\n      scoreshopCount: 0,\r\n      maidanCount: 0,\r\n      productCount: 0,\r\n\t\t\tyuyueorderCount: 0,\r\n\t\t\tcycleCount: 0,\r\n      hexiaoCount: 0,\r\n\t\t\tformlogCount:0,\r\n      auth_data: {},\r\n\t\t\tshowshoporder:false,\r\n\t\t\tshowbusinessqr:false,\r\n\t\t\tshowyuyueorder:false,\r\n\t\t\tshowcollageorder:false,\r\n\t\t\tshowkanjiaorder:false,\r\n\t\t\tshowseckillorder:false,\r\n\t\t\tshowscoreshoporder:false,\r\n\t\t\tshowluckycollageorder:false,\r\n\t\t\tshowtuangouorder:false,\r\n\t\t\tshowyuekeorder:false,\r\n\t\t\tshowmaidanlog:false,\r\n\t\t\tshowformlog:false,\r\n\t\t\tshowworkorder:false,\r\n\t\t\tshowworkadd:false,\r\n\t\t\tworkorderCount:0,\r\n\t\t\tshowrecharge:false,\r\n\t\t\twxtmplset:{},\r\n\t\t\tsearchmember:false,\r\n\t\t\tshowCycleorder:false\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApituanzhangAdminIndex/index', {}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.set = res.set;\r\n\t\t\t\tthat.wxtmplset = res.wxtmplset;\r\n\t\t\t\tthat.uinfo = res.uinfo;\r\n\t\t\t\tthat.count0 = res.count0;\r\n\t\t\t\tthat.count1 = res.count1;\r\n\t\t\t\tthat.count2 = res.count2;\r\n\t\t\t\tthat.count3 = res.count3;\r\n\t\t\t\tthat.count4 = res.count4;\r\n\t\t\t\tthat.seckillCount = res.seckillCount;\r\n\t\t\t\tthat.collageCount = res.collageCount;\r\n\t\t\t\tthat.cycleCount = res.cycleCount;\r\n\t\t\t\tthat.luckycollageCount = res.luckycollageCount;\r\n\t\t\t\tthat.kanjiaCount = res.kanjiaCount;\r\n\t\t\t\tthat.tuangouCount = res.tuangouCount;\r\n\t\t\t\tthat.scoreshopCount = res.scoreshopCount;\r\n\t\t\t\tthat.yuyueCount = res.yuyueCount;\r\n\t\t\t\tthat.maidanCount = res.maidanCount;\r\n\t\t\t\tthat.productCount = res.productCount;\r\n\t\t\t\tthat.hexiaoCount = res.hexiaoCount;\r\n\t\t\t\tthat.formlogCount = res.formlogCount;\r\n\t\t\t\tthat.auth_data = res.auth_data;\r\n\t\t\t\tthat.showbusinessqr = res.showbusinessqr;\r\n\t\t\t\tthat.yuyueorderCount = res.yuyueorderCount;\r\n\t\t\t\tthat.workordercount = res.workordercount;\r\n\t\t\t\tthat.showshoporder = res.showshoporder;\r\n\t\t\t\tthat.showcollageorder = res.showcollageorder;\r\n\t\t\t\tthat.showCycleorder = res.showCycleorder;\r\n\t\t\t\tthat.showkanjiaorder = res.showkanjiaorder;\r\n\t\t\t\tthat.showseckillorder = res.showseckillorder;\r\n\t\t\t\tthat.showtuangouorder = res.showtuangouorder;\r\n\t\t\t\tthat.showscoreshoporder = res.showscoreshoporder;\r\n\t\t\t\tthat.showluckycollageorder = res.showluckycollageorder;\r\n\t\t\t\tthat.showmaidanlog = res.showmaidanlog;\r\n\t\t\t\tthat.showformlog = res.showformlog;\r\n\t\t\t\tthat.showworkorder = res.showworkorder;\r\n\t\t\t\tthat.showworkadd = res.showworkadd;\r\n\t\t\t\tthat.showyuyueorder = res.showyuyueorder;\r\n\t\t\t\tthat.showrecharge = res.showrecharge;\r\n\t\t\t\tthat.searchmember = res.searchmember;\r\n\t\t\t\tthat.showyuekeorder = res.showyuekeorder;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    switchchange: function (e) {\r\n      console.log(e);\r\n      var field = e.currentTarget.dataset.type;\r\n      var value = e.detail.value ? 1 : 0;\r\n      app.post('ApituanzhangAdminIndex/setusertmpl', {\r\n        field: field,\r\n        value: value\r\n      }, function (data) {});\r\n    },\r\n    saoyisao: function (d) {\r\n      var that = this;\r\n\t\t\tif(app.globalData.platform == 'h5'){\r\n\t\t\t\tapp.alert('请使用微信扫一扫功能扫码核销');return;\r\n\t\t\t}else if(app.globalData.platform == 'mp'){\r\n\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\r\n\t\t\t\t\tjweixin.scanQRCode({\r\n\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\r\n\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\r\n\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\r\n\t\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\t\tapp.goto('/admin/hexiao/hexiao?'+params);\r\n\t\t\t\t\t\t\t//if(content.length == 18 && (/^\\d+$/.test(content))){ //是十八位数字 付款码\r\n\t\t\t\t\t\t\t//\tlocation.href = \"{:url('shoukuan')}/aid/{$aid}/auth_code/\"+content\r\n\t\t\t\t\t\t\t//}else{\r\n\t\t\t\t\t\t\t//\tlocation.href = content;\r\n\t\t\t\t\t\t\t//}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tuni.scanCode({\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tvar content = res.result;\r\n\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\tapp.goto('/admin/hexiao/hexiao?'+params);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n    },\r\n\t\taddsubnum:function(e){\t\r\n      var that = this;\r\n\t\t\tthat.tmplids = [e.currentTarget.dataset.tmplid];\r\n\t\t\tconsole.log(that.tmplids);\r\n\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t});\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n@import \"../common.css\";\r\n.banner{ display:flex;width:100%;height:352rpx;padding:40rpx 32rpx;color:#fff;position:relative}\r\n.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}\r\n.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}\r\n.banner .info .nickname{font-size:32rpx;font-weight:bold;padding-bottom:12rpx}\r\n.banner .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}\r\n.banner .set image{width:50rpx;height:50rpx;border-radius:0}\r\n\r\n.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-160rpx;position:relative;margin-bottom:20rpx}\r\n.money{ display:flex;width:100%;align-items:center;padding:10rpx 20rpx;background:#fff;}\r\n.money .f1{flex:auto}\r\n.money .f1 .t2{color:#ff3300}\r\n.money .f2{ background:#fff;color:#ff3300;border:1px solid #ff3300;height:50rpx;line-height:50rpx;padding:0 14rpx;font-size: 28rpx;}\r\n\r\n.score{ display:flex;width:100%;align-items:center;padding:10rpx 20rpx;background:#fff;border-top:1px dotted #eee}\r\n.score .f1 .t2{color:#ff3300}\r\n\r\n.order{width:100%;background:#fff;padding:0 20rpx;margin-top:20rpx;border-radius:16rpx}\r\n.order .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}\r\n.order .head .f1{flex:auto;color:#333}\r\n.order .head .f2{ display:flex;align-items:center;color:#FC5648;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\r\n.order .head .f2 image{ width:30rpx;height:30rpx;}\r\n.order .head .t3{ width: 40rpx; height: 40rpx;}\r\n.order .content{ display:flex;width:100%;padding:10rpx 0;align-items:center;font-size:24rpx}\r\n.order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\r\n.order .content .item image{ width:50rpx;height:50rpx}\r\n.order .content .item .t3{ padding-top:3px;color:#333}\r\n.order .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}\r\n\r\n.agent{width:100%;background:#fff;padding:0px 20rpx;margin-top:20rpx}\r\n.agent .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:1px solid #eee}\r\n.agent .head .f1{flex:auto;}\r\n.agent .head .f2{ display:flex;align-items:center;color:#999;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}\r\n.agent .head .f2 image{ width:30rpx;height:30rpx;}\r\n.agent .head .t3{ width: 40rpx; height: 40rpx;}\r\n.agent .content{ display:flex;width:100%;padding:10rpx 0;align-items:center;font-size:24rpx}\r\n.agent .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\r\n.agent .content .item image{ width:50rpx;height:50rpx}\r\n.agent .content .item .t3{ padding-top:3px}\r\n.agent .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;}\r\n\r\n.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}\r\n.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}\r\n.list .item:last-child{border-bottom:0;}\r\n.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}\r\n.list .f1 image{ width:40rpx;height:40rpx;}\r\n.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}\r\n.list .f2{color:#222}\r\n.list .f3{ color: #FC5648;text-align:right;flex:1;}\r\n.list .f4{ width: 24rpx; height: 24rpx;}\r\n\r\n.recharge{ background: #fff; width: 100rpx;color: #FB6534; text-align: center; font-size: 20rpx; padding: 5rpx; border-radius: 10rpx; margin-top: 10rpx;}\r\n\r\nswitch{transform:scale(.7);}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115063736\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}