{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?f03c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?b203", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?5fa4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?c629", "uni-app:///pagesExt/tuozhanyuan/shangjidetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?1fa7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjidetail.vue?9fb7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "member", "loading", "firstPic", "ordershow", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "mid", "uni", "title", "remark", "showFollowUpDialog", "remarkConfirm", "<PERSON><PERSON><PERSON>", "setTimeout", "followUpConfirm", "businessId", "follow<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8DtxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAG;UACAC;QACA;QAEA;QACAJ;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAN;QAAAO;QAAAH;MAAA;QACAJ;QACAQ;UACAT;QACA;MACA;IACA;IACAU;MACA;MACA;MACAT;QAAAU;QAAAC;MAAA;QACAX;QACAQ;UACAT;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/shangjidetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/shangjidetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shangjidetail.vue?vue&type=template&id=743f1958&\"\nvar renderjs\nimport script from \"./shangjidetail.vue?vue&type=script&lang=js&\"\nexport * from \"./shangjidetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shangjidetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/shangjidetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjidetail.vue?vue&type=template&id=743f1958&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjidetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjidetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n    <block v-if=\"isload\">\r\n        <view class=\"orderinfo\">\r\n            <view class=\"item\">\r\n                <text class=\"t1\">ID</text>\r\n                <text class=\"t2\">{{member.id}}</text>\r\n            </view>\r\n           \r\n            <view class=\"item\">\r\n                <text class=\"t1\">昵称</text>\r\n                <text class=\"t2\">{{member.company_name}}</text>\r\n            </view>\r\n            <view class=\"item\">\r\n                <text class=\"t1\">地区</text>\r\n                <text class=\"t2\">{{member.address}}</text>\r\n            </view>\r\n            <view class=\"item\">\r\n                <text class=\"t1\">加入时间</text>\r\n                <text class=\"t2\">{{member.created_at}}</text>\r\n            </view>\r\n            <view class=\"item\">\r\n                <text class=\"t1\">姓名</text>\r\n                <text class=\"t2\">{{member.contact_name}}</text>\r\n            </view>\r\n            <view class=\"item\">\r\n                <text class=\"t1\">电话</text>\r\n                <text class=\"t2\">{{member.phone}}</text>\r\n            </view>\r\n            \r\n            <view class=\"item\" v-if=\"member.remark\">\r\n                <text class=\"t1\">备注</text>\r\n                <text class=\"t2\">{{member.remark}}</text>\r\n            </view>\r\n            <view class=\"item\" v-if=\"ordershow\" style=\"justify-content: space-between;\">\r\n                <text class=\"t1\" style=\"color: #007aff;\">商城订单</text>\r\n                <view class=\"flex\" @tap=\"goto\" :data-url=\"'/admin/order/shoporder?mid='+member.id\">{{member.ordercount}} <text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal; margin-top: 2rpx;\"></text></view>\r\n            </view>\t\r\n        </view>\r\n        <view style=\"width:100%;height:120rpx\"></view>\r\n      <view class=\"bottom\">\r\n          <view class=\"btn\" @tap=\"remark\" :data-id=\"member.id\">备注</view>\r\n          <view class=\"btn\" @tap=\"showFollowUpDialog\">跟进</view>\r\n          <view class=\"btn\" @tap=\"goto\" :data-url=\"'shagjigenjinjilu?id=' + member.id\">跟进记录</view>\r\n          <view class=\"btn\" @tap=\"goto\" :data-url=\"'shangjixiugai?id=' + member.id\">商机修改</view>\r\n      </view>\r\n\r\n\r\n        <uni-popup id=\"remarkDialog\" ref=\"remarkDialog\" type=\"dialog\">\r\n            <uni-popup-dialog mode=\"input\" title=\"设置备注\" value=\"\" placeholder=\"请输入备注\" @confirm=\"remarkConfirm\"></uni-popup-dialog>\r\n        </uni-popup>\r\n\r\n        <uni-popup id=\"followUpDialog\" ref=\"followUpDialog\" type=\"dialog\">\r\n            <uni-popup-dialog mode=\"input\" title=\"添加跟进记录\" value=\"\" placeholder=\"请输入跟进内容\" @confirm=\"followUpConfirm\"></uni-popup-dialog>\r\n        </uni-popup>\r\n    </block>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n    <loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            isload: false,\r\n            member: \"\",\r\n            loading: false,\r\n            firstPic: '',\r\n            ordershow: false,\r\n        };\r\n    },\r\n    onLoad: function (opt) {\r\n        this.opt = app.getopts(opt);\r\n        this.getdata();\r\n    },\r\n    onPullDownRefresh: function () {\r\n        this.getdata();\r\n    },\r\n    methods: {\r\n        getdata: function () {\r\n            var that = this;\r\n            that.loading = true;\r\n            app.post('ApiTuozhancrm/shangjidetail', { mid: that.opt.mid }, function (res) {\r\n                that.loading = false;\r\n                that.member = res.member;\r\n                that.ordershow = res.ordershow;\r\n                uni.setNavigationBarTitle({\r\n                    title: that.t('会员') + '信息'\r\n                });\r\n\r\n                const picsArray = res.member.pics.split(',');\r\n                that.firstPic = picsArray[0]; // 赋值给 firstPic\r\n                that.loaded();\r\n            });\r\n        },\r\n        remark: function (e) {\r\n            this.$refs.remarkDialog.open();\r\n        },\r\n        showFollowUpDialog: function () {\r\n            this.$refs.followUpDialog.open();\r\n        },\r\n        remarkConfirm: function (done, value) {\r\n            this.$refs.remarkDialog.close();\r\n            var that = this;\r\n            app.post('ApiTuozhancrm/remark', { remarkmid: that.opt.mid, remark: value }, function (data) {\r\n                app.success(data.msg);\r\n                setTimeout(function () {\r\n                    that.getdata();\r\n                }, 1000);\r\n            });\r\n        },\r\n        followUpConfirm: function (done, value) {\r\n            this.$refs.followUpDialog.close();\r\n            var that = this;\r\n            app.post('ApiTuozhancrm/addFollowUp', { businessId: that.opt.mid, followUpContent: value }, function (data) {\r\n                app.success(data.msg);\r\n                setTimeout(function () {\r\n                    that.getdata();\r\n                }, 1000);\r\n            });\r\n        },\r\n    }\r\n};\r\n</script>\r\n\r\n<style>\r\n.address { display: flex; align-items: center; width: 100%; padding: 20rpx 3%; background: #FFF; margin-bottom: 20rpx; }\r\n.address .img { width: 60rpx; }\r\n.address image { width: 50rpx; height: 50rpx; }\r\n.address .info { flex: 1; display: flex; flex-direction: column; }\r\n.address .info .t1 { font-weight: bold; }\r\n\r\n.product { width: 100%; padding: 14rpx 3%; background: #FFF; }\r\n.product .content { display: flex; position: relative; width: 100%; padding: 16rpx 0px; border-bottom: 1px #e5e5e5 dashed; position: relative; }\r\n.product .content:last-child { border-bottom: 0; }\r\n.product .content image { width: 140rpx; height: 140rpx; }\r\n.product .content .detail { display: flex; flex-direction: column; margin-left: 14rpx; flex: 1; }\r\n.product .content .detail .t1 { height: 60rpx; line-height: 30rpx; color: #000; }\r\n.product .content .detail .t2 { height: 46rpx; line-height: 46rpx; color: #999; overflow: hidden; font-size: 26rpx; }\r\n.product .content .detail .t3 { display: flex; height: 30rpx; line-height: 30rpx; color: #ff4246; }\r\n.product .content .detail .x1 { flex: 1; }\r\n.product .content .detail .x2 { width: 100rpx; font-size: 32rpx; text-align: right; margin-right: 8rpx; }\r\n.product .content .comment { position: absolute; top: 64rpx; right: 10rpx; border: 1px #ffc702 solid; border-radius: 10rpx; background: #fff; color: #ffc702; padding: 0 10rpx; height: 46rpx; line-height: 46rpx; }\r\n\r\n.orderinfo { width: 94%; margin: 20rpx 3%; border-radius: 16rpx; padding: 14rpx 3%; background: #FFF; }\r\n.orderinfo .item { display: flex; width: 100%; padding: 20rpx 0; border-bottom: 1px dashed #ededed; }\r\n.orderinfo .item:last-child { border-bottom: 0; }\r\n.orderinfo .item .t1 { width: 200rpx; }\r\n.orderinfo .item .t2 { flex: 1; text-align: right; }\r\n.orderinfo .item .red { color: red; }\r\n\r\n.bottom { width: 100%; padding: 16rpx 20rpx; background: #fff; position: fixed; bottom: 0px; left: 0px; display: flex; justify-content: flex-end; align-items: center; }\r\n.bottom .btn { border-radius: 10rpx; padding: 10rpx 16rpx; margin-left: 10px; border: 1px #999 solid; }\r\n\r\n.uni-popup-dialog { width: 300px; border-radius: 5px; background-color: #fff; }\r\n.uni-dialog-title { display: flex; flex-direction: row; justify-content: center; padding-top: 15px; padding-bottom: 5px; }\r\n.uni-dialog-title-text { font-size: 16px; font-weight: 500; }\r\n.uni-dialog-content { display: flex; flex-direction: row; justify-content: center; align-items: center; padding: 5px 15px 15px 15px; width: 100%; }\r\n.uni-dialog-content-text { font-size: 14px; color: #6e6e6e; }\r\n.uni-dialog-button-group { display: flex; flex-direction: row; border-top-color: #f5f5f5; border-top-style: solid; border-top-width: 1px; }\r\n.uni-dialog-button { display: flex; flex: 1; flex-direction: row; justify-content: center; align-items: center; height: 45px; }\r\n.uni-border-left { border-left-color: #f0f0f0; border-left-style: solid; border-left-width: 1px; }\r\n.uni-dialog-button-text { font-size: 14px; }\r\n.uni-button-color { color: #007aff; }\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjidetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjidetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103414\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}