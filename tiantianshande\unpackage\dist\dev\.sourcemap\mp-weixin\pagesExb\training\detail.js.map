{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?4a00", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?073e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?3173", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?64e0", "uni-app:///pagesExb/training/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?2de9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/detail.vue?cb2c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "detail", "datalist", "pagenum", "id", "pagecontent", "title", "sharepic", "nodata", "nomore", "iszan", "plcount", "buyInfo", "onLoad", "console", "onPullDownRefresh", "onShareAppMessage", "desc", "pic", "callback", "that", "onShareTimeline", "imageUrl", "query", "onReachBottom", "methods", "sharecallback", "app", "getdata", "uni", "<PERSON><PERSON><PERSON><PERSON>", "zan", "pzan", "buyCourse", "toCourse"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuH/wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;IACA;EACA;EACAC;IACA;IACAD;IACA;EACA;EACAE;IACA;IACA;MAAAV;MAAAW;MAAAC;MAAAC;QACAC;MACA;IAAA;EACA;EACAC;IACA;IACA;MAAAf;MAAAW;MAAAC;MACAC;QACAC;MACA;IAAA;IACA;IACA;MACAd;MACAgB;MACAC;IACA;EACA;EACAC;IACA;MACA;MACAV;MACA;MACA;IACA;EACA;EACAW;IACAC;MACAC;QACA;QACAb;MACA;IACA;IACAc;MACA;MACA;;MAEA;MACAd;MAEAM;MACAO;QAAAvB;MAAA;QACAgB;;QAEA;QACAN;QAEA;UACAM;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACAA;YACA;YACAN;UACA;UAEAe;YACAvB;UACA;;UAEA;UACAQ;QACA;UACA;UACAA;UACAa;QACA;QACAP;QACAA;QACAA;QACAA;UAAAd;UAAAW;UAAAC;QAAA;MACA;IACA;IACAY;MACA;MACA;;MAEA;MACA;QACA;QACAhB;QACA;MACA;;MAEA;MACAA;MAEAM;MACAA;MACAA;MACAO;QAAAxB;QAAAC;MAAA;QACAgB;;QAEA;QACAN;QAEA;QACA;UACA;YACAM;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAW;MACA;MACA;;MAEA;MACAjB;MAEAa;QAAAvB;MAAA;QACA;QACAU;QAEA;UACA;UACA;QACA;UACA;QACA;QACAM;QACAA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;;MAEA;MACAlB;MAEAa;QAAAvB;MAAA;QACA;QACAU;QAEA;UACA;UACA;QACA;UACA;QACA;QAEAZ;QACAA;QACAkB;MACA;IACA;IACAa;MACA;MACA;QACAN;QACA;MACA;;MAEA;MACAb;;MAEA;MACAa;IACA;IACAO;MACA;MACA;MACA;QACAP;QACA;MACA;;MAEA;MACAb;;MAEA;MACAa;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxVA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/training/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/training/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=6895ad60&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/training/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=6895ad60&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.detail.kcname ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.detail.kcname ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload &&\n    !(_vm.detail.has_permission || _vm.detail.need_buy == 0) &&\n    _vm.buyInfo\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    !(_vm.detail.has_permission || _vm.detail.need_buy == 0) &&\n    _vm.buyInfo\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    _vm.isload &&\n    !(_vm.detail.has_permission || _vm.detail.need_buy == 0) &&\n    _vm.buyInfo\n      ? _vm.t(\"color1\")\n      : null\n  var l0 =\n    _vm.isload &&\n    _vm.detail.canpl == 1 &&\n    (_vm.detail.has_permission || _vm.detail.need_buy == 0)\n      ? _vm.__map(_vm.datalist, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = item.replylist.length\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<text class=\"title\" v-if=\"detail.showname==1\">{{detail.name}}</text>\r\n\t\t\t\t<view class=\"training-meta\" v-if=\"detail.subname\">\r\n\t\t\t\t\t<text class=\"subtitle\">{{detail.subname}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"artinfo\" v-if=\"detail.showsendtime==1 || detail.showauthor==1 || detail.showreadcount==1\">\r\n\t\t\t\t\t<text class=\"t1\" v-if=\"detail.showsendtime==1\">{{detail.createtime}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.showauthor==1\">{{detail.author}}</text>\r\n\t\t\t\t\t<text class=\"t3\" v-if=\"detail.showreadcount==1\">阅读：{{detail.readcount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 课程信息显示 -->\r\n\t\t\t\t<view class=\"course-binding\" v-if=\"detail.kcname\">\r\n\t\t\t\t\t<view class=\"course-info-card\" :style=\"{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t\t<view class=\"course-left\">\r\n\t\t\t\t\t\t\t<image class=\"course-pic\" :src=\"detail.kcpic || '/static/img/default-course.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"course-right\">\r\n\t\t\t\t\t\t\t<view class=\"course-badge\">\r\n\t\t\t\t\t\t\t\t<!-- <image class=\"course-icon\" src=\"/static/img/course-icon.png\"></image> -->\r\n\t\t\t\t\t\t\t\t<text class=\"course-label\">关联课程</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"course-title\">{{detail.kcname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"course-meta\">\r\n\t\t\t\t\t\t\t\t<text class=\"course-price\" v-if=\"detail.kcprice > 0\">¥{{detail.kcprice}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"course-price free\" v-else>免费</text>\r\n\t\t\t\t\t\t\t\t<text class=\"course-tip\" @tap=\"toCourse\" :data-id=\"detail.kcid\">查看课程 ></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 权限检查和内容显示 -->\r\n\t\t\t\t<block v-if=\"detail.has_permission || detail.need_buy == 0\">\r\n\t\t\t\t\t<view style=\"padding:8rpx 0\">\r\n\t\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<!-- 无权限时显示购买提示 -->\r\n\t\t\t\t\t<view class=\"no-permission-container\">\r\n\t\t\t\t\t\t<view class=\"preview-content\">\r\n\t\t\t\t\t\t\t<view class=\"preview-text\">{{detail.preview_content || '该训练营需要购买相关课程后才能查看完整内容'}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"buy-prompt-card\" v-if=\"buyInfo\" :style=\"{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t\t\t<view class=\"lock-icon\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/lock-icon.png\" class=\"lock-img\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"buy-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"buy-title\">解锁完整内容</text>\r\n\t\t\t\t\t\t\t\t<text class=\"buy-subtitle\">购买《{{buyInfo.kc_name}}》课程即可学习</text>\r\n\t\t\t\t\t\t\t\t<view class=\"course-preview\" v-if=\"buyInfo.kc_pic\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"course-thumb\" :src=\"buyInfo.kc_pic\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"course-details\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"course-price\">¥{{buyInfo.kc_price}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<button class=\"buy-btn\" @tap=\"buyCourse\" :style=\"{background: t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t立即购买课程\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<!--评论部分 - 只有有权限才显示评论-->\r\n\t\t\t<block v-if=\"detail.canpl==1 && (detail.has_permission || detail.need_buy == 0)\">\r\n\t\t\t<view class=\"plbox\">\r\n\t\t\t\t<view class=\"plbox_title\"><text class=\"t1\">评论</text><text>({{plcount}})</text></view>\r\n\t\t\t\t<view class=\"plbox_content\">\r\n\t\t\t\t\t<block v-for=\"(item, idx) in datalist\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"item1 flex\">\r\n\t\t\t\t\t\t<view class=\"f1 flex0\"><image :src=\"item.headimg\"></image></view>\r\n\t\t\t\t\t\t<view class=\"f2 flex-col\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2 plcontent\"><parse :content=\"item.content\" /></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.replylist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"relist\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(hfitem, index) in item.replylist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"item2\">\r\n\t\t\t\t\t\t\t\t\t<view>{{hfitem.nickname}}：</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2 plcontent\"><parse :content=\"hfitem.content\" /></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class=\"t3 flex\">\r\n\t\t\t\t\t\t\t\t<text>{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"><text v-if=\"detail.canplrp==1\" class=\"phuifu\" style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"'../training/pinglun?type=1&id=' + detail.id + '&hfid=' + item.id\">回复</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center pzan\" @tap=\"pzan\" :data-id=\"item.id\" :data-index=\"idx\"><image :src=\"'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'\"></image>{{item.zan}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height:160rpx\"></view>\r\n\t\t\t<view class=\"pinglun notabbarbot\">\r\n\t\t\t\t<view class=\"pinput\" @tap=\"goto\" :data-url=\"'../training/pinglun?type=0&id=' + detail.id\">发表评论</view>\r\n\t\t\t\t<view class=\"zan flex-y-center\" @tap=\"zan\" :data-id=\"detail.id\">\r\n\t\t\t\t\t<image :src=\"'/static/img/zan-' + (iszan?'2':'1') + '.png'\"/><text style=\"padding-left:2px\">{{detail.zan}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n            opt:{},\r\n            loading:false,\r\n            isload: false,\r\n            menuindex:-1,\r\n            pre_url:app.globalData.pre_url,\r\n\r\n            detail:[],\r\n            datalist: [],\r\n            pagenum: 1,\r\n            id: 0,\r\n            pagecontent: \"\",\r\n            title: \"\",\r\n            sharepic: \"\",\r\n            nodata:false,\r\n            nomore:false,\r\n            iszan: \"\",\r\n\t\t\tplcount:0,\r\n\t\t\tbuyInfo: null,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][onLoad_001] 初始化训练营详情页面\r\n\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][onLoad_001] 初始化训练营详情页面, id:', opt.id);\r\n    this.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][onPullDownRefresh_001] 下拉刷新训练营详情\r\n\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][onPullDownRefresh_001] 下拉刷新训练营详情');\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\tvar that = this;\r\n\t\treturn this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,callback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar that = this;\r\n\t\tvar sharewxdata = this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,\r\n\t\t\tcallback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore && this.detail.canpl==1 && (this.detail.has_permission || this.detail.need_buy == 0)) {\r\n      // 2025-01-03 22:55:53,565-INFO-[training_detail][onReachBottom_001] 上拉加载更多评论\r\n      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][onReachBottom_001] 上拉加载更多评论, pagenum:', this.pagenum + 1);\r\n      this.pagenum = this.pagenum + 1\r\n      this.getpllist();\r\n    }\r\n  },\r\n  methods: {\r\n\t  sharecallback: function() {\r\n\t  \tapp.post(\"ApiKechengTraining/giveScorenum\",{}, function(res) {\r\n\t  \t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][sharecallback_001] 分享训练营获得积分\r\n\t  \t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][sharecallback_001] 分享训练营获得积分:', res);\r\n\t  \t});\t\r\n\t  },\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\t\r\n\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_001] 开始获取训练营详情\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_001] 开始获取训练营详情, id:', id);\r\n\t\t\t\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKechengTraining/detail', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_002] 获取训练营详情响应\r\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_002] 获取训练营详情响应, status:', res.status);\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 1){\r\n\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\t\tthat.plcount = res.plcount || 0;\r\n\t\t\t\t\tthat.iszan = res.iszan;\r\n\t\t\t\t\tthat.title = res.detail.name;\r\n\t\t\t\t\tthat.sharepic = res.detail.pic;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 设置购买信息\r\n\t\t\t\t\tif (res.detail.buy_info) {\r\n\t\t\t\t\t\tthat.buyInfo = res.detail.buy_info;\r\n\t\t\t\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_003] 设置购买信息\r\n\t\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_003] 设置购买信息:', res.detail.buy_info);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.detail.name\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_004] 权限检查结果\r\n\t\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_004] 权限检查结果, has_permission:', res.detail.has_permission, 'need_buy:', res.detail.need_buy);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 2025-01-03 22:55:53,565-ERROR-[training_detail][getdata_005] 获取训练营详情失败\r\n\t\t\t\t\tconsole.error('2025-01-03 22:55:53,565-ERROR-[training_detail][getdata_005] 获取训练营详情失败:', res.msg);\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}\r\n\t\t\t\tthat.pagenum = 1;\r\n\t\t\t\tthat.datalist = [];\r\n\t\t\t\tthat.getpllist();\r\n\t\t\t\tthat.loaded({title:res.detail ? res.detail.name : '',desc:res.detail ? res.detail.subname : '',pic:res.detail ? res.detail.pic : ''});\r\n\t\t\t});\r\n\t\t},\r\n    getpllist: function () {\r\n        var that = this;\r\n        var pagenum = that.pagenum;\r\n        \r\n        // 只有有权限的情况下才获取评论\r\n        if (!that.detail.has_permission && that.detail.need_buy == 1) {\r\n        \t// 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_001] 无权限，跳过获取评论\r\n        \tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_001] 无权限，跳过获取评论');\r\n        \treturn;\r\n        }\r\n        \r\n        // 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_002] 开始获取评论列表\r\n        console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_002] 开始获取评论列表, pagenum:', pagenum);\r\n        \r\n\t\tthat.loading = true;\r\n\t\tthat.nodata = false;\r\n\t\tthat.nomore = false;\r\n        app.post('ApiKechengTraining/getpllist', {pagenum: pagenum,id: that.detail.id}, function (res) {\r\n\t\t\tthat.loading = false;\r\n\t\t\t\r\n\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_003] 获取评论列表响应\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_003] 获取评论列表响应, data_length:', res.data ? res.data.length : 0);\r\n\t\t\t\r\n            var data = res.data || [];\r\n            if (data.length == 0) {\r\n                if(pagenum == 1){\r\n                    that.nodata = true;\r\n                }else{\r\n                    that.nomore = true;\r\n                }\r\n            }\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n        });\r\n    },\r\n    zan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      \r\n      // 2025-01-03 22:55:53,565-INFO-[training_detail][zan_001] 点赞训练营\r\n      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][zan_001] 点赞训练营, id:', id);\r\n      \r\n      app.post(\"ApiKechengTraining/zan\", {id: id}, function (res) {\r\n      \t// 2025-01-03 22:55:53,565-INFO-[training_detail][zan_002] 点赞响应\r\n      \tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][zan_002] 点赞响应, type:', res.type, 'zancount:', res.zancount);\r\n      \t\r\n        if (res.type == 0) {\r\n          //取消点赞\r\n          var iszan = 0;\r\n        } else {\r\n          var iszan = 1;\r\n        }\r\n        that.iszan = iszan;\r\n        that.detail.zan = res.zancount;\r\n      });\r\n    },\r\n    pzan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var index = e.currentTarget.dataset.index;\r\n      var datalist = that.datalist;\r\n      \r\n      // 2025-01-03 22:55:53,565-INFO-[training_detail][pzan_001] 点赞评论\r\n      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][pzan_001] 点赞评论, id:', id, 'index:', index);\r\n      \r\n      app.post(\"ApiKechengTraining/pzan\", {id: id}, function (res) {\r\n      \t// 2025-01-03 22:55:53,565-INFO-[training_detail][pzan_002] 评论点赞响应\r\n      \tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][pzan_002] 评论点赞响应, type:', res.type, 'zancount:', res.zancount);\r\n      \t\r\n        if (res.type == 0) {\r\n          //取消点赞\r\n          var iszan = 0;\r\n        } else {\r\n          var iszan = 1;\r\n        }\r\n\r\n        datalist[index].iszan = iszan;\r\n        datalist[index].zan = res.zancount;\r\n        that.datalist = datalist;\r\n      });\r\n    },\r\n    buyCourse: function() {\r\n    \tvar that = this;\r\n    \tif (!that.buyInfo || !that.buyInfo.kc_id) {\r\n    \t\tapp.error('课程信息错误');\r\n    \t\treturn;\r\n    \t}\r\n    \t\r\n    \t// 2025-01-03 22:55:53,565-INFO-[training_detail][buyCourse_001] 跳转到课程购买页面\r\n    \tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][buyCourse_001] 跳转到课程购买页面, kc_id:', that.buyInfo.kc_id);\r\n    \t\r\n    \t// 跳转到课程详情购买页面\r\n    \tapp.goto('/activity/kecheng/product?id=' + that.buyInfo.kc_id);\r\n    },\r\n    toCourse: function(e) {\r\n    \tvar that = this;\r\n    \tvar kcid = e.currentTarget.dataset.id;\r\n    \tif (!kcid) {\r\n    \t\tapp.error('课程信息错误');\r\n    \t\treturn;\r\n    \t}\r\n    \t\r\n    \t// 2025-01-03 22:55:53,565-INFO-[training_detail][toCourse_001] 跳转到课程详情页面\r\n    \tconsole.log('2025-01-03 22:55:53,565-INFO-[training_detail][toCourse_001] 跳转到课程详情页面, kcid:', kcid);\r\n    \t\r\n    \t// 跳转到课程详情页面\r\n    \tapp.goto('/activity/kecheng/product?id=' + kcid);\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.header{ background-color: #fff;padding: 10rpx 20rpx 0 20rpx;position: relative;display:flex;flex-direction:column;}\r\n.header .title{width:100%;font-size: 36rpx;color:#333;line-height: 1.4;margin:10rpx 0;margin-top:20rpx;font-weight:bold}\r\n.header .training-meta{width:100%;margin:10rpx 0;}\r\n.header .subtitle{font-size:28rpx;color:#666;line-height:1.5}\r\n.header .artinfo{width:100%;font-size:28rpx;color: #8c8c8c;font-style: normal;overflow: hidden;display:flex;margin:10rpx 0;}\r\n.header .artinfo .t1{padding-right:8rpx}\r\n.header .artinfo .t2{color:#777;padding-right:8rpx}\r\n.header .artinfo .t3{text-align:right;flex:1;}\r\n\r\n/* 课程绑定信息样式 */\r\n.course-binding {\r\n  margin: 20rpx 0;\r\n}\r\n.course-info-card {\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n}\r\n.course-left {\r\n  margin-right: 20rpx;\r\n}\r\n.course-pic {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 8rpx;\r\n}\r\n.course-right {\r\n  flex: 1;\r\n  text-align: left;\r\n}\r\n.course-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n.course-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  margin-right: 8rpx;\r\n}\r\n.course-label {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 24rpx;\r\n}\r\n.course-title {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  flex: 1;\r\n}\r\n.course-meta {\r\n  margin-top: 10rpx;\r\n}\r\n.course-price {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-right: 10rpx;\r\n}\r\n.course-price.free {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n.course-tip {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 无权限容器样式 */\r\n.no-permission-container {\r\n  padding: 20rpx 0;\r\n}\r\n.preview-content {\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  border-left: 4rpx solid #ffc107;\r\n}\r\n.preview-text {\r\n  color: #666;\r\n  font-size: 28rpx;\r\n  line-height: 1.6;\r\n  text-align: center;\r\n}\r\n\r\n/* 购买提示卡片样式 */\r\n.buy-prompt-card {\r\n  border-radius: 16rpx;\r\n  padding: 40rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);\r\n}\r\n.buy-prompt-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -50%;\r\n  right: -50%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\r\n  transform: rotate(45deg);\r\n}\r\n.lock-icon {\r\n  text-align: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n.lock-img {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  filter: brightness(10);\r\n}\r\n.buy-content {\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.buy-title {\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  display: block;\r\n  margin-bottom: 12rpx;\r\n}\r\n.buy-subtitle {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 26rpx;\r\n  display: block;\r\n  margin-bottom: 24rpx;\r\n}\r\n.course-preview {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 24rpx 0;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n.course-thumb {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 8rpx;\r\n  margin-right: 20rpx;\r\n}\r\n.course-details {\r\n  flex: 1;\r\n  text-align: left;\r\n}\r\n.course-price {\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n}\r\n.buy-btn {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  color: #fff !important;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.5);\r\n  border-radius: 50rpx;\r\n  padding: 20rpx 60rpx;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-top: 20rpx;\r\n  backdrop-filter: blur(10rpx);\r\n  transition: all 0.3s;\r\n}\r\n.buy-btn:active {\r\n  background: rgba(255, 255, 255, 0.3) !important;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.pinglun{ width:96%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:10;border-top:1px solid #f7f7f7;padding:0 2%;box-sizing:content-box}\r\n.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}\r\n.pinglun .zan{padding:0 12rpx;line-height:100rpx;border-radius:50rpx}\r\n.pinglun .zan image{width:48rpx;height:48rpx}\r\n.pinglun .zan span{height:40rpx;line-height:50rpx;font-size:32rpx}\r\n.pinglun .buybtn{margin-left:0.08rpx;background:#31C88E;height:72rpx;line-height:72rpx;padding:0 20rpx;color:#fff;border-radius:6rpx}\r\n\r\n.plbox{width:100%;padding:40rpx 20rpx;background:#fff;margin-top:10px}\r\n.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx}\r\n.plbox_title .t1{color:#000;font-weight:bold}\r\n.plbox_content .plcontent{vertical-align: middle;color:#111}\r\n.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}\r\n.plbox_content .item1{width:100%;margin-bottom:20rpx}\r\n.plbox_content .item1 .f1{width:80rpx;}\r\n.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}\r\n.plbox_content .item1 .f2{flex:1}\r\n.plbox_content .item1 .f2 .t1{}\r\n.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}\r\n.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}\r\n.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:2px}\r\n.plbox_content .item1 .f2 .phuifu{margin-left:6px}\r\n.plbox_content .relist{width:100%;background:#f5f5f5;padding:4rpx 20rpx;margin-bottom:20rpx}\r\n.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}\r\n\r\n.copyright{display:none}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024265\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}