{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?d3c8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?d5a6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?38a4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?8842", "uni-app:///pagesExa/tuanzhang/apply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?73dd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/apply.vue?54f5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "isagree", "<PERSON><PERSON><PERSON><PERSON>", "pic", "pics", "zhengming", "info", "bset", "latitude", "longitude", "address", "conditions", "hasGroup", "hasOnlineShop", "hasOfflineShop", "groupPics", "licensePics", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "console", "uni", "title", "cateChange", "locationSelect", "success", "subform", "setTimeout", "jumpUrl", "url", "fail", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "removeimg", "conditionChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8L9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAA;QACAC;QACAA;QACAA;QAEAF;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAE;UACAC;QACA;QACA;QACA;QACA;UACA1B;QACA;QACA;QACA;UACAK;QACA;UACAA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACAgB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAA;QACA;QAEAA;QACAA;QAEAA;QACAA;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACAH;QACAI;UACAP;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAP;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACA;QACA;MAAA;MAEA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA;MAEA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACAhB;MACAA;MACAA;MACA;QACAgB;QACA;MACA;MACA;QACAhB;MACA;MACA;QACAA;MACA;MACAgB;MACAA;QAAAhB;MAAA;QACAgB;QACA;UACAA;UACAQ;YACA;cACA;cACA;gBACAC;cACA;cACAP;gBACAQ;gBACAC;kBACAT;oBACAQ;oBACAC;sBACAX;oBACA;kBACA;gBACA;cACA;YACA;cACAA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAY;MACAX;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAf;QACA;UACAlB;QACA;QACA;QACA;QACA;MACA;IACA;IACAkC;MACA;MACA;MACA;MACA;QACA;QACAlC;QACAiB;MACA;QACA;QACAjB;QACAiB;MACA;QACA;QACAjB;QACAiB;MACA;IACA;IACAkB;MACA;MACA;QACA3B;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACldA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhang/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhang/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=0fda0586&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhang/apply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=0fda0586&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pic.length : null\n  var g1 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g2 = _vm.isload && _vm.conditions.hasGroup ? _vm.groupPics.length : null\n  var g3 =\n    _vm.isload && _vm.conditions.hasOfflineShop ? _vm.licensePics.length : null\n  var g4 = _vm.isload ? _vm.groupPics.join(\",\") : null\n  var g5 = _vm.isload ? _vm.licensePics.join(\",\") : null\n  var m0 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <block v-if=\"isload\">\r\n            <view v-if=\"info.id && info.status==2\" style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n                <parse :content=\"bset.verify_reject || '审核未通过：'\"/>{{info.reason}}，请修改后重新提交\r\n            </view>\r\n            <view v-if=\"info.id && info.status==0\" style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n                <parse :content=\"bset.verify_notice || '您的团长申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'\"/>\r\n            </view>\r\n            <view v-if=\"info.id && info.status==1\" style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n                <parse :content=\"bset.verify_success || '恭喜您审核通过！'\"/>\r\n            </view>\r\n            <view v-if=\"!info.id || info.status==2 || info.status==0\" style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n                <parse :content=\"bset.verify_normal || '温馨提示：审核通过后可完成入驻'\"/>\r\n            </view>\r\n            <form @submit=\"subform\">\r\n              <!--  <view class=\"apply_box\">\r\n                    \r\n                   <view class=\"apply_item\">\r\n                        <view>申请人姓名<text style=\"color:red\"> *</text></view>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"linkman\" :value=\"info.linkman\" placeholder=\"请填写姓名\"></input></view>\r\n                    </view>\r\n                   \r\n                    <view class=\"apply_item\">\r\n                        <view>申请人电话<text style=\"color:red\"> *</text></view>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"linktel\" :value=\"info.linktel\" placeholder=\"请填写手机号码\"></input></view>\r\n                    </view>\r\n                </view> -->\r\n                \r\n                <view class=\"apply_box\">\r\n                    <view class=\"apply_item\">\r\n                        <view>团长名称<text style=\"color:red\"> *</text></view>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请输入团长名称\"></input></view>\r\n                    </view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t    <view>团长电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t    <view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写手机号码\"></input></view>\r\n\t\t\t\t\t</view>\r\n                   <!-- <view class=\"apply_item\">\r\n                        <view>团长描述<text style=\"color:red\"> *</text></view>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\" placeholder=\"请输入团长描述\"></input></view>\r\n                    </view> -->\r\n                    \r\n                    <view class=\"apply_item\">\r\n                        <view>团长微信<text style=\"color:red\"> *</text></view>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"wxid\" :value=\"info.wxid\" placeholder=\"请填写微信号\"></input></view>\r\n                    </view>\r\n                \r\n                </view>\r\n\t\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t    <view class=\"apply_item\" style=\"border-bottom:0\"><text>团长头像<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t    <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t        <view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t            <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t            <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t        </view>\r\n\t\t\t\t        <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t    </view>\r\n\t\t\t\t    <input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t\t</view>\r\n                <view class=\"apply_box\">\r\n                    <view class=\"apply_item\" style=\"border-bottom:0\">\r\n                        <text>申请条件<text style=\"color:red\"> *</text></text>\r\n                    </view>\r\n                    \r\n                    <checkbox-group @change=\"conditionChange\">\r\n                        <view class=\"condition-item\">\r\n                            <label class=\"flex-y-center\">\r\n                                <checkbox value=\"1\" :checked=\"conditions.hasGroup\"/>\r\n                                <text>自有微信群50人以上</text>\r\n                            </label>\r\n                            <view class=\"group-proof\" v-if=\"conditions.hasGroup\">\r\n                                <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                                    <view v-for=\"(item, index) in groupPics\" :key=\"index\" class=\"layui-imgbox\">\r\n                                        <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"groupPics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                                        <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                                    </view>\r\n                                    <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"groupPics\" v-if=\"groupPics.length<3\"></view>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n    \r\n                        <view class=\"condition-item\">\r\n                            <label class=\"flex-y-center\">\r\n                                <checkbox value=\"2\" :checked=\"conditions.hasOnlineShop\"/>\r\n                                <text>自有线上店铺</text>\r\n                            </label>\r\n                            <view v-if=\"conditions.hasOnlineShop\">\r\n                                <view class=\"apply_item\">\r\n                                    <view>店铺链接<text style=\"color:red\"> *</text></view>\r\n                                    <view class=\"flex-y-center\">\r\n                                        <input \r\n                                            type=\"text\" \r\n                                            name=\"shopUrl\" \r\n                                            :value=\"info.shop_url\" \r\n                                            placeholder=\"请填写店铺链接\"\r\n                                            placeholder-class=\"placeholder-style\"\r\n                                        ></input>\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n    \r\n                        <view class=\"condition-item\">\r\n                            <label class=\"flex-y-center\">\r\n                                <checkbox value=\"3\" :checked=\"conditions.hasOfflineShop\"/>\r\n                                <text>自有线下店铺</text>\r\n                            </label>\r\n                            <view v-if=\"conditions.hasOfflineShop\">\r\n                                <view class=\"apply_item\">\r\n                                    <view>店铺地址<text style=\"color:red\"> *</text></view>\r\n                                    <view class=\"flex-y-center\">\r\n                                        <input \r\n                                            type=\"text\" \r\n                                            name=\"shopAddress\" \r\n                                            :value=\"info.shop_address\" \r\n                                            placeholder=\"请填写店铺地址\"\r\n                                            placeholder-class=\"placeholder-style\"\r\n                                        ></input>\r\n                                    </view>\r\n                                </view>\r\n\t\t\t\t\t\t\t\t <view>上传营业执照<text style=\"color:red\"> *</text>\r\n                                <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                                    <view v-for=\"(item, index) in licensePics\" :key=\"index\" class=\"layui-imgbox\">\r\n                                        <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"licensePics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                                        <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                                    </view>\r\n                                    <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"licensePics\" v-if=\"licensePics.length<2\"></view>\r\n                                </view>\r\n                            </view>\r\n                        </view>  </view>\r\n                    </checkbox-group>\r\n                    <input type=\"text\" hidden=\"true\" name=\"groupPics\" :value=\"groupPics.join(',')\" maxlength=\"-1\"></input>\r\n                    <input type=\"text\" hidden=\"true\" name=\"licensePics\" :value=\"licensePics.join(',')\" maxlength=\"-1\"></input>\r\n                </view>\r\n               \r\n              <!--  <view class=\"apply_box\">\r\n                    <view class=\"apply_item\" style=\"border-bottom:0\"><text>团长照片(3-5张)<text style=\"color:red\"> *</text></text></view>\r\n                    <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                        <view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n                            <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                            <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n                        </view>\r\n                        <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n                    </view>\r\n                    <input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n                </view> -->\r\n                \r\n                <view class=\"apply_box\">\r\n                    <view class=\"apply_item\">\r\n                        <text>登录用户名<text style=\"color:red\"> *</text></text>\r\n                        <view class=\"flex-y-center\"><input type=\"text\" name=\"un\" :value=\"info.un\" placeholder=\"请填写登录账号\" autocomplete=\"off\"></input></view>\r\n                    </view>\r\n                    <view class=\"apply_item\">\r\n                        <text>登录密码<text style=\"color:red\"> *</text></text>\r\n                        <view class=\"flex-y-center\"><input type=\"password\" name=\"pwd\" :value=\"info.pwd\" placeholder=\"请填写登录密码\" autocomplete=\"off\"></input></view>\r\n                    </view>\r\n                    <view class=\"apply_item\">\r\n                        <text>确认密码<text style=\"color:red\"> *</text></text>\r\n                        <view class=\"flex-y-center\"><input type=\"password\" name=\"repwd\" :value=\"info.repwd\" placeholder=\"请再次填写密码\"></input></view>\r\n                    </view>\r\n                </view>\r\n                \r\n                \r\n                <block v-if=\"bset.xieyi_show==1\">\r\n                <view class=\"flex-y-center\" style=\"margin-left:20rpx;color:#999\" v-if=\"!info.id || info.status==2\">\r\n                    <checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox value=\"1\" :checked=\"isagree\"></checkbox>阅读并同意</label></checkbox-group>\r\n                    <text style=\"color:#666\" @tap=\"showxieyiFun\">《团长入驻协议》</text>\r\n                </view>\r\n                </block>\r\n                <view style=\"padding:30rpx 0\"><button v-if=\"!info.id || info.status==2\" form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\r\n    </view>\r\n            </form>\r\n            \r\n            <view id=\"xieyi\" :hidden=\"!showxieyi\" style=\"width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)\">\r\n                <view style=\"width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px\">\r\n                    <view style=\"overflow:scroll;height:100%;\">\r\n                        <parse :content=\"bset.xieyi\"/>\r\n                    </view>\r\n                    <view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center\" @tap=\"hidexieyi\">已阅读并同意</view>\r\n                </view>\r\n            </view>\r\n        </block>\r\n        <loading v-if=\"loading\"></loading>\r\n        <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n        <popmsg ref=\"popmsg\"></popmsg>\r\n    </view>\r\n    </template>\r\n    \r\n    <script>\r\n    var app = getApp();\r\n    \r\n    export default {\r\n      data() {\r\n        return {\r\n                opt:{},\r\n                loading:false,\r\n          isload: false,\r\n                menuindex:-1,\r\n                \r\n                pre_url:app.globalData.pre_url,\r\n          datalist: [],\r\n          pagenum: 1,\r\n          cateArr: [],\r\n          cindex: 0,\r\n          isagree: false,\r\n          showxieyi: false,\r\n                pic:[],\r\n                pics:[],\r\n                zhengming:[],\r\n          info: {},\r\n                bset:{},\r\n          latitude: '',\r\n          longitude: '',\r\n          address:'',\r\n          conditions: {\r\n            hasGroup: false,\r\n            hasOnlineShop: false,\r\n            hasOfflineShop: false\r\n          },\r\n          groupPics: [],\r\n          licensePics: [],\r\n        };\r\n      },\r\n    \r\n      onLoad: function (opt) {\r\n            this.opt = app.getopts(opt);\r\n            this.getdata();\r\n      },\r\n        onPullDownRefresh: function () {\r\n            this.getdata();\r\n        },\r\n      methods: {\r\n            getdata: function () {\r\n                var that = this;\r\n                that.loading = true;\r\n                app.get('ApiTuanzhang/apply', {}, function (res) {\r\n                    console.log('完整的info对象:', res.info);\r\n                    console.log('status值:', res.info?.status);\r\n                    console.log('status类型:', typeof res.info?.status);\r\n                    \r\n                    that.loading = false;\r\n                    if (res.status == 2) {\r\n                        app.alert(res.msg, function () {\r\n                            app.goto('/pagesExa/tuanzhangadmin/index/index', 'redirect');\r\n                        })\r\n                        return;\r\n                    }\r\n                    uni.setNavigationBarTitle({\r\n                        title: res.title\r\n                    });\r\n                    var clist = res.clist;\r\n                    var cateArr = [];\r\n                    for (var i in clist) {\r\n                        cateArr.push(clist[i].name);\r\n                    }\r\n                    var pics = res.info ? res.info.pics : '';\r\n                    if (pics) {\r\n                        pics = pics.split(',');\r\n                    } else {\r\n                        pics = [];\r\n                    }\r\n                    var zhengming = res.info ? res.info.zhengming : '';\r\n                    if (zhengming) {\r\n                        zhengming = zhengming.split(',');\r\n                    } else {\r\n                        zhengming = [];\r\n                    }\r\n                    that.clist = res.clist\r\n                    that.bset = res.bset\r\n                    that.info = res.info\r\n                    that.info.linktel = res.info.linktel;\r\n                    that.address = res.info.address;\r\n                    that.latitude = res.info.latitude;\r\n                    that.longitude = res.info.longitude;\r\n                    that.cateArr = cateArr;\r\n                    that.pic = res.info.logo ? [res.info.logo] : [];\r\n                    that.pics = pics;\r\n                    that.zhengming = zhengming;\r\n                    that.loaded();\r\n                    \r\n                    if(res.info && res.info.conditions) {\r\n                        that.conditions = res.info.conditions;\r\n                    }\r\n                    \r\n                    that.groupPics = res.info.group_pics ? res.info.group_pics.split(',') : [];\r\n                    that.licensePics = res.info.license_pics ? res.info.license_pics.split(',') : [];\r\n                    \r\n                    that.info = res.info;\r\n                    that.info.shopUrl = res.info.shop_url || '';\r\n                    that.info.shopAddress = res.info.shop_address || '';\r\n                });\r\n            },\r\n        cateChange: function (e) {\r\n          this.cindex = e.detail.value;\r\n        },\r\n        locationSelect: function () {\r\n          var that = this;\r\n          uni.chooseLocation({\r\n            success: function (res) {\r\n              that.info.address = res.name;\r\n                        that.info.latitude = res.latitude;\r\n              that.info.longitude = res.longitude;\r\n              that.address = res.name;\r\n              that.latitude = res.latitude;\r\n              that.longitude = res.longitude;\r\n            }\r\n          });\r\n        },\r\n        subform: function (e) {\r\n          var that = this;\r\n          var info = e.detail.value;\r\n          // if (info.linkman == '') {\r\n          //   app.error('请填写联系人姓名');\r\n          //   return false;\r\n          // }\r\n          // if (info.linktel == '') {\r\n          //   app.error('请填写联系人电话');\r\n          //   return false;\r\n          // }\r\n          if (info.tel == '') {\r\n            app.error('请填写客服电话');\r\n            return false;\r\n          }\r\n          if (info.name == '') {\r\n            app.error('请填写团长姓名');\r\n            return false;\r\n          }\r\n          if (info.zuobiao == '') {\r\n            //app.error('请选择店铺标');\r\n            //return false;\r\n          }\r\n          // if (info.address == '') {\r\n          //   app.error('请填写店铺地址');\r\n          //   return false;\r\n          // }\r\n          if (info.pic == '') {\r\n            app.error('请上传团长头像');\r\n            return false;\r\n          }\r\n          // if (info.pics == '') {\r\n          //   app.error('请上传团长照片');\r\n          //   return false;\r\n          // }\r\n          if (info.zhengming == '') {//$.error('请上传证明材料');return false;\r\n          }\r\n          if (info.un == '') {\r\n            app.error('请填写登录账号');\r\n            return false;\r\n          }\r\n          if (info.pwd == '') {\r\n            app.error('请填写登录密码');\r\n            return false;\r\n          }\r\n          var pwd = info.pwd;\r\n          if (pwd.length < 6) {\r\n            app.error('密码不能小于6位');\r\n            return false;\r\n          }\r\n          if (info.repwd != info.pwd) {\r\n            app.error('两次输入密码不一致');\r\n            return false;\r\n          } //if(!/(^0?1[3|4|5|6|7|8|9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$)/.test(tel)){\r\n          //\tdialog('手机号格式错误');return false;\r\n          //}\r\n          info.address = that.address || '';\r\n          info.latitude = that.latitude || '';\r\n          info.longitude = that.longitude || '';\r\n          if (that.bset && that.bset.xieyi_show == 1 && !that.isagree) {\r\n            app.error('请先阅读并同意商户入驻协议');\r\n            return false;\r\n          }\r\n          if (that.clist && that.clist[that.cindex]) {\r\n            info.cid = that.clist[that.cindex].id;\r\n          }\r\n          if (that.info && that.info.id) {\r\n            info.id = that.info.id;\r\n          }\r\n                app.showLoading('提交中');\r\n          app.post(\"ApiTuanzhang/apply\", {info: info}, function (res) {\r\n                    app.showLoading(false);\r\n            if (res.status == 1) {\r\n              app.success(res.msg);\r\n              setTimeout(function () {\r\n                if (res.after_register_url) {\r\n                  let jumpUrl = res.after_register_url;\r\n                  if (!jumpUrl.startsWith('/')) {\r\n                    jumpUrl = '/' + jumpUrl;\r\n                  }\r\n                  uni.redirectTo({\r\n                    url: jumpUrl,\r\n                    fail: function() {\r\n                      uni.switchTab({\r\n                        url: jumpUrl,\r\n                        fail: function() {\r\n                          app.goto(app.globalData.indexurl);\r\n                        }\r\n                      });\r\n                    }\r\n                  });\r\n                } else {\r\n                  app.goto(app.globalData.indexurl);\r\n                }\r\n              }, 1500);\r\n            } else {\r\n              app.error(res.msg);\r\n            }\r\n          });\r\n        },\r\n        isagreeChange: function (e) {\r\n          console.log(e.detail.value);\r\n          var val = e.detail.value;\r\n          if (val.length > 0) {\r\n            this.isagree = true;\r\n          } else {\r\n            this.isagree = false;\r\n          }\r\n        },\r\n        showxieyiFun: function () {\r\n          this.showxieyi = true;\r\n        },\r\n        hidexieyi: function () {\r\n          this.showxieyi = false;\r\n                this.isagree = true;\r\n        },\r\n            uploadimg:function(e){\r\n                var that = this;\r\n                var field= e.currentTarget.dataset.field\r\n                var pics = that[field]\r\n                if(!pics) pics = [];\r\n                app.chooseImage(function(urls){\r\n                    for(var i=0;i<urls.length;i++){\r\n                        pics.push(urls[i]);\r\n                    }\r\n                    if(field == 'pic') that.pic = pics;\r\n                    if(field == 'pics') that.pics = pics;\r\n                    if(field == 'zhengming') that.zhengming = pics;\r\n                },1)\r\n            },\r\n            removeimg:function(e){\r\n                var that = this;\r\n                var index= e.currentTarget.dataset.index\r\n                var field= e.currentTarget.dataset.field\r\n                if(field == 'pic'){\r\n                    var pics = that.pic\r\n                    pics.splice(index,1);\r\n                    that.pic = pics;\r\n                }else if(field == 'pics'){\r\n                    var pics = that.pics\r\n                    pics.splice(index,1);\r\n                    that.pics = pics;\r\n                }else if(field == 'zhengming'){\r\n                    var pics = that.zhengming\r\n                    pics.splice(index,1);\r\n                    that.zhengming = pics;\r\n                }\r\n            },\r\n        conditionChange(e) {\r\n            const values = e.detail.value;\r\n            this.conditions = {\r\n                hasGroup: values.includes('1'),\r\n                hasOnlineShop: values.includes('2'),\r\n                hasOfflineShop: values.includes('3')\r\n            };\r\n        },\r\n      }\r\n    }\r\n    </script>\r\n    <style>\r\n    radio{transform: scale(0.6);}\r\n    checkbox{transform: scale(0.6);}\r\n    .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n    .apply_title { background: #fff}\r\n    .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n    .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n    \r\n    .apply_item {\r\n      line-height: 100rpx; \r\n      display: flex;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #eee;\r\n    }\r\n    \r\n    .apply_item input {\r\n      width: 100%;\r\n      height: 100rpx;\r\n      border: none;\r\n      color: #111;\r\n      font-size: 28rpx;\r\n      text-align: right;\r\n    }\r\n    \r\n    .placeholder-style {\r\n      color: #999999;\r\n    }\r\n    \r\n    .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n    \r\n    .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n    .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\r\n    .layui-imgbox-close image{width:100%;height:100%}\r\n    .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n    .layui-imgbox-img>image{max-width:100%;}\r\n    .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n    .uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n    \r\n    .condition-item {\r\n      padding: 20rpx 0;\r\n      border-bottom: 1px solid #eee;\r\n    }\r\n    \r\n    .condition-item:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .group-proof {\r\n      margin-top: 20rpx;\r\n      padding-left: 40rpx;\r\n    }\r\n    </style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115056324\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}