{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?f96c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?c738", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?d697", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?a9ef", "uni-app:///pagesExt/business/apply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?0eeb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/business/apply.vue?4c2c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "rateArr", "rateIndex", "selectedRate", "isagree", "<PERSON><PERSON><PERSON><PERSON>", "pic", "pics", "zhengming", "info", "custom_rate", "invite_code", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bset", "latitude", "longitude", "address", "withdrawMethods", "withdrawMethodIndex", "selectedWithdrawMethod", "showCateSearchPopup", "cateSearchKeyword", "filteredCateList", "availableWithdrawMethods", "registerConfig", "simple_register_mode", "register_need_audit", "register_need_invite_code", "register_need_select_rate", "register_need_fill_rate", "rate_min", "rate_max", "register_need_product_images", "register_need_certificates", "register_need_withdraw_method", "customRateError", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "rateChange", "withdrawMethodChange", "locationSelect", "success", "subform", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "goBack", "delta", "removeimg", "showCateSearch", "hideCateSearch", "searchCate", "selectCate", "validateCustomRateValue", "validateCustomRate", "validateCustomRateOnBlur"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuP9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;QACA;QACA;QACA;UACA5C;QACA;QACA;QACA;QACA;QACA;UACAE;QACA;QACAuC;QACAA;;QAEA;QACA;UACA;YACA;cACAA;cACAA;cACA;YACA;UACA;QACA;QAEA;QACA;UACAjC;QACA;UACAA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACAgC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACAA;UACAA;QACA;;QAEA;QACA;UACAA;QACA;QAEAA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAL;QACAM;UACAR;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAS;MACA;MACA;;MAEA;MACA;QACAxC;MACA;;MAEA;MACA;QACAgC;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;YACAA;YACA;UACA;QACA;;QAEA;QACA;UACA;YACAA;YACA;UACA;UAEA;UACA;YACAA;YACA;UACA;UAEA;YACAA;YACA;UACA;;UAEA;UACA;YACAA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;YACAA;YACA;UACA;QACA;UACA;YACAA;YACA;UACA;QACA;UACA;YACAA;YACA;UACA;QACA;MACA;;MAEA;MACAhC;MACAA;MACAA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACAgC;QACA;MACA;;MAEA;MACAA;MACAA;QAAAhC;MAAA;QACAgC;QACA;UACAA;UACAS;YACA;cACAT;YACA;cACAA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEAU;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAb;QACA;UACAlC;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAgD;MACAb;QACAc;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAlD;QACAiC;MACA;QACA;QACAjC;QACAiC;MACA;QACA;QACAjC;QACAiC;MACA;IACA;IACAkB;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACApB;QACA;MACA;IACA;IACAqB;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAtB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACAA;MACA;IACA;IACA;IACAuB;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrsBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/business/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/business/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=539c6d5a&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/business/apply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=539c6d5a&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.isload && _vm.showCateSearchPopup ? _vm.filteredCateList.length : null\n  var g1 = _vm.isload ? _vm.pic.length : null\n  var g2 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g3 =\n    _vm.isload && _vm.registerConfig.register_need_product_images == 1\n      ? _vm.pics.length\n      : null\n  var g4 =\n    _vm.isload && _vm.registerConfig.register_need_product_images == 1\n      ? _vm.pics.join(\",\")\n      : null\n  var g5 =\n    _vm.isload && _vm.registerConfig.register_need_certificates == 1\n      ? _vm.zhengming.join(\",\")\n      : null\n  var g6 = _vm.isload\n    ? _vm.registerConfig.register_need_withdraw_method == 1 &&\n      _vm.availableWithdrawMethods &&\n      _vm.availableWithdrawMethods.length > 0\n    : null\n  var m0 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && (!_vm.info.id || _vm.info.status == 2)\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <block v-if=\"isload\">\r\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==2\">\r\n        <parse :content=\"bset.verify_reject || '审核未通过：'\"/>{{info.reason}}，请修改后重新提交\r\n      </view>\r\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==0\">\r\n        <parse :content=\"bset.verify_notice || '您的入驻申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'\"/>\r\n      </view>\r\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==1\">\r\n        <parse :content=\"bset.verify_success || '恭喜您审核通过！'\"/>\r\n      </view>\r\n      <view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\">\r\n        <parse :content=\"bset.verify_normal || '温馨提示：审核通过后需缴纳保证金可完成入驻'\"/>\r\n      </view>\r\n  \r\n      <form @submit=\"subform\">\r\n        <view class=\"apply_box\">\r\n          \r\n          <view class=\"apply_item\">\r\n            <view>联系人姓名<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"linkman\" :value=\"info.linkman\" placeholder=\"请填写姓名\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\">\r\n            <view>联系人电话<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"linktel\" :value=\"info.linktel\" placeholder=\"请填写手机号码\"></input></view>\r\n          </view>\r\n          <!-- 邀请码输入框：根据后台配置决定是否显示 -->\r\n          <view class=\"apply_item\" v-if=\"registerConfig.register_need_invite_code == 1\">\r\n            <view>\r\n              {{registerConfig.register_need_invite_code == 1 ? '邀请码' : '拓展人邀请码'}}\r\n              <text style=\"color:red\" v-if=\"registerConfig.register_need_invite_code == 1\"> *</text>\r\n            </view>\r\n            <view class=\"flex-y-center\">\r\n              <input type=\"text\" name=\"tuozhanid\" :value=\"info.tuozhanid\" :placeholder=\"registerConfig.register_need_invite_code == 1 ? '请填写邀请码' : '请填写拓展员邀请码'\"></input>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"apply_box\">\r\n          <view class=\"apply_item\">\r\n            <view>商家名称<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请输入商家名称\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\">\r\n            <view>商家描述<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\" placeholder=\"请输入商家描述\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\" v-if=\"registerConfig.simple_register_mode != 1\">\r\n            <view>主营类目<text style=\"color:red\"> *</text></view>\r\n            <view>\r\n              <view class=\"search-picker\" @tap=\"showCateSearch\">\r\n                <view class=\"picker\">{{cateArr[cindex] || '请选择主营类目'}}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 类目搜索弹窗 -->\r\n          <view v-if=\"showCateSearchPopup\" class=\"cate-search-mask\" @tap=\"hideCateSearch\">\r\n            <view class=\"cate-search-popup\" @tap.stop>\r\n              <view class=\"cate-search-header\">\r\n                <view class=\"cate-search-input\">\r\n                  <input type=\"text\" v-model=\"cateSearchKeyword\" placeholder=\"搜索类目\" @input=\"searchCate\"/>\r\n                </view>\r\n                <view class=\"cate-search-close\" @tap=\"hideCateSearch\">关闭</view>\r\n              </view>\r\n              <view class=\"cate-search-list\">\r\n                <view \r\n                  v-for=\"(item, index) in filteredCateList\" \r\n                  :key=\"index\" \r\n                  class=\"cate-search-item\"\r\n                  @tap=\"selectCate(index)\"\r\n                >\r\n                  {{item.name}}\r\n                </view>\r\n                <view v-if=\"filteredCateList.length === 0\" class=\"cate-no-result\">\r\n                  无匹配结果\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n            <!-- 费率选择 -->\r\n        <view class=\"apply_item\" v-if=\"registerConfig.register_need_select_rate == 1 || registerConfig.register_need_fill_rate == 1\">\r\n          <view>商户费率<text style=\"color:red\"> *</text></view>\r\n          \r\n          <!-- 费率套餐选择 -->\r\n          <view v-if=\"registerConfig.register_need_select_rate == 1\">\r\n            <picker @change=\"rateChange\" :value=\"rateIndex\" :range=\"rateArr\">\r\n              <view class=\"picker\">{{ rateArr[rateIndex] || '请选择费率套餐' }}</view>\r\n            </picker>\r\n          </view>\r\n          \r\n          <!-- 自定义费率填写 -->\r\n          <view v-if=\"registerConfig.register_need_fill_rate == 1\" class=\"flex-y-center\">\r\n            <input \r\n              type=\"number\" \r\n              name=\"custom_rate\" \r\n              v-model=\"info.custom_rate\" \r\n              :placeholder=\"'请填写费率 (' + registerConfig.rate_min + '%-' + registerConfig.rate_max + '%)'\"\r\n              @input=\"validateCustomRate\"\r\n              @blur=\"validateCustomRateOnBlur\"\r\n            ></input>\r\n          </view>\r\n          \r\n          <!-- 费率说明提示 -->\r\n          <!-- <view v-if=\"registerConfig.register_need_fill_rate == 1\" class=\"rate-tips\">\r\n            <text class=\"rate-tip-text\">费率范围：{{registerConfig.rate_min}}% - {{registerConfig.rate_max}}%</text>\r\n            <text v-if=\"customRateError\" class=\"rate-error\">{{customRateError}}</text>\r\n          </view> -->\r\n        </view>\r\n  \r\n          <view class=\"apply_item\">\r\n            <view>店铺坐标<text style=\"color:red\"> </text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\" :value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\">\r\n            <view>店铺地址<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"address\" :value=\"address\" placeholder=\"请输入商家详细地址\"></input></view>\r\n          </view>\r\n          <input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\r\n          <input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\r\n          <view class=\"apply_item\">\r\n            <view>客服电话<text style=\"color:red\"> *</text></view>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\" style=\"line-height:50rpx\"><textarea name=\"content\" placeholder=\"请输入商家简介\" :value=\"info.content\"></textarea></view>\r\n        </view>\r\n        <view class=\"apply_box\">\r\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>商家主图<text style=\"color:red\"> *</text></text></view>\r\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n            <view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n            </view>\r\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n          </view>\r\n          <input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n        </view>\r\n        <view class=\"apply_box\" v-if=\"registerConfig.register_need_product_images == 1\">\r\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>主要产品图片(3-5张)<text style=\"color:red\"> *</text></text></view>\r\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n            <view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n            </view>\r\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n          </view>\r\n          <input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n        </view>\r\n        <view class=\"apply_box\" v-if=\"registerConfig.register_need_certificates == 1\">\r\n          <view class=\"apply_item\" style=\"border-bottom:0\"><text>身份证，营业执照，及相关资质<text style=\"color:red\"> *</text></text></view>\r\n          <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n            <view v-for=\"(item, index) in zhengming\" :key=\"index\" class=\"layui-imgbox\">\r\n              <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"zhengming\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n              <view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n            </view>\r\n            <view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"zhengming\"></view>\r\n          </view>\r\n          <input type=\"text\" hidden=\"true\" name=\"zhengming\" :value=\"zhengming.join(',')\" maxlength=\"-1\"></input>\r\n        </view>\r\n        \r\n          \r\n          <!-- 新增提现方式部分 -->\r\n          <view class=\"apply_box\" v-if=\"registerConfig.register_need_withdraw_method == 1 && availableWithdrawMethods && availableWithdrawMethods.length > 0\">\r\n            <view class=\"apply_item\">\r\n              <view>提现方式<text style=\"color:red\"> *</text></view>\r\n              <view>\r\n                <picker @change=\"withdrawMethodChange\" :value=\"withdrawMethodIndex\" :range=\"availableWithdrawMethods\">\r\n                  <view class=\"picker\">{{ availableWithdrawMethods[withdrawMethodIndex] || '请选择提现方式' }}</view>\r\n                </picker>\r\n              </view>\r\n            </view>\r\n    \r\n            <!-- 支付宝相关字段 -->\r\n            <view v-if=\"selectedWithdrawMethod === '支付宝'\" class=\"apply_item\">\r\n              <view>支付宝姓名<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"alipayName\" v-model=\"info.bankcarduser\" placeholder=\"请输入支付宝姓名\"></input></view>\r\n            </view>\r\n            <view v-if=\"selectedWithdrawMethod === '支付宝'\" class=\"apply_item\">\r\n              <view>支付宝账号<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"alipayAccount\" v-model=\"info.aliaccount\" placeholder=\"请输入支付宝账号\"></input></view>\r\n            </view>\r\n    \r\n            <!-- 银行卡相关字段 -->\r\n            <view v-if=\"selectedWithdrawMethod === '银行卡'\" class=\"apply_item\">\r\n              <view>持卡人<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"bankcarduser\" v-model=\"info.bankcarduser\" placeholder=\"请输入持卡人姓名\"></input></view>\r\n            </view>\r\n            <view v-if=\"selectedWithdrawMethod === '银行卡'\" class=\"apply_item\">\r\n              <view>银行卡账号<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"bankCardAccount\" v-model=\"info.bankCardAccount\" placeholder=\"请输入银行卡账号\"></input></view>\r\n            </view>\r\n            <view v-if=\"selectedWithdrawMethod === '银行卡'\" class=\"apply_item\">\r\n              <view>开户行<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"bankName\" v-model=\"info.bankname\" placeholder=\"请输入开户行\"></input></view>\r\n            </view>\r\n    \r\n            <!-- 微信钱包相关字段 -->\r\n            <view v-if=\"selectedWithdrawMethod === '微信钱包'\" class=\"apply_item\">\r\n              <view>微信号<text style=\"color:red\"> *</text></view>\r\n              <view class=\"flex-y-center\"><input type=\"text\" name=\"weixinAccount\" v-model=\"info.weixin\" placeholder=\"请输入微信号\"></input></view>\r\n            </view>\r\n          </view>\r\n        <view class=\"apply_box\">\r\n          <view class=\"apply_item\">\r\n            <text>登录账号<text style=\"color:red\"> *</text></text>\r\n            <view class=\"flex-y-center\"><input type=\"text\" name=\"un\" :value=\"info.un\" placeholder=\"请填写登录账号\" autocomplete=\"off\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\">\r\n            <text>登录密码<text style=\"color:red\"> *</text></text>\r\n            <view class=\"flex-y-center\"><input type=\"password\" name=\"pwd\" :value=\"info.pwd\" placeholder=\"请填写登录密码\" autocomplete=\"off\"></input></view>\r\n          </view>\r\n          <view class=\"apply_item\">\r\n            <text>确认密码<text style=\"color:red\"> *</text></text>\r\n            <view class=\"flex-y-center\"><input type=\"password\" name=\"repwd\" :value=\"info.repwd\" placeholder=\"请再次填写密码\"></input></view>\r\n          </view>\r\n        </view>\r\n        <block v-if=\"bset.xieyi_show==1\">\r\n        <view class=\"flex-y-center\" style=\"margin-left:20rpx;color:#999\" v-if=\"!info.id || info.status==2\">\r\n          <checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox value=\"1\" :checked=\"isagree\"></checkbox>阅读并同意</label></checkbox-group>\r\n          <text style=\"color:#666\" @tap=\"showxieyiFun\">《商户入驻协议》</text>\r\n        </view>\r\n        </block>\r\n        <view style=\"padding:30rpx 0\"><button v-if=\"!info.id || info.status==2\" form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\r\n  </view>\r\n      </form>\r\n      <!-- 底部返回按钮 -->\r\n         <view class=\"content\">\r\n           <view class=\"back-button\" @tap=\"goBack\">\r\n             <text class=\"t1\">返回</text>\r\n           </view>\r\n         </view>\r\n      <view id=\"xieyi\" :hidden=\"!showxieyi\" style=\"width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)\">\r\n        <view style=\"width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px\">\r\n          <view style=\"overflow:scroll;height:100%;\">\r\n            <parse :content=\"bset.xieyi\"/>\r\n          </view>\r\n          <view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center\" @tap=\"hidexieyi\">已阅读并同意</view>\r\n        </view>\r\n      </view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n  </template>\r\n  <script>\r\n  var app = getApp();\r\n  \r\n  export default {\r\n    data() {\r\n      return {\r\n        opt: {},\r\n        loading: false,\r\n        isload: false,\r\n        menuindex: -1,\r\n        pre_url: app.globalData.pre_url,\r\n        datalist: [],\r\n        pagenum: 1,\r\n        cateArr: [],\r\n        cindex: 0,\r\n        rateArr: [], // 商户费率名称数组\r\n        rateIndex: 0, // 当前选中的费率索引\r\n        selectedRate: null, // 当前选中的费率对象\r\n        isagree: false,\r\n        showxieyi: false,\r\n        pic: [],\r\n        pics: [],\r\n        zhengming: [],\r\n        info: {\r\n          custom_rate: '',\r\n          invite_code: '',\r\n          tuozhanid: ''\r\n        },\r\n        bset: {},\r\n        latitude: '',\r\n        longitude: '',\r\n        address: '',\r\n        withdrawMethods: [], // 改为动态数组，根据后台配置\r\n        withdrawMethodIndex: 0,\r\n        selectedWithdrawMethod: '',\r\n        showCateSearchPopup: false,\r\n        cateSearchKeyword: '',\r\n        filteredCateList: [],\r\n        availableWithdrawMethods: [], // 新增：存储后台返回的可用提现方式\r\n        // 入驻模式配置\r\n        registerConfig: {\r\n          simple_register_mode: 0,\r\n          register_need_audit: 1,\r\n          register_need_invite_code: 0,\r\n          register_need_select_rate: 0,\r\n          register_need_fill_rate: 0,\r\n          rate_min: 0,\r\n          rate_max: 100,\r\n          register_need_product_images: 0,\r\n          register_need_certificates: 1,\r\n          register_need_withdraw_method: 1\r\n        },\r\n        customRateError: '' // 新增：自定义费率验证错误提示\r\n      };\r\n    },\r\n  \r\n    onLoad: function (opt) {\r\n      this.opt = app.getopts(opt);\r\n      this.getdata();\r\n    },\r\n    onPullDownRefresh: function () {\r\n      this.getdata();\r\n    },\r\n    methods: {\r\n      getdata: function () {\r\n        var that = this;\r\n        that.loading = true;\r\n        app.get('ApiBusiness/apply', {}, function (res) {\r\n          that.loading = false;\r\n          if (res.status == 2) {\r\n            app.alert(res.msg, function () {\r\n              app.goto('/admin/index/index', 'redirect');\r\n            });\r\n            return;\r\n          }\r\n          uni.setNavigationBarTitle({\r\n            title: res.title\r\n          });\r\n          var clist = res.clist;\r\n          var cateArr = [];\r\n          for (var i in clist) {\r\n            cateArr.push(clist[i].name);\r\n          }\r\n          // 处理费率数据，模仿分类的处理方式\r\n          var feilv = res.feilv || [];\r\n          var rateArr = [];\r\n          for (var i in feilv) {\r\n            rateArr.push(feilv[i].name);\r\n          }\r\n          that.feilv = feilv;\r\n          that.rateArr = rateArr;\r\n          \r\n          // 如果有现有的费率数据，设置对应的索引\r\n          if (res.info && res.info.rate_id) {\r\n            for (var j = 0; j < feilv.length; j++) {\r\n              if (feilv[j].id == res.info.rate_id) {\r\n                that.rateIndex = j;\r\n                that.selectedRate = feilv[j];\r\n                break;\r\n              }\r\n            }\r\n          }\r\n          \r\n          var pics = res.info ? res.info.pics : '';\r\n          if (pics) {\r\n            pics = pics.split(',');\r\n          } else {\r\n            pics = [];\r\n          }\r\n          var zhengming = res.info ? res.info.zhengming : '';\r\n          if (zhengming) {\r\n            zhengming = zhengming.split(',');\r\n          } else {\r\n            zhengming = [];\r\n          }\r\n          that.clist = res.clist;\r\n          that.bset = res.bset;\r\n          that.info = res.info;\r\n          that.address = res.info.address;\r\n          that.latitude = res.info.latitude;\r\n          that.longitude = res.info.longitude;\r\n          that.cateArr = cateArr;\r\n          that.pic = res.info.logo ? [res.info.logo] : [];\r\n          that.pics = pics;\r\n          that.zhengming = zhengming;\r\n          that.availableWithdrawMethods = res.availableWithdrawMethods || [];\r\n          \r\n          // 获取入驻模式配置\r\n          if(res.registerConfig) {\r\n            that.registerConfig = Object.assign(that.registerConfig, res.registerConfig);\r\n          }\r\n          \r\n          // 如果有可用提现方式，自动选中第一个\r\n          if (that.availableWithdrawMethods.length > 0) {\r\n            that.selectedWithdrawMethod = that.availableWithdrawMethods[0];\r\n            that.withdrawMethodIndex = 0;\r\n          }\r\n          \r\n          // 初始化自定义费率验证（如果有现有数据）\r\n          if (that.registerConfig.register_need_fill_rate == 1 && that.info.custom_rate) {\r\n            that.validateCustomRateValue(that.info.custom_rate);\r\n          }\r\n          \r\n          that.loaded();\r\n        });\r\n      },\r\n      cateChange: function (e) {\r\n        this.cindex = e.detail.value;\r\n      },\r\n      // 费率选择事件处理\r\n      rateChange: function (e) {\r\n        this.rateIndex = e.detail.value;\r\n        this.selectedRate = this.feilv[this.rateIndex]; // 更新选中的费率对象\r\n      },\r\n      withdrawMethodChange: function (e) {\r\n        this.withdrawMethodIndex = e.detail.value;\r\n        this.selectedWithdrawMethod = this.availableWithdrawMethods[this.withdrawMethodIndex];\r\n      },\r\n      locationSelect: function () {\r\n        var that = this;\r\n        uni.chooseLocation({\r\n          success: function (res) {\r\n            that.info.address = res.name;\r\n            that.info.latitude = res.latitude;\r\n            that.info.longitude = res.longitude;\r\n            that.address = res.name;\r\n            that.latitude = res.latitude;\r\n            that.longitude = res.longitude;\r\n          }\r\n        });\r\n      },\r\n      subform: function (e) {\r\n        var that = this;\r\n        var info = e.detail.value;\r\n      \r\n        // 添加 id 到表单数据中\r\n        if (that.info && that.info.id) {\r\n          info.id = that.info.id;  // 将已有的 id 添加到表单数据中\r\n        }\r\n      \r\n        // 验证所有必填项\r\n        if (info.linkman == '') {\r\n          app.error('请填写联系人姓名');\r\n          return false;\r\n        }\r\n        if (info.linktel == '') {\r\n          app.error('请填写联系人电话');\r\n          return false;\r\n        }\r\n        if (info.tel == '') {\r\n          app.error('请填写客服电话');\r\n          return false;\r\n        }\r\n        if (info.name == '') {\r\n          app.error('请填写商家名称');\r\n          return false;\r\n        }\r\n        // 简单入驻模式下不需要验证主营类目\r\n        if (that.registerConfig.simple_register_mode != 1 && that.cindex < 0) {\r\n          app.error('请选择主营类目');\r\n          return false;\r\n        }\r\n        if (info.address == '') {\r\n          app.error('请填写店铺地址');\r\n          return false;\r\n        }\r\n        if (info.pic == '') {\r\n          app.error('请上传商家主图');\r\n          return false;\r\n        }\r\n        \r\n        // 根据配置验证邀请码\r\n        if (that.registerConfig.register_need_invite_code == 1) {\r\n          if (!info.tuozhanid || info.tuozhanid == '') {\r\n            app.error('请填写邀请码');\r\n            return false;\r\n          }\r\n        }\r\n        \r\n        // 根据配置验证产品图片\r\n        if (that.registerConfig.register_need_product_images == 1 && info.pics == '') {\r\n          app.error('请上传商家照片');\r\n          return false;\r\n        }\r\n        \r\n        // 根据配置验证证明材料\r\n        if (that.registerConfig.register_need_certificates == 1 && info.zhengming == '') {\r\n          app.error('请上传证明材料');\r\n          return false;\r\n        }\r\n        if (info.un == '') {\r\n          app.error('请填写登录账号');\r\n          return false;\r\n        }\r\n        if (info.pwd == '') {\r\n          app.error('请填写登录密码');\r\n          return false;\r\n        }\r\n        if (info.pwd.length < 6) {\r\n          app.error('密码不能小于6位');\r\n          return false;\r\n        }\r\n        if (info.repwd != info.pwd) {\r\n          app.error('两次输入的密码不一致');\r\n          return false;\r\n        }\r\n      \r\n        // 根据配置验证费率\r\n        if (that.registerConfig.register_need_select_rate == 1 || that.registerConfig.register_need_fill_rate == 1) {\r\n          // 验证费率套餐选择\r\n          if (that.registerConfig.register_need_select_rate == 1) {\r\n            if (that.rateIndex < 0 || !that.feilv[that.rateIndex] || !that.feilv[that.rateIndex].id) {\r\n              app.error('请选择商户费率套餐');\r\n              return false;\r\n            }\r\n          }\r\n          \r\n          // 验证自定义费率填写\r\n          if (that.registerConfig.register_need_fill_rate == 1) {\r\n            if (!info.custom_rate || info.custom_rate == '' || info.custom_rate == '0') {\r\n              app.error('请填写商户费率');\r\n              return false;\r\n            }\r\n            \r\n            var rate = parseFloat(info.custom_rate);\r\n            if (isNaN(rate)) {\r\n              app.error('费率必须为有效数字');\r\n              return false;\r\n            }\r\n            \r\n            if (rate < that.registerConfig.rate_min || rate > that.registerConfig.rate_max) {\r\n              app.error('费率必须在 ' + that.registerConfig.rate_min + '% - ' + that.registerConfig.rate_max + '% 范围内');\r\n              return false;\r\n            }\r\n            \r\n            // 如果有实时验证错误，阻止提交\r\n            if (that.customRateError) {\r\n              app.error(that.customRateError);\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 根据配置验证提现方式（只有当需要提现方式且有可用提现方式时才验证）\r\n        if (that.registerConfig.register_need_withdraw_method == 1 && that.availableWithdrawMethods && that.availableWithdrawMethods.length > 0) {\r\n          if (that.selectedWithdrawMethod === '支付宝') {\r\n            if (!info.alipayName || !info.alipayAccount) {\r\n              app.error('请填写支付宝相关信息');\r\n              return false;\r\n            }\r\n          } else if (that.selectedWithdrawMethod === '银行卡') {\r\n            if (!info.bankcarduser || !info.bankCardAccount || !info.bankName) {\r\n              app.error('请填写银行卡相关信息');\r\n              return false;\r\n            }\r\n          } else if (that.selectedWithdrawMethod === '微信钱包') {\r\n            if (!info.weixin) {\r\n              app.error('请填写微信号');\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n      \r\n        // 赋值地址、纬度和经度\r\n        info.address = that.address;\r\n        info.latitude = that.latitude;\r\n        info.longitude = that.longitude;\r\n        \r\n        // 根据配置设置主营类目\r\n        if (that.registerConfig.simple_register_mode != 1) {\r\n          info.cid = that.clist[that.cindex] ? that.clist[that.cindex].id : '';\r\n        }\r\n      \r\n        // 根据配置设置费率相关字段\r\n        if (that.registerConfig.register_need_select_rate == 1) {\r\n          info.rate_id = that.feilv[that.rateIndex] ? that.feilv[that.rateIndex].id : null;\r\n        }\r\n        if (that.registerConfig.register_need_fill_rate == 1) {\r\n          info.custom_rate = info.custom_rate;\r\n        }\r\n      \r\n        // 根据配置设置提现方式（只有当需要提现方式且有可用提现方式时才设置）\r\n        if (that.registerConfig.register_need_withdraw_method == 1 && that.availableWithdrawMethods && that.availableWithdrawMethods.length > 0) {\r\n          info.withdrawMethod = that.selectedWithdrawMethod;\r\n          if (that.selectedWithdrawMethod === '支付宝') {\r\n            info.alipayName = that.info.alipayName;\r\n            info.alipayAccount = that.info.alipayAccount;\r\n          } else if (that.selectedWithdrawMethod === '银行卡') {\r\n            info.bankcarduser = that.info.bankcarduser;\r\n            info.bankCardAccount = that.info.bankCardAccount;\r\n            info.bankName = that.info.bankName;\r\n          } else if (that.selectedWithdrawMethod === '微信钱包') {\r\n            info.weixin = that.info.weixin;\r\n          }\r\n        }\r\n      \r\n        // 检查是否同意协议（如果需要）\r\n        if (that.bset.xieyi_show == 1 && !that.isagree) {\r\n          app.error('请先阅读并同意商户入驻协议');\r\n          return false;\r\n        }\r\n      \r\n        // 提交表单数据到服务器\r\n        app.showLoading('提交中');\r\n        app.post(\"ApiBusiness/apply\", { info: info }, function (res) {\r\n          app.showLoading(false);\r\n          if (res.status == 1) {\r\n            app.success(res.msg);\r\n            setTimeout(function () {\r\n              if (res.after_register_url) {\r\n                app.goto(res.after_register_url);\r\n              } else {\r\n                app.goto(app.globalData.indexurl);\r\n              }\r\n            }, 1500);\r\n          } else {\r\n            app.error(res.msg);\r\n          }\r\n        });\r\n      },\r\n  \r\n      isagreeChange: function (e) {\r\n        var val = e.detail.value;\r\n        this.isagree = val.length > 0;\r\n      },\r\n      showxieyiFun: function () {\r\n        this.showxieyi = true;\r\n      },\r\n      hidexieyi: function () {\r\n        this.showxieyi = false;\r\n        this.isagree = true;\r\n      },\r\n      uploadimg: function (e) {\r\n        var that = this;\r\n        var field = e.currentTarget.dataset.field;\r\n        var pics = that[field];\r\n        if (!pics) pics = [];\r\n        app.chooseImage(function (urls) {\r\n          for (var i = 0; i < urls.length; i++) {\r\n            pics.push(urls[i]);\r\n          }\r\n          if (field == 'pic') that.pic = pics;\r\n          if (field == 'pics') that.pics = pics;\r\n          if (field == 'zhengming') that.zhengming = pics;\r\n        }, 1);\r\n      },\r\n    // 返回功能\r\n      goBack: function () {\r\n        uni.navigateBack({\r\n          delta: 1\r\n        });\r\n      },\r\n      removeimg: function (e) {\r\n        var that = this;\r\n        var index = e.currentTarget.dataset.index;\r\n        var field = e.currentTarget.dataset.field;\r\n        if (field == 'pic') {\r\n          var pics = that.pic;\r\n          pics.splice(index, 1);\r\n          that.pic = pics;\r\n        } else if (field == 'pics') {\r\n          var pics = that.pics;\r\n          pics.splice(index, 1);\r\n          that.pics = pics;\r\n        } else if (field == 'zhengming') {\r\n          var pics = that.zhengming;\r\n          pics.splice(index, 1);\r\n          that.zhengming = pics;\r\n        }\r\n      },\r\n      showCateSearch: function () {\r\n        this.filteredCateList = this.clist || [];\r\n        this.cateSearchKeyword = '';\r\n        this.showCateSearchPopup = true;\r\n      },\r\n      hideCateSearch: function () {\r\n        this.showCateSearchPopup = false;\r\n      },\r\n      searchCate: function () {\r\n        var that = this;\r\n        var keyword = that.cateSearchKeyword.toLowerCase();\r\n        that.filteredCateList = (that.clist || []).filter(function (item) {\r\n          return item.name.toLowerCase().indexOf(keyword) !== -1;\r\n        });\r\n      },\r\n      selectCate: function (index) {\r\n        var item = this.filteredCateList[index];\r\n        var originalIndex = this.clist.findIndex(function(cateItem) {\r\n          return cateItem.id === item.id;\r\n        });\r\n        if (originalIndex !== -1) {\r\n          this.cindex = originalIndex;\r\n        }\r\n        this.hideCateSearch();\r\n      },\r\n             // 通用费率验证方法\r\n       validateCustomRateValue: function (value) {\r\n         var that = this;\r\n         var rate = parseFloat(value);\r\n         if (!value || value === '' || value === '0') {\r\n           that.customRateError = '';\r\n           return false;\r\n         }\r\n         if (isNaN(rate)) {\r\n           that.customRateError = '费率必须为有效数字';\r\n           return false;\r\n         }\r\n         if (rate < that.registerConfig.rate_min || rate > that.registerConfig.rate_max) {\r\n           that.customRateError = '费率必须在 ' + that.registerConfig.rate_min + '% - ' + that.registerConfig.rate_max + '% 范围内';\r\n           return false;\r\n         }\r\n         that.customRateError = '';\r\n         return true;\r\n       },\r\n       // 自定义费率实时验证\r\n       validateCustomRate: function (e) {\r\n         this.validateCustomRateValue(e.detail.value);\r\n       },\r\n       // 自定义费率失去焦点验证\r\n       validateCustomRateOnBlur: function () {\r\n         this.validateCustomRateValue(this.info.custom_rate);\r\n       }\r\n    }\r\n  };\r\n  </script>\r\n  \r\n  <style>\r\n  radio{transform: scale(0.6);}\r\n  checkbox{transform: scale(0.6);}\r\n  .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n  .apply_title { background: #fff}\r\n  .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n  .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n  \r\n  .apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n  .apply_box .apply_item:last-child{ border:none}\r\n  .apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n  .apply_item input::placeholder{ color:#999999}\r\n  .apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n  .apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n  .apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n  .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n  \r\n  .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n  .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}\r\n  .layui-imgbox-close image{width:100%;height:100%}\r\n  .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n  .layui-imgbox-img>image{max-width:100%;}\r\n  .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n  .uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n  \r\n  /* 返回按钮样式 */\r\n  .back-button {\r\n    width: 90%;\r\n    background: #b60000;\r\n    color: #fff;\r\n    text-align: center;\r\n    height: 96rpx;\r\n    line-height: 96rpx;\r\n    border-radius: 50px;\r\n    margin-top: 0rpx;\r\n    margin: auto;\r\n  }\r\n  \r\n  .back-button .t1 {\r\n    font-size: 30rpx;\r\n    color: #fff;\r\n  }\r\n\r\n  /* 类目搜索样式 */\r\n  .search-picker {\r\n    text-align: right;\r\n  }\r\n\r\n  .cate-search-mask {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    z-index: 1000;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .cate-search-popup {\r\n    position: relative;\r\n    width: 80%;\r\n    max-height: 70%;\r\n    background-color: #fff;\r\n    border-radius: 20rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n    display: flex;\r\n    flex-direction: column;\r\n    animation: popup 0.3s ease;\r\n  }\r\n\r\n  @keyframes popup {\r\n    from {\r\n      transform: scale(0.8);\r\n      opacity: 0;\r\n    }\r\n    to {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .cate-search-header {\r\n    display: flex;\r\n    padding: 20rpx;\r\n    border-bottom: 1rpx solid #eee;\r\n    background-color: #fff;\r\n  }\r\n  .cate-search-input {\r\n    flex: 1;\r\n    background: #f5f5f5;\r\n    border-radius: 36rpx;\r\n    padding: 10rpx 20rpx;\r\n    margin-right: 20rpx;\r\n  }\r\n  .cate-search-input input {\r\n    height: 60rpx;\r\n    width: 100%;\r\n  }\r\n  .cate-search-close {\r\n    width: 100rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    text-align: center;\r\n    color: #666;\r\n  }\r\n  .cate-search-list {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 0 30rpx;\r\n    max-height: 600rpx;\r\n  }\r\n  .cate-search-item {\r\n    padding: 30rpx 0;\r\n    border-bottom: 1rpx solid #eee;\r\n    font-size: 30rpx;\r\n  }\r\n  .cate-no-result {\r\n    padding: 50rpx 0;\r\n    text-align: center;\r\n    color: #999;\r\n  }\r\n\r\n  /* 费率选择样式 */\r\n  .rate-tips {\r\n    margin-top: 10rpx;\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    padding-left: 10rpx;\r\n  }\r\n\r\n  .rate-tip-text {\r\n    margin-right: 20rpx;\r\n  }\r\n\r\n  .rate-error {\r\n    color: #f00;\r\n    font-size: 24rpx;\r\n  }\r\n  </style>\r\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098384\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}