{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?76e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?f28f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?a5b6", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?8f83", "uni-app:///pagesExt/cycle/product.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?8ee8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/product.vue?1e5f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "indexurl", "tabnum", "buydialogHidden", "num", "teamCount", "sysset", "shopset", "isfavorite", "btntype", "ggselected", "gui<PERSON><PERSON>", "gui<PERSON>", "ks", "gwcnum", "nodata", "product", "userinfo", "current", "pagecontent", "business", "commentcount", "commentlist", "nowtime", "teamList", "teamid", "sharetypevisible", "showposter", "posterpic", "kfurl", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "methods", "toPlanList", "console", "app", "getdata", "that", "id", "setInterval", "uni", "swiper<PERSON><PERSON>e", "getdjs", "buydialogShow", "buydialogChange", "ggchange", "tobuy", "gwcplus", "gwcminus", "gwcinput", "addfavorite", "proid", "type", "tabClick", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyMhxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACA;MACAD;MACAG;MACAC;IACA;EACA;EACAC;IAEAC;MAEA;MACAC;MACAC;IACA;IACAC;MACA;MACA;MACAC;MACAF;QAAAG;MAAA;QACAD;QACA;UACAF;UACA;QACA;QACA;QACAE;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAE;UACAF;UACAA;QACA;QACAG;UACAb;QACA;QACAU;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAV;UAAAC;QAAA;MACA;IACA;IACAa;MACA;MACAJ;IACA;IACAK;MACA;MACA;MACA;QACA;QACA;QACA;UACAL;QACA;UACA;UACA;UACA;UACA;UACAA;QACA;QACAA;MACA;IACA;IACA;IACAM,0CAEA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAzC;MACA;MACA;MACA;IACA;IACA0C;MACA;MACA;MACA;MACA;MACA;MACA;MACAX;IACA;IACA;IACAY;MACA;MACA;MACA;QACAZ;QACA;MACA;MACA;IACA;IACA;IACAa;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAf;MACAA;QAAAgB;QAAAC;MAAA;QACAjB;QACA;UACAE;QACA;QACAF;MACA;IACA;IACAkB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAnB;MACAA;MACAF;MACAA;QAAAgB;MAAA;QACAhB;QACA;UACAA;QACA;UACAE;QACA;MACA;IACA;IACAoB;MACA;MAAA;IACA;IACAC;MACAvB;MACA;IACA;IACAwB;MACA;MACAnB;QACAoB;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAvB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7dA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=3241eb02&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/product.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=template&id=3241eb02&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.product.pics.length : null\n  var m0 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m3 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m5 = _vm.isload && _vm.product.status == 1 ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.btntype == 1 ? _vm.t(\"color2\") : null\n  var m7 = _vm.isload && _vm.btntype == 2 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.btntype == 3 ? _vm.t(\"color1\") : null\n  var m9 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m10 =\n    _vm.isload && _vm.sharetypevisible && !(m9 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m11 =\n    _vm.isload && _vm.sharetypevisible && !(m9 == \"app\") && !(m10 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g1: g1,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t<view class=\"container\">\r\n\t\t<view class=\"swiper-container\">\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\">{{current+1}}/{{product.pics.length}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"collage_title flex-bt\">\r\n\t\t\t<text>\r\n\t\t\t\t<text class=\"price\">￥{{product.sell_price}}</text>\r\n\t\t\t\t<text class=\"m-price\">￥{{product.market_price}}</text>\r\n\t\t\t</text>\r\n\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\" class=\"ps_title flex-y-center\">{{product.ps_cycle_title}}</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"header\"> \r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"lef\">\r\n\t\t\t\t\t<text>{{product.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t<image src=\"/static/img/share.png\"></image>\r\n\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t<view class=\"f2\">库存:{{product.stock}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\r\n\t\t<view class=\"commentbox\" v-if=\"shopset.comment==1 && commentcount > 0\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评度 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" src=\"/static/img/arrowright.png\"/></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"comment\">\r\n\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\r\n\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"p4\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t</view>\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\t\t\r\n\r\n\t\t<view style=\"width:100%;height:70px;\"></view>\r\n\r\n\t\t<view class=\"bottombar flex-row flex-xy-center\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"cart flex-col flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"cart flex-col flex-x-center flex-y-center\" v-else open-type=\"contact\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</button>\r\n\t\t\t<view class=\"cart flex-col flex-x-center flex-y-center\" @tap=\"shareClick\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/share2.png\"/>\r\n\t\t\t\t<view class=\"t1\">分享</view> \r\n\t\t\t</view>\r\n\t\t\t<view class=\"favorite flex-col flex-x-center flex-y-center\" @tap=\"addfavorite\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/shoucang.png\"/>\r\n\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tobuy flex1\"  @tap=\"toPlanList\" :style=\"{background:t('color1')}\"  :data-url=\"'/pagesExt/cycle/planWrite?id='+product.id\"><text>购买</text></view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view :hidden=\"buydialogHidden\">\r\n\t\t\t<view class=\"buydialog-mask\">\r\n\t\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<image :src=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"guigelist[ks].pic?guigelist[ks].pic:product.pic\"></image>\r\n\t\t\t\t\t\t<!-- <text class=\"name\">{{product.name}}</text> -->\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"btntype==1\"><text class=\"t1\">￥</text>{{guigelist[ks].market_price}}</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-else><text class=\"t1\">￥</text>{{guigelist[ks].sell_price}} <text v-if=\"guigelist[ks].market_price > guigelist[ks].sell_price\" class=\"t2\">￥{{guigelist[ks].market_price}}</text></view>\r\n\t\t\t\t\t\t<view class=\"choosename\">已选规格: {{guigelist[ks].name}}</view>\r\n\t\t\t\t\t\t<view class=\"stock\">剩余{{guigelist[ks].stock}}件</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" src=\"/static/img/cart-minus.png\" @tap=\"gwcminus\"/></view>\r\n\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" src=\"/static/img/cart-plus.png\" @tap=\"gwcplus\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<block v-if=\"btntype==1\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color2')}\" @tap=\"tobuy\" data-type=\"1\">确定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"btntype==2\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" data-type=\"2\">下一步</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"btntype==3\">\r\n\t\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{background:t('color1')}\" @tap=\"tobuy\" data-type=\"3\">确 定</button>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/sharepic.png\"/>\r\n\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t<view class=\"main\">\r\n\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" src=\"/static/img/close.png\"/></view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tindexurl:app.globalData.indexurl,\r\n\t\t\ttabnum: 1,\r\n      buydialogHidden: true,\r\n      num: 1,\r\n\t\t\tteamCount:0,\r\n\t\t\tsysset:{},\r\n\t\t\tshopset:{},\r\n      isfavorite: false,\r\n      btntype: 1,\r\n      ggselected: [],\r\n\t\t\tguigedata:[],\r\n\t\t\tguigelist:[],\r\n      ks: '',\r\n      gwcnum: 1,\r\n      nodata: 0,\r\n\t\t\tproduct:{},\r\n      userinfo: [],\r\n      current: 0,\r\n      pagecontent: \"\",\r\n\t\t\tbusiness:{},\r\n\t\t\tcommentcount:0,\r\n\t\t\tcommentlist:[],\r\n      nowtime: \"\",\r\n      teamList: [],\r\n      teamid: \"\",\r\n      sharetypevisible: false,\r\n      showposter: false,\r\n      posterpic: \"\",\r\n\t\t\tkfurl:'',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t  \r\n\t\ttoPlanList:function(e){\r\n\t\t\t\r\n\t\t\tvar url=e.currentTarget.dataset.url\r\n\t\t\tconsole.log(url,'url');\r\n\t\t\tapp.goto(url);\r\n\t\t},\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiCycle/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = JSON.parse(res.product.detail);\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\tthat.ggselected = res.ggselected;\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.ks = res.ks;\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.product = res.product;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.teamCount = res.teamCount;\r\n\t\t\t\tthat.teamList = res.teamList;\r\n\t\t\t\tsetInterval(function () {\r\n\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid='+res.product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:res.product.name,pic:res.product.pic});\r\n\t\t\t});\r\n\t\t},\r\n    swiperChange: function (e) {\r\n      var that = this;\r\n      that.current = e.detail.current;\r\n    },\r\n    getdjs: function () {\r\n      var that = this;\r\n      var nowtime = that.nowtime;\r\n      for (var i in that.teamList) {\r\n        var thisteam = that.teamList[i];\r\n        var totalsec = thisteam.createtime * 1 + thisteam.teamhour * 3600 - nowtime * 1;\r\n        if (totalsec <= 0) {\r\n          that.teamList[i].djs = '00时00分00秒';\r\n        } else {\r\n          var houer = Math.floor(totalsec / 3600);\r\n          var min = Math.floor((totalsec - houer * 3600) / 60);\r\n          var sec = totalsec - houer * 3600 - min * 60;\r\n          var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n          that.teamList[i].djs = djs;\r\n        }\r\n        that.teamList = that.teamList\r\n      }\r\n    },\r\n    //加入购物车\r\n    buydialogShow: function (e) {\r\n\t\t\r\n    },\r\n    buydialogChange: function (e) {\r\n      this.buydialogHidden = !this.buydialogHidden;\r\n    },\r\n    //选择规格\r\n    ggchange: function (e) {\r\n      var idx = e.currentTarget.dataset.idx;\r\n      var itemk = e.currentTarget.dataset.itemk;\r\n      var ggselected = this.ggselected;\r\n      ggselected[itemk] = idx;\r\n      var ks = ggselected.join(',');\r\n      this.ggselected = ggselected;\r\n      this.ks = ks;\r\n    },\r\n    tobuy: function (e) {\r\n      var type = e.currentTarget.dataset.type;\r\n      var that = this;\r\n      var ks = that.ks;\r\n      var proid = that.product.id;\r\n      var ggid = that.guigelist[ks].id;\r\n      var num = that.gwcnum;\r\n      app.goto('buy?proid=' + proid + '&ggid=' + ggid + '&num=' + num + '&buytype=' + type + (type == 3 ? '&teamid=' + that.teamid : ''));\r\n    },\r\n    //加\r\n    gwcplus: function (e) {\r\n      var gwcnum = this.gwcnum + 1;\r\n      var ggselected = this.ks;\r\n      if (gwcnum > this.guigelist[ggselected].stock) {\r\n        app.error('库存不足');\r\n        return;\r\n      }\r\n      this.gwcnum = this.gwcnum + 1\r\n    },\r\n    //减\r\n    gwcminus: function (e) {\r\n      var gwcnum = this.gwcnum - 1;\r\n      var ggselected = this.ks;\r\n      if (gwcnum <= 0) {\r\n        return;\r\n      }\r\n      this.gwcnum = this.gwcnum - 1\r\n    },\r\n    //输入\r\n    gwcinput: function (e) {\r\n      var ggselected = this.ks;\r\n      var gwcnum = parseInt(e.detail.value);\r\n      if (gwcnum < 1) return 1;\r\n\r\n      if (gwcnum > this.guigelist[ggselected].stock) {\r\n        return this.guigelist[ggselected].stock;\r\n      }\r\n      this.gwcnum = gwcnum;\r\n    },\r\n    //收藏操作\r\n    addfavorite: function () {\r\n      var that = this;\r\n      var proid = that.product.id;\r\n\t\t\tapp.showLoading('收藏中');\r\n      app.post('ApiCycle/addfavorite', {proid: proid,type: 'cycle'}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          that.isfavorite = !that.isfavorite\r\n        }\r\n        app.success(data.msg);\r\n      });\r\n    },\r\n    tabClick: function (e) {\r\n      this.tabnum = e.currentTarget.dataset.num;\r\n    },\r\n    shareClick: function () {\r\n      this.sharetypevisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.sharetypevisible = false;\r\n    },\r\n    showPoster: function () {\r\n      var that = this;\r\n      that.showposter = true;\r\n      that.sharetypevisible = false;\r\n      app.showLoading('努力生成中');\r\n\t\t\tapp.post('ApiCycle/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.alert(data.msg);\r\n        } else {\r\n          that.posterpic = data.poster;\r\n        }\r\n      });\r\n    },\r\n    posterDialogClose: function () {\r\n      this.showposter = false;;\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/collage/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/collage/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style>\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.header {width: 100%;padding: 0 3%;background: #fff;}\r\n.header .title {padding: 10px 0px 0px 0px;line-height:44rpx;font-size:32rpx;display:flex;}\r\n.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}\r\n.header .title .lef .t2{ font-size:26rpx;color:#999;padding-top:10rpx;font-weight:normal}\r\n.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:0 solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center}\r\n.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}\r\n\r\n.header .price{height: 86rpx;overflow: hidden;line-height: 86rpx;border-top: 1px solid #eee;}\r\n.header .price .t1 .x1{ color: #e94745; font-size: 34rpx;}\r\n.header .price .t1 .x2{ color: #939393; margin-left: 10rpx; text-decoration: line-through;font-size:24rpx}\r\n.header .price .t2{color: #aaa; font-size: 24rpx;}\r\n.header .fuwupoint{width:100%;font-size:24rpx;color:#999;display:flex;flex-wrap:wrap;border-top:1px solid #eee;padding:10rpx 0}\r\n.header .fuwupoint .t{ padding:4rpx 20rpx 4rpx 0}\r\n.header .fuwupoint .t:before{content: \"\";\tdisplay: inline-block;\tvertical-align: middle;\tmargin-top: -4rpx;\tmargin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\tbackground-size: 24rpx auto; }\r\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 80rpx; line-height: 80rpx; padding: 0 3%; color: #505050; }\r\n.choose .f2{ width: 40rpx; height: 40rpx;}\r\n\r\n.teamlist{ width:100%;background:#fff;padding:10rpx 20rpx;font-size:26rpx;margin-top:20rpx;display:flex;flex-direction:column}\r\n.teamlist .label{ width:100%;color:#222222;font-weight:bold;padding:10rpx 0;border-bottom:1px solid #eee}\r\n.teamlist .content{ width:100%;max-height:300rpx;overflow:scroll}\r\n.teamlist .item{width:100%;display:flex;align-items:center;padding:12rpx 3px;border-bottom:0px solid #f5f5f5}\r\n.teamlist .item .f1{width:300rpx;overflow:hidden;flex:auto;display:flex;align-items:center}\r\n.teamlist .item .f1 image{width:80rpx;height:80rpx;}\r\n.teamlist .item .f1 .t1{padding-left:6rpx;font-size:30rpx;color:#333}\r\n.teamlist .item .f2{ text-align:right;margin:0 8rpx}\r\n.teamlist .item .f2 .t1{font-size:24rpx;color:#333}\r\n.teamlist .item .f2 .t2{font-size:22rpx;color:#999}\r\n.teamlist .item .f3{ background: linear-gradient(90deg, #FF3143 0%, #FE6748 100%);color:#fff;border-radius:26rpx;padding:0 20rpx;height:50rpx;border:0;text-align:right;font-size:26rpx;display:flex;align-items:center}\r\n.teamlist .item .f3:after{border:0}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.sales_stock{  \r\n\tdisplay: flex;\r\n    justify-content: space-between;\r\n    height: 60rpx;\r\n    line-height: 60rpx;\r\n    font-size: 24rpx;\r\n    color: #777777;\r\n\t}\r\n\r\n.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;}\r\n.bottombar .favorite{width: 15%;color:#707070;font-size:26rpx}\r\n.bottombar .favorite .fa{ font-size:40rpx;height:50rpx;line-height:50rpx}\r\n.bottombar .favorite .img{ width:50rpx;height:50rpx}\r\n.bottombar .favorite .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .cart{width: 15%;font-size:26rpx;color:#707070}\r\n.bottombar .cart .img{ width:50rpx;height:50rpx}\r\n.bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .tocart{ width: 30%; height: 100rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.bottombar .tobuy{font-weight: bold;height: 80rpx;color: #fff; background: #FC635F; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin: 15rpx 30rpx 15rpx 15rpx;border-radius: 100rpx;}\r\n\r\n\r\n\r\n.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\r\n.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\r\n.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\r\n.buydialog .close .image{ width: 30rpx; height:30rpx; }\r\n.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n.buydialog .title .price .t1{ font-size:26rpx}\r\n.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa}\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\r\n.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\r\n.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\r\n.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.buydialog .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .img{width:24rpx;height:24rpx}\r\n.buydialog .addnum .input{flex:1;width:70rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx}\r\n.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}\r\n.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none; font-size:28rpx;font-weight:bold}\r\n.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;}\r\n.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\r\n\r\n.collage_title{width:100%;height:110rpx;display:flex;align-items:center;padding:0 30rpx;background-color: #fff;}\r\n.ps_title{height: 35rpx;background: #FFDED9;border-radius: 4rpx;padding: 0 10rpx;font-size: 20rpx;font-family: PingFang SC;color: #FF3143;margin-left: 20rpx;}\r\n.m-price{margin-left: 10rpx;font-size: 24rpx;color: #aaa;text-decoration: line-through;}\r\n.collage_title .f1{flex:1;display:flex;flex-direction:column;}\r\n.collage_title .f1 .t1{display:flex;align-items:center;height:60rpx;line-height:60rpx}\r\n.collage_title .price {font-size:50rpx;color:#FF9900;font-weight: 700;}\r\n.collage_title .f1 .t1 .x1{font-size:28rpx;color:#FF9900}\r\n.collage_title .f1 .t1 .x2{font-size:48rpx;color:#fff;padding-right:20rpx}\r\n.collage_title .f1 .t1 .x3{font-size:24rpx;font-weight:bold;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:20rpx;background:#fff;color:#ED533A}\r\n.collage_title .f1 .t2{color:rgba(255,255,255,0.6);font-size:20rpx;}\r\n.collage_title .f2{color:#fff;font-size:28rpx;}\r\n\r\n.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}\r\n.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}\r\n.sharetypecontent button::after{border:0}\r\n.sharetypecontent .f1 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}\r\n.sharetypecontent .f2 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}\r\n\r\n.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}\r\n.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}\r\n.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}\r\n.posterDialog .close .img{ width:40rpx;height:40rpx;}\r\n.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\r\n.posterDialog .content .img{width:540rpx;height:960rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098344\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}