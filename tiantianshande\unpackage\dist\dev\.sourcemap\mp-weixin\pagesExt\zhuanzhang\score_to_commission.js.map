{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?41c0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?5135", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?f7ac", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?851e", "uni-app:///pagesExt/zhuanzhang/score_to_commission.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?6d43", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zhuanzhang/score_to_commission.vue?9269"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "textset", "canrecharge", "userinfo", "config", "status", "rate", "beishu", "min", "max", "need_paypwd", "fee_rate", "shuoming", "score", "paypwd", "calculatedCommission", "calculatedFee", "caninput", "onLoad", "onPullDownRefresh", "methods", "getdata", "app", "setTimeout", "uni", "that", "title", "scoreinput", "paypwdinput", "calculateCommission", "convert", "content", "success", "doConvert", "postData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AACuE;AACL;AACa;;;AAG/E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAwwB,CAAgB,mxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgF5xB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACAC;MACAA;QACAA;QACA;UACAA;UACAC;YACAC;UACA;UACA;QACA;QACAC;QACAA;QACAD;UACAE;QACA;QAEAD;UACApB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAc;QACAA;QACAA;QAEA;UACAH;QACA;QAEAG;MACA;IACA;IAEAE;MACA;MACA;QACAL;QACA;MACA;MAEA;MACA;IACA;IAEAM;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;MACA;MAEA;QACA;MACA;MAEA;MAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;;MAEA;MACA;MAEAN;QACAE;QACAK;QACAC;UACA;YACAP;UACA;QACA;MACA;IACA;IAEAQ;MACA;MACAR;MAEA;QAAAZ;MAAA;MACA;QACAqB;MACA;MAEAZ;QACAG;QACA;UACAH;UACA;QACA;UACAA;UACA;UACAG;UACAA;UACAA;UACAA;UACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrQA;AAAA;AAAA;AAAA;AAAwlC,CAAgB,okCAAG,EAAC,C;;;;;;;;;;;ACA5mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zhuanzhang/score_to_commission.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zhuanzhang/score_to_commission.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./score_to_commission.vue?vue&type=template&id=0c0e239f&\"\nvar renderjs\nimport script from \"./score_to_commission.vue?vue&type=script&lang=js&\"\nexport * from \"./score_to_commission.vue?vue&type=script&lang=js&\"\nimport style0 from \"./score_to_commission.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zhuanzhang/score_to_commission.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./score_to_commission.vue?vue&type=template&id=0c0e239f&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var g0 =\n    _vm.isload && _vm.config.fee_rate > 0\n      ? (_vm.config.fee_rate * 100).toFixed(2)\n      : null\n  var g1 =\n    _vm.isload && _vm.score > 0 ? _vm.calculatedCommission.toFixed(6) : null\n  var g2 =\n    _vm.isload && _vm.score > 0 && _vm.calculatedFee > 0\n      ? _vm.calculatedFee.toFixed(6)\n      : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./score_to_commission.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./score_to_commission.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t<view class=\"f1\">积分转佣金</view>\r\n\t\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\">积分：</text>{{userinfo.score || 0}}</view>\r\n\t\t\t\t<view class=\"f3\" @tap=\"goto\" data-url=\"/pages/money/moneylog?st=6\"><text>转换记录</text>\r\n\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"font-size:20rpx;\"></text></view>\r\n\t\t\t\t<view class=\"f1\" style=\"font-size:20rpx;margin-top: 20px;\">转换比例</view>\r\n\t\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\"></text>{{config.rate || 100}} ：1</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"content2\">\r\n\t\t\t\t<view class=\"balance-info\">\r\n\t\t\t\t\t<view class=\"balance-item\">\r\n\t\t\t\t\t\t<text class=\"balance-label\">当前积分</text>\r\n\t\t\t\t\t\t<text class=\"balance-value\">{{userinfo.score || 0}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"balance-item\">\r\n\t\t\t\t\t\t<text class=\"balance-label\">当前佣金</text>\r\n\t\t\t\t\t\t<text class=\"balance-value\">{{userinfo.commission || 0}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"rules-section\">\r\n\t\t\t\t\t<view class=\"rules-title\">转换规则</view>\r\n\t\t\t\t\t<view class=\"rules-content\">\r\n\t\t\t\t\t\t<view class=\"rule-item\">• 兑换比例：{{config.rate || 100}}积分 = 1佣金</view>\r\n\t\t\t\t\t\t<view class=\"rule-item\" v-if=\"config.beishu > 0\">• 转换倍数：必须是{{config.beishu}}的倍数</view>\r\n\t\t\t\t\t\t<view class=\"rule-item\">• 转换范围：{{config.min || 0}} - {{config.max > 0 ? config.max : '不限制'}}积分</view>\r\n\t\t\t\t\t\t<view class=\"rule-item\" v-if=\"config.fee_rate > 0\">• 手续费：{{(config.fee_rate * 100).toFixed(2)}}%</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<block v-if=\"caninput==1\">\r\n\t\t\t\t\t<view class=\"item3\">\r\n\t\t\t\t\t\t<view class=\"f2\"><input type=\"digit\" name=\"score\" :value=\"score\" placeholder=\"请输入转换积分数量\"\r\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" @input=\"scoreinput\"\r\n\t\t\t\t\t\t\t\tstyle=\"font-size:60rpx\" /></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"preview-section\" v-if=\"score > 0\">\r\n\t\t\t\t\t<view class=\"preview-item\">\r\n\t\t\t\t\t\t<text class=\"preview-label\">预计获得佣金：</text>\r\n\t\t\t\t\t\t<text class=\"preview-value\">{{calculatedCommission.toFixed(6)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"preview-item\" v-if=\"calculatedFee > 0\">\r\n\t\t\t\t\t\t<text class=\"preview-label\">手续费：</text>\r\n\t\t\t\t\t\t<text class=\"preview-value fee\">{{calculatedFee.toFixed(6)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<block v-if=\"caninput==1 && config.need_paypwd\">\r\n\t\t\t\t\t<view class=\"item3\">\r\n\t\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"paypwd\" :value=\"paypwd\" placeholder=\"请输入支付密码\"\r\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" @input=\"paypwdinput\"\r\n\t\t\t\t\t\t\t\tstyle=\"font-size:60rpx\" maxlength=\"6\" /></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view style=\"margin-top:40rpx;padding:0 30rpx;line-height:42rpx;\" v-if=\"shuoming\">\r\n\t\t\t\t\t<parse :content=\"shuoming\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t<view class=\"btn\" @tap=\"convert\" :style=\"{background:t('color1')}\">确认转换</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view style=\"width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center\" @tap=\"goto\" data-url=\"/pages/my/paypwd\" v-if=\"config.need_paypwd && !userinfo.has_paypwd\">设置支付密码<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\ttextset: {},\r\n\t\t\t\tcanrecharge: 0,\r\n\t\t\t\tuserinfo: {},\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\trate: 100,\r\n\t\t\t\t\tbeishu: 0,\r\n\t\t\t\t\tmin: 0,\r\n\t\t\t\t\tmax: 0,\r\n\t\t\t\t\tneed_paypwd: false,\r\n\t\t\t\t\tfee_rate: 0\r\n\t\t\t\t},\r\n\t\t\t\tshuoming: '',\r\n\t\t\t\tscore: '',\r\n\t\t\t\tpaypwd: '',\r\n\t\t\t\tcalculatedCommission: 0,\r\n\t\t\t\tcalculatedFee: 0,\r\n\t\t\t\tcaninput: 1\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\t\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.loading = true;\r\n\t\t\t\tapp.get('ApiMoney/scoreToCommission', {}, function(res) {\r\n\t\t\t\t\tapp.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg || '功能未开启');\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '积分转佣金'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.config = {\r\n\t\t\t\t\t\tstatus: res.status,\r\n\t\t\t\t\t\trate: res.rate || 100,\r\n\t\t\t\t\t\tbeishu: res.beishu || 0,\r\n\t\t\t\t\t\tmin: res.min || 0,\r\n\t\t\t\t\t\tmax: res.max || 0,\r\n\t\t\t\t\t\tneed_paypwd: res.need_paypwd || false,\r\n\t\t\t\t\t\tfee_rate: res.fee_rate || 0\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.userinfo = res.userinfo || {};\r\n\t\t\t\t\tthat.shuoming = res.shuoming || '';\r\n\t\t\t\t\tthat.caninput = res.caninput || 1;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (that.config.need_paypwd && !res.has_paypwd) {\r\n\t\t\t\t\t\tapp.error('请先设置支付密码');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tscoreinput: function(e) {\r\n\t\t\t\tvar score = e.detail.value;\r\n\t\t\t\tif (parseFloat(score) < 0) {\r\n\t\t\t\t\tapp.error('必须大于0');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.score = score;\r\n\t\t\t\tthis.calculateCommission();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tpaypwdinput: function(e) {\r\n\t\t\t\tvar paypwd = e.detail.value;\r\n\t\t\t\tthis.paypwd = paypwd.trim();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tcalculateCommission: function() {\r\n\t\t\t\tconst score = parseInt(this.score) || 0;\r\n\t\t\t\tif (score <= 0) {\r\n\t\t\t\t\tthis.calculatedCommission = 0;\r\n\t\t\t\t\tthis.calculatedFee = 0;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst commission = score / this.config.rate;\r\n\t\t\t\tconst fee = commission * this.config.fee_rate;\r\n\t\t\t\t\r\n\t\t\t\tthis.calculatedCommission = commission - fee;\r\n\t\t\t\tthis.calculatedFee = fee;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tconvert: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar score = that.score;\r\n\t\t\t\tvar paypwd = that.paypwd;\r\n\t\t\t\t\r\n\t\t\t\tif (!score) {\r\n\t\t\t\t\treturn app.error('请输入转换积分数量');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst scoreNum = parseInt(score);\r\n\t\t\t\t\r\n\t\t\t\tif (scoreNum < that.config.min) {\r\n\t\t\t\t\treturn app.error('转换积分不能少于' + that.config.min);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (that.config.max > 0 && scoreNum > that.config.max) {\r\n\t\t\t\t\treturn app.error('转换积分不能超过' + that.config.max);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (that.config.beishu > 0 && scoreNum % that.config.beishu !== 0) {\r\n\t\t\t\t\treturn app.error('转换积分必须是' + that.config.beishu + '的倍数');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (scoreNum > parseFloat(that.userinfo.score || 0)) {\r\n\t\t\t\t\treturn app.error('积分余额不足');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (that.config.need_paypwd && !paypwd) {\r\n\t\t\t\t\treturn app.error('请输入支付密码');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 确认转换\r\n\t\t\t\tconst confirmMsg = `确认转换${scoreNum}积分为${that.calculatedCommission.toFixed(6)}佣金吗？${that.calculatedFee > 0 ? '\\n手续费：' + that.calculatedFee.toFixed(6) + '佣金' : ''}`;\r\n\t\t\t\t\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认转换',\r\n\t\t\t\t\tcontent: confirmMsg,\r\n\t\t\t\t\tsuccess: function(modalRes) {\r\n\t\t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t\tthat.doConvert(scoreNum, paypwd);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tdoConvert: function(score, paypwd) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\t\r\n\t\t\t\tvar postData = { score: score };\r\n\t\t\t\tif (that.config.need_paypwd) {\r\n\t\t\t\t\tpostData.paypwd = paypwd;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiMoney/scoreToCommission', postData, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error('转换成功');\r\n\t\t\t\t\t\t// 重置表单\r\n\t\t\t\t\t\tthat.score = '';\r\n\t\t\t\t\t\tthat.paypwd = '';\r\n\t\t\t\t\t\tthat.calculatedCommission = 0;\r\n\t\t\t\t\t\tthat.calculatedFee = 0;\r\n\t\t\t\t\t\t// 刷新数据\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column\r\n\t}\r\n\r\n\t.mymoney {\r\n\t\twidth: 94%;\r\n\t\tmargin: 20rpx 3%;\r\n\t\tborder-radius: 10rpx 56rpx 10rpx 10rpx;\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding: 70rpx 0\r\n\t}\r\n\r\n\t.mymoney .f1 {\r\n\t\tmargin: 0 0 0 60rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.mymoney .f2 {\r\n\t\tmargin: 20rpx 0 0 60rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 64rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.mymoney .f3 {\r\n\t\theight: 56rpx;\r\n\t\tpadding: 0 10rpx 0 20rpx;\r\n\t\tborder-radius: 28rpx 0px 0px 28rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tposition: absolute;\r\n\t\ttop: 94rpx;\r\n\t\tright: 0\r\n\t}\r\n\r\n\t.content2 {\r\n\t\twidth: 94%;\r\n\t\tmargin: 10rpx 3%;\r\n\t\tborder-radius: 10rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.balance-info {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t}\r\n\r\n\t.balance-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.balance-label {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.balance-value {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rules-section {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t}\r\n\r\n\t.rules-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rules-content {\r\n\t\tbackground: #f8f9fa;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.rule-item {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.6;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.rule-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.preview-section {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground: #f0f9ff;\r\n\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t}\r\n\r\n\t.preview-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.preview-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.preview-label {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.preview-value {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #007AFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.preview-value.fee {\r\n\t\tcolor: #ff6b6b;\r\n\t}\r\n\r\n\t.content2 .item3 {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t}\r\n\r\n\t.content2 .item3 .f2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 60rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: bold;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.content2 .item3 .f2 input {\r\n\t\theight: 120rpx;\r\n\t\tline-height: 120rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.op {\r\n\t\twidth: 96%;\r\n\t\tmargin: 20rpx 2%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 40rpx\r\n\t}\r\n\r\n\t.op .btn {\r\n\t\tflex: 1;\r\n\t\theight: 100rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tbackground: #07C160;\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./score_to_commission.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./score_to_commission.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098826\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}