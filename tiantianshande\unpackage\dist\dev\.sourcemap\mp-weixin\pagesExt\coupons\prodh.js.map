{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?0788", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?1b24", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?4a42", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?a9d2", "uni-app:///pagesExt/coupons/prodh.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?a233", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/prodh.vue?e53e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "test", "havetongcheng", "address", "usescore", "scoredk_money", "totalprice", "bid", "nowbid", "needaddress", "linkman", "tel", "userinfo", "pstimeDialogShow", "pstimeIndex", "manjian_money", "cxid", "latitude", "longitude", "allbuydata", "alltotalprice", "needChoose", "chooseIndex", "chooseInfo", "onLoad", "onPullDownRefresh", "methods", "getdata", "app", "dhcode", "cardno", "that", "storedata", "chooseClick", "scoredk", "inputLinkman", "inputTel", "inputfield", "<PERSON><PERSON><PERSON><PERSON>", "calculatePrice", "freight_price", "coupon_money", "allfreight_price", "changeFreight", "chooseFreight", "console", "itemlist", "uni", "itemList", "success", "choosePstime", "pstimeRadioChange", "hidePstimeDialog", "choosestore", "topay", "formdata", "newformdata", "proInfo", "buydata", "prodata", "cuxiaoid", "couponrid", "freight_id", "freight_time", "storeid", "frompage", "addressid", "handleClickMask", "openLocation", "name", "scale", "editorChooseImage", "editorFormdata", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6K9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QAAAC;QAAAC;MAAA;QACA;UACA;YACAF;cACA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;UACA;QACA;QACAG;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAH;YACA;YACA;YACAG;YACAA;YACA;YACA;cACA;cACA;gBACA;kBACA;kBACA;oBACA;sBACA;wBACA;wBACAC;sBACA;oBACA;oBACAA;sBACA;oBACA;oBACA;sBACA;wBACAA;sBACA;oBACA;oBACAb;kBACA;gBACA;cACA;YACA;YACAY;UACA;QACA;QAEA;UACAA;QACA;MACA;IACA;IACAE;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAlB;MACA;IACA;IACA;IACAmB;MACAV;IACA;IACA;IACAW;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAGA;UACA9B;QACA;QACA;UACA+B;UACAC;QACA;QACA;QACA;;QAEAnC;QACAa;QACAA;QACAC;QACAsB;MACA;MACAX;MAEA;MACAX;MACAW;MACAA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;MACAxB;MACAY;MACAA;MACAA;IACA;IACAa;MACA;MACA;MACA;MACAC;MACAA;MACA;MACA;MAEA;QACAC;MACA;MAEAC;QACAC;QACAC;UACA;YACA9B;YACAY;YACAA;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;MACA;MACA;QACAlB;QACA;MACA;MACAG;MACAA;MACAA;IACA;IACAoB;MACA;MACA;MACA;MACAN;MACA;MACA;MACA;MACA;MACA;MACA;MACA1B;MACAA;MACAY;MACAA;IACA;IACAqB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAlC;MACA;IACA;IACA;IACAmC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA1B;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;UACA;UACA;YACAA;YAAA;UACA;UACA;YACA2B;UACA;UACAC;QACA;QAEA;QACA;UACA;UACAC;QACA;QACAC;UACAnD;UACAoD;UACAC;UACAC;UACAC;UACAC;UACAC;UACAT;QACA;MACA;MACA3B;MACAA;QAAAC;QAAAC;QAAAmC;QAAAP;QAAAQ;QAAAxD;QAAAC;QAAAP;MAAA;QACAwB;QACA;UACA;UACAA;UACA;QACA;UACAA;YACAA;UACA;QACA;MACA;IACA;IACAuC,6CACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACAvB;MACA;MACA;MACA;MACAE;QACA9B;QACAC;QACAmD;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA3C;QACA4C;QACA3B;QACAd;QACAA;MACA;IACA;IACA0C;MACA;MACA;MACA;MACA;MACA;MACA;MACAD;MACA3B;MACAd;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxjBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/coupons/prodh.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/coupons/prodh.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prodh.vue?vue&type=template&id=46fa8e2b&\"\nvar renderjs\nimport script from \"./prodh.vue?vue&type=script&lang=js&\"\nexport * from \"./prodh.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prodh.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/coupons/prodh.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prodh.vue?vue&type=template&id=46fa8e2b&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l3 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var l0 = _vm.__map(buydata.prodata, function (item, index2) {\n          var $orig = _vm.__get_orig(item)\n          var m0 =\n            _vm.needChoose && _vm.chooseIndex === index2\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n        var l1 = _vm.__map(buydata.freightList, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = buydata.freightkey == idx2 ? _vm.t(\"color1\") : null\n          var m2 = buydata.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n          }\n        })\n        var g0 =\n          buydata.freightList[buydata.freightkey].minpriceset == 1 &&\n          buydata.freightList[buydata.freightkey].minprice > 0 &&\n          buydata.freightList[buydata.freightkey].minprice >\n            buydata.product_price\n            ? (\n                buydata.freightList[buydata.freightkey].minprice -\n                buydata.product_price\n              ).toFixed(2)\n            : null\n        var l2 =\n          buydata.freightList[buydata.freightkey].pstype == 1\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m3 =\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m3: m3,\n                  }\n                }\n              )\n            : null\n        return {\n          $orig: $orig,\n          l0: l0,\n          l1: l1,\n          g0: g0,\n          l2: l2,\n        }\n      })\n    : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l4 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.allbuydata[_vm.nowbid].freightList[\n            _vm.allbuydata[_vm.nowbid].freightkey\n          ].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m6 =\n              _vm.allbuydata[_vm.nowbid].freight_time == item.value\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m6: m6,\n            }\n          }\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l3: l3,\n        m4: m4,\n        m5: m5,\n        l4: l4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prodh.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prodh.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\r\n\t\t\t\t\t<view class=\"f1\"><image class=\"img\" src=\"/static/img/address.png\"/></view>\r\n\t\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\r\n\t\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\r\n\t\t\t\t\t<view class=\"btitle\"><image class=\"img\" src=\"/static/img/ico-shop.png\"/>{{buydata.business.name}}</view>\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\" class=\"item flex-y-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.product.id\"><image :src=\"item.product.pic\"></image></view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1 flex-y-center\" @click=\"chooseClick(item,index2)\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text><text style=\"padding-left:20rpx\"> × {{item.num}}</text></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"needChoose\" class=\"radio\" :style=\"chooseIndex===index2 ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"freight\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">配送方式</view>\r\n\t\t\t\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, idx2) in buydata.freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"freight-li\" :style=\"buydata.freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeFreight\" :data-bid=\"buydata.bid\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].minpriceset==1 && buydata.freightList[buydata.freightkey].minprice > 0 && buydata.freightList[buydata.freightkey].minprice > buydata.product_price\">满{{buydata.freightList[buydata.freightkey].minprice}}元起送，还差{{(buydata.freightList[buydata.freightkey].minprice - buydata.product_price).toFixed(2)}}元</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.freightList[buydata.freightkey].pstimeset==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{buydata.freightList[buydata.freightkey].pstype==1?'取货':'配送'}}时间</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\" :data-bid=\"buydata.bid\">{{buydata.pstimetext==''?'请选择时间':buydata.pstimetext}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==1\">\r\n\t\t\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openLocation\" :data-bid=\"buydata.bid\" :data-freightkey=\"buydata.freightkey\" :data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text class=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">商品金额</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{needChoose ? (chooseIndex === '' ? buydata.prodata[0].price : buydata.prodata[chooseIndex ].price) : buydata.product_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in buydata.freightList[buydata.freightkey].formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+buydata.bid+'_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+buydata.bid+'_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+buydata.bid+'_'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+buydata.bid+'_'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0\"> {{item.val2[buydata.editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"buydata.editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"buydata.editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"buydata.editorFormdata[idx]\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-bid=\"buydata.bid\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view style=\"width: 100%; height:110rpx;\"></view>\r\n\t\t\t\t<view class=\"footer flex\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥0.00</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定兑换</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tclass=\"popup__title-text\">请选择{{allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送'}}时间</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"hidePstimeDialog\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"pstime-item\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstimeArr\"\r\n\t\t\t\t\t\t\t:key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"allbuydata[nowbid].freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt:{},\r\n      loading:false,\r\n      isload: false,\r\n      menuindex:-1,\r\n\r\n      pre_url:app.globalData.pre_url,\r\n\t  test:'test',\r\n      havetongcheng:0,\r\n      address: [],\r\n      usescore: 0,\r\n      scoredk_money: 0,\r\n      totalprice: '0.00',\r\n      bid: 0,\r\n      nowbid: 0,\r\n      needaddress: 1,\r\n      linkman: '',\r\n      tel: '',\r\n      userinfo:{},\r\n      pstimeDialogShow: false,\r\n      pstimeIndex: -1,\r\n      manjian_money: 0,\r\n      cxid: 0,\r\n      latitude: \"\",\r\n      longitude: \"\",\r\n      allbuydata: \"\",\r\n      alltotalprice: \"\",\r\n\t  needChoose: false,\r\n\t  chooseIndex: '',\r\n\t  chooseInfo: ''\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.getdata();\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      app.get('ApiLipin2/prodh', {dhcode: that.opt.dhcode,cardno:that.opt.cardno}, function (res) {\r\n        if (res.status == 0) {\r\n          if (res.msg) {\r\n            app.alert(res.msg, function () {\r\n              if (res.url) {\r\n                app.goto(res.url);\r\n              } else {\r\n                app.goback();\r\n              }\r\n            });\r\n          } else if (res.url) {\r\n            app.goto(res.url);\r\n          } else {\r\n            app.alert('您没有权限购买该商品');\r\n          }\r\n          return;\r\n        }\r\n        that.havetongcheng = res.havetongcheng;\r\n        that.address = res.address;\r\n        that.linkman = res.linkman;\r\n        that.tel = res.tel;\r\n        that.userinfo = res.userinfo;\r\n        that.allbuydata = res.allbuydata;\r\n        that.needLocation = res.needLocation;\r\n        that.calculatePrice();\r\n        that.loaded();\r\n\r\n        if (res.needLocation == 1) {\r\n          app.getLocation(function (res) {\r\n            var latitude = res.latitude;\r\n            var longitude = res.longitude;\r\n            that.latitude = latitude;\r\n            that.longitude = longitude;\r\n            var allbuydata = that.allbuydata;\r\n            for (var i in allbuydata) {\r\n              var freightList = allbuydata[i].freightList;\r\n              for (var j in freightList) {\r\n                if (freightList[j].pstype == 1) {\r\n                  var storedata = freightList[j].storedata;\r\n                  if (storedata) {\r\n                    for (var x in storedata) {\r\n                      if (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\r\n                        var juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);\r\n                        storedata[x].juli = juli;\r\n                      }\r\n                    }\r\n                    storedata.sort(function (a, b) {\r\n                      return a[\"juli\"] - b[\"juli\"];\r\n                    });\r\n                    for (var x in storedata) {\r\n                      if (storedata[x].juli) {\r\n                        storedata[x].juli = storedata[x].juli + '千米';\r\n                      }\r\n                    }\r\n                    allbuydata[i].freightList[j].storedata = storedata;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            that.allbuydata = allbuydata;\r\n          });\r\n        }\r\n\t\t\r\n\t\tif(res.hdinfo.num_type==1){\r\n\t\t\tthat.needChoose = true;\r\n\t\t}\r\n      });\r\n    },\r\n\tchooseClick(item,index){\r\n\t\tthis.chooseIndex = index;\r\n\t\tthis.chooseInfo = item;\r\n\t},\r\n    //积分抵扣\r\n    scoredk: function (e) {\r\n      var usescore = e.detail.value[0];\r\n      if (!usescore) usescore = 0;\r\n      this.usescore = usescore;\r\n      this.calculatePrice();\r\n    },\r\n    inputLinkman: function (e) {\r\n      this.linkman = e.detail.value;\r\n    },\r\n    inputTel: function (e) {\r\n      this.tel = e.detail.value;\r\n    },\r\n    inputfield: function (e) {\r\n      var that = this;\r\n      var allbuydata = that.allbuydata;\r\n      var bid = e.currentTarget.dataset.bid;\r\n      var field = e.currentTarget.dataset.field;\r\n      allbuydata[bid][field] = e.detail.value;\r\n      this.allbuydata = allbuydata;\r\n    },\r\n    //选择收货地址\r\n    chooseAddress: function () {\r\n      app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\r\n    },\r\n    //计算价格\r\n    calculatePrice: function () {\r\n      var that = this;\r\n      var address = that.address;\r\n      var allbuydata = that.allbuydata;\r\n      var alltotalprice = 0;\r\n      var allfreight_price = 0;\r\n      var needaddress = 0;\r\n      for (var k in allbuydata) {\r\n        var product_price = parseFloat(allbuydata[k].product_price);\r\n        // var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣\r\n        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动\r\n        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣\r\n        // var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动\r\n        //算运费\r\n        var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];\r\n        var freight_price = freightdata ? freightdata['freight_price'] : 0;\r\n\t\t\r\n\t\t\r\n        if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\r\n          needaddress = 1;\r\n        }\r\n        if (allbuydata[k].coupontype == 4) {\r\n          freight_price = 0;\r\n          coupon_money = 0;\r\n        }\r\n        var totalprice = product_price;\r\n        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\r\n\r\n        totalprice = totalprice + freight_price;\r\n        allbuydata[k].freight_price = freight_price.toFixed(2);\r\n        allbuydata[k].totalprice = totalprice.toFixed(2);\r\n        alltotalprice += totalprice;\r\n        allfreight_price += freight_price;\r\n      }\r\n      that.needaddress = needaddress;\r\n\r\n      if (alltotalprice < 0) alltotalprice = 0;\r\n      alltotalprice = alltotalprice.toFixed(2);\r\n      that.alltotalprice = alltotalprice;\r\n      that.allbuydata = allbuydata;\r\n    },\r\n    changeFreight: function (e) {\r\n      var that = this;\r\n      var allbuydata = that.allbuydata;\r\n      var bid = e.currentTarget.dataset.bid;\r\n      var index = e.currentTarget.dataset.index;\r\n      var freightList = allbuydata[bid].freightList;\r\n      allbuydata[bid].freightkey = index;\r\n      that.allbuydata = allbuydata;\r\n      that.calculatePrice();\r\n\t\t\tthat.allbuydata[bid].editorFormdata = [];\r\n    },\r\n    chooseFreight: function (e) {\r\n      var that = this;\r\n      var allbuydata = that.allbuydata;\r\n      var bid = e.currentTarget.dataset.bid;\r\n      console.log(bid);\r\n      console.log(allbuydata);\r\n      var freightList = allbuydata[bid].freightList;\r\n      var itemlist = [];\r\n\r\n      for (var i = 0; i < freightList.length; i++) {\r\n        itemlist.push(freightList[i].name);\r\n      }\r\n\r\n      uni.showActionSheet({\r\n        itemList: itemlist,\r\n        success: function (res) {\r\n          if(res.tapIndex >= 0){\r\n            allbuydata[bid].freightkey = res.tapIndex;\r\n            that.allbuydata = allbuydata;\r\n            that.calculatePrice();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    choosePstime: function (e) {\r\n      var that = this;\r\n      var allbuydata = that.allbuydata;\r\n      var bid = e.currentTarget.dataset.bid;\r\n      var freightkey = allbuydata[bid].freightkey;\r\n      var freightList = allbuydata[bid].freightList;\r\n      var freight = freightList[freightkey];\r\n      var pstimeArr = freightList[freightkey].pstimeArr;\r\n      var itemlist = [];\r\n      for (var i = 0; i < pstimeArr.length; i++) {\r\n        itemlist.push(pstimeArr[i].title);\r\n      }\r\n      if (itemlist.length == 0) {\r\n        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\r\n        return;\r\n      }\r\n      that.nowbid = bid;\r\n      that.pstimeDialogShow = true;\r\n      that.pstimeIndex = -1;\r\n    },\r\n    pstimeRadioChange: function (e) {\r\n      var that = this;\r\n      var allbuydata = that.allbuydata;\r\n      var pstimeIndex = e.currentTarget.dataset.index;\r\n      console.log(pstimeIndex)\r\n      var nowbid = that.nowbid;\r\n      var freightkey = allbuydata[nowbid].freightkey;\r\n      var freightList = allbuydata[nowbid].freightList;\r\n      var freight = freightList[freightkey];\r\n      var pstimeArr = freightList[freightkey].pstimeArr;\r\n      var choosepstime = pstimeArr[pstimeIndex];\r\n      allbuydata[nowbid].pstimetext = choosepstime.title;\r\n      allbuydata[nowbid].freight_time = choosepstime.value;\r\n      that.allbuydata = allbuydata\r\n      that.pstimeDialogShow = false;\r\n    },\r\n    hidePstimeDialog: function () {\r\n      this.pstimeDialogShow = false;\r\n    },\r\n    choosestore: function (e) {\r\n      var bid = e.currentTarget.dataset.bid;\r\n      var storekey = e.currentTarget.dataset.index;\r\n      var allbuydata = this.allbuydata;\r\n      var buydata = allbuydata[bid];\r\n      var freightkey = buydata.freightkey\r\n      allbuydata[bid].freightList[freightkey].storekey = storekey\r\n      this.allbuydata = allbuydata;\r\n    },\r\n    //提交并支付\r\n    topay: function (e) {\r\n      var that = this;\r\n      var needaddress = that.needaddress;\r\n      var addressid = this.address.id;\r\n      var linkman = this.linkman;\r\n      var tel = this.tel;\r\n      var usescore = this.usescore;\r\n      var frompage = that.opt.frompage ? that.opt.frompage : '';\r\n      var allbuydata = that.allbuydata;\r\n      if (needaddress == 0) addressid = 0;\r\n\r\n      if (needaddress == 1 && addressid == undefined) {\r\n        app.error('请选择收货地址');\r\n        return;\r\n      }\r\n\t  if(that.needChoose){\r\n\t  \t\t  if(that.chooseInfo==''){\r\n\t  \t\t\t  app.error('请选择商品');\r\n\t\t\t\t  return;\r\n\t  \t\t  }\r\n\t  }\r\n      var buydata = [];\r\n      for (var i in allbuydata) {\r\n        var freightkey = allbuydata[i].freightkey;\r\n        if (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {\r\n          app.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');\r\n          return;\r\n        }\r\n        if(allbuydata[i].freightList[freightkey].pstype==1){\r\n          var storekey = allbuydata[i].freightList[freightkey].storekey;\r\n          var storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;\r\n        }else{\r\n          var storeid = 0;\r\n        }\r\n\t\t\t\t\r\n\t\t\t\tvar formdata_fields = allbuydata[i].freightList[freightkey].formdata;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\tvar newformdata = {};\r\n\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\r\n\t\t\t\t\tvar thisfield = 'form'+allbuydata[i].bid + '_' + j;\r\n\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\r\n\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\r\n\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewformdata['form'+j] = formdata[thisfield];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\tlet proInfo = allbuydata[i].prodatastr\r\n\t\tif(that.needChoose){\r\n\t\t\tlet shopInfo = that.chooseInfo.product.id + ',' + that.chooseInfo.guige.id + ',' + that.chooseInfo.num\r\n\t\t\tproInfo = shopInfo\r\n\t\t}\r\n        buydata.push({\r\n          bid: allbuydata[i].bid,\r\n          prodata: proInfo,\r\n          cuxiaoid: allbuydata[i].cuxiaoid,\r\n          couponrid: allbuydata[i].couponrid,\r\n          freight_id: allbuydata[i].freightList[freightkey].id,\r\n          freight_time: allbuydata[i].freight_time,\r\n          storeid: storeid,\r\n\t\t  formdata:newformdata,\r\n        });\r\n      }\r\n      app.showLoading('提交中');\r\n      app.post('ApiLipin2/createOrder', {dhcode:that.opt.dhcode,cardno:that.opt.cardno,frompage: frompage,buydata: buydata,addressid: addressid,linkman: linkman,tel: tel,usescore: usescore}, function (res) {\r\n        app.showLoading(false);\r\n        if (res.status == 0) {\r\n          //that.showsuccess(res.data.msg);\r\n          app.error(res.msg);\r\n          return;\r\n        }else{\r\n          app.alert(res.msg,function(){\r\n            app.goto('/pages/my/usercenter','reLaunch');\r\n          })\r\n        }\r\n      });\r\n    },\r\n    handleClickMask: function () {\r\n    },\r\n    openLocation:function(e){\r\n      var allbuydata = this.allbuydata\r\n      var bid = e.currentTarget.dataset.bid;\r\n      var freightkey = e.currentTarget.dataset.freightkey;\r\n      var storekey = e.currentTarget.dataset.storekey;\r\n      var frightinfo = allbuydata[bid].freightList[freightkey]\r\n      var storeinfo = frightinfo.storedata[storekey];\r\n      console.log(storeinfo)\r\n      var latitude = parseFloat(storeinfo.latitude);\r\n      var longitude = parseFloat(storeinfo.longitude);\r\n      var address = storeinfo.name;\r\n      uni.openLocation({\r\n        latitude:latitude,\r\n        longitude:longitude,\r\n        name:address,\r\n        scale: 13\r\n      })\r\n    },\r\n\t\teditorChooseImage: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t})\r\n\t\t},\r\n\t\teditorBindPickerChange:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\teditorFormdata[idx] = val;\r\n\t\t\tconsole.log(editorFormdata)\r\n\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata;\r\n\t\t\tthat.test = Math.random();\r\n\t\t},\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\r\n.address-add .f1{margin-right:20rpx}\r\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\r\n.address-add .f2{ color: #666; }\r\n.address-add .f3{ width: 26rpx; height: 26rpx;}\r\n\r\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\r\n.linkitem .f1{width:160rpx;color:#111111}\r\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\r\n\r\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\r\n\r\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\r\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\r\n\r\n.bcontent{width:100%;padding:0 20rpx}\r\n\r\n.product{width:100%;border-bottom:1px solid #f4f4f4}\r\n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\r\n.product .item:last-child{border:none}\r\n.product .item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\r\n.product .item .radio .radio-img{width:100%;height:100%}\r\n.product .info{padding-left:20rpx;}\r\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .info .f2{color: #999999; font-size:24rpx}\r\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\r\n.product image{ width:140rpx;height:140rpx}\r\n\r\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\r\n.freight .f1{color:#333;margin-bottom:10rpx}\r\n.freight .f2{color: #111111;text-align:right;flex:1}\r\n.freight .f3{width: 24rpx;height:28rpx;}\r\n.freighttips{color:red;font-size:24rpx;}\r\n\r\n.freight-ul{width:100%;display:flex;}\r\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\r\n\r\n\r\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\r\n.price .f1{color:#333}\r\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\r\n.price .f3{width: 24rpx;height:24rpx;}\r\n\r\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\r\n.scoredk .f1{color:#333333}\r\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\r\n\r\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\r\n.remark .f1{color:#333;width:200rpx}\r\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\r\n\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding:0 20rpx;display:flex;align-items:center;z-index:8}\r\n.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1  text{color: #e94745;font-size: 32rpx;}\r\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\r\n\r\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\r\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\r\n.storeitem .panel .f1{color:#333}\r\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\r\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\r\n.storeitem .radio-item:last-child{border:0}\r\n.storeitem .radio-item .f1{color:#666;flex:1}\r\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\r\n.storeitem .radio .radio-img{width:100%;height:100%}\r\n\r\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.pstime-item .radio .radio-img{width:100%;height:100%}\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prodh.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prodh.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098355\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}