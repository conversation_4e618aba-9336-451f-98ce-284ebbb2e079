{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?c0b0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?fac5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?6209", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?2a7b", "uni-app:///pagesExb/index/city.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?dd45", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/index/city.vue?cb96"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "data_c", "hot_list", "hot_ids", "hotarea", "onLoad", "Object", "arr", "methods", "select", "uni", "selectOne", "id", "name", "inputKeyword", "obj", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8C7wB;AAAA,eAEA;EACAC;IACA;MACAA;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IAAA;IACA;MACA;MAEA;MAEA;MAEA;MAEA;QAEA;QAEAC;UACA;UACAN;YAEA;cACAO;YACA;UACA;UACA;YACAP;UACA;YACA;UACA;QAEA;QAEA;QAEA;MAEA;QACA;QAEA;MACA;IAGA;EACA;EACAQ;IACAC;MACAC;MACAA;IACA;IACAC;MACA;QACAC;QACAC;MACA;MAEAH;MACAA;IACA;IACAI;MAEA;MACA;MAEA;MAEA;QAEAR;UACA;UACAS;YACA;cACAR;YACA;UACA;UACA;YACAQ;UACA;YACAC;UACA;QAEA;MACA;MAEAN;IAIA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/index/city.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/index/city.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./city.vue?vue&type=template&id=87bf0564&\"\nvar renderjs\nimport script from \"./city.vue?vue&type=script&lang=js&\"\nexport * from \"./city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/index/city.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=template&id=87bf0564&\"", "var components\ntry {\n  components = {\n    tIndexAddress: function () {\n      return import(\n        /* webpackChunkName: \"components/t-index-address/t-index-address\" */ \"@/components/t-index-address/t-index-address.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hotarea == 1 ? _vm.hot_list.length : null\n  var g1 = _vm.hotarea == 1 ? _vm.hot_list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=script&lang=js&\"", "<template>\r\n\r\n\t<view>\r\n\t\t\r\n\t\t<view class=\"dp-search\">\r\n\t\t\t\r\n\t\t\t<view class=\"dp-search-search\">\r\n\t\t\t\t<view class=\"dp-search-search-f1\"></view>\r\n\t\t\t\t<view class=\"dp-search-search-f2\">\r\n\t\t\t\t\t<input class=\"dp-search-search-input\"  @input=\"inputKeyword\"  name=\"keyword\"\r\n\t\t\t\t\t\tplaceholder=\"输入城市名查询\" placeholder-style=\"color:#aaa;font-size:28rpx\"/>  \r\n\t\t\t\t</view> \r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"tab-main\" v-if=\"hotarea == 1\">  \r\n\t\t\t\r\n<!-- \t\t\t<view class=\"tit\">历史访问城市</view>\r\n\t\t\t\r\n\t\t\t<view class=\"tab-ll\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"tab\" v-for=\"(item, index) in hs_list\" :key=\"index\">{{item}}</view>\r\n\t\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view class=\"tit\" v-if=\"hot_list.length>0\">国内热门城市</view>\r\n\t\t\t\r\n\t\t\t<view class=\"tab-ll\" v-if=\"hot_list.length>0\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"tab\" v-for=\"(item, index) in hot_list\" :key=\"index\" @click=\"selectOne(index)\">{{item}}</view>\r\n\t\r\n\t\t\t</view> \r\n\r\n\t\t\t\r\n\t\t</view>\r\n\r\n\r\n\t\t<t-index-address @select=\"select\" :data=\"data\"></t-index-address>\r\n\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n                 data : '' ,  \r\n\t\t\t\t data_c : '',\r\n\t\t\t\t hot_list : [],\r\n\t\t\t\t hot_ids : [],\r\n\t\t\t\t hotarea : 0\r\n\t\t\t\t \r\n\t\t\t};  \r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tif(opt.data){\r\n\t\t\t\t let json = JSON.parse(decodeURIComponent(opt.data));\r\n\r\n\t\t\t\t this.hot_list = json.area_config.hotareas_str.split('、');\r\n\t\t\t\t \r\n\t\t\t\t this.hot_ids = json.area_config.hotareas.split(',');\r\n\t\t\t\t \r\n\t\t\t\t this.hotarea = json.area_config.hotarea;\r\n\t\t\t\t \r\n\t\t\t\t if(json.area_config.switcharearange == 0){\r\n\t\t\t\t\t \r\n\t\t\t\t\t let data = json.areas;\r\n\t\t\t\t\t \r\n\t\t\t\t\t Object.keys(data).forEach((key) => {\r\n\t\t\t\t\t \tlet arr = [];\r\n\t\t\t\t\t \tdata[key].filter(m=>{\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t \t\tif( json.area_config.switcharearangeareas_str.indexOf(m.name)!=-1){\r\n\t\t\t\t\t \t\t\tarr.push(m);\r\n\t\t\t\t\t \t\t}\r\n\t\t\t\t\t \t}) \r\n\t\t\t\t\t \tif(arr.length>0){\r\n\t\t\t\t\t \t\tdata[key] = arr;\r\n\t\t\t\t\t \t}else{\r\n\t\t\t\t\t\t\tthis.$delete(data,key)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t \r\n\t\t\t\t\t });\r\n\t\t\t\t\t \r\n\t\t\t\t\t  this.data  = data;\r\n\t\t\t\t\t \r\n\t\t\t\t\t  this.data_c = JSON.stringify(data);\r\n\t\t\t\t\t \r\n\t\t\t\t }else{\r\n\t\t\t\t\t this.data  = json.areas;\r\n\t\t\t\t\t \r\n\t\t\t\t\t this.data_c = JSON.stringify(json.areas);\r\n\t\t\t\t }\r\n\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},  \r\n\t\tmethods: {\r\n\t\t\tselect(data) {\r\n\t\t\t\tuni.$emit(\"city\",data);\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\tselectOne(index){\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\tid :  this.hot_ids[index],\r\n\t\t\t\t\tname :   this.hot_list[index],\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.$emit(\"city\",obj);\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\tinputKeyword(e){\r\n\t\t\t\t\r\n\t            let that = this \r\n\t\t\t\tlet name  = e.detail.value;\r\n\t\t\t\t\r\n\t\t\t\tlet obj = JSON.parse(that.data_c);\r\n\t\t\t\t\r\n\t\t\t\tif(name!=''){\r\n\t\t\r\n\t\t\t\t\tObject.keys(obj).forEach((key) => {\r\n\t\t\t\t\t\tlet arr = [];\r\n\t\t\t\t\t\tobj[key].filter(m=>{\r\n\t\t\t\t\t\t\tif(m.name.indexOf(name)!=-1){\r\n\t\t\t\t\t\t\t\tarr.push(m);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif(arr.length>0){\r\n\t\t\t\t\t\t\tobj[key] = arr;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.$delete(obj,key)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t        uni.$emit(\"syncCity\",obj);\r\n\t\t\t\t\r\n\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t\r\n\t.dp-search {padding:20rpx;height: auto; position: relative;display: flex;align-items: center;background: #fff}\r\n\t.dp-search-search {height:72rpx;background: #EFEFEF;border-radius: 50rpx;overflow: hidden;display:flex;width:100%;}\r\n\t.dp-search-search-f1 {height:72rpx;width:72rpx;color: #888;border: 0px;padding: 0px;margin: 0px;background:url('~@/static/img/search_ico.png') center no-repeat; background-size:30rpx;}\r\n\t.dp-search-search-f2{height: 72rpx;flex:1}\r\n\t.dp-search-search-f3 {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background-position: center;background-repeat: no-repeat; background-size:40rpx;}\r\n\t.dp-search-search-input {height:72rpx;width: 100%;border: 0px;padding: 0px;margin: 0px;outline: none;color: #666;}\r\n\t\r\n\t.tab-main{\r\n\t\tbackground: #fff;\r\n\t\tpadding: 15px;\r\n\t}\r\n\t\r\n\t.tit{\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #aaa;\r\n\t}\r\n\t\r\n\t.tab-ll{\r\n\t\t flex-wrap: wrap;\r\n\t\t display: flex;\r\n\t}\r\n\t\r\n\t.tab{\r\n\t\tpadding: 5px 10px;\r\n\t\tborder: 1rpx solid #aaa;\r\n\t\tmargin: 10px 10px 10px 0;\r\n\t\tborder-radius: 5px;\r\n\t}\r\n\t\r\n\t\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115022972\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}