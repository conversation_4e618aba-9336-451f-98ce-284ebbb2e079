{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?3cb8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?79c2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?240a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?30e7", "uni-app:///pagesExa/tuandui/records.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?757f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuandui/records.vue?26f4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "recordsList", "statsData", "activityList", "activityOptions", "selectedActivityIndex", "selectedActivityId", "statusOptions", "value", "text", "selectedStatusIndex", "selectedStatus", "pagenum", "limit", "nodata", "nomore", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "title", "id", "refreshData", "getStatsData", "params", "getRecordsList", "page", "uni", "onActivityChange", "onStatusChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqGhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;;MAEA;MACAC;QACA;UACAD;UACAA;YAAAE;YAAAC;UAAA;UACAH;QACA;QACA;QACAA;QACAA;MACA;IACA;IAEAI;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACAC;MACA;MAEAL;QACA;UACAD;QACA;MACA;IACA;IAEAO;MACA;MACA;QACAC;QACAhB;MACA;MAEA;QACAc;MACA;MAEA;QACAA;MACA;MAEAN;MACAC;QACAD;QACAA;QAEA;UACA;UAEA;YACA;cACAA;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UAEAS;YACAP;UACA;QACA;UACAD;QACA;QAEAQ;MACA;IACA;IAEAC;MACA;MACA;MACAV;MACAA;MACAA;IACA;IAEAW;MACA;MACA;MACAX;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtPA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuandui/records.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuandui/records.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./records.vue?vue&type=template&id=686d0326&\"\nvar renderjs\nimport script from \"./records.vue?vue&type=script&lang=js&\"\nexport * from \"./records.vue?vue&type=script&lang=js&\"\nimport style0 from \"./records.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuandui/records.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=template&id=686d0326&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loading && _vm.recordsList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<text class=\"title\">奖励记录</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 筛选条件 -->\r\n\t\t\t<view class=\"filter-box\">\r\n\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t<picker @change=\"onActivityChange\" :value=\"selectedActivityIndex\" :range=\"activityOptions\" range-key=\"title\">\r\n\t\t\t\t\t\t<view class=\"picker-text\">\r\n\t\t\t\t\t\t\t{{selectedActivityIndex >= 0 ? activityOptions[selectedActivityIndex].title : '选择活动'}}\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"filter-item\">\r\n\t\t\t\t\t<picker @change=\"onStatusChange\" :value=\"selectedStatusIndex\" :range=\"statusOptions\" range-key=\"text\">\r\n\t\t\t\t\t\t<view class=\"picker-text\">\r\n\t\t\t\t\t\t\t{{selectedStatusIndex >= 0 ? statusOptions[selectedStatusIndex].text : '选择状态'}}\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 统计信息 -->\r\n\t\t\t<view class=\"stats-box\" v-if=\"statsData\">\r\n\t\t\t\t<view class=\"stats-title\">统计信息</view>\r\n\t\t\t\t<view class=\"stats-content\">\r\n\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t<view class=\"stats-label\">总记录数</view>\r\n\t\t\t\t\t\t<view class=\"stats-value\">{{statsData.total_records}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t<view class=\"stats-label\">总奖励金额</view>\r\n\t\t\t\t\t\t<view class=\"stats-value reward-amount\">¥{{statsData.total_reward}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t<view class=\"stats-label\">已发放</view>\r\n\t\t\t\t\t\t<view class=\"stats-value success\">¥{{statsData.issued_amount}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t\t<view class=\"stats-label\">待发放</view>\r\n\t\t\t\t\t\t<view class=\"stats-value pending\">¥{{statsData.pending_amount}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 记录列表 -->\r\n\t\t\t<view class=\"records-box\">\r\n\t\t\t\t<view class=\"records-title\">奖励记录</view>\r\n\t\t\t\t<view class=\"records-content\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in recordsList\" :key=\"item.id\">\r\n\t\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t\t<view class=\"record-header\">\r\n\t\t\t\t\t\t\t\t<view class=\"activity-title\">{{item.activity_title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"record-time\">{{item.create_time}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"record-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"performance-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"perf-text\">目标业绩：{{item.achievement_target}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"actual-perf\">实际业绩：{{item.team_performance}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"reward-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"reward-amount\">¥{{item.reward_amount}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"status-tag\" :class=\"item.status == 1 ? 'status-active' : item.status == 2 ? 'status-claimed' : item.status == 3 ? 'status-paid' : 'status-inactive'\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.status_text}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"record-footer\">\r\n\t\t\t\t\t\t\t\t<view class=\"type-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"type-tag\">{{item.performance_type_text}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"type-tag\">{{item.reward_type_text}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"level-info\">等级 {{item.achievement_level}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t<view class=\"load-status\">\r\n\t\t\t\t\t<text v-if=\"nodata\" class=\"no-data\">暂无记录</text>\r\n\t\t\t\t\t<text v-else-if=\"nomore\" class=\"no-more\">没有更多记录了</text>\r\n\t\t\t\t\t<loading v-else-if=\"loading\"></loading>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\r\n\t<loading v-if=\"loading && recordsList.length === 0\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\trecordsList: [],\r\n\t\t\tstatsData: null,\r\n\t\t\tactivityList: [],\r\n\t\t\tactivityOptions: [],\r\n\t\t\tselectedActivityIndex: -1,\r\n\t\t\tselectedActivityId: 0,\r\n\t\t\tstatusOptions: [\r\n\t\t\t\t{value: '', text: '全部状态'},\r\n\t\t\t\t{value: 0, text: '待发放'},\r\n\t\t\t\t{value: 1, text: '已发放'},\r\n\t\t\t\t{value: 2, text: '已拒绝'}\r\n\t\t\t],\r\n\t\t\tselectedStatusIndex: 0,\r\n\t\t\tselectedStatus: '',\r\n\t\t\tpagenum: 1,\r\n\t\t\tlimit: 20,\r\n\t\t\tnodata: false,\r\n\t\t\tnomore: false\r\n\t\t};\r\n\t},\r\n\tonLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.refreshData();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t\tif (!this.nodata && !this.nomore && !this.loading) {\r\n\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\tthis.getRecordsList();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\t// 获取活动列表\r\n\t\t\tapp.get('ApiTuandui/getActivityList', {}, function(res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.activityList = res.data || [];\r\n\t\t\t\t\tthat.activityOptions = [{title: '全部活动', id: 0}].concat(that.activityList);\r\n\t\t\t\t\tthat.selectedActivityIndex = 0;\r\n\t\t\t\t}\r\n\t\t\t\t// 获取统计数据和记录列表\r\n\t\t\t\tthat.getStatsData();\r\n\t\t\t\tthat.getRecordsList();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\trefreshData: function() {\r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.recordsList = [];\r\n\t\t\tthis.nodata = false;\r\n\t\t\tthis.nomore = false;\r\n\t\t\tthis.getStatsData();\r\n\t\t\tthis.getRecordsList();\r\n\t\t},\r\n\t\t\r\n\t\tgetStatsData: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar params = {};\r\n\t\t\tif (that.selectedActivityId > 0) {\r\n\t\t\t\tparams.activity_id = that.selectedActivityId;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiTuandui/getRewardStats', params, function(res) {\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.statsData = res.data;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tgetRecordsList: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar params = {\r\n\t\t\t\tpage: that.pagenum,\r\n\t\t\t\tlimit: that.limit\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tif (that.selectedActivityId > 0) {\r\n\t\t\t\tparams.activity_id = that.selectedActivityId;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (that.selectedStatus !== '') {\r\n\t\t\t\tparams.status = that.selectedStatus;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiTuandui/getRewardRecords', params, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.isload = true;\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tvar data = res.data || [];\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\t\tthat.recordsList = data;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.recordsList = that.recordsList.concat(data);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '奖励记录'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.alert(res.msg || '获取记录失败');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tonActivityChange: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar index = e.detail.value;\r\n\t\t\tthat.selectedActivityIndex = index;\r\n\t\t\tthat.selectedActivityId = that.activityOptions[index].id;\r\n\t\t\tthat.refreshData();\r\n\t\t},\r\n\t\t\r\n\t\tonStatusChange: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar index = e.detail.value;\r\n\t\t\tthat.selectedStatusIndex = index;\r\n\t\t\tthat.selectedStatus = that.statusOptions[index].value;\r\n\t\t\tthat.refreshData();\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tbackground-color: #fff;\r\n\tpadding: 20rpx;\r\n\ttext-align: center;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header .title {\r\n\tfont-size: 36rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 筛选条件样式 */\r\n.filter-box {\r\n\tbackground-color: #fff;\r\n\tpadding: 20rpx;\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.filter-item {\r\n\tflex: 1;\r\n}\r\n\r\n.picker-text {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 20rpx;\r\n\theight: 20rpx;\r\n}\r\n\r\n/* 统计信息样式 */\r\n.stats-box {\r\n\tbackground-color: #fff;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.stats-title {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tpadding: 20rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.stats-content {\r\n\tpadding: 20rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.stats-item {\r\n\ttext-align: center;\r\n\tflex: 1;\r\n}\r\n\r\n.stats-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.stats-value.reward-amount {\r\n\tcolor: #FA5151;\r\n}\r\n\r\n.stats-value.success {\r\n\tcolor: #28a745;\r\n}\r\n\r\n.stats-value.pending {\r\n\tcolor: #ffc107;\r\n}\r\n\r\n/* 记录列表样式 */\r\n.records-box {\r\n\tbackground-color: #fff;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.records-title {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tpadding: 20rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.record-item {\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.record-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.activity-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.record-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.record-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.performance-info {\r\n\tflex: 1;\r\n}\r\n\r\n.perf-text, .actual-perf {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.reward-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: flex-end;\r\n}\r\n\r\n.reward-amount {\r\n\tfont-size: 32rpx;\r\n\tcolor: #FA5151;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.status-tag {\r\n\tfont-size: 24rpx;\r\n\tpadding: 6rpx 12rpx;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.status-pending {\r\n\tbackground-color: #fff3cd;\r\n\tcolor: #856404;\r\n}\r\n\r\n.status-success {\r\n\tbackground-color: #d4edda;\r\n\tcolor: #155724;\r\n}\r\n\r\n.status-rejected {\r\n\tbackground-color: #f8d7da;\r\n\tcolor: #721c24;\r\n}\r\n\r\n.status-unknown {\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #999;\r\n}\r\n\r\n.record-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.type-info {\r\n\tdisplay: flex;\r\n\tgap: 10rpx;\r\n}\r\n\r\n.type-tag {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tpadding: 4rpx 8rpx;\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.level-info {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 加载状态样式 */\r\n.load-status {\r\n\tpadding: 40rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.no-data, .no-more {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065237\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}