{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?0c1a", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?daa5", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?fce6", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?6ad7", "uni-app:///restaurant/queue/quhao.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?1760", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/quhao.vue?d3b5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "business", "clist", "notice", "linkman", "tel", "renshuvisible", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renshu", "categoryvisible", "cid", "cidCache", "cname", "socketOpen", "token", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "bid", "uni", "url", "console", "socketMsgQueue", "sendSocketMessage", "<PERSON><PERSON><PERSON><PERSON>", "type", "aid", "inputLinkman", "inputTel", "showRenshuSelect", "changeRenshu", "<PERSON><PERSON><PERSON><PERSON>", "showCategorySelect", "changeCategory", "chooseCategory", "handleClickMask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2F9wB;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACA;UACA;UACAG;UACAA;YACAC;UACA;UACAD;YACAE;YACAL;YACA;cACAA;YACA;YACAM;UACA;QACA;MACA;IACA;IACAC;MACA;QACAF;QACAF;UACA5B;QACA;MACA;QACA8B;QACAC;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;QACAP;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAA;MACAA;QAAAC;QAAAnB;QAAAC;QAAAG;QAAAE;MAAA;QACAY;QACA;UACAA;QACA;UACAD;YAAAS;YAAAhB;YAAAlB;cAAAmC;cAAAR;YAAA;UAAA;UACAD;YACAA;UACA;QACA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAd;IACA;IACAe;MACA;MACAf;MACA;IACA;IACAgB;MACA;IACA;IACAC;MACA;MACAjB;IACA;IACAkB;MACA;MACAlB;MACA;MACA;QACA;UACAA;QACA;MACA;MACA;IACA;IACAmB;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/queue/quhao.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/queue/quhao.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./quhao.vue?vue&type=template&id=27f9d9b0&\"\nvar renderjs\nimport script from \"./quhao.vue?vue&type=script&lang=js&\"\nexport * from \"./quhao.vue?vue&type=script&lang=js&\"\nimport style0 from \"./quhao.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/queue/quhao.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quhao.vue?vue&type=template&id=27f9d9b0&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.renshuvisible\n      ? _vm.__map(\n          [\n            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\n            20,\n          ],\n          function (item, __i0__) {\n            var $orig = _vm.__get_orig(item)\n            var m0 = _vm.renshuCache == item ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m0: m0,\n            }\n          }\n        )\n      : null\n  var m1 = _vm.isload && _vm.renshuvisible ? _vm.t(\"color1\") : null\n  var l1 =\n    _vm.isload && _vm.categoryvisible\n      ? _vm.__map(_vm.clist, function (item, __i1__) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.cidCache == item.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var m3 = _vm.isload && _vm.categoryvisible ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        l1: l1,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quhao.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quhao.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topbannerbg\" :style=\"business.pic?'background:url('+business.pic+') 100%':''\"></view>\r\n\t\t<view class=\"topbannerbg2\"></view>\r\n\t\t<view class=\"topbanner\">\r\n\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\r\n\t\t\t<view class=\"right\">\r\n\t\t\t\t<view class=\"f1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\r\n\t\t\t\t<!-- <view class=\"f3\"><view class=\"flex1\"></view><view class=\"t2\">收藏<image class=\"img\" src=\"/static/img/like1.png\"/></view></view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"notice\">\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<image src=\"/static/img/queue-notice.png\" class=\"f1\"/>\r\n\t\t\t\t<view class=\"f2\">{{notice}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"form\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">您的姓名</text>\r\n\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入姓名\" name=\"linkman\" :value=\"linkman\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" @input=\"inputLinkman\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">手机号</text>\r\n\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" name=\"tel\" :value=\"tel\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" @input=\"inputTel\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">用餐人数</text>\r\n\t\t\t\t<text class=\"f2\" @tap=\"showRenshuSelect\" :style=\"renshu==''?'color:#BBBBBB;font-size:28rpx':''\">{{renshu!='' ? renshu+'人' : '请选择人数'}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal;font-size:28rpx\"></text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">排队队列</text>\r\n\t\t\t\t<text class=\"f2\" @tap=\"showCategorySelect\" :style=\"cname==''?'color:#BBBBBB;font-size:28rpx':''\">{{cname!='' ? cname : '请选择队列'}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal;font-size:28rpx\"></text></text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @tap=\"confirmQuhao\">确定取号</view>\r\n\r\n\t\t<view v-if=\"renshuvisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择用餐人数</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"cuxiao-desc\">\r\n\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changeRenshu\" :data-id=\"item\" v-for=\"item in [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">{{item}}人</text></view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"renshuCache==item ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\r\n\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\r\n\t\t\t\t\t\t<view style=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\" :style=\"{background:t('color1')}\" @tap=\"chooseRenshu\">确 定</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"categoryvisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择队列</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"cuxiao-desc\">\r\n\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changeCategory\" :data-id=\"item.id\" v-for=\"item in clist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cidCache==item.id ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\r\n\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\r\n\t\t\t\t\t\t<view style=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\" :style=\"{background:t('color1')}\" @tap=\"chooseCategory\">确 定</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar intervel;\r\nvar socketMsgQueue = [];\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tbusiness:{},\r\n      clist: [],\r\n\t\t\tnotice:'',\r\n\r\n\t\t\tlinkman:'',\r\n\t\t\ttel:'',\r\n\t\t\trenshuvisible:false,\r\n\t\t\trenshuCache:'',\r\n\t\t\trenshu:'',\r\n\t\t\tcategoryvisible:false,\r\n\t\t\tcid:0,\r\n\t\t\tcidCache:0,\r\n\t\t\tcname:'',\r\n\t\t\tsocketOpen:false,\r\n\t\t\ttoken:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onUnload: function () {\r\n    clearInterval(intervel);\r\n    if(this.socketOpen) uni.closeSocket();\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiRestaurantQueue/quhao', {bid:that.opt.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status==0){\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tapp.goback(true);\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.business = res.business\r\n\t\t\t\tthat.linkman = res.linkman\r\n\t\t\t\tthat.tel = res.tel\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.notice = res.notice;\r\n\t\t\t\tthat.token = res.token;\r\n\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\tif(!that.socketOpen){\r\n\t\t\t\t\tvar pre_url = app.globalData.pre_url;\r\n\t\t\t\t\tvar wssurl = pre_url.replace('https://', \"wss://\") + '/wss';\r\n\t\t\t\t\tuni.closeSocket();\r\n\t\t\t\t\tuni.connectSocket({\r\n\t\t\t\t\t\turl: wssurl\r\n\t\t\t\t\t});\r\n\t\t\t\t\tuni.onSocketOpen(function (res) {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.socketOpen = true;\r\n\t\t\t\t\t\tfor (var i = 0; i < socketMsgQueue.length; i++) {\r\n\t\t\t\t\t\t\tthat.sendSocketMessage(socketMsgQueue[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tsocketMsgQueue = [];\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsendSocketMessage:function(msg) {\r\n\t\t\tif (this.socketOpen) {\r\n\t\t\t\tconsole.log(msg)\r\n\t\t\t\tuni.sendSocketMessage({\r\n\t\t\t\t\tdata: JSON.stringify(msg)\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('111')\r\n\t\t\t\tsocketMsgQueue.push(msg);\r\n\t\t\t}\r\n\t\t},\r\n\t\tconfirmQuhao:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar linkman = this.linkman\r\n\t\t\tvar tel = this.tel\r\n\t\t\tvar renshu = this.renshu;\r\n\t\t\tvar cid = this.cid;\r\n\t\t\tif(!linkman){\r\n\t\t\t\tapp.alert('请输入您的姓名');return;\r\n\t\t\t}\r\n\t\t\tif(!tel){\r\n\t\t\t\tapp.alert('请输入您的手机号');return;\r\n\t\t\t}\r\n\t\t\tif(!renshu){\r\n\t\t\t\tapp.alert('请选择用餐人数');return;\r\n\t\t\t}\r\n\t\t\tif(!cid){\r\n\t\t\t\tapp.alert('请选择队列');return;\r\n\t\t\t}\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiRestaurantQueue/quhao',{bid:that.opt.bid,linkman:linkman,tel:tel,renshu:renshu,cid:cid},function(res){\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif(res.status==0){\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.sendSocketMessage({type: 'restaurant_queue_add',token: that.token,data:{ aid:app.globalData.aid,bid:that.opt.bid }});\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tapp.goto('index?bid='+that.opt.bid,'reLaunch');\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n    inputLinkman: function (e) {\r\n      this.linkman = e.detail.value;\r\n    },\r\n    inputTel: function (e) {\r\n      this.tel = e.detail.value;\r\n    },\r\n\t\tshowRenshuSelect:function(e){\r\n\t\t\tthis.renshuvisible = true;\r\n\t\t},\r\n\t\tchangeRenshu: function (e) {\r\n      var that = this;\r\n      that.renshuCache = e.currentTarget.dataset.id;\r\n\t\t},\r\n    chooseRenshu: function () {\r\n      var that = this;\r\n\t\t\tthat.renshu = that.renshuCache\r\n      this.renshuvisible = false;\r\n\t\t},\r\n\t\tshowCategorySelect:function(e){\r\n\t\t\tthis.categoryvisible = true;\r\n\t\t},\r\n\t\tchangeCategory: function (e) {\r\n      var that = this;\r\n      that.cidCache = e.currentTarget.dataset.id;\r\n\t\t},\r\n    chooseCategory: function () {\r\n      var that = this;\r\n\t\t\tthat.cid = that.cidCache;\r\n\t\t\tvar clist = that.clist;\r\n\t\t\tfor(var i in clist){\r\n\t\t\t\tif(clist[i].id == that.cid){\r\n\t\t\t\t\tthat.cname = clist[i].name;\r\n\t\t\t\t}\r\n\t\t\t}\r\n      this.categoryvisible = false;\r\n\t\t},\r\n    handleClickMask: function () {\r\n      this.categoryvisible = false;\r\n      this.renshuvisible = false;\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {position: relative;width: 100%;height: 100%;}\r\n.container{height:100%;overflow:hidden;position: relative;}\r\n\r\n.topbannerbg{width:100%;height:280rpx;background:#fff;}\r\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:280rpx;background:rgba(0,0,0,0.7);top:0}\r\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:60rpx;top:0;align-items:center}\r\n.topbanner .left{width:100rpx;height:100rpx;flex-shrink:0;margin-right:20rpx;display:none}\r\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\r\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\r\n.topbanner .right .f1{font-size:32rpx;font-weight:bold;color:#fff}\r\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\r\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\r\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\r\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\r\n\r\n.notice{display:flex;width:94%;margin:0 3%;height:120rpx;background: #fff;position:absolute;z-index:9;padding:0 50rpx;border-radius:10rpx;margin-top:-70rpx;}\r\n.notice .content{display:flex;width:100%;align-items:center}\r\n.notice .content .f1{width:40rpx;height:40rpx;margin-right:20rpx}\r\n.notice .content .f2{flex:1;color:#FC5729;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n\r\n.form{ width:94%;margin:0 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;margin-top:70rpx}\r\n.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;font-size:30rpx}\r\n.form-item:last-child{border:0}\r\n.form-item .label{color: #000;width:200rpx;}\r\n.form-item .input{flex:1;color: #000;text-align:right}\r\n.form-item .f2{flex:1;color: #000;text-align:right}\r\n.form-item .picker{height: 60rpx;line-height:60rpx;margin-left: 0;flex:1;color: #000;}\r\n\r\n.btn{width:94%;margin:0 3%;margin-top:40rpx;height:90rpx;line-height:90rpx;text-align:center;background: linear-gradient(90deg, #FF7D15 0%, #FC5729 100%);color:#fff;font-size:32rpx;font-weight:bold;border-radius:10rpx}\r\n\r\n.cuxiao-desc{width:100%}\r\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\r\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quhao.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quhao.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065989\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}