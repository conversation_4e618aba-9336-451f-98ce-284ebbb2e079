{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?c22a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?f85d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?4754", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?d99e", "uni-app:///pagesExa/tongxunlu/maps.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?1257", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tongxunlu/maps.vue?ffec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "keyword", "datalist", "type", "nodata", "curTopIndex", "index", "curCid", "nomore", "pagenum", "regionList", "regionNames", "subRegionList", "subRegionNames", "selectedRegion", "selectedSubRegion", "clist", "markers", "latitude", "longitude", "scale", "nearestStation", "<PERSON><PERSON><PERSON><PERSON>", "showNavigation", "userMarkerId", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "cid", "bid", "id", "name", "children", "onRegionChange", "onSubRegionChange", "getAll<PERSON><PERSON><PERSON>n", "allChildren", "getdatalist", "field", "order", "item", "loadMoreData", "console", "searchChange", "searchConfirm", "call", "phoneNumber", "getNearbyRegions", "iconPath", "width", "height", "callout", "content", "color", "fontSize", "borderRadius", "bgColor", "display", "onMarkerTap", "closeNavigation", "navigateToMarker", "address", "locateMe", "success", "fail", "title", "icon", "calculateNearestStation", "minDistance", "nearest", "getDistance", "Math", "toRad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2D7wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACAC;MAEAC;QAAAC;QAAAC;MAAA;QACAH;QAEA;UACAA;YAAA;cACAI;cACAC;cACAC;YACA;UAAA;UACAN;YAAA;UAAA;;UAEAA;UAEA;YACAA;YACA;YACAA;YACAA;cAAA;YAAA;UACA;;UAEAA;QACA;QAEAA;MACA;IACA;IAEAO;MACA;MACA;;MAEA;MACA;MACA;QAAA;MAAA;;MAEA;IACA;IAEAC;MACA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAH;QACAI;UAAAN;UAAAC;QAAA;QACA;UACAK;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAX;MACAA;MACAA;MACA;MACA;MAEAC;QACAtB;QACAR;QACAyC;QACAC;QACAX;QACAC;QACA9B;QACAgB;QACAD;MACA;QACAY;QACA;;QAEA;QACAlC;UACA;YACAgD;UACA;QACA;;QAEA;UACAd;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAe;MACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;;IAEAC;MACA;IACA;;IAEAC;MACA1D;QACA2D;MACA;IACA;IAEA;IACAC;MACA;MACApB;QACAe;QACA;UACA;UACA;;UAEA;UACA;YACAhB;YACAA;UACA;UAEAA;YACAgB;YACA;cACAZ;cACAhB;cAAA;cACAC;cACAiC;cAAA;cACAC;cACAC;cACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;UACA;;UAEA;UACA/B;QACA;MACA;IACA;IAEAgC;MACAhB;MACA;QAAA;MAAA;MACA;QACA;QACA;;QAEA;QACA;MACA;IACA;;IAEA;IACAiB;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAzE;UACA2B;UACAC;UACAgB;UACA8B;UAAA;UACA7C;QACA;;QACA;MACA;IACA;IAEA;IACA8C;MACA;MACA3E;QACAY;QAAA;QACAgE;UACArC;UACAA;;UAEA;UACAA;YAAA;UAAA;;UAEA;UACAA;YACAI;YACAhB;YACAC;YACAiC;YAAA;YACAC;YACAC;YACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;;UAEA;UACA/B;UACAA;;UAEA;UACAA;QACA;QACAsC;UACA7E;YACA8E;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACAzC;UACA;UACA;YAAA;YACA0C;YACAC;UACA;QACA;QACA3C;MACA;IACA;IAEA;IACA4C;MACA;MACA;MACA;MACA;MACA,+DACAC;MACA;IACA;;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClZA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tongxunlu/maps.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tongxunlu/maps.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./maps.vue?vue&type=template&id=f3c8adf2&\"\nvar renderjs\nimport script from \"./maps.vue?vue&type=script&lang=js&\"\nexport * from \"./maps.vue?vue&type=script&lang=js&\"\nimport style0 from \"./maps.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tongxunlu/maps.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=template&id=f3c8adf2&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <!-- 定位信息和地图 -->\r\n    <view class=\"fixed-section\">\r\n      <view class=\"location-info\">\r\n        <view class=\"location-text\">\r\n          <text>您当前所在位置</text>\r\n          <text>{{ nearestStation }}</text>\r\n        </view>\r\n        <button class=\"location-button\" @tap=\"locateMe\">使用定位</button>\r\n      </view>\r\n \r\n\r\n      <view class=\"map-container\">\r\n        <map \r\n         :style=\"{ width: '100%', height: selectedMarker ? '250px' : '300px' }\"\" \r\n          :latitude=\"latitude\" \r\n          :longitude=\"longitude\" \r\n          :scale=\"scale\" \r\n          :markers=\"markers\" \r\n          @markertap=\"onMarkerTap\">\r\n        </map>\r\n\t\t<view v-if=\"selectedMarker\" class=\"navigation-bar\">\r\n\t\t    <text class=\"station-name\">导航到 {{ selectedMarker.callout.content }}</text>\r\n\t\t    <button class=\"navigate-button\" @tap=\"navigateToMarker\">点击导航</button>\r\n\t\t  </view>\r\n      </view>\r\n <!-- 导航弹窗 -->\r\n     \r\n      <!-- 表头 -->\r\n      <view class=\"table-header\">\r\n        <view class=\"header-cell\">小区名称</view>\r\n        <view class=\"header-cell\">服务人员</view>\r\n        <view class=\"header-cell\">电话</view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 滚动数据列表 -->\r\n    <scroll-view class=\"scroll-view\" scroll-y=\"true\">\r\n      <view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"table-row\" :data-id=\"item.id\">\r\n        <view class=\"table-cell community-name\">\r\n          {{ item.third_level_name }}\r\n        </view>\r\n        <view class=\"table-cell service-staff\">\r\n          {{ item.realname }}\r\n        </view>\r\n        <view class=\"table-cell service-tel\">\r\n          <image src=\"/static/img/tongxunlutle.png\" mode=\"aspectFit\" class=\"call-icon\" @click=\"call(item.tel)\" />\r\n        </view>\r\n      </view>\r\n      <nodata v-if=\"nodata\"></nodata>\r\n      <nomore v-if=\"nomore\"></nomore>\r\n    </scroll-view>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      menuindex: -1,\r\n      keyword: '', // 搜索关键词\r\n      datalist: [], // 用于存放人员数据\r\n      type: \"\",\r\n      nodata: false,\r\n      curTopIndex: -1,\r\n      index: 0,\r\n      curCid: 0, // 当前选择的小区ID\r\n      nomore: false,\r\n      pagenum: 1,\r\n      regionList: [], // 存放区县列表对象\r\n      regionNames: [], // 存放区县名称的字符串数组，供 picker 使用\r\n      subRegionList: [], // 存放小区列表对象\r\n      subRegionNames: [], // 存放小区名称的字符串数组，供 picker 使用\r\n      selectedRegion: {}, // 用户选择的区县\r\n      selectedSubRegion: {}, // 用户选择的小区\r\n      clist: [], // 存放API返回的数据，确保其定义并初始化\r\n      markers: [], // 地图标记点\r\n      latitude: 39.9042, // 默认中心纬度 (北京)\r\n      longitude: 116.4074, // 默认中心经度 (北京)\r\n      scale: 12, // 地图缩放级别\r\n      nearestStation: '*****小区', // 最近的服务站信息，默认显示\r\n\t   selectedMarker: null, // 当前选中的标记\r\n\t      showNavigation: false, // 控制导航弹窗显示\r\n      userMarkerId: 'user_location' // 用户位置标记的ID\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.type = this.opt.type || '';\r\n    this.getdata();\r\n    this.getNearbyRegions(); // 加载地图标记点\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdatalist(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      var nowcid = that.opt.cid;\r\n      var bid = that.opt.bid || 0;\r\n      if (!nowcid) nowcid = '';\r\n      that.loading = true;\r\n\r\n      app.get('Apitongxunlu/peocategory', { cid: nowcid, bid: bid }, function (res) {\r\n        that.loading = false;\r\n\r\n        if (res.data && res.data.length > 0) {\r\n          that.regionList = res.data.map(item => ({\r\n            id: item.id,\r\n            name: item.name,\r\n            children: item.children || []\r\n          }));\r\n          that.regionNames = res.data.map(item => item.name); // 提取区县名称供 picker 使用\r\n\r\n          that.clist = res.data;\r\n\r\n          if (that.regionList.length > 0) {\r\n            that.selectedRegion = that.regionList[0];\r\n            // 递归获取所有子层级的小区列表\r\n            that.subRegionList = that.getAllChildren(that.regionList[0].children);\r\n            that.subRegionNames = that.subRegionList.map(item => item.name); // 提取小区名称供 picker 使用\r\n          }\r\n\r\n          that.getdatalist();\r\n        }\r\n\r\n        that.loaded();\r\n      });\r\n    },\r\n\r\n    onRegionChange: function (e) {\r\n      var selectedRegionIndex = e.detail.value;\r\n      this.selectedRegion = this.regionList[selectedRegionIndex];\r\n\r\n      // 使用递归函数处理子层级小区\r\n      this.subRegionList = this.getAllChildren(this.selectedRegion.children);\r\n      this.subRegionNames = this.subRegionList.map(item => item.name); // 更新小区名称供 picker 使用\r\n\r\n      this.selectedSubRegion = {};\r\n    },\r\n\r\n    onSubRegionChange: function (e) {\r\n      var selectedSubRegionIndex = e.detail.value;\r\n      this.selectedSubRegion = this.subRegionList[selectedSubRegionIndex];\r\n      this.curCid = this.selectedSubRegion.id;\r\n\r\n      // 调用接口获取选定小区的人员数据\r\n      this.getdatalist();\r\n    },\r\n\r\n    // 递归获取所有子层级的小区\r\n    getAllChildren: function (children) {\r\n      let allChildren = [];\r\n      children.forEach(child => {\r\n        allChildren.push({ id: child.id, name: child.name });\r\n        if (child.children && child.children.length > 0) {\r\n          allChildren = allChildren.concat(this.getAllChildren(child.children));\r\n        }\r\n      });\r\n      return allChildren;\r\n    },\r\n\r\n    getdatalist: function (loadmore) {\r\n      if (!loadmore) {\r\n        this.pagenum = 1;\r\n        this.datalist = [];\r\n      }\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var cid = that.curCid; // 使用选择的小区ID\r\n      var bid = that.opt.bid ? that.opt.bid : '';\r\n      var order = that.order;\r\n      var keyword = that.keyword; // 关键字\r\n      var field = that.field;\r\n      that.loading = true;\r\n      that.nodata = false;\r\n      that.nomore = false;\r\n      var latitude = that.latitude;\r\n      var longitude = that.longitude;\r\n\r\n      app.post('Apitongxunlu/selectpeople2', {\r\n        pagenum: pagenum,\r\n        keyword: keyword,\r\n        field: field,\r\n        order: order,\r\n        cid: cid,\r\n        bid: bid,\r\n        type: 'list',\r\n        longitude: longitude,\r\n        latitude: latitude\r\n      }, function (res) {\r\n        that.loading = false;\r\n        var data = res.data;\r\n\r\n        // 遍历每个数据项并提取服务站信息\r\n        data.forEach(item => {\r\n          if (item.second_level_name) {\r\n            item.service_area = item.third_level_name; // 使用 second_level_name 作为服务站信息\r\n          }\r\n        });\r\n\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        } else {\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n   loadMoreData: function () {\r\n       console.log('滚动到底部，加载更多');\r\n       if (!this.nomore && !this.nodata) {\r\n         this.pagenum += 1;\r\n         this.getdatalist(true);\r\n       }\r\n     },\r\n    searchChange: function (e) {\r\n      this.keyword = e.detail.value; // 更新搜索关键字\r\n    },\r\n\r\n    searchConfirm: function () {\r\n      this.getdatalist(false); // 当用户确认搜索时调用，重新加载数据\r\n    },\r\n\r\n    call: function (tel) {\r\n      wx.makePhoneCall({\r\n        phoneNumber: tel\r\n      });\r\n    },\r\n\r\n    // 获取附近的服务区域并添加到地图标记点中\r\n    getNearbyRegions: function () {\r\n      var that = this;\r\n      app.get('Apitongxunlu/peocategory', {}, (res) => {\r\n        console.log(\"接口返回的数据：\", res.data); // 输出接口返回的数据\r\n        if (res.data && res.data.length > 0) {\r\n          const firstRegion = res.data[0]; // 获取第一个区域的children\r\n          const children = firstRegion.children || [];\r\n\r\n          // 确保地图中心点有坐标值\r\n          if (children.length > 0) {\r\n            that.latitude = parseFloat(children[0].latitude);\r\n            that.longitude = parseFloat(children[0].longitude);\r\n          }\r\n\r\n          that.markers = children.map((item) => {\r\n            console.log(\"子区域数据：\", item); // 输出每个子区域的数据\r\n            return {\r\n              id: item.id,\r\n              latitude: parseFloat(item.latitude), // 确保经纬度是浮点数\r\n              longitude: parseFloat(item.longitude),\r\n              iconPath: item.pic || '/static/img/addre.png', // 确保有图标路径\r\n              width: 40,\r\n              height: 40,\r\n              callout: {\r\n                content: item.name,\r\n                color: '#ffffff',\r\n                fontSize: 14,\r\n                borderRadius: 10,\r\n                bgColor: '#333',\r\n                display: 'ALWAYS'\r\n              }\r\n            };\r\n          });\r\n\r\n          // 根据当前位置计算最近的服务站\r\n          that.calculateNearestStation();\r\n        }\r\n      });\r\n    },\r\n\r\n     onMarkerTap: function (e) {\r\n       console.log(\"标记点点击事件触发，ID:\", e.detail.markerId);\r\n       const tappedMarker = this.markers.find(marker => marker.id === e.detail.markerId);\r\n       if (tappedMarker) {\r\n\t\t   this.curCid = e.detail.markerId; // 获取标记点的 ID 作为 curCid\r\n\t\t         this.getdatalist(false); // 触发获取列表数据的方法\r\n\r\n         this.selectedMarker = tappedMarker; // 保存选中的标记\r\n         this.showNavigation = true; // 显示导航弹窗\r\n       }\r\n     },\r\n   \r\n     // 关闭导航弹窗\r\n     closeNavigation: function () {\r\n       this.showNavigation = false;\r\n       this.selectedMarker = null;\r\n     },\r\n   \r\n     // 一键导航方法\r\n     navigateToMarker: function () {\r\n       if (this.selectedMarker) {\r\n         wx.openLocation({\r\n           latitude: this.selectedMarker.latitude,\r\n           longitude: this.selectedMarker.longitude,\r\n           name: this.selectedMarker.callout.content || '服务站',\r\n           address: this.selectedMarker.address || '', // 如果有地址信息，可以传递\r\n           scale: 18 // 缩放级别，可根据需要调整\r\n         });\r\n         this.closeNavigation();\r\n       }\r\n     },\r\n\r\n    // 获取用户当前位置并更新地图中心点\r\n    locateMe: function () {\r\n      var that = this;\r\n      wx.getLocation({\r\n        type: 'gcj02', // 使用 'gcj02' 以便地图组件能正确解析\r\n        success(res) {\r\n          that.latitude = res.latitude;\r\n          that.longitude = res.longitude;\r\n\r\n          // 删除之前的用户位置标记（如果存在）\r\n          that.markers = that.markers.filter(marker => marker.id !== that.userMarkerId);\r\n\r\n          // 添加当前定位的标记点\r\n          that.markers.push({\r\n            id: that.userMarkerId,\r\n            latitude: res.latitude,\r\n            longitude: res.longitude,\r\n            iconPath: '/static/img/userloc.png', // 用户定位图标\r\n            width: 40,\r\n            height: 40,\r\n            callout: {\r\n              content: '当前位置',\r\n              color: '#ffffff',\r\n              fontSize: 14,\r\n              borderRadius: 10,\r\n              bgColor: '#1aad19',\r\n              display: 'ALWAYS'\r\n            }\r\n          });\r\n\r\n          // 更新列表数据为当前位置的附近人员信息\r\n          that.curCid = ''; // 清空 curCid 以便重新获取数据\r\n          that.getdatalist(false);\r\n\r\n          // 重新计算最近的服务站\r\n          that.calculateNearestStation();\r\n        },\r\n        fail() {\r\n          wx.showToast({\r\n            title: '无法获取定位信息',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 计算最近的服务站\r\n    calculateNearestStation: function () {\r\n      var that = this;\r\n      if (that.markers.length > 0) {\r\n        let nearest = that.markers[0];\r\n        let minDistance = that.getDistance(nearest.latitude, nearest.longitude, that.latitude, that.longitude);\r\n        that.markers.forEach(marker => {\r\n          const distance = that.getDistance(marker.latitude, marker.longitude, that.latitude, that.longitude);\r\n          if (distance < minDistance && marker.id !== that.userMarkerId) { // 忽略用户自身位置标记\r\n            minDistance = distance;\r\n            nearest = marker;\r\n          }\r\n        });\r\n        that.nearestStation = nearest.callout.content || '*****小区';\r\n      }\r\n    },\r\n\r\n    // 计算两个坐标之间的距离\r\n    getDistance: function (lat1, lon1, lat2, lon2) {\r\n      const radLat1 = this.toRad(lat1);\r\n      const radLat2 = this.toRad(lat2);\r\n      const a = radLat1 - radLat2;\r\n      const b = this.toRad(lon1) - this.toRad(lon2);\r\n      const s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +\r\n        Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));\r\n      return s * 6378137; // 地球半径，单位为米\r\n    },\r\n\r\n    toRad: function (d) {\r\n      return d * Math.PI / 180.0;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 样式代码保持与之前一致 */\r\n.container {\r\n  padding: 16rpx;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n.header-image {\r\n  width: 100%;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.header-img {\r\n  width: 100%;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 定位信息的样式 */\r\n.location-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e6e6e6;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.location-text {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 定位按钮的样式 */\r\n.location-button {\r\n  background-color: #1aad19;\r\n  color: white;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 地图样式 */\r\n.map-container {\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n/* 其他样式保持不变 */\r\n.table-header {\r\n  display: flex;\r\n  background-color: #f0f0f0;\r\n  padding: 10rpx 20rpx;\r\n  font-weight: bold;\r\n  border-bottom: 1rpx solid #dcdcdc;\r\n}\r\n\r\n.header-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10rpx 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.table-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.call-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-left: 10rpx;\r\n  vertical-align: middle;\r\n}\r\n\r\n/* 固定定位信息和地图部分 */\r\n.fixed-section {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n}\r\n\r\n/* 定位信息的样式 */\r\n.location-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e6e6e6;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 地图样式 */\r\n.map-container {\r\n  width: 100%;\r\n  height: 300px;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 表头固定 */\r\n.table-header {\r\n  display: flex;\r\n  background-color: #f0f0f0;\r\n  padding: 10rpx 20rpx;\r\n  font-weight: bold;\r\n  border-bottom: 1rpx solid #dcdcdc;\r\n}\r\n\r\n.header-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 滚动区域的样式 */\r\n.scroll-view {\r\n  margin-top: 420rpx; /* 确保滚动区域在固定部分下方 */\r\n  height: calc(100vh - 420rpx); /* 动态计算滚动视图高度 */\r\n  overflow-y: scroll;\r\n  padding: 16rpx;\r\n}\r\n\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10rpx 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.table-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.call-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-left: 10rpx;\r\n  vertical-align: middle;\r\n}\r\n\r\n/* 样式代码保持与之前一致 */\r\n.container {\r\n  padding: 16rpx;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n.header-image {\r\n  width: 100%;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.header-img {\r\n  width: 100%;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 定位信息的样式 */\r\n.location-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e6e6e6;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.location-text {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 定位按钮的样式 */\r\n.location-button {\r\n  background-color: #1aad19;\r\n  color: white;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 地图样式 */\r\n.map-container {\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.location-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e6e6e6;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.map-container {\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.navigation-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10rpx;\r\n  background-color: #fff;\r\n  border: 1rpx solid #ddd;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.station-name {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  flex: 1;  /* Ensures the text takes up the remaining space */\r\n}\r\n\r\n.navigate-button {\r\n  background-color: #f90;\r\n  color: white;\r\n  padding: 5rpx 20rpx;\r\n  border-radius: 10rpx;\r\n  font-size: 28rpx;\r\n  white-space: nowrap; /* Prevents button text from wrapping */\r\n}\r\n\r\n.table-header, .table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10rpx;\r\n  background-color: #f0f0f0;\r\n  margin-bottom: 5rpx;\r\n}\r\n\r\n.header-cell, .table-cell {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.call-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./maps.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115054744\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}