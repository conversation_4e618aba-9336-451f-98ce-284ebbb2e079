{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?2429", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?0ac4", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?4528", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?94ed", "uni-app:///zhaopin/components/navigationBar/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?2b51", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/navigationBar/index.vue?a2a6"], "names": ["data", "height", "paddingTop", "showHomeButton", "show", "navigationBarTextStyle", "props", "back", "type", "default", "hasHei", "zIndex", "background", "placeholderBg", "color", "fontSize", "title", "fixed", "backEvent", "backHomeEvent", "beforeMount", "a", "i", "r", "methods", "navigateBack", "runBack", "uni", "url", "delta", "navigateBackHome", "runBackHome", "toggleShow", "toggleHide", "watch", "handler", "n", "immediate"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0C9wB;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;EACA;EACAW;IACA;IACA;IACA;MACAC;IACA;IAEA;IACA;IACA;IACA;IACAC;IACAC;IACA;IACA;MACAtB;MACAC;MACAC;MACAE;IACA;EACA;EACAmB;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MAEA;QACAC;UACAC;QACA;MACA;QACAD;UACAE;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAJ;UACAE;QACA;MACA;QACAF;UACAC;QACA;MACA;IACA;IACAI;MACA,aACA;QACA5B;MACA;IACA;IACA6B;MACA;QACA;UACA7B;QACA;MACA;IACA;EACA;EACA8B;IACAvB;MACAwB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAzB;MACAuB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAxB;MACAsB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAvB;MACAqB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAtB;MACAoB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEArB;MACAmB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEApB;MACAkB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAnB;MACAiB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;IAEAlB;MACAgB;QACA;UACA;UACAC;UACA;QACA;MACA;MAEAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzRA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/components/navigationBar/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=770b5066&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/components/navigationBar/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=770b5066&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <view\r\n            class=\"navigation-bar\"\r\n            :style=\"\r\n                'padding-top:' +\r\n                paddingTop +\r\n                'px;height:' +\r\n                height +\r\n                'px;line-height:' +\r\n                height +\r\n                'px;background:' +\r\n                background +\r\n                ';color:' +\r\n                color +\r\n                ';font-size:' +\r\n                fontSize +\r\n                ';position:' +\r\n                (fixed && 'fixed') +\r\n                ';transform:translateY(' +\r\n                (!show ? -paddingTop - height + 'px' : '0') +\r\n                ');z-index:' +\r\n                zIndex\r\n            \"\r\n        >\r\n            <view class=\"back\" :style=\"'padding-top:' + paddingTop + 'px;height:' + height + 'px;line-height:' + height + 'px;'\" v-if=\"back\">\r\n                <view class=\"style-simple\">\r\n                    <view @tap=\"navigateBackHome\" class=\"iconfont iconhome1\" style=\"\" v-if=\"showHomeButton\"></view>\r\n                    <view @tap=\"navigateBack\" class=\"iconfont iconarrow_left\" v-else></view>\r\n                </view>\r\n            </view>\r\n            <view class=\"title\">{{ title != 'none' ? title : '' }}</view>\r\n        </view>\r\n        <view\r\n            class=\"navigation-bar-holder\"\r\n            :style=\"'background:' + placeholderBg + ';padding-top:' + paddingTop + 'px;height:' + height + 'px;line-height:' + height + 'px;width:750rpx;'\"\r\n            v-if=\"fixed && show && hasHei\"\r\n        ></view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    data() {\r\n        return {\r\n            height: 44,\r\n            paddingTop: 20,\r\n            showHomeButton: false,\r\n            show: true,\r\n            navigationBarTextStyle: 'black'\r\n        };\r\n    },\r\n    props: {\r\n        back: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        hasHei: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        zIndex: {\r\n            type: Number,\r\n            default: 99\r\n        },\r\n        background: {\r\n            type: String,\r\n            default: '#ffffff'\r\n        },\r\n        placeholderBg: {\r\n            type: String,\r\n            default: 'transparent'\r\n        },\r\n        color: {\r\n            type: String,\r\n            default: '#000000'\r\n        },\r\n        fontSize: {\r\n            type: String,\r\n            default: '16px'\r\n        },\r\n        title: {\r\n            type: String,\r\n            default: 'none'\r\n        },\r\n        fixed: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        backEvent: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        backHomeEvent: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    beforeMount: function (t) {\r\n        var e = getCurrentPages();\r\n        var a = false;\r\n        if (e.length < 2 && e[0].route != __wxConfig.pages[0]) {\r\n            a = true;\r\n        }\r\n\r\n        var n = uni.getSystemInfoSync();\r\n        var o = uni.getMenuButtonBoundingClientRect();\r\n        var i = 20;\r\n        var r = 44;\r\n        i = n.statusBarHeight;\r\n        r = o.top + o.bottom - 2 * n.statusBarHeight;\r\n        var s = __wxConfig.global.window.navigationBarTextStyle;\r\n        this.setData({\r\n            height: r,\r\n            paddingTop: i,\r\n            showHomeButton: a,\r\n            navigationBarTextStyle: s\r\n        });\r\n    },\r\n    methods: {\r\n        navigateBack: function () {\r\n            if (this.backEvent) {\r\n                this.$emit('back');\r\n            } else {\r\n                this.runBack();\r\n            }\r\n        },\r\n        runBack: function () {\r\n            var t = getCurrentPages();\r\n\r\n            if (t.length < 2 && t[0].route != __wxConfig.pages[0]) {\r\n                uni.reLaunch({\r\n                    url: '/' + __wxConfig.pages[0]\r\n                });\r\n            } else {\r\n                uni.navigateBack({\r\n                    delta: 1\r\n                });\r\n            }\r\n        },\r\n        navigateBackHome: function () {\r\n            if (this.backHomeEvent) {\r\n                this.$emit('backHome');\r\n            } else {\r\n                this.runBackHome();\r\n            }\r\n        },\r\n        runBackHome: function () {\r\n            if (getCurrentPages()[0].route === __wxConfig.pages[0]) {\r\n                uni.navigateBack({\r\n                    delta: 10\r\n                });\r\n            } else {\r\n                uni.reLaunch({\r\n                    url: '/' + __wxConfig.pages[0]\r\n                });\r\n            }\r\n        },\r\n        toggleShow: function () {\r\n            this.show ||\r\n                this.setData({\r\n                    show: true\r\n                });\r\n        },\r\n        toggleHide: function () {\r\n            if (this.show) {\r\n                this.setData({\r\n                    show: false\r\n                });\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        zIndex: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        background: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        placeholderBg: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        color: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        fontSize: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        title: {\r\n            handler: function (t, e, a) {\r\n                if (!t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        fixed: {\r\n            handler: function (t, e, a) {\r\n                if (false !== t && true !== t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        backEvent: {\r\n            handler: function (t, e, a) {\r\n                if (false !== t && true !== t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        },\r\n\r\n        backHomeEvent: {\r\n            handler: function (t, e, a) {\r\n                if (false !== t && true !== t) {\r\n                    var n = {};\r\n                    n[a[0]] = e;\r\n                    this.setData(n);\r\n                }\r\n            },\r\n\r\n            immediate: true\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style>\r\n@import './index.css';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066569\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}