{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?10a8", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?3da6", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?8da3", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?4266", "uni-app:///yuyue/packageappoint.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?4718", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/packageappoint.vue?bd59"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "packageOrderId", "productId", "packageOrderInfo", "packageInfo", "productFwtype", "fwtype", "addressInfo", "selectedAddressId", "linkman", "tel", "loading", "order_flow_mode", "computed", "canSubmit", "onLoad", "console", "app", "methods", "t", "getPackageOrderInfo", "order_id", "that", "item", "getProductInfo", "id", "selectServiceType", "loadAddressInfo", "type", "goToAddressPage", "inputLinkman", "inputTel", "submitAppoint", "product_id", "formdata", "params"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmwB,CAAgB,8wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8EvxB;AAAA,eACA;EACAC;IACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;;EAEAC;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EAEAC;IACAC;IAEA;MACA;MACA;MAEA;;MAEA;MACA;MACA;MACA;MACA;IAEA;MACAC;MACA;IACA;EACA;;EAEAC;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAH;MAEAA;QACAI;MACA;QACA;;QAEA;UACAL;UACAM;;UAEA;UACA;YACA;cAAA,OACAC;YAAA,EACA;YAEA;cACAD;cACAN;;cAEA;cACAM;YAEA;cACAL;cACAK;YACA;UACA;YACAL;YACAK;UACA;QAEA;UACAL;UACAK;QACA;MACA;QACAL;QACAA;QACAK;MACA;IACA;IAEA;IACAE;MACA;MACAR;MACA;;MAEAC;QAAA;QACAQ;MACA;QACAR;;QAEA;UACAD;;UAEA;UACA;YACAM;YACAN;;YAEA;YACA;cAAA;cACAM;cACAA;YACA;cAAA;cACAA;YACA;UACA;YACAN;YACAM;YACAA;YACAA;UACA;;UAEA;UACA;YACAA;YACAA;YACAN;UACA;QAEA;UACAA;UACAM;UACAA;UACAA;QACA;QACA;MACA;QACAL;QACAD;QACAM;QACAA;QACAA;QACA;MACA;IACA;IAEA;IACAI;MACAV;MACA;MAEA;QACA;QACA;MACA;IACA;IAEA;IACAW;MACA;MACAV;;MAEA;MACAA;QAAAW;MAAA;QAAA;QACAX;QAEA;UACAD;UACA;UACA;YAAA;UAAA;UACA;YACAA;YACAM;YACAA;UACA;YACA;YACAN;YACAM;YACAA;UACA;QACA;UACAN;UACAM;UACAA;QACA;MACA;QACAL;QACAD;QACAM;QACAA;MACA;IACA;IAEA;IACAO;MACA;MACAb;MACAC;IACA;IAEA;IACAa;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACAf;UACA;QACA;QAEA;UACAA;UACA;QACA;QAEA;MACA;MAEAA;QACAA;;QAEA;QACA;UACAI;UACAY;UACA3B;UAAA;UACAM;UAAA;UACAsB;QACA;;QAEA;QACA;UACAC;QACA;UACA;UACAA;UACAA;QACA;QAEAnB;;QAEA;QACAC;UACAA;UAEA;YACAA;cACA;cACA;gBACAA;cACA;gBACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;QACA;UACAA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1XA;AAAA;AAAA;AAAA;AAAmlC,CAAgB,+jCAAG,EAAC,C;;;;;;;;;;;ACAvmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/packageappoint.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/packageappoint.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./packageappoint.vue?vue&type=template&id=02a487eb&\"\nvar renderjs\nimport script from \"./packageappoint.vue?vue&type=script&lang=js&\"\nexport * from \"./packageappoint.vue?vue&type=script&lang=js&\"\nimport style0 from \"./packageappoint.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/packageappoint.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageappoint.vue?vue&type=template&id=02a487eb&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.fwtype == 1 ? _vm.t(\"color1\") : null\n  var m1 = _vm.fwtype == 1 ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.fwtype == 1 ? _vm.t(\"color1\") : null\n  var m3 = _vm.fwtype == 2 ? _vm.t(\"color1\") : null\n  var m4 = _vm.fwtype == 2 ? _vm.t(\"color1rgb\") : null\n  var m5 = _vm.fwtype == 2 ? _vm.t(\"color1\") : null\n  var m6 = _vm.canSubmit ? _vm.t(\"color1\") : null\n  var m7 = _vm.canSubmit ? _vm.t(\"color1rgb\") : null\n  var m8 = _vm.canSubmit ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageappoint.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageappoint.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 套餐信息卡片 -->\n    <view class=\"card order-info-card\" v-if=\"packageInfo\">\n      <view class=\"product-info\">\n        <image :src=\"packageInfo.product_pic || packageInfo.service_pic\" mode=\"aspectFill\" class=\"product-image\"></image>\n        <view class=\"info\">\n          <text class=\"name\">{{ packageInfo.product_name || packageInfo.service_name }}</text>\n          <text class=\"package\">来自套餐：{{ packageOrderInfo.package_name }}</text>\n        </view>\n      </view>\n      <view class=\"order-detail\">\n        <view class=\"detail-item\">\n          <text class=\"label\">套餐订单号：</text>\n          <text class=\"value\">{{ packageOrderInfo.ordernum }}</text>\n        </view>\n        <view class=\"detail-item\">\n          <text class=\"label\">剩余次数：</text>\n          <text class=\"value\">{{ packageInfo.remain_num || packageInfo.remain_times }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 服务方式与信息卡片 -->\n    <view class=\"card service-info-card\">\n      <!-- 服务方式选择 -->\n      <view class=\"body_item service-method-item\">\n        <view class=\"body_title flex flex-bt\">服务方式<text class=\"body_text\">请选择服务方式</text></view>\n        <view class=\"body_content\">\n          <view class=\"body_tag\" \n            :class=\"fwtype == 1 ? 'body_active' : ''\"\n            :style=\"fwtype == 1 ? 'border-color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''\"\n            @tap=\"selectServiceType(1)\">到店服务\n          </view>\n          <view class=\"body_tag\" \n            :class=\"fwtype == 2 ? 'body_active' : ''\"\n            :style=\"fwtype == 2 ? 'border-color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''\"\n            @tap=\"selectServiceType(2)\">上门服务\n          </view>\n        </view>\n      </view>\n\n      <!-- 地址选择 (上门) -->\n      <view class=\"address-section\" v-if=\"fwtype == 2\" @tap=\"goToAddressPage\">\n        <view class=\"address-content flex-y-center\">\n          <image class=\"location-icon\" src=\"/static/img/address.png\" />\n          <view class=\"address-details flex1\" v-if=\"addressInfo && addressInfo.id\">\n            <view class=\"user-info\">{{addressInfo.name}} {{addressInfo.tel}} <text v-if=\"addressInfo.company\">{{addressInfo.company}}</text></view>\n            <view class=\"address-text\">{{addressInfo.area}} {{addressInfo.address}}</view>\n          </view>\n          <view v-else class=\"placeholder flex1\">请选择服务地址</view>\n          <image src=\"/static/img/arrowright.png\" class=\"arrow-icon\"></image>\n        </view>\n      </view>\n\n      <!-- 联系信息 (到店) -->\n      <view class=\"contact-section\" v-if=\"fwtype==1\">\n        <view class=\"linkitem\">\n          <text class=\"label\">联 系 人</text>\n          <input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-class=\"input-placeholder\"/>\n        </view>\n        <view class=\"linkitem\">\n          <text class=\"label\">联系电话</text>\n          <input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-class=\"input-placeholder\"/>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"footer-placeholder\"></view> <!-- 底部按钮占位 -->\n    <view class=\"footer\">\n      <button class=\"submit-btn\" :disabled=\"!canSubmit\" \n        :style=\"canSubmit ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%); box-shadow: 0 6rpx 12rpx rgba('+t('color1rgb')+',0.3)' : 'background:#ccc'\" \n        @tap=\"submitAppoint\">确认使用</button>\n    </view>\n  </view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n      // 套餐订单相关\n      packageOrderId: '', // 套餐订单ID\n      productId: '', // 服务/商品ID\n      packageOrderInfo: {}, // 套餐订单信息\n      packageInfo: {}, // 服务项信息\n\n      // 服务方式选择\n      productFwtype: 1, // 产品支持的服务方式: 1到店 2上门 3两者都支持\n      fwtype: 1, // 默认选择到店服务\n      \n      // 地址选择\n      addressInfo: null, // 用户选择的地址信息\n      selectedAddressId: 0, // 选中的地址ID\n\n      // 联系人信息\n      linkman: '',\n      tel: '',\n\n      // UI状态\n      loading: true,\n      order_flow_mode: 1, // 设置为简化模式\n    }\n  },\n  \n  computed: {\n    canSubmit() {\n      // 验证是否可以提交\n      if (this.fwtype == 2) {\n        // 如果是上门服务，必须选择地址\n        return this.addressInfo && this.selectedAddressId > 0;\n      } else {\n        // 如果是到店服务，必须填写联系人和电话\n        return this.linkman && this.tel;\n      }\n    }\n  },\n  \n  onLoad(options) {\n    console.log('加载套餐使用页面，参数:', options);\n    \n    if (options && options.package_order_id && options.product_id) {\n      this.packageOrderId = options.package_order_id;\n      this.productId = options.product_id;\n      \n      this.getPackageOrderInfo(); // 会触发 getProductInfo\n      \n      // 注意：默认服务方式的设置移到 getProductInfo 内部，\n      // 因为需要先判断商品是否支持上门服务。\n      // 如果需要强制默认上门（无论商品是否支持），可以在 getProductInfo 回调后设置\n      // this.fwtype = 2;\n      // this.loadAddressInfo();\n      \n    } else {\n      app.error('缺少必要参数');\n      this.loading = false; // 参数错误也要结束loading\n    }\n  },\n  \n  methods: {\n    t(name) {\n      return app.globalData.config && app.globalData.config.t ? app.globalData.config.t(name) : '';\n    },\n    \n    // 获取套餐订单信息\n    getPackageOrderInfo() {\n      var that = this;\n      app.showLoading('加载套餐信息');\n      \n      app.post('ApiYuyuePackage/getPackageOrderDetail', { \n        order_id: that.packageOrderId \n      }, function(res) {\n        // app.showLoading(false); // loading在getProductInfo后结束\n        \n        if (res.status == 1 && res.data) {\n          console.log('套餐订单信息:', res.data);\n          that.packageOrderInfo = res.data;\n          \n          // 找到对应的服务项\n          if (res.data.items && res.data.items.length > 0) {\n            const serviceItem = res.data.items.find(item => \n              item.product_id == that.productId || item.service_id == that.productId\n            );\n            \n            if (serviceItem) {\n              that.packageInfo = serviceItem;\n              console.log('找到的服务项信息:', serviceItem);\n              \n              // 获取商品详情以获取fwtype 和 默认联系方式\n              that.getProductInfo(that.productId);\n              \n            } else {\n              app.error('未找到指定的服务项');\n              that.loading = false;\n            }\n          } else {\n            app.error('套餐中没有服务项');\n            that.loading = false;\n          }\n          \n        } else {\n          app.error(res.msg || '获取套餐订单详情失败');\n          that.loading = false;\n        }\n      }, function() {\n        app.showLoading(false);\n        app.error('请求失败');\n        that.loading = false;\n      });\n    },\n    \n    // 获取商品详情，主要是获取fwtype 和 默认联系方式\n    getProductInfo(productId) {\n      var that = this;\n      console.log('正在获取商品信息，ID:', productId);\n      // app.showLoading('获取商品信息'); // 减少loading次数\n      \n      app.post('ApiYuyue/product', { // 调用商品详情接口\n        id: productId \n      }, function(res) {\n        app.showLoading(false); // 在这里结束loading\n        \n        if (res.status == 1 && res.data) {\n          console.log('商品详情:', res.data);\n          \n          // 获取商品支持的服务方式\n          if (res.data.product && res.data.product.fwtype !== undefined) {\n            that.productFwtype = parseInt(res.data.product.fwtype || '0');\n            console.log('商品支持的服务方式:', that.productFwtype);\n            \n            // 设置默认服务方式为上门 (fwtype=2)，如果商品支持的话\n            if (that.productFwtype === 2 || that.productFwtype === 3) { // 支持上门或两者都支持\n              that.fwtype = 2;\n              that.loadAddressInfo(); // 加载地址信息\n            } else { // 只支持到店\n              that.fwtype = 1;\n            }\n          } else {\n            console.log('商品未设置服务方式，默认设置为上门');\n            that.productFwtype = 3; // 假设支持所有\n            that.fwtype = 2; // 默认上门\n            that.loadAddressInfo(); // 加载地址信息\n          }\n          \n          // 获取联系人和电话 (即使默认上门，也可能需要备用)\n          if (res.data.linkman || res.data.tel) {\n             that.linkman = res.data.linkman || '';\n             that.tel = res.data.tel || '';\n             console.log('获取到默认联系方式:', that.linkman, that.tel);\n          }\n          \n        } else {\n          console.log('获取商品详情失败，默认设置为上门');\n          that.productFwtype = 3; // 假设支持所有\n          that.fwtype = 2; // 默认上门\n          that.loadAddressInfo(); // 加载地址信息\n        }\n        // that.loading = false; // loading移到回调开始处结束\n      }, function(err) {\n        app.showLoading(false); // 错误时也要结束loading\n        console.log('请求商品详情出错，默认设置为上门:', err);\n        that.productFwtype = 3; // 假设支持所有\n        that.fwtype = 2; // 默认上门\n        that.loadAddressInfo(); // 加载地址信息\n        // that.loading = false;\n      });\n    },\n    \n    // 选择服务方式\n    selectServiceType(type) {\n      console.log('选择服务方式:', type);\n      this.fwtype = type;\n      \n      if (type == 2) {\n        // 选择了上门服务，加载地址信息\n        this.loadAddressInfo();\n      }\n    },\n    \n    // 加载地址信息\n    loadAddressInfo() {\n      var that = this;\n      app.showLoading('获取地址信息');\n      \n      // 通过地址列表接口获取地址，默认地址排在最前\n      app.post('ApiAddress/address', { type: 0 }, function(res) { // type=0 获取所有地址\n        app.showLoading(false);\n        \n        if (res.status == 1 && res.data && res.data.length > 0) {\n          console.log('获取到地址列表:', res.data);\n          // 查找默认地址 (isdefault=1)\n          let defaultAddress = res.data.find(item => item.isdefault == 1);\n          if (defaultAddress) {\n            console.log('找到默认地址:', defaultAddress);\n            that.addressInfo = defaultAddress;\n            that.selectedAddressId = defaultAddress.id;\n          } else {\n            // 如果没有默认地址，选择列表中的第一个地址作为备选\n            console.log('未找到默认地址，使用列表第一个地址');\n            that.addressInfo = res.data[0];\n            that.selectedAddressId = res.data[0].id;\n          }\n        } else {\n          console.log('地址列表为空或获取失败');\n          that.addressInfo = null;\n          that.selectedAddressId = 0;\n        }\n      }, function(err) {\n        app.showLoading(false);\n        console.log('获取地址列表失败:', err);\n        that.addressInfo = null;\n        that.selectedAddressId = 0;\n      });\n    },\n    \n    // 跳转到地址选择页面\n    goToAddressPage() {\n      var url = '/pages/address/' + (this.addressInfo && this.addressInfo.id ? 'address' : 'addressadd') + '?fromPage=packageappoint&type=1';\n      console.log('跳转到地址页面:', url);\n      app.goto(url);\n    },\n    \n    // 处理联系人输入\n    inputLinkman(e) {\n      this.linkman = e.detail.value;\n    },\n    \n    // 处理电话输入\n    inputTel(e) {\n      this.tel = e.detail.value;\n    },\n    \n    // 提交预约\n    submitAppoint() {\n      var that = this;\n      \n      if (!that.canSubmit) {\n        if (that.fwtype == 2 && (!that.addressInfo || that.selectedAddressId <= 0)) {\n          app.error('请选择服务地址');\n          return;\n        }\n        \n        if (that.fwtype == 1 && (!that.linkman || !that.tel)) {\n          app.error('请填写联系人和电话');\n          return;\n        }\n        \n        return;\n      }\n      \n      app.confirm('确认使用套餐服务吗？', function() {\n        app.showLoading('提交中');\n        \n        // 构建请求参数\n        const params = {\n          order_id: that.packageOrderId,\n          product_id: that.productId,\n          fwtype: that.fwtype, // 添加服务方式参数\n          order_flow_mode: 1,  // 设置为简化模式\n          formdata: {} // 如果需要表单数据，可以在这里添加\n        };\n        \n        // 如果是上门服务，添加地址ID\n        if (that.fwtype == 2 && that.addressInfo) {\n          params.address_id = that.selectedAddressId;\n        } else if (that.fwtype == 1) {\n          // 如果是到店服务，添加联系人和电话\n          params.linkman = that.linkman;\n          params.tel = that.tel;\n        }\n        \n        console.log('提交预约参数:', params);\n        \n        // 调用使用套餐服务接口\n        app.post('ApiYuyuePackage/usePackageService', params, function(res) {\n          app.showLoading(false);\n          \n          if (res.status == 1) {\n            app.success(res.msg || '预约成功', function() {\n              // 如果返回了预约订单ID，跳转到预约详情页\n              if (res.data && res.data.yuyue_order_id) {\n                app.goto('/yuyue/orderdetail?id=' + res.data.yuyue_order_id);\n              } else {\n                // 否则返回套餐详情页\n                app.goto('/yuyue/packageorderdetail?order_id=' + that.packageOrderId);\n              }\n            });\n          } else {\n            app.error(res.msg || '预约失败');\n          }\n        }, function() {\n          app.showLoading(false);\n          app.error('请求失败');\n        });\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n  padding: 20rpx 20rpx 140rpx; /* 增加底部padding给按钮留空间 */\n  min-height: 100vh;\n  background-color: #f7f8fa; /* 更柔和的背景色 */\n  box-sizing: border-box;\n}\n\n.card {\n  background: #fff;\n  border-radius: 16rpx; /* 更大的圆角 */\n  padding: 30rpx; /* 增加内边距 */\n  margin-bottom: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 更柔和的阴影 */\n}\n\n/* 套餐信息卡片 */\n.order-info-card .product-info {\n  display: flex;\n  align-items: center; /* 垂直居中 */\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 20rpx;\n}\n\n.order-info-card .product-image {\n  width: 100rpx; /* 略微缩小图片 */\n  height: 100rpx;\n  border-radius: 8rpx;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.order-info-card .info {\n  flex: 1;\n}\n\n.order-info-card .name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  line-height: 1.4;\n  margin-bottom: 8rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.order-info-card .package {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.order-info-card .order-detail {\n  font-size: 26rpx; /* 统一字体大小 */\n  color: #666;\n}\n\n.order-info-card .order-detail .detail-item {\n  display: flex;\n  justify-content: space-between;\n  line-height: 1.6;\n}\n\n.order-info-card .order-detail .label {\n  color: #666;\n}\n\n.order-info-card .order-detail .value {\n  color: #333;\n}\n\n/* 服务方式与信息卡片 */\n.service-info-card {\n  /* 可以在这里添加卡片特定样式 */\n}\n\n.service-method-item {\n  padding-bottom: 30rpx !important;\n  border-bottom: 1rpx solid #f0f0f0 !important; /* 加粗分割线 */\n}\n\n.body_title {\n  font-size: 30rpx; /* 稍大标题 */\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.body_text {\n  font-size: 24rpx;\n  font-weight: normal;\n  color: #999;\n}\n\n.body_content {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 20rpx;\n}\n\n.body_tag {\n  padding: 12rpx 30rpx; /* 调整标签内边距 */\n  height: auto;\n  min-height: 54rpx;\n  line-height: normal;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  background: #f7f8fa;\n  border: 2rpx solid #f7f8fa; /* 默认边框与背景同色 */\n  border-radius: 30rpx; /* 更圆的标签 */\n  margin-right: 20rpx;\n  margin-bottom: 20rpx; /* 增加底部间距 */\n  font-size: 26rpx; /* 标签字体 */\n  color: #666;\n  transition: all 0.2s;\n}\n\n.body_active {\n  background: rgba(252, 67, 67, 0.08) !important; /* 选中背景色，使用主题色 */\n  border-color: #FC4343 !important; /* 选中边框色，使用主题色 */\n  color: #FC4343 !important; /* 选中文字颜色，使用主题色 */\n  font-weight: bold;\n}\n\n/* 地址选择 */\n.address-section {\n  padding: 30rpx 0 10rpx; /* 调整上下边距 */\n}\n\n.address-content {\n  display: flex;\n  align-items: center;\n  min-height: 100rpx; /* 保证足够高度 */\n}\n\n.location-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.address-details {\n  flex: 1;\n  overflow: hidden; /* 防止文本溢出 */\n}\n\n.user-info {\n  font-weight: bold;\n  color: #333;\n  font-size: 30rpx;\n  margin-bottom: 8rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.address-text {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.placeholder {\n  color: #999;\n  font-size: 28rpx;\n}\n\n.arrow-icon {\n  width: 28rpx;\n  height: 28rpx;\n  margin-left: 20rpx;\n  flex-shrink: 0;\n}\n\n/* 联系信息 */\n.contact-section {\n  padding-top: 30rpx;\n}\n\n.contact-section .linkitem {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0; /* 增加垂直间距 */\n  border-bottom: 1rpx solid #f0f0f0;\n}\n.contact-section .linkitem:last-child {\n  border-bottom: none;\n}\n\n.contact-section .label {\n  width: 140rpx; /* 调整标签宽度 */\n  color: #333;\n  font-size: 28rpx;\n  flex-shrink: 0;\n}\n\n.contact-section .input {\n  flex: 1;\n  height: 50rpx;\n  line-height: 50rpx;\n  font-size: 28rpx;\n  color: #333;\n  text-align: right;\n}\n\n.input-placeholder {\n  color: #ccc; /* 更浅的占位符颜色 */\n  font-size: 28rpx;\n  text-align: right;\n}\n\n\n/* 底部确认按钮 */\n.footer-placeholder {\n  height: 130rpx; /* 占位高度，等于footer高度+一些额外间距 */\n}\n.footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom)); /* 适配iPhone X等底部安全区 */\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.06);\n  z-index: 10;\n}\n\n.submit-btn {\n  height: 88rpx;\n  line-height: 88rpx;\n  border-radius: 44rpx;\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #fff;\n  border: none;\n  text-align: center;\n}\n\n.submit-btn:after {\n  display: none;\n}\n\n.submit-btn[disabled] {\n  opacity: 0.6;\n  box-shadow: none;\n}\n\n/* 继承buy.vue的必要样式，去除冗余 */\n.flex-bt {\n\tjustify-content: space-between;\n}\n\n.flex-y-center {\n  display: flex;\n  align-items: center;\n}\n\n.flex1 {\n  flex: 1;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageappoint.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./packageappoint.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115040499\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}