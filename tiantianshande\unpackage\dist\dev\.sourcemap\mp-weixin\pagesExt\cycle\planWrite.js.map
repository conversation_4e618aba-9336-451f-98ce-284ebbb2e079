{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?ab28", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?f021", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?5194", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?0250", "uni-app:///pagesExt/cycle/planWrite.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?0a1a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?b9bb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?731a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/planWrite.vue?fc1f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "pre_url", "product", "isload", "loading", "num", "qsnum", "min_num", "min_qsnum", "gui<PERSON><PERSON>", "ggselected", "nowguige", "specsIndex", "rateList", "rateIndex", "totalprice", "ks", "startDate", "week", "pspl", "qsnumState", "numState", "ps_cycle", "onLoad", "onShow", "uni", "that", "methods", "tobuy", "app", "getdata", "id", "console", "ggchange", "rateClick", "qsplus", "<PERSON><PERSON><PERSON>", "gwcplus", "gwcminus", "getQsTotal", "getTotal", "toCheckDate", "type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACa;AACwB;;;AAG7F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqFlxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAA;IACA;EAEA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;MACA;MACA;MACAA;IACA;IACAC;MACA;MACAJ;MACAG;QAAAE;MAAA;QACAL;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;UACA;UACA;YACAhB;UACA;UACAgB;UACAM;UACAN;UACAA;UACAA;UACAA;UACA;YAAA;YACAA;UACA;UACAA;UACAA;QACA;UACAG;QACA;MACA;IACA;IACAI;MACA;MACA;MACA;MACAvB;MACA;MACAsB;MACA;MACA;MACA;MACAA;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAE;MACA;MACA;MAEA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACAN;MACA;QACA;QACAA;MACA;QACA;MACA;MACA;IACA;IACAO;MAAA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACAR;MACA;QACA;QACAA;MACA;QACA;MACA;MACA;IACA;IACAS;MAAA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACAV;MACA;QACA;QACAA;MACA;QACA;MACA;MACA;MACA;IACA;IACAW;MACA;QACA;QACAX;MACA;QACA;QACAA;MACA;QACA;MACA;MACA;MACA;IACA;IACAY;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1SA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAsmC,CAAgB,klCAAG,EAAC,C;;;;;;;;;;;ACA1nC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/planWrite.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/planWrite.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./planWrite.vue?vue&type=template&id=6f5c83ee&scoped=true&\"\nvar renderjs\nimport script from \"./planWrite.vue?vue&type=script&lang=js&\"\nexport * from \"./planWrite.vue?vue&type=script&lang=js&\"\nimport style0 from \"./planWrite.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./planWrite.vue?vue&type=style&index=1&id=6f5c83ee&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f5c83ee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/planWrite.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=template&id=6f5c83ee&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.product.status == 1 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=script&lang=js&\"", "<template>\n\t<view >\n\t\t<view class=\"page\"  v-if=\"isload\">\n\t\t\t<view class=\"head\">\n\t\t\t\t<img class=\"head_img\" @tap=\"previewImage\" :src=\"nowguige.pic || product.pic\" :data-url=\"nowguige.pic || product.pic\" alt=\"\" />\n\t\t\t\t<view>\n\t\t\t\t\t<view class=\"head_title\">{{product.name}}</view>\n\t\t\t\t\t<view class=\"head_text\">{{nowguige.name}} | {{product.ps_cycle_title}}</view>\n\t\t\t\t\t<view class=\"head_price\">\n\t\t\t\t\t\t<text class=\"head_icon\">￥</text>\n\t\t\t\t\t\t<text v-if=\"nowguige.sell_price > 0\">{{nowguige.sell_price}}</text>\n\t\t\t\t\t\t<text v-else>{{product.sell_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"body\">\n\t\t\t\t<view class=\"body_item\" v-for=\"(item, index) in guigedata\">\n\t\t\t\t\t<view class=\"body_title flex flex-bt\">{{item.title}}<text class=\"body_text\">请选择周期购计划</text></view>\n\t\t\t\t\t<view class=\"body_content\">\n\t\t\t\t\t\t<view v-for=\"(item2, index2) in item.items\" :key=\"index\" :data-itemk=\"item.k\" :data-idx=\"item2.k\" @tap=\"ggchange\" class=\"body_tag\" :class=\"ggselected[item.k]==item2.k?'body_active':''\">{{item2.title}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"body_item\" >\n\t\t\t\t\t<view class=\"body_title flex flex-bt\">配送时间<text class=\"body_text\">{{product.ps_cycle_title}}</text></view>\n\t\t\t\t\t<view class=\"body_content\" v-if=\"product.ps_cycle == 1\">\n\t\t\t\t\t\t<view v-for=\"(item,index) in rateList\" :key=\"index\" @click=\"rateClick(index)\" class=\"body_tag\" :class=\"rateIndex===index?'body_active':''\">{{item.label}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"body_item\">\n\t\t\t\t\t<view class=\"body_title flex flex-bt\">\n\t\t\t\t\t\t<span>开始时间</span>\n\t\t\t\t\t\t<view class=\"body_data\" @click=\"toCheckDate\">{{week}} {{startDate?startDate:'请选择开始时间'}} <img class=\"body_detail\" :src=\"pre_url+'/static/img/week/week_detail.png'\" /> </view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"body_item\">\n\t\t\t\t\t<view class=\"body_title flex flex-bt\">\n\t\t\t\t\t\t<text>配送期数</text>\n\t\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t\t<text v-if=\"min_qsnum!=1\" class=\"body_notice\">{{min_qsnum}}期起订</text>\n\t\t\t\t\t\t\t<view class=\"body_data\">\n\t\t\t\t\t\t\t\t<img v-if=\"qsnumState\" @click=\"qsminus\" class=\"body_opt\" :class=\"qsnum<=min_qsnum?'body_disabled':''\" :src=\"pre_url+'/static/img/week/week_cut.png'\" />\n\t\t\t\t\t\t\t\t<img v-if=\"!qsnumState\" class=\"body_opt body_disabled\" :src=\"pre_url+'/static/img/week/week_cut.png'\" />\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<input @blur=\"getQsTotal\" type=\"number\" class=\"body_num\" :value=\"qsnum\"/>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<img @click=\"qsplus\" class=\"body_opt\" :src=\"pre_url+'/static/img/week/week_add.png'\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"body_item\">\n\t\t\t\t\t<view class=\"body_title flex flex-bt\">\n\t\t\t\t\t\t<text>每期数量</text>\n\t\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t\t<text v-if=\"min_num!=1\" class=\"body_notice\">{{min_num}}件起订</text>\n\t\t\t\t\t\t\t<view class=\"body_data\">\n\t\t\t\t\t\t\t\t<img v-if=\"numState\" @click=\"gwcminus\" class=\"body_opt\" :class=\"num<=min_num?'body_disabled':''\" :src=\"pre_url+'/static/img/week/week_cut.png'\" />\n\t\t\t\t\t\t\t\t<img v-if=\"!numState\" class=\"body_opt body_disabled\" :src=\"pre_url+'/static/img/week/week_cut.png'\" />\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<input @blur=\"getTotal\" type=\"number\" class=\"body_num\" :value=\"num\"/>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<img @click=\"gwcplus\" class=\"body_opt\" :src=\"pre_url+'/static/img/week/week_add.png'\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"bottombar flex-row flex-xy-center\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\n\t\t\t<view class=\"operate_data\">\n\t\t\t\t<view class=\"operate_text\">\n\t\t\t\t\t配送<text class=\"operate_color\">{{!qsnum?'':qsnum}}</text>期，共<text class=\"operate_color\">{{!num||!qsnum?'':num*qsnum}}</text>件商品\n\t\t\t\t</view>\n\t\t\t\t<view class=\"operate_price\">\n\t\t\t\t\t<text class=\"operate_lable\">总价：</text><text class=\"operate_tag\">￥</text><text class=\"operate_num\">{{!num||!qsnum?'':totalprice}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"tobuy flex1\"  @tap=\"tobuy\" :style=\"{background:t('color1')}\"><text>去结算</text></view>\n\t\t</view>\n\t\t<loading v-if=\"loading\"></loading>\n\t</view>\n\t\n</template>\n\n<script>\n\tvar app = getApp();\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\topt:{},\n\t\t\t\tpre_url: app.globalData.pre_url,\n\t\t\t\tproduct:{},\n\t\t\t\tisload: false,\n\t\t\t\tloading:false,\n\t\t\t\tnum: 1,\n\t\t\t\tqsnum: 1,\n\t\t\t\tmin_num:1,\n\t\t\t\tmin_qsnum:1,\n\t\t\t\tguigedata:{},\n\t\t\t\tggselected:{},\n\t\t\t\tnowguige:{},\n\t\t\t\tspecsIndex: 0,\n\t\t\t\trateList:{},\n\t\t\t\trateIndex: 0,\n\t\t\t\ttotalprice:0,\n\t\t\t\tks:'',\n\t\t\t\tstartDate:'',\n\t\t\t\tweek :'',\n\t\t\t\tpspl:'',//配送频率,\n\t\t\t\tqsnumState: true,\n\t\t\t\tnumState: true,\n\t\t\t\t\n\t\t\t\tps_cycle:''\n\t\t\t}\n\t\t},\n\t\tonLoad(opt) {\n\t\t\t this.opt = app.getopts(opt);\n\t\t\tthis.getdata();\n\t\t},\n\t\tonShow(){\n\t\t\tvar that  = this;\n\t\t\tuni.$on('selectedDate',function(data){\n\t\t\t\t\t\tthat.startDate = data.startStr.dateStr;\n\t\t\t\t\t\tthat.week = data.startStr.week\n\t\t\t\t\t})\n\t\t\t\t\t\n\t\t},\n\t\tmethods: {\n\t\t\ttobuy: function (e) {\n\t\t\t\tvar that = this;\n\t\t\t\tvar ks = that.ks;\n\t\t\t\tvar proid = that.product.id;\n\t\t\t\tvar ggid = that.guigelist[ks].id;\n\t\t\t\tvar stock = that.guigelist[ks].stock;\n\t\t\t\tvar num = that.num;\n\t\t\t\tif (num < 1) num = 1;\n\t\t\t\tif (stock < num) {\n\t\t\t\t\tapp.error('库存不足');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (that.startDate =='') {\n\t\t\t\t\tapp.error('请选择开始时间');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif(!that.qsnumState){\n\t\t\t\t\tapp.error('配送期数最小数量为' + that.min_qsnum);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif(!that.numState){\n\t\t\t\t\tapp.error('每期数量最小数量为' + that.min_num);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\n\t\t\t\tvar pspl_value = that.pspl?that.pspl.value:'';\n\t\t\t\tvar qsdata = that.startDate+','+that.qsnum+','+pspl_value;\n\t\t\t\tapp.goto('/pagesExt/cycle/buy?prodata=' + prodata+'&qsdata='+qsdata);\n\t\t\t},\n\t\t\tgetdata:function(){\n\t\t\t\tvar that = this;\n\t\t\t\t that.loading = true;\n\t\t\t\tapp.get('ApiCycle/product', {id:that.opt.id}, function (res) {\n\t\t\t\t\t that.loading = false;\n\t\t\t\t\tif(res.status==1){\n\t\t\t\t\t\tthat.loading = false;\n\t\t\t\t\t\tthat.ps_cycle = res.product.ps_cycle;\n\t\t\t\t\t\tthat.product = res.product;\n\t\t\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\t\t\tthat.guigelist = res.guigelist;\n\t\t\t\t\t\tthat.guigedata = res.guigedata;\n\t\t\t\t\t\tthat.rateList = res.product.everyday_item;\n\t\t\t\t\t\tthat.num  = res.product.min_num;\n\t\t\t\t\t\tthat.qsnum = res.product.min_qsnum;\n\t\t\t\t\t\tthat.min_num  = res.product.min_num;\n\t\t\t\t\t\tthat.min_qsnum  = res.product.min_qsnum;\n\t\t\t\t\t\tvar guigedata = res.guigedata;\n\t\t\t\t\t\tvar ggselected = [];\n\t\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\n\t\t\t\t\t\t\tggselected.push(0);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.ks = ggselected.join(','); \n\t\t\t\t\t\tconsole.log(that.ks,'ks');\n\t\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\n\t\t\t\t\t\tthat.ggselected = ggselected;\n\t\t\t\t\t\tthat.pspl = that.rateList?that.rateList[0]:'';\n\t\t\t\t\t\tthat.isload = true;\n\t\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\n\t\t\t\t\t\t\tthat.canaddcart = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.totalprice = (that.nowguige.sell_price * that.num * that.qsnum).toFixed(2);\n\t\t\t\t\t\tthat.isload = true;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.alert(res.msg)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tggchange(e){\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\n\t\t\t\tvar ggselected = this.ggselected;\n\t\t\t\tggselected[itemk] = idx;\n\t\t\t\tvar ks = ggselected.join(',');\n\t\t\t\tconsole.log(ks,'ks');\n\t\t\t\tthis.ggselected = ggselected;\n\t\t\t\tthis.ks = ks;\n\t\t\t\tthis.nowguige = this.guigelist[this.ks];\n\t\t\t\tconsole.log(this.nowguige,'nowguige');\n\t\t\t\tif(this.nowguige.limit_start > 0) {\n\t\t\t\t\tif (this.gwcnum < this.nowguige.limit_start) {\n\t\t\t\t\t\tthis.gwcnum = this.nowguige.limit_start;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.num * this.qsnum).toFixed(2);\n\t\t\t},\n\t\t\trateClick(e){\n\t\t\t\tthis.rateIndex = e;\n\t\t\t\tthis.pspl = this.rateList[e];\n\t\t\t\t\n\t\t\t\tthis.startDate = '';\n\t\t\t\tthis.week = '';\n\t\t\t},\n\t\t\tqsplus(){//期数加\n\t\t\t\tthis.qsnum +=1;\n\t\t\t\tif(this.qsnum==''){\n\t\t\t\t\tthis.qsnumState = false;\n\t\t\t\t\tapp.error('配送期数不能为空');\n\t\t\t\t}else if(this.qsnum<this.min_qsnum){\n\t\t\t\t\tthis.qsnumState = false;\n\t\t\t\t\tapp.error('配送期数最小数量为' + this.min_qsnum);\n\t\t\t\t}else{\n\t\t\t\t\tthis.qsnumState = true;\n\t\t\t\t}\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum * this.num).toFixed(2);\n\t\t\t},\n\t\t\tqsminus(){//期数减\n\t\t\t\tthis.qsnum==1?null:this.qsnum -=1;\n\t\t\t\tthis.qsnum = this.qsnum <= this.min_qsnum?this.min_qsnum:this.qsnum;\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum * this.num).toFixed(2);\n\t\t\t},\n\t\t\tgwcplus(){//数量加\n\t\t\t\tthis.num +=1;\n\t\t\t\tif(this.num==''){\n\t\t\t\t\tthis.numState = false;\n\t\t\t\t\tapp.error('每期数量不能为空');\n\t\t\t\t}else if(this.num<this.min_num){\n\t\t\t\t\tthis.numState = false;\n\t\t\t\t\tapp.error('每期数量最小数量为' + this.min_num);\n\t\t\t\t}else{\n\t\t\t\t\tthis.numState = true;\n\t\t\t\t}\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum* this.num).toFixed(2);\n\t\t\t},\n\t\t\tgwcminus(){//数量减\n\t\t\t\tthis.num==1?null:this.num -=1;\n\t\t\t\tthis.num = \tthis.num <= this.min_num?this.min_num:this.num;\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum * this.num).toFixed(2);\n\t\t\t},\n\t\t\tgetQsTotal(e){\n\t\t\t\tif(e.detail.value==''){\n\t\t\t\t\tthis.qsnumState = false;\n\t\t\t\t\tapp.error('配送期数不能为空');\n\t\t\t\t}else if(parseInt(e.detail.value)<this.min_qsnum){\n\t\t\t\t\tthis.qsnumState = false;\n\t\t\t\t\tapp.error('配送期数最小数量为' + this.min_qsnum);\n\t\t\t\t}else{\n\t\t\t\t\tthis.qsnumState = true;\n\t\t\t\t}\n\t\t\t\tthis.qsnum = parseInt(e.detail.value);\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum * this.num).toFixed(2);\n\t\t\t},\n\t\t\tgetTotal(e){\n\t\t\t\tif(e.detail.value==''){\n\t\t\t\t\tthis.numState = false;\n\t\t\t\t\tapp.error('每期数量不能为空');\n\t\t\t\t}else if(parseInt(e.detail.value)<this.min_num){\n\t\t\t\t\tthis.numState = false;\n\t\t\t\t\tapp.error('每期数量最小数量为' + this.min_num);\n\t\t\t\t}else{\n\t\t\t\t\tthis.numState = true;\n\t\t\t\t}\n\t\t\t\tthis.num = parseInt(e.detail.value);\n\t\t\t\tthis.totalprice = (this.nowguige.sell_price * this.qsnum * this.num).toFixed(2);\n\t\t\t},\n\t\t\ttoCheckDate(){\n\t\t\t\tlet type = ''\n\t\t\t\tif(this.ps_cycle=='2'){\n\t\t\t\t\ttype = 5\n\t\t\t\t}else if(this.ps_cycle=='3'){\n\t\t\t\t\ttype = 6\n\t\t\t\t}else{\n\t\t\t\t\ttype =this.pspl?this.pspl.value:''\n\t\t\t\t}\n\t\t\t\tapp.goto('/pagesExt/cycle/checkDate?date='+this.startDate+'&ys='+this.product.advance_pay_days+'&type='+type);\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style>\n\tpage {\n\t\tbackground: #f6f6f6;\n\t}\n</style>\n<style scoped>\n\t.page {\n\t\tpadding: 30rpx 30rpx 200rpx 30rpx;\n\t}\n\n\t.head {\n\t\tposition: relative;\n\t\twidth: 690rpx;\n\t\tpadding: 30rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 10rpx;\n\t\tbox-sizing: border-box;\n\t\tmargin: 0 auto;\n\t\tdisplay: flex;\n\t}\n\n\t.head_img {\n\t\twidth: 172rpx;\n\t\theight: 172rpx;\n\t\tborder-radius: 10rpx;\n\t\tmargin-right: 30rpx;\n\t}\n\n\t.head_title {\n\t\tfont-size: 28rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #323232;\n\t}\n\n\t.head_text {\n\t\tfont-size: 24rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #999999;\n\t\tmargin-top: 15rpx;\n\t}\n\n\t.head_price {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #FD4A46;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.head_icon {\n\t\tfont-size: 24rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tfont-weight: normal;\n\t\tcolor: #FD4A46;\n\t}\n\n\t.body {\n\t\tposition: relative;\n\t\twidth: 690rpx;\n\t\tpadding: 0 30rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 10rpx;\n\t\tbox-sizing: border-box;\n\t\tmargin: 20rpx auto 0 auto;\n\t}\n\n\t.body_item {\n\t\tpadding: 20px 0;\n\t\tborder-bottom: 1px solid #f6f6f6;\n\t}\n\n\t.body_item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.body_title {\n\t\tfont-size: 28rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #323232;\n\t}\n\n\t.body_text {\n\t\tfont-size: 24rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #999999;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.body_content {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.body_tag {\n\t\tpadding: 0 20rpx;\n\t\theight: 54rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #F4F4F4;\n\t\tborder-radius: 27rpx;\n\t\tmargin: 20rpx 10rpx 0 0;\n\t\tfont-size: 22rpx;\n\t\tborder: 1px solid rgba(0, 0, 0, 0);\n\t\tfont-family: PingFang SC;\n\t}\n\n\t.body_active {\n\t\tbackground: rgba(252, 67, 67, 0.1200);\n\t\tborder: 1px solid #FC4343;\n\t\tcolor: #FC4343;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.body_data {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: normal;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 500;\n\t\tcolor: #686868;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.body_detail {\n\t\theight: 35rpx;\n\t\twidth: 35rpx;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t.body_opt {\n\t\theight: 40rpx;\n\t\twidth: 40rpx;\n\t}\n\n\t.body_num {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #686868;\n\t\twidth: 100rpx;\n\t\ttext-align: center;\n\t\tpadding: 0 20rpx;\n\t}\n\t\n\t.body_notice{\n\t\tfont-size: 26rpx;\n\t\tcolor: #FC4343;\n\t\tmargin-left: 30rpx;\n\t\tfont-weight: normal;\n\t\tmargin-right: 30rpx;\n\t}\n\n\t.body_disabled {\n\t\topacity: 0.5;\n\t}\n\n\t.operate {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tbackground: #FFFFFF;\n\t\tbox-sizing: border-box;\n\t\tpadding: 15rpx 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0rpx 0rpx 18rpx 0rpx rgba(132, 132, 132, 0.3200);\n\t}\n\n\t.operate_data {\n\t\tflex: 1;\n\t\tmargin-left: 30rpx;\n\t}\n\n\t.operate_text {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.operate_color {\n\t\tcolor: #FC4343;\n\t}\n\n\t.operate_price {\n\t\tfont-size: 24rpx;\n\t\tfont-family: Alibaba PuHuiTi;\n\t\tcolor: #FC4343;\n\t\tmargin-top: 5rpx;\n\t}\n\n\t.operate_lable {\n\t\tfont-size: 22rpx;\n\t\tcolor: #222222;\n\t}\n\n\t.operate_tag {\n\t\tfont-size: 24rpx;\n\t\tfont-family: Alibaba PuHuiTi;\n\t\tfont-weight: bold;\n\t\tcolor: #FC4343;\n\t}\n\n\t.operate_num {\n\t\tfont-weight: bold;\n\t\tfont-size: 30rpx;\n\t\tcolor: #FC4343;\n\t}\n\n\t.operate_btn {\n\t\twidth: 320rpx;\n\t\tbackground: #FD4A46;\n\t\tborder-radius: 8rpx;\n\t\ttext-align: center;\n\t\tline-height: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #FFFFFF;\n\t}\n\t\n\t.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;}\n\t.bottombar .favorite{width: 15%;color:#707070;font-size:26rpx}\n\t.bottombar .favorite .fa{ font-size:40rpx;height:50rpx;line-height:50rpx}\n\t.bottombar .favorite .img{ width:50rpx;height:50rpx}\n\t.bottombar .favorite .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\n\t.bottombar .cart{width: 15%;font-size:26rpx;color:#707070}\n\t.bottombar .cart .img{ width:50rpx;height:50rpx}\n\t.bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\n\t.bottombar .tocart{ width: 30%; height: 100rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center}\n\t.bottombar .tobuy{font-weight: bold;height: 80rpx;color: #fff; background: #FC635F; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin: 15rpx 30rpx;border-radius: 100rpx;}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098408\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=style&index=1&id=6f5c83ee&scoped=true&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./planWrite.vue?vue&type=style&index=1&id=6f5c83ee&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098333\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}