{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?905e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?3ea8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?eefb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?2f44", "uni-app:///pagesExt/order/orderlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?8de0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderlist.vue?b691"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "canrefund", "express_content", "selectExpressShow", "hexiao_qr", "keyword", "is_more", "cart_info", "loop_point", "loop_length", "isSelectMode", "selectedOrders", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "add_cart", "app", "uni", "url", "that", "more_one", "obj", "proInfo", "more_one2", "confirm", "title", "content", "success", "close", "getdata", "changetab", "scrollTop", "duration", "toclose", "orderid", "setTimeout", "tui<PERSON><PERSON>", "todel", "orderCollect", "logistics", "console", "express_oglist", "hideSelectExpressDialog", "showhxqr", "closeHxqr", "searchConfirm", "toggleSelectMode", "toggleOrderSelect", "confirmSelect", "computed", "hasInvoiceOrder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgMlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;IACA;EAEA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC,kCACA;MACA;MACA;MACA;MACAC,4BACA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,GACA,eACA;QACA,qBACA;UAEA,6CACA;YACAC;cACAC;YACA;YAEA;UACA;YACAC;YACAA;UACA;QAEA;MACA,EACA;IAEA;IACAC,+BACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MAEA,yCACA;QACA;QACAC;QACAA;QACAA;QACAC;MACA;MAGA;MACA;MACA;MACA;MACA;MACA;IAIA;IACAC,iCACA;MACA;MACA;;MAGAP,6BACA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,GACA,eACA;QACA,qBACA;UACAC;YACAC;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA,EACA;IAEA;IACAM;MACA;MACA;MACA;MACA;MAEA,2BACA;QACAP;UACAQ;UACAC;UACAC;YACAR;UACA;QACA;MACA;MACA;MACAF;QACAC;MACA;MACA;IACA;IAEAU,wBACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAV;MACAA;MACAA;MACAH;QAAAzB;QAAAE;QAAAQ;MAAA;QACAkB;QACA;QACA;UACAA;UACAA;UACAA;UACA;UACA;YAAA;UAAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEAW;MACA;MACAb;QACAc;QACAC;MACA;MACA;MACAb;IACA;IACAc;MACA;MACA;MACAjB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACApB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACArB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACAtB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;QACAxB;MACA;QACAlB;QACA;UACA;YACA;YACA0C;YACA;YACA;cACA;gBACAC;cACA;YACA;YACA3C;UACA;QACA;QACA;QACA0C;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;MACA;QACA9B;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA+B;MACA;MACA;QACA;MACA;QACA;MACA;MACAP;IACA;IACAQ;MACA;QACAhC;MACA;IACA;EACA;EACAiC;IACAC;MACA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/orderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/orderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderlist.vue?vue&type=template&id=5cf17e27&\"\nvar renderjs\nimport script from \"./orderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/orderlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=template&id=5cf17e27&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.isSelectMode && _vm.selectedOrders.length > 0 : null\n  var g1 = _vm.isload && g0 ? _vm.selectedOrders.length : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g2 =\n          _vm.isSelectMode && item.invoice\n            ? _vm.selectedOrders.includes(item.id)\n            : null\n        var g3 = [1, 2, 3].includes(item.status) && item.invoice\n        var g4 = item.prolist && item.prolist.length > 0\n        var m0 =\n          item.status == 0 && item.paytypeid != 5 ? _vm.t(\"color1\") : null\n        var m1 =\n          item.status == 0 && item.paytypeid == 5 && item.transfer_check == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m2 =\n          item.status == 0 && item.paytypeid == 5 && !(item.transfer_check == 1)\n            ? _vm.t(\"color1\")\n            : null\n        var m3 =\n          item.status == 2 &&\n          item.balance_pay_status == 0 &&\n          item.balance_price > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m4 =\n          item.status == 2 &&\n          item.paytypeid != \"4\" &&\n          (item.balance_pay_status == 1 || item.balance_price == 0)\n            ? _vm.t(\"color1\")\n            : null\n        var m5 =\n          item.bid > 0 && item.status == 3 && item.iscommentdp == 0\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          g2: g2,\n          g3: g3,\n          g4: g4,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<dd-tab :itemdata=\"['全部','待付款','待发货','待收货','已完成']\" :itemst=\"['all','0','1','2','3']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\r\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--  #endif -->\r\n\t\t\t\t<view class=\"batch-invoice\" @tap=\"toggleSelectMode\" v-if=\"hasInvoiceOrder\">\r\n\t\t\t\t\t{{isSelectMode ? '取消' : '批量开票'}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"confirm-btn\" v-if=\"isSelectMode && selectedOrders.length > 0\" @tap=\"confirmSelect\">\r\n\t\t\t\t确认开票({{selectedOrders.length}})\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-content\">\r\n\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'detail?id=' + item.id\">\r\n\t\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t\t<view class=\"checkbox\" v-if=\"isSelectMode && item.invoice\">\r\n\t\t\t\t\t\t\t\t<checkbox :checked=\"selectedOrders.includes(item.id)\" @tap.stop=\"toggleOrderSelect(item.id)\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.bid!=0\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.bid\"><image src=\"/static/img/ico-shop.png\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-else><image :src=\"item.binfo.logo\" class=\"logo-row\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type!=1\" class=\"st1\">待发货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type==1\" class=\"st1\">待提货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">待收货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text>\r\n\t\t\t\t\t\t</view>\r\n\t\r\n\t\t\t\t\t\t<block v-for=\"(item2, idx) in item.prolist\" :key=\"idx\">\r\n\t\t\t\t\t\t\t<!-- 这里进行修改 -->\r\n\t\t\t\t\t\t\t<view v-if=\"item2.is_yh == 0\"  class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item2.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.num}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item2.is_yh == 1\" class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item2.yh_prices}}(优惠价格)</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.yh_nums}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view  class=\"bottom\"><text class=\"t2\">{{item.typenamename}}</text></view>\r\n\t\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t\t<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <span v-if=\"item.balance_price > 0 && item.balance_pay_status == 0\"  style=\"display: block; float: right;\">尾款：￥{{item.balance_price}}</span></text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red;padding-left:6rpx\">退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red;padding-left:6rpx\">已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red;padding-left:6rpx\">退款申请已驳回</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom\" v-if=\"item.tips!=''\">\r\n\t\t\t\t\t\t\t<text style=\"color:red\">{{item.tips}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t\t<block v-if=\"([1,2,3]).includes(item.status) && item.invoice\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'invoice?type=shop&orderid=' + item.id\">发票</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'detail?id=' + item.id\" class=\"btn2\">详情</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.prolist && item.prolist.length > 0\" @tap.stop=\"more_one\" :data-key='index' :data-id=\"item.prolist[0].proid\"  class=\"btn2\" :data-num=\"item.prolist[0].num\" :data-buytype=\"1\" :data-ggid=\"item.prolist[0].ggid\">再来一单</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<block v-if=\"item.status==0\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn1\" v-if=\"item.paytypeid!=5\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.payorderid\">去付款</view>\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.paytypeid==5\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn1\" v-if=\"item.transfer_check == 1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/transfer?id=' + item.payorderid\">付款凭证</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn1\" v-else :style=\"{background:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.transfer_check == 0\">转账待审核</text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.transfer_check == -1\">转账已驳回</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.status==1\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.paytypeid!='4'\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundSelect?orderid=' + item.id + '&price=' + item.totalprice\" v-if=\"canrefund==1 && item.refundnum < item.procount\">退款</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.status==2\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.paytypeid!='4'\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundSelect?orderid=' + item.id + '&price=' + item.totalprice\" v-if=\"canrefund==1 && item.refundnum < item.procount\">退款</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.freight_type!=3 && item.freight_type!=4\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn2\" v-if=\"item.express_type =='express_wx'\" @tap.stop=\"logistics\" :data-index=\"index\">订单跟踪</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn2\" v-else @tap.stop=\"logistics\" :data-index=\"index\">查看物流</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.balance_pay_status == 0 && item.balance_price > 0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.balance_pay_orderid\">支付尾款</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.paytypeid!='4' && (item.balance_pay_status==1 || item.balance_price==0)\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"orderCollect\" :data-id=\"item.id\">确认收货</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"(item.status==1 || item.status==2) && item.freight_type==1\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"showhxqr\" :data-hexiao_qr=\"item.hexiao_qr\">核销码</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view v-if=\"item.refundCount\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundlist?orderid='+ item.id\">查看退款</view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.status==3 || item.status==4\">\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"btn2\" @tap.stop=\"tuikuang\" :data-id=\"item.id\">退框</view> -->\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<block v-if=\"item.bid>0 && item.status==3\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.iscommentdp==0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/order/commentdp?orderid=' + item.id\">评价店铺</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\r\n\t\t\t\r\n\t\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t\t<image :src=\"hexiao_qr\" @tap=\"previewImage\" :data-url=\"hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\r\n\t\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\r\n\t\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\r\n\t\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\r\n\t\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t\r\n\t\t<uni-popup ref=\"more_one\" type=\"center\" >\r\n\t\t\t<uni-popup-dialog mode=\"input\" message=\"成功消息\" :duration=\"2000\" valueType=\"number\" :before-close=\"true\"  @close=\"close\" @confirm=\"confirm\"></uni-popup-dialog>\r\n\t\t\t<!-- <view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text>test</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<input type=\"number\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t <view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t</uni-popup>\r\n\t</view>\r\n\t</template>\r\n\t\r\n\t<script>\r\n\tvar app = getApp();\r\n\t\r\n\texport default {\r\n\t  data() {\r\n\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t  isload: false,\r\n\t\t\t\tmenuindex:-1,\r\n\t\r\n\t\t  st: 'all',\r\n\t\t  datalist: [],\r\n\t\t  pagenum: 1,\r\n\t\t  nomore: false,\r\n\t\t\t\tnodata:false,\r\n\t\t  codtxt: \"\",\r\n\t\t\t\tcanrefund:1,\r\n\t\t\t\texpress_content:'',\r\n\t\t\t\tselectExpressShow:false,\r\n\t\t\t\thexiao_qr:'',\r\n\t\t\t\tkeyword:'',\r\n\t\t\t\t  is_more:0,\r\n\t\t\t\t \r\n\t\t\t\t cart_info:[],\r\n\t\t\t\t loop_point:0 ,\r\n\t\t\t\t loop_length:0,\r\n\t\t\t\t \r\n\t\t\t\t isSelectMode: false,\r\n\t\t\t\t selectedOrders: [],\r\n\t\t};\r\n\t\t\r\n\t  },\r\n\t\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tif(this.opt && this.opt.st){\r\n\t\t\t\tthis.st = this.opt.st;\r\n\t\t\t}\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t  onReachBottom: function () {\r\n\t\tif (!this.nodata && !this.nomore) {\r\n\t\t  this.pagenum = this.pagenum + 1;\r\n\t\t  this.getdata(true);\r\n\t\t}\r\n\t  },\r\n\t\tonNavigationBarSearchInputConfirmed:function(e){\r\n\t\t\tthis.searchConfirm({detail:{value:e.text}});\r\n\t\t},\r\n\t  methods: {\r\n\t\tadd_cart(data)\r\n\t\t{\r\n\t\t\t//let index = i;\r\n\t\t\t//let data = datas[index];\r\n\t\t\tlet that = this;\r\n\t\t\tapp.post('ApiShop/addcart',\r\n\t\t\t{'ggid':data['ggid'],'num':data['num'],'proid':data['id'],'tid':0,'glass_record_id':0},\r\n\t\t\t\t\t   function(res)\r\n\t\t\t\t\t   {\r\n\t\t\t\t\t\t   if(res.status== 1)\r\n\t\t\t\t\t\t   {\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t   if(that.loop_point== that.loop_length-1)\r\n\t\t\t\t\t\t\t   {\r\n\t\t\t\t\t\t\t\t   uni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\t url:'/shopPackage/shop/cart'\r\n\t\t\t\t\t\t\t\t   });\r\n\t\t\t\t\t\t\t\t   \r\n\t\t\t\t\t\t\t\t   return ;\r\n\t\t\t\t\t\t\t   }else{\r\n\t\t\t\t\t\t\t\t   that.loop_point++;\r\n\t\t\t\t\t\t\t\t   that.add_cart(that.cart_info[that.loop_point]);\r\n\t\t\t\t\t\t\t   }\r\n\t\t\t\t\t\t\t  \r\n\t\t\t\t\t\t   }\r\n\t\t\t\t\t   }\r\n\t\t\t);\r\n\t\t\t\r\n\t\t},\r\n\t\tmore_one:function(e)\r\n\t\t{\r\n\t\t\t// console.log(e);\r\n\t\t\tlet data = e.target.dataset;\r\n\t\t\t// let url = \"/activity/luckycollage/buy\"+\"?proid=\"+data['id']+\"&num=\"+data['num']+\"&buytype=\"+data['buytype']+\"&ggid=\"+data['ggid'];\r\n\t\t\t\r\n\t\t\tlet proInfo = [];\r\n\t\t\tlet o_index = data['key'];\r\n\t\t\tlet order= this.datalist[o_index];\r\n\t\t\t\r\n\t\t\tlet prolist  = order['prolist'];\r\n\t\t\t\r\n\t\t\tfor(let i=0;i<prolist.length;i++)\r\n\t\t\t{\r\n\t\t\t\tlet obj={};\r\n\t\t\t\tobj['id'] = prolist[i]['proid'];\r\n\t\t\t\tobj['num'] = prolist[i]['num'];\r\n\t\t\t\tobj['ggid'] = prolist[i]['ggid'];\r\n\t\t\t\tproInfo.push(obj);\r\n\t\t\t}\r\n\t\t\t\r\n\t\r\n\t\t\tthis.loop_length = proInfo.length;\r\n\t\t\tthis.loop_point = 0;\r\n\t\t\tthis.cart_info = proInfo;\r\n\t\t\t// console.log(proInfo);\r\n\t\t\t// return ;\r\n\t\t\tthis.add_cart(proInfo[0]);\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\t   \r\n\t\t},\r\n\t\tmore_one2:function(e)\r\n\t\t{\r\n\t\t\tlet data = e.target.dataset;\r\n\t\t\t// let url = \"/activity/luckycollage/buy\"+\"?proid=\"+data['id']+\"&num=\"+data['num']+\"&buytype=\"+data['buytype']+\"&ggid=\"+data['ggid'];\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tapp.post('/ApiShop/addcart',\r\n\t\t\t{'ggid':data['ggid'],'num':data['num'],'proid':data['id'],'tid':0,'glass_record_id':0},\r\n\t\t\t\t\t   function(res)\r\n\t\t\t\t\t   {\r\n\t\t\t\t\t\t   if(res.status== 1)\r\n\t\t\t\t\t\t   {\r\n\t\t\t\t\t\t\t   uni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t url:'/shopPackage/shop/cart'\r\n\t\t\t\t\t\t\t   });\r\n\t\t\t\t\t\t\t   \r\n\t\t\t\t\t\t\t   // if(that.loop_point== that.loop_length-1)\r\n\t\t\t\t\t\t\t   // {\r\n\t\t\t\t\t\t\t\t  //  return ;\r\n\t\t\t\t\t\t\t   // }else{\r\n\t\t\t\t\t\t\t\t  //  that.add_cart(cart_info[that.loop_point]);\r\n\t\t\t\t\t\t\t   // }\r\n\t\t\t\t\t\t\t   // that.loop_point++;\r\n\t\t\t\t\t\t   }\r\n\t\t\t\t\t   }\r\n\t\t\t);\r\n\t\t\t\r\n\t\t},\r\n\t\tconfirm(value1,value2) {\r\n\t\t\tlet data = this.more_data;\r\n\t\t\t//console.log(value2);\r\n\t\t\tlet num = value2;\r\n\t\t\tlet that = this;\r\n\t\t\t\r\n\t\t\tif(num==\"\"||num==0)\r\n\t\t\t{\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle:\"提示\",\r\n\t\t\t\t\tcontent:\"请输入数量\",\r\n\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\tthat.$refs.more_one.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet url = \"/shopPackage/shop/buy?\"+\"prodata=\"+data['id']+\",\"+data['ggid']+\",\"+num;\r\n\t\t\tuni.redirectTo({\r\n\t\t\t\turl:url,\r\n\t\t\t})\r\n\t\t\tthis.$refs.more_one.close();\r\n\t\t}\r\n\t\t,\r\n\t\tclose()\r\n\t\t{\r\n\t\t\t this.$refs.more_one.close();\r\n\t\t},\t  \r\n\t\tgetdata: function (loadmore) {\r\n\t\t\t\tif(!loadmore){\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t  var that = this;\r\n\t\t  var pagenum = that.pagenum;\r\n\t\t  var st = that.st;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tthat.loading = true;\r\n\t\t  app.post('ApiOrder/orderlist', {st: st,pagenum: pagenum,keyword:that.keyword}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\tvar data = res.datalist;\r\n\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.codtxt = res.codtxt;\r\n\t\t\t\t\t\tthat.canrefund = res.canrefund;\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t  // 检查是否有可开发票的订单\r\n\t\t\t  const hasInvoiceOrder = data.some(item => item.invoice);\r\n\t\t\t  if(!hasInvoiceOrder) {\r\n\t\t\t\t  that.isSelectMode = false;\r\n\t\t\t  }\r\n\t\t\t  if (data.length == 0) {\r\n\t\t\t\tthat.nodata = true;\r\n\t\t\t  }\r\n\t\t\t\t\t\tthat.loaded();\r\n\t\t\t}else{\r\n\t\t\t  if (data.length == 0) {\r\n\t\t\t\tthat.nomore = true;\r\n\t\t\t  } else {\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t  }\r\n\t\t\t}\r\n\t\t  });\r\n\t\t},\r\n\t\t   \r\n\t\tchangetab: function (st) {\r\n\t\t  this.st = st;\r\n\t\t  uni.pageScrollTo({\r\n\t\t\tscrollTop: 0,\r\n\t\t\tduration: 0\r\n\t\t  });\r\n\t\t  this.getdata();\r\n\t\t  that.is_more = res['is_more'];\r\n\t\t},\r\n\t\ttoclose: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiOrder/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\ttuikuang:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = e.currentTarget.dataset.id;\r\n\t\t\tapp.confirm('确定退框吗?', function () {\r\n\t\t\t\t\tapp.showLoading('退框中');\r\n\t\t\tapp.post('ApiOrder/delOrders', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\ttodel: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\t\tapp.showLoading('删除中');\r\n\t\t\tapp.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\torderCollect: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要收货吗?', function () {\r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\t\tlogistics:function(e){\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar orderinfo = this.datalist[index];\r\n\t\t\t\tvar express_com = orderinfo.express_com\r\n\t\t\t\tvar express_no = orderinfo.express_no\r\n\t\t\t\tvar express_content = orderinfo.express_content\r\n\t\t\t\tvar express_type = orderinfo.express_type\r\n\t\t\t\tvar prolist = orderinfo.prolist\r\n\t\t\t\tconsole.log(express_content)\r\n\t\t\t\tif(!express_content){\r\n\t\t\t\t\tapp.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\r\n\t\t\t\t}else{\r\n\t\t\t\t\texpress_content = JSON.parse(express_content);\r\n\t\t\t\t\tfor(var i in express_content){\r\n\t\t\t\t\t\tif(express_content[i].express_ogids){\r\n\t\t\t\t\t\t\tvar express_ogids = (express_content[i].express_ogids).split(',');\r\n\t\t\t\t\t\t\tconsole.log(express_ogids);\r\n\t\t\t\t\t\t\tvar express_oglist = [];\r\n\t\t\t\t\t\t\tfor(var j in prolist){\r\n\t\t\t\t\t\t\t\tif(app.inArray(prolist[j].id+'',express_ogids)){\r\n\t\t\t\t\t\t\t\t\texpress_oglist.push(prolist[j]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\texpress_content[i].express_oglist = express_oglist;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.express_content = express_content;\r\n\t\t\t\t\tconsole.log(express_content);\r\n\t\t\t\t\tthis.$refs.dialogSelectExpress.open();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thideSelectExpressDialog:function(){\r\n\t\t\t\tthis.$refs.dialogSelectExpress.close();\r\n\t\t\t},\r\n\t\t\tshowhxqr:function(e){\r\n\t\t\t\tthis.hexiao_qr = e.currentTarget.dataset.hexiao_qr\r\n\t\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t\t},\r\n\t\t\tcloseHxqr:function(){\r\n\t\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t\t},\r\n\t\t\tsearchConfirm:function(e){\r\n\t\t\t\tthis.keyword = e.detail.value;\r\n\t\t  this.getdata(false);\r\n\t\t\t},\r\n\t\t\ttoggleSelectMode() {\r\n\t\t\t\t// 检查是否有可开发票的订单\r\n\t\t\t\tconst hasInvoiceOrder = this.datalist.some(item => item.invoice && ([1,2,3]).includes(item.status));\r\n\t\t\t\tif(!hasInvoiceOrder) {\r\n\t\t\t\t\tapp.error('当前没有可开发票的订单');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.isSelectMode = !this.isSelectMode;\r\n\t\t\t\tif (!this.isSelectMode) {\r\n\t\t\t\t\tthis.selectedOrders = [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoggleOrderSelect(orderId) {\r\n\t\t\t\tconst index = this.selectedOrders.indexOf(orderId);\r\n\t\t\t\tif (index === -1) {\r\n\t\t\t\t\tthis.selectedOrders.push(orderId);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.selectedOrders.splice(index, 1);\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log('已选择的订单：', this.selectedOrders);\r\n\t\t\t},\r\n\t\t\tconfirmSelect() {\r\n\t\t\t\tif(this.selectedOrders.length > 0) {\r\n\t\t\t\t\tapp.goto('/pagesExt/order/batchInvoice?ids=' + this.selectedOrders.join(','));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t  },\r\n\t  computed: {\r\n\t\thasInvoiceOrder() {\r\n\t\t\treturn this.datalist.some(item => item.invoice && ([1,2,3]).includes(item.status));\r\n\t\t}\r\n\t  }\r\n\t};\r\n\t</script>\r\n\t<style>\r\n\t.container{ width:100%;}\r\n\t.topsearch{\r\n\t\twidth:94%;\r\n\t\tmargin:10rpx 3%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tposition: relative;\r\n\t\tz-index: 100;\r\n\t\tbackground: #f8f8f8;\r\n\t\tpadding: 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\t.topsearch .f1{\r\n\t\theight:60rpx;\r\n\t\tborder-radius:30rpx;\r\n\t\tborder:0;\r\n\t\tbackground-color:#fff;\r\n\t\tflex:1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n\t.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\t.order-content{display:flex;flex-direction:column}\r\n\t.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n\t.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\r\n\t.order-box .head .f1{display:flex;align-items:center;color:#333}\r\n\t.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}\r\n\t.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n\t.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n\t.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n\t.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n\t.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\t\r\n\t.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\r\n\t.order-box .content:last-child{ border-bottom: 0; }\r\n\t.order-box .content image{ width: 140rpx; height: 140rpx;}\r\n\t.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n\t.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n\t.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n\t.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n\t.order-box .content .detail .x1{ flex:1}\r\n\t.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\t\r\n\t.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\t.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\t\r\n\t.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\r\n\t.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\r\n\t\r\n\t.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n\t.hxqrbox .img{width:400rpx;height:400rpx}\r\n\t.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n\t.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\t\r\n\t.batch-invoice {\r\n\t\tpadding: 0 30rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tbackground: #ff4246;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 30rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tflex-shrink: 0;\r\n\t\tz-index: 999;\r\n\t\tposition: relative;\r\n\t}\r\n\t.checkbox {\r\n\t\tmargin-right: 20rpx;\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.checkbox checkbox {\r\n\t\ttransform: scale(0.8);\r\n\t}\r\n\t.order-box .head {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.confirm-btn {\r\n\t\tposition: fixed;\r\n\t\tbottom: 120rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tbackground: #ff4246;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 20rpx 60rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tz-index: 999;\r\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\r\n\t}\r\n\t</style>\r\n\t", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103585\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}