{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?8a94", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?a4fb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?cb96", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?db86", "uni-app:///pagesExt/tuozhanyuan/shangjipull.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?215d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/tuozhanyuan/shangjipull.vue?3af1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "rateArr", "rateIndex", "selectedRate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yixiangduIndex", "linkman", "linktel", "name", "desc", "cuisine_type", "price", "sample_feedback", "id", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "cateChange", "yixiang<PERSON><PERSON><PERSON><PERSON>", "sourceChange", "console", "member_id", "withdrawMethodChange", "locationSelect", "success", "fail", "subform", "info", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "pics", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgJpxB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IAAA,uDACA,8DACA,sDACA,yDACA,mDACA,iDACA,sDACA,sDACA,wDACA,wDACA,iDACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;IACA,mDACA,oDACA,iDACA,qDACA,sDACA,oDACA,4DACA,oFACA,kEACA;EAEA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QAEA;UACAC;YACAA;UACA;UACA;QACA;QACAC;UACAC;QACA;;QAEA;QACA;QACA;UAAA;QAAA;QACAH;QACAA;;QAEA;QACA;QACA;UAAA;QAAA;QACAA;QACAA;;QAEA;QACA;QACA;UAAA;QAAA;QACAA;QACAA;;QAEA;QACAA;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;MACA;IACA;IAEAI;MACA;IACA;IACAC;MACA;MACAL;MACAA;IACA;;IACAM;MACA;MACA;MAGAC;MAEAP;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACAC;YACAO;UACA;YACA;cACA;cACA;cACA;gBACAR;gBACAA;gBACAO;cACA;YACA;cACAN;cACAD;cACAA;YACA;UACA;QACA;MACA;QACA;QACAA;QACAA;MACA;IACA;IAEAS;MACA;MACA;IACA;IACAC;MACA;MACAR;QACAS;UACAX;UACAA;UACAA;QACA;;QACAY;UACAX;QACA;MACA;IACA;IAEAY;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;MACA;MACA;QACAA;MACA;QACAA;MACA;;MAEAA;MACA;QACAA;MACA;MACAA;MACA;QACAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACAb;MACAA;QACAa;MACA;QACAb;QACAA;QACA;UACAc;YACAd;UACA;QACA;MACA;IACA;IACAe;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAlB;QACA;UACAmB;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAD;QACApB;MACA;QACA;QACAoB;QACApB;MACA;QACA;QACAoB;QACApB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxbA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/tuozhanyuan/shangjipull.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/tuozhanyuan/shangjipull.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shangjipull.vue?vue&type=template&id=59462e08&\"\nvar renderjs\nimport script from \"./shangjipull.vue?vue&type=script&lang=js&\"\nexport * from \"./shangjipull.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shangjipull.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/tuozhanyuan/shangjipull.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjipull.vue?vue&type=template&id=59462e08&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.subordinates.length : null\n  var g1 = _vm.isload ? _vm.pics.length : null\n  var g2 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjipull.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjipull.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==2\">\r\n\t\t\t\t审核不通过：{{info.reason}}，请修改后再提交</view>\r\n\t\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.status==0\">您已提交申请，请等待审核\r\n\t\t\t</view>\r\n\t\t\t<form @submit=\"subform\">\r\n\t\t\t\t<view class=\"apply_box\">\r\n\r\n\r\n\t\t\t\t\t<view class=\"apply_item\" v-if=\"subordinates.length > 0\">\r\n\t\t\t\t\t\t<view>选择商机来源<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<picker @change=\"sourceChange\" :value=\"sourceIndex\" :range=\"sourceArr\">\r\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{sourceArr[sourceIndex]}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>联系人姓名<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"linkman\" v-model=\"linkman\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请填写姓名\"></input></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>联系人电话<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"linktel\" v-model=\"linktel\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请填写手机号码\"></input></view>\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>商家名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入商家名称\"></input></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>商家备注<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"desc\" :value=\"info.desc\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入商家描述\"></input></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>主营类目<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\">\r\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{cateArr[cindex]}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"apply_item\">\r\n\t\t\t\t\t<view>餐饮类目<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"cuisine_type\" :value=\"info.cuisine_type\" placeholder=\"类目不确定可以填\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>商家单价<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"price\" :value=\"info.price\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"类目不确定可以填\"></input></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>意向度<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<picker @change=\"yixiangduChange\" :value=\"yixiangduIndex\" :range=\"yixiangduArr\">\r\n\t\t\t\t\t\t\t<view class=\"picker\">{{ yixiangduArr[yixiangduIndex] }}</view>\r\n\t\t\t\t\t\t</picker>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>店铺坐标<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" disabled placeholder=\"请选择坐标\" name=\"zuobiao\"\r\n\t\t\t\t\t\t\t\t:value=\"latitude ? latitude+','+longitude:''\" @tap=\"locationSelect\"></input></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t\t\t<view>店铺地址<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t<input type=\"text\" name=\"address\" v-model=\"address\" placeholder=\"请输入商家详细地址\"></input>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"latitude\" :value=\"latitude\"></input>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"longitude\" :value=\"longitude\"></input>\r\n\t\t\t\t\t<!-- \t<view class=\"apply_item\">\r\n\t\t\t\t\t<view>联系电话<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写客服电话\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class=\"apply_item\" style=\"line-height:50rpx\"><textarea name=\"sample_feedback\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入商家样品反馈\" :value=\"info.sample_feedback\"></textarea></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- \t<view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家主图<text style=\"color:red\"> *</text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view> -->\r\n\t\t\t\t<view class=\"apply_box\">\r\n\t\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>商家照片(3-4张)<text style=\"color:red\">\r\n\t\t\t\t\t\t\t</text></text></view>\r\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/ico-del.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\"\r\n\t\t\t\t\t\t\t:style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\"\r\n\t\t\t\t\t\t\t@tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<5\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"apply_box\">\r\n\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>证明材料<text style=\"color:red\"> </text></text></view>\r\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in zhengming\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"zhengming\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"zhengming\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"zhengming\" :value=\"zhengming.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t\t -->\r\n\r\n\t\t\t\t<view style=\"padding:30rpx 0\"><button form-type=\"submit\" class=\"set-btn\"\r\n\t\t\t\t\t\t:style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tcateArr: [],\r\n\t\t\t\tcindex: 0,\r\n\t\t\t\trateArr: [], // 商户费率名称数组\r\n\t\t\t\trateIndex: 0, // 当前选中的费率索引\r\n\t\t\t\tselectedRate: null, // 当前选中的费率对象\r\n\t\t\t\tyixiangduArr: [], // 商户费率名称数组\r\n\t\t\t\tyixiangduIndex: 0, // 当前选中的费率索引\r\n\t\t\t\tyixiangduArr: [], // 意向度数组\r\n\t\t\t\tselectedyixiangdu: null, // 当前选中的费率对象\r\n\t\t\t\tisagree: false,\r\n\t\t\t\tshowxieyi: false,\r\n\t\t\t\tpic: [],\r\n\t\t\t\tpics: [],\r\n\t\t\t\tzhengming: [],\r\n\t\t\t\tsourceArr: [], // 存储商机来源名称数组\r\n\t\t\t\tsourceIndex: 0, // 当前选中的商机来源索引\r\n\t\t\t\tsubordinates: [], // 存储完整的商机来源数据\r\n\t\t\t\tinfo: {\r\n\t\t\t\t\tlinkman: '',\r\n\t\t\t\t\tlinktel: '',\r\n\t\t\t\t\tname: '', \r\n\t\t\t\t\tdesc: '',\r\n\t\t\t\t\tcuisine_type: '',\r\n\t\t\t\t\tprice: '',\r\n\t\t\t\t\tsample_feedback: '',\r\n\t\t\t\t\tid: null,\r\n\t\t\t\t\t// 如果有其他属性，也请在这里初始化\r\n\t\t\t\t},\r\n\t\t\t\tlinkman : '',\r\n\t\t\t\tlinktel : '',\r\n\t\t\t\tbset: {},\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\twithdrawMethods: ['支付宝', '银行卡', '三方支付'],\r\n\t\t\t\twithdrawMethodIndex: 0,\r\n\t\t\t\tselectedWithdrawMethod: '支付宝'\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiTuozhancrm/apply', {}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\tapp.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.title\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 处理分类列表\r\n\t\t\t\t\tvar clist = res.clist;\r\n\t\t\t\t\tvar cateArr = clist.map(item => item.name);\r\n\t\t\t\t\tthat.clist = clist;\r\n\t\t\t\t\tthat.cateArr = cateArr;\r\n\r\n\t\t\t\t\t// 处理意向度数据\r\n\t\t\t\t\tvar yixiangdu = res.yixiangdu || [];\r\n\t\t\t\t\tvar yixiangduArr = yixiangdu.map(item => item.name);\r\n\t\t\t\t\tthat.yixiangdu = yixiangdu;\r\n\t\t\t\t\tthat.yixiangduArr = yixiangduArr;\r\n\r\n\t\t\t\t\t// 处理商机来源数据，将对象转换为数组\r\n\t\t\t\t\tvar subordinates = Object.values(res.subordinates || {});\r\n\t\t\t\t\tvar sourceArr = subordinates.map(item => item.realname || item.nickname || '无名');\r\n\t\t\t\t\tthat.subordinates = subordinates;\r\n\t\t\t\t\tthat.sourceArr = sourceArr;\r\n\r\n\t\t\t\t\t// 初始化其他数据\r\n\t\t\t\t\tthat.info = res.info || {};\r\n\r\n\t\t\t\t\tthat.linkman = that.info.realname || '';\r\n\t\t\t\t\tthat.linktel = that.info.tel || '';\r\n\t\t\t\t\tthat.address = that.info.address || '';\r\n\t\t\t\t\tthat.latitude = that.info.latitude || '';\r\n\t\t\t\t\tthat.longitude = that.info.longitude || '';\r\n\t\t\t\t\tthat.pic = that.info.pic ? [that.info.pic] : [];\r\n\t\t\t\t\tthat.pics = that.info.pics ? that.info.pics.split(',') : [];\r\n\t\t\t\t\tthat.zhengming = that.info.zhengming ? that.info.zhengming.split(',') : [];\r\n\t\t\t\t\tthat.bset = res.bset || {};\r\n\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tcateChange: function(e) {\r\n\t\t\t\tthis.cindex = e.detail.value;\r\n\t\t\t},\r\n\t\t\tyixiangduChange: function(e) {\r\n\t\t\t\tvar that = this; // 引用组件的上下文\r\n\t\t\t\tthat.yixiangduIndex = e.detail.value;\r\n\t\t\t\tthat.selectedyixiangdu = that.yixiangdu[that.yixiangduIndex]; // 使用 that\r\n\t\t\t},\r\n\t\t\tsourceChange: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar sourceIndex = e.detail.value;\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\t\r\n\t\t\t\tthat.sourceIndex = sourceIndex;\r\n\t\t\t\tvar selectedSubordinate = that.subordinates[sourceIndex];\r\n\r\n\t\t\t\tif (selectedSubordinate) {\r\n\t\t\t\t\tvar memberId = selectedSubordinate.id;\r\n\r\n\t\t\t\t\t// 保存当前的 memberId 和 that\r\n\t\t\t\t\t(function(currentMemberId, currentSourceIndex) {\r\n\t\t\t\t\t\t// 调用后端接口获取会员详细信息\r\n\t\t\t\t\t\tapp.post('ApiTuozhancrm/getMemberInfo', {\r\n\t\t\t\t\t\t\tmember_id: currentMemberId\r\n\t\t\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\t\t\t\t// 确保在回调中更新的是当前选择的用户信息\r\n\t\t\t\t\t\t\t\tif (that.sourceIndex === currentSourceIndex) {\r\n\t\t\t\t\t\t\t\t\tthat.linkman = data.realname || ''; // 更新联系人姓名\r\n\t\t\t\t\t\t\t\t\tthat.linktel = data.tel || ''; // 更新联系人电话\r\n\t\t\t\t\t\t\t\t\tconsole.log('更新后的 info 对象：', that.info);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t\t\tthat.linkman = '';\r\n\t\t\t\t\t\t\t\tthat.linktel = '';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})(memberId, sourceIndex); // 立即执行函数，传入当前的 memberId 和 sourceIndex\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果没有选中有效的下级成员，清空联系人信息\r\n\t\t\t\t\tthat.linkman = '';\r\n\t\t\t\t\tthat.linktel = '';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\twithdrawMethodChange: function(e) {\r\n\t\t\t\tthis.withdrawMethodIndex = e.detail.value;\r\n\t\t\t\tthis.selectedWithdrawMethod = this.withdrawMethods[this.withdrawMethodIndex];\r\n\t\t\t},\r\n\t\t\tlocationSelect: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\tthat.address = res.name; // 更新地址为选择的地理位置名称\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\tapp.error('无法获取当前位置，请手动填写店铺地址');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tsubform: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar info = e.detail.value;\r\n\r\n\t\t\t\t// if (info.linkman == '') {\r\n\t\t\t\t//   app.error('请填写联系人姓名');\r\n\t\t\t\t//   return false;\r\n\t\t\t\t// }\r\n\t\t\t\t// if (info.linktel == '') {\r\n\t\t\t\t//   app.error('请填写联系人电话');\r\n\t\t\t\t//   return false;\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t//   if (info.name == '') {\r\n\t\t\t\t//     app.error('请填写商家名称');\r\n\t\t\t\t//     return false;\r\n\t\t\t\t//   }\r\n\r\n\t\t\t\t//   if (info.address == '') {\r\n\t\t\t\t//     app.error('请填写店铺地址');\r\n\t\t\t\t//     return false;\r\n\t\t\t\t//   }\r\n\t\t\t\t// 打印调试信息，检查 sourceIndex 和 subordinates 是否有效\r\n\r\n\r\n\t\t\t\t// if (info.pics == '') {\r\n\t\t\t\t//   app.error('请上传商家照片');\r\n\t\t\t\t//   return false;\r\n\t\t\t\t// }\r\n\t\t\t\t// 检查 subordinates 和 sourceIndex 是否有效\r\n\t\t\t\t// if (!that.subordinates || !that.subordinates[that.sourceIndex]) {\r\n\t\t\t\t//   app.error('请选择有效的商机来源');\r\n\t\t\t\t//   return false;\r\n\t\t\t\t// }\r\n\t\t\t\t// 设置表单信息\r\n\t\t\t\tinfo.address = that.address; // 使用手动输入的地址\r\n\t\t\t\tinfo.latitude = that.latitude || ''; // 如果没有选择坐标，则留空\r\n\t\t\t\tinfo.longitude = that.longitude || ''; // 如果没有选择坐标，则留空\r\n\t\t\t\t// 设置商机来源（下级成员ID）\r\n\t\t\t\t// 设置商机来源（下级成员ID）\r\n\t\t\t\tif (that.subordinates && that.subordinates.length > 0) {\r\n\t\t\t\t\tinfo.source = that.subordinates[that.sourceIndex].id;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tinfo.source = ''; // 或根据后端需求设置为 null 或者不设置该字段\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinfo.cid = that.clist[that.cindex].id;\r\n\t\t\t\tif (that.info && that.info.id) {\r\n\t\t\t\t\tinfo.id = that.info.id;\r\n\t\t\t\t}\r\n\t\t\t\tinfo.yixiangduid = that.yixiangdu[that.yixiangduIndex].id;\r\n\t\t\t\tif (that.info && that.info.id) {\r\n\t\t\t\t\tinfo.yixiangduid = that.info.yixiangduid;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// if (!info.rate_id) {\r\n\t\t\t\t//  app.error('请选择商户费率');\r\n\t\t\t\t// return false;\r\n\t\t\t\t// }\r\n\t\t\t\t// 继续现有的表单验证和提交逻辑\r\n\t\t\t\t// 您的现有验证逻辑...\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post(\"ApiTuozhancrm/apply\", {\r\n\t\t\t\t\tinfo: info\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tapp.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tisagreeChange: function(e) {\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tthis.isagree = val.length > 0;\r\n\t\t\t},\r\n\t\t\tshowxieyiFun: function() {\r\n\t\t\t\tthis.showxieyi = true;\r\n\t\t\t},\r\n\t\t\thidexieyi: function() {\r\n\t\t\t\tthis.showxieyi = false;\r\n\t\t\t\tthis.isagree = true;\r\n\t\t\t},\r\n\t\t\tuploadimg: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\t\tvar pics = that[field];\r\n\t\t\t\tif (!pics) pics = [];\r\n\t\t\t\tapp.chooseImage(function(urls) {\r\n\t\t\t\t\tfor (var i = 0; i < urls.length; i++) {\r\n\t\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (field == 'pic') that.pic = pics;\r\n\t\t\t\t\tif (field == 'pics') that.pics = pics;\r\n\t\t\t\t\tif (field == 'zhengming') that.zhengming = pics;\r\n\t\t\t\t}, 1);\r\n\t\t\t},\r\n\t\t\tremoveimg: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\t\tif (field == 'pic') {\r\n\t\t\t\t\tvar pics = that.pic;\r\n\t\t\t\t\tpics.splice(index, 1);\r\n\t\t\t\t\tthat.pic = pics;\r\n\t\t\t\t} else if (field == 'pics') {\r\n\t\t\t\t\tvar pics = that.pics;\r\n\t\t\t\t\tpics.splice(index, 1);\r\n\t\t\t\t\tthat.pics = pics;\r\n\t\t\t\t} else if (field == 'zhengming') {\r\n\t\t\t\t\tvar pics = that.zhengming;\r\n\t\t\t\t\tpics.splice(index, 1);\r\n\t\t\t\t\tthat.zhengming = pics;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\tradio {\r\n\t\ttransform: scale(0.6);\r\n\t}\r\n\r\n\tcheckbox {\r\n\t\ttransform: scale(0.6);\r\n\t}\r\n\r\n\t.apply_box {\r\n\t\tpadding: 2rpx 24rpx 0 24rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin: 24rpx;\r\n\t\tborder-radius: 10rpx\r\n\t}\r\n\r\n\t.apply_title {\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.apply_title .qr_goback {\r\n\t\twidth: 18rpx;\r\n\t\theight: 32rpx;\r\n\t\tmargin-left: 24rpx;\r\n\t\tmargin-top: 34rpx;\r\n\t}\r\n\r\n\t.apply_title .qr_title {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #242424;\r\n\t\tfont-weight: bold;\r\n\t\tmargin: 0 auto;\r\n\t\tline-height: 100rpx;\r\n\t}\r\n\r\n\t.apply_item {\r\n\t\tline-height: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #eee\r\n\t}\r\n\r\n\t.apply_box .apply_item:last-child {\r\n\t\tborder: none\r\n\t}\r\n\r\n\t.apply_item input {\r\n\t\twidth: 100%;\r\n\t\tborder: none;\r\n\t\tcolor: #111;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: right\r\n\t}\r\n\r\n\t.apply_item input::placeholder {\r\n\t\tcolor: #999999\r\n\t}\r\n\r\n\t.apply_item textarea {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 200rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.apply_item .upload_pic {\r\n\t\tmargin: 50rpx 0;\r\n\t\tbackground: #F3F3F3;\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.apply_item .upload_pic image {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.set-btn {\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 5%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.layui-imgbox {\r\n\t\tmargin-right: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.layui-imgbox-close {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tright: -16rpx;\r\n\t\ttop: -16rpx;\r\n\t\tz-index: 90;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.layui-imgbox-close image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.layui-imgbox-img {\r\n\t\tdisplay: block;\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tpadding: 2px;\r\n\t\tborder: #d3d3d3 1px solid;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.layui-imgbox-img>image {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.layui-imgbox-repeat {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tright: 2px;\r\n\t\tbottom: 2px;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 30rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.uploadbtn {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjipull.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shangjipull.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103458\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}