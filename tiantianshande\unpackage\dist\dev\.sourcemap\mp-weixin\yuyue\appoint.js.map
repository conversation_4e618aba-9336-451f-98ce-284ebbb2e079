{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?c7fa", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?a985", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?c82a", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?3645", "uni-app:///yuyue/appoint.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?4de7", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/appoint.vue?2df2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "orderInfo", "product", "pic", "name", "sell_price", "order_no", "pay_time", "selectedDate", "selectedTime", "timeList", "dateList", "startDate", "endDate", "loading", "activePeriod", "timePeriods", "label", "value", "productId", "dateType", "dayDate", "dateFromUrl", "computed", "canSubmit", "filteredTimeList", "formattedSelectedDate", "onLoad", "console", "app", "methods", "getOrderInfo", "id", "that", "initDateRange", "today", "getDateList", "params", "processDateList", "apiDateList", "day", "month", "fullDate", "year", "weekday", "isToday", "foundTodayDate", "todayIndex", "foundPreselectedDate", "generateDefaultDateList", "date", "<PERSON><PERSON><PERSON><PERSON>", "selectDate", "switchPeriod", "getTimeList", "formattedDate", "replace", "order_id", "timeData", "processTimeData", "hour", "isAvailable", "item", "time", "available", "period", "reason", "booked_count", "max_bookings", "full_time", "hasAvailable", "createDefaultTimeSlots", "disabled", "firstAvailableTime", "selectTime", "submitAppoint", "yydate", "yytime", "success", "message", "needSelectWorker"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6FhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IAEAC;MACA;MAEA;QACA;QACA;MACA;MAEA;IACA;EACA;EAEAC;IACAC;IACA;IACA;IAEA;MACA;;MAEA;MACA;QACAA;QACA;QACA;QACA;MACA;QACA;QACAA;QACA;MACA;MAEA;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAF;MAEAD;MAEAC;QACAG;MACA;QACAH;QACAD;;QAEA;QACA;UACAA;UACAK;QACA;UACA;UACAL;UACAK;QACA;UACA;UACAL;UACAK;QACA;QAEAL;;QAEA;QACA;UACA;UACAK;YACA9B;YACAC;YACAC;UACA;UACAuB;QACA;;QAEA;QACA;UACAK;QACA;QAEA;UACA;UACA;YACA;cACA;cACAA,qDACA,gDACA,yCACA,0CACA,4CACA;YACA;cACAA;YACA;UACA;YACAA;UACA;QACA;;QAEA;QACAA;QACAL;QACAA;QAEAK;;QAEA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;MACArB;MACA;;MAEA;MACA,4CACAsB,0FACAA;;MAEA;MACA;QACA;QACAP;MACA;IACA;IAEA;IACAQ;MACA;MACA;MACA;MAEAR;MACAA;MACAA;MAEA;QACAA;QACAK;QACA;MACA;MAEAJ;;MAEA;MACA;QACAG;MACA;;MAEA;MACA;QACAK;QACAT;MACA;QACAA;MACA;MAEAA;MAEAC;QACAA;QACAD;QAEA;UACA;UACAK;;UAEA;UACA;YACAA;YACAL;UACA;;UAEA;UACAK;QACA;UACAL;UACAK;QACA;MACA;QACAJ;QACAD;QACAK;MACA;IACA;IAEA;IACAK;MAAA;MACAV;MACA;;MAEA;MACA;MACA,4CACAO,0FACAA;MAEAI;QACA;QACA;UAAAC;QACA;UACA;UACA;YACAC;YACAD;UACA;QACA;;QAEA;QACA;QACA;UACAE;QACA;UACA;UACA;UACA;UACA;UACAA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACAxB;UACAyB;UACAF;UACAD;UACAI;UACAC;QACA;MACA;;MAEAjB;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACA;YACAkB;YACAC;YACA;UACA;QACA;;QAEA;QACA;UACA;YACA;cACAnB;cACAoB;cACA;YACA;UACA;;UAEA;UACA;YACA;YACA;cACApB;cACA;YACA;cACA;cACAA;cACA;YACA;YACAC;UACA;QACA;UACA;UACA;YACA;YACAD;UACA;YACA;YACA;YACAA;UACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAqB;MAAA;MACA;MACA;MAEA;MACA;MAEA;QAAA;QACA;QACAC;QAEA;QACA;QACA;QACA;;QAEA;QACA,kCACAT,2CACAD;QAEA;QACA;UACAW;QACA;UACAA;QACA;QAEA;UACAjC;UACAyB;UACAF;UACAD;UACAI;UACAC;QACA;MACA;MAEAjB;;MAEA;MACA;MACAA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAwB;MACA;MACA;MACA;MACA;MACAxB;MACA;IACA;IAEA;IACAyB;MACA;IACA;IAEA;IACAC;MACA;MACAzB;;MAEA;MACA;MACA;QACA;QACA0B,8BACAC,kBACAA,kBACAA;MACA;MAEA5B;MAEA;QACA6B;MACA;;MAEA;MACA;MACApB;MAEAT;;MAEA;MACAC;QACAA;QACAD;;QAEA;QACA;UACAA;UACAK;UACA;QACA;QAEA;;QAEA;QACA;UACAyB;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACAzB;MACA;QACA;QACAJ;QACAD;QACAK;MACA;IACA;IAEA;IACA0B;MAAA;MACA/B;;MAEA;MACA;QACA;QACA;QACA;QACA;QACAA;;QAEA;QACA;UACA;UACA;;UAEA;UACA;YACA;YACA;YACA;cACA;cACA;gBACAgC;cACA;gBACA;gBACA;gBACA;kBACAA;gBACA;cACA;YACA;YAEA;cACAC;cACAC;cACAlC;YACA;UACA;;UAEA;UACA;YACAmC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;QAEAzC;;QAEA;QACA;QACA;QACA;UACA;YACA;cACA;cACA0C;cACA1C;cACA;YACA;UACA;QACA;UACA;UACA;YAAA,OACAkC;UAAA;UAEA;YACAlC;YACA;YACA;cACA;gBACA;gBACA0C;gBACA1C;gBACA;cACA;YACA;UACA;YACA0C;UACA;QACA;;QAEA;QACA;UACAzC;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACA0C;MACA;MACA;MACA;MACA;MAEA3C;MACAA;;MAEA;MACA;MACAA;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;UACAqC;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;QACA;QAEA;UACAO;UACAN;UACAtC;QACA;;QAEA;QACA;UACA6C;UACA7C;QACA;QAEA;UACAmC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACAzC;MACA;QACAA;MACA;MAEAA;IACA;IAEA;IACA8C;MACA9C;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;MACA;MACA;IACA;IAEA;IACA8C;MACA;MACA;QACA9C;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA0B,8BACAC,kBACAA,kBACAA;MACA;MAEA;QACAxB;QACA4C;QACAC;MACA;MAEAjD;MAEAC;MACAA;QACAA;QACAD;;QAEA;QACA;QACA;QACA;QAEA;UACAkD;UACAC;UACAC;QACA;UACAF;UACAC;UACAC;QACA;QAEA;UACAnD;YACA;YACA;cACA;gBACA;gBACAD;gBACA;gBACAC;gBACAD;cACA;gBACAA;gBACAC;cACA;YACA;cACAA;YACA;UACA;QACA;UACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzzBA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/appoint.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/appoint.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./appoint.vue?vue&type=template&id=755eb8c1&\"\nvar renderjs\nimport script from \"./appoint.vue?vue&type=script&lang=js&\"\nexport * from \"./appoint.vue?vue&type=script&lang=js&\"\nimport style0 from \"./appoint.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/appoint.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./appoint.vue?vue&type=template&id=755eb8c1&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.orderInfo && _vm.orderInfo.product ? _vm.t(\"color1\") : null\n  var l0 = _vm.__map(_vm.dateList, function (date, index) {\n    var $orig = _vm.__get_orig(date)\n    var m1 = _vm.selectedDate === date.value ? _vm.t(\"color1\") : null\n    var m2 = _vm.selectedDate === date.value ? _vm.t(\"color1rgb\") : null\n    return {\n      $orig: $orig,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var l1 = _vm.selectedDate\n    ? _vm.__map(_vm.timePeriods, function (period, idx) {\n        var $orig = _vm.__get_orig(period)\n        var m3 = _vm.activePeriod === period.value ? _vm.t(\"color1\") : null\n        var m4 = _vm.activePeriod === period.value ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var l2 = _vm.selectedDate\n    ? _vm.__map(_vm.filteredTimeList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m5 = _vm.selectedTime === item.time ? _vm.t(\"color1\") : null\n        var m6 = _vm.selectedTime === item.time ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m5: m5,\n          m6: m6,\n        }\n      })\n    : null\n  var m7 = _vm.canSubmit ? _vm.t(\"color1\") : null\n  var m8 = _vm.canSubmit ? _vm.t(\"color1rgb\") : null\n  var m9 = _vm.canSubmit ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./appoint.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./appoint.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"order-info\" v-if=\"orderInfo && orderInfo.product\">\n      <view class=\"product-info\">\n        <image :src=\"orderInfo.product.pic\" mode=\"aspectFill\"></image>\n        <view class=\"info\">\n          <text class=\"name\">{{orderInfo.product.name}}</text>\n          <text class=\"price\" :style=\"{color:t('color1')}\">￥{{orderInfo.product.sell_price}}</text>\n        </view>\n      </view>\n      <view class=\"order-detail\">\n        <text>订单号：{{orderInfo.order_no}}</text>\n        <text>支付时间：{{orderInfo.pay_time}}</text>\n      </view>\n    </view>\n\n    <view class=\"appoint-section\">\n      <view class=\"section-title\">选择预约时间</view>\n      \n      <!-- 优化日期选择器 -->\n      <view class=\"date-selector\">\n        <view class=\"date-header\">\n          <text class=\"date-title\">选择日期</text>\n          <text class=\"date-subtitle\">请选择您方便的日期</text>\n        </view>\n        \n        <view class=\"date-scroll-container\">\n          <scroll-view scroll-x class=\"date-scroll\" show-scrollbar=\"false\">\n            <view \n              v-for=\"(date, index) in dateList\" \n              :key=\"index\" \n              class=\"date-item\" \n              :class=\"{'date-active': selectedDate === date.value}\"\n              :style=\"selectedDate === date.value ? 'background:'+t('color1')+'; box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : ''\"\n              @tap=\"selectDate(date)\">\n              <text class=\"date-weekday\" :style=\"selectedDate === date.value ? 'color:#fff' : ''\">{{date.weekday}}</text>\n              <text class=\"date-day\" :style=\"selectedDate === date.value ? 'color:#fff' : ''\">{{date.day}}</text>\n              <text class=\"date-month\" :style=\"selectedDate === date.value ? 'color:#fff' : ''\">{{date.month}}月</text>\n            </view>\n          </scroll-view>\n        </view>\n      </view>\n      \n      <!-- 优化时间段选择 -->\n      <view class=\"time-selector\" v-if=\"selectedDate\">\n        <view class=\"time-header\">\n          <text class=\"time-title\">选择时间段</text>\n          <text class=\"time-subtitle\">请选择您方便的时间</text>\n        </view>\n        \n        <view class=\"time-period-tabs\">\n          <view \n            v-for=\"(period, idx) in timePeriods\" \n            :key=\"idx\"\n            class=\"time-period-tab\"\n            :class=\"{'period-active': activePeriod === period.value}\"\n            :style=\"activePeriod === period.value ? 'background:'+t('color1')+'; box-shadow: 0 2rpx 6rpx rgba('+t('color1rgb')+',0.2); color: #fff' : ''\"\n            @tap=\"switchPeriod(period.value)\">\n            {{period.label}}\n          </view>\n        </view>\n        \n        <view class=\"time-list\">\n          <view class=\"time-item\" \n            v-for=\"(item, index) in filteredTimeList\" \n            :key=\"index\"\n            :class=\"{'disabled': !item.available}\"\n            :style=\"selectedTime === item.time ? 'background:'+t('color1')+'; transform: scale(1.05); box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : ''\"\n            @tap=\"selectTime(item)\">\n            <view class=\"time-content\">\n              <text class=\"time-text\" :style=\"selectedTime === item.time ? 'color:#fff' : ''\">{{item.time}}</text>\n              <text class=\"time-status\" :style=\"selectedTime === item.time ? 'color:#fff' : ''\">\n                <template v-if=\"item.available\">可预约</template>\n                <template v-else>{{item.reason || '已约满'}}</template>\n              </text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"footer\">\n      <view class=\"selected-info\" v-if=\"selectedDate && selectedTime\">\n        <text>已选：{{formattedSelectedDate}} {{selectedTime}}</text>\n      </view>\n      <button class=\"submit-btn\" :disabled=\"!canSubmit\" \n        :style=\"canSubmit ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%); box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : 'background:#ccc'\" \n        @tap=\"submitAppoint\">确认预约</button>\n    </view>\n  </view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n      orderId: '',\n      orderInfo: {\n        product: {\n          pic: '',\n          name: '',\n          sell_price: 0\n        },\n        order_no: '',\n        pay_time: ''\n      },\n      selectedDate: '',\n      selectedTime: '',\n      timeList: [],\n      dateList: [],\n      startDate: '',\n      endDate: '',\n      loading: true,\n      activePeriod: 'all',\n      timePeriods: [\n        { label: '全部', value: 'all' },\n        { label: '上午', value: '上午' },\n        { label: '下午', value: '下午' },\n        { label: '傍晚', value: '傍晚' }\n      ],\n      productId: '', // 存储商品ID\n      dateType: 1,    // 日期类型：1基于周期，2固定范围，3指定天数\n      dayDate: '',     // 当前日期\n      dateFromUrl: false // 标记日期是否来自URL参数\n    }\n  },\n  \n  computed: {\n    canSubmit() {\n      return this.selectedDate && this.selectedTime;\n    },\n    \n    filteredTimeList() {\n      if (this.activePeriod === 'all') {\n        return this.timeList;\n      } else {\n        return this.timeList.filter(item => item.period === this.activePeriod);\n      }\n    },\n    \n    formattedSelectedDate() {\n      if (!this.selectedDate) return '';\n      \n      if (this.selectedDate.includes('-')) {\n        const parts = this.selectedDate.split('-');\n        return `${parts[0]}年${parts[1]}月${parts[2]}日`;\n      }\n      \n      return this.selectedDate;\n    }\n  },\n  \n  onLoad(options) {\n    console.log('加载预约页面，参数:', options);\n    // 初始化日期范围 - 提前初始化，确保有默认值\n    this.initDateRange();\n    \n    if(options && options.id) {\n      this.orderId = options.id;\n      \n      // 如果传入了日期参数，保存起来\n      if(options.date) {\n        console.log('已传入日期参数:', options.date);\n        this.selectedDate = options.date;\n        // 标记日期来自URL参数\n        this.dateFromUrl = true;\n      } else {\n        // 没有传入日期参数\n        console.log('未传入日期参数，不会向接口发送日期');\n        this.dateFromUrl = false;\n      }\n      \n      this.getOrderInfo();\n    } else {\n      app.error('未找到订单信息');\n    }\n  },\n  \n  methods: {\n    // 获取订单信息\n    getOrderInfo() {\n      var that = this;\n      app.showLoading('加载订单信息');\n      \n      console.log('当前选择的日期(获取订单前):', that.selectedDate);\n      \n      app.get('ApiYuyue/orderDetail', {\n        id: that.orderId\n      }, function(res) {\n        app.showLoading(false);\n        console.log('获取订单信息原始返回:', res);\n        \n        // 兼容不同的返回结构\n        if (res.status == 1 && res.data) {\n          console.log('标准接口返回结构 status+data');\n          that.orderInfo = res.data;\n        } else if (res.detail) {\n          // 直接返回detail结构\n          console.log('直接detail结构返回');\n          that.orderInfo = res.detail;\n        } else {\n          // 尝试直接使用返回对象\n          console.log('使用整个返回对象');\n          that.orderInfo = res;\n        }\n        \n        console.log('处理后的订单信息:', that.orderInfo);\n        \n        // 确保product存在\n        if(!that.orderInfo.product) {\n          // 构建产品信息\n          that.orderInfo.product = {\n            pic: that.orderInfo.propic || '',\n            name: that.orderInfo.proname || '商品信息',\n            sell_price: that.orderInfo.product_price || that.orderInfo.totalprice || 0\n          };\n          console.log('构建的产品信息:', that.orderInfo.product);\n        }\n        \n        // 添加订单号和支付时间\n        if (!that.orderInfo.order_no) {\n          that.orderInfo.order_no = that.orderInfo.ordernum || '';\n        }\n        \n        if (!that.orderInfo.pay_time) {\n          // 尝试转换时间戳格式的支付时间\n          if (that.orderInfo.paytime) {\n            if (typeof that.orderInfo.paytime === 'number') {\n              let date = new Date(that.orderInfo.paytime * 1000);\n              that.orderInfo.pay_time = date.getFullYear() + '-' +\n                ('0' + (date.getMonth() + 1)).slice(-2) + '-' +\n                ('0' + date.getDate()).slice(-2) + ' ' +\n                ('0' + date.getHours()).slice(-2) + ':' +\n                ('0' + date.getMinutes()).slice(-2) + ':' +\n                ('0' + date.getSeconds()).slice(-2);\n            } else {\n              that.orderInfo.pay_time = that.orderInfo.paytime;\n            }\n          } else {\n            that.orderInfo.pay_time = '';\n          }\n        }\n        \n        // 从订单信息中提取商品ID\n        that.productId = that.orderInfo.product_id || that.orderInfo.proid || '';\n        console.log('提取的商品ID:', that.productId);\n        console.log('当前选择的日期(订单信息处理后):', that.selectedDate);\n          \n        that.loading = false;\n        \n        // 获取可预约日期列表\n        that.getDateList();\n      });\n    },\n    \n    // 初始化日期范围\n    initDateRange() {\n      var today = new Date();\n      this.startDate = today.toISOString().split('T')[0];\n      \n      var endDate = new Date();\n      endDate.setDate(today.getDate() + 30); // 最多可预约30天\n      this.endDate = endDate.toISOString().split('T')[0];\n      \n      // 保存当前日期，格式：YYYY-MM-DD\n      this.dayDate = today.getFullYear() + '-' + \n        ((today.getMonth() + 1) < 10 ? '0' + (today.getMonth() + 1) : (today.getMonth() + 1)) + '-' + \n        (today.getDate() < 10 ? '0' + today.getDate() : today.getDate());\n        \n      // 设置默认选中当天日期（如果不是从URL获取的日期）\n      if(!this.selectedDate) {\n        this.selectedDate = this.dayDate;\n        console.log('初始化选中当天日期:', this.selectedDate);\n      }\n    },\n    \n    // 获取可预约日期列表\n    getDateList() {\n      var that = this;\n      // 确保有商品ID\n      const productId = that.productId || that.orderId;\n      \n      console.log('获取日期列表前，当前选择的日期:', that.selectedDate);\n      console.log('当前系统日期:', that.dayDate);\n      console.log('日期是否来自URL:', that.dateFromUrl);\n      \n      if (!productId) {\n        console.log('未找到商品ID，使用本地生成的日期列表');\n        that.generateDefaultDateList();\n        return;\n      }\n      \n      app.showLoading('获取可预约日期');\n      \n      // 构建参数对象\n      const params = {\n        id: productId\n      };\n      \n      // 只有当日期来自URL时才传递给接口\n      if (that.dateFromUrl && that.selectedDate) {\n        params.date = that.selectedDate;\n        console.log('将URL指定的日期传递给接口:', that.selectedDate);\n      } else {\n        console.log('日期未从URL获取，不传递给接口');\n      }\n      \n      console.log('获取日期列表参数:', params);\n      \n      app.get('ApiYuyue/getDateList', params, function(res) {\n        app.showLoading(false);\n        console.log('获取日期列表返回:', res);\n        \n        if (res.status == 1 && res.datelist && res.datelist.length > 0) {\n          // 更新日期类型\n          that.dateType = res.rqtype || 1;\n          \n          // 更新当前日期\n          if (res.daydate) {\n            that.dayDate = res.daydate;\n            console.log('接口返回的当前日期:', that.dayDate);\n          }\n          \n          // 处理接口返回的日期列表\n          that.processDateList(res.datelist);\n        } else {\n          console.log('接口未返回有效的日期列表，使用默认生成');\n          that.generateDefaultDateList();\n        }\n      }, function(err) {\n        app.showLoading(false);\n        console.log('获取日期列表失败，使用默认列表:', err);\n        that.generateDefaultDateList();\n      });\n    },\n    \n    // 处理接口返回的日期列表\n    processDateList(apiDateList) {\n      console.log('处理接口返回的日期列表:', apiDateList);\n      this.dateList = [];\n      \n      // 获取今天的日期用于比较\n      const today = new Date();\n      const todayStr = today.getFullYear() + '-' + \n        ((today.getMonth() + 1) < 10 ? '0' + (today.getMonth() + 1) : (today.getMonth() + 1)) + '-' + \n        (today.getDate() < 10 ? '0' + today.getDate() : today.getDate());\n        \n      apiDateList.forEach(item => {\n        // 解析日期部分\n        let month = '', day = '';\n        if (item.date) {\n          const dateParts = item.date.split('月');\n          if (dateParts.length > 1) {\n            month = dateParts[0].replace('月', '');\n            day = dateParts[1];\n          }\n        }\n        \n        // 构建完整日期\n        let fullDate = '';\n        if (item.full_date) {\n          fullDate = item.full_date;\n        } else if (item.year && item.date) {\n          // 从year和date构建，格式如：2025年 和 04月12\n          const year = item.year.replace('年', '');\n          const month = item.date.split('月')[0];\n          const day = item.date.split('月')[1];\n          fullDate = `${year}-${month}-${day}`;\n        }\n        \n        // 直接使用接口返回的周几标签，不再进行今天/明天的替换\n        // 这样可以避免与接口返回的 daydate 不匹配的问题\n        let dayLabel = day;\n        let weekday = item.weeks || '';\n        \n        // 构建日期项\n        this.dateList.push({\n          value: fullDate,\n          year: item.year ? item.year.replace('年', '') : '',\n          month: month,\n          day: dayLabel,\n          weekday: weekday,\n          isToday: fullDate === todayStr // 标记是否为今天\n        });\n      });\n      \n      console.log('处理后的日期列表:', this.dateList);\n      \n      // 根据是否有预选日期选中相应日期\n      if (this.dateList.length > 0) {\n        let foundPreselectedDate = false;\n        let foundTodayDate = false;\n        let todayIndex = -1;\n        \n        // 首先查找今天的日期\n        for (let i = 0; i < this.dateList.length; i++) {\n          if (this.dateList[i].isToday) {\n            foundTodayDate = true;\n            todayIndex = i;\n            break;\n          }\n        }\n        \n        // 如果有预选的日期，找到对应的日期选中\n        if (this.selectedDate) {\n          for (let i = 0; i < this.dateList.length; i++) {\n            if (this.dateList[i].value === this.selectedDate) {\n              console.log('找到预选日期:', this.selectedDate);\n              foundPreselectedDate = true;\n              break;\n            }\n          }\n          \n          // 如果没找到预选日期在列表中\n          if (!foundPreselectedDate) {\n            // 优先选择今天的日期\n            if (foundTodayDate) {\n              console.log('选择今天的日期:', this.dateList[todayIndex].value);\n              this.selectedDate = this.dateList[todayIndex].value;\n            } else {\n              // 如果没有今天，则选择第一个可用日期\n              console.log('预选日期不在可用日期列表中，使用第一个可用日期');\n              this.selectedDate = this.dateList[0].value;\n            }\n            app.toast('选择的日期不可用，已为您选择最近可用日期');\n          }\n        } else {\n          // 如果没有预选日期，优先选择今天\n          if (foundTodayDate) {\n            this.selectedDate = this.dateList[todayIndex].value;\n            console.log('选择今天的日期:', this.selectedDate);\n          } else {\n            // 如果没有今天，选中第一个\n            this.selectedDate = this.dateList[0].value;\n            console.log('没有找到今天的日期，选择第一个日期:', this.selectedDate);\n          }\n        }\n        \n        // 加载时间段\n        this.$nextTick(() => {\n          this.getTimeList();\n        });\n      }\n    },\n    \n    // 生成默认日期列表（当接口不可用时）\n    generateDefaultDateList() {\n      this.dateList = [];\n      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n      \n      const today = new Date();\n      const todayStr = today.toISOString().split('T')[0];\n      \n      for (let i = 0; i < 14; i++) { // 显示未来14天\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n        \n        const year = date.getFullYear();\n        const month = date.getMonth() + 1;\n        const day = date.getDate();\n        const weekday = weekdays[date.getDay()];\n        \n        // 格式化日期为YYYY-MM-DD\n        const formattedDate = year + '-' + \n          (month < 10 ? '0' + month : month) + '-' + \n          (day < 10 ? '0' + day : day);\n        \n        let dayLabel = day.toString();\n        if (i === 0) {\n          dayLabel = '今天';\n        } else if (i === 1) {\n          dayLabel = '明天';\n        }\n        \n        this.dateList.push({\n          value: formattedDate,\n          year: year,\n          month: month,\n          day: dayLabel,\n          weekday: weekday,\n          isToday: i === 0\n        });\n      }\n      \n      console.log('生成的默认日期列表:', this.dateList);\n      \n      // 默认选中当天日期\n      this.selectedDate = todayStr;\n      console.log('默认选中当天日期:', this.selectedDate);\n      \n      // 加载当天时间段\n      this.$nextTick(() => {\n        this.getTimeList();\n      });\n    },\n    \n    // 选择日期（从日期滚动列表）\n    selectDate(date) {\n      this.selectedDate = date.value;\n      this.selectedTime = '';\n      // 用户主动选择了日期，此时不再依赖URL参数\n      this.dateFromUrl = false;\n      console.log('用户选择了新日期, dateFromUrl设为false');\n      this.getTimeList();\n    },\n    \n    // 切换时间段筛选\n    switchPeriod(period) {\n      this.activePeriod = period;\n    },\n    \n    // 获取时间段列表\n    getTimeList() {\n      var that = this;\n      app.showLoading('加载时间段');\n      \n      // 确保日期格式正确 (YYYY-MM-DD)\n      let formattedDate = that.selectedDate;\n      if (formattedDate && formattedDate.includes('年')) {\n        // 转换中文格式日期 (YYYY年MM月DD日) 为 YYYY-MM-DD\n        formattedDate = formattedDate\n          .replace('年', '-')\n          .replace('月', '-')\n          .replace('日', '');\n      }\n      \n      console.log('当前选择的日期:', formattedDate);\n      \n      const params = {\n        order_id: that.orderId\n      };\n      \n      // 这里的日期已经由用户选择，因此不需要检查dateFromUrl标记\n      // 与getDateList不同，时间段查询必须要有日期参数\n      params.date = formattedDate;\n      \n      console.log('获取时间段参数:', params);\n      \n      // 从API获取时间段列表\n      app.get('ApiYuyue/getTimeList', params, function(res) {\n        app.showLoading(false);\n        console.log('获取时间段返回:', res);\n        \n        // 检查是否为HTML错误页面或者接口错误\n        if (typeof res === 'string' && res.indexOf('<!DOCTYPE html>') !== -1 || (res.status === 0 && res.msg)) {\n          console.log('接口报错，使用默认时间段');\n          that.createDefaultTimeSlots();\n          return;\n        }\n        \n        let timeData = [];\n        \n        // 兼容不同的返回结构\n        if (res.status == 1 && res.data) {\n          timeData = res.data;\n        } else if (res.data) {\n          timeData = res.data;\n        } else if (Array.isArray(res)) {\n          timeData = res;\n        }\n        \n        // 处理并设置时间段\n        that.processTimeData(timeData);\n      }, function(err) {\n        // 请求出错的回调处理\n        app.showLoading(false);\n        console.log('时间段获取失败，使用默认时间段:', err);\n        that.createDefaultTimeSlots();\n      });\n    },\n    \n    // 处理时间段数据\n    processTimeData(timeData) {\n      console.log('提取的时间段数据:', timeData);\n      \n      // 设置时间段列表\n      if (timeData && timeData.length > 0) {\n        // 检查当前时间，用于筛选今天已过期的时间段\n        const today = new Date();\n        const currentHour = today.getHours();\n        const selectedDateIsToday = this.selectedDate === today.toISOString().split('T')[0];\n        console.log('处理时间段数据 - 当前小时:', currentHour, '是否今天:', selectedDateIsToday);\n        \n        // 处理API返回的时间段格式\n        this.timeList = timeData.map(item => {\n          // 检查是否有disabled字段，如果没有则使用available字段\n          let isAvailable = item.disabled !== undefined ? !item.disabled : (item.available !== undefined ? item.available : true);\n          \n          // 如果是今天，检查时间是否已过期\n          if (selectedDateIsToday && isAvailable) {\n            // 从时间字符串中提取小时\n            let hour = -1;\n            if (item.time) {\n              const timeParts = item.time.split(':');\n              if (timeParts.length > 0) {\n                hour = parseInt(timeParts[0]);\n              } else if (item.time.includes('-')) {\n                // 处理类似 \"08:00-09:00\" 格式\n                const parts = item.time.split('-')[0].split(':');\n                if (parts.length > 0) {\n                  hour = parseInt(parts[0]);\n                }\n              }\n            }\n            \n            if (hour >= 0 && hour <= currentHour) {\n              isAvailable = false;\n              item.reason = '已过期';\n              console.log('时间段已过期:', item.time);\n            }\n          }\n          \n          // 直接使用接口返回的时间格式，不做转换\n          return {\n            time: item.time,\n            available: isAvailable,\n            period: item.period || '',\n            reason: item.reason || '不可预约',\n            booked_count: item.booked_count || 0,\n            max_bookings: item.max_bookings || 999999,\n            full_time: item.full_time || ''\n          };\n        });\n        \n        console.log('处理后的时间段数据:', this.timeList);\n        \n        // 检查是否有可用的时间段\n        let hasAvailable = false;\n        // 默认选择第一个可用的时间段\n        if (!this.selectedTime) {\n          for (let i = 0; i < this.timeList.length; i++) {\n            if (this.timeList[i].available) {\n              this.selectedTime = this.timeList[i].time;\n              hasAvailable = true;\n              console.log('自动选择第一个可用时间段:', this.selectedTime);\n              break;\n            }\n          }\n        } else {\n          // 检查已选择的时间段是否可用\n          const selectedTimeAvailable = this.timeList.some(item => \n            item.time === this.selectedTime && item.available);\n            \n          if (!selectedTimeAvailable) {\n            console.log('已选择的时间段不可用:', this.selectedTime);\n            // 尝试选择第一个可用的时间段\n            for (let i = 0; i < this.timeList.length; i++) {\n              if (this.timeList[i].available) {\n                this.selectedTime = this.timeList[i].time;\n                hasAvailable = true;\n                console.log('重新选择第一个可用时间段:', this.selectedTime);\n                break;\n              }\n            }\n          } else {\n            hasAvailable = true;\n          }\n        }\n        \n        // 如果没有可用时间段，提示用户\n        if (!hasAvailable) {\n          app.toast('当前日期没有可用的预约时间段，请选择其他日期');\n          this.selectedTime = ''; // 清空不可用的时间选择\n        }\n      } else {\n        // 如果没有数据，使用默认时间段\n        this.createDefaultTimeSlots();\n      }\n    },\n    \n    // 创建默认时间段\n    createDefaultTimeSlots() {\n      this.timeList = [];\n      var today = new Date();\n      var currentHour = today.getHours();\n      let firstAvailableTime = '';\n      \n      console.log('创建默认时间段，当前小时:', currentHour);\n      console.log('当前选择的日期:', this.selectedDate, '今天日期:', today.toISOString().split('T')[0]);\n      \n      // 判断选择的日期是否是今天\n      const selectedDateIsToday = this.selectedDate === today.toISOString().split('T')[0];\n      console.log('选择的日期是否为今天:', selectedDateIsToday);\n      \n      // 添加默认时间段，从8点到20点，每1小时一个时间段\n      for (let i = 8; i < 20; i++) {\n        const start = i < 10 ? '0' + i + ':00' : i + ':00';\n        const end = (i + 1) < 10 ? '0' + (i + 1) + ':00' : (i + 1) + ':00';\n        const timeStr = start + '-' + end;\n        \n        // 设置时间段\n        let period = '';\n        if (i < 12) {\n          period = '上午';\n        } else if (i < 17) {\n          period = '下午';\n        } else {\n          period = '傍晚';\n        }\n        \n        // 今天的过期时间段不可选\n        let disabled = false;\n        let reason = '';\n        \n        if (selectedDateIsToday && i <= currentHour) {\n          disabled = true;\n          reason = '已过期';\n          console.log('时间段已过期:', timeStr);\n        }\n        \n        // 记录第一个可用的时间段\n        if (!disabled && !firstAvailableTime) {\n          firstAvailableTime = timeStr;\n          console.log('找到第一个可用时间段:', timeStr);\n        }\n        \n        this.timeList.push({\n          time: timeStr,\n          available: !disabled,\n          period: period,\n          reason: reason,\n          booked_count: 0,\n          max_bookings: 999999,\n          full_time: this.selectedDate + ' ' + timeStr\n        });\n      }\n      \n      // 默认选择第一个可用的时间段\n      if (!this.selectedTime && firstAvailableTime) {\n        this.selectedTime = firstAvailableTime;\n        console.log('自动选择默认时间段:', this.selectedTime);\n      } else {\n        console.log('保留已选择的时间段:', this.selectedTime);\n      }\n      \n      console.log('创建的默认时间段:', this.timeList);\n    },\n    \n    // 选择时间段\n    selectTime(item) {\n      console.log('选择时间段:', item);\n      if(!item.available) {\n        // 显示不可选原因\n        if(item.reason) {\n          app.toast(item.reason);\n        } else {\n          app.toast('该时间段不可预约');\n        }\n        return;\n      }\n      this.selectedTime = item.time;\n    },\n    \n    // 提交预约\n    submitAppoint() {\n      var that = this;\n      if(!that.selectedDate || !that.selectedTime) {\n        app.error('请选择预约日期和时间');\n        return;\n      }\n      \n      // 确保日期格式正确 (YYYY-MM-DD)\n      let formattedDate = that.selectedDate;\n      if (formattedDate && formattedDate.includes('年')) {\n        // 转换中文格式日期 (YYYY年MM月DD日) 为 YYYY-MM-DD\n        formattedDate = formattedDate\n          .replace('年', '-')\n          .replace('月', '-')\n          .replace('日', '');\n      }\n      \n      const params = {\n        id: that.orderId,\n        yydate: formattedDate,\n        yytime: that.selectedTime\n      };\n      \n      console.log('提交预约参数:', params);\n      \n      app.showLoading('提交预约');\n      app.post('ApiYuyue/appointTime', params, function(res) {\n        app.showLoading(false);\n        console.log('预约接口返回:', res);\n        \n        // 处理不同的返回结构\n        let success = false;\n        let message = '';\n        let needSelectWorker = false;\n        \n        if (res.status == 1) {\n          success = true;\n          message = res.msg || '预约成功';\n          needSelectWorker = res.needSelectWorker || false;\n        } else if (res.code == 200 || res.code == 1) {\n          success = true;\n          message = res.message || res.msg || '预约成功';\n          needSelectWorker = res.needSelectWorker || false;\n        }\n        \n        if (success) {\n          app.alert(message, function() {\n            // 判断是否需要继续选择服务人员\n            if (needSelectWorker) {\n              try {\n                // 使用完整的路径格式\n                console.log('转到选择服务人员页面');\n                const url = '/yuyue/selectworker?id=' + that.orderId + '&yydate=' + formattedDate;\n                app.goto(url);\n                console.log('导航到选择服务人员页面:', url);\n              } catch (e) {\n                console.error('导航失败:', e);\n                app.error('页面跳转失败');\n              }\n            } else {\n              app.goto('/yuyue/orderlist');\n            }\n          });\n        } else {\n          // 预约失败，显示错误信息\n          app.error(res.msg || res.message || '预约失败');\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n  padding: 20rpx;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.order-info {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.product-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.product-info image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n}\n\n.info {\n  flex: 1;\n}\n\n.name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.price {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.order-detail {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.order-detail text {\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.appoint-section {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 120rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n/* 日期选择器样式 */\n.date-selector {\n  margin-bottom: 30rpx;\n}\n\n.date-header {\n  margin-bottom: 20rpx;\n}\n\n.date-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  display: block;\n}\n\n.date-subtitle {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 6rpx;\n  display: block;\n}\n\n.date-scroll-container {\n  margin: 0 -20rpx;\n}\n\n.date-scroll {\n  white-space: nowrap;\n  padding: 20rpx;\n}\n\n.date-item {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 100rpx;\n  height: 130rpx;\n  background: #f7f7f7;\n  border-radius: 12rpx;\n  margin-right: 15rpx;\n  padding: 10rpx 0;\n  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\n  transition: all 0.3s;\n}\n\n.date-weekday {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.date-day {\n  font-size: 34rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 4rpx;\n}\n\n.date-month {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.date-picker-fallback {\n  margin-top: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n\n.date-picker-fallback text {\n  font-size: 24rpx;\n  color: #999;\n  margin-right: 10rpx;\n}\n\n.picker-fallback-btn {\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border: 1rpx solid;\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n}\n\n/* 时间选择器样式 */\n.time-selector {\n  margin-top: 30rpx;\n}\n\n.time-header {\n  margin-bottom: 20rpx;\n}\n\n.time-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  display: block;\n}\n\n.time-subtitle {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 6rpx;\n  display: block;\n}\n\n.time-period-tabs {\n  display: flex;\n  margin-bottom: 20rpx;\n  background: #f7f7f7;\n  border-radius: 8rpx;\n  padding: 6rpx;\n}\n\n.time-period-tab {\n  flex: 1;\n  height: 60rpx;\n  line-height: 60rpx;\n  text-align: center;\n  font-size: 26rpx;\n  color: #666;\n  border-radius: 6rpx;\n  transition: all 0.3s;\n}\n\n.time-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.time-item {\n  width: 30%;\n  height: 90rpx;\n  margin: 1.5%;\n  border-radius: 8rpx;\n  background: #f7f7f7;\n  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\n  overflow: hidden;\n  transition: all 0.3s;\n}\n\n.time-content {\n  height: 100%;\n  padding: 10rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.time-text {\n  font-size: 26rpx;\n  color: #333;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.time-status {\n  font-size: 20rpx;\n  color: #999;\n  margin-top: 6rpx;\n}\n\n.time-item.disabled {\n  background: #f0f0f0;\n  opacity: 0.8;\n}\n\n.time-item.disabled .time-text {\n  color: #999;\n}\n\n.footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 20rpx;\n  background: #fff;\n  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.selected-info {\n  font-size: 26rpx;\n  color: #333;\n  text-align: center;\n  margin-bottom: 15rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  color: #fff;\n  font-size: 30rpx;\n  border-radius: 40rpx;\n  transition: all 0.3s;\n}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./appoint.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./appoint.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115042690\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}