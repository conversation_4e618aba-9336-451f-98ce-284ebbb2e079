{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?00ba", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?fd67", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?2b7d", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?8eac", "uni-app:///yuyue/comments.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?41f4", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/comments.vue?3007"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "pagenum", "pernum", "nomore", "nodata", "avg_score", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "console", "previewImage", "uni", "current", "urls", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsDjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;;MAEA;MACA;QACAV;QACAC;MACA;MAEAU;QACAD;QACAE;QACA;UACA;UACA;UACA;UACA;UACA;UACAF;UAEA;YACAA;YACA;cACAA;YACA;YACAA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;;cAEA;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAC;UACAD;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAH;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9JA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/comments.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/comments.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./comments.vue?vue&type=template&id=0fb8b3a4&\"\nvar renderjs\nimport script from \"./comments.vue?vue&type=script&lang=js&\"\nexport * from \"./comments.vue?vue&type=script&lang=js&\"\nimport style0 from \"./comments.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/comments.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comments.vue?vue&type=template&id=0fb8b3a4&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.datalist, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.content_pic && item.content_pic.length > 0\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comments.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comments.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<view class=\"avg-score\" v-if=\"avg_score\">\r\n\t\t<view class=\"score-title\">平均评分</view>\r\n\t\t<view class=\"score-value\">{{avg_score}}</view>\r\n\t\t<view class=\"stars\">\r\n\t\t\t<image class=\"star\" v-for=\"(item,index) in 5\" :key=\"index\" :src=\"'/static/img/star' + (avg_score>=item?'2':'') + '.png'\"/>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"comment\">\r\n\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<image class=\"t1\" :src=\"item.headimg\"/>\r\n\t\t\t\t<view class=\"t2\">{{item.nickname}}</view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t<text class=\"comment-tag\" :class=\"{\r\n\t\t\t\t\t\t'good': item.score >= 4,\r\n\t\t\t\t\t\t'middle': item.score == 3,\r\n\t\t\t\t\t\t'bad': item.score <= 2\r\n\t\t\t\t\t}\">{{item.score >= 4 ? '好评' : (item.score == 3 ? '中评' : '差评')}}</text>\r\n\t\t\t\t\t<image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (item.score>item2?'2':'') + '.png'\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"color:#777;font-size:22rpx;\">{{item.createtime}}</view>\r\n\t\t\t<view class=\"f2\">\r\n\t\t\t\t<text class=\"t1\">{{item.content}}</text>\r\n\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t<block v-if=\"item.content_pic && item.content_pic.length>0\">\r\n\t\t\t\t\t\t<block v-for=\"(itemp, index) in item.content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"item.content_pic\">\r\n\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t4\" v-if=\"item.ordernum\">\r\n\t\t\t\t\t<text>订单号：{{item.ordernum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t4\" v-if=\"item.title\">\r\n\t\t\t\t\t<text>项目：{{item.title}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n  </view>\r\n\t\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<nodata v-if=\"nodata\" text=\"暂无评价~\"></nodata>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      pernum: 10, // 每页条数\r\n      nomore: false,\r\n      nodata: false,\r\n\t\t\tavg_score: null\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var pernum = that.pernum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n      \r\n      // 根据筛选类型设置评分范围参数\r\n      var params = {\r\n        pagenum: pagenum, \r\n        pernum: pernum\r\n      };\r\n      \r\n      app.post('ApiYuyueWorker/getComments', params, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tconsole.log(\"评论数据返回:\", res); // 添加日志查看返回数据\r\n\t\t\t\tif(res.status == 1){\r\n          // 正确获取list列表\r\n          var data = res.data && res.data.list ? res.data.list : [];\r\n          // 获取总数\r\n          var total = res.data && res.data.total_count ? parseInt(res.data.total_count) : 0;\r\n          // 获取平均评分\r\n          that.avg_score = res.data && res.data.avg_score ? res.data.avg_score : null;\r\n          \r\n          if (pagenum == 1) {\r\n            that.datalist = data;\r\n            if (data.length == 0) {\r\n              that.nodata = true;\r\n            }\r\n            that.loaded();\r\n          } else {\r\n            if (data.length == 0) {\r\n              that.nomore = true;\r\n            } else {\r\n              var datalist = that.datalist;\r\n              var newdata = datalist.concat(data);\r\n              that.datalist = newdata;\r\n              \r\n              // 判断是否已加载全部数据\r\n              if (newdata.length >= total) {\r\n                that.nomore = true;\r\n              }\r\n            }\r\n          }\r\n\t\t\t\t} else {\r\n          app.toast(res.msg || '加载失败');\r\n          that.nodata = true;\r\n\t\t\t\t}\r\n      });\r\n    },\r\n    \r\n    // 图片预览\r\n    previewImage: function(e) {\r\n      var url = e.currentTarget.dataset.url;\r\n      var urls = e.currentTarget.dataset.urls;\r\n      uni.previewImage({\r\n        current: url,\r\n        urls: urls\r\n      });\r\n    },\r\n    \r\n    // 页面加载完成\r\n    loaded: function(){\r\n      this.isload = true;\r\n      uni.stopPullDownRefresh();\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{background:#f8f8f8; min-height: 100vh;}\r\n.comment{display:flex;flex-direction:column;padding:10rpx 0;}\r\n.comment .item{background-color:#fff;padding:20rpx;display:flex;flex-direction:column;margin-bottom:20rpx;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .flex1{flex:1;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;line-height:1.5;}\r\n.comment .item .f2 .t2{display:flex;width:100%;flex-wrap:wrap;margin-top:10rpx;}\r\n.comment .item .f2 .t2 image{width:160rpx;height:160rpx;margin:10rpx;border-radius:8rpx;}\r\n.comment .item .f2 .t4{color:#aaa;font-size:24rpx;margin-top:10rpx;}\r\n\r\n.avg-score{display:flex;flex-direction:column;align-items:center;background:#fff;padding:30rpx;margin-bottom:20rpx;}\r\n.avg-score .score-title{font-size:28rpx;color:#666;}\r\n.avg-score .score-value{font-size:48rpx;color:#333;font-weight:bold;margin:10rpx 0;}\r\n.avg-score .stars{display:flex;margin-top:10rpx;}\r\n.avg-score .stars .star{width:32rpx;height:32rpx;margin:0 5rpx;}\r\n\r\n.comment-tag {\r\n  font-size: 20rpx;\r\n  padding: 2rpx 10rpx;\r\n  border-radius: 5rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.good {\r\n  background-color: #06A051;\r\n  color: #fff;\r\n}\r\n\r\n.middle {\r\n  background-color: #FFA500;\r\n  color: #fff;\r\n}\r\n\r\n.bad {\r\n  background-color: #FF0000;\r\n  color: #fff;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comments.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./comments.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045425\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}