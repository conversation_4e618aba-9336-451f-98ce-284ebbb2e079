{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?693d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?2012", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?0316", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?9a8a", "uni-app:///pagesExt/cityagent/bind.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?ca22", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/bind.vue?55ae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "apply_status", "agentInfo", "submitting", "formData", "name", "tel", "agent_level", "remark", "agentLevels", "id", "selectedLevel", "computed", "statusIcon", "statusText", "statusDesc", "onLoad", "methods", "getColor", "console", "getColorRgb", "checkApplyStatus", "app", "that", "onLevelChange", "submitApply", "uni", "setTimeout", "goBack", "getAgentLevelName", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,owBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsH7wB;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,cACA;QAAAC;QAAAL;MAAA,GACA;QAAAK;QAAAL;MAAA,GACA;QAAAK;QAAAL;MAAA,GACA;QAAAK;QAAAL;MAAA,EACA;MACAM;QAAAD;QAAAL;MAAA;IACA;EACA;EAEAO;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAD;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;MACA;QACAC;UAAAhB;QAAA;UACA;YACAiB;YACAA;UACA;YACAA;UACA;QACA;MACA;QACA;QACAD;UACA;YACAC;YACAA;UACA;YACAA;UACA;QACA;UACA;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAH;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;MACA;QACAA;QACA;MACA;MAEA;;MAEA;MACAA;QACAC;QAEA;UACAD;UACA;UACAI;UACA;UACAC;YACAJ;UACA;QACA;UACAD;QACA;MACA;IACA;IAEA;IACAM;MACA;QACA;QACAN;MACA;QACA;QACAI;MACA;IACA;IAEA;IACAG;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpTA;AAAA;AAAA;AAAA;AAAykC,CAAgB,qjCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/bind.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/bind.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bind.vue?vue&type=template&id=26bd8c26&\"\nvar renderjs\nimport script from \"./bind.vue?vue&type=script&lang=js&\"\nexport * from \"./bind.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bind.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/bind.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bind.vue?vue&type=template&id=26bd8c26&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.apply_status !== \"none\" && _vm.agentInfo\n      ? _vm.getAgentLevelName(_vm.agentInfo.agent_level)\n      : null\n  var m1 =\n    _vm.apply_status !== \"none\" && _vm.agentInfo\n      ? _vm.formatTime(_vm.agentInfo.createtime)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bind.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bind.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<view class=\"header\">\r\n\t\t<image src=\"/static/img/agent-banner.png\" class=\"banner-img\"></image>\r\n\t\t<view class=\"header-content\">\r\n\t\t\t<text class=\"title\">申请成为代理商</text>\r\n\t\t\t<text class=\"subtitle\">享受专属代理权益，获得丰厚佣金回报</text>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<view class=\"content\">\r\n\t\t<!-- 申请表单 -->\r\n\t\t<view class=\"form-container\" v-if=\"apply_status === 'none'\">\r\n\t\t\t<view class=\"form-title\">\r\n\t\t\t\t<image src=\"/static/img/icon-form.png\" class=\"form-icon\"></image>\r\n\t\t\t\t<text>代理申请信息</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">代理商名称</text>\r\n\t\t\t\t<input class=\"input\" type=\"text\" v-model=\"formData.name\" placeholder=\"请输入代理商名称\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">联系电话</text>\r\n\t\t\t\t<input class=\"input\" type=\"text\" v-model=\"formData.tel\" placeholder=\"请输入联系电话\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">代理级别</text>\r\n\t\t\t\t<picker mode=\"selector\" :range=\"agentLevels\" range-key=\"name\" @change=\"onLevelChange\">\r\n\t\t\t\t\t<view class=\"picker-display\">\r\n\t\t\t\t\t\t<text>{{selectedLevel.name}}</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/arrow-down.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">备注说明</text>\r\n\t\t\t\t<textarea class=\"textarea\" v-model=\"formData.remark\" placeholder=\"请输入申请理由或备注信息\"></textarea>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"submit-btn\" @tap=\"submitApply\" :disabled=\"submitting\">\r\n\t\t\t\t<text v-if=\"submitting\">提交中...</text>\r\n\t\t\t\t<text v-else>提交申请</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 申请状态显示 -->\r\n\t\t<view class=\"status-container\" v-if=\"apply_status !== 'none'\">\r\n\t\t\t<view class=\"status-card\">\r\n\t\t\t\t<image :src=\"statusIcon\" class=\"status-icon\"></image>\r\n\t\t\t\t<text class=\"status-text\">{{statusText}}</text>\r\n\t\t\t\t<text class=\"status-desc\">{{statusDesc}}</text>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"agent-info\" v-if=\"agentInfo\">\r\n\t\t\t\t\t<text class=\"info-title\">申请信息</text>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">代理商名称：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{agentInfo.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">联系电话：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{agentInfo.tel}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">代理级别：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{getAgentLevelName(agentInfo.agent_level)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">申请时间：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{formatTime(agentInfo.createtime)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"back-btn\" @tap=\"goBack\" v-if=\"apply_status === 1\">\r\n\t\t\t\t\t进入代理中心\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 代理权益说明 -->\r\n\t\t<view class=\"benefits-container\">\r\n\t\t\t<view class=\"benefits-title\">\r\n\t\t\t\t<image src=\"/static/img/icon-benefits.png\" class=\"benefits-icon\"></image>\r\n\t\t\t\t<text>代理权益</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t<image src=\"/static/img/icon-commission.png\" class=\"benefit-icon\"></image>\r\n\t\t\t\t<view class=\"benefit-content\">\r\n\t\t\t\t\t<text class=\"benefit-title\">高额佣金</text>\r\n\t\t\t\t\t<text class=\"benefit-desc\">享受订单高额佣金分成，收益丰厚</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t<image src=\"/static/img/icon-area.png\" class=\"benefit-icon\"></image>\r\n\t\t\t\t<view class=\"benefit-content\">\r\n\t\t\t\t\t<text class=\"benefit-title\">区域独享</text>\r\n\t\t\t\t\t<text class=\"benefit-desc\">独享覆盖区域代理权，无竞争压力</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t<image src=\"/static/img/icon-support.png\" class=\"benefit-icon\"></image>\r\n\t\t\t\t<view class=\"benefit-content\">\r\n\t\t\t\t\t<text class=\"benefit-title\">全程支持</text>\r\n\t\t\t\t\t<text class=\"benefit-desc\">专业团队全程指导，助您成功</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tapply_status: 'none', // none: 未申请, 0: 待审核, 1: 已通过, 2: 已拒绝\r\n\t\t\tagentInfo: null,\r\n\t\t\tsubmitting: false,\r\n\t\t\tformData: {\r\n\t\t\t\tname: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tagent_level: 1,\r\n\t\t\t\tremark: ''\r\n\t\t\t},\r\n\t\t\tagentLevels: [\r\n\t\t\t\t{id: 1, name: '市级代理'},\r\n\t\t\t\t{id: 2, name: '省级代理'},\r\n\t\t\t\t{id: 3, name: '区县代理'},\r\n\t\t\t\t{id: 4, name: '街道代理'}\r\n\t\t\t],\r\n\t\t\tselectedLevel: {id: 1, name: '市级代理'}\r\n\t\t};\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\tstatusIcon() {\r\n\t\t\tconst icons = {\r\n\t\t\t\t0: '/static/img/status-pending.png',\r\n\t\t\t\t1: '/static/img/status-approved.png',\r\n\t\t\t\t2: '/static/img/status-rejected.png'\r\n\t\t\t};\r\n\t\t\treturn icons[this.apply_status] || '/static/img/status-pending.png';\r\n\t\t},\r\n\t\t\r\n\t\tstatusText() {\r\n\t\t\tconst texts = {\r\n\t\t\t\t0: '审核中',\r\n\t\t\t\t1: '申请通过',\r\n\t\t\t\t2: '申请被拒绝'\r\n\t\t\t};\r\n\t\t\treturn texts[this.apply_status] || '未知状态';\r\n\t\t},\r\n\t\t\r\n\t\tstatusDesc() {\r\n\t\t\tconst descs = {\r\n\t\t\t\t0: '您的代理申请正在审核中，请耐心等待...',\r\n\t\t\t\t1: '恭喜您！代理申请已通过，您可以开始享受代理权益',\r\n\t\t\t\t2: '很抱歉，您的代理申请被拒绝，请联系客服了解详情'\r\n\t\t\t};\r\n\t\t\treturn descs[this.apply_status] || '';\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tthis.checkApplyStatus();\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t// 安全获取颜色值\r\n\t\tgetColor: function(colorKey) {\r\n\t\t\ttry {\r\n\t\t\t\tif (typeof this.t === 'function') {\r\n\t\t\t\t\treturn this.t(colorKey);\r\n\t\t\t\t}\r\n\t\t\t\treturn null;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('获取颜色失败:', e);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 安全获取RGB颜色值\r\n\t\tgetColorRgb: function(colorKey) {\r\n\t\t\ttry {\r\n\t\t\t\tif (typeof this.t === 'function') {\r\n\t\t\t\t\treturn this.t(colorKey);\r\n\t\t\t\t}\r\n\t\t\t\treturn null;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('获取RGB颜色失败:', e);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 检查申请状态\r\n\t\tcheckApplyStatus() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 使用公开接口，通过手机号查询状态\r\n\t\t\tif (uni.getStorageSync('userTel')) {\r\n\t\t\t\tapp.get('ApiCityAgentPublic/getApplyStatusByTel', {tel: uni.getStorageSync('userTel')}, function(res) {\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tthat.apply_status = res.apply_status;\r\n\t\t\t\t\t\tthat.agentInfo = res.agent;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.apply_status = 'none';\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 如果没有存储的手机号，尝试获取登录用户的申请状态\r\n\t\t\t\tapp.get('ApiCityAgent/getApplyStatus', {}, function(res) {\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tthat.apply_status = res.apply_status;\r\n\t\t\t\t\t\tthat.agentInfo = res.agent;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.apply_status = 'none';\r\n\t\t\t\t\t}\r\n\t\t\t\t}, function(error) {\r\n\t\t\t\t\t// 接口调用失败，说明用户未登录或不是代理，显示申请表单\r\n\t\t\t\t\tthat.apply_status = 'none';\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 代理级别选择\r\n\t\tonLevelChange(e) {\r\n\t\t\tconst index = e.detail.value;\r\n\t\t\tthis.selectedLevel = this.agentLevels[index];\r\n\t\t\tthis.formData.agent_level = this.selectedLevel.id;\r\n\t\t},\r\n\t\t\r\n\t\t// 提交申请\r\n\t\tsubmitApply() {\r\n\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\t// 表单验证\r\n\t\t\tif (!this.formData.name) {\r\n\t\t\t\tapp.alert('请输入代理商名称');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.formData.tel) {\r\n\t\t\t\tapp.alert('请输入联系电话');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 手机号格式验证\r\n\t\t\tvar telReg = /^1[3-9]\\d{9}$/;\r\n\t\t\tif (!telReg.test(this.formData.tel)) {\r\n\t\t\t\tapp.alert('请输入正确的手机号');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.submitting = true;\r\n\t\t\t\r\n\t\t\t// 使用公开接口提交申请\r\n\t\t\tapp.post('ApiCityAgentPublic/applyAgent', this.formData, function(res) {\r\n\t\t\t\tthat.submitting = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t// 存储手机号用于后续查询状态\r\n\t\t\t\t\tuni.setStorageSync('userTel', that.formData.tel);\r\n\t\t\t\t\t// 刷新申请状态\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.checkApplyStatus();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 返回或进入代理中心\r\n\t\tgoBack() {\r\n\t\t\tif (this.apply_status === 1) {\r\n\t\t\t\t// 申请通过，进入代理中心\r\n\t\t\t\tapp.goto('/pagesExt/cityagent/index');\r\n\t\t\t} else {\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取代理级别名称\r\n\t\tgetAgentLevelName(level) {\r\n\t\t\tconst levels = {\r\n\t\t\t\t1: '市级代理',\r\n\t\t\t\t2: '省级代理',\r\n\t\t\t\t3: '区县代理',\r\n\t\t\t\t4: '街道代理'\r\n\t\t\t};\r\n\t\t\treturn levels[level] || '未知';\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化时间\r\n\t\tformatTime(timestamp) {\r\n\t\t\tconst date = new Date(timestamp * 1000);\r\n\t\t\treturn date.toLocaleString();\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tmin-height: 100vh;\r\n\tbackground: #f8f8f8;\r\n}\r\n\r\n.header {\r\n\tposition: relative;\r\n\theight: 300rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: white;\r\n}\r\n\r\n.banner-img {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\topacity: 0.3;\r\n}\r\n\r\n.header-content {\r\n\ttext-align: center;\r\n\tz-index: 1;\r\n}\r\n\r\n.title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.subtitle {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.9;\r\n}\r\n\r\n.content {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-container {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.form-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.form-title text {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.label {\r\n\tdisplay: block;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.input {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 0 20rpx;\r\n\tfont-size: 28rpx;\r\n\tbackground: #fafafa;\r\n}\r\n\r\n.textarea {\r\n\twidth: 100%;\r\n\tmin-height: 120rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 15rpx 20rpx;\r\n\tfont-size: 28rpx;\r\n\tbackground: #fafafa;\r\n}\r\n\r\n.picker-display {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\theight: 80rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 0 20rpx;\r\n\tbackground: #fafafa;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 24rpx;\r\n\theight: 24rpx;\r\n}\r\n\r\n.submit-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tborder-radius: 40rpx;\r\n\tborder: none;\r\n\tmargin-top: 30rpx;\r\n}\r\n\r\n.submit-btn[disabled] {\r\n\topacity: 0.6;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-container {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.status-card {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 40rpx;\r\n\ttext-align: center;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.status-icon {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.status-text {\r\n\tdisplay: block;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.status-desc {\r\n\tdisplay: block;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.agent-info {\r\n\tbackground: #f8f8f8;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 25rpx;\r\n\tmargin: 30rpx 0;\r\n\ttext-align: left;\r\n}\r\n\r\n.info-title {\r\n\tdisplay: block;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.info-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.info-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\twidth: 140rpx;\r\n}\r\n\r\n.info-value {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tflex: 1;\r\n}\r\n\r\n.back-btn {\r\n\twidth: 300rpx;\r\n\theight: 60rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\tfont-size: 28rpx;\r\n\tborder-radius: 30rpx;\r\n\tborder: none;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n/* 权益说明样式 */\r\n.benefits-container {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.benefits-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.benefits-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.benefits-title text {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.benefit-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 25rpx;\r\n}\r\n\r\n.benefit-icon {\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.benefit-content {\r\n\tflex: 1;\r\n}\r\n\r\n.benefit-title {\r\n\tdisplay: block;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.benefit-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.4;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bind.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bind.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102890\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}