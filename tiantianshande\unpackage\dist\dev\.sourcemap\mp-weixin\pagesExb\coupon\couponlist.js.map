{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?8330", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?f251", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?f3fe", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?4817", "uni-app:///pagesExb/coupon/couponlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?be93", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/coupon/couponlist.vue?a50e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nomore", "nodata", "datalist", "pagenum", "title", "needAuth", "authCanClose", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "st", "bid", "getcoupon", "id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgDnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAX;QAAAY;MAAA;QACAH;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;MAEA;QACAH;UAAAI;QAAA;UACA;YACAJ;UACA;YACAA;UACA;QACA;QACA;MACA;MACA;MACA;QACAA;UACAA;UACAA;YAAAI;UAAA;YACAJ;YACA;cACAA;YACA;cACAA;cACAX;cACAU;YACA;UACA;QACA;MACA;QACAC;QACAA;UAAAI;QAAA;UACAJ;UACA;YACAA;UACA;YACAA;YACAX;YACAU;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/coupon/couponlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/coupon/couponlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./couponlist.vue?vue&type=template&id=053ac577&\"\nvar renderjs\nimport script from \"./couponlist.vue?vue&type=script&lang=js&\"\nexport * from \"./couponlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./couponlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/coupon/couponlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./couponlist.vue?vue&type=template&id=053ac577&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m1 = item.type == 10 ? _vm.t(\"color1\") : null\n        var m2 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m3 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m4 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m5 = item.type == 5 ? _vm.t(\"color1\") : null\n        var m6 =\n          item.type == 1 || item.type == 10 || item.type == 4 || item.type == 5\n            ? _vm.t(\"color1\")\n            : null\n        var m7 = item.type == 1 ? _vm.t(\"color1rgb\") : null\n        var m8 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m9 = item.type == 2 ? _vm.t(\"color1rgb\") : null\n        var m10 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m11 = item.type == 3 ? _vm.t(\"color1rgb\") : null\n        var m12 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m13 = item.type == 4 ? _vm.t(\"color1rgb\") : null\n        var m14 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m15 = item.type == 5 ? _vm.t(\"color1rgb\") : null\n        var m16 = item.type == 5 ? _vm.t(\"color1\") : null\n        var m17 =\n          !(item.haveget >= item.perlimit) && !(item.stock <= 0)\n            ? _vm.t(\"color1\")\n            : null\n        var m18 =\n          !(item.haveget >= item.perlimit) && !(item.stock <= 0)\n            ? _vm.t(\"color1rgb\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n          m16: m16,\n          m17: m17,\n          m18: m18,\n        }\n      })\n    : null\n  var m19 = _vm.isload && _vm.nodata ? _vm.t(\"优惠券\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m19: m19,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./couponlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./couponlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"coupon-list\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"item.id\" class=\"coupon\" @tap.stop=\"goto\" :data-url=\"'coupondetail?id=' + item.id\">\r\n\t\t\t\t<view class=\"pt_left\">\r\n\t\t\t\t\t<view class=\"pt_left-content\">\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==1\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==10\"><text class=\"t1\">{{item.discount/10}}</text><text class=\"t2\">折</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==2\">礼品券</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==3\"><text class=\"t1\">{{item.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==4\">抵运费</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==5\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f2\" :style=\"{color:t('color1')}\" v-if=\"item.type==1 || item.type==10 || item.type==4 || item.type==5\">\r\n\t\t\t\t\t\t\t<text v-if=\"item.minprice>0\">满{{item.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"pt_right\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==1\">代金券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==2\">礼品券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==3\">计次券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==4\">运费抵扣券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==5\">餐饮券</text>\r\n\t\t\t\t\t\t<view class=\"t4\" v-if=\"item.bid>0\">适用商家：{{item.bname}}</view>\r\n                        <view class=\"t4\" v-if=\"item.house_status\">一户仅限一次</view>\r\n\t\t\t\t\t\t<view class=\"t3\" :style=\"item.bid>0?'margin-top:0':'margin-top:10rpx'\">有效期至 {{item.yxqdate}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"btn\" v-if=\"item.haveget>=item.perlimit\" style=\"background:#9d9d9d\">已领取</button>\r\n\t\t\t\t\t<button class=\"btn\" v-else-if=\"item.stock<=0\" style=\"background:#9d9d9d\">已抢光了</button>\r\n\t\t\t\t\t<button class=\"btn\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-else @tap.stop=\"getcoupon\" :data-id=\"item.id\" :data-price=\"item.price\" :data-score=\"item.score\" :data-key=\"index\">{{item.price > 0 ? '购买' : (item.score>0?'兑换':'领取')}}</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata\" :text=\"'暂无可领' + t('优惠券')\"></nodata>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tnomore:false,\r\n\t\t\tnodata:false,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      title: '列表',\r\n      needAuth: 0,\r\n      authCanClose: false\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n    this.pagenum = 1;\r\n    this.datalist = [];\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata();\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n      var bid = that.opt && (that.opt.bid || that.opt.bid === '0') ? that.opt.bid : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.nodata = false;\r\n      app.post('ApiCoupon/couponlist', {st: st,pagenum: pagenum,bid: bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n\t\t\t\tthat.loaded();\r\n      });\r\n    },\r\n    getcoupon: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar datalist = that.datalist;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar score = parseInt(e.currentTarget.dataset.score);\r\n\t\t\tvar price = e.currentTarget.dataset.price;\r\n\r\n\t\t\tif (price > 0) {\r\n\t\t\t\tapp.post('ApiCoupon/buycoupon', {id: id}, function (res) {\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tvar key = e.currentTarget.dataset.key;\r\n\t\t\tif (score > 0) {\r\n\t\t\t\tapp.confirm('确定要消耗' + score + '' + that.t('积分') + '兑换吗?', function () {\r\n\t\t\t\t\tapp.showLoading('兑换中');\r\n\t\t\t\t\tapp.post('ApiCoupon/getcoupon', {id: id}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\t\tdatalist[key]['haveget'] = data.haveget;\r\n\t\t\t\t\t\t\tthat.datalist = datalist;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tapp.showLoading('领取中');\r\n\t\t\t\tapp.post('ApiCoupon/getcoupon', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tdatalist[key]['haveget'] = data.haveget;\r\n\t\t\t\t\t\tthat.datalist = datalist;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.coupon-list{width:100%;padding:20rpx}\r\n.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden}\r\n.coupon .pt_left{background: #fff;min-height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}\r\n.coupon .pt_left .t0{padding-right:0;}\r\n.coupon .pt_left .t1{font-size:60rpx;}\r\n.coupon .pt_left .t2{padding-left:10rpx;}\r\n.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}\r\n.coupon .pt_right{background: #fff;width:70%;display:flex;min-height:200rpx;text-align: left;padding:20rpx 20rpx;position:relative}\r\n.coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}\r\n.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}\r\n.coupon .pt_right .f1 .t2{height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx}\r\n.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .btn{position:absolute;right:30rpx;top:40%;margin-top:-28rpx;border-radius:28rpx;width:160rpx;height:56rpx;line-height:56rpx;color:#fff}\r\n.coupon .pt_right .sygq{position:absolute;right:30rpx;top:50%;margin-top:-50rpx;width:100rpx;height:100rpx;}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./couponlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./couponlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024317\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}