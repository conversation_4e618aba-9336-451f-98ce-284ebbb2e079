{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?8695", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?5736", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?09ed", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?8073", "uni-app:///pagesExa/my/setinvite.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?aefe", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setinvite.vue?1aed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "invite_code", "inviteCodeType", "inviteCodePlaceholder", "parentInfo", "onLoad", "onPullDownRefresh", "methods", "getdata", "app", "that", "formSubmit", "tipText", "setTimeout", "inviteCodeInput", "queryParentInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;QACAC;QACAA;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;QAEAA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACAH;QACA;MACA;MACAA;MACAA;QAAAR;MAAA;QACAQ;QACA;UACAA;UACAI;YACAJ;UACA;QACA;UACAA;QACA;MACA;IACA;IACAK;MACA;;MAEA;MACA;MACA;QACAD;UACAH;QACA;MACA;QACAA;MACA;IACA;IACAK;MACA;MACAN;QAAAR;MAAA;QACA;UACAS;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/setinvite.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/setinvite.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setinvite.vue?vue&type=template&id=429f386f&\"\nvar renderjs\nimport script from \"./setinvite.vue?vue&type=script&lang=js&\"\nexport * from \"./setinvite.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setinvite.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/setinvite.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinvite.vue?vue&type=template&id=429f386f&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinvite.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinvite.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t<view class=\"form\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<input type=\"text\" class=\"input\" :placeholder=\"inviteCodePlaceholder\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" name=\"invite_code\" v-model=\"invite_code\" @input=\"inviteCodeInput\"></input>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-item\" v-if=\"parentInfo.nickname\" style=\"border:0;padding:20rpx 0;\">\r\n\t\t\t\t<view style=\"color:#666;font-size:28rpx;\">\r\n\t\t\t\t\t<view style=\"margin-bottom:10rpx;\">邀请人信息：</view>\r\n\t\t\t\t\t<view style=\"display:flex;align-items:center;\">\r\n\t\t\t\t\t\t<image :src=\"parentInfo.headimg\" style=\"width:60rpx;height:60rpx;border-radius:50%;margin-right:20rpx;\"></image>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:30rpx;color:#333;\">{{parentInfo.nickname}}</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:26rpx;color:#999;\" v-if=\"parentInfo.tel\">{{parentInfo.tel}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"set-btn\" form-type=\"submit\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确认绑定</button>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tinvite_code:'',\r\n\t\t\tinviteCodeType: 0, // 0=手机号, 1=邀请码, 2=用户ID\r\n\t\t\tinviteCodePlaceholder: '请输入邀请人手机号',\r\n\t\t\tparentInfo: {} // 邀请人信息\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthis.loading = true\r\n\t\t\tapp.get('ApiMy/setinvite', {}, function(res){\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.inviteCodeType = res.invite_code_type || 0;\r\n\t\t\t\tthat.invite_code = res.current_invite_code || '';\r\n\t\t\t\tthat.parentInfo = res.parent_info || {};\r\n\t\t\t\t\r\n\t\t\t\t// 设置placeholder\r\n\t\t\t\tif(that.inviteCodeType == 0) {\r\n\t\t\t\t\tthat.inviteCodePlaceholder = '请输入邀请人手机号';\r\n\t\t\t\t} else if(that.inviteCodeType == 1) {\r\n\t\t\t\t\tthat.inviteCodePlaceholder = '请输入邀请码';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.inviteCodePlaceholder = '请输入邀请人ID';\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded();\r\n\t\t\t})\r\n\t\t},\r\n    formSubmit: function (e) {\r\n      var formdata = e.detail.value;\r\n\t\t\tvar invite_code = formdata.invite_code;\r\n      if (invite_code == '') {\r\n\t\t\t\tvar tipText = '请输入邀请码';\r\n\t\t\t\tif(this.inviteCodeType == 0) {\r\n\t\t\t\t\ttipText = '请输入邀请人手机号';\r\n\t\t\t\t} else if(this.inviteCodeType == 2) {\r\n\t\t\t\t\ttipText = '请输入邀请人ID';\r\n\t\t\t\t}\r\n        app.alert(tipText); \r\n        return;\r\n      }\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post(\"ApiMy/setinvitesub\", {invite_code:invite_code}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            app.goback(true);\r\n          }, 1000);\r\n        } else {\r\n          app.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n    inviteCodeInput: function (e) {\r\n      this.invite_code = e.detail.value;\r\n\t\t\t\r\n\t\t\t// 实时查询邀请人信息（可选功能）\r\n\t\t\tvar that = this;\r\n\t\t\tif(e.detail.value && e.detail.value.length >= 3) {\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tthat.queryParentInfo(e.detail.value);\r\n\t\t\t\t}, 500);\r\n\t\t\t} else {\r\n\t\t\t\tthat.parentInfo = {};\r\n\t\t\t}\r\n    },\r\n\t\tqueryParentInfo: function(code) {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post(\"ApiMy/queryparent\", {invite_code: code}, function (data) {\r\n\t\t\t\tif (data.status == 1 && data.parent) {\r\n\t\t\t\t\tthat.parentInfo = data.parent;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.parentInfo = {};\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}\r\n.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;min-height:98rpx;line-height:98rpx;}\r\n.form-item:last-child{border:0}\r\n.form-item .label{color: #000;width:200rpx;}\r\n.form-item .input{flex:1;color: #000;}\r\n.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinvite.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setinvite.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115061035\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}