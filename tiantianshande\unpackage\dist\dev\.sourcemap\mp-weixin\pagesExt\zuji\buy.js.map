{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?2469", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?2a04", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?0f9a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?cfe2", "uni-app:///pagesExt/zuji/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?b659", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/zuji/buy.vue?4b09"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "editorFormdata", "test", "business", "productList", "freightList", "couponList", "couponrid", "coupontype", "address", "needaddress", "linkman", "tel", "freightkey", "freight_price", "pstimetext", "freight_time", "usescore", "totalprice", "product_price", "leveldk_money", "scoredk_money", "coupon_money", "storedata", "storeid", "storename", "latitude", "longitude", "onLoad", "onPullDownRefresh", "methods", "getProductData", "that", "app", "id", "ggselected", "console", "getdata", "prodata", "qsdata", "leadermoney", "toplanList", "inputLinkman", "inputTel", "<PERSON><PERSON><PERSON><PERSON>", "calculatePrice", "oldtotalprice", "scoredk", "changeFreight", "chooseCoupon", "choosePstime", "itemlist", "uni", "itemList", "success", "pstimeRadioChange", "hidePstimeDialog", "choosestore", "topay", "undefined", "formdata", "newformdata", "num", "qsnum", "start_date", "buytype", "teamid", "freightid", "addressid", "showCouponList", "handleClickMask", "openMendian", "openLocation", "name", "scale", "editorChooseImage", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "doStoreShowAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzIA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgP5wB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,uDACA,yDACA,gEACA,2DACA,oDACA,kDACA,qDACA,oDACA,uDACA,qDACA,kDACA,uDACA,qDACA,0DACA,mDACA,qDACA,mDACA,sDACA,wDACA,0DACA,kDACA;EAEA;EAEAC;IACA;IAEA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;UACA;UACA;YACAG;UACA;UACAH;UACAI;UACAJ;UACAA;UACAA;UACAA;UACA;YAAA;YACAA;UACA;UACAA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACAI;MACA;MACAL;MACAC;QACAC;QACA;QACAI;QACAC;;QAEA;MAEA;QAEAH;QACAJ;QACA;UACAC;YACAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;;QAEA;QAEA;UACAQ;QACA;QACAA;QACA;QACA;UACApB;UACAA;QACA;QACAY;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAC;YACA;YACA;YACA;cACA;gBACA;gBAEA;kBACA;oBACA,sDACAV;sBACA,iDACAA;sBACAA;oBACA;kBACA;kBACAA;oBACA;kBACA;kBACA;oBACA;sBACAA;oBACA;kBACA;kBACAlB;gBACA;cACA;YACA;YACA2B;UACA;QACA;MACA;IACA;IACAS;MACAR;IACA;IACAS;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAX;IACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAb;MACA;MACA;QACAlB;QACAQ;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;MACAJ;MACA;QACA;QACAA;QACAG;MACA;MACA;;MAEA,6FACAyB;QACAzB;QACAH;MACA;MACA;MACAJ;MACAI;MACAc;MACAA;IACA;IACA;IACAe;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAhB;IACA;IACAiB;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACA3B;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA4B;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;MACA;QACAlB;QACA;MACA;MACA;QACAD;QACAA;MACA;QACAoB;UACAC;UACAC;YACA;cACA;cACAtB;cACAA;YACA;UACA;QACA;MACA;IACA;IACAuB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACApD;MACA;IACA;IACA;IACAqD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA1B;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;MACA;MAEA;MACA;MAEA;QACAC;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;QACA,yFACA0B;UACA1B;UACA;QACA;QACA;UACA2B;QACA;QACAC;MACA;MAEA5B;MACA;MACAA;QACA;QACAM;QACAuB;QACAC;QACAC;QACAC;QACAC;QACA1C;QACAjB;QACA4D;QACAnD;QACAoD;QACAnD;QACAN;QACAC;QACAgD;QACAtB;MACA;QACAL;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACAoC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAnC;MACAH;IACA;IACAuC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACApB;QACA1B;QACAC;QACA8C;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA1C;QACAhC;QACAmC;QACAJ;QACAA;MACA;IACA;IACA4C;MACA;MACA;MACA;MACA;MACA;MACA3E;MACAmC;MACA;MACA;IACA;IACAyC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtuBA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/zuji/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/zuji/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=944d54b6&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/zuji/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=944d54b6&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.freightList, function (item, idx2) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.freightkey == idx2 ? _vm.t(\"color1\") : null\n        var m1 = _vm.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g0 =\n    _vm.isload &&\n    _vm.freightList[_vm.freightkey].minpriceset == 1 &&\n    _vm.freightList[_vm.freightkey].minprice > 0 &&\n    _vm.freightList[_vm.freightkey].minprice * 1 > _vm.product_price * 1\n      ? (_vm.freightList[_vm.freightkey].minprice - _vm.product_price).toFixed(\n          2\n        )\n      : null\n  var l1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].storedata,\n          function (item, idx) {\n            var $orig = _vm.__get_orig(item)\n            var m2 =\n              (idx < 5 || _vm.storeshowall == true) &&\n              _vm.freightList[_vm.freightkey].storekey == idx\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m2: m2,\n            }\n          }\n        )\n      : null\n  var g1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.storeshowall == false &&\n        _vm.freightList[_vm.freightkey].storedata.length > 5\n      : null\n  var m3 = _vm.isload ? _vm.t(\"会员\") : null\n  var m4 = _vm.isload ? _vm.t(\"优惠券\") : null\n  var g2 = _vm.isload ? _vm.couponList.length : null\n  var m5 = _vm.isload && g2 > 0 ? _vm.t(\"color1\") : null\n  var g3 =\n    _vm.isload && g2 > 0 && !(_vm.couponrid != 0) ? _vm.couponList.length : null\n  var m6 = _vm.isload && !(g2 > 0) ? _vm.t(\"优惠券\") : null\n  var m7 = _vm.isload ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m9 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l2 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m10 = _vm.freight_time == item.value ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m10: m10,\n            }\n          }\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        m3: m3,\n        m4: m4,\n        g2: g2,\n        m5: m5,\n        g3: g3,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx;\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx;\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pages/address/address?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/address.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.name\">\r\n\t\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}}<text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buydata\">\r\n\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/ico-shop.png\" />{{business.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" v-if=\"guige && guige.pic\" :src=\"guige && guige.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" v-else :src=\"product && product.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{guige && guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\">￥{{guige && guige.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"freight\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">配送方式</view>\r\n\t\t\t\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, idx2) in freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"freight-li\" :style=\"freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeFreight\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].minpriceset==1 && freightList[freightkey].minprice > 0 && freightList[freightkey].minprice*1 > product_price*1\">满{{freightList[freightkey].minprice}}元起送，还差{{(freightList[freightkey].minprice - product_price).toFixed(2)}}元</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"price\" v-if=\"freightList[freightkey].pstimeset==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\">{{pstimetext==''?'请选择时间':pstimetext}}<text\r\n\t\t\t\t\t\t\t\t\tclass=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"storeitem\" v-if=\"freightList[freightkey].pstype==1\">\r\n\t\t\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-freightkey=\"freightkey\" :data-storekey=\"freightList[freightkey].storekey\"><text class=\"iconfont icondingwei\"></text>{{freightList[freightkey].storedata[freightList[freightkey].storekey].name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-for=\"(item, idx) in freightList[freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"freightList[freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view v-if=\"storeshowall==false && (freightList[freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">配送</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">每期{{totalnum}}件*{{qsnum}}期</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" @tap=\"toplanList\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t<img class=\"body_detail\" :src=\"pre_url+'/static/img/week/date.png'\"/>\r\n\t\t\t\t\t\t\t\t\t{{start_date}} —— {{end_date}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view style=\"padding: 10px 0px;\">(第1/{{qsnum}}期)预计 {{start_date}} 送达</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t<img class=\"body_detail\" @tap=\"toplanList\" :src=\"pre_url+'/static/img/week/week_detail.png'\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">商品金额</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{product_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">开始日期</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">{{start_date}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">期数</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">{{qsnum}}期</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">-¥{{leveldk_money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\" v-if=\"freightList[freightkey].pstype==1\">服务费</text>\r\n\t\t\t\t\t\t\t<text class=\"f1\" v-else>运费</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">+¥{{freight_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"couponList.length>0\" class=\"f2\" @tap=\"showCouponList\">\r\n\t\t\t\t\t\t\t\t<text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\" :style=\"{background:t('color1')}\">{{couponrid!=0?couponList[couponkey].couponname:couponList.length+'张可用'}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in freightList[freightkey].formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" />\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\" />\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\" />{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\" />{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.val2[editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\" />\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"scoredk flex\" v-if=\"userinfo.score2money > 0\">\r\n\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\r\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t</view>\r\n\t\t\t</checkbox-group>\r\n\t\t</view> -->\r\n\t\t\t\t<view style=\"width: 100%;height:110rpx\"></view>\r\n\t\t\t\t<view class=\"footer flex notabbarbot\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponrid\" :bid=\"product.bid\" @chooseCoupon=\"chooseCoupon\"></couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in freightList[freightkey].pstimeArr\" :key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\teditorFormdata: [],\r\n\t\t\t\ttest: 'test',\r\n\r\n\t\t\t\tbusiness: {},\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tfreightList: [],\r\n\t\t\t\tcouponList: [],\r\n\t\t\t\tcouponrid: 0,\r\n\t\t\t\tcoupontype: 1,\r\n\t\t\t\taddress: [],\r\n\t\t\t\tneedaddress: 1,\r\n\t\t\t\tlinkman: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tfreightkey: 0,\r\n\t\t\t\tfreight_price: 0,\r\n\t\t\t\tpstimetext: '',\r\n\t\t\t\tfreight_time: '',\r\n\t\t\t\tusescore: 0,\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tproduct_price: 0,\r\n\t\t\t\tleveldk_money: 0,\r\n\t\t\t\tscoredk_money: 0,\r\n\t\t\t\tcoupon_money: 0,\r\n\t\t\t\tstoredata: [],\r\n\t\t\t\tstoreid: '',\r\n\t\t\t\tstorename: '',\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\tisload: 0,\r\n\t\t\t\tleadermoney: 0,\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tpstimeDialogShow: false,\r\n\t\t\t\tpstimeIndex: -1,\r\n\t\t\t\tproduct: \"\",\r\n\t\t\t\tguige: \"\",\r\n\t\t\t\tuserinfo: \"\",\r\n\t\t\t\tbuytype: \"\",\r\n\t\t\t\tscorebdkyf: \"\",\r\n\t\t\t\ttotalnum: \"\",\r\n\t\t\t\tqsnum: \"\",\r\n\t\t\t\tstart_date: \"\",\r\n\t\t\t\tend_date: \"\",\r\n\t\t\t\thavetongcheng: \"\",\r\n\t\t\t\tweight: \"\",\r\n\t\t\t\tgoodsnum: \"\",\r\n\t\t\t\tbeizhu: \"\",\r\n\t\t\t\tcouponkey: 0,\r\n\t\t\t\tstoreshowall: false,\r\n\t\t\t\tggselected:'',\r\n\t\t\t\tproid: null\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\t\r\n\t\t\tthis.ggselected = opt.guigeString\r\n\t\t\tthis.proid = opt.prodid\r\n\t\t\tthis.getProductData()\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetProductData:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t that.loading = true;\r\n\t\t\t\tapp.get('ApiCycle/product', {id:that.proid}, function (res) {\r\n\t\t\t\t\t that.loading = false;\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tthat.ps_cycle = res.product.ps_cycle;\r\n\t\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\t\tthat.rateList = res.product.everyday_item;\r\n\t\t\t\t\t\tthat.num  = res.product.min_num;\r\n\t\t\t\t\t\tthat.qsnum = res.product.min_qsnum;\r\n\t\t\t\t\t\tthat.min_num  = res.product.min_num;\r\n\t\t\t\t\t\tthat.min_qsnum  = res.product.min_qsnum;\r\n\t\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\t\tconsole.log(that.ks,'ks');\r\n\t\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\t\tthat.ggselected = ggselected;\r\n\t\t\t\t\t\tthat.pspl = that.rateList?that.rateList[0]:'';\r\n\t\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.totalprice = (that.nowguige.sell_price * that.num * that.qsnum).toFixed(2);\r\n\t\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this; //获取产品信息\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('Apizuji/buy', {\r\n\t\t\t\t\tid:this.prodid,\r\n\t\t\t\t\t// prodata: that.opt.prodata,\r\n\t\t\t\t\tprodata: '1,1,1',\r\n\t\t\t\t\tqsdata: '2024-07-31,1,1'\r\n\t\t\t\t\t\r\n\t\t\t\t\t// qsdata: that.opt.qsdata\r\n\t\t\t\t\t\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('res========', res)\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\tapp.goback()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar product = res.product;\r\n\t\t\t\t\tvar freightList = res.freightList;\r\n\t\t\t\t\tvar userinfo = res.userinfo;\r\n\t\t\t\t\tvar couponList = res.couponList;\r\n\t\t\t\t\tthat.product = product;\r\n\t\t\t\t\tthat.guige = res.guige;\r\n\t\t\t\t\tthat.business = res.business;\r\n\t\t\t\t\tthat.freightList = freightList;\r\n\t\t\t\t\tthat.userinfo = userinfo;\r\n\t\t\t\t\tthat.couponList = couponList;\r\n\t\t\t\t\tthat.buytype = res.buytype;\r\n\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\r\n\t\t\t\t\tthat.totalnum = res.totalnum;\r\n\t\t\t\t\tthat.qsnum = res.qsnum;\r\n\t\t\t\t\tthat.start_date = res.start_date;\r\n\t\t\t\t\tthat.end_date = res.end_date;\r\n\t\t\t\t\tthat.havetongcheng = res.havetongcheng;\r\n\t\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\t\tthat.pspl = res.pspl;\r\n\t\t\t\t\tvar leadermoney = 0; //商品总价 重量\r\n\r\n\t\t\t\t\tvar product_price = res.product_price;\r\n\r\n\t\t\t\t\tif (res.buytype == 2 && product.leadermoney * 1 > 0) {\r\n\t\t\t\t\t\tleadermoney = product.leadermoney * 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tleadermoney = leadermoney.toFixed(2); //会员折扣\r\n\t\t\t\t\tvar leveldk_money = 0;\r\n\t\t\t\t\tif (userinfo.discount > 0 && userinfo.discount < 10) {\r\n\t\t\t\t\t\tleveldk_money = (product_price - leadermoney) * (1 - userinfo.discount * 0.1);\r\n\t\t\t\t\t\tleveldk_money = leveldk_money.toFixed(2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.product_price = res.product_price;\r\n\t\t\t\t\tthat.leadermoney = leadermoney;\r\n\t\t\t\t\tthat.leveldk_money = leveldk_money;\r\n\t\t\t\t\tthat.scoredk_money = userinfo.scoredk_money;\r\n\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\t\tif (res.needLocation == 1) {\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t\t\tfor (var j in freightList) {\r\n\t\t\t\t\t\t\t\tif (freightList[j].pstype == 1) {\r\n\t\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\r\n\r\n\t\t\t\t\t\t\t\t\tif (storedata) {\r\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude &&\r\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].longitude) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].latitude, storedata[x].longitude);\r\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tstoredata.sort(function(a, b) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tfreightList[j].storedata = storedata;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.freightList = freightList;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoplanList() {\r\n\t\t\t\tapp.goto('/pagesExt/cycle/planList?ps_cycle=' + this.product.ps_cycle + \"&pspl=\" + this.pspl + \"&qsnum=\" + this.qsnum + \"&start_date=\" + this.start_date);\r\n\t\t\t},\r\n\t\t\tinputLinkman: function(e) {\r\n\t\t\t\tthis.linkman = e.detail.value\r\n\t\t\t},\r\n\t\t\tinputTel: function(e) {\r\n\t\t\t\tthis.tel = e.detail.value\r\n\t\t\t},\r\n\t\t\t//选择收货地址\r\n\t\t\tchooseAddress: function() {\r\n\t\t\t\tapp.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar product_price = parseFloat(that.product_price); //+商品总价\r\n\t\t\t\tvar leadermoney = parseFloat(that.leadermoney); //-团长优惠\r\n\t\t\t\tvar leveldk_money = parseFloat(that.leveldk_money); //-会员折扣\r\n\t\t\t\tvar coupon_money = parseFloat(that.coupon_money); //-优惠券抵扣 \r\n\t\t\t\tvar address = that.address; //算运费\r\n\t\t\t\tvar freightdata = that.freightList[that.freightkey];\r\n\t\t\t\tif (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\r\n\t\t\t\t\tvar needaddress = 1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar needaddress = 0;\r\n\t\t\t\t}\r\n\t\t\t\tthat.needaddress = needaddress;\r\n\t\t\t\tvar freight_price = freightdata.freight_price;\r\n\t\t\t\tif (that.coupontype == 4) {\r\n\t\t\t\t\tfreight_price = 0;\r\n\t\t\t\t\tcoupon_money = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar totalprice = product_price - leadermoney - leveldk_money - coupon_money;\r\n\t\t\t\tif (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\r\n\r\n\t\t\t\tvar oldtotalprice = totalprice;\r\n\t\t\t\tif (that.usescore) {\r\n\t\t\t\t\tvar scoredk_money = parseFloat(that.scoredk_money); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scoredk_money = 0;\r\n\t\t\t\t}\r\n\t\t\t\ttotalprice = totalprice + freight_price - scoredk_money;\r\n\t\t\t\tif (that.scorebdkyf == '1' && scoredk_money > 0 && totalprice < freight_price) {\r\n\t\t\t\t\t//积分不抵扣运费\r\n\t\t\t\t\ttotalprice = freight_price;\r\n\t\t\t\t\tscoredk_money = oldtotalprice - freight_price;\r\n\t\t\t\t}\r\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\r\n\t\t\t\tif (scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money >\r\n\t\t\t\t\toldtotalprice * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t\tscoredk_money = oldtotalprice * scoredkmaxpercent * 0.01;\r\n\t\t\t\t\ttotalprice = oldtotalprice - scoredk_money;\r\n\t\t\t\t}\r\n\t\t\t\tif (totalprice < 0) totalprice = 0;\r\n\t\t\t\tfreight_price = freight_price.toFixed(2);\r\n\t\t\t\ttotalprice = totalprice.toFixed(2);\r\n\t\t\t\tthat.totalprice = totalprice\r\n\t\t\t\tthat.freight_price = freight_price;\r\n\t\t\t},\r\n\t\t\t//积分抵扣\r\n\t\t\tscoredk: function(e) {\r\n\t\t\t\tvar usescore = e.detail.value[0];\r\n\t\t\t\tif (!usescore) usescore = 0;\r\n\t\t\t\tthis.usescore = usescore;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tchangeFreight: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tthis.freightkey = index;\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t},\r\n\t\t\tchooseCoupon: function(e) {\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t\tvar couponkey = e.key;\r\n\r\n\t\t\t\tif (couponrid == this.couponrid) {\r\n\t\t\t\t\tthis.couponkey = 0;\r\n\t\t\t\t\tthis.couponrid = 0;\r\n\t\t\t\t\tthis.coupontype = 1;\r\n\t\t\t\t\tthis.coupon_money = 0;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar couponList = this.couponList;\r\n\t\t\t\t\tvar coupon_money = couponList[couponkey]['money'];\r\n\t\t\t\t\tvar coupontype = couponList[couponkey]['type'];\r\n\t\t\t\t\tif (coupontype == 4) {\r\n\t\t\t\t\t\tcoupon_money = this.freightprice;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.couponkey = couponkey;\r\n\t\t\t\t\tthis.couponrid = couponrid;\r\n\t\t\t\t\tthis.coupontype = coupontype;\r\n\t\t\t\t\tthis.coupon_money = coupon_money;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tchoosePstime: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar freightkey = this.freightkey;\r\n\t\t\t\tvar freightList = this.freightList;\r\n\t\t\t\tvar freight = freightList[freightkey];\r\n\t\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\r\n\t\t\t\tvar itemlist = [];\r\n\r\n\t\t\t\tfor (var i = 0; i < pstimeArr.length; i++) {\r\n\t\t\t\t\titemlist.push(pstimeArr[i].title);\r\n\t\t\t\t}\r\n\t\t\t\tif (itemlist.length == 0) {\r\n\t\t\t\t\tapp.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (itemlist.length > 6) {\r\n\t\t\t\t\tthat.pstimeDialogShow = true;\r\n\t\t\t\t\tthat.pstimeIndex = -1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\titemList: itemlist,\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\t\tvar choosepstime = pstimeArr[res.tapIndex];\r\n\t\t\t\t\t\t\t\tthat.pstimetext = choosepstime.title;\r\n\t\t\t\t\t\t\t\tthat.freight_time = choosepstime.value;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpstimeRadioChange: function(e) {\r\n\t\t\t\tvar pstimeIndex = e.currentTarget.dataset.index;\r\n\t\t\t\tvar freightkey = this.freightkey;\r\n\t\t\t\tvar freightList = this.freightList;\r\n\t\t\t\tvar freight = freightList[freightkey];\r\n\t\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\r\n\t\t\t\tvar choosepstime = pstimeArr[pstimeIndex];\r\n\t\t\t\tthis.pstimetext = choosepstime.title;\r\n\t\t\t\tthis.freight_time = choosepstime.value;\r\n\t\t\t\tthis.pstimeDialogShow = false;\r\n\t\t\t},\r\n\t\t\thidePstimeDialog: function() {\r\n\t\t\t\tthis.pstimeDialogShow = false\r\n\t\t\t},\r\n\t\t\tchoosestore: function(e) {\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.index;\r\n\t\t\t\tvar freightkey = this.freightkey\r\n\t\t\t\tvar freightList = this.freightList\r\n\t\t\t\tfreightList[freightkey].storekey = storekey\r\n\t\t\t\tthis.freightList = freightList;\r\n\t\t\t},\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar buytype = this.buytype;\r\n\t\t\t\tvar freightkey = this.freightkey;\r\n\t\t\t\tvar freightid = this.freightList[freightkey].id;\r\n\t\t\t\tvar prodata = this.opt.prodata;\r\n\t\t\t\tvar addressid = this.address.id;\r\n\t\t\t\tvar linkman = this.linkman;\r\n\t\t\t\tvar tel = this.tel;\r\n\t\t\t\tvar usescore = this.usescore;\r\n\t\t\t\tvar couponkey = this.couponkey;\r\n\t\t\t\tvar couponrid = this.couponrid;\r\n\t\t\t\t\r\n\t\t\t\tthat.ks = this.ggselected \r\n\t\t\t\tvar ks = that.ks;\r\n\t\t\t\tvar proid = this.proid;\r\n\t\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\tif (this.freightList[freightkey].pstype == 1) {\r\n\t\t\t\t\tvar storekey = this.freightList[freightkey].storekey\r\n\t\t\t\t\tvar storeid = this.freightList[freightkey].storedata[storekey].id;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar storeid = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar freight_time = that.freight_time;\r\n\r\n\t\t\t\tvar needaddress = that.needaddress;\r\n\t\t\t\tif (needaddress == 0) addressid = 0;\r\n\r\n\t\t\t\tif (needaddress == 1 && addressid == undefined) {\r\n\t\t\t\t\tapp.error('请选择收货地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// if (this.freightList[freightkey].pstimeset == 1 && freight_time == '') {\r\n\t\t\t\t// \tapp.error('请选择' + (this.freightList[freightkey].pstype == 0 ? '配送' : '提货') + '时间');\r\n\t\t\t\t// \treturn;\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tvar formdataSet = this.freightList[freightkey].formdata;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\tvar newformdata = {};\r\n\t\t\t\tfor (var i = 0; i < formdataSet.length; i++) {\r\n\t\t\t\t\tif (formdataSet[i].val3 == 1 && (formdata['form' + i] === '' || formdata['form' + i] ===\r\n\t\t\t\t\t\t\tundefined || formdata['form' + i].length == 0)) {\r\n\t\t\t\t\t\tapp.alert(formdataSet[i].val1 + ' 必填');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formdataSet[i].key == 'selector') {\r\n\t\t\t\t\t\tformdata['form' + i] = formdataSet[i].val2[formdata['form' + i]]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewformdata['form' + i] = formdata['form' + i];\r\n\t\t\t\t}\r\n\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tvar prodata = proid + ',' + ggid + ',' +  that.num;\r\n\t\t\t\tapp.post('ApiCycle/createOrder', {\r\n\t\t\t\t\t// prodata: that.opt.prodata,\r\n\t\t\t\t\tqsdata: that.opt.qsdata,\r\n\t\t\t\t\tnum: that.opt.num,\r\n\t\t\t\t\tqsnum: that.qsnum,\r\n\t\t\t\t\tstart_date: that.start_date,\r\n\t\t\t\t\tbuytype: buytype,\r\n\t\t\t\t\tteamid: that.opt.teamid,\r\n\t\t\t\t\tstoreid: storeid,\r\n\t\t\t\t\tcouponrid: couponrid,\r\n\t\t\t\t\tfreightid: freightid,\r\n\t\t\t\t\tfreight_time: freight_time,\r\n\t\t\t\t\taddressid: addressid,\r\n\t\t\t\t\tusescore: usescore,\r\n\t\t\t\t\tlinkman: linkman,\r\n\t\t\t\t\ttel: tel,\r\n\t\t\t\t\tformdata: newformdata,\r\n\t\t\t\t\tprodata:prodata\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + data.payorderid);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowCouponList: function() {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t},\r\n\t\t\topenMendian: function(e) {\r\n\t\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\t\tvar frightinfo = this.freightList[freightkey]\r\n\t\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\t\tconsole.log(storeinfo)\r\n\t\t\t\tapp.goto('/shopPackage/shop/mendian?id=' + storeinfo.id);\r\n\t\t\t},\r\n\t\t\topenLocation: function(e) {\r\n\t\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\t\tvar frightinfo = this.freightList[freightkey]\r\n\t\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\t\tvar latitude = parseFloat(storeinfo.latitude);\r\n\t\t\t\tvar longitude = parseFloat(storeinfo.longitude);\r\n\t\t\t\tvar address = storeinfo.name;\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tname: address,\r\n\t\t\t\t\tscale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif (!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data) {\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\teditorBindPickerChange: function(e) {\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif (!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tthis.editorFormdata = editorFormdata\r\n\t\t\t\tthis.test = Math.random();\r\n\t\t\t},\r\n\t\t\tdoStoreShowAll: function() {\r\n\t\t\t\tthis.storeshowall = true;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.address-add {\r\n\t\twidth: 94%;\r\n\t\tmargin: 20rpx 3%;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 20rpx 3%;\r\n\t\tmin-height: 140rpx;\r\n\t}\r\n\r\n\t.address-add .f1 {\r\n\t\tmargin-right: 20rpx\r\n\t}\r\n\r\n\t.address-add .f1 .img {\r\n\t\twidth: 66rpx;\r\n\t\theight: 66rpx;\r\n\t}\r\n\r\n\t.address-add .f2 {\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.address-add .f3 {\r\n\t\twidth: 26rpx;\r\n\t\theight: 26rpx;\r\n\t}\r\n\r\n\t.linkitem {\r\n\t\twidth: 100%;\r\n\t\tpadding: 1px 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.linkitem .f1 {\r\n\t\twidth: 160rpx;\r\n\t\tcolor: #111111\r\n\t}\r\n\r\n\t.linkitem .input {\r\n\t\theight: 50rpx;\r\n\t\tpadding-left: 10rpx;\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.buydata {\r\n\t\twidth: 94%;\r\n\t\tmargin: 0 3%;\r\n\t\tbackground: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.btitle {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: #111111;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx\r\n\t}\r\n\r\n\t.btitle .img {\r\n\t\twidth: 34rpx;\r\n\t\theight: 34rpx;\r\n\t\tmargin-right: 10rpx\r\n\t}\r\n\r\n\t.bcontent {\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 20rpx\r\n\t}\r\n\r\n\t.product {\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px solid #f4f4f4\r\n\t}\r\n\r\n\t.product .item {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1px #ededed dashed;\r\n\t}\r\n\r\n\t.product .item:last-child {\r\n\t\tborder: none\r\n\t}\r\n\r\n\t.product .info {\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\r\n\t.product .info .f1 {\r\n\t\tcolor: #222222;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.product .info .f2 {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 24rpx\r\n\t}\r\n\r\n\t.product .info .f3 {\r\n\t\tcolor: #FF4C4C;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 10rpx\r\n\t}\r\n\r\n\t.product .img {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx\r\n\t}\r\n\r\n\t.collage_icon {\r\n\t\tcolor: #fe7203;\r\n\t\tborder: 1px solid #feccaa;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 20rpx;\r\n\t\tpadding: 0 6rpx;\r\n\t\tmargin-left: 6rpx\r\n\t}\r\n\r\n\t.freight {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.freight .f1 {\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx\r\n\t}\r\n\r\n\t.freight .f2 {\r\n\t\tcolor: #111111;\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.freight .f3 {\r\n\t\twidth: 24rpx;\r\n\t\theight: 28rpx;\r\n\t}\r\n\r\n\t.freighttips {\r\n\t\tcolor: red;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.freight-ul {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.freight-li {\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\tbackground: #F5F6F8;\r\n\t\tborder-radius: 24rpx;\r\n\t\tcolor: #6C737F;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\theight: 48rpx;\r\n\t\tline-height: 48rpx;\r\n\t\tpadding: 0 28rpx;\r\n\t\tmargin: 12rpx 10rpx 12rpx 0\r\n\t}\r\n\r\n\r\n\t.price {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.price .f1 {\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.price .f2 {\r\n\t\tcolor: #111;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.price .f3 {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t}\r\n\r\n\t.scoredk {\r\n\t\twidth: 94%;\r\n\t\tmargin: 0 3%;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 24rpx 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.scoredk .f1 {\r\n\t\tcolor: #333333\r\n\t}\r\n\r\n\t.scoredk .f2 {\r\n\t\tcolor: #999999;\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.remark {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.remark .f1 {\r\n\t\tcolor: #333;\r\n\t\twidth: 200rpx\r\n\t}\r\n\r\n\t.remark input {\r\n\t\tborder: 0px solid #eee;\r\n\t\theight: 70rpx;\r\n\t\tpadding-left: 10rpx;\r\n\t\ttext-align: right\r\n\t}\r\n\r\n\t.footer {\r\n\t\twidth: 96%;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 5px;\r\n\t\tposition: fixed;\r\n\t\tleft: 0px;\r\n\t\tbottom: 0px;\r\n\t\tpadding: 0 2%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tz-index: 8;\r\n\t\tbox-sizing: content-box\r\n\t}\r\n\r\n\t.footer .text1 {\r\n\t\theight: 110rpx;\r\n\t\tline-height: 110rpx;\r\n\t\tcolor: #2a2a2a;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.footer .text1 text {\r\n\t\tcolor: #e94745;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.footer .op {\r\n\t\twidth: 200rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-radius: 44rpx\r\n\t}\r\n\r\n\t.storeitem {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.storeitem .panel {\r\n\t\twidth: 100%;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.storeitem .panel .f1 {\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.storeitem .panel .f2 {\r\n\t\tcolor: #111;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.storeitem .radio-item {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tcolor: #000;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 0 solid #eee;\r\n\t\tpadding: 8rpx 20rpx;\r\n\t}\r\n\r\n\t.storeitem .radio-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.storeitem .radio-item .f1 {\r\n\t\tcolor: #666;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.storeitem .radio {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder: 2rpx solid #BFBFBF;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-left: 30rpx\r\n\t}\r\n\r\n\t.storeitem .radio .radio-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.pstime-item {\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\r\n\t.pstime-item .radio {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder: 2rpx solid #BFBFBF;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 30rpx\r\n\t}\r\n\r\n\t.pstime-item .radio .radio-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.cuxiao-desc {\r\n\t\twidth: 100%\r\n\t}\r\n\r\n\t.cuxiao-item {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 40rpx 20rpx 40rpx;\r\n\t}\r\n\r\n\t.cuxiao-item .type-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #49aa34;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.cuxiao-item .radio {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder: 2rpx solid #BFBFBF;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 30rpx\r\n\t}\r\n\r\n\t.cuxiao-item .radio .radio-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\r\n\t.form-item {\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 0;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.form-item .label {\r\n\t\tcolor: #333;\r\n\t\twidth: 200rpx;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.form-item .radio {\r\n\t\ttransform: scale(.7);\r\n\t}\r\n\r\n\t.form-item .checkbox {\r\n\t\ttransform: scale(.7);\r\n\t}\r\n\r\n\t.form-item .input {\r\n\t\tborder: 0px solid #eee;\r\n\t\theight: 70rpx;\r\n\t\tpadding-left: 10rpx;\r\n\t\ttext-align: right;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.form-item .textarea {\r\n\t\theight: 140rpx;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\tflex: 1;\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 2px;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.form-item .radio-group {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end\r\n\t}\r\n\r\n\t.form-item .radio {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.form-item .radio2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.form-item .radio .myradio {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 50%\r\n\t}\r\n\r\n\t.form-item .checkbox-group {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end\r\n\t}\r\n\r\n\t.form-item .checkbox {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.form-item .checkbox2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\r\n\t.form-item .checkbox .mycheckbox {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 2px\r\n\t}\r\n\r\n\t.form-item .picker {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tflex: 1;\r\n\t\ttext-align: right\r\n\t}\r\n\r\n\t.form-imgbox {\r\n\t\tmargin-right: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.form-imgbox-close {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tright: -16rpx;\r\n\t\ttop: -16rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.form-imgbox-close .image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.form-imgbox-img {\r\n\t\tdisplay: block;\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tpadding: 2px;\r\n\t\tborder: #d3d3d3 1px solid;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.form-imgbox-img>.image {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.form-imgbox-repeat {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tright: 2px;\r\n\t\tbottom: 2px;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 30rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.form-uploadbtn {\r\n\t\tposition: relative;\r\n\t\theight: 180rpx;\r\n\t\twidth: 180rpx\r\n\t}\r\n\r\n\t.storeviewmore {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tcolor: #889;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tmargin-top: 10rpx\r\n\t}\r\n\r\n\t.body_detail {\r\n\t\theight: 40rpx;\r\n\t\twidth: 40rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115096543\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}