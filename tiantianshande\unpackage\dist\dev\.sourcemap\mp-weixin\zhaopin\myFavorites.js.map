{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?c424", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?835f", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?0771", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?db4a", "uni-app:///zhaopin/myFavorites.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?2e53", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/myFavorites.vue?a2cc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "currentTab", "favoriteJobs", "isLoading", "isRefreshing", "noMoreData", "page", "pageSize", "pre_url", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "desc", "pic", "onShareTimeline", "imageUrl", "query", "onReachBottom", "methods", "switchTab", "loadFavoriteJobs", "params", "limit", "app", "uni", "id", "salary", "companyName", "companyLogo", "tags", "item", "collectTime", "icon", "onRefresh", "loadMore", "viewJobDetail", "url", "removeFromFavorites", "content", "success", "toggleFavorite", "position_id", "goToJobList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpFA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsIpxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACAH;MACAC;MACAC;IACA;IACA;IACA;MACAF;MACAI;MACAC;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAEAC;kBACAhB;kBACAiB;gBACA,GAEA;gBACA;kBACAD;gBACA;gBAEAE;kBACA;kBACA;kBACA;kBACAC;kBAEA;oBACA;sBAAA;wBACAC;wBACAd;wBACAe;wBACAC;wBACAC;wBACAC,OACAC,gBACAA,iBACAA,kBACA;wBACAC;sBACA;oBAAA;oBAEA;sBACA;oBACA;sBACA;oBACA;oBAEA;oBACA;oBACA;kBACA;oBACAP;sBACAb;sBACAqB;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAX;QACAY;MACA;IACA;IAEA;IACAC;MAAA;MACAb;QACAb;QACA2B;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAjB;QACAkB;MACA;QACA;UACA;UACA;YACA;cAAA;YAAA;UACA;UACAjB;YACAb;YACAqB;UACA;QACA;UACAR;YACAb;YACAqB;UACA;QACA;MACA;IACA;IAEA;IACAU;MACAlB;QACAY;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9TA;AAAA;AAAA;AAAA;AAAu6C,CAAgB,k3CAAG,EAAC,C;;;;;;;;;;;ACA37C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/myFavorites.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/myFavorites.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myFavorites.vue?vue&type=template&id=707ca897&\"\nvar renderjs\nimport script from \"./myFavorites.vue?vue&type=script&lang=js&\"\nexport * from \"./myFavorites.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myFavorites.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/myFavorites.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myFavorites.vue?vue&type=template&id=707ca897&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.currentTab === \"all\" ? _vm.t(\"color1rgb\") : null\n  var m1 = _vm.isload && _vm.currentTab === \"all\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.currentTab === \"all\" ? _vm.t(\"color1rgb\") : null\n  var m3 =\n    _vm.isload && _vm.currentTab === \"fulltime\" ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.isload && _vm.currentTab === \"fulltime\" ? _vm.t(\"color1\") : null\n  var m5 =\n    _vm.isload && _vm.currentTab === \"fulltime\" ? _vm.t(\"color1rgb\") : null\n  var m6 =\n    _vm.isload && _vm.currentTab === \"parttime\" ? _vm.t(\"color1rgb\") : null\n  var m7 = _vm.isload && _vm.currentTab === \"parttime\" ? _vm.t(\"color1\") : null\n  var m8 =\n    _vm.isload && _vm.currentTab === \"parttime\" ? _vm.t(\"color1rgb\") : null\n  var g0 = _vm.isload ? _vm.favoriteJobs.length : null\n  var m9 = _vm.isload && !(g0 > 0) ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && !(g0 > 0) ? _vm.t(\"color1\") : null\n  var m11 = _vm.isload && !(g0 > 0) ? _vm.t(\"color1rgb\") : null\n  var g1 = _vm.isload ? _vm.isLoading && _vm.favoriteJobs.length > 0 : null\n  var g2 = _vm.isload ? _vm.noMoreData && _vm.favoriteJobs.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        g0: g0,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myFavorites.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myFavorites.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <block v-if=\"isload\">\r\n      <view class=\"favorites-page\">\r\n        <!-- 顶部筛选栏 -->\r\n        <view class=\"filter-bar\">\r\n          <view \r\n            class=\"filter-item\"\r\n            :class=\"{ active: currentTab === 'all' }\"\r\n            :style=\"{\r\n              backgroundColor: currentTab === 'all' \r\n                ? `rgba(${t('color1rgb')}, 0.1)`\r\n                : '#f8f8f8',\r\n              color: currentTab === 'all' \r\n                ? t('color1')\r\n                : '#666',\r\n              boxShadow: currentTab === 'all' \r\n                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`\r\n                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'\r\n            }\"\r\n            @tap=\"switchTab('all')\"\r\n          >全部</view>\r\n          <view \r\n            class=\"filter-item\"\r\n            :class=\"{ active: currentTab === 'fulltime' }\"\r\n            :style=\"{\r\n              backgroundColor: currentTab === 'fulltime' \r\n                ? `rgba(${t('color1rgb')}, 0.1)`\r\n                : '#f8f8f8',\r\n              color: currentTab === 'fulltime' \r\n                ? t('color1')\r\n                : '#666',\r\n              boxShadow: currentTab === 'fulltime' \r\n                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`\r\n                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'\r\n            }\"\r\n            @tap=\"switchTab('fulltime')\"\r\n          >全职</view>\r\n          <view \r\n            class=\"filter-item\"\r\n            :class=\"{ active: currentTab === 'parttime' }\"\r\n            :style=\"{\r\n              backgroundColor: currentTab === 'parttime' \r\n                ? `rgba(${t('color1rgb')}, 0.1)`\r\n                : '#f8f8f8',\r\n              color: currentTab === 'parttime' \r\n                ? t('color1')\r\n                : '#666',\r\n              boxShadow: currentTab === 'parttime' \r\n                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`\r\n                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'\r\n            }\"\r\n            @tap=\"switchTab('parttime')\"\r\n          >兼职</view>\r\n        </view>\r\n\r\n        <!-- 职位列表 -->\r\n        <scroll-view \r\n          scroll-y \r\n          class=\"job-list\"\r\n          @scrolltolower=\"loadMore\"\r\n          refresher-enabled\r\n          :refresher-triggered=\"isRefreshing\"\r\n          @refresherrefresh=\"onRefresh\"\r\n        >\r\n          <!-- 列表内容 -->\r\n          <block v-if=\"favoriteJobs.length > 0\">\r\n            <view \r\n              v-for=\"(job, index) in favoriteJobs\" \r\n              :key=\"index\"\r\n              class=\"job-card\"\r\n              @tap=\"viewJobDetail(job.id)\"\r\n            >\r\n              <!-- 职位信息 -->\r\n              <view class=\"job-info\">\r\n                <view class=\"job-header\">\r\n                  <text class=\"job-title\">{{ job.title }}</text>\r\n                  <text class=\"job-salary\">{{ job.salary }}</text>\r\n                </view>\r\n                <view class=\"company-info\">\r\n                  <image class=\"company-logo\" :src=\"job.companyLogo\" mode=\"aspectFit\"></image>\r\n                  <text class=\"company-name\">{{ job.companyName }}</text>\r\n                  <text class=\"collect-time\">{{ job.collectTime }}</text>\r\n                </view>\r\n              </view>\r\n              \r\n              <!-- 底部标签和操作 -->\r\n              <view class=\"job-footer\">\r\n                <view class=\"job-tags\">\r\n                  <text class=\"tag\" v-for=\"(tag, tagIndex) in job.tags\" :key=\"tagIndex\">\r\n                    {{ tag }}\r\n                  </text>\r\n                </view>\r\n                <view class=\"action-buttons\">\r\n                  <button \r\n                    class=\"action-btn delete\"\r\n                    @tap.stop=\"removeFromFavorites(job.id)\"\r\n                  >取消收藏</button>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </block>\r\n\r\n          <!-- 空状态 -->\r\n          <view v-else class=\"empty-state\">\r\n            <image class=\"empty-icon\" src=\"/static/icons/empty-favorites.png\" mode=\"aspectFit\"></image>\r\n            <text class=\"empty-text\">暂无收藏的职位</text>\r\n            <button \r\n              class=\"browse-btn\" \r\n              @tap=\"goToJobList\"\r\n              :style=\"{\r\n                background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`,\r\n                boxShadow: `0 8rpx 20rpx rgba(${t('color1rgb')}, 0.25)`\r\n              }\"\r\n            >去浏览职位</button>\r\n          </view>\r\n\r\n          <!-- 加载状态 -->\r\n          <view v-if=\"isLoading && favoriteJobs.length > 0\" class=\"loading-more\">\r\n            正在加载更多...\r\n          </view>\r\n          <view v-if=\"noMoreData && favoriteJobs.length > 0\" class=\"no-more-data\">\r\n            没有更多数据了\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      currentTab: 'all',\r\n      favoriteJobs: [],\r\n      isLoading: false,\r\n      isRefreshing: false,\r\n      noMoreData: false,\r\n      page: 1,\r\n      pageSize: 10,\r\n      pre_url: app.globalData.pre_url\r\n    }\r\n  },\r\n\r\n  onLoad(opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.loadFavoriteJobs();\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.onRefresh();\r\n  },\r\n\r\n  onShareAppMessage() {\r\n    return this._sharewx({\r\n      title: '我的收藏',\r\n      desc: '查看收藏的职位',\r\n      pic: ''\r\n    });\r\n  },\r\n\r\n  onShareTimeline() {\r\n    var sharewxdata = this._sharewx({\r\n      title: '我的收藏',\r\n      desc: '查看收藏的职位',\r\n      pic: ''\r\n    });\r\n    var query = (sharewxdata.path).split('?')[1];\r\n    return {\r\n      title: sharewxdata.title,\r\n      imageUrl: sharewxdata.imageUrl,\r\n      query: query\r\n    }\r\n  },\r\n\r\n  onReachBottom() {\r\n    if (!this.noMoreData && !this.isLoading) {\r\n      this.loadMore();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 切换标签\r\n    switchTab(tab) {\r\n      if (this.currentTab === tab) return;\r\n      this.currentTab = tab;\r\n      this.page = 1;\r\n      this.noMoreData = false;\r\n      this.favoriteJobs = [];\r\n      this.loadFavoriteJobs();\r\n    },\r\n\r\n    // 加载收藏的职位\r\n    async loadFavoriteJobs() {\r\n      if (this.isLoading || this.noMoreData) return;\r\n      this.isLoading = true;\r\n      this.loading = true;\r\n\r\n      const params = {\r\n        page: this.page,\r\n        limit: this.pageSize\r\n      };\r\n      \r\n      // 根据当前标签筛选类型\r\n      if (this.currentTab !== 'all') {\r\n        params.type = this.currentTab === 'fulltime' ? 1 : 2;\r\n      }\r\n\r\n      app.get('/apiZhaopin/getFavoriteList', params, (res) => {\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.isRefreshing = false;\r\n        uni.stopPullDownRefresh();\r\n\r\n        if (res.status === 1) {\r\n          const jobList = res.data.list.map(item => ({\r\n            id: item.id,\r\n            title: item.title,\r\n            salary: item.salary,\r\n            companyName: item.company_name,\r\n            companyLogo: item.company_logo,\r\n            tags: [\r\n              item.education,\r\n              item.experience,\r\n              item.work_address\r\n            ].filter(Boolean),\r\n            collectTime: item.create_time\r\n          }));\r\n\r\n          if (this.page === 1) {\r\n            this.favoriteJobs = jobList;\r\n          } else {\r\n            this.favoriteJobs = [...this.favoriteJobs, ...jobList];\r\n          }\r\n\r\n          this.noMoreData = jobList.length < this.pageSize || this.favoriteJobs.length >= res.data.total;\r\n          this.page++;\r\n          this.isload = true;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '加载失败，请重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 下拉刷新\r\n    async onRefresh() {\r\n      this.isRefreshing = true;\r\n      this.page = 1;\r\n      this.noMoreData = false;\r\n      await this.loadFavoriteJobs();\r\n    },\r\n\r\n    // 加载更多\r\n    loadMore() {\r\n      this.loadFavoriteJobs();\r\n    },\r\n\r\n    // 查看职位详情\r\n    viewJobDetail(jobId) {\r\n      uni.navigateTo({\r\n        url: `/zhaopin/partdetails?id=${jobId}`\r\n      });\r\n    },\r\n\r\n    // 移除收藏\r\n    removeFromFavorites(jobId) {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要取消收藏该职位吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.toggleFavorite(jobId);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 切换收藏状态\r\n    toggleFavorite(jobId) {\r\n      app.post('/apiZhaopin/favoritePosition', {\r\n        position_id: jobId\r\n      }, (res) => {\r\n        if (res.status === 1) {\r\n          // 如果是取消收藏，从列表中移除\r\n          if (res.data.is_favorite === 0) {\r\n            this.favoriteJobs = this.favoriteJobs.filter(job => job.id !== jobId);\r\n          }\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'success'\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '操作失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 跳转到职位列表\r\n    goToJobList() {\r\n      uni.navigateTo({\r\n        url: '/zhaopin/index'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.favorites-page {\r\n  min-height: 100vh;\r\n  background-color: #f7f8fa;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  \r\n  .filter-bar {\r\n    display: flex;\r\n    background-color: rgba(255, 255, 255, 0.98);\r\n    padding: 24rpx 30rpx;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 100;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n    backdrop-filter: blur(10px);\r\n    \r\n    .filter-item {\r\n      padding: 16rpx 40rpx;\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      background-color: #f8f8f8;\r\n      border-radius: 36rpx;\r\n      margin-right: 24rpx;\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      position: relative;\r\n      overflow: hidden;\r\n      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);\r\n      \r\n      &.active {\r\n        background-color: v-bind('`rgba(${t(\"color1rgb\")}, 0.1)`');\r\n        color: v-bind('t(\"color1\")');\r\n        font-weight: 500;\r\n        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t(\"color1rgb\")}, 0.15)`');\r\n      }\r\n    }\r\n  }\r\n  \r\n  .job-list {\r\n    height: calc(100vh - 88rpx);\r\n    padding: 16rpx;\r\n    \r\n    .job-card {\r\n      margin: 20rpx 12rpx;\r\n      padding: 28rpx;\r\n      background-color: #fff;\r\n      border-radius: 16rpx;\r\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      border: 1rpx solid rgba(0, 0, 0, 0.02);\r\n      \r\n      &:active {\r\n        transform: scale(0.985);\r\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);\r\n      }\r\n      \r\n      .job-info {\r\n        .job-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 20rpx;\r\n          \r\n          .job-title {\r\n            font-size: 32rpx;\r\n            font-weight: 600;\r\n            color: #2c3e50;\r\n            line-height: 1.4;\r\n          }\r\n          \r\n          .job-salary {\r\n            font-size: 30rpx;\r\n            font-weight: 600;\r\n            color: #ff4d4f;\r\n            background: rgba(255, 77, 79, 0.08);\r\n            padding: 4rpx 16rpx;\r\n            border-radius: 6rpx;\r\n          }\r\n        }\r\n        \r\n        .company-info {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 24rpx;\r\n          \r\n          .company-logo {\r\n            width: 56rpx;\r\n            height: 56rpx;\r\n            border-radius: 12rpx;\r\n            margin-right: 16rpx;\r\n            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n            border: 1rpx solid rgba(0, 0, 0, 0.04);\r\n          }\r\n          \r\n          .company-name {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            margin-right: 16rpx;\r\n          }\r\n          \r\n          .collect-time {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n            background: #f8f9fa;\r\n            padding: 2rpx 12rpx;\r\n            border-radius: 4rpx;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .job-footer {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-top: 20rpx;\r\n        \r\n        .job-tags {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          gap: 12rpx;\r\n          margin-right: 16rpx;\r\n          \r\n          .tag {\r\n            padding: 4rpx 16rpx;\r\n            background-color: #f8f9fa;\r\n            border-radius: 4rpx;\r\n            font-size: 24rpx;\r\n            color: #666;\r\n            border: 1rpx solid #eee;\r\n          }\r\n        }\r\n        \r\n        .action-buttons {\r\n          .action-btn {\r\n            padding: 6rpx 16rpx;\r\n            font-size: 24rpx;\r\n            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n            border: none;\r\n            position: relative;\r\n            overflow: visible;\r\n            font-weight: 400;\r\n            min-width: 80rpx;\r\n            text-align: center;\r\n            height: 32rpx;\r\n            line-height: 32rpx;\r\n            \r\n            &::before {\r\n              content: '';\r\n              position: absolute;\r\n              top: 0;\r\n              right: -16rpx;\r\n              width: 32rpx;\r\n              height: 100%;\r\n              background: inherit;\r\n              transform: skewX(-30deg);\r\n              transform-origin: left bottom;\r\n              z-index: -1;\r\n            }\r\n            \r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              bottom: -4rpx;\r\n              right: -4rpx;\r\n              width: 8rpx;\r\n              height: 8rpx;\r\n              background: rgba(0, 0, 0, 0.1);\r\n              transform: rotate(45deg);\r\n              z-index: -2;\r\n            }\r\n            \r\n            &.delete {\r\n              background: #f5f5f5;\r\n              color: #999;\r\n              \r\n              &:active {\r\n                transform: translateY(1rpx);\r\n                color: #666;\r\n                background: #f0f0f0;\r\n              }\r\n              \r\n              &::before {\r\n                box-shadow: 1rpx 0 3rpx rgba(0, 0, 0, 0.03);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding-top: 240rpx;\r\n    \r\n    .empty-icon {\r\n      width: 240rpx;\r\n      height: 240rpx;\r\n      margin-bottom: 40rpx;\r\n      opacity: 0.8;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n      margin-bottom: 48rpx;\r\n    }\r\n    \r\n    .browse-btn {\r\n      padding: 24rpx 64rpx;\r\n      background: v-bind('`linear-gradient(135deg, ${t(\"color1\")}, ${t(\"color1\")}dd)`');\r\n      color: #fff;\r\n      font-size: 28rpx;\r\n      border-radius: 44rpx;\r\n      box-shadow: v-bind('`0 8rpx 20rpx rgba(${t(\"color1rgb\")}, 0.25)`');\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      border: none;\r\n      font-weight: 500;\r\n      letter-spacing: 2rpx;\r\n      \r\n      &:active {\r\n        transform: translateY(2rpx) scale(0.98);\r\n        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t(\"color1rgb\")}, 0.2)`');\r\n        background: v-bind('`linear-gradient(135deg, ${t(\"color1\")}, ${t(\"color1\")}dd)`');\r\n      }\r\n    }\r\n  }\r\n  \r\n  .loading-more,\r\n  .no-more-data {\r\n    text-align: center;\r\n    padding: 30rpx 0;\r\n    font-size: 26rpx;\r\n    color: #999;\r\n    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.02));\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myFavorites.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myFavorites.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115048806\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}