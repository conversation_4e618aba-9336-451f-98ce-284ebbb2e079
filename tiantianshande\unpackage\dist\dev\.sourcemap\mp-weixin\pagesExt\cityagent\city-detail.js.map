{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?f5a9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?dabd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?0107", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?ed10", "uni-app:///pagesExt/cityagent/city-detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?1755", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/city-detail.vue?6313"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "city_id", "loading", "cityInfo", "statistics", "districts", "monthlyTrend", "onLoad", "methods", "getColor", "console", "loadCityDetail", "that", "app", "uni", "title", "viewDistrictDetail", "viewOrders", "viewIncome", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsHpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MAEAC;QAAAZ;MAAA;QACAW;QAEA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACAE;YACAC;UACA;QACA;UACAF;QACA;MACA;IACA;IAEA;IACAG;MACAH;IACA;IAEA;IACAI;MACAJ;IACA;IAEA;IACAK;MACAL;IACA;IAEA;IACAM;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/city-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/city-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./city-detail.vue?vue&type=template&id=3167b31a&\"\nvar renderjs\nimport script from \"./city-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./city-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city-detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/city-detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-detail.vue?vue&type=template&id=3167b31a&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatTime(_vm.cityInfo.start_time)\n  var g0 = _vm.districts.length\n  var m1 = _vm.getColor(\"color1\") || \"#4CAF50\"\n  var m2 = _vm.getColor(\"color1\") || \"#4CAF50\"\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<view class=\"header\">\r\n\t\t<text class=\"title\">{{cityInfo.name || '城市详情'}}</text>\r\n\t\t<text class=\"subtitle\">查看城市详细数据</text>\r\n\t</view>\r\n\t\r\n\t<view class=\"content\">\r\n\t\t<!-- 城市基本信息 -->\r\n\t\t<view class=\"info-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-city.png\" class=\"card-icon\"></image>\r\n\t\t\t\t<text class=\"card-title\">基本信息</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-list\">\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">城市名称：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{cityInfo.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">行政代码：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{cityInfo.code}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">下辖区县：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{cityInfo.district_count}}个</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">代理开始：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{formatTime(cityInfo.start_time)}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 业绩统计 -->\r\n\t\t<view class=\"stats-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-stats.png\" class=\"card-icon\"></image>\r\n\t\t\t\t<text class=\"card-title\">业绩统计</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-grid\">\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{statistics.total_orders}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">总订单数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">￥{{statistics.total_amount}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">总业绩</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">￥{{statistics.total_commission}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">总佣金</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{statistics.month_orders}}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">本月订单</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 区县列表 -->\r\n\t\t<view class=\"district-card\" v-if=\"districts.length > 0\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-district.png\" class=\"card-icon\"></image>\r\n\t\t\t\t<text class=\"card-title\">下辖区县</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"district-list\">\r\n\t\t\t\t<view class=\"district-item\" \r\n\t\t\t\t\tv-for=\"district in districts\" \r\n\t\t\t\t\t:key=\"district.id\"\r\n\t\t\t\t\t@tap=\"viewDistrictDetail(district)\">\r\n\t\t\t\t\t<view class=\"district-info\">\r\n\t\t\t\t\t\t<text class=\"district-name\">{{district.name}}</text>\r\n\t\t\t\t\t\t<text class=\"district-stats\">{{district.orders}}笔订单 | ￥{{district.revenue}}收益</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/img/arrow-right.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 月度趋势 -->\r\n\t\t<view class=\"trend-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<image src=\"/static/img/icon-trend.png\" class=\"card-icon\"></image>\r\n\t\t\t\t<text class=\"card-title\">月度趋势</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"trend-chart\">\r\n\t\t\t\t<view class=\"chart-item\" v-for=\"item in monthlyTrend\" :key=\"item.month\">\r\n\t\t\t\t\t<view class=\"chart-bar\">\r\n\t\t\t\t\t\t<view class=\"bar-fill\" :style=\"{height: item.percentage + '%', background: getColor('color1') || '#4CAF50'}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"chart-label\">{{item.month}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"trend-legend\">\r\n\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t<view class=\"legend-color\" :style=\"{background: getColor('color1') || '#4CAF50'}\"></view>\r\n\t\t\t\t\t<text class=\"legend-text\">订单量</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"action-container\">\r\n\t\t\t<button class=\"action-btn\" @tap=\"viewOrders\">查看订单</button>\r\n\t\t\t<button class=\"action-btn\" @tap=\"viewIncome\">查看收益</button>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 加载状态 -->\r\n\t<loading v-if=\"loading\"></loading>\r\n\t\r\n\t<!-- 消息提示 -->\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcity_id: 0,\r\n\t\t\tloading: false,\r\n\t\t\tcityInfo: {},\r\n\t\t\tstatistics: {},\r\n\t\t\tdistricts: [],\r\n\t\t\tmonthlyTrend: []\r\n\t\t};\r\n\t},\r\n\t\r\n\tonLoad(options) {\r\n\t\tthis.city_id = options.city_id || 0;\r\n\t\tif (this.city_id) {\r\n\t\t\tthis.loadCityDetail();\r\n\t\t}\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t// 安全获取颜色值\r\n\t\tgetColor: function(colorKey) {\r\n\t\t\ttry {\r\n\t\t\t\tif (typeof this.t === 'function') {\r\n\t\t\t\t\treturn this.t(colorKey);\r\n\t\t\t\t}\r\n\t\t\t\treturn null;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('获取颜色失败:', e);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 加载城市详情\r\n\t\tloadCityDetail() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.get('ApiCityAgent/getCityDetail', {city_id: this.city_id}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\tthat.cityInfo = res.cityInfo;\r\n\t\t\t\t\tthat.statistics = res.statistics;\r\n\t\t\t\t\tthat.districts = res.districts;\r\n\t\t\t\t\tthat.monthlyTrend = res.monthlyTrend;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 设置导航标题\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.cityInfo.name + ' - 详情'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查看区县详情\r\n\t\tviewDistrictDetail(district) {\r\n\t\t\tapp.goto('/pagesExt/cityagent/district-detail?district_id=' + district.id);\r\n\t\t},\r\n\t\t\r\n\t\t// 查看订单\r\n\t\tviewOrders() {\r\n\t\t\tapp.goto('/pagesExt/cityagent/orders?city_id=' + this.city_id);\r\n\t\t},\r\n\t\t\r\n\t\t// 查看收益\r\n\t\tviewIncome() {\r\n\t\t\tapp.goto('/pagesExt/cityagent/income?city_id=' + this.city_id);\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化时间\r\n\t\tformatTime(timestamp) {\r\n\t\t\tif (!timestamp) return '暂无';\r\n\t\t\tconst date = new Date(timestamp * 1000);\r\n\t\t\treturn date.toLocaleDateString();\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tpadding: 40rpx 30rpx;\r\n\tcolor: white;\r\n\ttext-align: center;\r\n}\r\n\r\n.title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.subtitle {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.9;\r\n}\r\n\r\n.content {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n/* 卡片通用样式 */\r\n.info-card, .stats-card, .district-card, .trend-card {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.card-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.card-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 信息列表样式 */\r\n.info-list {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.info-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.info-item:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\twidth: 160rpx;\r\n}\r\n\r\n.info-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tflex: 1;\r\n}\r\n\r\n/* 统计样式 */\r\n.stats-grid {\r\n\tdisplay: flex;\r\n\tpadding: 30rpx;\r\n\tflex-wrap: wrap;\r\n}\r\n\r\n.stats-item {\r\n\twidth: 50%;\r\n\ttext-align: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.stats-number {\r\n\tdisplay: block;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4CAF50;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.stats-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 区县列表样式 */\r\n.district-list {\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.district-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 25rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.district-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.district-info {\r\n\tflex: 1;\r\n}\r\n\r\n.district-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.district-stats {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 24rpx;\r\n\theight: 24rpx;\r\n}\r\n\r\n/* 趋势图样式 */\r\n.trend-chart {\r\n\tdisplay: flex;\r\n\talign-items: flex-end;\r\n\tpadding: 30rpx;\r\n\theight: 200rpx;\r\n\tjustify-content: space-around;\r\n}\r\n\r\n.chart-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.chart-bar {\r\n\twidth: 20rpx;\r\n\theight: 120rpx;\r\n\tbackground: #f0f0f0;\r\n\tborder-radius: 10rpx;\r\n\tposition: relative;\r\n\tmargin-bottom: 10rpx;\r\n\tdisplay: flex;\r\n\talign-items: flex-end;\r\n}\r\n\r\n.bar-fill {\r\n\twidth: 100%;\r\n\tborder-radius: 10rpx;\r\n\ttransition: height 0.3s ease;\r\n}\r\n\r\n.chart-label {\r\n\tfont-size: 20rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.trend-legend {\r\n\tpadding: 0 30rpx 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.legend-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.legend-color {\r\n\twidth: 20rpx;\r\n\theight: 20rpx;\r\n\tborder-radius: 50%;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.legend-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-container {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\tfont-size: 28rpx;\r\n\tborder-radius: 40rpx;\r\n\tborder: none;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102943\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}