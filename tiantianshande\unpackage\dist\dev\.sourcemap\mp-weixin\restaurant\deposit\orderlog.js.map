{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?dbc0", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?0961", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?a125", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?1605", "uni-app:///restaurant/deposit/orderlog.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?736c", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderlog.vue?c6a1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pagenum", "nomore", "nodata", "boxShow", "num", "onLoad", "app", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "id", "handleClickMask", "takeout", "formSubmit", "bid", "orderid", "numbers", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgFjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAJ;MACAK;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;MACAC;MAAA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC,yCAEA;EACAC;IACAC;MACA;MACAC;MACAA;MACAL;QAAAM;MAAA;QACAD;QACAA;QACAA;MACA;IACA;IAEAE;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;;MAEAT;QAAAU;QAAAC;QAAAC;MAAA;QACA;UACAZ;UAAA;QACA;QACAA;QACAa;UACAR;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/deposit/orderlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/deposit/orderlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderlog.vue?vue&type=template&id=f96d8d0a&\"\nvar renderjs\nimport script from \"./orderlog.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/deposit/orderlog.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlog.vue?vue&type=template&id=f96d8d0a&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.dateFormat(_vm.data.createtime) : null\n  var m1 = _vm.isload && _vm.data.status == 1 ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.data.log.length : null\n  var l0 =\n    _vm.isload && g0 > 0\n      ? _vm.__map(_vm.data.log, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.dateFormat(item.createtime)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var m3 = _vm.isload && _vm.boxShow ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlog.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"order-content\">\r\n\t\t\t\t<view class=\"order-box\">\r\n\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap.stop=\"goto\" :data-url=\"data.bid!=0?'/pagesExt/business/index?bid=' + data.bid:'/pages/index/index'\">\r\n\t\t\t\t\t\t\t<image :src=\"data.binfo.logo\"></image>\r\n\t\t\t\t\t\t\t<text>{{data.binfo.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content\" :style=\"idx+1==data.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t\t<image :src=\"data.pic\" class=\"img\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{data.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t2\">数量：{{data.num}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t2\">存入时间：{{dateFormat(data.createtime)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"data.status==0\" class=\"takeout st0\" :data-orderid=\"data.id\">审核中</view>\r\n\t\t\t\t\t\t<view v-if=\"data.status==1\" class=\"takeout\" >寄存中</view>\r\n\t\t\t\t\t\t<view v-if=\"data.status==2\" class=\"takeout st2\" :data-orderid=\"data.id\">已取走</view>\r\n\t\t\t\t\t\t<view v-if=\"data.status==3\" class=\"takeout st3\" :data-orderid=\"data.id\">未通过</view>\r\n\t\t\t\t\t\t<view v-if=\"data.status==4\" class=\"takeout st4\" :data-orderid=\"data.id\">已过期</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>备注：{{data.message}}</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<view v-if=\"data.status==1\" @tap.stop=\"takeout\" :data-bid=\"data.bid\" data-orderid=\"0\" class=\"btn1\" :data-num=\"data.num\" :style=\"{background:t('color1')}\">取出</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"expressinfo\" v-if=\"data.log.length >0\">\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in data.log\" :key=\"index\" :class=\"'item ' + (index==0?'on':'')\">\r\n\t\t\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/dot' + (index==0?'2':'1') + '.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t<text class=\"t2\">{{dateFormat(item.createtime)}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.remark}}{{item.num}}件</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t\r\n\t\t<!-- 弹框 -->\r\n\t\t<view v-if=\"boxShow\" class=\"\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请输入取出数量</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content takeoutBox\">\r\n\t\t\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">取出数量</text>\r\n\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"请输入要取出的数量\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"numbers\" :value=\"num\"></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"btn\" form-type=\"submit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      data: {},\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n\t\t\tboxShow:false,\r\n\t\t\tnum:1,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.st){\r\n\t\t\tthis.st = this.opt.st;\r\n\t\t}\r\n\t\tif(!this.opt.id){\r\n\t\t\tapp.alert('缺少参数');return;\r\n\t\t}\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    \r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiRestaurantDeposit/orderlog', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        that.data = res.data;\r\n        that.loaded();\r\n      });\r\n    },\r\n\t\t\r\n\t\thandleClickMask: function() {\t\r\n\t\t\tthis.boxShow = !this.boxShow;\r\n\t\t},\r\n    takeout: function (e) {\r\n       var that = this;\r\n       this.orderid = e.currentTarget.dataset.orderid;\r\n    \t this.boxShow = true; //显示弹框\r\n    \t this.num = e.currentTarget.dataset.num;\r\n    },\r\n\t\tformSubmit: function (e) {\r\n\t\t\t var that = this;\r\n\t\t\t var formdata = e.detail.value;\r\n\t\t\t //alert(formdata.numbers);\r\n\t\t\t\r\n\t\t\tapp.post('ApiRestaurantDeposit/takeout', {bid:that.data.bid,orderid:that.data.id,numbers:formdata.numbers}, function (data) {\r\n\t\t\t\tif(data.status== 0){\r\n\t\t\t\t\tapp.alert(data.msg);return;\r\n\t\t\t\t}\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\t  that.boxShow = false; //隐藏弹框\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ width:100%;}\r\n.order-content{display:flex;flex-direction:column}\r\n.order-box{ width: 96%;margin:0 2%;margin-top:20rpx;padding:6rpx 20rpx; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;border-bottom: 1px #f4f4f4 solid; height:90rpx; line-height: 90rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{flex:1;display:flex;align-items:center;color:#222;font-weight:bold}\r\n.order-box .head .f1 image{width:56rpx;height:56rpx;margin-right:20rpx;border-radius:50%}\r\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\r\n.order-box .content{display:flex;width: 100%; border-bottom: 0 #f4f4f4 dashed;position:relative; padding: 20rpx 0;}\r\n.order-box .content:last-child{ border-bottom: 0; }\r\n.order-box .content .pic{ width: 120rpx; height: 120rpx;}\r\n.order-box .content .pic .img{ width: 120rpx; height: 120rpx;}\r\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:20rpx;flex:1;margin-top:6rpx}\r\n.order-box .content .detail .t1{font-size:28rpx;font-weight:bold;height:40rpx;line-height:40rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .detail .t2{height: 36rpx;line-height: 36rpx;color: #999;overflow: hidden;font-size: 22rpx;}\r\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.order-box .content .takeout{display:flex;align-items:center;justify-content:center;padding:0 24rpx;height:52rpx;position:absolute;top:50%;margin-top:-26rpx;right:0;border-radius:26rpx 0 0 26rpx;background:#FFE8E1;color:#222222;font-size:24rpx;font-weight:bold;\r\n    margin-right: -10px}\r\n.order-box .content .takeout .img{width:28rpx;height:28rpx;margin-right:6rpx}\r\n.order-box .content .takeout.st0{color:#f55}\r\n.order-box .content .takeout.st2{background:#F7F7F7;color:#BBBBBB}\r\n.order-box .content .takeout.st3{background:#F7F7F7;color:#888}\r\n.order-box .content .takeout.st4{background:#F7F7F7;color:#808080}\r\n\r\n.order-box .bottom{ width:100%; padding:20rpx; border-top: 0 #f4f4f4 solid; color: #555;}\r\n.order-box .op{ display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0; border-top: 0 #f4f4f4 solid; color: #555;}\r\n\r\n.btn1{margin-left:20rpx;width:200rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:44rpx;text-align:center;font-weight:bold}\r\n.btn2{margin-left:20rpx;width:200rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;font-weight:bold;border-radius:44rpx;text-align:center}\r\n\r\n.expressinfo { width: 96%;margin:0 2%;margin-top:20rpx;padding:6rpx 0;padding:6rpx 0; background: #fff;border-radius:8px}\r\n.expressinfo .content{ width: 100%;  background: #fff;display:flex;flex-direction:column;color: #979797;padding:20rpx 40rpx}\r\n.expressinfo .content .on{color: #23aa5e;}\r\n.expressinfo .content .item{display:flex;width: 96%;  margin: 0 2%;border-left: 1px #dadada solid;padding:10rpx 0}\r\n.expressinfo .content .item .f1{ width:40rpx;flex-shrink:0;position:relative}\r\n.expressinfo .content image{width: 30rpx; height: 30rpx; position: absolute; left: -16rpx; top: 22rpx;}\r\n.expressinfo .content .item .f1 image{ width: 30rpx; height: 30rpx;}\r\n.expressinfo .content .item .f2{display:flex;flex-direction:column;flex:auto;}\r\n.expressinfo .content .item .f2 .t1{font-size: 30rpx;}\r\n.expressinfo .content .item .f2 .t1{font-size: 26rpx;}\r\n\r\n.takeoutBox .btn {border-radius:44rpx; margin: 0 auto; width: 96%; color: #FFF;}\r\n.takeoutBox { padding-bottom: 30rpx;}\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:0px dashed #ededed;overflow:hidden}\r\n.orderinfo .item{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n.popup__modal{ min-height: 0; position: fixed;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065951\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}