{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?d86c", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?bb8c", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?9a19", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?c51f", "uni-app:///zhaopin/jobDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?929c", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobDetail.vue?7678"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "jobInfo", "id", "title", "salary", "work_address", "education", "experience", "company_logo", "company_name", "description", "requirement", "benefits", "work_mode", "work_intensity", "work_time_type", "payment", "numbers", "nature", "scale", "formatted_options", "is_favorite", "status", "hasApplied", "isCollected", "reportVisible", "agreementVisible", "agreementContent", "submitting", "formData", "name", "phone", "agreement", "genderOptions", "label", "value", "experienceOptions", "educationOptions", "computed", "getCompanyNature", "getCompanyScale", "isFormValid", "onLoad", "uni", "icon", "onPullDownRefresh", "setTimeout", "methods", "getJobDetail", "app", "handleCollect", "position_id", "handleApply", "closeReportDialog", "resetForm", "handleAgreementChange", "showAgreement", "closeAgreement", "submitApply", "goToCompany", "url", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4NlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAL;MACAM;QACAC;QACAC;QACAC;MACA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,oBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAE,mBACA;QAAAH;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAG;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QAAAX;QAAAC;QAAAC;MACA,eACAD,SACAA,uBACAC;IACA;EACA;EAEAU;IACA;MACA;IACA;MACA;MACAC;QACAxC;QACAyC;MACA;IACA;EACA;EAEAC;IACA;MACA;IACA;IACAC;MACAH;IACA;EACA;EAEAI;IACAC;MAAA;MACA;MACAC;QACA/C;MACA;QACA;QACA;UACA;UACA;YACA;cACAH;YACA;UACA;UAEA;UACA;UACA;UAEA4C;YACAxC;UACA;QACA;UACAwC;YACAxC;YACAyC;UACA;QACA;MACA;QACA;QACAD;UACAxC;UACAyC;QACA;MACA;IACA;IAEAM;MAAA;MACA;MAEA;MACA;;MAEAD;QACAE;MACA;QACA;UACA;UACAR;YACAxC;YACAyC;UACA;QACA;UACA;UACAD;YACAxC;YACAyC;UACA;QACA;MACA;QACA;QACAD;UACAxC;UACAyC;QACA;MACA;IACA;IAEAQ;MACA;QACAT;UACAxC;UACAyC;QACA;QACA;MACA;MAEA;QACAD;UACAxC;UACAyC;QACA;QACA;MACA;MAEA;IACA;IAEAS;MACA;MACA;MACA;IACA;IAEAC;MACA;QACAxB;QACAC;QACAC;MACA;IACA;IAEAuB;MACA;IACA;IAEAC;MAAA;MACA;MACAP;QACA;UACA;UACA;QACA;MACA;IACA;IAEAQ;MACA;IACA;IAEAC;MAAA;MACA;MAEA;MACAT;QACAE;QACArB;QACAC;MACA;QACA;QACA;UACA;UACA;UACAY;YACAxC;YACAyC;UACA;UACA;UACA;QACA;UACAD;YACAxC;YACAyC;UACA;QACA;MACA;QACA;QACAD;UACAxC;UACAyC;QACA;MACA;IACA;IAEAe;MACA;MAEAhB;QACAiB;QACAC;UACAlB;YACAxC;YACAyC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxfA;AAAA;AAAA;AAAA;AAAq6C,CAAgB,g3CAAG,EAAC,C;;;;;;;;;;;ACAz7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/jobDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/jobDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jobDetail.vue?vue&type=template&id=ff4ee70c&\"\nvar renderjs\nimport script from \"./jobDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./jobDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jobDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/jobDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobDetail.vue?vue&type=template&id=ff4ee70c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.loading ? _vm.t(\"color1\") : null\n  var m1 = !_vm.loading && !_vm.hasApplied ? _vm.t(\"color1\") : null\n  var m2 = !_vm.loading && !_vm.hasApplied ? _vm.t(\"color1rgb\") : null\n  var m3 = !_vm.loading && !_vm.hasApplied ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.reportVisible ? _vm.t(\"color1\") : null\n  var m5 = _vm.reportVisible ? _vm.t(\"color1rgb\") : null\n  var m6 = _vm.agreementVisible ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"job-detail\">\r\n    <!-- 加载状态 -->\r\n    <view class=\"loading-wrapper\" v-if=\"loading\">\r\n      <view class=\"loading-spinner\"></view>\r\n    </view>\r\n\r\n    <block v-else>\r\n      <!-- 顶部信息 -->\r\n      <view class=\"header\">\r\n        <view class=\"title-wrapper\">\r\n          <text class=\"title\">{{jobInfo.title}}</text>\r\n          <text class=\"status\" :class=\"{'status-active': jobInfo.status === 1}\">\r\n            {{jobInfo.status === 1 ? '招聘中' : '已结束'}}\r\n          </text>\r\n        </view>\r\n        <view class=\"salary\" :style=\"{color: t('color1')}\">{{jobInfo.salary}}</view>\r\n        <view class=\"basic-info\">\r\n          <text><text class=\"iconfont icon-location\"></text>{{jobInfo.work_address}}</text>\r\n          <text><text class=\"iconfont icon-education\"></text>{{jobInfo.education}}</text>\r\n          <text><text class=\"iconfont icon-experience\"></text>{{jobInfo.experience}}</text>\r\n        </view>\r\n        <scroll-view scroll-x class=\"tags-scroll\" show-scrollbar=\"false\">\r\n          <view class=\"tags\">\r\n            <text class=\"tag\" v-for=\"(tags, type) in jobInfo.formatted_options\" :key=\"type\">\r\n              <text v-for=\"(tag, index) in tags\" :key=\"index\">{{tag}}</text>\r\n            </text>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n\r\n      <!-- 公司信息 -->\r\n      <view class=\"company\" hover-class=\"company-hover\" @click=\"goToCompany\">\r\n        <image class=\"logo\" :src=\"jobInfo.company_logo\" mode=\"aspectFit\"></image>\r\n        <view class=\"company-info\">\r\n          <view class=\"name\">{{jobInfo.company_name}}</view>\r\n          <view class=\"desc\">\r\n            <text>{{getCompanyNature}}</text>\r\n            <text>{{getCompanyScale}}</text>\r\n          </view>\r\n        </view>\r\n        <text class=\"iconfont icon-arrow-right\"></text>\r\n      </view>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <scroll-view class=\"content-scroll\" scroll-y>\r\n        <!-- 工作信息 -->\r\n        <view class=\"section\">\r\n          <view class=\"section-title\">\r\n            <text class=\"iconfont icon-info\"></text>\r\n            工作信息\r\n          </view>\r\n          <view class=\"info-list\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">工作方式</text>\r\n              <text class=\"value\">{{jobInfo.work_mode}}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">工作强度</text>\r\n              <text class=\"value\">{{jobInfo.work_intensity}}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">工作时间</text>\r\n              <text class=\"value\">{{jobInfo.work_time_type}}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">结算方式</text>\r\n              <text class=\"value\">{{jobInfo.payment}}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">招聘人数</text>\r\n              <text class=\"value highlight\">{{jobInfo.numbers}}人</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 职位描述 -->\r\n        <view class=\"section\">\r\n          <view class=\"section-title\">\r\n            <text class=\"iconfont icon-description\"></text>\r\n            职位描述\r\n          </view>\r\n          <rich-text :nodes=\"jobInfo.description\" class=\"rich-content\"></rich-text>\r\n        </view>\r\n\r\n        <!-- 任职要求 -->\r\n        <view class=\"section\">\r\n          <view class=\"section-title\">\r\n            <text class=\"iconfont icon-requirement\"></text>\r\n            任职要求\r\n          </view>\r\n          <rich-text :nodes=\"jobInfo.requirement\" class=\"rich-content\"></rich-text>\r\n        </view>\r\n\r\n        <!-- 工作福利 -->\r\n        <view class=\"section\">\r\n          <view class=\"section-title\">\r\n            <text class=\"iconfont icon-welfare\"></text>\r\n            工作福利\r\n          </view>\r\n          <rich-text :nodes=\"jobInfo.benefits\" class=\"rich-content\"></rich-text>\r\n        </view>\r\n\r\n        <!-- 底部占位 -->\r\n        <view style=\"height: 120rpx;\"></view>\r\n      </scroll-view>\r\n\r\n      <!-- 底部按钮 -->\r\n      <view class=\"footer\">\r\n        <view class=\"collect\" @click=\"handleCollect\">\r\n          <text :class=\"['iconfont', isCollected ? 'icon-heart-fill' : 'icon-heart']\"></text>\r\n          <text>{{isCollected ? '已收藏' : '收藏'}}</text>\r\n        </view>\r\n        <button \r\n          class=\"apply-btn\" \r\n          :class=\"{\r\n            'apply-btn-disabled': jobInfo.status !== 1,\r\n            'apply-btn-applied': hasApplied\r\n          }\"\r\n          :style=\"{\r\n            background: hasApplied ? '#52c41a' : 'linear-gradient(135deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)',\r\n            boxShadow: hasApplied ? '0 8rpx 24rpx rgba(82, 196, 26, 0.25)' : '0 8rpx 24rpx rgba('+t('color1rgb')+',0.25)'\r\n          }\"\r\n          :disabled=\"jobInfo.status !== 1 || hasApplied\"\r\n          @click=\"handleApply\"\r\n        >\r\n          <block v-if=\"jobInfo.status === 1\">\r\n            {{hasApplied ? '已报名' : '立即报名'}}\r\n          </block>\r\n          <block v-else>已结束</block>\r\n        </button>\r\n      </view>\r\n    </block>\r\n\r\n    <!-- 报名弹窗 -->\r\n    <view class=\"report-dialog\" v-if=\"reportVisible\">\r\n      <view class=\"dialog-mask\" @click=\"closeReportDialog\"></view>\r\n      <view class=\"dialog-content\">\r\n        <!-- 弹窗头部 -->\r\n        <view class=\"dialog-header\">\r\n          <text class=\"dialog-title\">职位报名</text>\r\n          <text class=\"dialog-close iconfont icon-close\" @click=\"closeReportDialog\"></text>\r\n        </view>\r\n\r\n        <!-- 职位信息 -->\r\n        <view class=\"dialog-job-info\">\r\n          <text class=\"job-title\">{{jobInfo.title}}</text>\r\n          <text class=\"job-salary\">{{jobInfo.salary}}</text>\r\n          <text class=\"job-company\">{{jobInfo.company_name}}</text>\r\n        </view>\r\n\r\n        <!-- 报名表单 -->\r\n        <scroll-view class=\"dialog-form\" scroll-y>\r\n          <!-- 基本信息 -->\r\n          <view class=\"form-section\">\r\n            <view class=\"form-title\">基本信息</view>\r\n            <view class=\"form-item\">\r\n              <text class=\"label\">姓名</text>\r\n              <input \r\n                class=\"input\" \r\n                v-model=\"formData.name\" \r\n                placeholder=\"请输入真实姓名\"\r\n                :maxlength=\"20\"\r\n              />\r\n            </view>\r\n            <view class=\"form-item\">\r\n              <text class=\"label\">手机号</text>\r\n              <input \r\n                class=\"input\" \r\n                v-model=\"formData.phone\" \r\n                type=\"number\"\r\n                placeholder=\"请输入手机号\"\r\n                :maxlength=\"11\"\r\n              />\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n\r\n        <!-- 底部按钮 -->\r\n        <view class=\"dialog-footer\">\r\n          <checkbox-group class=\"agreement\" @change=\"handleAgreementChange\">\r\n            <checkbox :checked=\"formData.agreement\" />\r\n            <text>我已阅读并同意</text>\r\n            <text class=\"link\" @click=\"showAgreement\">《用户服务协议》</text>\r\n          </checkbox-group>\r\n          <button \r\n            class=\"submit-btn\" \r\n            :disabled=\"!isFormValid || submitting\"\r\n            :style=\"{background: 'linear-gradient(135deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\"\r\n            @click=\"submitApply\"\r\n          >\r\n            {{submitting ? '提交中...' : '确认报名'}}\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 协议弹窗 -->\r\n    <view class=\"agreement-dialog\" v-if=\"agreementVisible\">\r\n      <view class=\"dialog-mask\" @click=\"closeAgreement\"></view>\r\n      <view class=\"dialog-content\">\r\n        <view class=\"dialog-header\">\r\n          <text class=\"dialog-title\">用户服务协议</text>\r\n          <text class=\"dialog-close iconfont icon-close\" @click=\"closeAgreement\"></text>\r\n        </view>\r\n        <scroll-view class=\"agreement-content\" scroll-y>\r\n          <rich-text :nodes=\"agreementContent\"></rich-text>\r\n        </scroll-view>\r\n        <view class=\"dialog-footer\">\r\n          <button class=\"confirm-btn\" \r\n            :style=\"{background: t('color1')}\"\r\n            @click=\"closeAgreement\"\r\n          >我知道了</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nconst app = getApp()\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      jobInfo: {\r\n        id: 0,\r\n        title: '',\r\n        salary: '',\r\n        work_address: '',\r\n        education: '',\r\n        experience: '',\r\n        company_logo: '',\r\n        company_name: '',\r\n        description: '',\r\n        requirement: '',\r\n        benefits: '',\r\n        work_mode: '',\r\n        work_intensity: '',\r\n        work_time_type: '',\r\n        payment: '',\r\n        numbers: 0,\r\n        nature: 1,\r\n        scale: 1,\r\n        formatted_options: {},\r\n        is_favorite: 0,\r\n        status: 1,\r\n        hasApplied: false\r\n      },\r\n      isCollected: false,\r\n      reportVisible: false,\r\n      agreementVisible: false,\r\n      agreementContent: '',\r\n      submitting: false,\r\n      hasApplied: false,\r\n      formData: {\r\n        name: '',\r\n        phone: '',\r\n        agreement: false\r\n      },\r\n      genderOptions: [\r\n        { label: '男', value: '1' },\r\n        { label: '女', value: '2' }\r\n      ],\r\n      experienceOptions: [\r\n        { label: '应届生', value: '0' },\r\n        { label: '1年以下', value: '1' },\r\n        { label: '1-3年', value: '2' },\r\n        { label: '3-5年', value: '3' },\r\n        { label: '5-10年', value: '4' },\r\n        { label: '10年以上', value: '5' }\r\n      ],\r\n      educationOptions: [\r\n        { label: '初中及以下', value: '1' },\r\n        { label: '高中', value: '2' },\r\n        { label: '大专', value: '3' },\r\n        { label: '本科', value: '4' },\r\n        { label: '硕士', value: '5' },\r\n        { label: '博士', value: '6' }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    getCompanyNature() {\r\n      const natureMap = {\r\n        1: '民营企业',\r\n        2: '国有企业',\r\n        3: '合资企业',\r\n        4: '外资企业'\r\n      }\r\n      return natureMap[this.jobInfo.nature] || '其他企业'\r\n    },\r\n\r\n    getCompanyScale() {\r\n      const scaleMap = {\r\n        1: '20-99人',\r\n        2: '100-499人',\r\n        3: '500-999人',\r\n        4: '1000-9999人',\r\n        5: '10000人以上'\r\n      }\r\n      return scaleMap[this.jobInfo.scale] || '规模未知'\r\n    },\r\n\r\n    isFormValid() {\r\n      const { name, phone, agreement } = this.formData\r\n      return name && \r\n             phone && \r\n             phone.length === 11 && \r\n             agreement\r\n    }\r\n  },\r\n\r\n  onLoad(options) {\r\n    if (options.id) {\r\n      this.getJobDetail(options.id)\r\n    } else {\r\n      this.loading = false\r\n      uni.showToast({\r\n        title: '参数错误',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    if (this.jobInfo.id) {\r\n      this.getJobDetail(this.jobInfo.id)\r\n    }\r\n    setTimeout(() => {\r\n      uni.stopPullDownRefresh()\r\n    }, 1000)\r\n  },\r\n\r\n  methods: {\r\n    getJobDetail(id) {\r\n      this.loading = true\r\n      app.get('ApiZhaopin/getPositionDetail', {\r\n        id: id\r\n      }, (res) => {\r\n        this.loading = false\r\n        if (res.status === 1 && res.data) {\r\n          const data = res.data\r\n          ;['description', 'requirement', 'benefits'].forEach(key => {\r\n            if (data[key]) {\r\n              data[key] = data[key].replace(/<img/gi, '<img style=\"max-width:100%;height:auto;display:block;margin:10rpx 0;\"')\r\n            }\r\n          })\r\n          \r\n          this.jobInfo = data\r\n          this.isCollected = data.is_favorite === 1\r\n          this.hasApplied = data.hasApplied || false\r\n          \r\n          uni.setNavigationBarTitle({\r\n            title: data.title || '职位详情'\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取数据失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      }, () => {\r\n        this.loading = false\r\n        uni.showToast({\r\n          title: '网络错误，请重试',\r\n          icon: 'none'\r\n        })\r\n      })\r\n    },\r\n\r\n    handleCollect() {\r\n      if (!this.jobInfo.id) return\r\n      \r\n      const prevState = this.isCollected\r\n      this.isCollected = !this.isCollected // 优先切换状态，提升体验\r\n      \r\n      app.post('apiZhaopin/favoritePosition', {\r\n        position_id: this.jobInfo.id\r\n      }, (res) => {\r\n        if (res.status === 1) {\r\n          this.isCollected = res.data.is_favorite === 1\r\n          uni.showToast({\r\n            title: this.isCollected ? '收藏成功' : '已取消收藏',\r\n            icon: 'none'\r\n          })\r\n        } else {\r\n          this.isCollected = prevState // 恢复之前的状态\r\n          uni.showToast({\r\n            title: res.msg || '操作失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      }, () => {\r\n        this.isCollected = prevState // 恢复之前的状态\r\n        uni.showToast({\r\n          title: '网络错误，请重试',\r\n          icon: 'none'\r\n        })\r\n      })\r\n    },\r\n\r\n    handleApply() {\r\n      if (this.jobInfo.status !== 1) {\r\n        uni.showToast({\r\n          title: '该职位已结束招聘',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.hasApplied) {\r\n        uni.showToast({\r\n          title: '您已经报名过该职位',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.reportVisible = true\r\n    },\r\n\r\n    closeReportDialog() {\r\n      this.reportVisible = false\r\n      // 重置表单\r\n      this.resetForm()\r\n    },\r\n\r\n    resetForm() {\r\n      this.formData = {\r\n        name: '',\r\n        phone: '',\r\n        agreement: false\r\n      }\r\n    },\r\n\r\n    handleAgreementChange(e) {\r\n      this.formData.agreement = e.detail.value.length > 0\r\n    },\r\n\r\n    showAgreement() {\r\n      // 获取协议内容\r\n      app.get('ApiZhaopin/getAgreement', {}, (res) => {\r\n        if (res.status === 1) {\r\n          this.agreementContent = res.data.content\r\n          this.agreementVisible = true\r\n        }\r\n      })\r\n    },\r\n\r\n    closeAgreement() {\r\n      this.agreementVisible = false\r\n    },\r\n\r\n    submitApply() {\r\n      if (!this.isFormValid) return\r\n\r\n      this.submitting = true\r\n      app.post('ApiZhaopin/submitApply', {\r\n        position_id: this.jobInfo.id,\r\n        name: this.formData.name,\r\n        phone: this.formData.phone\r\n      }, (res) => {\r\n        this.submitting = false\r\n        if (res.status === 1) {\r\n          this.hasApplied = true\r\n          this.reportVisible = false\r\n          uni.showToast({\r\n            title: '报名成功',\r\n            icon: 'success'\r\n          })\r\n          // 重新获取职位详情，更新状态\r\n          this.getJobDetail(this.jobInfo.id)\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '报名失败，请重试',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      }, () => {\r\n        this.submitting = false\r\n        uni.showToast({\r\n          title: '网络错误，请重试',\r\n          icon: 'none'\r\n        })\r\n      })\r\n    },\r\n\r\n    goToCompany() {\r\n      if (!this.jobInfo.company_id) return\r\n      \r\n      uni.navigateTo({\r\n        url: `/zhaopin/company?id=${this.jobInfo.company_id}`,\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import './jobDetail.scss';\r\n\r\n/* 添加弹窗相关样式 */\r\n.report-dialog {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 999;\r\n\r\n  .dialog-mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.6);\r\n    backdrop-filter: blur(4px);\r\n  }\r\n\r\n  .dialog-content {\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: #fff;\r\n    border-radius: 24rpx 24rpx 0 0;\r\n    padding: 30rpx;\r\n    max-height: 80vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    animation: slideUp 0.3s ease-out;\r\n  }\r\n\r\n  .dialog-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 30rpx;\r\n\r\n    .dialog-title {\r\n      font-size: 36rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n\r\n    .dialog-close {\r\n      font-size: 40rpx;\r\n      color: #999;\r\n      padding: 10rpx;\r\n    }\r\n  }\r\n\r\n  .dialog-job-info {\r\n    background: #f8f9fa;\r\n    padding: 20rpx;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 30rpx;\r\n\r\n    .job-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n      margin-bottom: 12rpx;\r\n    }\r\n\r\n    .job-salary {\r\n      font-size: 28rpx;\r\n      color: #ff4d4f;\r\n      margin-bottom: 8rpx;\r\n    }\r\n\r\n    .job-company {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .dialog-form {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n\r\n    .form-section {\r\n      margin-bottom: 30rpx;\r\n\r\n      .form-title {\r\n        font-size: 30rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n      }\r\n\r\n      .form-item {\r\n        margin-bottom: 20rpx;\r\n\r\n        .label {\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          margin-bottom: 12rpx;\r\n          display: block;\r\n        }\r\n\r\n        .input {\r\n          width: 100%;\r\n          height: 88rpx;\r\n          background: #f8f9fa;\r\n          border-radius: 12rpx;\r\n          padding: 0 24rpx;\r\n          font-size: 28rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    padding-top: 20rpx;\r\n    border-top: 1px solid #f0f0f0;\r\n\r\n    .agreement {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      font-size: 26rpx;\r\n      color: #666;\r\n\r\n      .link {\r\n        color: var(--primary-color);\r\n      }\r\n    }\r\n\r\n    .submit-btn {\r\n      width: 100%;\r\n      height: 88rpx;\r\n      border-radius: 44rpx;\r\n      color: #fff;\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      &:disabled {\r\n        opacity: 0.6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.agreement-dialog {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n\r\n  .dialog-mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.6);\r\n    backdrop-filter: blur(4px);\r\n  }\r\n\r\n  .dialog-content {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 80%;\r\n    max-width: 600rpx;\r\n    background: #fff;\r\n    border-radius: 24rpx;\r\n    padding: 30rpx;\r\n    max-height: 80vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .agreement-content {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    margin: 30rpx 0;\r\n    padding: 0 20rpx;\r\n  }\r\n\r\n  .confirm-btn {\r\n    width: 100%;\r\n    height: 88rpx;\r\n    background: #ff4d4f;\r\n    border-radius: 44rpx;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(100%);\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 修改底部按钮样式 */\r\n.footer {\r\n  .apply-btn {\r\n    &.apply-btn-disabled {\r\n      opacity: 0.6;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115058830\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}