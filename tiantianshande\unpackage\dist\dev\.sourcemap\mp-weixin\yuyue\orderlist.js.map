{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?cb91", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?fc20", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?cf61", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?7a54", "uni-app:///yuyue/orderlist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?71b7", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/orderlist.vue?5411"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "keyword", "isshow<PERSON><PERSON>", "hasPayFirstOrders", "flow_mode", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "getdata", "that", "app", "console", "hasOrders", "item", "changetab", "uni", "scrollTop", "duration", "toclose", "orderid", "setTimeout", "todel", "orderCollect", "searchConfirm", "goToAppoint", "goToSelectWorker", "goToComment", "goToCalendar"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3IA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4JlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;IACA;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAnB;QAAAE;QAAAI;MAAA;QACAY;QACA;QACA;QACA;QACAA;QAEA;UACAA;;UAEA;UACA;YACAE;YACAA;YACA;YACAzB;cACA;gBACAyB;cACA;YACA;UACA;UAEA;YACAF;UACA;;UAEA;UACA;UACA;YACA;YACA;cACA;cACA;gBACAG;gBACA;cACA;YACA;cACA;cACA,iFACAC,6DACAA;gBACAD;gBACA;cACA;YACA;UACA;UACAH;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAK;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAR;QACAA;QACAA;UAAAS;QAAA;UACAT;UACAA;UACAU;YACAX;UACA;QACA;MACA;IACA;IACAY;MACA;MACA;MACAX;QACAA;QACAA;UAAAS;QAAA;UACAT;UACAA;UACAU;YACAX;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACAZ;QACAA;UAAAS;QAAA;UACAT;UACAU;YACAX;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;IACA;IACAC;MACAb;MACAD;IACA;IACAe;MACAd;MACA;MACA;MAEA;QACA;QACA;QACAA;QACAD;MACA;QACAC;QACAD;MACA;IACA;IACAgB;MACAhB;IACA;IAEA;IACAiB;MACAjB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChWA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/orderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/orderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderlist.vue?vue&type=template&id=7864b928&\"\nvar renderjs\nimport script from \"./orderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/orderlist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=template&id=7864b928&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.status == 0 ? _vm.t(\"color1\") : null\n        var m1 =\n          item.status == 1 &&\n          (item.order_flow_mode == 1 || !item.order_flow_mode) &&\n          (item.appointment_status === 0 || !item.appointment_status) &&\n          !item.yy_time &&\n          !item.yy_time_format\n            ? _vm.t(\"color1\")\n            : null\n        var m2 =\n          item.status == 1 &&\n          (item.order_flow_mode == 1 || !item.order_flow_mode) &&\n          item.appointment_status === 1 &&\n          (item.worker_assign_status === 0 || !item.worker_id) &&\n          !item.fwname &&\n          !item.worker_id\n            ? _vm.t(\"color1\")\n            : null\n        var m3 =\n          item.status == 5 && !item.yy_time && !item.yy_time_format\n            ? _vm.t(\"color1\")\n            : null\n        var m4 =\n          item.status == 6 && !item.fwname && !item.worker_id\n            ? _vm.t(\"color1\")\n            : null\n        var m5 =\n          item.status == 2 &&\n          item.balance_pay_status == 0 &&\n          item.balance_price > 0 &&\n          item.addmoney == 0\n            ? _vm.t(\"color1\")\n            : null\n        var m6 =\n          item.status == 2 &&\n          !(\n            item.balance_pay_status == 0 &&\n            item.balance_price > 0 &&\n            item.addmoney == 0\n          ) &&\n          item.balance_pay_status == 0 &&\n          item.balance_price > 0 &&\n          item.addmoney > 0 &&\n          item.addmoneyStatus == 1\n            ? _vm.t(\"color1\")\n            : null\n        var m7 =\n          item.status == 2 && item.addmoneyStatus == 0 && item.addmoney > 0\n            ? _vm.t(\"color1\")\n            : null\n        var m8 =\n          (item.status == 3 || item.status == 4) &&\n          item.status == 3 &&\n          item.isconmement == 0\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<dd-tab :itemdata=\"flow_mode === 1 ? ['全部','待付款','待选择时间','待分配服务','服务中','已完成','已取消'] : ['全部','待付款','派单中','待确认','已完成','已取消']\" \r\n\t\t:itemst=\"flow_mode === 1 ? ['all','0','5','6','2','3','4'] : ['all','0','1','2','3','4']\" \r\n\t\t:st=\"st\" \r\n\t\t:isfixed=\"true\" \r\n\t\t@changetab=\"changetab\"></dd-tab>\r\n\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t<!-- #ifndef H5 || APP-PLUS -->\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!--  #endif -->\r\n\t\t\r\n\t\t<!-- 添加先支付后预约模式提示 -->\r\n\t\t<view class=\"tips\" v-if=\"hasPayFirstOrders\">\r\n\t\t\t<text>您有订单需要预约或选择服务人员，请点击对应订单的按钮完成操作</text>\r\n\t\t\t<view class=\"tips-action\">\r\n\t\t\t\t<text @tap=\"goToCalendar\">打开预约日历</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"order-content\">\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"order-box\" @tap.stop=\"goto\" :data-url=\"'orderdetail?id=' + item.id\">\r\n\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.bid!=0\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.bid\"><image src=\"/static/img/ico-shop.png\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t<view v-else>订单号：{{item.ordernum}}</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"item.status==1 && item.refund_status==0 && item.worker_orderid && flow_mode !== 1\">\r\n\t\t\t\t\t\t\t<text v-if=\"item.worker.status==0\" class=\"st1\">待接单</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.worker.status==1\" class=\"st1\">已接单</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.worker.status==2\" class=\"st2\">服务中</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else-if=\"item.status==1 && item.refund_status==0 && flow_mode !== 1\">\r\n\t\t\t\t\t\t\t<text v-if=\"(item.order_flow_mode == 1 || !item.order_flow_mode) && (item.appointment_status === 0 || !item.appointment_status) && (!item.yy_time && !item.yy_time_format)\" class=\"st1\" style=\"color:#ff4d4f;\">待预约</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"(item.order_flow_mode == 1 || !item.order_flow_mode) && item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id) && !item.fwname && !item.worker_id\" class=\"st1\" style=\"color:#ff4d4f;\">待选择服务人员</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.yy_time && (item.worker_id > 0 || item.fwname)\" class=\"st1\">已接单</text>\r\n\t\t\t\t\t\t\t<text v-else class=\"st1\">派单中</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<!-- 优化模式下的状态处理 -->\r\n\t\t\t\t\t\t<block v-else-if=\"item.status==1 && flow_mode === 1\">\r\n\t\t\t\t\t\t\t<text v-if=\"(item.appointment_status === 0 || !item.appointment_status) && (!item.yy_time && !item.yy_time_format)\" class=\"st1\" style=\"color:#ff4d4f;\">待选择时间</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id) && !item.fwname && !item.worker_id\" class=\"st1\" style=\"color:#ff4d4f;\">待分配服务</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.yy_time && (item.worker_id > 0 || item.fwname)\" class=\"st1\">已接单</text>\r\n\t\t\t\t\t\t\t<text v-else class=\"st1\">派单中</text>\r\n\t\t\t\t\t\t</block>\t\r\n\t\t\t\t\t\t<text v-if=\"item.status==1 && item.refund_status==1\" class=\"st1\">退款审核中</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">服务中</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==3 && item.isconmement==0\" class=\"st3\">待评价</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st4\">已完成</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">订单已关闭</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==5\" class=\"st1\" style=\"color:#ff4d4f;\">待选择时间</text>\r\n\t\t\t\t\t\t<text v-if=\"item.status==6\" class=\"st1\" style=\"color:#ff4d4f;\">待分配服务</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content\" style=\"border-bottom:none\">\r\n\t\t\t\t\t\t<view v-if=\"item.paidan_type==3\" >\r\n\t\t\t\t\t\t\t<image :src=\"item.propic\" ></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else @tap.stop=\"goto\" :data-url=\"'product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t<image :src=\"item.propic\"></image>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.proname}}</text>\r\n\t\t\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.yy_time_format\">预约日期：{{item.yy_time_format}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-else-if=\"item.yy_time\">预约日期：{{item.yy_time}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-else-if=\"item.status==5\" style=\"color:#ff4d4f;\">请点击选择预约时间</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-else-if=\"item.status==6 && !item.fwname && !item.worker_id\" style=\"color:#ff4d4f;\">等待分配服务人员</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-else-if=\"item.status==1 && (item.order_flow_mode == 1 || !item.order_flow_mode)\">\r\n\t\t\t\t\t\t\t\t<template v-if=\"(item.appointment_status === 0 || !item.appointment_status) && (!item.yy_time && !item.yy_time_format)\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#ff4d4f;\">请点击立即预约</text>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<template v-else-if=\"item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id) && !item.fwname && !item.worker_id\">\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#ff4d4f;\">请选择服务人员</text>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<template v-else-if=\"item.fwname\">\r\n\t\t\t\t\t\t\t\t\t<text>服务人员：{{item.fwname}}</text>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t\t\t<text>预约日期：暂未预约</text>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\" v-else>预约日期：暂未预约</text>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.balance_price>0\"><text class=\"x1 flex1\">实付金额：￥{{item.totalprice}}</text><text class=\"x1 flex1\" v-if=\"item.balance_price>0\">尾款：￥{{item.balance_price}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"t3\" v-else><text class=\"x1 flex1\">实付金额：￥{{item.totalprice}}</text><text class=\"x1 flex1\" v-if=\"isshowpandan\">含跑腿费：￥{{item.paidan_money}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bottom\"  v-if=\"item.send_time>0\">\r\n\t\t\t\t\t\t<text>派单时间：{{item.senddate}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red\"> 退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red\"> 已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red\"> 退款申请已驳回</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'orderdetail?id=' + item.id\" class=\"btn2\">详情</view>\r\n\t\t\t\t\t\t<block v-if=\"item.status==0\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\r\n\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.payorderid\">去付款</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"item.status==1\" >\r\n\t\t\t\t\t\t\t<view class=\"btn2\"  v-if=\"item.totalprice==0\" @tap.stop=\"toclose\" :data-id=\"item.id\">取消订单</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.totalprice>0 && (item.refund_status==0 || item.refund_status==3)\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\r\n\t\t\t\t\t\t\t<view v-if=\"(item.order_flow_mode == 1 || !item.order_flow_mode) && (item.appointment_status === 0 || !item.appointment_status) && (!item.yy_time && !item.yy_time_format)\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goToAppoint(item)\">立即预约</view>\r\n\t\t\t\t\t\t\t<view v-if=\"(item.order_flow_mode == 1 || !item.order_flow_mode) && item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id) && !item.fwname && !item.worker_id\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goToSelectWorker(item)\">选择服务人员</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.yy_time && (item.worker_id > 0 || item.fwname) && item.worker_orderid\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'logistics?express_no=' + item.worker_orderid\">查看进度</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"item.status==5\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" v-if=\"item.totalprice>0 && (item.refund_status==0 || item.refund_status==3)\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\r\n\t\t\t\t\t\t\t<view v-if=\"!item.yy_time && !item.yy_time_format\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goToAppoint(item)\">选择时间</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.yy_time && (item.worker_id > 0 || item.fwname) && item.worker_orderid\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'logistics?express_no=' + item.worker_orderid\">查看进度</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"item.status==6\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" v-if=\"item.totalprice>0 && (item.refund_status==0 || item.refund_status==3)\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\r\n\t\t\t\t\t\t\t<view v-if=\"!item.fwname && !item.worker_id\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goToSelectWorker(item)\">选择服务人员</view>\r\n\t\t\t\t\t\t\t<view v-if=\"(item.fwname || item.worker_id) && item.worker_orderid\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'logistics?express_no=' + item.worker_orderid\">查看进度</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"item.status==2\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.refund_status==0 || item.refund_status==3\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.worker_orderid\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'logistics?express_no=' + item.worker_orderid\">查看进度</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"item.balance_pay_status == 0 && item.balance_price > 0 && item.addmoney == 0\"  class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.balance_pay_orderid\">支付尾款</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.balance_pay_status == 0 && item.balance_price > 0 && item.addmoney > 0 && item.addmoneyStatus == 1\"  class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.balance_pay_orderid\">支付尾款</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"item.addmoneyStatus == 0 && item.addmoney > 0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.addmoneyPayorderid\">补差价</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"item.status==3 || item.status==4\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.status==3 && item.isconmement==0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goToComment(item)\">评价</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      st: 'all',\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n      nodata: false,\r\n      codtxt: \"\",\r\n\t\t\tkeyword:'',\r\n\t\t\tisshowpandan:false,\r\n\t\t\thasPayFirstOrders: false,\r\n\t\t\tflow_mode: 0 // 默认为常规模式\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\t// --- 添加的代码开始 ---\r\n\t\t// 检查 opt 中是否存在 st 参数，如果存在则赋值给 this.st\r\n\t\tif (this.opt && this.opt.st) {\r\n\t\t\tthis.st = this.opt.st;\r\n\t\t}\r\n\t\t// --- 添加的代码结束 ---\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n\tonNavigationBarSearchInputConfirmed:function(e){\r\n\t\tthis.searchConfirm({detail:{value:e.text}});\r\n\t},\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiYuyue/orderlist', {st: st,pagenum: pagenum,keyword:that.keyword}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.datalist;\r\n\t\t\t\tvar ishowpaidan = res.ishowpaidan;\r\n\t\t\t\t// 获取flow_mode\r\n\t\t\t\tthat.flow_mode = res.flow_mode || 0;\r\n\t\t\t\t\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 添加调试信息\r\n\t\t\t\t\tif (data && data.length > 0) {\r\n\t\t\t\t\t\tconsole.log('订单数据示例:', data[0]);\r\n\t\t\t\t\t\tconsole.log('系统模式:', that.flow_mode === 1 ? '优化模式' : '常规模式');\r\n\t\t\t\t\t\t// 检测数据中是否具有先支付后预约的特征\r\n\t\t\t\t\t\tdata.forEach(item => {\r\n\t\t\t\t\t\t\tif ((item.status == 5 || item.status == 6 || (item.status == 1 && !item.yy_time))) {\r\n\t\t\t\t\t\t\t\tconsole.log('需要处理的订单:', item.id, ', status:', item.status);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新逻辑以使用新的API数据结构和状态码\r\n\t\t\t\t\tlet hasOrders = false;\r\n\t\t\t\t\tfor (let i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tconst item = data[i];\r\n\t\t\t\t\t\tif (that.flow_mode === 1) {\r\n\t\t\t\t\t\t\t// 优化模式下的判断逻辑\r\n\t\t\t\t\t\t\tif (item.status == 5 || item.status == 6) {\r\n\t\t\t\t\t\t\t\thasOrders = true;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 常规模式下的判断逻辑\r\n\t\t\t\t\t\t\tif (item.status == 1 && (item.order_flow_mode == 1 || !item.order_flow_mode) && \r\n\t\t\t\t\t\t\t   ((item.appointment_status === 0 || !item.appointment_status) || \r\n\t\t\t\t\t\t\t    (item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id)))) {\r\n\t\t\t\t\t\t\t\thasOrders = true;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.hasPayFirstOrders = hasOrders;\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    changetab: function (st) {\r\n      this.st = st;\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n    toclose: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiYuyue/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    todel: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\tapp.showLoading('删除中');\r\n        app.post('ApiYuyue/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    orderCollect: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定已完成吗?', function () {\r\n        app.post('ApiYuyue/orderCollect', {orderid: orderid}, function (data) {\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\t\tsearchConfirm:function(e){\r\n\t\t\tthis.keyword = e.detail.value;\r\n      this.getdata(false);\r\n\t\t},\r\n    goToAppoint(item) {\r\n      console.log('跳转到预约页面:', item.id);\r\n      app.goto('/yuyue/appoint?id=' + item.id);\r\n    },\r\n    goToSelectWorker(item) {\r\n      console.log('跳转到选择服务人员页面:', item.id);\r\n      // 优先使用yydate，如果不存在则使用yy_date\r\n      const date = item.yydate || item.yy_date || '';\r\n      \r\n      try {\r\n        // 使用完整的路径格式\r\n        const url = '/yuyue/selectworker?id=' + item.id + '&yydate=' + date;\r\n        console.log('导航路径:', url);\r\n        app.goto(url);\r\n      } catch (e) {\r\n        console.error('导航失败:', e);\r\n        app.error('页面跳转失败');\r\n      }\r\n    },\r\n    goToComment(item) {\r\n      app.goto('/yuyue/comment?id=' + item.id);\r\n    },\r\n    \r\n    // 跳转到日历界面\r\n    goToCalendar() {\r\n      app.goto('/yuyue/usercalendar');\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ width:100%;}\r\n.topsearch{width:94%;margin:10rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n.order-content{display:flex;flex-direction:column}\r\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#333}\r\n.order-box .head .f1 image{width:34rpx;height:34rpx;margin-right:4px}\r\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\r\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\r\n.order-box .content:last-child{ border-bottom: 0; }\r\n.order-box .content image{ width: 140rpx; height: 140rpx;}\r\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.order-box .content .detail .x1{ flex:1}\r\n.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\r\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n.order-box .op{ display:flex;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\r\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center; font-size: 24rpx;}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;font-size: 24rpx;}\r\n\r\n/* 添加提示样式 */\r\n.tips {\r\n  width: 94%;\r\n  margin: 0 3% 10rpx 3%;\r\n  padding: 15rpx;\r\n  background-color: #fff5f5;\r\n  border: 1px solid #ffccc7;\r\n  border-radius: 8rpx;\r\n  color: #ff4d4f;\r\n  font-size: 24rpx;\r\n  line-height: 32rpx;\r\n  text-align: center;\r\n}\r\n\r\n.tips-action {\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.tips-action text {\r\n  display: inline-block;\r\n  padding: 6rpx 20rpx;\r\n  background-color: #ff4d4f;\r\n  color: #fff;\r\n  border-radius: 6rpx;\r\n  font-size: 24rpx;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045126\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}