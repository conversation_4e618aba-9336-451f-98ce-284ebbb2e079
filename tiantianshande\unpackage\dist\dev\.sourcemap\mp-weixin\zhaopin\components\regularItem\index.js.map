{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?2fb6", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?78c3", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?6b90", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?9b0d", "uni-app:///zhaopin/components/regularItem/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?2d51", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/components/regularItem/index.vue?9dd9"], "names": ["getApp", "data", "item", "channel", "recommendLabelType", "props", "type", "default", "ptpId", "index", "dotShow", "isJobList", "className", "listIndex", "needSelfClick", "keywords", "noMarginBottom", "lazyload", "page", "isRank", "remark", "methods", "_universalJump", "init", "e", "icon", "labelName", "background", "r", "a", "p", "<PERSON><PERSON><PERSON>", "param", "c", "jumpBannerLink", "handleTapFresh", "classTap", "showJobDetail", "uni", "url", "watch", "handler", "immediate", "deep"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4G9wBA;AAAA,gBAGA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAJ;MACAK;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAD;MACAA;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;EACA;EACAc;IACAC;IACAC;MACA;MAEA;QACAC;UACA;YACAA;UACA;UAEA;QACA;MACA;MAEA;QACAA;QACAA;MACA;QACAA;MACA;MAEAA;MACAA;MAEA;QACAA;UACAC;UACAC;UACAC;QACA;MACA;QACA;UACAH;YACAC;YACAC;YACAC;UACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UAEA;YACAC;UACA;UAEAA;UACAA;UACAJ;UACAK;QACA;QAEA;UACA;UACA;UACA;YACAC;UACA;UAEA;YACA;cACAC;cACAC;cACAL;cACAF;YACA;YAEA;cACAQ;YACA;cACA;gBACAA;cACA;gBACA;kBACAA;gBACA;cACA;YACA;YAEAT;UACA;QACA;MACA;MAEA;QACA;UACAA;QACA;UACAA;QACA;MACA;MAEA;QACAA;QACAA;MACA;QACAA;MACA;MACA;MACA;IACA;IACAU;IACAC;IACAC;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACAvC;MACAwC;QACA;MACA;MAEAC;MACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAAy7C,CAAgB,o4CAAG,EAAC,C;;;;;;;;;;;ACA78C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/components/regularItem/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=413743fd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=413743fd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"413743fd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/components/regularItem/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=413743fd&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !(_vm.item.componentName === \"innerSlider\") &&\n    !(_vm.item.componentName === \"innerBanner\") &&\n    !(_vm.item.componentName === \"innerRankList\") &&\n    !(_vm.item.componentName === \"classList\") &&\n    !(_vm.item.componentName === \"refreshBtn\") &&\n    !(_vm.item.componentName === \"anchorList\") &&\n    !(_vm.item.componentName === \"innerLikeArea\")\n      ? _vm.t(\"color2\")\n      : null\n  var m1 =\n    !(_vm.item.componentName === \"innerSlider\") &&\n    !(_vm.item.componentName === \"innerBanner\") &&\n    !(_vm.item.componentName === \"innerRankList\") &&\n    !(_vm.item.componentName === \"classList\") &&\n    !(_vm.item.componentName === \"refreshBtn\") &&\n    !(_vm.item.componentName === \"anchorList\") &&\n    !(_vm.item.componentName === \"innerLikeArea\") &&\n    _vm.item.unit\n      ? _vm.t(\"color2\")\n      : null\n  var g0 =\n    !(_vm.item.componentName === \"innerSlider\") &&\n    !(_vm.item.componentName === \"innerBanner\") &&\n    !(_vm.item.componentName === \"innerRankList\") &&\n    !(_vm.item.componentName === \"classList\") &&\n    !(_vm.item.componentName === \"refreshBtn\") &&\n    !(_vm.item.componentName === \"anchorList\") &&\n    !(_vm.item.componentName === \"innerLikeArea\")\n      ? _vm.item.labels && _vm.item.labels.length && _vm.item.jobLineType !== 2\n      : null\n  var m2 =\n    !(_vm.item.componentName === \"innerSlider\") &&\n    !(_vm.item.componentName === \"innerBanner\") &&\n    !(_vm.item.componentName === \"innerRankList\") &&\n    !(_vm.item.componentName === \"classList\") &&\n    !(_vm.item.componentName === \"refreshBtn\") &&\n    !(_vm.item.componentName === \"anchorList\") &&\n    !(_vm.item.componentName === \"innerLikeArea\") &&\n    !(_vm.isRank && _vm.index <= 2)\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <inner-slider :list=\"item.componentData\" :ptpId=\"'innerSlider-' + listIndex + '-' + (index + 1)\" v-if=\"item.componentName === 'innerSlider'\"></inner-slider>\r\n        <inner-banner :list=\"item.componentData\" :ptpId=\"'innerBanner-' + listIndex + '-' + (index + 1)\" v-else-if=\"item.componentName === 'innerBanner'\"></inner-banner>\r\n        <block v-else-if=\"item.componentName === 'innerRankList'\">\r\n            <inner-rank\r\n                :ptpId=\"'innerRank-' + listIndex + '-' + (index + 1)\"\r\n                :rankList=\"item.componentData.rankingList\"\r\n                v-if=\"item.componentData && item.componentData.rankingList\"\r\n            ></inner-rank>\r\n        </block>\r\n        <class-list :classData=\"item.componentData\" v-else-if=\"item.componentName === 'classList'\"></class-list>\r\n        <view @tap=\"handleTapFresh\" class=\"fresh-button ptp_exposure\" data-ptpid=\"b33d-19b7-a285-2f9a\" id=\"pid=b33d-19b7-a285-2f9a\" v-else-if=\"item.componentName === 'refreshBtn'\">\r\n            <image lazyLoad src=\"https://qiniu-image.qtshe.com/newHomePage/fresh.png\"></image>\r\n            点击刷新更多优质岗位\r\n        </view>\r\n        <anchor-list :hostList=\"item.componentData\" v-else-if=\"item.componentName === 'anchorList'\"></anchor-list>\r\n        <inner-like :likeList=\"item.componentData\" v-else-if=\"item.componentName === 'innerLikeArea'\"></inner-like>\r\n        <view\r\n            @tap=\"showJobDetail\"\r\n            :class=\"'regular-item ' + (isRank && index < 4 ? 'mb44' : '') + ' ptp_exposure'\"\r\n            :data-algorithmStrategyId=\"item.algorithmStrategyId\"\r\n            :data-businessId=\"item.partJobId\"\r\n            data-businessType=\"1\"\r\n            :data-dataSource=\"item.dataSource\"\r\n            :data-distance=\"item.distance\"\r\n            :data-index=\"index\"\r\n            :data-keywords=\"keywords\"\r\n            :data-listTag=\"1\"\r\n            :data-partjobid=\"item.partJobId\"\r\n            :data-ptpid=\"ptpId\"\r\n            :data-remark=\"remark ? 'tagType_' + recommendLabelType + '&' + remark : recommendLabelType\"\r\n            :data-sourceid=\"item.sourceId\"\r\n            v-else\r\n        >\r\n            <block v-if=\"item.descArea.labelName && !isRank\">\r\n                <view class=\"regular-gradient\" :style=\"'background:linear-gradient(314deg, #FFFFFF 0%, #FFFFFF 40% ,' + item.descArea.background + ' 100%)'\"></view>\r\n                <view\r\n                    @tap.stop.prevent=\"_universalJump\"\r\n                    class=\"regular-desc ellipsis ptp_exposure\"\r\n                    :data-businessId=\"item.partJobId\"\r\n                    data-businessType=\"0\"\r\n                    :data-item=\"item.descArea\"\r\n                    data-ptpid=\"8m72-mk82-ss34-dea1\"\r\n                    :data-remark=\"'param_' + item.descArea.param + '&jumpKey_' + item.descArea.jumpKey\"\r\n                    :id=\"'pid=8m72-mk82-ss34-dea1|bid=' + item.partJobId + '|bt=0|r=param_' + item.descArea.param + '&jumpKey_' + item.descArea.jumpKey\"\r\n                >\r\n                    <image :src=\"item.descArea.icon\"></image>\r\n                    <view class=\"ellipsis\">{{ item.descArea.labelName }}</view>\r\n                </view>\r\n            </block>\r\n            <view class=\"regular-ranking\" v-if=\"isRank && index <= 4\">\r\n                <view class=\"regular-num\">{{ index + 1 }}</view>\r\n                <view class=\"regular-num-des\" v-if=\"index <= 2\">{{ index === 0 ? '本周最热' : 'Top' + index + 1 }}</view>\r\n            </view>\r\n            <view class=\"regular-title-box\">\r\n                <view class=\"regular-title ellipsis\">{{ item.title }}</view>\r\n                <view class=\"regular-salary\" :style=\"{color: t('color2')}\">\r\n                    {{ item.salaryDescCount }}\r\n                    <text v-if=\"item.unit\" :style=\"{color: t('color2')}\">{{ '/' + item.unit }}</text>\r\n                </view>\r\n            </view>\r\n            <view class=\"regular-tag-list\" v-if=\"item.labels && item.labels.length && item.jobLineType !== 2\">\r\n                <view\r\n                    :class=\"'regular-tag-item ' + (item.specialIcon ? 'special' : 'normal')\"\r\n                    :style=\"item.specialIcon ? 'background-image:url(' + item.specialIcon + ')' : ''\"\r\n                    v-for=\"(item, index) in item.labels\"\r\n                    :key=\"item.labelName\"\r\n                >\r\n                    <view class=\"regular-tag-content\">{{ item.labelName }}</view>\r\n                </view>\r\n            </view>\r\n            <block v-if=\"item.jobLineType === 2\">\r\n                <view class=\"regular-address ellipsis\" v-if=\"item.gateLabel\">\r\n                    <image class=\"icon_jl icon_gate\" :src=\"item.gateLabel.icon\"></image>\r\n                    <view class=\"regular-disdance\">{{ item.gateLabel.lines }}</view>\r\n                    <view class=\"regular-addText ellipsis\">| 距{{ item.gateLabel.station_name }}站{{ item.gateLabel.distance }}米</view>\r\n                </view>\r\n                <view class=\"regular-address ellipsis\" v-else>\r\n                    <image class=\"icon_jl\" src=\"https://qiniu-image.qtshe.com/newLabel/icon_jl.png\"></image>\r\n                    <view class=\"regular-disdance\" v-if=\"item.distance\">{{ item.distance }}</view>\r\n                    <view class=\"regular-addText ellipsis\">{{ item.distance ? '|' : '' }} {{ item.addressDetail || '不限工作地点' }}</view>\r\n                </view>\r\n            </block>\r\n            <view class=\"regular-company\">\r\n                <view class=\"regular-company-inner\">\r\n                    <!-- <view class=\"regular-header\">\r\n                        <image class=\"icon_jl\" :src=\"item.companyLogo\" v-if=\"item.companyLogo\"></image>\r\n                        <view :class=\"'regular-word ' + (item.companyType.key === '2' ? 'orange' : '')\" v-else>{{ item.newCompanyNameCharAt }}</view>\r\n                    </view>\r\n                    <view class=\"regular-company-name ellipsis\">{{ item.newCompanyName }}</view>\r\n                    <image class=\"icon_corner\" src=\"https://qiniu-image.qtshe.com/newLabel/icon_qy.png\" v-if=\"item.companyType.key === '1'\"></image>\r\n                    <image class=\"icon_corner\" src=\"https://qiniu-image.qtshe.com/newLabel/icon_gr.png\" v-else></image> -->\r\n                </view>\r\n                <view class=\"regular-join\" :style=\"{background: t('color1'), opacity: 0.8, color: '#FFFFFF'}\" v-if=\"!(isRank && index <= 2)\">立即报名</view>\r\n            </view>\r\n            <view class=\"regular-status\" v-if=\"isRank && index <= 2\">\r\n                <view class=\"regular-browse\" :style=\"item.labelRankingAttachParams.icon ? 'background-image:url(' + item.labelRankingAttachParams.icon + ')' : ''\">\r\n                    {{ item.labelRankingAttachParams.desc || '' }}\r\n                </view>\r\n                <image class=\"regular-btn\" src=\"https://qiniu-image.qtshe.com/newLabel/icon_bm1.png\" v-if=\"!item.applyStatus\"></image>\r\n                <image class=\"regular-btn\" src=\"https://qiniu-image.qtshe.com/newLabel/icon_ybm1.png\" v-else></image>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\ngetApp();\r\n\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            item: {},\r\n            channel: '',\r\n            recommendLabelType: ''\r\n        };\r\n    },\r\n    props: {\r\n        data: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        ptpId: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        type: {\r\n            type: Number,\r\n            default: 0\r\n        },\r\n        index: {\r\n            type: Number,\r\n            default: 0\r\n        },\r\n        dotShow: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        isJobList: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        className: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        listIndex: {\r\n            type: Number,\r\n            default: 0\r\n        },\r\n        needSelfClick: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        keywords: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        noMarginBottom: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        lazyload: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        page: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        isRank: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        remark: {\r\n            type: String,\r\n            default: ''\r\n        }\r\n    },\r\n    methods: {\r\n        _universalJump: function (a) {},\r\n        init: function (e) {\r\n            var a = '';\r\n\r\n            if ((e = JSON.parse(JSON.stringify(e))).labelList && e.labelList.descLabels && e.labelList.descLabels.length) {\r\n                e.labels = e.labelList.descLabels.map(function (e) {\r\n                    if (e.labelStyle) {\r\n                        e.labelStyle = JSON.parse(e.labelStyle || '{}');\r\n                    }\r\n\r\n                    return e;\r\n                });\r\n            }\r\n\r\n            if (e.companyType && '2' === e.companyType.key) {\r\n                e.newCompanyName = e.companyName || '';\r\n                e.newCompanyNameCharAt = e.newCompanyName.charAt(0);\r\n            } else {\r\n                e.newCompanyName = e.brandName || e.companyName || '';\r\n            }\r\n\r\n            e.newCompanyNameCharAt = e.newCompanyName.charAt(0);\r\n            e.descArea = {};\r\n\r\n            if (2 === e.listStyle) {\r\n                e.descArea = {\r\n                    icon: 'https://qiniu-image.qtshe.com/newLabel/icon_face.png',\r\n                    labelName: e.newJobIntroduction || '',\r\n                    background: '#FFF2E5'\r\n                };\r\n            } else {\r\n                if (3 === e.listStyle) {\r\n                    e.descArea = {\r\n                        icon: 'https://qiniu-image.qtshe.com/newLabel/class.png',\r\n                        labelName: e.newJobIntroduction || '',\r\n                        background: '#E5FCFF'\r\n                    };\r\n                }\r\n            }\r\n\r\n            if (e.labelRecommendNewList) {\r\n                var t = e.labelRecommendNewList;\r\n                var l = t.subwayLabels;\r\n                var n = void 0 === l ? [] : l;\r\n                var s = t.rankingLabels;\r\n                var i = void 0 === s ? [] : s;\r\n                if (n.length) {\r\n                    var r = {};\r\n\r\n                    try {\r\n                        r = JSON.parse(n[0].tagParams || '{}');\r\n                    } catch (e) {}\r\n\r\n                    r.icon = (n[0] && n[0].icon) || '';\r\n                    r.lines = r.lines && r.lines.replace(new RegExp(',', 'g'), '、');\r\n                    e.gateLabel = r;\r\n                    a = 'gateway';\r\n                }\r\n\r\n                if (i.length && !e.descArea.labelName) {\r\n                    var o = i[0];\r\n                    var p = {};\r\n                    try {\r\n                        p = JSON.parse(o.tagParams || '{}');\r\n                    } catch (e) {}\r\n\r\n                    if (1 !== o.tagType) {\r\n                        var c = {\r\n                            jumpKey: o.jumpKey,\r\n                            param: o.param,\r\n                            background: o.backgroundColor || '#FFF2E5',\r\n                            icon: o.icon || ''\r\n                        };\r\n\r\n                        if (2 === o.tagType && p.schoolName) {\r\n                            c.labelName = '入选「' + p.schoolName + '校友热推榜」第' + p.ranking + '名';\r\n                        } else {\r\n                            if (3 === o.tagType && p.business_area) {\r\n                                c.labelName = '入选「' + p.business_area + '热推榜」第' + p.job_rnk + '名';\r\n                            } else {\r\n                                if (4 === o.tagType && p.label_name) {\r\n                                    c.labelName = '入选「' + p.label_name + '热推榜」第' + p.job_rnk + '名';\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        e.descArea = c;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (e.specialLabel) {\r\n                if (e.labels) {\r\n                    e.labels.unshift(e.specialLabel);\r\n                } else {\r\n                    e.labels = [e.specialLabel];\r\n                }\r\n            }\r\n\r\n            if (e.salary && e.salary.indexOf('/') > -1) {\r\n                e.salaryDescCount = e.salary.split('/')[0];\r\n                e.unit = e.salary.split('/')[1];\r\n            } else {\r\n                e.salaryDescCount = e.salary;\r\n            }\r\n\t\t\tthis.item = e;\r\n\t\t\tthis.recommendLabelType = a;\r\n        },\r\n        jumpBannerLink: function (a) {},\r\n        handleTapFresh: function () {},\r\n        classTap: function (e) {},\r\n        showJobDetail: function (e) {\r\n            const partJobId = e.currentTarget.dataset.partjobid;\r\n            if (partJobId) {\r\n                uni.navigateTo({\r\n                    url: `/zhaopin/partdetails?id=${partJobId}`\r\n                });\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        data: {\r\n            handler: function (e) {\r\n                this.init(e);\r\n            },\r\n\r\n            immediate: true,\r\n            deep: true\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t@import './index.scss';\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=413743fd&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=413743fd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115070532\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}