{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?c8eb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?c78b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?4abe", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?da3d", "uni-app:///pages/address/addressadd.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?e1cc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pages/address/addressadd.vue?5867"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "name", "tel", "area", "address", "longitude", "latitude", "regiondata", "type", "addressxx", "company", "items", "showCompany", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "onPullDownRefresh", "methods", "getdata", "id", "regionchange", "console", "<PERSON><PERSON><PERSON><PERSON>", "fail", "formSubmit", "addressid", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "bindPickerChange", "setaddressxx", "shibie", "isrs", "getweixinaddress"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmDnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;;EAGAC;IACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACAnB;QACAqB;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;;IAGA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAT;QACA;UACAM;QACA;MACA;MACA;QACAA;QACAN;UAAAU;UAAAhB;QAAA;UACAY;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACA;UACA;YACA;UACA;UACAA;UACAA;QACA;MACA;QACAA;MACA;IACA;IACAK;MACA;MACAC;MACA;IACA;IACAC;MACAD;MACA;MACAV;QACAG;UACAO;UACAN;UACAA;UACAA;UACAA;QACA;QACAQ;UACAF;UACA;YACA;YACAZ;cACAE;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;;MAGA;QACA;QACA;UACAf;UACA;QACA;MACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;MACA;;MAGA;QACAA;QACA;MACA;MACAA;MACAA;QAAAN;QAAAsB;QAAA7B;QAAAC;QAAAC;QAAAC;QAAAE;QAAAD;QAAAK;MAAA;QACAI;QACA;UACAA;UACA;QACA;QACAA;QACAiB;UACAjB;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAlB;QACAA;QACAA;UAAAgB;QAAA;UACAhB;UACAA;UACAiB;YACAjB;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACArB;QAAAL;MAAA;QACA;QACA;UACA2B;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAN;QACA;UACAA;QACA;MACA;IACA;IACAuB;MACA;MACA9C;QACA4B;UACAL;UACAA;YAAAN;YAAAsB;YAAA7B;YAAAC;YAAAC;YAAAC;UAAA;YACAU;YACA;cACAA;cACA;YACA;YACAA;YACAiB;cACAjB;YACA;UACA;QACA;MACA;IACA;EACA;;;;;;;;;;;;;;;AC9QA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/addressadd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/addressadd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addressadd.vue?vue&type=template&id=4af01161&\"\nvar renderjs\nimport script from \"./addressadd.vue?vue&type=script&lang=js&\"\nexport * from \"./addressadd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addressadd.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/addressadd.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addressadd.vue?vue&type=template&id=4af01161&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.getplatform() == \"wx\" && _vm.type != 1 : null\n  var m1 = _vm.isload ? _vm.t(\"详细地址\") : null\n  var m2 = _vm.isload ? _vm.t(\"详细地址\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  var m4 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addressadd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addressadd.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"addfromwx\" v-if=\"getplatform() == 'wx' && type!=1\" @tap=\"getweixinaddress\"><image src=\"/static/img/weixin.png\" class=\"img\"/> 获取微信收货地址 <view class=\"flex1\"></view><image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx;\"/></view>\r\n\t\t<form @submit=\"formSubmit\">\n\t\t\t<view class=\"form\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">姓 名</text>\r\n\t\t\t\t\t<input class=\"input\" type=\"text\" placeholder=\"请输入姓名\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"name\" :value=\"name\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"showCompany\">\r\n\t\t\t\t\t<text class=\"label\">公 司</text>\r\n\t\t\t\t\t<input class=\"input\" type=\"text\" placeholder=\"请输入公司或单位名称\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"company\" :value=\"company\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">手机号</text>\r\n\t\t\t\t\t<input class=\"input\" type=\"number\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tel\" :value=\"tel\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"type==1\">\r\n\t\t\t\t\t<text class=\"label flex0\">选择位置</text>\r\n\t\t\t\t\t<text class=\"flex1\" style=\"text-align:right\" :style=\"area ? '' : 'color:#BBBBBB'\" @tap=\"selectzuobiao\" >{{area ? area : '请选择您的位置'}}</text>\r\n\t\t\t\t\t<!-- <input class=\"input\" type=\"text\" placeholder=\"请选择您的位置\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"area\" :value=\"area\" @tap=\"selectzuobiao\"></input> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" v-else>\r\n\t\t\t\t\t<text class=\"label flex1\">所在地区</text>\n\t\t\t\t\t<uni-data-picker :localdata=\"items\" :border=\"false\" :placeholder=\"regiondata || '请选择省市区'\" @change=\"regionchange\"></uni-data-picker>\r\n\t\t\t\t\t<!-- <picker mode=\"region\" name=\"regiondata\" :value=\"regiondata\" class=\"input\" @change=\"bindPickerChange\">\r\n\t\t\t\t\t\t<view class=\"picker\" v-if=\"regiondata\">{{regiondata}}</view>\r\n\t\t\t\t\t\t<view v-else>请选择地区</view>\r\n\t\t\t\t\t</picker> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">{{t('详细地址')}}</text>\r\n\t\t\t\t\t<input class=\"input\" type=\"text\" :placeholder=\"'请输入'+t('详细地址')\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"address\" :value=\"address\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-y-center\" v-if=\"type!=1\">\r\n\t\t\t\t\t<view class=\"f2 flex-y-center flex1\">\r\n\t\t\t\t\t\t<input id=\"addressxx\" placeholder=\"粘贴地址信息，可自动识别并填写，如：张三，188********，广东省 东莞市 xx区 xx街道 xxxx\" placeholder-style=\"font-size:24rpx;color:#BBBBBB\" style=\"width:85%;font-size:24rpx;margin:20rpx 0;height:100rpx;padding:4rpx 10rpx\" @input=\"setaddressxx\"></input>\r\n\t\t\t\t\t\t<view style=\"width:15%;text-align:center;color:#999\" @tap=\"shibie\">识别</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">保 存</button>\r\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\r\n<script>\r\nvar app = getApp();\r\r\nexport default {\n  data() {\n    return {\n\t  opt:{},\n\t  loading:false,\n\t  isload: false,\n\t  menuindex:-1,\n      name: '',\n      tel: '',\n      area: '',\n      address: '',\n      longitude: '',\n      latitude: '',\n      regiondata: '',\n      type: 0,\n      addressxx: '',\n      company: '',\n\t\t\titems:[],\r\n\t\t\tshowCompany:false,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    this.type = this.opt.type || 0;\n\t\tvar that = this;\r\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\tif(customs.data.includes('plug_zhiming')) {\r\n\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\r\n\t\t\t}\r\n\t\t\tuni.request({\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: {},\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\n\t\t\n    this.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar addressId = that.opt.id || '';\r\n\t\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\t\tif(customs.data.includes('plug_xiongmao')) {\r\n\t\t\t\t\tthat.showCompany = true;\r\n\t\t\t\t}\r\n\t\t\t});\n\t\t\tif(addressId) {\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.get('ApiAddress/addressadd', {id: addressId,type: that.type}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tthat.name = res.data.name;\n\t\t\t\t\tthat.tel = res.data.tel;\n\t\t\t\t\tthat.area = res.data.area;\n\t\t\t\t\tthat.address = res.data.address;\n\t\t\t\t\tthat.longitude = res.data.longitude;\n\t\t\t\t\tthat.latitude = res.data.latitude;\r\n\t\t\t\t\tthat.company = res.data.company;\n\t\t\t\t\tif (res.data.province){\n\t\t\t\t\t\tvar regiondata = res.data.province+ ',' + res.data.city+ ',' + res.data.district;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar regiondata = '';\n\t\t\t\t\t}\n\t\t\t\t\tthat.regiondata = regiondata\n\t\t\t\t\tthat.loaded();\n\t\t\t\t});\n\t\t\t}else{\n\t\t\t\tthat.loaded();\n\t\t\t}\n\t\t},\n\t\tregionchange(e) {\n\t\t\tconst value = e.detail.value\n\t\t\tconsole.log(value[0].text + ',' + value[1].text + ',' + value[2].text);\n\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text\n\t\t},\n    selectzuobiao: function () {\n\t\t\tconsole.log('selectzuobiao')\n      var that = this;\n      uni.chooseLocation({\n        success: function (res) {\n          console.log(res);\n          that.area = res.address;\n          that.address = res.name;\n          that.latitude = res.latitude;\n          that.longitude = res.longitude;\n        },\n        fail: function (res) {\n\t\t\t\t\tconsole.log(res)\n          if (res.errMsg == 'chooseLocation:fail auth deny') {\n            //$.error('获取位置失败，请在设置中开启位置信息');\n            app.confirm('获取位置失败，请在设置中开启位置信息', function () {\n              uni.openSetting({});\n            });\n          }\n        }\n      });\n    },\n    formSubmit: function (e) {\n      var that = this;\n      var formdata = e.detail.value;\n      var addressId = that.opt.id || '';\n      var name = formdata.name;\n      var tel = formdata.tel;\n      var regiondata = that.regiondata;\n\n      if (that.type == 1) {\n        var area = that.area;\r\n\t\t\t\tif(area == '') {\r\n\t\t\t\t\tapp.error('请选择位置');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\n      } else {\n        var area = regiondata;\r\n\t\t\t\tif(area == '') {\r\n\t\t\t\t\tapp.error('请选择省市区');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\n      }\n      var address = formdata.address;\n      var longitude = that.longitude;\n      var latitude = that.latitude;\n      var company = formdata.company;\n\n      if (name == '' || tel == '' || address == '') {\n        app.error('请填写完整信息');\n        return;\n      }\n\t\t\tapp.showLoading('提交中');\n      app.post('ApiAddress/addressadd', {type: that.type,addressid: addressId,name: name,tel: tel,area: area,address: address,latitude: latitude,longitude: longitude,company:company}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 0) {\n          app.alert(res.msg);\n          return;\n        }\n        app.success('保存成功');\n        setTimeout(function () {\n          app.goback(true);\n        }, 1000);\n      });\n    },\n    delAddress: function () {\n      var that = this;\n      var addressId = that.opt.id;\n      app.confirm('确定要删除该收获地址吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiAddress/del', {addressid: addressId}, function () {\n\t\t\t\t\tapp.showLoading(false);\n          app.success('删除成功');\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    bindPickerChange: function (e) {\n      var val = e.detail.value;\n      this.regiondata = val;\n    },\n    setaddressxx: function (e) {\n      this.addressxx = e.detail.value;\n    },\n    shibie: function () {\n      var that = this;\n      var addressxx = that.addressxx;\n      app.post('ApiAddress/shibie', {addressxx: addressxx}, function (res) {\n        var isrs = 0;\n        if (res.province) {\n          isrs = 1;\n          that.regiondata = res.province + ',' +res.city + ',' +res.county\n        }\n        if (res.detail) {\n          isrs = 1;\n          that.address = res.detail\n        }\n        if (res.person) {\n          isrs = 1;\n          that.name = res.person\n        }\n        if (res.phonenum) {\n          isrs = 1;\n          that.tel = res.phonenum\n        }\n        if (isrs == 0) {\n          app.error('识别失败');\n        } else {\n          app.success('识别完成');\n        }\n      });\n    },\n\t\tgetweixinaddress:function(){\n      var that = this;\n\t\t\twx.chooseAddress({\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tapp.showLoading('提交中');\n\t\t\t\t\tapp.post('ApiAddress/addressadd', {type: that.type,addressid: '',name: res.userName,tel: res.telNumber,area: res.provinceName+','+res.cityName+','+res.countyName,address: res.detailInfo}, function (res) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tapp.success('添加成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goback(true);\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t})\n\t\t}\n  }\n};\r\n</script>\r\n<style>\r\n.container{display:flex;flex-direction:column}\n\n.addfromwx{width:94%;margin:20rpx 3% 0 3%;border-radius:5px;padding:20rpx 3%;background: #FFF;display:flex;align-items:center;color:#666;font-size:28rpx;}\n.addfromwx .img{width:40rpx;height:40rpx;margin-right:20rpx;}\n.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding: 0 3%;background: #FFF;}\n.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;}\n.form-item:last-child{border:0}\r\n.form-item .label{ color:#8B8B8B;font-weight:bold;height: 60rpx; line-height: 60rpx; text-align:left;width:160rpx;padding-right:20rpx}\r\n.form-item .input{ flex:1;height: 60rpx; line-height: 60rpx;text-align:right}\r\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\r\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addressadd.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./addressadd.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115014057\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}