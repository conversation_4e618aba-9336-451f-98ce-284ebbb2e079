{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?2f2f", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?a0cc", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?bba4", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?7d5e", "uni-app:///restaurant/queue/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?a458", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/queue/index.vue?8aef"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "business", "clist", "myqueue", "myjust<PERSON>ue", "notice", "bid", "socketOpen", "token", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "url", "console", "socketMsgQueue", "type", "aid", "intervel", "sendSocketMessage", "receiveMessage", "cancel", "id", "queue_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8D9wB;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAV;MAAA;QACAS;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACA;UACA;UACAE;UACAA;YACAC;UACA;UACAD;YACAE;YACAJ;YACA;cACAA;YACA;YACAK;UACA;UACAL;YAAAM;YAAAb;YAAAZ;cAAA0B;cAAAhB;YAAA;UAAA;UACAiB;YACAR;cAAAM;cAAAb;YAAA;UACA;UAEAS;YACAE;YACA;cACA;cACAJ;YACA;UACA;QACA;MACA;IACA;IACAS;MACA;QACAL;QACAF;UACArB;QACA;MACA;QACAuB;QACAC;MACA;IACA;IACAK;MACAN;MACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACA;MACAV;QACAA;QACAA;UAAAW;QAAA;UACAX;UACAA;YACAD;UACA;UACA;YACAA;cAAAM;cAAAb;cAAAZ;gBAAA0B;gBAAAhB;gBAAAsB;cAAA;YAAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/KA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/queue/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/queue/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3b311ee0&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/queue/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3b311ee0&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.myqueue\n      ? _vm.dateFormat(_vm.myqueue.create_time, \"H:i:s\")\n      : null\n  var m1 =\n    _vm.isload && _vm.myjustqueue\n      ? _vm.dateFormat(_vm.myjustqueue.create_time, \"H:i:s\")\n      : null\n  var m2 =\n    _vm.isload && _vm.myjustqueue\n      ? _vm.dateFormat(_vm.myjustqueue.call_time, \"H:i:s\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topbannerbg\" :style=\"business.pic?'background:url('+business.pic+') 100%':''\"></view>\r\n\t\t<view class=\"topbannerbg2\"></view>\r\n\t\t<view class=\"topbanner\">\r\n\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\r\n\t\t\t<view class=\"right\">\r\n\t\t\t\t<view class=\"f1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\r\n\t\t\t\t<!-- <view class=\"f3\"><view class=\"flex1\"></view><view class=\"t2\">收藏<image class=\"img\" src=\"/static/img/like1.png\"/></view></view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"notice\">\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<image src=\"/static/img/queue-notice.png\" class=\"f1\"/>\r\n\t\t\t\t<view class=\"f2\">{{notice}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"clist\">\r\n\t\t\t<view class=\"title\">当前排队</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"item\" v-for=\"item in clist\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{item.seat_min}}-{{item.seat_max}}人</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">等待<text style=\"color:#FC5729;padding:0 6rpx;font-size:28rpx\">{{item.waitnum}}</text>桌</view>\r\n\t\t\t\t\t<view class=\"f3\"><text style=\"color:#FC5729;padding:0 6rpx;font-size:28rpx\">{{item.need_minute}}</text>分钟</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"myqueue\" v-if=\"myqueue\">\r\n\t\t\t<view class=\"title\"><text class=\"t1\">我的排队</text><text class=\"t2\" @tap=\"cancel\" :data-id=\"myqueue.id\" :data-queue_no=\"myqueue.queue_no\">取消排队</text></view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"f1\">{{myqueue.queue_no}}</view>\r\n\t\t\t\t<view class=\"f2\">{{myqueue.cname}}</view>\r\n\t\t\t\t<view class=\"f2\">取号时间：{{dateFormat(myqueue.create_time,'H:i:s')}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"bottom\">\r\n\t\t\t\t<view class=\"f1\">前面还有<text style=\"color:#FC5729;padding:0 6rpx;\">{{myqueue.beforenum}}</text>人等待中</view>\r\n\t\t\t\t<view class=\"f2\">预计等待<text style=\"color:#FC5729;padding:0 6rpx;\">{{myqueue.need_minute}}</text>分钟</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"myqueue\" v-if=\"myjustqueue\">\r\n\t\t\t<view class=\"title\"><text class=\"t1\">我的排队</text><text class=\"t2\">已叫号 请前往用餐</text></view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"f1\">{{myjustqueue.queue_no}}</view>\r\n\t\t\t\t<view class=\"f2\">{{myjustqueue.cname}}</view>\r\n\t\t\t\t<view class=\"f2\">取号时间：{{dateFormat(myjustqueue.create_time,'H:i:s')}}</view>\r\n\t\t\t\t<view class=\"f2\">叫号时间：{{dateFormat(myjustqueue.call_time,'H:i:s')}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" v-if=\"!myqueue\" @tap=\"goto\" :data-url=\"'quhao?bid='+bid\">取号排队</view>\r\n\t\t<view class=\"log\" v-if=\"!myqueue\" @tap=\"goto\" :data-url=\"'record?bid='+bid\">我的排队记录</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar intervel;\r\nvar socketMsgQueue = [];\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tbusiness:{},\r\n      clist: [],\r\n\t\t\tmyqueue:'',\r\n\t\t\tmyjustqueue:'',\r\n\t\t\tnotice:'',\r\n\t\t\tbid:'',\r\n\t\t\tsocketOpen:false,\r\n\t\t\ttoken:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid || 0;\r\n\t\tthis.getdata();\r\n  },\r\n  onUnload: function () {\r\n    clearInterval(intervel);\r\n    if(this.socketOpen) uni.closeSocket();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiRestaurantQueue/index', {bid:that.opt.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.business = res.business\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.notice = res.notice;\r\n\t\t\t\tthat.myqueue = res.myqueue;\r\n\t\t\t\tthat.myjustqueue = res.myjustqueue;\r\n\t\t\t\tthat.token = res.token;\r\n\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\tif(!that.socketOpen){\r\n\t\t\t\t\tvar pre_url = app.globalData.pre_url;\r\n\t\t\t\t\tvar wssurl = pre_url.replace('https://', \"wss://\") + '/wss';\r\n\t\t\t\t\tuni.closeSocket();\r\n\t\t\t\t\tuni.connectSocket({\r\n\t\t\t\t\t\turl: wssurl\r\n\t\t\t\t\t});\r\n\t\t\t\t\tuni.onSocketOpen(function (res) {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.socketOpen = true;\r\n\t\t\t\t\t\tfor (var i = 0; i < socketMsgQueue.length; i++) {\r\n\t\t\t\t\t\t\tthat.sendSocketMessage(socketMsgQueue[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tsocketMsgQueue = [];\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.sendSocketMessage({type: 'restaurant_queue',token: that.token,data:{ aid:app.globalData.aid,bid:that.bid }});\r\n\t\t\t\t\tintervel = setInterval(function () {\r\n\t\t\t\t\t\tthat.sendSocketMessage({type: 'connect',token: that.token});\r\n\t\t\t\t\t}, 25000);\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.onSocketMessage(function (res) {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\t\t\tthat.receiveMessage(data);\r\n\t\t\t\t\t\t} catch (e) {}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsendSocketMessage:function(msg) {\r\n\t\t\tif (this.socketOpen) {\r\n\t\t\t\tconsole.log(msg)\r\n\t\t\t\tuni.sendSocketMessage({\r\n\t\t\t\t\tdata: JSON.stringify(msg)\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('111')\r\n\t\t\t\tsocketMsgQueue.push(msg);\r\n\t\t\t}\r\n\t\t},\r\n    receiveMessage: function (data) {\r\n\t\t\tconsole.log(data);\r\n\t\t\tif(data.type == 'restaurant_queue_add' || data.type == 'restaurant_queue_callno' || data.type == 'restaurant_queue_cancel'){\r\n\t\t\t\tthis.getdata();\r\n\t\t\t}\r\n    },\r\n\t\tcancel:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar queue_no = e.currentTarget.dataset.queu_no;\r\n\t\t\tapp.confirm('确定要取消排队吗?',function(){\r\n\t\t\t\tapp.showLoading('取消中');\r\n\t\t\t\tapp.get('ApiRestaurantQueue/cancel', {id:id}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tthat.sendSocketMessage({type: 'restaurant_queue_cancel',token: that.token,data:{ aid:app.globalData.aid,bid:that.bid,queue_id:id }});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{height:100%;overflow:hidden;position: relative;}\r\n\r\n.topbannerbg{width:100%;height:280rpx;background:#fff;}\r\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:280rpx;background:rgba(0,0,0,0.7);top:0}\r\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:60rpx;top:0;align-items:center}\r\n.topbanner .left{width:100rpx;height:100rpx;flex-shrink:0;margin-right:20rpx;display:none}\r\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\r\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\r\n.topbanner .right .f1{font-size:32rpx;font-weight:bold;color:#fff}\r\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\r\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\r\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\r\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\r\n\r\n.notice{display:flex;width:94%;margin:0 3%;height:120rpx;background: #fff;position:absolute;z-index:11;padding:0 50rpx;border-radius:10rpx;margin-top:-70rpx;}\r\n.notice .content{display:flex;width:100%;align-items:center}\r\n.notice .content .f1{width:40rpx;height:40rpx;margin-right:20rpx}\r\n.notice .content .f2{flex:1;color:#FC5729;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n\r\n.clist{display:flex;flex-direction:column;width:94%;margin:0 3%;margin-top:70rpx;background: #fff;padding:30rpx 38rpx;border-radius:10rpx;}\r\n.clist .title{color:#161616;font-weight:bold}\r\n.clist .content{display:flex;flex-direction:column;margin:20rpx 0}\r\n.clist .content .item{background:#FFF6F3;border-radius:10rpx;display:flex;align-items:center;width:100%;height:110rpx;margin-bottom:16rpx;padding:0 40rpx}\r\n.clist .content .item .f1{display:flex;flex-direction:column;width:50%;padding-left:20rpx}\r\n.clist .content .item .f1 .t1{color:#222222}\r\n.clist .content .item .f1 .t2{color:#999999;font-size:20rpx}\r\n.clist .content .item .f2{display:flex;width:30%;align-items:center;font-size:24rpx}\r\n.clist .content .item .f3{display:flex;width:20%;align-items:center;font-size:24rpx;justify-content:flex-end}\r\n\r\n.myqueue{display:flex;flex-direction:column;width:94%;margin:0 3%;margin-top:20rpx;background: #fff;padding:30rpx 38rpx;border-radius:10rpx;}\r\n.myqueue .title{display:flex;align-items:center;justify-content:space-between}\r\n.myqueue .title .t1{color:#161616;font-weight:bold}\r\n.myqueue .title .t2{color:#FC5729;font-size:24rpx}\r\n.myqueue .content{display:flex;flex-direction:column;align-items:center;width:100%;margin:40rpx 0}\r\n.myqueue .content .f1{font-size:64rpx;font-weight:bold;color:#564B4B}\r\n.myqueue .content .f2{font-size:24rpx;color:#A29E9E;margin-top:10rpx}\r\n.myqueue .bottom{display:flex;align-items:center;width:100%;color:#564B4B;justify-content:space-between}\r\n\r\n.btn{width:94%;margin:0 3%;margin-top:40rpx;height:90rpx;line-height:90rpx;text-align:center;background: linear-gradient(90deg, #FF7D15 0%, #FC5729 100%);color:#fff;font-size:32rpx;font-weight:bold;border-radius:10rpx}\r\n.log{width:100%;text-align:center;margin-top:30rpx;margin-bottom:40rpx;color:#888}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115066009\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}