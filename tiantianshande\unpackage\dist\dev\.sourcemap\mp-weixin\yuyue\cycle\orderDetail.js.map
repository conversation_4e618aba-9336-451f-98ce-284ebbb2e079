{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?ab78", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?89dc", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?30c9", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?fd5c", "uni-app:///yuyue/cycle/orderDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?83de", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderDetail.vue?ce31"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "info", "stageList", "onLoad", "onShow", "onPullDownRefresh", "methods", "getdata", "app", "that", "id", "formatTime", "month", "day", "hour", "minute", "toggle<PERSON><PERSON>e", "setTimeout", "cancelOrder", "copy"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwIpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;UAAAA;QAAA;QACA;MACA;MACAC;MACAD;QAAAE;MAAA;QACAD;QACA;UACAD;YAAAA;UAAA;UACA;QACA;QACAC;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEAC;MACAC;MACAC;MACAC;MAEA;IACA;IACAC;MACA;MACA;MACA;MACAR;QACAA;QACAA;UAAAE;QAAA;UACAF;UACA;YACAA;YACAS;cACAR;YACA;UACA;YACAD;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;MACAV;QACAA;QACAA;UAAAE;QAAA;UACAF;UACA;YACAA;YACAS;cACAR;YACA;UACA;YACAD;UACA;QACA;MACA;IACA;IACAW;MACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrPA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/cycle/orderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/cycle/orderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=10b5c9d4&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/cycle/orderDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=template&id=10b5c9d4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.stageList.length : null\n  var l0 = _vm.isload ? _vm.stageList.slice(0, 3) : null\n  var g1 = _vm.isload ? _vm.stageList.length : null\n  var m0 =\n    _vm.isload && _vm.info.createtime\n      ? _vm.formatTime(_vm.info.createtime)\n      : null\n  var m1 =\n    _vm.isload && _vm.info.pay_time ? _vm.formatTime(_vm.info.pay_time) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 订单状态 -->\n\t\t<view class=\"order-status-block\" :class=\"'status-bg-' + info.status\">\n\t\t\t<view class=\"status-text\">{{info.status_text}}</view>\n\t\t\t<view class=\"status-desc\" v-if=\"info.status == 1\">下次服务日期：{{info.next_service_date || '暂无安排'}}</view>\n\t\t\t<view class=\"status-desc\" v-else-if=\"info.status == 2\">服务已暂停，可随时恢复</view>\n\t\t\t<view class=\"status-desc\" v-else-if=\"info.status == 3\">您的周期服务已全部完成</view>\n\t\t\t<view class=\"status-desc\" v-else-if=\"info.status == 4\">服务已取消</view>\n\t\t</view>\n\n\t\t<!-- 服务地址 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-title\">\n\t\t\t\t<text>服务地址</text>\n\t\t\t</view>\n\t\t\t<view class=\"address-content\">\n\t\t\t\t<view class=\"user-info\">{{info.linkman}} {{info.tel}}</view>\n\t\t\t\t<view class=\"address-text\">{{info.address}}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 服务信息 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-title\">\n\t\t\t\t<text>服务信息</text>\n\t\t\t\t<view class=\"more-link\" @tap=\"goto\" :data-url=\"'/yuyue/cycle/productDetail?id='+info.product_id\">查看详情 ></view>\n\t\t\t</view>\n\t\t\t<view class=\"service-header\">\n\t\t\t\t<image class=\"service-image\" :src=\"info.product_pic\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"service-title\">{{info.product_name}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"info-list\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">单期价格</text>\n\t\t\t\t\t<text class=\"value price\">￥{{info.price}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">订单总价</text>\n\t\t\t\t\t<text class=\"value price\">￥{{info.total_price || (info.price * info.total_period)}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">总期数</text>\n\t\t\t\t\t<text class=\"value\">{{info.total_period}}期</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">已完成</text>\n\t\t\t\t\t<text class=\"value\">{{info.served_period}}期</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">服务周期</text>\n\t\t\t\t\t<text class=\"value\">{{info.period_type_text || '-'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">开始日期</text>\n\t\t\t\t\t<text class=\"value\">{{info.start_date || '-'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"info.end_date\">\n\t\t\t\t\t<text class=\"label\">结束日期</text>\n\t\t\t\t\t<text class=\"value\">{{info.end_date}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分期计划 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-title\">\n\t\t\t\t<text>服务计划</text>\n\t\t\t\t<view class=\"more-link\" v-if=\"stageList.length > 3\" @tap=\"goto\" :data-url=\"'/yuyue/cycle/planList?id=' + info.id\">查看全部</view>\n\t\t\t</view>\n\t\t\t<view class=\"stage-list\">\n\t\t\t\t<block v-for=\"(stage, index) in stageList.slice(0, 3)\" :key=\"index\">\n\t\t\t\t\t<view class=\"stage-item\">\n\t\t\t\t\t\t<view class=\"stage-header\">\n\t\t\t\t\t\t\t<view class=\"stage-number\">第 {{stage.period_num || (index+1)}} 期</view>\n\t\t\t\t\t\t\t<view class=\"stage-status\" :class=\"'status-' + stage.status\">{{stage.status_text}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stage-content\">\n\t\t\t\t\t\t\t<view class=\"stage-date\" v-if=\"stage.scheduled_date\">计划日期：{{stage.scheduled_date}}</view>\n\t\t\t\t\t\t\t<view class=\"stage-worker\" v-if=\"stage.stage_worker_name\">服务人员：{{stage.stage_worker_name}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<view class=\"no-data\" v-if=\"stageList.length === 0\">暂无服务计划</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单信息 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-title\">\n\t\t\t\t<text>订单信息</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-list\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">订单编号</text>\n\t\t\t\t\t<view class=\"value-group\">\n\t\t\t\t\t\t<text class=\"value order-num\">{{info.ordernum}}</text>\n\t\t\t\t\t\t<text class=\"copy-btn\" @tap=\"copy\" :data-text=\"info.ordernum\">复制</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"info.createtime\">\n\t\t\t\t\t<text class=\"label\">下单时间</text>\n\t\t\t\t\t<text class=\"value\">{{formatTime(info.createtime)}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"info.pay_status == 1\">\n\t\t\t\t\t<text class=\"label\">支付方式</text>\n\t\t\t\t\t<text class=\"value\">{{info.pay_type || '线上支付'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"info.pay_time\">\n\t\t\t\t\t<text class=\"label\">支付时间</text>\n\t\t\t\t\t<text class=\"value\">{{formatTime(info.pay_time)}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"info.remark\">\n\t\t\t\t\t<text class=\"label\">订单备注</text>\n\t\t\t\t\t<text class=\"value\">{{info.remark}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作按钮 -->\n\t\t<view class=\"bottom-bar\" v-if=\"info.status == 1 || info.status == 2\">\n\t\t\t<view class=\"btn\" :class=\"info.status == 1 ? 'btn-pause' : 'btn-resume'\" @tap.stop=\"togglePause\" :data-id=\"info.id\">\n\t\t\t\t{{info.status == 1 ? '暂停服务' : '恢复服务'}}\n\t\t\t</view>\n\t\t\t<view class=\"btn btn-cancel\" @tap.stop=\"cancelOrder\" :data-id=\"info.id\">取消服务</view>\n\t\t</view>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: true,\n\t\t\tisload: false,\n\t\t\tmenuindex: -1,\n\t\t\tinfo: {},       // 订单主信息\n\t\t\tstageList: [],  // 分期计划列表\n\t\t};\n\t},\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n\t},\n\tonShow: function() {\n\t\t// 每次打开页面都刷新数据\n\t\tthis.getdata(); \n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tmethods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tvar id = that.opt.id;\n\t\t\tif (!id) {\n\t\t\t\tapp.alert('缺少订单ID', function() { app.goback(); });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiPeriodicService/orderDetail', { id: id }, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg, function() { app.goback(); });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.info = res.info;\n\t\t\t\tthat.stageList = res.stageList || [];\n\t\t\t\tthat.isload = true;\n\t\t\t\t\n\t\t\t\t// 处理订单时间格式\n\t\t\t\tif (that.info.createtime && typeof that.info.createtime === 'number') {\n\t\t\t\t\tthat.info.createtime_text = that.formatTime(that.info.createtime);\n\t\t\t\t}\n\t\t\t\tif (that.info.pay_time && typeof that.info.pay_time === 'number') {\n\t\t\t\t\tthat.info.pay_time_text = that.formatTime(that.info.pay_time);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tformatTime: function(timestamp) {\n\t\t\tif (!timestamp) return '-';\n\t\t\tvar date = new Date(timestamp * 1000);\n\t\t\tvar year = date.getFullYear();\n\t\t\tvar month = date.getMonth() + 1;\n\t\t\tvar day = date.getDate();\n\t\t\tvar hour = date.getHours();\n\t\t\tvar minute = date.getMinutes();\n\t\t\t\n\t\t\tmonth = month < 10 ? '0' + month : month;\n\t\t\tday = day < 10 ? '0' + day : day;\n\t\t\thour = hour < 10 ? '0' + hour : hour;\n\t\t\tminute = minute < 10 ? '0' + minute : minute;\n\t\t\t\n\t\t\treturn year + '-' + month + '-' + day + ' ' + hour + ':' + minute;\n\t\t},\n\t\ttogglePause: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id;\n\t\t\tvar actionText = that.info.status == 1 ? '暂停' : '恢复';\n\t\t\tapp.confirm('确定要'+actionText+'该服务吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiPeriodicService/togglePauseOrder', {id: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status == 1){\n\t\t\t\t\t\tapp.success(actionText+'成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata(); // 刷新页面数据\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tcancelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要取消该周期服务吗? 未完成的服务将一并取消。', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiPeriodicService/cancelOrder', {id: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status == 1){\n\t\t\t\t\t\tapp.success('取消成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata(); // 刷新页面数据\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tcopy: function(e) {\n\t\t\tapp.copy(e.currentTarget.dataset.text);\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container { \n\tbackground-color: #f8f8f8; \n\tpadding-bottom: 120rpx; \n\tmin-height: 100vh;\n}\n\n/* 订单状态区块 */\n.order-status-block { \n\tpadding: 40rpx 30rpx; \n\tmargin-bottom: 20rpx; \n\ttext-align: center;\n\tposition: relative;\n\tcolor: #fff;\n}\n.status-bg-0 { background: linear-gradient(to right, #1890ff, #40a9ff); } /* 待激活 */\n.status-bg-1 { background: linear-gradient(to right, #FFB600, #FFC740); } /* 进行中 */\n.status-bg-2 { background: linear-gradient(to right, #FF8D00, #FFA940); } /* 已暂停 */\n.status-bg-3 { background: linear-gradient(to right, #06A051, #36C17C); } /* 已完成 */\n.status-bg-4 { background: linear-gradient(to right, #909399, #C0C4CC); } /* 已取消 */\n\n.order-status-block .status-text { \n\tfont-size: 36rpx; \n\tfont-weight: bold; \n\tmargin-bottom: 16rpx; \n}\n.order-status-block .status-desc { \n\tfont-size: 28rpx; \n\topacity: 0.9;\n}\n\n/* 详情卡片通用样式 */\n.detail-card {\n\twidth: 94%; \n\tmargin: 0 3% 20rpx; \n\tbackground: #fff; \n\tborder-radius: 16rpx; \n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.card-title {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 24rpx 30rpx;\n\tborder-bottom: 1px solid #f4f4f4;\n}\n\n.card-title text {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.more-link {\n\tfont-size: 24rpx;\n\tcolor: #2196f3;\n}\n\n/* 地址区块 */\n.address-content {\n\tpadding: 24rpx 30rpx;\n}\n.user-info { \n\tfont-size: 30rpx; \n\tcolor: #333; \n\tfont-weight: bold; \n\tmargin-bottom: 10rpx; \n}\n.address-text { \n\tfont-size: 28rpx; \n\tcolor: #666; \n\tline-height: 1.5; \n}\n\n/* 服务信息区块 */\n.service-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 24rpx 30rpx;\n\tborder-bottom: 1px solid #f4f4f4;\n}\n.service-image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 12rpx;\n\tmargin-right: 20rpx;\n\tbackground-color: #f7f7f7;\n}\n.service-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.info-list {\n\tpadding: 0 30rpx;\n}\n.info-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tfont-size: 28rpx;\n\tborder-bottom: 1px dashed #f4f4f4;\n}\n.info-item:last-child {\n\tborder-bottom: none;\n}\n.info-item .label {\n\tcolor: #999;\n}\n.info-item .value {\n\tcolor: #333;\n\ttext-align: right;\n}\n.info-item .price {\n\tcolor: #ff6b00;\n\tfont-weight: bold;\n}\n.value-group {\n\tdisplay: flex;\n\talign-items: center;\n}\n.order-num {\n\tmax-width: 360rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tmargin-right: 20rpx;\n}\n.copy-btn {\n\tcolor: #2196f3;\n\tfont-size: 24rpx;\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 30rpx;\n\tborder: 1px solid #2196f3;\n}\n\n/* 分期计划区块 */\n.stage-list {\n\tpadding: 10rpx 30rpx 30rpx;\n}\n.stage-item {\n\tpadding: 20rpx 0;\n\tborder-bottom: 1px dashed #f4f4f4;\n}\n.stage-item:last-child {\n\tborder-bottom: none;\n}\n.stage-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n}\n.stage-number {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n.stage-status {\n\tfont-size: 24rpx;\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 30rpx;\n\tfont-weight: 500;\n}\n.status-0 { background-color: #e6f7ff; color: #1890ff; } /* 待服务 */\n.status-1 { background-color: #fff7e6; color: #FFB600; } /* 服务中 */\n.status-2 { background-color: #e6fffb; color: #06A051; } /* 已完成 */\n.status-3 { background-color: #f5f5f5; color: #999999; } /* 已跳过 */\n.status-4 { background-color: #fff1f0; color: #f44336; } /* 服务失败 */\n\n.stage-content {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n.no-data {\n\ttext-align: center;\n\tpadding: 30rpx 0;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tdisplay: flex;\n\tjustify-content: space-around;\n\talign-items: center;\n\tpadding: 20rpx 30rpx;\n\tbackground: #fff;\n\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\n\tz-index: 100;\n}\n\n.btn {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tmargin: 0 15rpx;\n}\n\n.btn-pause {\n\tbackground-color: #fff;\n\tcolor: #ff9800;\n\tborder: 1px solid #ff9800;\n}\n\n.btn-resume {\n\tbackground-color: #fff;\n\tcolor: #2196f3;\n\tborder: 1px solid #2196f3;\n}\n\n.btn-cancel {\n\tbackground-color: #fff;\n\tcolor: #f44336;\n\tborder: 1px solid #f44336;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041086\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}