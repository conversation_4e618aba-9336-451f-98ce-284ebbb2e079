{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?7353", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?2571", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?a461", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?6405", "uni-app:///pagesExa/ranking-reward/records.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?e570", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/ranking-reward/records.vue?2331"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordsList", "rulesList", "currentPage", "pageSize", "hasMore", "selectedRuleId", "selectedRuleName", "showRuleSelector", "loading", "onLoad", "onPullDownRefresh", "uni", "methods", "initData", "callback", "resetData", "getRulesList", "console", "app", "that", "title", "icon", "getRecordsList", "page", "limit", "params", "loadMore", "selectRule", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkGhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAC;MAEA;MAEAC;QACA;UACAD;UACAE;QACA;UACAF;UACAN;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAL;MAEA;MAEA;MAEA;QACAM;QACAC;MACA;MAEA;QACAC;MACA;MAEA;MAEAP;QACAC;QAEA;UACAF;UAEA;YACAE;UACA;YACAA;UACA;;UAEA;UACAA;QACA;UACAF;UACAN;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACAV;MAEA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAW;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAAm6C,CAAgB,82CAAG,EAAC,C;;;;;;;;;;;ACAv7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/ranking-reward/records.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/ranking-reward/records.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./records.vue?vue&type=template&id=156955c0&\"\nvar renderjs\nimport script from \"./records.vue?vue&type=script&lang=js&\"\nexport * from \"./records.vue?vue&type=script&lang=js&\"\nimport style0 from \"./records.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/ranking-reward/records.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=template&id=156955c0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recordsList.length\n  var g1 = _vm.recordsList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showRuleSelector = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showRuleSelector = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showRuleSelector = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 规则筛选 -->\r\n\t\t<view class=\"filter-container\">\r\n\t\t\t<view class=\"filter-item\" @click=\"showRuleSelector = true\">\r\n\t\t\t\t<text class=\"filter-label\">规则：</text>\r\n\t\t\t\t<text class=\"filter-value\">{{selectedRuleName || '全部'}}</text>\r\n\t\t\t\t<text class=\"filter-arrow\">▼</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 记录列表 -->\r\n\t\t<view class=\"records-list\">\r\n\t\t\t<view class=\"no-data\" v-if=\"recordsList.length === 0\">\r\n\t\t\t\t<text>暂无奖励记录</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"record-card\" v-for=\"(item, index) in recordsList\" :key=\"index\">\r\n\t\t\t\t<view class=\"record-top\">\r\n\t\t\t\t\t<view class=\"record-title\">{{item.rule_name}}</view>\r\n\t\t\t\t\t<view class=\"record-status\" :class=\"{'status-success': item.status === 1, 'status-pending': item.status === 0, 'status-rejected': item.status === 2}\">\r\n\t\t\t\t\t\t{{item.status_text}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"record-info\">\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">统计周期：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">{{item.period_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">排名：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">第{{item.rank}}名</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">统计数量：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">{{item.count_num}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">奖励比例：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">{{item.reward_rate}}%</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">奖励金额：</text>\r\n\t\t\t\t\t\t<text class=\"record-value reward-color\">¥{{item.reward_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">创建时间：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">{{item.create_time_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\" v-if=\"item.status === 1\">\r\n\t\t\t\t\t\t<text class=\"record-label\">发放时间：</text>\r\n\t\t\t\t\t\t<text class=\"record-value\">{{item.issue_time_text}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载更多 -->\r\n\t\t<view class=\"load-more\" v-if=\"recordsList.length > 0\">\r\n\t\t\t<text v-if=\"loading\">加载中...</text>\r\n\t\t\t<text v-else-if=\"hasMore\" @click=\"loadMore\">点击加载更多</text>\r\n\t\t\t<text v-else>没有更多数据了</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 规则选择器弹窗 -->\r\n\t\t<view class=\"rule-selector\" v-if=\"showRuleSelector\">\r\n\t\t\t<view class=\"selector-mask\" @click=\"showRuleSelector = false\"></view>\r\n\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"selector-title\">选择规则</text>\r\n\t\t\t\t\t<text class=\"selector-close\" @click=\"showRuleSelector = false\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-y class=\"selector-body\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"selector-item\" \r\n\t\t\t\t\t\t:class=\"{'item-selected': selectedRuleId === 0}\"\r\n\t\t\t\t\t\t@click=\"selectRule(0, '全部')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>全部</text>\r\n\t\t\t\t\t\t<text class=\"item-check\" v-if=\"selectedRuleId === 0\">✓</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"selector-item\" \r\n\t\t\t\t\t\t:class=\"{'item-selected': selectedRuleId === item.id}\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in rulesList\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectRule(item.id, item.name)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t<text class=\"item-check\" v-if=\"selectedRuleId === item.id\">✓</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\trecordsList: [],\r\n\t\t\t\trulesList: [],\r\n\t\t\t\t\r\n\t\t\t\tcurrentPage: 1,\r\n\t\t\t\tpageSize: 10,\r\n\t\t\t\thasMore: true,\r\n\t\t\t\t\r\n\t\t\t\tselectedRuleId: 0,\r\n\t\t\t\tselectedRuleName: '全部',\r\n\t\t\t\tshowRuleSelector: false,\r\n\t\t\t\t\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initData();\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.resetData();\r\n\t\t\tthis.initData(() => {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitData(callback) {\r\n\t\t\t\t// 获取规则列表\r\n\t\t\t\tthis.getRulesList();\r\n\t\t\t\t// 获取记录列表\r\n\t\t\t\tthis.getRecordsList();\r\n\t\t\t\t\r\n\t\t\t\tif (callback && typeof callback === 'function') {\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 重置数据\r\n\t\t\tresetData() {\r\n\t\t\t\tthis.currentPage = 1;\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.recordsList = [];\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取规则列表\r\n\t\t\tgetRulesList() {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/records][getRulesList_001] 开始获取排名奖励规则列表`);\r\n\t\t\t\t\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getRankingRules', {}, function(res) {\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/records][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);\r\n\t\t\t\t\t\tthat.rulesList = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/records][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取规则列表失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取记录列表\r\n\t\t\tgetRecordsList(isLoadMore = false) {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/records][getRecordsList_001] 开始获取排名奖励记录，页码：${this.currentPage}，规则ID：${this.selectedRuleId}`);\r\n\t\t\t\t\r\n\t\t\t\tif (this.loading) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: this.currentPage,\r\n\t\t\t\t\tlimit: this.pageSize\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tif (this.selectedRuleId > 0) {\r\n\t\t\t\t\tparams.rule_id = this.selectedRuleId;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tapp.post('ApiPaimingjiang/getRankingRecords', params, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status === 1) {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-INFO-[ranking-reward/records][getRecordsList_002] 获取排名奖励记录成功，当前页数据：${res.data.list.length}条`);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (isLoadMore) {\r\n\t\t\t\t\t\t\tthat.recordsList = [...that.recordsList, ...res.data.list];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.recordsList = res.data.list;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 判断是否还有更多数据\r\n\t\t\t\t\t\tthat.hasMore = that.recordsList.length < res.data.total;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(`${that.formatTime()}-ERROR-[ranking-reward/records][getRecordsList_003] 获取排名奖励记录失败：${res.msg}`);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取奖励记录失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载更多\r\n\t\t\tloadMore() {\r\n\t\t\t\tif (!this.hasMore || this.loading) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.currentPage++;\r\n\t\t\t\tthis.getRecordsList(true);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择规则\r\n\t\t\tselectRule(ruleId, ruleName) {\r\n\t\t\t\tconsole.log(`${this.formatTime()}-INFO-[ranking-reward/records][selectRule_001] 选择规则，ID：${ruleId}，名称：${ruleName}`);\r\n\t\t\t\t\r\n\t\t\t\tthis.selectedRuleId = ruleId;\r\n\t\t\t\tthis.selectedRuleName = ruleName;\r\n\t\t\t\tthis.showRuleSelector = false;\r\n\t\t\t\t\r\n\t\t\t\t// 重置数据，重新加载列表\r\n\t\t\t\tthis.resetData();\r\n\t\t\t\tthis.getRecordsList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间，用于日志\r\n\t\t\tformatTime() {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\tconst seconds = date.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\tconst milliseconds = date.getMilliseconds().toString().padStart(3, '0');\r\n\t\t\t\t\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.reward-color {\r\n\tcolor: #FF6600 !important;\r\n}\r\n\r\n// 筛选器\r\n.filter-container {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx 30rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.filter-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.filter-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.filter-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tflex: 1;\r\n}\r\n\r\n.filter-arrow {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n// 记录列表\r\n.record-card {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.record-top {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n}\r\n\r\n.record-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.record-status {\r\n\tfont-size: 24rpx;\r\n\tpadding: 4rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.status-pending {\r\n\tcolor: #FF9900;\r\n\tbackground-color: rgba(255, 153, 0, 0.1);\r\n}\r\n\r\n.status-success {\r\n\tcolor: #00CC66;\r\n\tbackground-color: rgba(0, 204, 102, 0.1);\r\n}\r\n\r\n.status-rejected {\r\n\tcolor: #FF3333;\r\n\tbackground-color: rgba(255, 51, 51, 0.1);\r\n}\r\n\r\n.record-info {\r\n}\r\n\r\n.record-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.record-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\twidth: 160rpx;\r\n}\r\n\r\n.record-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n// 加载更多\r\n.load-more {\r\n\ttext-align: center;\r\n\tpadding: 30rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n// 无数据提示\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 50rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n// 规则选择器弹窗\r\n.rule-selector {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 999;\r\n}\r\n\r\n.selector-mask {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0,0,0,0.5);\r\n}\r\n\r\n.selector-content {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\toverflow: hidden;\r\n}\r\n\r\n.selector-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n}\r\n\r\n.selector-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.selector-close {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.selector-body {\r\n\tmax-height: 600rpx;\r\n}\r\n\r\n.selector-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.item-selected {\r\n\tcolor: #FF6600;\r\n}\r\n\r\n.item-check {\r\n\tfont-size: 32rpx;\r\n\tcolor: #FF6600;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./records.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115083700\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}