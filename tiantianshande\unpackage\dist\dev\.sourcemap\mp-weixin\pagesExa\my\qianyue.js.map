{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?aa50", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?fa5f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?e3ea", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?0577", "uni-app:///pagesExa/my/qianyue.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?4643", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?cc67", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?9e99", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/qianyue.vue?3b56"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "second", "secondstr", "loading", "isload", "menuindex", "pre_url", "platform2", "pic", "platform", "platformname", "platformimg", "logintype", "logintype_1", "logintype_2", "logintype_3", "logo", "name", "xystatus", "xyname", "xycontent", "xyname2", "xycontent2", "needsms", "<PERSON><PERSON><PERSON><PERSON>", "showxieyi2", "showxieyi3", "isagree", "smsdjs", "tel", "<PERSON><PERSON>", "frompage", "wxloginclick", "login_bind", "login_setnickname", "reg_invite_code", "reg_invite_code_text", "reg_invite_code_type", "parent", "has_custom", "show_custom_field", "regiondata", "editorFormdata", "test", "formfields", "custom_formdata", "items", "formvaldata", "submitDisabled", "tmplids", "default_headimg", "headimg", "nickname", "ctx", "points", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "onLoad", "that", "console", "id", "type", "uni", "success", "onPullDownRefresh", "methods", "getSign", "app", "getdata", "pid", "url", "method", "header", "formSubmit", "formdata", "pwd", "smscode", "yqcode", "postdata", "setTimeout", "nosetnicknameregister", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showxieyiFun3", "<PERSON><PERSON><PERSON><PERSON>", "showxieyiFun2", "hidexieyi2", "telinput", "uploadHeadimg", "count", "sizeType", "sourceType", "globalData", "filePath", "fail", "onChooseAvatar", "onchange", "setfield", "removeimg", "touchstart", "X", "Y", "touchmove", "tempPoint", "touchend", "draw", "handleCancel", "handleReset", "handleConfirm", "title", "icon", "duration", "canvasId", "agreement_signed", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACa;AACyB;;;AAG5F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkDhxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;IACAC;IACAC;IACA;IACA;IACA;IACA;IACA;IAEAC;MACAC;QACAJ;QACAD;QACAA;MACA;IACA;EACA;EACAM;IACA;EACA;EACAC;IAEAC;MACA;MACAC;QACAR;QACA;UAEAD;QACA;MAEA;IACA;IAEAU;MACA;MACAV;MACAS;QACAE;MACA;QACAX;QACA;UACAS;UACA;QACA;QACAT;QACAA;QACAA;QAcAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;;QAEA;QACA;UACAA;UACAA;UACAA;UACAI;YACAQ;YACAzE;YACA0E;YACAC;cACA;YACA;YACAT;cACAL;YACA;UACA;QACA;QACAA;MACA;IACA;IACAe;MACA;MACA;MACA;QACAN;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MAGA;QACA;UACAA;UACA;QACA;MACA;QACAO;MACA;MACA;QACAnB;QACAL;QACAvB;QACAgD;QACAC;QACAP;QACAQ;MACA;MACA;MACA;QACA;QACA;QACA;UACA;QACA;QACAC;QACAA;MACA;MAEA;QACAX;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACAA;MACAA;QACAA;QACA;UACAA;UACA;YACAT;UACA;UACAA;YACAqB;cACA;gBACAZ;cACA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;MACA;IACA;IAGAa;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAIAC;MACA;MACA;QACA;MACA;QACA;MACA;MACAtB;IACA;IACAuB;MACA;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA1B;QACA2B;QACAC;QACAC;QACA5B;UACA;UACA;UACAI;UACAL;YACAQ,oEACAsB,0DACA;YACAC;YACA9E;YACAgD;cACAJ;cACAQ;cACA;cACA;gBACAT;cACA;gBACAS;cACA;YACA;YACA2B;cACA3B;cACAA;YACA;UACA;QACA;QACA2B;QAAA;MAEA;IACA;IACAC;MACApC;MACA;MACAQ;MACAL;QACAQ,qFACA,sFACA;QACAuB;QACA9E;QACAgD;UACAI;UACA;UACA;YACAT;UACA;YACAS;UACA;QACA;QACA2B;UACA3B;UACAA;QACA;MACA;IACA;IAEA;IACA6B;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAGAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA1D;MACAkB;MACAA;MACAA;IACA;IACA;IACAyC;MAEAxC;MAEA;MACA;MACA;QACAyC;QACAC;MACA;;MAEA;AACA;AACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAF;QACAC;MACA;MACA;MACA;MACA;QACA;MACA;;MACAE;IACA;IAEA;IACAC;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAhD;MACAD;MACAA;MACA6C;IACA;IAEA;IACAK;MACA;QACA9C;UACA+C;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACAjD;QACAkD;QACAjD;UAAA;UACA;UAEA;UACAZ;UACAA;UACAA;UACAA;UAEA4B;YACA;YACAjB;cACAkD;cACAjD;gBACA;;gBAEA;gBACAD;kBACAQ;kBACAuB;kBACA9E;kBACAgD;oBACA;oBACA;sBACA;sBACAI;wBACA8C;sBACA;wBACAtD;wBACAQ;;wBAEA;wBACAT;wBACAlE;0BACA0H;wBACA;sBACA;oBACA;sBACA/C;oBACA;kBACA;kBACA2B;oBACA3B;kBACA;gBACA;cACA;cACA2B;gBACAnC;cACA;YACA;UACA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7mBA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA27C,CAAgB,s4CAAG,EAAC,C;;;;;;;;;;;ACA/8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/qianyue.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/qianyue.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qianyue.vue?vue&type=template&id=6f53e368&scoped=true&\"\nvar renderjs\nimport script from \"./qianyue.vue?vue&type=script&lang=js&\"\nexport * from \"./qianyue.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qianyue.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./qianyue.vue?vue&type=style&index=1&id=6f53e368&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f53e368\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/qianyue.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=template&id=6f53e368&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t\r\n\r\n\t\t\t<view style=\"text-align: center;font-size: 20px;margin-top: 10px;font-weight: bold;\">签约中心</view>\r\n\r\n\t\t\t<view style=\"padding: 20px;\"  v-html=\"content\"></view>\r\n\r\n\t\t\t<view class=\"ll\">\r\n\r\n\t\t\t\t<button class=\"form-btn\"\r\n\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\r\n\t\t\t\t\tform-type=\"submit\" @tap=\"showxieyiFun3\">签字</button>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"showxieyi3\" class=\"xieyibox\">\r\n\t\t\t\t<view class=\"container3\">\r\n\r\n\t\t\t\t\t<view class=\"sigh-btns\">\r\n\t\t\t\t\t\t<button class=\"btn\" @tap=\"handleCancel\"\r\n\t\t\t\t\t\t\tstyle=\"background:red;padding:10px;color:#fff;\">取消</button>\r\n\t\t\t\t\t\t<button class=\"btn\" @tap=\"handleReset\"\r\n\t\t\t\t\t\t\tstyle=\"background:red;padding:10px;color:#fff;\">重写</button>\r\n\t\t\t\t\t\t<button class=\"btn\" @tap=\"handleConfirm\"\r\n\t\t\t\t\t\t\tstyle=\"background:red;padding:10px;color:#fff;\">确认</button>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"sign-box\">\r\n\t\t\t\t\t\t<canvas class=\"mycanvas\" :style=\"{width:width +'px',height:height +'px'}\" canvas-id=\"mycanvas\"\r\n\t\t\t\t\t\t\t@touchstart=\"touchstart\" @touchmove=\"touchmove\" @touchend=\"touchend\"></canvas>\r\n\r\n\t\t\t\t\t\t<canvas canvas-id=\"camCacnvs\" :style=\"{width:height +'px',height:width +'px'}\"\r\n\t\t\t\t\t\t\tclass=\"canvsborder\"></canvas>\r\n\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\tvar x = 20;\r\n\tvar y = 20;\r\n\tvar tempPoint = []; //用来存放当前画纸上的轨迹点\r\n\tvar id = 0;\r\n\tvar type = '';\r\n\tlet that;\r\n\tlet canvasw;\r\n\tlet canvash;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tsecond: 30,\r\n\t\t\t\tsecondstr: '请阅读至少30秒',\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tplatform2: app.globalData.platform2,\r\n\t\t\t\tpic: [],\r\n\t\t\t\tplatform: '',\r\n\t\t\t\tplatformname: '',\r\n\t\t\t\tplatformimg: 'weixin',\r\n\t\t\t\tlogintype: 0,\r\n\t\t\t\tlogintype_1: true,\r\n\t\t\t\tlogintype_2: false,\r\n\t\t\t\tlogintype_3: false,\r\n\t\t\t\tlogo: '',\r\n\t\t\t\tname: '',\r\n\t\t\t\txystatus: 0,\r\n\t\t\t\txyname: '',\r\n\t\t\t\txycontent: '',\r\n\t\t\t\txyname2: '',\r\n\t\t\t\txycontent2: '',\r\n\t\t\t\tneedsms: false,\r\n\t\t\t\tshowxieyi: false,\r\n\t\t\t\tshowxieyi2: false,\r\n\t\t\t\tshowxieyi3: false,\r\n\t\t\t\tisagree: false,\r\n\t\t\t\tsmsdjs: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\thqing: 0,\r\n\t\t\t\tfrompage: '/pages/my/usercenter',\r\n\t\t\t\twxloginclick: false,\r\n\t\t\t\tlogin_bind: 0,\r\n\t\t\t\tlogin_setnickname: 0,\r\n\t\t\t\treg_invite_code: 0,\r\n\t\t\t\treg_invite_code_text: '',\r\n\t\t\t\treg_invite_code_type: 0,\r\n\t\t\t\tparent: {},\r\n\t\t\t\t//自定义表单Start\r\n\t\t\t\thas_custom: 0,\r\n\t\t\t\tshow_custom_field: false,\r\n\t\t\t\tregiondata: '',\r\n\t\t\t\teditorFormdata: {},\r\n\t\t\t\ttest: '',\r\n\t\t\t\tformfields: [],\r\n\t\t\t\tcustom_formdata: [],\r\n\t\t\t\titems: [],\r\n\t\t\t\tformvaldata: {},\r\n\t\t\t\tsubmitDisabled: false,\r\n\t\t\t\t//自定义表单End\r\n\t\t\t\ttmplids: [],\r\n\t\t\t\tdefault_headimg: app.globalData.pre_url + '/static/img/touxiang.png',\r\n\t\t\t\theadimg: '',\r\n\t\t\t\tnickname: '',\r\n\t\t\t\tctx: '', //绘图图像\r\n\t\t\t\tpoints: [], //路径点集合,\r\n\t\t\t\twidth: 0,\r\n\t\t\t\theight: 0,\r\n\t\t\t\tqianmingurl: '',\r\n\t\t\t\tcontent : ''\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthat = this;\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tif (this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);\r\n\t\t\tif (this.opt.logintype) this.logintype = this.opt.logintype;\r\n\t\t\tif (this.opt.login_bind) this.login_bind = this.opt.login_bind;\r\n\t\t\tthis.getdata();\r\n\t\t\tthis.getSign();\r\n\t\t\tconsole.log(opt);\r\n\t\t\tid = opt.id;\r\n\t\t\ttype = opt.type;\r\n\t\t\tthis.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象\r\n\t\t\t//设置画笔样式\r\n\t\t\tthis.ctx.lineWidth = 4;\r\n\t\t\tthis.ctx.lineCap = 'round';\r\n\t\t\tthis.ctx.lineJoin = 'round';\r\n\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tthat.width = res.windowWidth * 0.8;\r\n\t\t\t\t\tthat.height = res.windowHeight * 0.85;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\tgetSign(){\r\n                let that = this \r\n\t\t\t\tapp.get('ApiMy/getRenzhengItems', {}, function(res) {\r\n                        console.log(res)\r\n\t\t\t\t\t  if(res.data){\r\n\t\t\t\t\t\t  \r\n\t\t\t\t\t\t  that.content = res.content;\r\n\t\t\t\t\t  }\r\n\t\t\t\t\t  \r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiIndex/reg', {\r\n\t\t\t\t\tpid: app.globalData.pid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.logintype_2 = res.logintype_2;\r\n\t\t\t\t\tthat.logintype_3 = res.logintype_3;\r\n\t\t\t\t\tthat.logintype_3 = res.logintype_3;\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tif (that.platform2 == 'ios') {\r\n\t\t\t\t\t\tif (plus.runtime.isApplicationExist({\r\n\t\t\t\t\t\t\t\tpname: 'com.tencent.mm',\r\n\t\t\t\t\t\t\t\taction: 'weixin://'\r\n\t\t\t\t\t\t\t})) {\r\n\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.logintype_3 = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\tthat.xystatus = res.xystatus;\r\n\t\t\t\t\tthat.xyname = res.xyname;\r\n\t\t\t\t\tthat.xycontent = res.xycontent;\r\n\t\t\t\t\tthat.xyname2 = res.xyname2;\r\n\t\t\t\t\tthat.xycontent2 = res.xycontent2;\r\n\t\t\t\t\tthat.logo = res.logo;\r\n\t\t\t\t\tthat.name = res.name;\r\n\t\t\t\t\tthat.needsms = res.needsms;\r\n\t\t\t\t\tthat.platform = res.platform;\r\n\t\t\t\t\tthat.reg_invite_code = res.reg_invite_code;\r\n\t\t\t\t\tthat.reg_invite_code_text = res.reg_invite_code_text;\r\n\t\t\t\t\tthat.reg_invite_code_type = res.reg_invite_code_type;\r\n\t\t\t\t\tthat.parent = res.parent;\r\n\t\t\t\t\tif (that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app') {\r\n\t\t\t\t\t\tthat.platformname = '微信';\r\n\t\t\t\t\t\tthat.platformimg = 'weixin';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.platform == 'toutiao') {\r\n\t\t\t\t\t\tthat.platformname = '头条';\r\n\t\t\t\t\t\tthat.platformimg = 'toutiao';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.platform == 'alipay') {\r\n\t\t\t\t\t\tthat.platformname = '支付宝';\r\n\t\t\t\t\t\tthat.platformimg = 'alipay';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.platform == 'qq') {\r\n\t\t\t\t\t\tthat.platformname = 'QQ';\r\n\t\t\t\t\t\tthat.platformimg = 'qq';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.platform == 'baidu') {\r\n\t\t\t\t\t\tthat.platformname = '百度';\r\n\t\t\t\t\t\tthat.platformimg = 'baidu';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t//自定义表单\r\n\t\t\t\t\tif (res.has_custom) {\r\n\t\t\t\t\t\tthat.formfields = res.custom_form_field;\r\n\t\t\t\t\t\tthat.has_custom = res.has_custom\r\n\t\t\t\t\t\tthat.show_custom_field = true\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\turl: app.globalData.pre_url + '/static/area.json',\r\n\t\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t'content-type': 'application/json'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tformSubmit: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\tif (formdata.tel == '') {\r\n\t\t\t\t\tapp.alert('请输入手机号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.pwd == '') {\r\n\t\t\t\t\tapp.alert('请输入密码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.pwd.length < 6) {\r\n\t\t\t\t\tapp.alert('新密码不小于6位');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.repwd == '') {\r\n\t\t\t\t\tapp.alert('请再次输入新密码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.pwd != formdata.repwd) {\r\n\t\t\t\t\tapp.alert('两次密码不一致');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\tif (that.needsms) {\r\n\t\t\t\t\tif (formdata.smscode == '') {\r\n\t\t\t\t\t\tapp.alert('请输入短信验证码');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tformdata.smscode = '';\r\n\t\t\t\t}\r\n\t\t\t\tvar postdata = {\r\n\t\t\t\t\tqianmingurl: formdata.qianming,\r\n\t\t\t\t\tnickname: formdata.nickname,\r\n\t\t\t\t\ttel: formdata.tel,\r\n\t\t\t\t\tpwd: formdata.pwd,\r\n\t\t\t\t\tsmscode: formdata.smscode,\r\n\t\t\t\t\tpid: app.globalData.pid,\r\n\t\t\t\t\tyqcode: formdata.yqcode\r\n\t\t\t\t}\r\n\t\t\t\t//如果有自定义表单则验证表单内容\r\n\t\t\t\tif (that.show_custom_field) {\r\n\t\t\t\t\tvar customformdata = {};\r\n\t\t\t\t\tvar customData = that.checkCustomFormFields();\r\n\t\t\t\t\tif (!customData) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tpostdata['customformdata'] = customData\r\n\t\t\t\t\tpostdata['customformid'] = that.formfields.id\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (that.xystatus == 1 && !that.isagree) {\r\n\t\t\t\t\tapp.error('请先阅读并同意用户注册协议');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.second > 0) {\r\n\t\t\t\t\tapp.error('请先阅读并同意用户注册协议');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.qianming.length <= 0) {\r\n\t\t\t\t\tapp.error('请先签名');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post(\"ApiIndex/regsub\", postdata, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\t\tif (data.tmplids) {\r\n\t\t\t\t\t\t\tthat.tmplids = data.tmplids;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.subscribeMessage(function() {\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tif (that.opt.fromapp == 1 && data.toappurl) {\r\n\t\t\t\t\t\t\t\t\tapp.goto(data.toappurl, 'redirect');\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tapp.goto('/pages/my/usercenter', 'redirect');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tnosetnicknameregister: function() {\r\n\t\t\t\tthis.nickname = '';\r\n\t\t\t\tthis.headimg = '';\r\n\t\t\t\tif (this.login_bind != 0) {\r\n\t\t\t\t\tthis.logintype = 4;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.register('', '', '', '');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tisagreeChange: function(e) {\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tif (val.length > 0) {\r\n\t\t\t\t\tthis.isagree = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isagree = false;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(this.isagree);\r\n\t\t\t},\r\n\t\t\tshowxieyiFun: function() {\r\n\t\t\t\tthis.showxieyi = true;\r\n\t\t\t\tthis.addxieyisen();\r\n\t\t\t},\r\n\t\t\tshowxieyiFun3: function() {\r\n\t\t\t\tthis.showxieyi3 = true;\r\n\t\t\t},\r\n\t\t\r\n\t\t\thidexieyi: function() {\r\n\t\t\t\tif (this.second <= 0) {\r\n\t\t\t\t\tthis.showxieyi = false;\r\n\t\t\t\t\tthis.isagree = true;\r\n\t\t\t\t\tif (this.wxloginclick) {\r\n\t\t\t\t\t\tthis.weixinlogin();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowxieyiFun2: function() {\r\n\t\t\t\tthis.showxieyi2 = true;\r\n\t\t\t},\r\n\t\t\thidexieyi2: function() {\r\n\t\t\t\tthis.showxieyi2 = false;\r\n\t\t\t\tthis.isagree = true;\r\n\t\t\t\tif (this.wxloginclick) { \r\n\t\t\t\t\tthis.weixinlogin();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.iosloginclick) {\r\n\t\t\t\t\tthis.ioslogin();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttelinput: function(e) {\r\n\t\t\t\tthis.tel = e.detail.value\r\n\t\t\t},\r\n\t\t\tuploadHeadimg: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsizeType: ['original', 'compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tvar tempFilePaths = res.tempFilePaths;\r\n\t\t\t\t\t\tvar tempFilePath = tempFilePaths[0];\r\n\t\t\t\t\t\tapp.showLoading('上传中');\r\n\t\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app\r\n\t\t\t\t\t\t\t\t.globalData.aid + '/platform/' + app.globalData.platform +\r\n\t\t\t\t\t\t\t\t'/session_id/' + app.globalData.session_id + '/isheadimg/1',\r\n\t\t\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(res) { //alert(res.errMsg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonChooseAvatar: function(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.showLoading('上传中');\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid +\r\n\t\t\t\t\t\t'/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id +\r\n\t\t\t\t\t\t'/isheadimg/1',\r\n\t\t\t\t\tfilePath: e.detail.avatarUrl,\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\t\tthat.headimg = data.url;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tapp.alert(res.errMsg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t//自定义表单\r\n\t\t\tonchange(e) {\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;\r\n\t\t\t},\r\n\t\t\tsetfield: function(e) {\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tthis.formvaldata[field] = value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tremoveimg: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif (!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = '';\r\n\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t\tthat.formvaldata[field] = '';\r\n\t\t\t},\r\n\t\t\t//触摸开始，获取到起点\r\n\t\t\ttouchstart: function(e) {\r\n\r\n\t\t\t\tconsole.log(e);\r\n\r\n\t\t\t\tlet startX = e.changedTouches[0].x;\r\n\t\t\t\tlet startY = e.changedTouches[0].y;\r\n\t\t\t\tlet startPoint = {\r\n\t\t\t\t\tX: startX,\r\n\t\t\t\t\tY: startY\r\n\t\t\t\t};\r\n\r\n\t\t\t\t/* **************************************************\r\n\t\t\t\t\t    #由于uni对canvas的实现有所不同，这里需要把起点存起来\r\n\t\t\t\t\t * **************************************************/\r\n\t\t\t\tthis.points.push(startPoint);\r\n\r\n\t\t\t\t//每次触摸开始，开启新的路径\r\n\t\t\t\tthis.ctx.beginPath();\r\n\t\t\t},\r\n\r\n\t\t\t//触摸移动，获取到路径点\r\n\t\t\ttouchmove: function(e) {\r\n\t\t\t\tlet moveX = e.changedTouches[0].x;\r\n\t\t\t\tlet moveY = e.changedTouches[0].y;\r\n\t\t\t\tlet movePoint = {\r\n\t\t\t\t\tX: moveX,\r\n\t\t\t\t\tY: moveY\r\n\t\t\t\t};\r\n\t\t\t\tthis.points.push(movePoint); //存点\r\n\t\t\t\tlet len = this.points.length;\r\n\t\t\t\tif (len >= 2) {\r\n\t\t\t\t\tthis.draw(); //绘制路径\r\n\t\t\t\t}\r\n\t\t\t\ttempPoint.push(movePoint);\r\n\t\t\t},\r\n\r\n\t\t\t// 触摸结束，将未绘制的点清空防止对后续路径产生干扰\r\n\t\t\ttouchend: function() {\r\n\t\t\t\tthis.points = [];\r\n\t\t\t},\r\n\r\n\t\t\t/* ***********************************************\t\r\n\t\t\t\t\t#   绘制笔迹\r\n\t\t\t\t\t#   1.为保证笔迹实时显示，必须在移动的同时绘制笔迹\r\n\t\t\t\t\t#   2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)\r\n\t\t\t\t\t#   3.将上一次的终点作为下一次绘制的起点（即清除第一个点）\r\n\t\t\t\t\t************************************************ */\r\n\t\t\tdraw: function() {\r\n\t\t\t\tlet point1 = this.points[0];\r\n\t\t\t\tlet point2 = this.points[1];\r\n\t\t\t\tthis.points.shift();\r\n\t\t\t\tthis.ctx.moveTo(point1.X, point1.Y);\r\n\t\t\t\tthis.ctx.lineTo(point2.X, point2.Y);\r\n\t\t\t\tthis.ctx.stroke();\r\n\t\t\t\tthis.ctx.draw(true);\r\n\t\t\t},\r\n\r\n\t\t\thandleCancel() {\r\n\t\t\t\t// uni.navigateBack({\r\n\t\t\t\t// \tdelta: 1\r\n\t\t\t\t// });\r\n\t\t\t\tthis.showxieyi3 = false;\r\n\t\t\t},\r\n\r\n\t\t\t//清空画布\r\n\t\t\thandleReset: function() {\r\n\t\t\t\tconsole.log('handleReset');\r\n\t\t\t\tthat.ctx.clearRect(0, 0, that.width, that.height);\r\n\t\t\t\tthat.ctx.draw(true);\r\n\t\t\t\ttempPoint = [];\r\n\t\t\t},\r\n\r\n\t\t\t//将签名笔迹上传到服务器，并将返回来的地址存到本地\r\n\t\t\thandleConfirm: function() {\r\n\t\t\t    if (tempPoint.length == 0) {\r\n\t\t\t        uni.showToast({\r\n\t\t\t            title: '请先签名',\r\n\t\t\t            icon: 'none',\r\n\t\t\t            duration: 2000\r\n\t\t\t        });\r\n\t\t\t        return;\r\n\t\t\t    }\r\n\t\t\t    \r\n\t\t\t    // 将签名转换为临时文件路径\r\n\t\t\t    uni.canvasToTempFilePath({\r\n\t\t\t        canvasId: 'mycanvas',\r\n\t\t\t        success: function(res) {\r\n\t\t\t            let tempPath = res.tempFilePath;\r\n\t\t\t\r\n\t\t\t            const ctx = uni.createCanvasContext('camCacnvs', that);\r\n\t\t\t            ctx.translate(0, that.width);\r\n\t\t\t            ctx.rotate((-90 * Math.PI) / 180);\r\n\t\t\t            ctx.drawImage(tempPath, 0, 0, that.width, that.height);\r\n\t\t\t            ctx.draw();\r\n\t\t\t\r\n\t\t\t            setTimeout(() => {\r\n\t\t\t                // 保存签名图片到本地\r\n\t\t\t                uni.canvasToTempFilePath({\r\n\t\t\t                    canvasId: 'camCacnvs',\r\n\t\t\t                    success: function(res) {\r\n\t\t\t                        let path = res.tempFilePath;\r\n\t\t\t\r\n\t\t\t                        // 上传签名图片到服务器\r\n\t\t\t                        uni.uploadFile({\r\n\t\t\t                            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n\t\t\t                            filePath: path,\r\n\t\t\t                            name: 'file',\r\n\t\t\t                            success: function(res) {\r\n\t\t\t                                var data = JSON.parse(res.data);\r\n\t\t\t                                if (data.status == 1) {\r\n\t\t\t                                    // 服务器接口调用，保存签名的 URL\r\n\t\t\t                                    app.post('ApiMy/setfield', {\r\n\t\t\t                                        agreement_signed: data.url\r\n\t\t\t                                    }, function(response) {\r\n\t\t\t                                        console.log(response);\r\n\t\t\t                                        app.alert('签约成功');\r\n\t\t\t\r\n\t\t\t                                        // 签字成功后返回上一页\r\n\t\t\t                                        that.showxieyi3 = false;\r\n\t\t\t                                        wx.navigateBack({\r\n\t\t\t                                            delta: 1 // 返回上一页\r\n\t\t\t                                        });\r\n\t\t\t                                    });\r\n\t\t\t                                } else {\r\n\t\t\t                                    app.alert(data.msg);\r\n\t\t\t                                }\r\n\t\t\t                            },\r\n\t\t\t                            fail: function(res) {\r\n\t\t\t                                app.alert(res.errMsg);\r\n\t\t\t                            }\r\n\t\t\t                        });\r\n\t\t\t                    },\r\n\t\t\t                    fail: err => {\r\n\t\t\t                        console.log('fail', err);\r\n\t\t\t                    }\r\n\t\t\t                }, this);\r\n\t\t\t            }, 200);\r\n\t\t\t        }\r\n\t\t\t    });\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground: #ffffff\r\n\t}\r\n\r\n\t.container {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.title {\r\n\t\tmargin: 70rpx 50rpx 50rpx 40rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #000000;\r\n\t}\r\n\r\n\t.regform {\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 50rpx;\r\n\t\tborder-radius: 5px;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.regform .form-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tborder-bottom: 1px solid #F0F3F6;\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\t.regform .form-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.regform .form-item .img {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tmargin-right: 30rpx\r\n\t}\r\n\r\n\t.regform .form-item .input {\r\n\t\tflex: 1;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.regform .form-item .code {\r\n\t\tfont-size: 30rpx\r\n\t}\r\n\r\n\t.regform .xieyi-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 50rpx\r\n\t}\r\n\r\n\t.regform .xieyi-item {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #B2B5BE\r\n\t}\r\n\r\n\t.regform .xieyi-item .checkbox {\r\n\t\ttransform: scale(0.6);\r\n\t}\r\n\r\n\t.regform .form-btn {\r\n\t\tmargin-top: 20rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t}\r\n\r\n\t.regform .form-btn2 {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tbackground: #EEEEEE;\r\n\t\tborder-radius: 40rpx;\r\n\t\tcolor: #A9A9A9;\r\n\t\tmargin-top: 30rpx\r\n\t}\r\n\r\n\t.tologin {\r\n\t\tcolor: #737785;\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 80rpx;\r\n\t\tmargin-top: 30rpx\r\n\t}\r\n\r\n\t.othertip {\r\n\t\theight: auto;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 580rpx;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\r\n\t.othertip-line {\r\n\t\theight: auto;\r\n\t\tpadding: 0;\r\n\t\toverflow: hidden;\r\n\t\tflex: 1;\r\n\t\theight: 0;\r\n\t\tborder-top: 1px solid #F2F2F2\r\n\t}\r\n\r\n\t.othertip-text {\r\n\t\tpadding: 0 32rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\t.othertip-text .txt {\r\n\t\tcolor: #A3A3A3;\r\n\t\tfont-size: 22rpx\r\n\t}\r\n\r\n\t.othertype {\r\n\t\twidth: 70%;\r\n\t\tmargin: 20rpx 15%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.othertype-item {\r\n\t\twidth: 50%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.othertype-item .img {\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx;\r\n\t\tmargin-bottom: 20rpx\r\n\t}\r\n\r\n\t.othertype-item .txt {\r\n\t\tcolor: #A3A3A3;\r\n\t\tfont-size: 24rpx\r\n\t}\r\n\r\n\t.xieyibox {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t\tbackground: rgba(0, 0, 0, 0.7)\r\n\t}\r\n\r\n\t.xieyibox-content {\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 auto;\r\n\t\theight: 80%;\r\n\t\tmargin-top: 20%;\r\n\t\tbackground: #fff;\r\n\t\tcolor: #333;\r\n\t\tpadding: 5px 10px 50px 10px;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 2px\r\n\t}\r\n\r\n\r\n\t.authlogin {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.authlogin-logo {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tmargin-top: 120rpx\r\n\t}\r\n\r\n\t.authlogin-name {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\r\n\t.authlogin-btn {\r\n\t\twidth: 580rpx;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tbackground: #51B1F5;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin-top: 100rpx\r\n\t}\r\n\r\n\t.authlogin-btn2 {\r\n\t\twidth: 580rpx;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tbackground: #EEEEEE;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #A9A9A9;\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\r\n\t/* 自定义字段显示 */\r\n\t.dp-form-item {\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\tpadding: 16rpx 0px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-form-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.dp-form-item .label {\r\n\t\tline-height: 40rpx;\r\n\t\twidth: 140rpx;\r\n\t\tmargin-right: 10px;\r\n\t\tflex-shrink: 0;\r\n\t\ttext-align: right;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.dp-form-item .input {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\toverflow: hidden;\r\n\t\tflex: 1;\r\n\t\tborder: 1px solid #eee;\r\n\t\tpadding: 0 8rpx;\r\n\t\tborder-radius: 2px;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.dp-form-item .textarea {\r\n\t\theight: 180rpx;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\tflex: 1;\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 2px;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.dp-form-item .radio {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.dp-form-item .radio2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-form-item .radio .myradio {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 50%\r\n\t}\r\n\r\n\t.dp-form-item .checkbox {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.dp-form-item .checkbox2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\r\n\t.dp-form-item .checkbox .mycheckbox {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 2px\r\n\t}\r\n\r\n\t.dp-form-item .layui-form-switch {}\r\n\r\n\t.dp-form-item .picker {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.dp-form-item2 {\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\tpadding: 10rpx 0px;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t}\r\n\r\n\t.dp-form-item2:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.dp-form-item2 .label {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\twidth: 100%;\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.dp-form-item2 .input {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\toverflow: hidden;\r\n\t\twidth: 100%;\r\n\t\tborder: 1px solid #eee;\r\n\t\tpadding: 0 8rpx;\r\n\t\tborder-radius: 2px;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.dp-form-item2 .textarea {\r\n\t\theight: 180rpx;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\twidth: 100%;\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 2px;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.dp-form-item2 .radio {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-form-item2 .radio2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-form-item2 .radio .myradio {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 50%\r\n\t}\r\n\r\n\t.dp-form-item2 .checkbox {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.dp-form-item2 .checkbox2 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\r\n\t.dp-form-item2 .checkbox .mycheckbox {\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tborder: 1px solid #aaa;\r\n\t\tbackground: #fff;\r\n\t\theight: 32rpx;\r\n\t\twidth: 32rpx;\r\n\t\tborder-radius: 2px\r\n\t}\r\n\r\n\t.dp-form-item2 .layui-form-switch {}\r\n\r\n\t.dp-form-item2 .picker {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tflex: 1;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.dp-form-uploadbtn {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx\r\n\t}\r\n\r\n\t.dp-form-imgbox {\r\n\t\tmargin-right: 16rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.dp-form-imgbox-close {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tright: -16rpx;\r\n\t\ttop: -16rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.dp-form-imgbox-close .image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.dp-form-imgbox-img {\r\n\t\tdisplay: block;\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tpadding: 2px;\r\n\t\tborder: #d3d3d3 1px solid;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.dp-form-imgbox-img>.image {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.dp-form-imgbox-repeat {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tright: 2px;\r\n\t\tbottom: 2px;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 30rpx;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.dp-form-uploadbtn {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.container3 {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.sign-box {\r\n\t\twidth: 80%;\r\n\t\theight: 90%;\r\n\t\tmargin: auto;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.sign-view {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.sigh-btns {\r\n\t\theight: 100%;\r\n\t\tmargin: auto;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-around;\r\n\t}\r\n\r\n\t.btn {\r\n\t\tmargin: auto;\r\n\t\tpadding: 8rpx;\r\n\t\ttransform: rotate(90deg);\r\n\t\tborder: grey 1rpx solid;\r\n\t}\r\n\r\n\t.mycanvas {\r\n\t\tmargin: auto 0rpx;\r\n\t\tbackground-color: #ececec;\r\n\t}\r\n\r\n\t.canvsborder {\r\n\t\tborder: 1rpx solid #333;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 10000rpx;\r\n\t}\r\n\r\n   .ll{\r\n\t   width: 100%;\r\n\t   padding: 0 50rpx;\r\n\t   border-radius: 5px;\r\n\t   background: #FFF;\r\n\t   position: fixed;\r\n\t   bottom: 20px;\r\n   }\r\n\r\n\t.ll .form-btn {\r\n\t\tmargin-top: 20rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115062490\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=style&index=1&id=6f53e368&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./qianyue.vue?vue&type=style&index=1&id=6f53e368&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115072034\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}