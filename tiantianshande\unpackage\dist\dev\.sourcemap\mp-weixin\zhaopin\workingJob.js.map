{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?4d79", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?6e17", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?b78d", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?a823", "uni-app:///zhaopin/workingJob.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?e483", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/workingJob.vue?2d5b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "jobInfo", "companyName", "companyLogo", "industry", "title", "workingDuration", "currentSalary", "startDate", "location", "department", "referrer", "name", "avatar", "referralDate", "benefits", "onLoad", "onShareAppMessage", "desc", "pic", "onShareTimeline", "imageUrl", "query", "methods", "loadJobInfo", "res", "uni", "icon", "mockLoadData", "setTimeout", "resolve", "contactHR", "content", "success", "phoneNumber", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2FnxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAR;QACA;QACAS;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACAZ;MACAa;MACAC;IACA;EACA;EAEAC;IACA;MACAf;MACAa;MACAC;IACA;IACA;IACA;MACAd;MACAgB;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;kBACArB;kBACAsB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAC;UACA;YACAhC;cACAK;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;gBACAC;gBACAC;gBACAR;cACA;cACAS;cACAC,WACA,QACA,OACA,QACA,MACA,QACA,QACA,QACA;YAEA;UACA;UACAe;QACA;MACA;IACA;IAEA;IACAC;MACAL;QACArB;QACA2B;QACAC;UACA;YACA;YACAP;cACAQ;cAAA;cACAC;gBACAT;kBACArB;kBACAsB;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjOA;AAAA;AAAA;AAAA;AAAs6C,CAAgB,i3CAAG,EAAC,C;;;;;;;;;;;ACA17C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/workingJob.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/workingJob.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./workingJob.vue?vue&type=template&id=d88d1120&\"\nvar renderjs\nimport script from \"./workingJob.vue?vue&type=script&lang=js&\"\nexport * from \"./workingJob.vue?vue&type=script&lang=js&\"\nimport style0 from \"./workingJob.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/workingJob.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workingJob.vue?vue&type=template&id=d88d1120&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workingJob.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workingJob.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <block v-if=\"isload\">\r\n      <view class=\"working-job-page\">\r\n        <!-- 顶部企业信息卡片 -->\r\n        <view class=\"company-card\">\r\n          <view class=\"company-header\">\r\n            <image class=\"company-logo\" :src=\"jobInfo.companyLogo\" mode=\"aspectFit\"></image>\r\n            <view class=\"company-info\">\r\n              <text class=\"company-name\">{{ jobInfo.companyName }}</text>\r\n              <text class=\"company-industry\">{{ jobInfo.industry }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"divider\"></view>\r\n          <view class=\"job-basic-info\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">目前职位</text>\r\n              <text class=\"value\">{{ jobInfo.title }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">工作时长</text>\r\n              <text class=\"value\">{{ jobInfo.workingDuration }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 详细信息卡片 -->\r\n        <view class=\"detail-card\">\r\n          <view class=\"card-title\">职位信息</view>\r\n          <view class=\"info-grid\">\r\n            <view class=\"grid-item\">\r\n              <text class=\"label\">目前薪资</text>\r\n              <text class=\"value salary\">{{ jobInfo.currentSalary }}</text>\r\n            </view>\r\n            <view class=\"grid-item\">\r\n              <text class=\"label\">入职时间</text>\r\n              <text class=\"value\">{{ jobInfo.startDate }}</text>\r\n            </view>\r\n            <view class=\"grid-item\">\r\n              <text class=\"label\">工作地点</text>\r\n              <text class=\"value\">{{ jobInfo.location }}</text>\r\n            </view>\r\n            <view class=\"grid-item\">\r\n              <text class=\"label\">部门</text>\r\n              <text class=\"value\">{{ jobInfo.department }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 推荐信息卡片 -->\r\n        <view class=\"detail-card\">\r\n          <view class=\"card-title\">推荐信息</view>\r\n          <view class=\"referral-info\">\r\n            <view class=\"referrer\">\r\n              <image class=\"avatar\" :src=\"jobInfo.referrer.avatar\" mode=\"aspectFit\"></image>\r\n              <view class=\"referrer-info\">\r\n                <text class=\"name\">{{ jobInfo.referrer.name }}</text>\r\n                <text class=\"title\">{{ jobInfo.referrer.title }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"referral-date\">\r\n              推荐时间：{{ jobInfo.referralDate }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 工作福利卡片 -->\r\n        <view class=\"detail-card\">\r\n          <view class=\"card-title\">工作福利</view>\r\n          <view class=\"benefits-list\">\r\n            <view class=\"benefit-item\" v-for=\"(benefit, index) in jobInfo.benefits\" :key=\"index\">\r\n              <text class=\"benefit-text\">{{ benefit }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 添加悬浮底部按钮 -->\r\n      <view class=\"floating-footer\" @tap=\"contactHR\">\r\n        <view class=\"contact-btn\">\r\n          <text class=\"icon\">👋</text>\r\n          <text class=\"text\">问题联系人力资源</text>\r\n        </view>\r\n      </view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      opt: {},\r\n      loading: false,\r\n      isload: false,\r\n      jobInfo: {\r\n        companyName: '',\r\n        companyLogo: '',\r\n        industry: '',\r\n        title: '',\r\n        workingDuration: '',\r\n        currentSalary: '',\r\n        startDate: '',\r\n        location: '',\r\n        department: '',\r\n        referrer: {\r\n          name: '',\r\n          avatar: '',\r\n          title: ''\r\n        },\r\n        referralDate: '',\r\n        benefits: []\r\n      }\r\n    }\r\n  },\r\n\r\n  onLoad(opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.loadJobInfo();\r\n  },\r\n\r\n  onShareAppMessage() {\r\n    return this._sharewx({\r\n      title: '我的工作信息',\r\n      desc: '查看工作详情',\r\n      pic: ''\r\n    });\r\n  },\r\n\r\n  onShareTimeline() {\r\n    var sharewxdata = this._sharewx({\r\n      title: '我的工作信息',\r\n      desc: '查看工作详情',\r\n      pic: ''\r\n    });\r\n    var query = (sharewxdata.path).split('?')[1];\r\n    return {\r\n      title: sharewxdata.title,\r\n      imageUrl: sharewxdata.imageUrl,\r\n      query: query\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 加载工作信息\r\n    async loadJobInfo() {\r\n      this.loading = true;\r\n      try {\r\n        // 模拟接口调用\r\n        const res = await this.mockLoadData();\r\n        this.jobInfo = res.data;\r\n        this.isload = true;\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '加载失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 模拟数据加载\r\n    mockLoadData() {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          const mockData = {\r\n            data: {\r\n              companyName: '科技有限公司',\r\n              companyLogo: '/static/images/company-logo.png',\r\n              industry: '互联网/软件开发',\r\n              title: '高级前端工程师',\r\n              workingDuration: '1年2个月',\r\n              currentSalary: '25k',\r\n              startDate: '2023-01-15',\r\n              location: '上海市浦东新区',\r\n              department: '技术部-前端组',\r\n              referrer: {\r\n                name: '张三',\r\n                avatar: '/static/images/avatar.png',\r\n                title: '技术总监'\r\n              },\r\n              referralDate: '2022-12-20',\r\n              benefits: [\r\n                '五险一金',\r\n                '年终奖',\r\n                '带薪年假',\r\n                '餐补',\r\n                '交通补助',\r\n                '节日福利',\r\n                '团建活动',\r\n                '免费零食'\r\n              ]\r\n            }\r\n          };\r\n          resolve(mockData);\r\n        }, 1000);\r\n      });\r\n    },\r\n\r\n    // 联系人力资源\r\n    contactHR() {\r\n      uni.showModal({\r\n        title: '联系人力资源',\r\n        content: '是否联系人力资源部门？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 这里可以替换为实际的联系方式\r\n            uni.makePhoneCall({\r\n              phoneNumber: '10086', // 替换为实际的HR电话\r\n              fail: () => {\r\n                uni.showToast({\r\n                  title: '请稍后重试',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.working-job-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(125deg, #000428 0%, #004e92 100%);\r\n  padding: 32rpx 24rpx;\r\n  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));\r\n\r\n  .company-card {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 32rpx;\r\n    padding: 40rpx;\r\n    margin-bottom: 32rpx;\r\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);\r\n    backdrop-filter: blur(20px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 1px;\r\n      background: linear-gradient(90deg, \r\n        rgba(255, 255, 255, 0) 0%,\r\n        rgba(255, 255, 255, 0.3) 50%,\r\n        rgba(255, 255, 255, 0) 100%\r\n      );\r\n    }\r\n\r\n    .company-header {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 32rpx;\r\n\r\n      .company-logo {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        border-radius: 30rpx;\r\n        margin-right: 32rpx;\r\n        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);\r\n        border: 2px solid rgba(255, 255, 255, 0.1);\r\n        background: rgba(255, 255, 255, 0.1);\r\n        padding: 4rpx;\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          transform: scale(1.05);\r\n          box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.3);\r\n        }\r\n      }\r\n\r\n      .company-info {\r\n        flex: 1;\r\n\r\n        .company-name {\r\n          font-size: 44rpx;\r\n          font-weight: 700;\r\n          color: #fff;\r\n          margin-bottom: 12rpx;\r\n          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n          background: linear-gradient(120deg, #fff, #e0e7ff);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n          letter-spacing: 1px;\r\n        }\r\n\r\n        .company-industry {\r\n          font-size: 28rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          padding: 8rpx 24rpx;\r\n          background: rgba(255, 255, 255, 0.1);\r\n          border-radius: 24rpx;\r\n          display: inline-block;\r\n          backdrop-filter: blur(4px);\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n        }\r\n      }\r\n    }\r\n\r\n    .divider {\r\n      height: 1px;\r\n      background: linear-gradient(90deg,\r\n        rgba(255, 255, 255, 0) 0%,\r\n        rgba(255, 255, 255, 0.2) 50%,\r\n        rgba(255, 255, 255, 0) 100%\r\n      );\r\n      margin: 32rpx 0;\r\n    }\r\n\r\n    .job-basic-info {\r\n      .info-item {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 24rpx;\r\n        padding: 24rpx 32rpx;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 20rpx;\r\n        border: 1px solid rgba(255, 255, 255, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          transform: translateX(4rpx);\r\n        }\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .label {\r\n          font-size: 28rpx;\r\n          color: rgba(255, 255, 255, 0.6);\r\n          font-weight: 500;\r\n        }\r\n\r\n        .value {\r\n          font-size: 32rpx;\r\n          color: #fff;\r\n          font-weight: 600;\r\n          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .detail-card {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 32rpx;\r\n    padding: 40rpx;\r\n    margin-bottom: 32rpx;\r\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);\r\n    backdrop-filter: blur(20px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 1px;\r\n      background: linear-gradient(90deg,\r\n        rgba(255, 255, 255, 0) 0%,\r\n        rgba(255, 255, 255, 0.3) 50%,\r\n        rgba(255, 255, 255, 0) 100%\r\n      );\r\n    }\r\n\r\n    .card-title {\r\n      font-size: 36rpx;\r\n      font-weight: 700;\r\n      color: #fff;\r\n      margin-bottom: 36rpx;\r\n      position: relative;\r\n      padding-left: 32rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        width: 8rpx;\r\n        height: 36rpx;\r\n        background: linear-gradient(180deg, #60a5fa, #3b82f6);\r\n        border-radius: 4rpx;\r\n        box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.5);\r\n      }\r\n    }\r\n\r\n    .info-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 24rpx;\r\n\r\n      .grid-item {\r\n        padding: 28rpx;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 24rpx;\r\n        border: 1px solid rgba(255, 255, 255, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          transform: translateY(2rpx);\r\n        }\r\n\r\n        .label {\r\n          font-size: 26rpx;\r\n          color: rgba(255, 255, 255, 0.6);\r\n          margin-bottom: 12rpx;\r\n          display: block;\r\n        }\r\n\r\n        .value {\r\n          font-size: 32rpx;\r\n          color: #fff;\r\n          font-weight: 600;\r\n          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n\r\n          &.salary {\r\n            background: linear-gradient(120deg, #ffd700, #ffa500);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            font-size: 40rpx;\r\n            font-weight: 700;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .referral-info {\r\n      .referrer {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 24rpx;\r\n        padding: 28rpx;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 24rpx;\r\n        border: 1px solid rgba(255, 255, 255, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          transform: translateY(2rpx);\r\n        }\r\n\r\n        .avatar {\r\n          width: 96rpx;\r\n          height: 96rpx;\r\n          border-radius: 50%;\r\n          margin-right: 24rpx;\r\n          border: 2px solid rgba(255, 255, 255, 0.2);\r\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\r\n          transition: all 0.3s ease;\r\n\r\n          &:active {\r\n            transform: scale(1.1);\r\n            border-color: rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n\r\n        .referrer-info {\r\n          .name {\r\n            font-size: 32rpx;\r\n            color: #fff;\r\n            font-weight: 600;\r\n            margin-bottom: 8rpx;\r\n            display: block;\r\n            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n          }\r\n\r\n          .title {\r\n            font-size: 26rpx;\r\n            color: rgba(255, 255, 255, 0.6);\r\n            padding: 6rpx 20rpx;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border-radius: 20rpx;\r\n            display: inline-block;\r\n            backdrop-filter: blur(4px);\r\n            border: 1px solid rgba(255, 255, 255, 0.1);\r\n          }\r\n        }\r\n      }\r\n\r\n      .referral-date {\r\n        font-size: 26rpx;\r\n        color: rgba(255, 255, 255, 0.6);\r\n        padding: 20rpx;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 20rpx;\r\n        text-align: center;\r\n        border: 1px solid rgba(255, 255, 255, 0.1);\r\n      }\r\n    }\r\n\r\n    .benefits-list {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 20rpx;\r\n\r\n      .benefit-item {\r\n        padding: 16rpx 32rpx;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 24rpx;\r\n        border: 1px solid rgba(255, 255, 255, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(255, 255, 255, 0.1);\r\n          transform: translateY(2rpx);\r\n        }\r\n\r\n        .benefit-text {\r\n          font-size: 28rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          font-weight: 500;\r\n          background: linear-gradient(120deg, #fff, #e0e7ff);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  @keyframes slideUp {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(40rpx);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .company-card {\r\n    animation: slideUp 0.6s ease-out forwards;\r\n  }\r\n\r\n  .detail-card {\r\n    opacity: 0;\r\n    animation: slideUp 0.6s ease-out forwards;\r\n  }\r\n\r\n  .detail-card:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n  }\r\n\r\n  .detail-card:nth-child(3) {\r\n    animation-delay: 0.3s;\r\n  }\r\n\r\n  .detail-card:nth-child(4) {\r\n    animation-delay: 0.4s;\r\n  }\r\n}\r\n\r\n.floating-footer {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 24rpx 32rpx calc(env(safe-area-inset-bottom) + 24rpx);\r\n  z-index: 999;\r\n  pointer-events: none;\r\n  background: linear-gradient(180deg, \r\n    rgba(0, 0, 0, 0) 0%,\r\n    rgba(0, 0, 0, 0.3) 100%\r\n  );\r\n  backdrop-filter: blur(10px);\r\n\r\n  .contact-btn {\r\n    height: 88rpx;\r\n    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n    border-radius: 44rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);\r\n    pointer-events: auto;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n    animation: float 3s ease-in-out infinite;\r\n    \r\n    @keyframes float {\r\n      0%, 100% {\r\n        transform: translateY(0);\r\n      }\r\n      50% {\r\n        transform: translateY(-6rpx);\r\n      }\r\n    }\r\n    \r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 50%;\r\n      background: linear-gradient(\r\n        180deg,\r\n        rgba(255, 255, 255, 0.2) 0%,\r\n        rgba(255, 255, 255, 0) 100%\r\n      );\r\n    }\r\n    \r\n    &:active {\r\n      transform: scale(0.98);\r\n      box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);\r\n    }\r\n\r\n    .icon {\r\n      font-size: 32rpx;\r\n      margin-right: 12rpx;\r\n      animation: wave 2s ease-in-out infinite;\r\n      \r\n      @keyframes wave {\r\n        0%, 100% {\r\n          transform: rotate(0deg);\r\n        }\r\n        50% {\r\n          transform: rotate(15deg);\r\n        }\r\n      }\r\n    }\r\n\r\n    .text {\r\n      font-size: 30rpx;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      letter-spacing: 1px;\r\n      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workingJob.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./workingJob.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115047343\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}