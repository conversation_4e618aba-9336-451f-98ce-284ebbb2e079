{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?3b24", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?303d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?52e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?d27c", "uni-app:///pagesExb/training/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?b83f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/training/index.vue?3a19"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nodata", "nomore", "keyword", "datalist", "pagenum", "onLoad", "console", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "bid", "kcid", "page", "pagesize", "uni", "title", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiG9wB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,iDACA,+CACA,oDACA,+CACA;EAEA;EACAC;IACA;IACA;IACA;IACA;MACA;IACA;;IAEA;IACAC;IAEA;EACA;EACAC;IACA;IACAD;IACA;EACA;EACAE;IACA;MACA;MACAF;MACA;MACA;IACA;EACA;EAEAG;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAJ;MAEAK;MACAA;MACAA;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAd;MACA;QACAS;;QAEA;QACAL;QAEA;UACA;UACAA;UACAM;UACA;QACA;QAEA;QACA;UACAD;UACAA;UAEAM;YACAC;UACA;UAEAP;UACA;YACAA;YACA;YACAL;UACA;UACAK;QACA;UACA;YACAA;YACA;YACAL;UACA;YACA;YACA;YACAK;;YAEA;YACAL;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACAR;;MAEA;MACAL;MAEAK;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClOA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/training/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/training/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=75453aa3&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/training/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=75453aa3&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的训练营\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"training_list\">\r\n\t\t\t<!--横排-->\r\n\t\t\t<view v-if=\"listtype=='0'\" class=\"training-itemlist\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExb/training/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"training-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"training-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.subname\">\r\n\t\t\t\t\t\t{{item.subname}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"course-info\" v-if=\"item.kcname\">\r\n\t\t\t\t\t\t<text class=\"course-tag\">课程</text>\r\n\t\t\t\t\t\t<text class=\"course-name\">{{item.kcname}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"permission-status\" v-if=\"item.need_buy == 1\">\r\n\t\t\t\t\t\t<text class=\"permission-tag\" :class=\"item.has_permission ? 'has-permission' : 'no-permission'\">\r\n\t\t\t\t\t\t\t{{item.has_permission ? '已解锁' : '需购买课程'}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--双排-->\r\n\t\t\t<view v-if=\"listtype=='1'\" class=\"training-item2\" v-for=\"(item,index) in datalist\" :key=\"item.id\" :style=\"{marginRight:index%2==0?'2%':'0'}\" @click=\"goto\" :data-url=\"'/pagesExb/training/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"training-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"training-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.subname\">\r\n\t\t\t\t\t\t{{item.subname}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"course-info\" v-if=\"item.kcname\">\r\n\t\t\t\t\t\t<text class=\"course-tag\">课程</text>\r\n\t\t\t\t\t\t<text class=\"course-name\">{{item.kcname}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"permission-status\" v-if=\"item.need_buy == 1\">\r\n\t\t\t\t\t\t<text class=\"permission-tag\" :class=\"item.has_permission ? 'has-permission' : 'no-permission'\">\r\n\t\t\t\t\t\t\t{{item.has_permission ? '已解锁' : '需购买课程'}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--单排-->\r\n\t\t\t<view v-if=\"listtype=='3'\" class=\"training-item1\" v-for=\"(item,index) in datalist\" :key=\"item.id\" @click=\"goto\" :data-url=\"'/pagesExb/training/detail?id='+item.id\">\r\n\t\t\t\t<view class=\"training-pic\">\r\n\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"training-info\">\r\n\t\t\t\t\t<view class=\"p1\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"p3\" :style=\"set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''\" v-if=\"item.subname\">\r\n\t\t\t\t\t\t{{item.subname}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"p2\">\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\" class=\"flex1\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text style=\"overflow:hidden\">阅读 {{item.readcount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"course-info\" v-if=\"item.kcname\">\r\n\t\t\t\t\t\t<text class=\"course-tag\">课程</text>\r\n\t\t\t\t\t\t<text class=\"course-name\">{{item.kcname}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"permission-status\" v-if=\"item.need_buy == 1\">\r\n\t\t\t\t\t\t<text class=\"permission-tag\" :class=\"item.has_permission ? 'has-permission' : 'no-permission'\">\r\n\t\t\t\t\t\t\t{{item.has_permission ? '已解锁' : '需购买课程'}}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<nodata v-if=\"nodata\" text=\"暂无训练营内容\"></nodata>\r\n\t<nomore v-if=\"nomore\" text=\"没有更多训练营了\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tnodata:false,\r\n\t\t\tnomore:false,\r\n\t\t\tkeyword:'',\r\n      datalist: [],\r\n      pagenum: 1,\r\n      datalist: [],\r\n      kcid: 0,\r\n\t\t\tbid: 0,\r\n\t\t\tlisttype:0,\r\n            set:'',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.kcid = this.opt.kcid || 0;\t\r\n\t\tthis.bid = this.opt.bid || 0;\r\n        if(this.opt.keyword) {\r\n        \tthis.keyword = this.opt.keyword;\r\n        }\r\n        \r\n        // 2025-01-03 22:55:53,565-INFO-[training_index][onLoad_001] 初始化训练营列表页面\r\n        console.log('2025-01-03 22:55:53,565-INFO-[training_index][onLoad_001] 初始化训练营列表页面, kcid:', this.kcid);\r\n        \r\n    this.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\t// 2025-01-03 22:55:53,565-INFO-[training_index][onPullDownRefresh_001] 下拉刷新训练营列表\r\n\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_index][onPullDownRefresh_001] 下拉刷新训练营列表');\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nomore && !this.nodata) {\r\n      // 2025-01-03 22:55:53,565-INFO-[training_index][onReachBottom_001] 上拉加载更多训练营\r\n      console.log('2025-01-03 22:55:53,565-INFO-[training_index][onReachBottom_001] 上拉加载更多训练营, pagenum:', this.pagenum + 1);\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var keyword = that.keyword;\r\n      var kcid = that.kcid;\r\n      \r\n\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_index][getdata_001] 开始获取训练营数据\r\n\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_001] 开始获取训练营数据, pagenum:', pagenum, 'kcid:', kcid, 'keyword:', keyword);\r\n\t\t\t\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\t\r\n      app.post('ApiKechengTraining/gettraininglist', {\r\n      \tbid: that.bid,\r\n      \tkcid: kcid,\r\n      \tpage: pagenum,\r\n      \tpagesize: 10,\r\n      \tkeyword: keyword\r\n      }, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\t// 2025-01-03 22:55:53,565-INFO-[training_index][getdata_002] 获取训练营数据响应\r\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_002] 获取训练营数据响应, status:', res.status, 'data_length:', res.data ? res.data.length : 0);\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t// 2025-01-03 22:55:53,565-ERROR-[training_index][getdata_003] 获取训练营数据失败\r\n\t\t\t\t\tconsole.error('2025-01-03 22:55:53,565-ERROR-[training_index][getdata_003] 获取训练营数据失败:', res.msg);\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.listtype = res.listtype || 0;\r\n                    that.set = res.set || {};\r\n\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.title || '训练营'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_004] 没有训练营数据\r\n            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_004] 没有训练营数据');\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_005] 没有更多训练营数据\r\n            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_005] 没有更多训练营数据');\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n            \r\n            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_006] 加载更多训练营数据成功\r\n            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_006] 加载更多训练营数据成功, 新增:', data.length, '总数:', newdata.length);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    searchConfirm: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword;\r\n      \r\n      // 2025-01-03 22:55:53,565-INFO-[training_index][searchConfirm_001] 搜索训练营\r\n      console.log('2025-01-03 22:55:53,565-INFO-[training_index][searchConfirm_001] 搜索训练营, keyword:', keyword);\r\n      \r\n      that.getdata();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage{background:#f6f6f7}\r\n.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}\r\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}\r\n.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}\r\n\r\n.training_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}\r\n.training_list .training-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}\r\n.training_list .training-item1 .training-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}\r\n.training_list .training-item1 .training-pic .image{width: 100%;height:auto}\r\n.training_list .training-item1 .training-info {padding:10rpx 20rpx 20rpx 20rpx;}\r\n.training_list .training-item1 .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.training_list .training-item1 .training-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}\r\n.training_list .training-item1 .training-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}\r\n.training_list .training-item1 .training-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.training_list .training-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}\r\n.training_list .training-item2 .training-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}\r\n.training_list .training-item2 .training-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.training_list .training-item2 .training-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}\r\n.training_list .training-item2 .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.training_list .training-item2 .training-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}\r\n\r\n.training_list .training-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}\r\n.training_list .training-itemlist .training-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}\r\n.training_list .training-itemlist .training-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.training_list .training-itemlist .training-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}\r\n.training_list .training-itemlist .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}\r\n.training_list .training-itemlist .training-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}\r\n\r\n.p3{color:#8c8c8c;font-size:26rpx;line-height:40rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin:6rpx 0;}\r\n\r\n/* 课程信息样式 */\r\n.course-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 8rpx 0;\r\n}\r\n.course-tag {\r\n  background: linear-gradient(45deg, #FF6B35, #F7931E);\r\n  color: #fff;\r\n  font-size: 20rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 12rpx;\r\n  font-weight: bold;\r\n}\r\n.course-name {\r\n  color: #666;\r\n  font-size: 24rpx;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 权限状态样式 */\r\n.permission-status {\r\n  margin: 8rpx 0;\r\n}\r\n.permission-tag {\r\n  font-size: 22rpx;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-weight: bold;\r\n}\r\n.permission-tag.has-permission {\r\n  background: linear-gradient(45deg, #4CAF50, #45a049);\r\n  color: #fff;\r\n}\r\n.permission-tag.no-permission {\r\n  background: linear-gradient(45deg, #ff9800, #f57c00);\r\n  color: #fff;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024441\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}