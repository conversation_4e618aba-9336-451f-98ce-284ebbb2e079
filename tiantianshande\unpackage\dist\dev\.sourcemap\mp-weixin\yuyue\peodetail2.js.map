{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?7837", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?1dac", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?951b", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?2626", "uni-app:///yuyue/peodetail2.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?0cef", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail2.vue?0a94"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "type", "keyword", "nodata", "curTopIndex", "set", "onLoad", "app", "console", "that", "onPullDownRefresh", "methods", "getdata", "id", "subid", "longitude", "latitude", "switchTopTab", "getdatalist", "pagenum", "field", "order", "uni", "scrolltolower"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoDnxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAT;MACAU;IAEA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MACAC;MACAC;MACAA;MACAA;IACA;IACA;EACA;;EACAC;IAEA;EACA;EACAC;IACAC;MACA;MACAH;MACAA;MACA;MACA;MACA;MAEAF;QAAAM;QAAAC;QAAAC;QAAAC;MAAA;QACAP;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAT;MACAA;MACAA;MACAF;QAAAH;QAAAe;QAAAC;QAAAC;QAAAR;MAAA;QACAJ;QACAa;QACA;QACA;UACAd;UACA;YACAC;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IAEA;IACAc;MAEA;QAEA;QACA;MAEA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/peodetail2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/peodetail2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peodetail2.vue?vue&type=template&id=17ac8dd7&\"\nvar renderjs\nimport script from \"./peodetail2.vue?vue&type=script&lang=js&\"\nexport * from \"./peodetail2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peodetail2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/peodetail2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail2.vue?vue&type=template&id=17ac8dd7&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.curTopIndex == 0 && item.price > 0 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail2.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"top flex\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"data.avatar\"> </view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"t1\"><text  class=\"bold\">{{data.name}}</text><text>{{data.juli}}km</text></view>\r\n\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t<view class=\"t11\"><text>服务单量：{{data.recentOrderTotalDesc}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t11\">服务状态：<text class=\"statusdesc\">{{data.statusDesc}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t11\">最后上线时间：{{data.lastOnlineTime}} </view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"desc\" v-if=\"data.desc\">\r\n\t\t\t\t{{data.desc}}\r\n\t\t\t</view>\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"tab\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == 0 ? 'on' : '') \" @tap=\"switchTopTab\" :data-index=\"0\">服务项目 <view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"curTopIndex==0 && item.price>0\" v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content2 flex\" :data-id=\"item.id\">\r\n\t\t\t\t\t<view class=\"f1\" @click=\"goto\"  :data-url=\"'product2?skillid='+item.skillId+'&masterid='+data.id\" >\r\n\t\t\t\t\t\t<view class=\"headimg2\"><image :src=\"set.pic\" /></view>\r\n\t\t\t\t\t\t<view class=\"text1\">\t\r\n\t\t\t\t\t\t\t<view class=\"text2 flex\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.firstCategoryName}} {{item.secondCategoryName}} </text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"text3\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t4\"><text class=\"price\">{{item.price}}{{item.unit}}</text> </text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t<view  class=\"textdesc\"><text>{{set.desc}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"flex\" style=\"justify-content: space-between;\">\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 24rpx; margin: 20rpx 30rpx; \">服务类型：{{item.serviceType}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"yuyue\" :style=\"{background:t('color1')}\"  @click=\"goto\" :data-url=\"'product2?skillid='+item.skillId+'&masterid='+data.id+'&masterName='\">预约</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t</view>\t\t\t\t\r\n\t\t\t\t</view>\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<view style=\"height:140rpx\"></view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tdatalist: [],\r\n\t\t\ttype: \"\",\r\n\t\t\tkeyword:'',\r\n\t\t\tnodata:false,\r\n\t\t\tcurTopIndex:0,\r\n\t\t\tdata:[],\r\n\t\t\tset:[]\r\n\t\t\t\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.type = this.opt.type || '';\r\n\t\tthis.getdata();\r\n\t\tvar that = this;\r\n\t\t//if(that.latitude=='' && that.longitude==''){\r\n\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\tthat.getdata();\r\n\t\t\t});\r\n\t\t//}\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tvar subid = this.opt.subid || 0;\r\n\r\n\t\t\tapp.get('ApiYuyue2/peodetail', {id:id,subid:subid,longitude: that.longitude,latitude: that.latitude}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.data = data\r\n\t\t\t\tthat.set = res.set\r\n\t\t\t\tthat.datalist = data.skillApiVOList\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  this.curTopIndex = index;\r\n\t\t  this.datalist = [];\r\n\t\t  this.getdatalist(true);\r\n\t\t},   \r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tvar order = that.order;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.post('ApiYuyue/getdlist', {curTopIndex:that.curTopIndex,pagenum: pagenum,field: field,order: order,id:id}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tconsole.log(pagenum);\r\n\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t});\r\n\t\t \r\n\t\t},\r\n\t\tscrolltolower: function () {\r\n\t\t     \r\n\t\t\tif (!this.nomore) {\r\n\t\t   \r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t \r\n\t\t\t}\r\n\t\t \r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ background: #fff; padding: 30rpx;}\r\n.headimg{ width: 240rpx; height: 240rpx;margin-right: 20rpx;}\r\n.headimg image{ width: 240rpx; height: 240rpx;border-radius: 10rpx ; }\r\n.content .top { width: 100%; } \r\n.content .top .fl{ width: 100%; }\r\n.right{ width: 100%;}\r\n.right .t1{ display: flex; color:#323232;  font-size: 36rpx; font-weight: bold; justify-content: space-between; margin-top: 10rpx; }\r\n.right .t2{ color:#999;  font-size: 24rpx; }\r\n.right .t3{ margin-top: 20rpx;color:#999;font-size: 24rpx; background: #F8FBFF; border-radius: 10rpx; line-height: 40rpx; padding: 20rpx;}\r\n.right .t3 .bold{ font-size: 36rpx; color: #323232; font-weight: bold;margin-left: 4rpx;}\r\n.right .t11{ font-size: 26rpx;}\r\n.statusdesc{ color:#06A051; }\r\n\r\n.desc{ color: #6d6e74; font-size: 26rpx; margin-top: 60rpx;}\r\n.list .headimg2{ width: 220rpx; height: 220rpx;border-radius: 10rpx ; }\r\n.tab{ margin-top: 80rpx; display: flex; }\r\n.tab .item{ padding-right:20rpx; color: #323232;font-size: 28rpx; font-weight:bold; margin-right: 40rpx; line-height: 60rpx; overflow: hidden;position:relative; }\r\n.tab .after{display:none;position:absolute;left:25%;margin-left:-20rpx;bottom:0rpx;height:6rpx;border-radius:1.5px;width:80rpx}\r\n.tab .on .after{display:block}\r\n\r\n\r\n.content2{width:100%;background:#fff;border-radius:5px; justify-content: space-between; margin-top: 40rpx; border-bottom: 1px solid #EEEEEE;}\r\n.content2 .f1{display:flex;align-items:center; flex-wrap: wrap;}\r\n.content2 .f1 image{ width: 210rpx; height: 210rpx; border-radius: 10rpx;}\r\n.content2 .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:28rpx;margin-left:10rpx; margin-top: 5rpx; }\r\n.content2 .f1 .t2{color:#999999;font-size:28rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content2 .f1 .t3{ margin-left:10rpx;display: block; height: 40rpx;line-height: 40rpx;}\r\n.content2 .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}\r\n.content2 .f3{height:96rpx;display:flex;align-items:center}\r\n.content2 .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content2 .radio .radio-img{width:100%;height:100%}\r\n.content2 .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\r\n.list .text1{ width: 67%;}\r\n.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx; margin-bottom: 10rpx; justify-content: space-between; margin-right: 20rpx;}\r\n.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3 .t5{ margin-left: 20rpx;}\r\n.text3 .t5 text{ color:#7A83EC}\r\n.text3 .t4 text{ color:#FF5347}\r\n.text3 .t4 { color:#FF5347}\r\n.textdesc{ font-size: 20rpx; padding: 10rpx 20rpx; background:#F8FBFF; margin: 0 20rpx;}\r\n.text3 .t4 .price{ font-weight: bold;font-size: 32rpx; line-height: 0rpx;}\r\n.yuyue{ background: #7A83EC; width:126rpx;height: 50rpx; line-height: 50rpx; margin-top: 10rpx;; padding: 0 10rpx; color:#fff; \r\nborder-radius:28rpx; ; font-size: 20rpx; text-align: center;  margin-bottom: 10rpx;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115043768\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}