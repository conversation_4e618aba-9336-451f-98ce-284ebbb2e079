{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?8723", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?e300", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?a8db", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?2f55", "uni-app:///pagesExt/order/refundDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?f4e0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refundDetail.vue?08c9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "detail", "prolist", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "toclose", "setTimeout", "orderCollect", "orderid", "openLocation", "uni", "latitude", "longitude", "name", "scale"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkHrxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QAEAA;MACA;IACA;IACAG;MACA;MACA;MACAF;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAG;YACAJ;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;MACAJ;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAG;YACAJ;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/refundDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/refundDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./refundDetail.vue?vue&type=template&id=23d44f04&\"\nvar renderjs\nimport script from \"./refundDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./refundDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./refundDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/refundDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refundDetail.vue?vue&type=template&id=23d44f04&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refundDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refundDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop_refund.jpg);background-size:100%'\">\r\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==0\">\r\n\t\t\t\t<view class=\"t1\">已取消</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==1\">\r\n\t\t\t\t<view class=\"t1\">审核中</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==2\">\r\n\t\t\t\t<view class=\"t1\">审核通过，已退款</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==3\">\r\n\t\t\t\t<view class=\"t1\">驳回</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status==4\">\r\n\t\t\t\t<view class=\"t1\">审核通过，待退货</view>\r\n\t\t\t\t<view class=\"t2\">联系商家进行退货</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\">\r\n\t\t\t<image :src=\"detail.binfo.logo\" style=\"width:36rpx;height:36rpx;\"></image>\r\n\t\t\t<view class=\"flex1\" decode=\"true\" space=\"true\" style=\"padding-left:16rpx\">{{detail.binfo.name}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"product\">\r\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"content\">\r\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.proid\">\r\n\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\r\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.refund_num}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">退货单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.refund_ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\" @tap=\"goto\" :data-url=\"'detail?id='+detail.orderid\">{{detail.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">申请时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">类型</text>\r\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_type_label}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">申请退款金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.refund_money}}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t<text class=\"t2 grey\" v-if=\"detail.refund_status==0\">已取消</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==4\">审核通过，待退货</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">退款原因</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.refund_reason}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\r\n\t\t\t\t<text class=\"t1\">审核备注</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.refund_checkremark}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_pics\">\r\n\t\t\t\t<text class=\"t1\">图片</text>\r\n\t\t\t\t<view id=\"content_picpreview\" class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in detail.refund_pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\" v-if=\"detail.freight_type==11\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">发货地址</text>\r\n\t\t\t\t<text class=\"t2\">¥{{detail.freight_content.send_address}} - {{detail.freight_content.send_tel}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">收货地址</text>\r\n\t\t\t\t<text class=\"t2\">¥{{detail.freight_content.receive_address}} - {{detail.freight_content.receive_tel}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<block v-if=\"detail.refund_status==1 || detail.refund_status==4\">\r\n\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">取消</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      prodata: '',\r\n      detail: \"\",\r\n      prolist: \"\",\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiOrder/refundDetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\tthat.prolist = res.prolist;\r\n\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    toclose: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定要关闭该退款单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiOrder/refundOrderClose', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    orderCollect: function (e) {\r\n      var that = this;\r\n      var orderid = e.currentTarget.dataset.id;\r\n      app.confirm('确定要收货吗?', function () {\r\n\t\t\t\tapp.showLoading('收货中');\r\n        app.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\t\topenLocation:function(e){\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\tvar address = e.currentTarget.dataset.address;\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t\t})\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n.ordertop .f1 .t2{font-size:24rpx}\r\n\r\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\r\n.address .img{width:40rpx}\r\n.address image{width:40rpx; height:40rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n.address .info .t2{font-size:24rpx;color:#999}\r\n\r\n.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content image{ width: 140rpx; height: 140rpx;}\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n.orderinfo .item .grey{color:grey}\r\n\r\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n\r\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n\r\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\r\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.layui-imgbox-close image{width:100%;height:100%}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refundDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refundDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098809\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}