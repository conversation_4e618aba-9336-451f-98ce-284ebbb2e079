{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?614e", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?267f", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?2175", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?3fd6", "uni-app:///yuyue/cycle/productDetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?62cd", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/productDetail.vue?e80c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "isfavorite", "product", "pics_list", "pagecontent", "business", "current", "sharetypevisible", "showposter", "posterpic", "kfurl", "onLoad", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "path", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "id", "res", "console", "uni", "desc", "swiper<PERSON><PERSON>e", "tobuy", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+HtxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;QAAAC;MAAA;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IAAA;IACA;MAAAH;MAAAC;MAAAC;IAAA;IACA;IACA;MACAF;MACAI;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACA;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;YACAA;UACA;UACA;QACA;;QAEA;QACA;UACAE;QACA;QAEAH;QACA;QACA;UACAA;QACA;UACAA;UACAI;QACA;QACAJ;QACAA;;QAEAK;UACAb;QACA;;QAEA;QACAQ;QACA;UACAA;QACA;QACA;UACAA;QACA;QAEAA;UAAAR;UAAAc;UAAAb;QAAA;MACA;IACA;;IACAc;MACA;IACA;IACAC;MACA;MACA;MACAP;IACA;IACAQ;MACA;MACA;MACAR;MACA;MACAA;QAAAS;QAAAC;MAAA;QACAV;QACA;UACAD;QACA;QACAC;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAd;MACAA;MACAC;MACA;MACAA;QAAAS;MAAA;QACAT;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAe;MACA;IACA;IACAC;MACAf;MACA;IACA;IACAgB;MACA;MACAZ;QACAa;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;YACA;YACAhB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjSA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/cycle/productDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/cycle/productDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./productDetail.vue?vue&type=template&id=798cd7b7&\"\nvar renderjs\nimport script from \"./productDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./productDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./productDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/cycle/productDetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./productDetail.vue?vue&type=template&id=798cd7b7&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.product.pics_list.length : null\n  var g1 = _vm.isload && g0 > 1 ? _vm.product.pics_list.length : null\n  var m0 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 =\n    _vm.isload && _vm.business && _vm.business.id ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.business && _vm.business.id ? _vm.t(\"color1rgb\") : null\n  var m4 = _vm.isload && _vm.product.status == 1 ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m6 =\n    _vm.isload && _vm.sharetypevisible && !(m5 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m7 =\n    _vm.isload && _vm.sharetypevisible && !(m5 == \"app\") && !(m6 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./productDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./productDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<!-- Swiper for product images -->\r\n\t\t<view class=\"swiper-container\">\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics_list\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item\" :data-urls=\"product.pics_list\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\" v-if=\"product.pics_list.length > 1\">{{current+1}}/{{product.pics_list.length}}</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Price and Cycle Info -->\r\n\t\t<view class=\"collage_title flex-bt\">\r\n\t\t\t<text>\r\n\t\t\t\t<text class=\"price\">￥{{product.sell_price}}<text style=\"font-size:24rpx;color:#999\">/期</text></text>\r\n\t\t\t\t<text class=\"m-price\" v-if=\"product.market_price > 0\">￥{{product.market_price}}</text>\r\n\t\t\t</text>\r\n\t\t\t<view :style=\"{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}\" class=\"ps_title flex-y-center\">共{{product.total_period}}期</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Product Header -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"lef\">\r\n\t\t\t\t\t<text>{{product.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t<image src=\"/static/img/share.png\"></image>\r\n\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t<view class=\"f2\">已售:{{product.sales}}</view>\r\n\t\t\t\t<!-- 库存信息暂时不明确，周期服务可能没有传统库存 -->\r\n\t\t\t\t<!-- <view class=\"f2\">库存:{{product.stock}}</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Shop Info (if needed) -->\r\n\t\t<view class=\"shop\" v-if=\"business && business.id\">\r\n\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"p4\" :style=\"{background: 'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"'/pages/business/index?id='+business.id\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- Product Description -->\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">服务描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:120rpx;\"></view>\r\n\r\n\t\t<!-- Bottom Bar -->\r\n\t\t<view class=\"bottombar flex-row flex-xy-center\" :class=\"menuindex>-1 ? 'tabbarbot' : 'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"cart flex-col flex-x-center flex-y-center\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"cart flex-col flex-x-center flex-y-center\" v-else open-type=\"contact\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t</button>\r\n\t\t\t<view class=\"favorite flex-col flex-x-center flex-y-center\" @tap=\"addfavorite\">\r\n\t\t\t\t<image class=\"img\" :src=\"'/static/img/shoucang' + (isfavorite?'2':'') + '.png'\"/>\r\n\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tobuy flex1\" @tap=\"tobuy\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t<text>立即购买</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Share Options Popup -->\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/sharepic.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Poster Popup -->\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" src=\"/static/img/close.png\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tisfavorite: false,\r\n\t\t\tproduct:{ pics_list: [] }, // Initialize with empty list\r\n      pagecontent: [], // Initialize as empty array for dp component\r\n\t\t\tbusiness:{},\r\n      current: 0, // Swiper index\r\n      sharetypevisible: false,\r\n      showposter: false,\r\n      posterpic: \"\",\r\n\t\t\tkfurl:'',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic,path:'/tiantianshande/yuyue/cycle/productDetail?id=' + this.product.id});\r\n\t},\r\n\tonShareTimeline:function(){ // Share to Moments\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic,path:'/tiantianshande/yuyue/cycle/productDetail?id=' + this.product.id});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id;\r\n\t\t\tthat.loading = true;\r\n\t\t\t// Use the correct API endpoint\r\n\t\t\tapp.get('ApiPeriodicService/productDetail', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg, function(){\r\n\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// Ensure pics_list is always an array\r\n\t\t\t\tif (!res.data.pics_list) {\r\n\t\t\t\t\tres.data.pics_list = res.data.pic ? [res.data.pic] : [];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.product = res.data;\r\n\t\t\t\t// Parse product detail if it's a JSON string\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthat.pagecontent = res.data.detail ? JSON.parse(res.data.detail) : [];\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tthat.pagecontent = []; \r\n\t\t\t\t\tconsole.error(\"Failed to parse product detail JSON:\", e);\r\n\t\t\t\t}\r\n\t\t\t\tthat.business = res.business || {}; // Handle potential missing business data\r\n\t\t\t\tthat.isfavorite = res.isfavorite || false; // Handle potential missing favorite status\r\n\t\t\t\t\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: res.data.name\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// Set up customer service URL\r\n\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid='+ (that.business.id || 0); // Default to platform if no business ID\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded({title:res.data.name, desc:res.data.fuwupoint, pic:res.data.pic}); // Loaded callback\r\n\t\t\t});\r\n\t\t},\r\n    swiperChange: function (e) {\r\n      this.current = e.detail.current;\r\n    },\r\n    tobuy: function () {\r\n      // Navigate to the buy page, passing the product ID\r\n\t\t\t// Use the new buy page path\r\n      app.goto('/yuyue/cycle/buy?product_id=' + this.product.id);\r\n    },\r\n    addfavorite: function () {\r\n      var that = this;\r\n      var proid = that.product.id;\r\n\t\t\tapp.showLoading('操作中');\r\n\t\t\t// Use the correct API for periodic service favorites\r\n      app.post('ApiPeriodicService/addfavorite', {proid: proid, type: 'periodic_service'}, function (data) { \r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          that.isfavorite = !that.isfavorite;\r\n        }\r\n        app.success(data.msg);\r\n      });\r\n    },\r\n    shareClick: function () {\r\n      this.sharetypevisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.sharetypevisible = false;\r\n    },\r\n    showPoster: function () {\r\n      var that = this;\r\n      that.showposter = true;\r\n      that.sharetypevisible = false;\r\n      app.showLoading('努力生成中');\r\n\t\t\t// Use the correct API for periodic service posters\r\n\t\t\tapp.post('ApiPeriodicService/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.alert(data.msg);\r\n        } else {\r\n          that.posterpic = data.poster;\r\n        }\r\n      });\r\n    },\r\n    posterDialogClose: function () {\r\n      this.showposter = false;\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false;\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\tsharedata.summary = that.product.fuwupoint || '';\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/' + 'tiantianshande/yuyue/cycle/productDetail?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\t// Customize share content based on global settings if needed\r\n\t\t\t\t\t\t// ... (similar logic as in the original file if required)\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n/* Reuse styles from tiantianshande/pagesExt/cycle/product.vue */\r\n.container { background-color: #f8f8f8; }\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.collage_title{width:100%;height:110rpx;display:flex;align-items:center;padding:0 30rpx;background-color: #fff;margin-bottom: 20rpx;}\r\n.collage_title .price {font-size:50rpx;color:#FF5043;font-weight: 700;}\r\n.m-price{margin-left: 10rpx;font-size: 24rpx;color: #aaa;text-decoration: line-through;}\r\n.ps_title{height: 35rpx;background: #FFDED9;border-radius: 4rpx;padding: 0 10rpx;font-size: 20rpx;font-family: PingFang SC;color: #FF3143;margin-left: 20rpx;line-height: 35rpx;}\r\n\r\n.header {width: 100%;padding: 0 3%;background: #fff; border-radius: 10rpx; margin: 0 3% 20rpx 3%; width: 94%; }\r\n.header .title {padding: 20rpx 0px;line-height:44rpx;font-size:32rpx;display:flex; border-bottom: 1px solid #f5f5f5;}\r\n.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}\r\n.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:1px solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center; justify-content: center;}\r\n.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}\r\n\r\n.sales_stock{ display: flex; justify-content: space-between; height: 80rpx; line-height: 80rpx; font-size: 24rpx; color: #777777; padding: 0 10rpx;}\r\n\r\n.shop{display:flex;align-items:center;width: 94%; background: #fff; margin: 0 3% 20rpx 3%; padding: 20rpx 3%;position: relative; min-height: 100rpx; border-radius: 10rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;background-color: #fff; padding: 20rpx; width: 94%; margin: 0 3%; border-radius: 10rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff; padding: 10rpx 0; border-top: 1px solid #eee;}\r\n.bottombar .favorite{width: 18%;color:#707070;font-size:26rpx}\r\n.bottombar .favorite .img{ width:44rpx;height:44rpx}\r\n.bottombar .favorite .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:2rpx}\r\n.bottombar .cart{width: 18%;font-size:26rpx;color:#707070}\r\n.bottombar .cart .img{ width:44rpx;height:44rpx}\r\n.bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:2rpx}\r\n.bottombar .tobuy{font-weight: bold;height: 80rpx;color: #fff; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin: 0 30rpx 0 15rpx;border-radius: 40rpx;}\r\n\r\n/* Popups */\r\n.popup__container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 100; }\r\n.popup__overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); }\r\n.popup__modal { position: absolute; left: 0; right: 0; bottom: 0; background-color: #fff; border-top-left-radius: 20rpx; border-top-right-radius: 20rpx; padding-bottom: env(safe-area-inset-bottom); max-height: 80%; overflow-y: auto; }\r\n.popup__title { display: flex; align-items: center; justify-content: space-between; padding: 30rpx; border-bottom: 1px solid #eee; }\r\n.popup__title-text { font-size: 32rpx; font-weight: bold; color: #333; }\r\n.popup__close { width: 36rpx; height: 36rpx; }\r\n.popup__content { padding: 20rpx; }\r\n\r\n.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx 0;display:flex;padding:50rpx;align-items:flex-end}\r\n.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}\r\n.sharetypecontent button::after{border:0}\r\n.sharetypecontent .f1 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}\r\n.sharetypecontent .f2 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}\r\n\r\n.posterDialog{ position:fixed;z-index:999;width:100%;height:100%;background:rgba(0,0,0,0.8);top:0;left:0; display: flex; align-items: center; justify-content: center; }\r\n.posterDialog .main{ width:80%; background:#fff;position:relative;border-radius:20rpx; max-height: 90%; overflow-y: auto;}\r\n.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0; z-index: 10;}\r\n.posterDialog .close .img{ width:40rpx;height:40rpx;}\r\n.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\r\n.posterDialog .content .img{width:100%;height:auto}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./productDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./productDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041208\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}