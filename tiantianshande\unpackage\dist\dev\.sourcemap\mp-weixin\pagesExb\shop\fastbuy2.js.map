{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?dfb1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?57c5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?5729", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?dbdc", "uni-app:///pagesExb/shop/fastbuy2.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?ec54", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/shop/fastbuy2.vue?8344"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "cartListShow", "buydialogShow", "harr", "business", "datalist", "menuList", "cartList", "numtotal", "proid", "totalprice", "currentActiveIndex", "animation", "scrollToViewId", "commentlist", "comment_nodata", "comment_nomore", "paylist", "bid", "scrollState", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "list", "total", "uni", "title", "console", "changetab", "scrollTop", "duration", "getCommentList", "pagenum", "id", "clickRootItem", "setTimeout", "addcart", "ggid", "num", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "currentTarget", "dataset", "clearShopCartFn", "gopay", "prodata", "url", "fail", "gotoCatproductPage", "scroll", "countH", "buydialogChange", "handleClickMask", "openLocation", "latitude", "longitude", "name", "scale", "createpayorder", "loaded", "getmenuindex"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwMjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAR;MAAA;QACAO;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;UACAA;YAAAE;YAAAC;YAAAlB;UAAA;QACA;QAEAe;QACAA;QACAA;QACAA;QACAI;UACAC;QACA;QAEA;UACAL;QACA;;QAEA;QACA;QACA;QACA;QACAM;QACA;UACA;UACAA;UACA5B;QACA;QACAsB;QAEA;UACAA;UACA;YACA;UACA;QACA;QACAA;MACA;QACAA;QACAC;MACA;IACA;IACAM;MACA;MACA;MACA;MACAH;QACAI;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAV;MACAA;MACAA;MACA;QACAC;UAAAU;UAAA3B;QAAA;UACAgB;UACAI;UACA;UACA;YACAJ;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;QACAC;UAAAW;UAAArC;UAAAoC;QAAA;UACAX;UACAI;UACA;UACA;YACAJ;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACAa;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAf;MACAC;QAAAjB;QAAAgC;QAAAC;MAAA;QACAjB;QACA;UACAA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAiB;MACAC;MACAA;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACAC;MACA;MACArB;QAAAR;MAAA;QACAO;MACA;IACA;IACAuB;MACA;MACA;QACAtB;QACA;MACA;MACA;MACA;QACA;UACA;YACAuB;UACA;QACA;QACA;;QAEA;QACA;UACApB;YACAqB;YACAC;cACA;cACAzB;YACA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IACA0B;MACA;MACA1B;IACA;IACA2B;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA5B;QACA6B;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACApC;MACAA;QAAAW;MAAA;QACA;UACAX;QACA;UACAA;QACA;MACA;IACA;IACAqC;MACA;MACA;MACAtC;MACA;MACAI;IACA;IACAmC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5eA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/shop/fastbuy2.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/shop/fastbuy2.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fastbuy2.vue?vue&type=template&id=53dbd0eb&\"\nvar renderjs\nimport script from \"./fastbuy2.vue?vue&type=script&lang=js&\"\nexport * from \"./fastbuy2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fastbuy2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/shop/fastbuy2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy2.vue?vue&type=template&id=53dbd0eb&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.paylist && _vm.paylist.length > 0 : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.menuList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var m1 = _vm.isload && !g0 ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && !g0 ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload && !g0 ? !_vm.paylist || _vm.paylist.length == 0 : null\n  var m3 = _vm.isload && !g0 && g1 ? _vm.t(\"color1\") : null\n  var l1 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = index === _vm.currentActiveIndex ? _vm.t(\"color1\") : null\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var l3 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.datalist, function (detail, index) {\n          var $orig = _vm.__get_orig(detail)\n          var l2 = _vm.__map(detail.prolist, function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m6 =\n              item.price_type != 1 || item.sell_price > 0\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m6: m6,\n            }\n          })\n          return {\n            $orig: $orig,\n            l2: l2,\n          }\n        })\n      : null\n  var g2 = _vm.isload && _vm.st == 2 ? _vm.commentlist.length : null\n  var m7 = _vm.isload && _vm.st == 2 && g2 > 0 ? _vm.t(\"color1\") : null\n  var l4 =\n    _vm.isload && _vm.st == 3\n      ? _vm.__map(_vm.paylist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m8 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m8: m8,\n          }\n        })\n      : null\n  var m9 = _vm.isload ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m11 = _vm.isload && _vm.cartList.total > 0 ? _vm.t(\"color1\") : null\n  var m12 = _vm.isload ? _vm.t(\"color1\") : null\n  var m13 = _vm.isload ? _vm.t(\"color1\") : null\n  var m14 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g3 = _vm.isload && _vm.cartListShow ? _vm.cartList.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        g1: g1,\n        m3: m3,\n        l1: l1,\n        l3: l3,\n        g2: g2,\n        m7: m7,\n        l4: l4,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy2.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"view-show\">\r\n\t\t\t<view class=\"topbannerbg\" :style=\"business.pic?'background:url('+business.pic+') 100%':''\"></view>\r\n\t\t\t<view class=\"topbannerbg2\"></view>\r\n\t\t\t<view class=\"topbanner\">\r\n\t\t\t\t<view class=\"left\"><image class=\"img\" :src=\"business.logo\"/></view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"f1\">{{business.name}}</view>\r\n\t\t\t\t\t<view class=\"f2\">{{business.desc}}</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"opacity:0.9\" v-if=\"business.address\" @tap=\"openLocation\" :data-latitude=\"business.latitude\" :data-longitude=\"business.longitude\" :data-company=\"business.name\" :data-address=\"business.address\"><text class=\"iconfont icondingwei\"></text>{{business.address}}</view>\r\n\t\t\t\t\t<!-- <view class=\"f3\"><view class=\"flex1\"></view><view class=\"t2\">收藏<image class=\"img\" src=\"/static/img/like1.png\"/></view></view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navtab\">\r\n\t\t\t\t<block v-if=\"paylist && paylist.length > 0\">\r\n\t\t\t\t<block v-for=\"(item, index) in menuList\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'item ' + (st == item.st ? 'on' : '')\" @tap=\"changetab\" :data-st=\"item.st\">{{item.alias ? item.alias : item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\t<!-- <view class=\"item\" :class=\"st==0?'on':''\" @tap=\"changetab\" data-st=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view class=\"item\" :class=\"st==1?'on':''\" @tap=\"changetab\" data-st=\"1\">商家信息<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view class=\"item\" :class=\"st==3?'on':''\" @tap=\"changetab\" data-st=\"3\" v-if=\"paylist && paylist.length > 0\">会员支付<view class=\"after\" :style=\"{background:t('color1')}\"></view></view> -->\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"item\" :class=\"st==0?'on':''\" @tap=\"changetab\" data-st=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view class=\"item\" :class=\"st==1?'on':''\" @tap=\"changetab\" data-st=\"1\">商家信息<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view class=\"item\" :class=\"st==2?'on':''\" @tap=\"changetab\" data-st=\"2\" v-if=\"!paylist || paylist.length == 0\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"st==0\" class=\"content\" style=\"overflow:hidden;display:flex;margin-top:86rpx\" :style=\"{height:'calc(100% - '+(menuindex>-1?550:450)+'rpx)'}\">\r\n\t\t\t\t<scroll-view class=\"nav_left\" :scrollWithAnimation=\"animation\" scroll-y=\"true\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\r\n\t\t\t\t\t<view class=\"nav_left_items\" :class=\"index===currentActiveIndex?'active':''\" :style=\"{color:index===currentActiveIndex?t('color1'):'#333'}\" @tap=\"clickRootItem\" :data-root-item-id=\"item.id\" :data-root-item-index=\"index\"><view class=\"before\" :style=\"{background:t('color1')}\"></view>{{item.name}}</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<view class=\"nav_right\">\r\n\t\t\t\t\t<view class=\"nav_right-content\">\r\n\t\t\t\t\t\t<scroll-view @scroll=\"scroll\" class=\"detail-list\" :scrollIntoView=\"scrollToViewId\" :scrollWithAnimation=\"animation\" scroll-y=\"true\">\r\n\t\t\t\t\t\t\t<view v-for=\"(detail, index) in datalist\" :key=\"index\" class=\"classification-detail-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"head\" :data-id=\"detail.id\" :id=\"'detail-' + detail.id\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{detail.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"show-all\" @tap=\"gotoCatproductPage\">查看全部<text class=\"iconfont iconjiantou\"></text></view> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product-itemlist\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in detail.prolist\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-pic\" @click=\"goto\" :data-url=\"'product?id='+item.id\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p2\" v-if=\"item.price_type != 1 || item.sell_price > 0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx;padding-right:1px\">￥</text>{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.market_price*1 > item.sell_price*1\">￥{{item.market_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"p3-1\" v-if=\"item.sales>0\"><text style=\"overflow:hidden\">已售{{item.sales}}件</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"p4\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" @click.stop=\"buydialogChange\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view> -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\" v-if=\"!item.price_type\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"numtotal[item.id]>0\" class=\"minus\" @tap.stop=\"addcart\" data-num=\"-1\" :data-proid=\"item.id\" :data-stock=\"item.stock\">-</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"numtotal[item.id]>0\" class=\"i\">{{numtotal[item.id]}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.ggcount>1\" class=\"plus\" @tap.stop=\"buydialogChange\" :data-proid=\"item.id\" :data-stock=\"item.stock\">+</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"plus\" @tap.stop=\"addcart\" data-num=\"1\" :data-proid=\"item.id\" :data-ggid=\"item.gglist[0].id\" :data-stock=\"item.stock\">+</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"st==1\" class=\"content1\" style=\"margin-top:86rpx;padding-top:20rpx\">\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\">联系电话</text>\r\n\t\t\t\t\t<text class=\"t2\">{{business.tel}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\">商家地址</text>\r\n\t\t\t\t\t<text class=\"t2\">{{business.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\">商家简介</text>\r\n\t\t\t\t\t<text class=\"t2\">{{business.content}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item flex-col\" v-if=\"bid!=0\">\r\n\t\t\t\t\t<text class=\"t1\">营业时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{business.start_hours}} 至 {{business.end_hours}}</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"st==2\" class=\"content2\" style=\"margin-top:86rpx;padding-top:20rpx\">\r\n\t\t\t\t<view class=\"comment\" style=\"height:calc(100vh - 460rpx);overflow:scroll\">\r\n\t\t\t\t\t<block v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">评价({{business.comment_num}})</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\">好评率 <text :style=\"{color:t('color1')}\">{{business.comment_haopercent}}%</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in commentlist\" :key=\"index\" class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<image class=\"t1\" :src=\"item.headimg\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"t2\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (item.score>item2?'2':'') + '.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"color:#777;font-size:22rpx;\">{{item.createtime}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.content}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.content_pic!=''\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in item.content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.reply_content\">\r\n\t\t\t\t\t\t\t\t<view class=\"arrow\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"t1\">商家回复：{{item.reply_content}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<nodata v-show=\"comment_nodata\"></nodata>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"st==3\" class=\"content2\" style=\"margin-top:86rpx;padding-top:20rpx\">\r\n\t\t\t\t<view class=\"paylist\" style=\"height:calc(100vh - 460rpx);overflow:scroll\">\r\n\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in paylist\" :key=\"index\" @click=\"createpayorder\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<view class=\"t1\"><text>{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"t2\" :style=\"{color:t('color1')}\">￥{{item.market_price}}<text style=\"padding:0 4rpx\">/</text><text style=\"font-size:24rpx\">会员价￥</text>{{item.sell_price}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"arrowright\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\"></buydialog>\r\n\t\t<view style=\"height:auto;position:relative\">\r\n\t\t\t<view style=\"width:100%;height:100rpx\"></view>\r\n\t\t\t<view class=\"footer flex\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t<view class=\"cart_ico\" :style=\"{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap.stop=\"handleClickMask\"><image class=\"img\" src=\"/static/img/cart.png\"/><view class=\"cartnum\" :style=\"{background:t('color1')}\" v-if=\"cartList.total>0\">{{cartList.total}}</view></view>\r\n\t\t\t\t<view class=\"text1\">合计</view>\r\n\t\t\t\t<view class=\"text2\" :style=\"{color:t('color1')}\"><text style=\"font-size:20rpx\">￥</text>{{cartList.totalprice}}</view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view class=\"op\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"gopay\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view v-if=\"cartListShow\" class=\"popup__container\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\" style=\"margin-bottom:100rpx\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"min-height:400rpx;padding:0\">\r\n\t\t\t\t<view class=\"popup__title\" style=\"border-bottom:1px solid #EFEFEF\">\r\n\t\t\t\t\t<text class=\"popup__title-text\" style=\"color:#323232;font-weight:bold;font-size:32rpx\">购物车</text>\r\n\t\t\t\t\t<view class=\"popup__close flex-y-center\" @tap.stop=\"clearShopCartFn\" style=\"color:#999999;font-size:24rpx\"><image src=\"/static/img/del.png\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>清空</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\" style=\"padding:0\">\r\n\t\t\t\t\t<scroll-view scroll-y class=\"prolist\">\r\n\t\t\t\t\t\t<block v-for=\"(cart, index) in cartList.list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"proitem\">\r\n\t\t\t\t\t\t\t\t<image :src=\"cart.guige.pic?cart.guige.pic:cart.product.pic\" class=\"pic flex0\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"con\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cart.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"cart.guige.name!='默认规格'\">{{cart.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\" style=\"color:#ff5555;margin-top:10rpx;font-size:28rpx\">￥{{cart.guige.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"minus\"><image class=\"img\" src=\"/static/img/cart-minus.png\" @tap=\"addcart\" data-num=\"-1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"i\">{{cart.num}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"plus\"><image class=\"img\" src=\"/static/img/cart-plus.png\" @tap=\"addcart\" data-num=\"1\" :data-proid=\"cart.proid\" :data-ggid=\"cart.ggid\" :data-stock=\"cart.guige.stock\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"!cartList.list.length\">\r\n\t\t\t\t\t\t\t<text class=\"nopro\">暂时没有商品喔~</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tst:0,\r\n\t\t\tcartListShow:false,\r\n\t\t\tbuydialogShow:false,\r\n\t\t\tharr:[],\r\n\t\t\tbusiness:{},\r\n      datalist: [],\r\n\t\t\tmenuList:[],\r\n\t\t\tcartList:{},\r\n\t\t\tnumtotal:[],\r\n\t\t\tproid:'',\r\n\t\t\ttotalprice:'0.00',\r\n      currentActiveIndex: 0,\r\n      animation: true,\r\n      scrollToViewId: \"\",\r\n\t\t\tcommentlist:[],\r\n\t\t\tcomment_nodata:false,\r\n\t\t\tcomment_nomore:false,\r\n\t\t\tpaylist:[],\r\n\t\t\tbid:0,\r\n\t\t\tscrollState:true,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t\tif (this.st == 2) {\r\n\t\t\tif (!this.comment_nodata && !this.comment_nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getCommentList(true);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiShop/fastbuy2', {bid:that.opt.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.business = res.business\r\n\t\t\t\tthat.datalist = res.data;\r\n\t\t\t\t\r\n\t\t\t\t// 确保cartList正确初始化\r\n\t\t\t\tif (res.cartList) {\r\n\t\t\t\t\tthat.cartList = res.cartList;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.cartList = {list: [], total: 0, totalprice: '0.00'};\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.menuList = res.menuList;\r\n\t\t\t\tthat.numtotal = res.numtotal;\r\n\t\t\t\tthat.paylist = res.paylist;\r\n\t\t\t\tthat.bid = res.bid;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.business.name\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tif(that.menuList.length > 0) {\r\n\t\t\t\t\tthat.st = that.menuList[0].st;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//计算每个高度\r\n\t\t\t\tvar harr = [];\r\n\t\t\t\tvar clientwidth = uni.getSystemInfoSync().windowWidth;\r\n\t\t\t\tvar datalist = res.data;\r\n\t\t\t\tconsole.log(datalist.length)\r\n\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\r\n\t\t\t\t\tvar child = datalist[i].prolist;\r\n\t\t\t\t\tconsole.log(child)\r\n\t\t\t\t\tharr.push(Math.ceil(child.length) * 200 / 750 * clientwidth);\r\n\t\t\t\t}\r\n\t\t\t\tthat.harr = harr;\r\n\r\n\t\t\t\tif(that.opt.cid){\r\n\t\t\t\t\tthat.scrollToViewId = 'detail-' + that.opt.cid;\r\n\t\t\t\t\tfor (var i = 0; i < datalist.length; i++) {\r\n\t\t\t\t\t\tif(datalist[i].id == that.opt.cid) that.currentActiveIndex = i;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t}, function(err) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tapp.error('获取数据失败，请重试');\r\n\t\t\t});\r\n\t\t},\r\n\t\tchangetab:function(e){\r\n\t\t\tthis.st = e.currentTarget.dataset.st;\r\n\t\t\tthis.pagenum = 1;\r\n\t\t\tthis.commentlist = [];\r\n\t\t\tuni.pageScrollTo({\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\tduration: 0\r\n\t\t\t});\r\n\t\t\tthis.getCommentList();\r\n\t\t},\r\n\t\tgetCommentList: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.commentlist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar st = that.st;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.comment_nodata = false;\r\n\t\t\tthat.comment_nomore = false;\r\n\t\t\tif(that.bid == 0){\r\n\t\t\t\tapp.post('ApiShop/commentlist', {pagenum: pagenum,proid:0}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.commentlist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.comment_nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.comment_nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar commentlist = that.commentlist;\r\n\t\t\t\t\t\t\tvar newdata = commentlist.concat(data);\r\n\t\t\t\t\t\t\tthat.commentlist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tapp.post('ApiBusiness/getdatalist', {id: that.business.id,st: st,pagenum: pagenum}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.commentlist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.comment_nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.comment_nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar commentlist = that.commentlist;\r\n\t\t\t\t\t\t\tvar newdata = commentlist.concat(data);\r\n\t\t\t\t\t\t\tthat.commentlist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n    clickRootItem: function (t) {\r\n\t\t\tthis.scrollState=false;\r\n      var e = t.currentTarget.dataset;\r\n      this.scrollToViewId = 'detail-' + e.rootItemId;\r\n      this.currentActiveIndex = e.rootItemIndex;\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthis.scrollState=true;\r\n\t\t\t},500)\r\n    },\r\n\t\taddcart:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ks = that.ks;\r\n\t\t\tvar num = e.currentTarget.dataset.num;\r\n\t\t\tvar proid = e.currentTarget.dataset.proid;\r\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    //加入购物车弹窗后\r\n    afteraddcart: function (e) {\r\n\t\t\te.hasoption = false;\r\n\t\t\te.num = e.num || 1; // 确保有数量参数\r\n      this.addcart({currentTarget:{dataset:e}});\r\n    },\r\n    clearShopCartFn: function () {\r\n      var that = this;\r\n      app.post(\"ApiShop/cartclear\", {bid:that.opt.bid}, function (res) {\r\n        that.getdata();\r\n      });\r\n    },\r\n    gopay: function () {\r\n      var cartList = this.cartList.list;\r\n      if (!cartList || cartList.length == 0) {\r\n        app.alert('请先添加商品到购物车');\r\n        return;\r\n      }\r\n      var prodata = [];\r\n      try {\r\n        for (var i = 0; i < cartList.length; i++) {\r\n          if (cartList[i].proid && cartList[i].ggid) {\r\n            prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);\r\n          }\r\n        }\r\n        var url = 'buy?frompage=fastbuy&prodata=' + prodata.join('-');\r\n        \r\n        // 确保页面正确跳转\r\n        if (prodata.length > 0) {\r\n          uni.navigateTo({\r\n            url: '/shopPackage/shop/' + url,\r\n            fail: function(err) {\r\n              // 尝试使用app.goto方式\r\n              app.goto(url);\r\n            }\r\n          });\r\n        } else {\r\n          app.alert('购物车数据异常，请重新添加商品');\r\n        }\r\n      } catch (e) {\r\n        app.alert('处理异常，请稍后重试');\r\n      }\r\n    },\r\n    gotoCatproductPage: function (t) {\r\n      var e = t.currentTarget.dataset;\r\n      app.goto('prolist?cid=' + e.id);\r\n    },\r\n    scroll: function (e) {\r\n\t\t\tif(this.scrollState){\r\n\t\t\t\tvar scrollTop = e.detail.scrollTop;\r\n\t\t\t\tvar harr = this.harr;\r\n\t\t\t\tvar countH = 0;\r\n\t\t\t\tfor (var i = 0; i < harr.length; i++) {\r\n\t\t\t\t\tif (scrollTop >= countH && scrollTop < countH + harr[i]) {\r\n\t\t\t\t\t\tthis.currentActiveIndex = i;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcountH += harr[i];\r\n\t\t\t\t}\r\n\t\t\t}\r\n    },\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\thandleClickMask:function(){\r\n\t\t\tthis.cartListShow = !this.cartListShow;\r\n\t\t},\r\n\t\topenLocation:function(e){\r\n\t\t\t//console.log(e)\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t })\t\t\r\n\t\t},\r\n\t\tcreatepayorder:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiPlugBusinessqr/createpayorder',{id:id},function(res){\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloaded: function() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 设置页面加载完成状态\r\n\t\t\tthat.isload = true;\r\n\t\t\t// 停止下拉刷新\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t},\r\n\t\tgetmenuindex: function(e) {\r\n\t\t\tthis.menuindex = e || -1;\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage {position: relative;width: 100%;height: 100%;}\r\n.container{height:100%;overflow:hidden;position: relative;}\r\n\r\n.topbannerbg{width:100%;height:264rpx;background:#fff;}\r\n.topbannerbg2{position:absolute;z-index:7;width:100%;height:264rpx;background:rgba(0,0,0,0.7);top:0}\r\n.topbanner{position:absolute;z-index:8;width:100%;display:flex;padding:40rpx 20rpx;top:0}\r\n.topbanner .left{width:160rpx;height:160rpx;flex-shrink:0;margin-right:20rpx}\r\n.topbanner .left .img{width:100%;height:100%;border-radius:50%}\r\n.topbanner .right{display:flex;flex-direction:column;padding:20rpx 0}\r\n.topbanner .right .f1{font-size:36rpx;font-weight:bold;color:#fff}\r\n.topbanner .right .f2{font-size:22rpx;color:#fff;opacity:0.7;margin-top:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;line-height:30rpx;}\r\n.topbanner .right .f3{width:100%;display:flex;padding-right:20rpx;margin-top:10rpx}\r\n.topbanner .right .f3 .t2{display:flex;align-items:center;font-size:24rpx;color:rgba(255,255,255,0.9)}\r\n.topbanner .right .f3 .img{width:32rpx;height:32rpx;margin-left:10rpx}\r\n\r\n.navtab{display:flex;width:100%;height:110rpx;background: #fff;position:absolute;z-index:9;padding:0 50rpx;border-radius:24rpx 24rpx 0 0;margin-top:-24rpx;}\r\n.navtab .item{flex:1;font-size:32rpx; text-align:center; color:#222222; height: 110rpx; line-height: 110rpx;overflow: hidden;position:relative}\r\n.navtab .item .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:20rpx;height:4px;border-radius:2px;width:40rpx}\r\n.navtab .on{font-size:36rpx;font-weight:bold}\r\n.navtab .on .after{display:block}\r\n\r\n.content1 .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}\r\n.content1 .item:last-child{ border-bottom: 0;}\r\n.content1 .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}\r\n.content1 .item .t2{color:#2B2B2B;font-size:24rpx;line-height:30rpx}\r\n\r\n.content2 .comment{padding:0 10rpx;overflow:scroll}\r\n\r\n.content2 .comment .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex;margin:0 3%}\r\n.content2 .comment .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.content2 .comment .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n\r\n.content2 .comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.content2 .comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.content2 .comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.content2 .comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.content2 .comment .item .f1 .t3{text-align:right;}\r\n.content2 .comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.content2 .comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.content2 .comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.content2 .comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.content2 .comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.content2 .comment .item .f2 .t2{display:flex;width:100%}\r\n.content2 .comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.content2 .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.content2 .comment .item .f3{width:100%;padding:10rpx 0;position:relative}\r\n.content2 .comment .item .f3 .arrow{width: 16rpx;height: 16rpx;background:#eee;transform: rotate(45deg);position:absolute;top:0rpx;left:36rpx}\r\n.content2 .comment .item .f3 .t1{width:100%;border-radius:10rpx;padding:10rpx;font-size:22rpx;color:#888;background:#eee}\r\n\r\n\r\n.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;}\r\n.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\r\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\r\n.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}\r\n\r\n.nav_left{width: 25%;height:100%;background:#F6F6F6;overflow-y:scroll;}\r\n.nav_left .nav_left_items{line-height:50rpx;color:#333333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}\r\n.nav_left .nav_left_items.active{background: #fff;color:#333333;font-size:28rpx;font-weight:bold}\r\n.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-22rpx;left:0rpx;height:44rpx;border-radius:4rpx;width:6rpx}\r\n.nav_left .nav_left_items.active .before{display:block}\r\n\r\n.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #fff;box-sizing: border-box;padding:0 0 0 0}\r\n.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%;position:relative}\r\n.detail-list {height:100%;overflow:scroll}\r\n.classification-detail-item {width: 100%;overflow: visible;background:#fff}\r\n.classification-detail-item .head {height: 82rpx;width: 100%;display: flex;align-items:center;justify-content:space-between;}\r\n.classification-detail-item .head .txt {color:#222222;font-weight:bold;font-size:28rpx;}\r\n.classification-detail-item .head .show-all {font-size: 26rpx;color:#949494;display:flex;align-items:center}\r\n\r\n.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\r\n.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}\r\n.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}\r\n.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}\r\n.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\r\n.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}\r\n.product-itemlist .product-info .p2 .t1{font-size:32rpx;font-weight:bold;}\r\n.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\r\n.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}\r\n.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}\r\n.product-itemlist .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}\r\n.product-itemlist .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}\r\n.product-itemlist .addnum {position: absolute;right:10rpx;bottom:20rpx;font-size: 32rpx;color: #666;width: auto;display:flex;align-items:center}\r\n.product-itemlist .addnum .plus {width:44rpx;height:44rpx;background:#FD4A46;color:#FFFFFF;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:32rpx}\r\n.product-itemlist .addnum .minus {width:44rpx;height:44rpx;background:#FFFFFF;color:#FD4A46;border:1px solid #FD4A46;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:32rpx}\r\n.product-itemlist .addnum .img{width:24rpx;height:24rpx}\r\n.product-itemlist .addnum .i {padding: 0 20rpx;color:#999999;font-size:32rpx}\r\n\r\n.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}\r\n.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}\r\n.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}\r\n.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}\r\n.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}\r\n.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}\r\n.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}\r\n.prolist .proitem .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.prolist .proitem .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.prolist .proitem .addnum .img{width:24rpx;height:24rpx}\r\n.prolist .proitem .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\r\n.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}\r\n\r\n.paylist{padding:20rpx 30rpx;background:#f5f5f5}\r\n.paylist .item{width:100%;display: inline-block;position: relative;margin-bottom: 20rpx;background: #fff;display:flex;padding:0;border-radius:10rpx;border-bottom:1px solid #F8F8F8;overflow:hidden}\r\n.paylist .item .f1{width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}\r\n.paylist .item .f1 .image{position:absolute;top:0;left:0;width: 100%;height:auto}\r\n.paylist .item .f2{width: 70%;padding:30rpx 20rpx 30rpx 40rpx;position: relative;}\r\n.paylist .item .f2 .t1 {color:#323232;font-weight:bold;font-size:40rpx;line-height:50rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:100rpx}\r\n.paylist .item .f2 .t2{font-size:36rpx;line-height:46rpx;}\r\n.paylist .item .arrowright{position:absolute;top:90rpx;right:20rpx;width:40rpx;height:40rpx}\r\n\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}\r\n.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}\r\n.footer .cart_ico .img{width:36rpx;height:36rpx;}\r\n.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}\r\n.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}\r\n.footer .text2 {font-size: 32rpx;font-weight:bold}\r\n.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}\r\n::-webkit-scrollbar{width: 0;height: 0;color: transparent;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fastbuy2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024588\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}