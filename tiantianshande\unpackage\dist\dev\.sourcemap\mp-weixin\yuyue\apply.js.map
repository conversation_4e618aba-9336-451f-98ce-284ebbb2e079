{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?aac9", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?b991", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?a3a6", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?b11e", "uni-app:///yuyue/apply.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?6c31", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/apply.vue?9be1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "regiondata", "pre_url", "datalist", "pagenum", "cateArr", "cindex", "isagree", "<PERSON><PERSON><PERSON><PERSON>", "codepic", "otherpic", "headimg", "info", "set", "latitude", "longitude", "address", "items", "xieyi_show", "bindex", "bname", "fwlistshow", "fwcids", "fwcateArr", "fwcnames", "blist", "order", "isapply2", "bid", "hide_city", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "onPullDownRefresh", "methods", "getdata", "title", "changeClistDialog", "cateChange", "busChange", "locationSelect", "regionchange", "console", "fwcidsChange", "newfwcids", "ischecked", "getcnames", "subform", "setTimeout", "isagreeChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uploadimg", "pics", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsM9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACApC;QACAsC;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAH;MACAN;QACAM;QACA;UACAN;YACAA;UACA;UACA;QACA;QACAE;UACAQ;QACA;QACA;QACA;QACA;UACApC;QACA;QACA;QACA;UACAI;QACA;UACAA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA0B;QACAA;QACAA;QACAA;QACA;AACA;AACA;AACA;AACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;YACAA;UACA;YACAA;UACA;UACAA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IACAK;MACA;MACA;QACAX;QAAA;MACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;MACA;MACAP;MACAA;MACAN;QAAAH;MAAA;QACA;UACA;UACAS;QACA;UACAN;UAAA;QACA;MACA;IACA;IACAc;MACA;MACAZ;QACAG;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAS;MACA;MACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;QACA;UACAnB;UAAA;QACA;QACAkB;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;QACA3B;MACA;MACA;IACA;IACA4B;MACA;MACA;MACA;QACArB;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACA;UACAA;UACA;QACA;QACAnB;QACAmC;QACA;UACAhB;UACA;QACA;QACAnB;QACA;QACA;UACA;YACAmB;YACA;UACA;UACAnB;UACA;YACAmB;YACA;UACA;UACA;YACAA;YACA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;QAAA;MAEA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACAnB;MACAA;MACAA;MACA;QACAmB;QACA;MACA;MACAnB;MACA;QACAA;MACA;;MAGA;MACAmB;MACAA;QAAAnB;MAAA;QACAmB;QACAA;QACA;UACAsB;YACAhB;YACA;cACAN;YACA;cACAA;YACA;YACA;UACA;QACA;MACA;IACA;IACAuB;MACAP;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA1B;QACA;UACA2B;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAD;QACArB;MACA;QACA;QACAqB;QACArB;MACA;QACA;QACAqB;QACArB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9iBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=6f492e24&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/apply.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=6f492e24&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload && !_vm.isapply2 ? _vm.fwcids.length : null\n  var g1 = _vm.isload ? _vm.headimg.length : null\n  var g2 = _vm.isload ? _vm.headimg.join(\",\") : null\n  var g3 = _vm.isload ? _vm.codepic.length : null\n  var g4 = _vm.isload ? _vm.codepic.join(\",\") : null\n  var g5 = _vm.isload ? _vm.otherpic.join(\",\") : null\n  var m0 =\n    _vm.isload &&\n    _vm.set.xieyi_show == 1 &&\n    (!_vm.info.id || _vm.info.shstatus == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && (!_vm.info.id || _vm.info.shstatus == 2)\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload && (!_vm.info.id || _vm.info.shstatus == 2)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.info.id &&\n    _vm.info.status == 0 &&\n    _vm.info.apply_paymoney > 0 &&\n    _vm.order.status == 0 &&\n    _vm.info.shstatus == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.info.id &&\n    _vm.info.status == 0 &&\n    _vm.info.apply_paymoney > 0 &&\n    _vm.order.status == 0 &&\n    _vm.info.shstatus == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l1 = _vm.fwlistshow\n    ? _vm.__map(_vm.fwclist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m5 = _vm.inArray(item.id, _vm.fwcids)\n        var m6 = m5 ? _vm.t(\"color1\") : null\n        var g6 = item.child.length\n        var l0 = _vm.__map(item.child, function (item2, index2) {\n          var $orig = _vm.__get_orig(item2)\n          var m7 = _vm.inArray(item2.id, _vm.fwcids)\n          var m8 = m7 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n          }\n        })\n        return {\n          $orig: $orig,\n          m5: m5,\n          m6: m6,\n          g6: g6,\n          l0: l0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.shstatus==2\">审核不通过：{{info.reason}}，请修改后再提交</view>\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-if=\"info.id && info.shstatus==0 && info.apply_paymoney>0 && order.status==0\">您已提交资料，待支付</view>\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-else-if=\"info.id && info.shstatus==0 && info.apply_paymoney>0 && order.status==1\">您已提交申请，请等待审核</view>\n\t\t<view style=\"color:red;padding:10rpx 30rpx;margin-top:20rpx\" v-else-if=\"info.id && info.shstatus==0 && info.apply_paymoney==0\">您已提交申请，请等待审核</view>\n\t\t<form @submit=\"subform\">\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>姓名<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"realname\" :value=\"info.realname\" placeholder=\"请填写姓名\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>电话<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"tel\" :value=\"info.tel\" placeholder=\"请填写手机号码\"></input></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<block v-if=\"!isapply2\">\n\t\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t<view>加入商家<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t<view>\t\n\t\t\t\t\t\t\t<picker @change=\"busChange\" :value=\"bindex\" :range=\"blist\" range-key=\"name\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{bname}}</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t<view class=\"f1\">服务类目<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t<view  style=\"display: flex;align-items: center;\"  @tap=\"changeClistDialog\">\n\t\t\t\t\t\t\t<text v-if=\"fwcids.length>0\">{{fwcnames}}</text><text v-else style=\"color:#888\">请选择</text><image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>所属类型<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{cateArr[cindex]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t<text class=\"label\">年龄</text>\n\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"number\" name=\"age\" :value=\"info.age\" placeholder=\"请填写年龄\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t<text class=\"label\">性别</text>\n\t\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"sex\">\n\t\t\t\t\t\t\t<label class=\"radio\">\n\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"info.sex==1?true:false\" ></radio>男\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t<label class=\"radio\">\n\t\t\t\t\t\t\t\t<radio value=\"2\"  :checked=\"info.sex==2?true:false\" ></radio>女\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t</view>\t\n\t\t\t\t</view>\n\t\t\t\t<block v-if=\"!hide_city\">\n\t\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t<view>服务城市<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t\t<uni-data-picker :localdata=\"items\" :border=\"false\" :placeholder=\"regiondata || '请选择省市区'\" @change=\"regionchange\"></uni-data-picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-if=\"!isapply2\">\n\t\t\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t\t<view>定位坐标<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t\t<view class=\"flex-y-center\" @tap=\"locationSelect\"><input type=\"text\" disabled placeholder=\"请选择坐标\" :value=\"latitude ? latitude+','+longitude:''\" ></input></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t\t\t<view>服务公里数<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"fuwu_juli\"  :value=\"info.fuwu_juli\"  placeholder=\"请输入服务公里数\"></input> &nbsp;KM</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>个人简介</view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"desc\"  :value=\"info.desc\"  placeholder=\"个人简介\"></input> </view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"latitude\" style=\"position: fixed;left: -200%;\" :value=\"latitude\"></input>\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"longitude\" style=\"position: fixed;left: -200%;\" :value=\"longitude\"></input>\n\t\t\t</view>\n\n\t\t\t\t<view class=\"apply_box\">\n\t\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>工作照片<text style=\"color:red\"> *</text></view></view>\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in headimg\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"headimg\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"headimg\" v-if=\"headimg.length==0\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"headimg\" :value=\"headimg.join(',')\" maxlength=\"-1\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_box\">\n\t\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>身份证正反面<text style=\"color:red\"> *</text></view></view>\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in codepic\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"codepic\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"codepic\" v-if=\"codepic.length<2\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"codepic\" :value=\"codepic.join(',')\" maxlength=\"-1\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_box\">\n\t\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>其他证件<text style=\"color:red\"> </text> (上传资格证书和健康证）</text></view>\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in otherpic\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"otherpic\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"otherpic\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"otherpic\" :value=\"otherpic.join(',')\" maxlength=\"-1\"></input>\n\t\t\t\t</view>\n\n\t\t\t<view class=\"apply_box\">\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>设置登录账号<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"un\" :value=\"info.un\" placeholder=\"请填写登录账号\" autocomplete=\"off\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>设置登录密码<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"password\" name=\"pwd\" :value=\"info.pwd\" placeholder=\"请填写登录密码\" autocomplete=\"off\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\">\n\t\t\t\t\t<view>确认密码<text style=\"color:red\"> *</text></view>\n\t\t\t\t\t<view class=\"flex-y-center\"><input type=\"password\" name=\"repwd\" :value=\"info.repwd\" placeholder=\"请再次填写密码\"></input></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"apply_item\" v-if=\"set.apply_paymoney>0\">{{isapply2?'保证金费用':'缴纳金额'}}：<view style=\"color: red;\">￥<text style=\"font-weight:bold;font-size: 36rpx;color: red;\">{{set.apply_paymoney}}</text></view></view>\n\t\t\t</view>\n\n\n\t\t\t<block v-if=\"set.xieyi_show==1\">\n\t\t\t<view class=\"flex-y-center\" style=\"margin-left:20rpx;color:#999\" v-if=\"!info.id || info.shstatus==2\">\n\t\t\t\t<checkbox-group @change=\"isagreeChange\"><label class=\"flex-y-center\"><checkbox value=\"1\" :checked=\"isagree\"></checkbox>阅读并同意</label></checkbox-group>\n\t\t\t\t<text :style=\"'color:'+t('color1')\" @tap=\"showxieyiFun\">《入驻协议》</text>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t<view style=\"padding:30rpx 0\"><button v-if=\"!info.id || info.shstatus==2\" form-type=\"submit\" class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">提交申请</button>\n</view>\n\t\t\t<view style=\"padding:30rpx 0\"><button v-if=\"info.id && info.status==0 && info.apply_paymoney>0 && order.status==0 && info.shstatus==0\"  class=\"set-btn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" @tap=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + order.payorderid\">立即支付</button>\n</view>\n\n\t\t</form>\n\t\t\n\t\t<view id=\"xieyi\" :hidden=\"!showxieyi\" style=\"width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)\">\n\t\t\t<view style=\"width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"set.xieyi\"/>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center\" @tap=\"hidexieyi\">已阅读并同意</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t\n\t<view class=\"popup__container\" v-if=\"fwlistshow\">\n\t\t<view class=\"popup__overlay\" @tap.stop=\"changeClistDialog\"></view>\n\t\t<view class=\"popup__modal\">\n\t\t\t<view class=\"popup__title\">\n\t\t\t\t<text class=\"popup__title-text\">请选择服务类目</text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"changeClistDialog\"/>\n\t\t\t</view>\n\t\t\t<view class=\"popup__content\">\n\t\t\t\t<block v-for=\"(item, index) in fwclist\" :key=\"item.id\">\n\t\t\t\t\t<view class=\"clist-item\" @tap=\"fwcidsChange\" :data-id=\"item.id\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item.id,fwcids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-for=\"(item2, index2) in item.child\" :key=\"item2.id\">\n\t\t\t\t\t\t<view class=\"clist-item\" style=\"padding-left:80rpx\" @tap=\"fwcidsChange\" :data-id=\"item2.id\">\n\t\t\t\t\t\t\t<view class=\"flex1\" v-if=\"item.child.length-1==index2\">└ {{item2.name}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\" v-else>├ {{item2.name}}</view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item2.id,fwcids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t\n\t\n\t\n\t\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tregiondata:'',\n\t\t\tpre_url:app.globalData.pre_url,\n      datalist: [],\n      pagenum: 1,\n      cateArr: [],\n      cindex: 0,\n      isagree: false,\n      showxieyi: false,\n\t\t\tcodepic:[],\n\t\t\totherpic:[],\n\t\t\theadimg:[],\n      info: [],\n\t\t\tset:{},\n      latitude: '',\n      longitude: '',\n      address:'',\n\t\t\titems:[],\n\t\t\txieyi_show:false,\n\t\t\tbindex:'0',\n\t\t\tbname:'请选择加入商家',\n\t\t\tfwlistshow:false,\n\t\t\tfwcids:[],\n\t\t\tfwcateArr:[],\n\t\t\tfwcnames:'',\n\t\t\tblist:[],\n\t\t\torder:[],\n\t\t\tisapply2:false,\n\t\t\tbid:'',\n\t\t\thide_city:false\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.getdata();\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.type = this.opt.type || 0;\n\t\tvar that = this;\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\n\t\t\tif(customs.data.includes('plug_zhiming')) {\n\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\n\t\t\t}\n\t\t\tuni.request({\n\t\t\t\turl: url,\n\t\t\t\tdata: {},\n\t\t\t\tmethod: 'GET',\n\t\t\t\theader: { 'content-type': 'application/json' },\n\t\t\t\tsuccess: function(res2) {\n\t\t\t\t\tthat.items = res2.data\n\t\t\t\t}\n\t\t\t});\n\t\t});\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiYuyue/apply', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 2) {\n\t\t\t\t\tapp.alert(res.msg, function () {\n\t\t\t\t\t\tapp.goto('/yuyue/yuyue/my', 'redirect');\n\t\t\t\t\t})\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: res.title\n\t\t\t\t});\n\t\t\t\tvar clist = res.clist;\n\t\t\t\tvar cateArr = [];\n\t\t\t\tfor (var i in clist) {\n\t\t\t\t\tcateArr.push(clist[i].name);\n\t\t\t\t}\n\t\t\t\tvar codepic = res.info ? res.info.codepic : '';\n\t\t\t\tif (codepic) {\n\t\t\t\t\tcodepic = codepic.split(',');\n\t\t\t\t} else {\n\t\t\t\t\tcodepic = [];\n\t\t\t\t}\n\t\t\t\tvar otherpic = res.info ? res.info.otherpic : '';\n\t\t\t\tif (otherpic) {\n\t\t\t\t\totherpic = otherpic.split(',');\n\t\t\t\t} else {\n\t\t\t\t\totherpic = [];\n\t\t\t\t}\n\t\t\t\tvar headimg = res.info ? res.info.headimg : '';\n\t\t\t\tif (headimg) {\n\t\t\t\t\theadimg = headimg.split(',');\n\t\t\t\t} else {\n\t\t\t\t\theadimg = [];\n\t\t\t\t}\n\t\t\t\tthat.clist = res.clist\n\t\t\t\tthat.blist = res.blist\n\t\t\t\tthat.set = res.set\n\t\t\t\tthat.info = res.info\n\t\t\t\t/* if (res.info.province){\n\t\t\t\t\tvar regiondata = res.info.province+ ',' + res.info.city+ ',' + res.info.district;\n\t\t\t\t } else {\n\t\t\t\t\tvar regiondata = '';\n\t\t\t\t }*/\n\t\t\t\tthat.regiondata = res.info.citys\n        that.latitude = res.info.latitude;\n        that.longitude = res.info.longitude;\n\t\t\t\tthat.cateArr = cateArr;\n\t\t\t\tthat.codepic = codepic;\n\t\t\t\tthat.otherpic = otherpic;\n\t\t\t\tthat.headimg = headimg;\n\t\t\t\tthat.order = res.order\n\t\t\t\tthat.fwcateArr = res.fwcateArr\n\t\t\t\tthat.fwcids = res.fwcids;\n\t\t\t\tthat.hide_city = res.hide_city\n\t\t\t\tif(res.info.id){\n\t\t\t\t\tif(res.info.bid==0){\n\t\t\t\t\t\t\tthat.bname = '平台自营';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.bname = res.busarr[res.info.bid].name\n\t\t\t\t\t}\n\t\t\t\t\tthat.bid = res.info.bid\n\t\t\t\t}\n\t\t\t\tthat.isapply2 = res.isapply2\n\t\t\t\tthat.getcnames();\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tchangeClistDialog:function(){\n\t\t\tvar that =this\n\t\t\tif(!that.bid){\n\t\t\t\t\tapp.error('请先选择加入商家');return;\n\t\t\t}\n\t\t\tthis.fwlistshow = !this.fwlistshow\n\t\t},\n    cateChange: function (e) {\n      this.cindex = e.detail.value;\n    },\n\t\tbusChange: function (e) {\n\t\t\tvar that=this\n\t\t  var bindex = e.detail.value;\n\t\t\tthat.bname = that.blist[bindex].name\n\t\t\tthat.bid = that.blist[bindex].id\n\t\t\tapp.get('ApiYuyue/classify', {bid:that.bid}, function (res) {\n\t\t\t\tif(res.status==1){\n\t\t\t\t\tvar fwclist = res.data\n\t\t\t\t\tthat.fwclist = fwclist;\n\t\t\t\t}else{\n\t\t\t\t\t\tapp.error('获取失败');return;\n\t\t\t\t}\n\t\t\t})\n\t\t},\n    locationSelect: function () {\n      var that = this;\n      uni.chooseLocation({\n        success: function (res) {\n          that.info.address = res.name;\n\t\t\t\t\tthat.info.latitude = res.latitude;\n          that.info.longitude = res.longitude;\n          that.address = res.name;\n          that.latitude = res.latitude;\n          that.longitude = res.longitude;\n        }\n      });\n    },\n\t\tregionchange(e) {\n\t\t\tconst value = e.detail.value\n\t\t\tconsole.log(value[0].text + ',' + value[1].text + ',' + value[2].text);\n\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text\n\t\t},\n\t\tfwcidsChange:function(e){\n\t\t\tvar fwlist = this.fwlist;\n\t\t\tvar fwcids = this.fwcids;\n\t\t\tvar fwcid = e.currentTarget.dataset.id;\n\t\t\tvar newfwcids = [];\n\t\t\tvar ischecked = false;\n\t\t\tfor(var i in fwcids){\n\t\t\t\tif(fwcids[i] != fwcid){\n\t\t\t\t\tnewfwcids.push(fwcids[i]);\n\t\t\t\t}else{\n\t\t\t\t\tischecked = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(ischecked==false){\n\t\t\t\tif(newfwcids.length >= 5){\n\t\t\t\t\tapp.error('最多只能选择五个分类');return;\n\t\t\t\t}\n\t\t\t\tnewfwcids.push(fwcid);\n\t\t\t}\n\t\t\tthis.fwcids = newfwcids;\n\t\t\tthis.getcnames();\n\t\t},\n\t\tgetcnames:function(){\n\t\t\tvar fwcateArr = this.fwcateArr;\n\t\t\tvar fwcids = this.fwcids;\n\t\t\tvar fwcnames = [];\n\t\t\tfor(var i in fwcids){\n\t\t\t\tfwcnames.push(fwcateArr[fwcids[i]]);\n\t\t\t}\n\t\t\tthis.fwcnames = fwcnames.join(',');\n\t\t},\n    subform: function (e) {\n      var that = this;\n      var info = e.detail.value;\n      if (info.realname == '') {\n        app.error('请填写姓名');\n        return false;\n      }\n\n      if (info.tel == '') {\n        app.error('请填写电话');\n        return false;\n      }\n\n\t\t\tif(!that.isapply2){\n\t\t\t\tif(that.bid===''){\n\t\t\t\t\tapp.error('请选择加入商家');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinfo.bid = that.bid\n\t\t\t\t\tconsole.log(that.fwcids.length);\n\t\t\t\tif(that.fwcids.length==0){\n\t\t\t\t\tapp.error('请选择服务类目');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinfo.fwcids = that.fwcids\n\t\t\t\tvar regiondata = that.regiondata;\n\t\t\t\tif(!that.hide_city){\n\t\t\t\t\tif(regiondata == '' || regiondata==undefined) {\n\t\t\t\t\t\tapp.error('请选择服务城市');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tinfo.citys = regiondata;\n\t\t\t\t\tif (info.latitude == '' || info.latitude==undefined ) {\n\t\t\t\t\t\tapp.error('请选择店铺坐标');\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif (info.fuwu_juli == '') {\n\t\t\t\t\t\tapp.error('请填写服务公里数');\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\t\n\t\t\t\t}\n\t\t\t\tif (info.headimg == '') {\n\t\t\t\t\tapp.error('请上传工作照');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (info.codepic == '') {\n\t\t\t\t\tapp.error('请上传身份证正反面');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (info.other_pic== '') {//$.error('请上传证明材料');return false;\n\t\t\t\t}\n\t\t\t}\n      if (info.un == '') {\n        app.error('请填写登录账号');\n        return false;\n      }\n      if (!that.info.id &&  info.pwd == '') {\n        app.error('请填写登录密码');\n        return false;\n      }\n      var pwd = info.pwd;\n      if (!that.info.id && pwd.length < 6) {\n        app.error('密码不能小于6位');\n        return false;\n      }\n      if (info.repwd != info.pwd) {\n        app.error('两次输入密码不一致');\n        return false;\n      } //if(!/(^0?1[3|4|5|6|7|8|9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$)/.test(tel)){\n      //\tdialog('手机号格式错误');return false;\n      //}\n      info.address = that.address;\n      info.latitude = that.latitude;\n      info.longitude = that.longitude;\n      if (that.set.xieyi_show == 1 && !that.isagree) {\n        app.error('请先阅读并同意入驻协议');\n        return false;\n      }\n      info.cid = that.clist[that.cindex].id;\n      if (that.info && that.info.id) {\n        info.id = that.info.id;\n      }\n\t\t\t\n\t\t\t\n\t\t\t//console.log(info);return;\n\t\t\tapp.showLoading('提交中');\n      app.post(\"ApiYuyue/apply\", {info: info}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        app.error(res.msg);\n\t\t\t\tif(res.status == 1){\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata()\n\t\t\t\t\t\tif(res.payorderid){\n\t\t\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t  app.goto(res.tourl,'reLaunch');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//app.goto(app.globalData.indexurl);\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n      });\n    },\n    isagreeChange: function (e) {\n      console.log(e.detail.value);\n      var val = e.detail.value;\n      if (val.length > 0) {\n        this.isagree = true;\n      } else {\n        this.isagree = false;\n      }\n    },\n    showxieyiFun: function () {\n      this.showxieyi = true;\n    },\n    hidexieyi: function () {\n      this.showxieyi = false;\n\t\t\tthis.isagree = true;\n    },\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'headimg') that.headimg = pics;\n\t\t\t\tif(field == 'codepic') that.codepic = pics;\n\t\t\t\tif(field == 'otherpic') that.otherpic = pics;\n\t\t\t},1)\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tif(field == 'headimg'){\n\t\t\t\tvar pics = that.headimg\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.headimg = pics;\n\t\t\t}else if(field == 'codepic'){\n\t\t\t\tvar pics = that.codepic\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.codepic = pics;\n\t\t\t}else if(field == 'otherpic'){\n\t\t\t\tvar pics = that.otherpic\n\t\t\t\tpics.splice(index,1);\n\t\t\t\tthat.otherpic = pics;\n\t\t\t}\n\t\t},\n  }\n}\n</script>\n<style>\nradio{transform: scale(0.6);}\ncheckbox{transform: scale(0.6);}\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\n.apply_title { background: #fff}\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\n\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\n.apply_box .apply_item:last-child{ border:none}\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\n.apply_item input::placeholder{ color:#999999}\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;height:100%;object-fit: cover;}\n.layui-imgbox-close{position: absolute; top: 5rpx; right: 5rpx; width: 32rpx; height: 32rpx; background-color: rgba(0,0,0,0.5); border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 1;}\n.layui-imgbox-close image{width: 20rpx; height: 20rpx;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n\n\n\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.clist-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.clist-item .radio .radio-img{width:100%;height:100%;display:block}\n\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115040480\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}