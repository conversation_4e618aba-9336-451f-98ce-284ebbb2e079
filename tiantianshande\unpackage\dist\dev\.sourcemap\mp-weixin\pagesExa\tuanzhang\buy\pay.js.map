{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?db0f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?c08c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?74c9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?a4ba", "uni-app:///pagesExa/tuanzhang/buy/pay.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?ee07", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/buy/pay.vue?12cc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "<PERSON><PERSON><PERSON>", "tourl", "typeid", "sand<PERSON>ay", "wxpay", "wxpay_type", "alipay", "baid<PERSON>ay", "bid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moneypay", "heipay", "cancod", "daifu", "daifu_txt", "pay_month", "pay_transfer", "codtxt", "pay_month_txt", "give_coupon_list", "give_coupon_num", "userinfo", "paypwd", "hiddenmodalput", "payorder", "san_pay", "tmplids", "give_coupon_show", "give_coupon_close_url", "more_alipay", "alipay2", "alipay3", "paypal", "yuanbao_money", "total_yuanbao", "yuanbao_msg", "yuanbaopay", "open_pay", "pay_type", "s_base_info", "invite_free", "invite_status", "free_tmplids", "sharepic", "is_shop_pay", "charge_status", "onShareAppMessage", "title", "tolink", "pic", "console", "onShareTimeline", "imageUrl", "query", "link", "onLoad", "onPullDownRefresh", "methods", "switch_shop_pay", "getdata", "that", "thisurl", "app", "orderid", "scene", "setTimeout", "paymentId", "PayerID", "getpwd", "currentTarget", "dataset", "changeradio", "check_base_info", "info", "topay", "uni", "content", "showCancel", "success", "url", "op", "location", "qq", "referer", "fail", "document", "swan", "tt", "jscode", "<PERSON><PERSON><PERSON>", "timeStamp", "nonceStr", "package", "signType", "paySign", "top", "wv", "currentWebview", "topay2", "topayMonth", "topayTransfer", "give_coupon_close", "got<PERSON>l", "showOpenWeapp", "close<PERSON><PERSON><PERSON>", "PayconfirmFun", "close_pay", "closeInvite", "gotoInvite", "tmplIds", "todaifu", "itemList", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,SAAI;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrZA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2T5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;MAAAC;MAAAC;MAAAC;IAAA;IACAC;IACAA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;MAAAJ;MAAAC;MAAAC;IAAA;IACA;IACA;IACA;MACAF;MACAK;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAL;IACA;IACA;EACA;EACAM;IACA;EACA;EACAC;IACAC,4CACA;MACA;IACA;IACAC;MACA;MACAC;MACA;MACA;QACAC;MACA;MACAC;QAAAC;QAAAF;QAAA5D;QAAA+D;MAAA;QAEAJ;QACA;UACAE;UAEA,yBACA;YACAG;cACAH;YACA;UACA;UAEA;QACA;QAEAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAV;QACA,6DACA;UACAU;UACAA;QACA;QACAA;QACA;UACA;YACAA;UACA;QACA;QACAA;QACAA;QAEAA;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QAGA;UACAA;UACAE;UACAA;YAAAC;YAAAG;YAAAC;UAAA;YACAL;YACA;cACAA;cACAF;gBACA;kBACAA;gBACA;kBACAK;oBACA;sBACAL;sBACAA;oBACA;sBACA;sBACAA;oBACA;kBACA;gBACA;cACA;YACA;cACAE;YACA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;QAAAC;UAAAC;YAAApE;UAAA;QAAA;MAAA;IACA;IACAqE;MACA;MACA;MACAX;MACAV;IACA;IACAsB,4CACA;MACA;MACA,kDACA;QACA;QACA,yCACA;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAGA,8BACA;QACA;QACA,uBACA;UACAZ;UACA;UACA;UACA;UACA;UACA;;UAEAa;YACA5B;YACA6B;YACAC;YACAC;cACAH;gBACAI;cACA;YACA;UACA;UAEA;QACA;QACA;QACAjB;UACAkB;UACAjB;UACA7D;UAAAoB;UACAgB;UACA;QACA;UACAwB;UACA;YACAA;YACA;UACA;UACA;YACAA;YACAF;cACA;gBACAA;cACA;gBACAK;kBACA;oBACAL;oBACAA;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;YACA;UACA;QACA;QAEA;MACA;MAEA;QAAA;QACA;UACAA;UAAA;QACA;QACAE;UACAA;UACAA;YAAAkB;YAAAjB;YAAA7D;YAAAoB;YAAAgB;UAAA;YACAwB;YACA;cACAA;cACA;YACA;YACA;cACAA;cACAF;gBACA;kBACAA;gBACA;kBACAK;oBACA;sBACAL;sBACAA;oBACA;sBACAA;oBACA;kBACA;gBACA;cACA;cACA;YACA;UACA;QACA;MACA,OACA,kBACA;QACAE;UACAA;UAEAA;YAAAkB;YAAAjB;YAAA7D;YAAAoB;YAAAgB;UAAA;YACAwB;YACA;cACAA;cACA;YACA;YACA;cACAA;cACAF;gBACA;kBACAA;gBACA;kBACAK;oBACA;sBACAL;sBACAA;oBACA;sBACAA;oBACA;kBACA;gBACA;cACA;cACA;YACA;UACA;QACA;MACA,OACA,iBACA;QAAA;QACAV;QACAY;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACA;YACAA;YACA;UACA;UACA;YACA;YACAA;YACAF;cACA;gBACAA;cACA;gBACAK;kBACA;oBACAL;oBACAA;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;YACA;UACA;UACA;UACA;YACA;cACA;gBACAV;gBACA7D;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACAyE;oBACAF;sBACA;wBACAA;sBACA;wBACAK;0BACA;4BACAL;4BACAA;0BACA;4BACAA;0BACA;wBACA;sBACA;oBACA;kBACA;kBACA;oBACA;kBAAA;gBAEA;cACA;gBACAV;gBACA7D;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACAyE;oBACAF;sBACA;wBACAA;sBACA;wBACAK;0BACA;4BACAL;4BACAA;0BACA;4BACAA;0BACA;wBACA;sBACA;oBACA;kBACA;kBACA;oBACA;kBAAA;gBAEA;cACA;YACA;cACAe;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAb;kBACAF;oBACA;sBACAA;oBACA;sBACAK;wBACA;0BACAL;0BACAA;wBACA;0BACAA;wBACA;sBACA;oBACA;kBACA;gBACA;gBACA;kBACA;gBAAA;cAEA;YACA;UACA;;YAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UAvBA,CAwBA;YACAqB;UACA;YACA/B;YACAyB;cACA;cACA;cACA;gBACAb;gBACAF;kBACA;oBACAA;kBACA;oBACAK;sBACA;wBACAL;wBACAA;sBACA;wBACAA;sBACA;oBACA;kBACA;gBACA;cACA;cACA;gBACAV;gBACA;cACA;YACA;UACA;YACAgC;cACAH;cACAI;cACAL;gBACAlB;kBACA;oBACAA;kBACA;oBACAK;sBACA;wBACAL;wBACAA;sBACA;wBACAA;sBACA;oBACA;kBACA;gBACA;cACA;cACAwB;YACA;UACA;QACA;MACA;QAAA;QACAlC;QACAY;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACA;YACAA;YACA;UACA;UACA;YACA;YACAA;YACAF;cACA;gBACAA;cACA;gBACAK;kBACA;oBACAL;oBACAA;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;YACA;UACA;QAEA;MACA;QAAA;QACAE;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACAgD;UACAY;UACA;YACAA;YACA;UACA;UACA;YACA;YACAA;YACAF;cACA;gBACAA;cACA;gBACAK;kBACA;oBACAL;oBACAA;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;YACA;UACA;UACA;UACA;YACAe;cACA;cACA;cACA;gBACAzB;gBACA;kBACA;gBACA;gBACAY;gBACAF;kBACA;oBACAA;kBACA;oBACAK;sBACA;wBACAL;wBACAA;sBACA;wBACAA;sBACA;oBACA;kBACA;gBACA;cACA;cACA;gBACA;cAAA;YAEA;UACA;YACAyB;YACAA;UACA;YACAnC;YACAA;YACAA;YACAyB;cACA;cACA;cACA;gBACAzB;gBACAA;gBACAY;gBACAF;kBACA;oBACAA;kBACA;oBACAK;sBACA;wBACAL;wBACAA;sBACA;wBACAA;sBACA;oBACA;kBACA;gBACA;cACA;cACA;gBACAV;gBACA;cACA;YACA;UACA;QACA;MACA;QACAY;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACAwB;YACA;YACA;cACAxB;cACAF;gBACA;kBACAA;gBACA;kBACAK;oBACA;sBACAL;sBACAA;oBACA;sBACAA;oBACA;kBACA;gBACA;cACA;YACA;YACA;cACA;gBACAE;cACA;YACA;UACA;QACA;MACA;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACAZ;UACAqC;YACA;YACA;YACA;cACA;gBACAzB;gBACAF;kBACA;oBACAA;kBACA;oBACAK;sBACA;wBACAL;wBACAA;sBACA;wBACAA;sBACA;oBACA;kBACA;gBACA;cACA;YACA;YACA;cACAE;YACA;UACA;QACA;MACA;QACA;UACAzE;YACAyF;cACA;gBACAhB;gBACAA;kBAAA0B;gBAAA;kBACA1B;kBACAA;oBACAZ;oBACAY;sBAAAC;sBAAA0B;oBAAA;sBACA3B;wBACA;0BACAzE;4BACAqG;4BACAC;4BACAC;4BACAC;4BACAC;4BACAhB;8BACAhB;8BACAF;gCACA;kCACAA;gCACA;kCACAK;oCACA;sCACAL;sCACAA;oCACA;sCACAA;oCACA;kCACA;gCACA;8BACA;4BACA;4BACAwB;8BACA;4BAAA;0BAEA;wBACA;0BACAtB;wBACA;sBACA;oBACA;kBACA;gBACA;cACA;gBACAZ;cACA;YACA;UACA;QACA;UACA;UACA6B;UACAE;QACA;MACA;QACA;QACA;QACA;QACAhB;UACAL;QACA;QAEAE;QACA;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACAZ;UACAY;QACA;MACA;QACA;QACA;QACA;;QAEAA;QACA;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACAZ;UACAY;QACA;MACA;QACA;QACAZ;QAEA;QACA;QACA;UACAY;UACA;QACA;QACAF;QACAA;MACA;QACAE;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACAZ;UACA;YACA;cACA;gBACA6C;cACA;cACAC;cACA;cACAC;YACA;cACAnC;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAoC;MACA;MACA;MACApC;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACA;YACAA;YACA;UACA;UACA;YACA;YACAA;YACAF;cACAK;gBACAL;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAuC;MACA;MACA;MACArC;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UACA;YACAA;YACA;UACA;UACA;YACA;YACAA;YACAF;cACAK;gBACAL;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAwC;MACA;MACA;MACAtC;QACAA;QACAA;UAAAkB;UAAAjB;UAAA7D;QAAA;UACA4D;UAEA;YACA;YACAA;YACAG;cACAL;YACA;YACA;UACA;YACA;YACAE;YACAF,mCAEA;YACAK;cACAL;YACA;YACA;UACA;YACAE;YACA;UACA;QACA;MACA;IACA;IACAuC;MACA;MACA;MACA;MACAzC;IACA;IACA0C;MACA;MACA;QACA;UACA;UACArG;UACA;UACAiD;UACAU;UACA;QACA;MACA;MACAE;IACA;IACAyC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA9C;IACA;IACA+C;MACA;MACA/C;MACAK;QACA;UACAL;UACAA;QACA;UACAA;QACA;MACA;IACA;IACAgD;MACA;MACA;MACA;QACAjC;UACAkC;UACA/B;YACA5B;UACA;UACAkC;YACAlC;UACA;QACA;MACA;MACAY;IACA;IACAgD;MACA;MACA;MACA;MACA;QACA;QACA;UAAA/D;UAAAO;UAAAL;QAAA;QACAa;MACA;QACAa;UACAoC;UACAjC;YACA;cACA;cACA;gBACAd;cACA;cACA;cACAgD;cACAA;cACAA;cACAA;cACAA;cACAA;cACAA;cACArC;YACA;UACA;QACA;MACA;QACAb;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACr5CA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhang/buy/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhang/buy/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=67078992&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhang/buy/pay.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=template&id=67078992&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    !(_vm.payorder.score == 0) &&\n    _vm.payorder.money > 0 &&\n    _vm.payorder.score > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m1 =\n    _vm.isload &&\n    !(_vm.payorder.score == 0) &&\n    !(_vm.payorder.money > 0 && _vm.payorder.score > 0)\n      ? _vm.t(\"积分\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.payorder.money == 0 &&\n    _vm.payorder.score > 0 &&\n    _vm.moneypay == 1\n      ? _vm.t(\"积分\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.payorder.money == 0 &&\n    _vm.payorder.score > 0 &&\n    _vm.moneypay == 1\n      ? _vm.t(\"积分\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.payorder.money == 0 &&\n    _vm.payorder.score > 0 &&\n    _vm.moneypay == 1 &&\n    _vm.typeid == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.sandepay == 1 &&\n    _vm.typeid == \"29\"\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.wxpay == 1 &&\n    (_vm.wxpay_type == 0 ||\n      _vm.wxpay_type == 1 ||\n      _vm.wxpay_type == 2 ||\n      _vm.wxpay_type == 3 ||\n      _vm.wxpay_type == 4) &&\n    _vm.typeid == \"2\"\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.wxpay == 1 &&\n    _vm.wxpay_type == 5 &&\n    _vm.typeid == \"2\"\n      ? _vm.t(\"color1\")\n      : null\n  var m8 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.wxpay == 1 &&\n    _vm.wxpay_type == 22 &&\n    _vm.typeid == \"22\"\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.alipay == 2 &&\n    _vm.typeid == \"23\"\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.alipay == 1 &&\n    _vm.typeid == \"3\"\n      ? _vm.t(\"color1\")\n      : null\n  var m11 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.more_alipay == 1 &&\n    _vm.alipay2 == 1 &&\n    _vm.typeid == \"31\"\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.more_alipay == 1 &&\n    _vm.alipay3 == 1 &&\n    _vm.typeid == \"32\"\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.paypal == 1 &&\n    _vm.typeid == \"51\"\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.baidupay == 1 &&\n    _vm.typeid == \"11\"\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.toutiaopay == 1 &&\n    _vm.typeid == \"12\"\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.moneypay == 1\n      ? _vm.t(\"余额\")\n      : null\n  var m17 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.moneypay == 1 &&\n    _vm.typeid == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m18 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.heipay == 1\n      ? _vm.t(\"购物积分\")\n      : null\n  var m19 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.heipay == 1 &&\n    _vm.typeid == \"88\"\n      ? _vm.t(\"color1\")\n      : null\n  var m20 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.yuanbaopay == 1\n      ? _vm.t(\"元宝\")\n      : null\n  var m21 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.yuanbaopay == 1\n      ? _vm.t(\"元宝\")\n      : null\n  var m22 =\n    _vm.isload &&\n    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&\n    _vm.yuanbaopay == 1 &&\n    _vm.typeid == \"yuanbao\"\n      ? _vm.t(\"color1\")\n      : null\n  var g0 = _vm.isload\n    ? _vm.s_base_info.length != 0 && _vm.charge_status != 0\n    : null\n  var g1 = _vm.isload\n    ? _vm.is_shop_pay == true && _vm.s_base_info.length != 0\n    : null\n  var l0 =\n    _vm.isload && g1\n      ? _vm.__map(_vm.s_base_info, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m23 = _vm.t(\"余额\")\n          var m24 = _vm.t(\"要支付\")\n          var m25 = true ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m23: m23,\n            m24: m24,\n            m25: m25,\n          }\n        })\n      : null\n  var m26 =\n    _vm.isload && _vm.typeid != \"0\" && _vm.typeid != 29 ? _vm.t(\"color1\") : null\n  var m27 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t(\"color2\") : null\n  var m28 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t(\"转账汇款\") : null\n  var m29 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t(\"转账汇款\") : null\n  var m30 = _vm.isload && _vm.pay_month == 1 ? _vm.t(\"color1\") : null\n  var m31 =\n    _vm.isload && _vm.daifu\n      ? _vm.getplatform() == \"h5\" ||\n        _vm.getplatform() == \"mp\" ||\n        _vm.getplatform() == \"app\"\n      : null\n  var m32 = _vm.isload && _vm.give_coupon_show ? _vm.t(\"优惠券\") : null\n  var m33 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.wxpay == 1 &&\n    (_vm.wxpay_type == 0 ||\n      _vm.wxpay_type == 1 ||\n      _vm.wxpay_type == 2 ||\n      _vm.wxpay_type == 3) &&\n    _vm.typeid == \"2\"\n      ? _vm.t(\"color1\")\n      : null\n  var m34 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.wxpay == 1 &&\n    _vm.wxpay_type == 22 &&\n    _vm.typeid == \"22\"\n      ? _vm.t(\"color1\")\n      : null\n  var m35 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.alipay == 2 &&\n    _vm.typeid == \"23\"\n      ? _vm.t(\"color1\")\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.alipay == 1 &&\n    _vm.typeid == \"3\"\n      ? _vm.t(\"color1\")\n      : null\n  var m37 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.more_alipay == 1 &&\n    _vm.alipay2 == 1 &&\n    _vm.typeid == \"31\"\n      ? _vm.t(\"color1\")\n      : null\n  var m38 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.more_alipay == 1 &&\n    _vm.alipay3 == 1 &&\n    _vm.typeid == \"32\"\n      ? _vm.t(\"color1\")\n      : null\n  var m39 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.baidupay == 1 &&\n    _vm.typeid == \"11\"\n      ? _vm.t(\"color1\")\n      : null\n  var m40 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.toutiaopay == 1 &&\n    _vm.typeid == \"12\"\n      ? _vm.t(\"color1\")\n      : null\n  var m41 =\n    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.moneypay == 1\n      ? _vm.t(\"余额\")\n      : null\n  var m42 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.moneypay == 1 &&\n    _vm.typeid == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m43 =\n    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.heipay == 1\n      ? _vm.t(\"抵扣积分\")\n      : null\n  var m44 =\n    _vm.isload &&\n    _vm.yuanbaopay == 1 &&\n    _vm.open_pay &&\n    _vm.heipay == 1 &&\n    _vm.typeid == \"1\"\n      ? _vm.t(\"color1\")\n      : null\n  var m45 =\n    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.typeid != \"0\"\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"f1\">需支付金额</view>\r\n\t\t\t<view class=\"f2\" v-if=\"payorder.score==0\"><text class=\"t1\">￥</text><text class=\"t2\">{{payorder.money}}</text></view>\r\n\t\t\t<view class=\"f2\" v-else-if=\"payorder.money>0 && payorder.score>0\"><text class=\"t1\">￥</text><text class=\"t2\">{{payorder.money}}</text><text style=\"font-size:28rpx\"> + {{payorder.score}}{{t('积分')}}</text></view>\r\n\t\t\t<view class=\"f2\" v-else><text class=\"t3\">{{payorder.score}}{{t('积分')}}</text></view>\r\n\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"detailurl\" v-if=\"detailurl!=''\">订单详情<text class=\"iconfont iconjiantou\"></text></view>\r\n\t\t</view>\r\n\t\t<view class=\"paytype\" style=\"margin-bottom: 0px; position: relative;overflow: auto;\">\r\n\t\t\t<view v-if=\"is_shop_pay==true\" style=\"z-index: 3;width: 100%;height: 200px;background-color: #ccc; position: absolute;opacity: 0.5;\"></view>\r\n\t\t\t<view class=\"f1\">选择支付方式：</view>\r\n\t\t\t<block v-if=\"payorder.money==0 && payorder.score>0\">\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"item\" v-if=\"moneypay==1\" @tap.stop=\"changeradio\" data-typeid=\"1\">\r\n\t\t\t\t\t\t<view class=\"t1 flex\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex-col\"><text>{{t('积分')}}支付</text><text style=\"font-size:22rpx;font-weight:normal\">剩余{{t('积分')}}<text style=\"color:#FC5729\">{{userinfo.score}}</text></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='1' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"item\"  @tap.stop=\"changeradio\" data-typeid=\"29\" v-if=\"sandepay==1\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/pay-money.png\"/>杉德支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='29' ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"wxpay==1 && (wxpay_type==0 || wxpay_type==1 || wxpay_type==2 || wxpay_type==3||wxpay_type==4)\" @tap.stop=\"changeradio\" data-typeid=\"2\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>微信支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='2' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"wxpay==1 && (wxpay_type==5 )\" @tap.stop=\"changeradio\" data-typeid=\"2\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>嘉联微信支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='2' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"wxpay==1 && wxpay_type==22\" @tap.stop=\"changeradio\" data-typeid=\"22\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>微信支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='22' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"item\" v-if=\"alipay==2\" @tap.stop=\"changeradio\" data-typeid=\"23\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='23' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n                    \r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t<view class=\"item\" v-if=\"alipay==1\" @tap.stop=\"changeradio\" data-typeid=\"3\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='3' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n                    \r\n\t\t\t\t\t<block v-if=\"more_alipay==1\">\r\n\t\t\t\t\t\t<view class=\"item\" v-if=\"alipay2==1\" @tap.stop=\"changeradio\" data-typeid=\"31\">\r\n\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付2</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='31' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" v-if=\"alipay3==1\" @tap.stop=\"changeradio\" data-typeid=\"32\">\r\n\t\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付3</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='32' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"item\" v-if=\"paypal==1\" @tap.stop=\"changeradio\" data-typeid=\"51\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/paypal.png\"/>PayPal支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='51' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n                    \r\n\t\t\t\t\t<view class=\"item\" v-if=\"baidupay==1\" @tap.stop=\"changeradio\" data-typeid=\"11\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/pay-money.png\"/>在线支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='11' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"toutiaopay==1\" @tap.stop=\"changeradio\" data-typeid=\"12\">\r\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/pay-money.png\"/>在线支付</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='12' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"moneypay==1\" @tap.stop=\"changeradio\" data-typeid=\"1\">\r\n\t\t\t\t\t\t<view class=\"t1 flex\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex-col\"><text>{{t('余额')}}支付</text><text style=\"font-size:22rpx;font-weight:normal\">可用余额<text style=\"color:#FC5729\">￥{{userinfo.money}}</text></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='1' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"heipay==1\" @tap.stop=\"changeradio\" data-typeid=\"88\">\r\n\t\t\t\t\t\t<view class=\"t1 flex\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex-col\"><text>{{t('购物积分')}}支付</text><text style=\"font-size:22rpx;font-weight:normal\">可用购物积分<text style=\"color:#FC5729\">￥{{userinfo.heiscore}}</text></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='88' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"yuanbaopay==1\" @tap.stop=\"changeradio\" data-typeid=\"yuanbao\" style=\"height: 130rpx;\">\r\n\t\t\t\t\t\t<view class=\"t1 flex\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{t('元宝')}}支付</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:22rpx;font-weight:normal\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t可用{{t('元宝')}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#FC5729\">{{userinfo.yuanbao}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:22rpx;font-weight:normal\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t需支付\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#FC5729\">{{yuanbao_msg}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"typeid=='yuanbao' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<view class=\"paytype\" style=\"margin-bottom: 0px;\" v-if=\"s_base_info.length!=0&&charge_status!=0\">\r\n\t\t\t<view class=\"f1\">使用商店余额支付：<switch :checked='is_shop_pay==true' @change=\"switch_shop_pay\"></switch></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"paytype\" v-if=\"is_shop_pay==true&&s_base_info.length!=0\">\r\n\t\t\t<view class=\"f1\">商店支付：</view>\r\n\t\t\t<block >\r\n\t\t\t\t<view class=\"f2\" >\r\n\t\t\t\t\t<view class=\"item\"  data-typeid=\"1\" v-for=\"(item,index) in s_base_info\">\r\n\t\t\t\t\t\t<view class=\"t1 flex\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t\t\t<view class=\"flex-col\">\r\n\t\t\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:22rpx;font-weight:normal\">剩余{{t('余额')}}<text style=\"color:#FC5729\">{{item.balance}}元</text></text>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:22rpx;font-weight:normal\">剩余{{t('要支付')}}<text style=\"color:#FC5729\">{{item.pay_total}}元</text></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"true ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n    <block>\r\n      <view v-if=\"typeid == 29\"  v-html=\"san_pay.form\"></view>\r\n<!--      //杉德支付表单支付-->\r\n\r\n    </block>\r\n    <button class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\" v-if=\"typeid != '0' && typeid != 29\">立即支付</button>\r\n\t\t<button class=\"btn\" @tap=\"topay2\" v-if=\"cancod==1\" style=\"background:rgba(126,113,246,0.5);\">{{codtxt}}</button>\r\n\t\t<button class=\"btn\" @tap=\"topayTransfer\" v-if=\"pay_transfer==1\" :style=\"{background:t('color2')}\" :data-text=\"t('转账汇款')\">{{t('转账汇款')}}</button>\r\n\t\t<button class=\"btn\" @tap=\"topayMonth\" v-if=\"pay_month==1\" :style=\"{background:t('color1')}\">{{pay_month_txt}}</button>\r\n\t\t<block v-if=\"daifu\">\r\n\t\t\t<button class=\"btn daifu-btn\" @tap=\"todaifu\" v-if=\"getplatform() == 'h5' || getplatform() == 'mp' || getplatform() == 'app'\">\r\n\t\t\t\t{{daifu_txt}}\r\n\t\t\t</button>\r\n\t\t\t<button class=\"btn daifu-btn\" open-type=\"share\" v-else>\r\n\t\t\t\t{{daifu_txt}}\r\n\t\t\t</button>\r\n\t\t</block>\r\n\t\t<uni-popup id=\"dialogInput\" ref=\"dialogInput\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"支付密码\" value=\"\" placeholder=\"请输入支付密码\" @confirm=\"getpwd\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\r\n\t\t<block v-if=\"give_coupon_show\">\r\n\t\t\t<view class=\"give-coupon flex-x-center flex-y-center\">\r\n\t\t\t\t<view class='coupon-block'>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/coupon-top.png'\" style=\"width:630rpx;height:330rpx;\"></image>\r\n\t\t\t\t\t<view @tap=\"give_coupon_close\" :data-url=\"give_coupon_close_url\" class=\"coupon-del flex-x-center flex-y-center\">\r\n\t\t\t\t\t\t<image src=\"/static/img/coupon-del.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-x-center\">\r\n\t\t\t\t\t\t<view class=\"coupon-info\">\r\n\t\t\t\t\t\t\t<view class=\"flex-x-center coupon-get\">获得{{give_coupon_num}}张{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view style=\"background:#f5f5f5;padding:10rpx 0\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in give_coupon_list\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"index < 3\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"coupon-coupon\">\r\n\t\t\t\t\t\t\t\t\t\t<view :class=\"item.type==1?'pt_img1':'pt_img2'\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"pt_left\" :class=\"item.type==1?'':'bg2'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.type==1\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.type==2\">礼品券</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.type==3\"><text class=\"t1\">{{item.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.type==4\">抵运费</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"item.type==1 || item.type==4\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.minprice>0\">满{{item.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"pt_right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.type==1\">代金券</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.type==2\">礼品券</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.type==3\">计次券</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.type==4\">运费抵扣券</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"t4\" v-if=\"item.bid>0\">适用商家：{{item.bname}}</view> -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"t3\">有效期至 {{item.yxqdate}}</view> -->\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"coupon_num\" v-if=\"item.givenum > 1\">×{{item.givenum}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @tap=\"goto\" data-url=\"/pages/coupon/mycoupon\" class=\"flex-x-center coupon-btn\">前往查看</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<uni-popup id=\"dialogOpenWeapp\" ref=\"dialogOpenWeapp\" type=\"dialog\" :maskClick=\"false\">\r\n\t\t\t<view style=\"background:#fff;padding:50rpx;position:relative;border-radius:20rpx\">\r\n\t\t\t\t<view style=\"height:80px;line-height:80px;width:200px;margin:0 auto;font-size: 18px;text-align:center;font-weight:bold;text-align:center;color:#333\">恭喜您支付成功</view>\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<wx-open-launch-weapp :username=\"payorder.payafter_username\" :path=\"payorder.payafter_path\">\r\n\t\t\t\t\t<script type=\"text/wxtag-template\">\r\n\t\t\t\t\t\t<div style=\"background:#FD4A46;height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;margin-top:15px;color: #fff;font-size: 15px;font-weight:bold;text-align:center\">{{payorder.payafterbtntext}}</div>\r\n\t\t\t\t\t</script>\r\n\t\t\t\t</wx-open-launch-weapp>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view style=\"height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size: 14px;text-align:center\" @tap=\"goto\" :data-url=\"detailurl\">查看订单详情</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t\r\n\t\t<uni-popup id=\"dialogPayconfirm\" ref=\"dialogPayconfirm\" type=\"dialog\" :maskClick=\"false\">\r\n\t\t\t<uni-popup-dialog type=\"info\" title=\"支付确认\" content=\"是否已完成支付\" @confirm=\"PayconfirmFun\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n        \r\n        <!-- 元宝s -->\r\n        <view v-if=\"yuanbaopay==1 && open_pay\" style=\"width: 100%;height: 100%;position: fixed;z-index: 10;background-color: #000;opacity: 0.45;top: 0;\"></view>\r\n        <view v-if=\"yuanbaopay==1 && open_pay\" style=\"width: 90%;position: fixed;z-index: 11;left: 5%;top:25%;background-color:#fff ;\">\r\n            <view class=\"paytype\">\r\n                <view class=\"f2\">\r\n                    <view class=\"item\" v-if=\"wxpay==1 && (wxpay_type==0 || wxpay_type==1 || wxpay_type==2 || wxpay_type==3)\" @tap.stop=\"changeradio\" data-typeid=\"2\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>微信支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='2' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"wxpay==1 && wxpay_type==22\" @tap.stop=\"changeradio\" data-typeid=\"22\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>微信支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='22' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n\t\t\t\t\t\r\n                    \r\n                    <view class=\"item\" v-if=\"alipay==2\" @tap.stop=\"changeradio\" data-typeid=\"23\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='23' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"alipay==1\" @tap.stop=\"changeradio\" data-typeid=\"3\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='3' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n                    \r\n                    <block v-if=\"more_alipay==1\">\r\n                        <view class=\"item\" v-if=\"alipay2==1\" @tap.stop=\"changeradio\" data-typeid=\"31\">\r\n                            <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付2</view>\r\n                            <view class=\"radio\" :style=\"typeid=='31' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                        </view>\r\n                        <view class=\"item\" v-if=\"alipay3==1\" @tap.stop=\"changeradio\" data-typeid=\"32\">\r\n                            <view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝支付3</view>\r\n                            <view class=\"radio\" :style=\"typeid=='32' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                        </view>\r\n                    </block>\r\n                    \r\n                    <view class=\"item\" v-if=\"baidupay==1\" @tap.stop=\"changeradio\" data-typeid=\"11\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/pay-money.png\"/>在线支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='11' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"toutiaopay==1\" @tap.stop=\"changeradio\" data-typeid=\"12\">\r\n                        <view class=\"t1\"><image class=\"img\" src=\"/static/img/pay-money.png\"/>在线支付</view>\r\n                        <view class=\"radio\" :style=\"typeid=='12' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"moneypay==1\" @tap.stop=\"changeradio\" data-typeid=\"1\">\r\n                        <view class=\"t1 flex\">\r\n                            <image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n                            <view class=\"flex-col\"><text>{{t('余额')}}支付555</text><text style=\"font-size:22rpx;font-weight:normal\">可用余额<text style=\"color:#FC5729\">￥{{userinfo.money}}</text></text></view>\r\n                        </view>\r\n                        <view class=\"radio\" :style=\"typeid=='1' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n                    </view>\r\n\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t<view class=\"item\" v-if=\"heipay==1\" @tap.stop=\"changeradio\" data-typeid=\"1\">\r\n\t\t\t\t\t    <view class=\"t1 flex\">\r\n\t\t\t\t\t        <image class=\"img\" src=\"/static/img/pay-money.png\"/>\r\n\t\t\t\t\t        <view class=\"flex-col\"><text>{{t('抵扣积分')}}支付</text><text style=\"font-size:22rpx;font-weight:normal\">可用抵扣<text style=\"color:#FC5729\">￥{{userinfo.heiscore}}</text></text></view>\r\n\t\t\t\t\t    </view>\r\n\t\t\t\t\t    <view class=\"radio\" :style=\"typeid=='1' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- heipay -->\r\n                </view>\r\n            </view>\r\n            <view style=\"overflow: hidden;width: 100%;\">\r\n                <view style=\"width: 300rpx;float: left;\">\r\n                    <view>\r\n                        <button class=\"btn\" @tap=\"close_pay\" style=\"margin-bottom: 20rpx;background-color: #999;\">取消</button>\r\n                    </view>\r\n                </view>\r\n                <view style=\"width: 300rpx;float: right;\">\r\n                    <view>\r\n                        <button class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\" v-if=\"typeid != '0'\" style=\"margin-bottom: 20rpx;\">确定</button>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        <!-- 元宝e -->\r\n\t</block>\r\n    <view @tap=\"closeInvite\" v-if=\"invite_status && invite_free\" style=\"width:100%;height: 100%;background-color: #000;position: fixed;opacity: 0.5;z-index: 99;top:0\"></view>\r\n    <view v-if=\"invite_status && invite_free\" style=\"width: 700rpx;margin: 0 auto;position: fixed;top:10%;left: 25rpx;z-index: 100;\">\r\n        <view @tap=\"gotoInvite\" style=\"background-color: #fff;border-radius: 20rpx;overflow: hidden;width: 100%;min-height: 700rpx;\">\r\n            <image :src=\"invite_free.pic\" mode=\"widthFix\" style=\"width: 100%;height: auto;\"></image>\r\n        </view>\r\n        <view @tap=\"closeInvite\" v-if=\"invite_status && invite_free\" style=\"width: 80rpx;height: 80rpx;line-height: 80rpx;text-align: center;font-size: 30rpx;background-color: #fff;margin: 0 auto;border-radius: 50%;margin-top: 20rpx;\">\r\n            X\r\n        </view>\r\n    </view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n            isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tdetailurl:'',\r\n\t\t\ttourl:'',\r\n\t\t\ttypeid:'0',\r\n\t\t\tsandepay:0,\r\n\t\t\twxpay:0,\r\n\t\t\twxpay_type:0,\r\n\t\t\talipay:0,\r\n\t\t\tbaidupay:0,\r\n\t\t\tbid:0,\r\n\t\t\ttoutiaopay:0,\r\n\t\t\tmoneypay:0,\r\n\t\t\theipay:0,\r\n\t\t\tcancod:0,\r\n\t\t\tdaifu:0,\r\n\t\t\tdaifu_txt:'好友代付',\r\n\t\t\tpay_month:0,\r\n\t\t\tpay_transfer:0,\r\n\t\t\tcodtxt:'',\r\n\t\t\tpay_month_txt:'',\r\n\t\t\tgive_coupon_list:[],\r\n\t\t\tgive_coupon_num:0,\r\n\t\t\tuserinfo:[],\r\n\t\t\tpaypwd: '',\r\n\t\t\thiddenmodalput: true,\r\n\t\t\tpayorder: {},\r\n\t\t\tsan_pay: {},\r\n\t\t\ttmplids: [],\r\n\t\t\tgive_coupon_show: false,\r\n\t\t\tgive_coupon_close_url: \"\",\r\n\t\t\tmore_alipay : 0,\r\n\t\t\talipay2 : 0,\r\n\t\t\talipay3 : 0,\r\n\t\t\tpaypal:0,\r\n\r\n\t\t\t//元宝支付\r\n\t\t\tyuanbao_money:0,//现金\r\n\t\t\ttotal_yuanbao:0,//总元宝\r\n\t\t\tyuanbao_msg:'',//元宝文字描述\r\n\t\t\tyuanbaopay:0,//是否开启元宝支付\r\n\t\t\topen_pay:false,//打开支付选项\r\n\t\t\tpay_type:'',//支付类型（新增）\r\n\t\t\ts_base_info:[],\r\n\t\t\t\r\n\t\t\tinvite_free:'',\r\n\t\t\tinvite_status:false,\r\n\t\t\tfree_tmplids:'',\r\n\t\t\tsharepic:app.globalData.initdata.logo,\r\n\t\t\t\r\n\t\t\tis_shop_pay:false,\r\n\t\t\tcharge_status:0\r\n    };\r\n  },\r\n\tonShareAppMessage: function () {\r\n\t\tvar that = this;\r\n\t\tvar title = '您有一份好友代付待查收，请尽快处理！';\r\n\t\tvar sharepic = that.sharepic;\r\n\t\tvar sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;\r\n\t\tvar sharedata = this._sharewx({title:title,tolink:sharelink,pic:sharepic});\r\n\t\tconsole.log('pay share data')\r\n\t\tconsole.log(sharedata)\r\n\t\treturn sharedata;\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar that = this;\r\n\t\tvar title = '您有一份好友代付待查收，请尽快处理1！';\r\n\t\tvar sharepic = that.sharepic;\r\n\t\tvar sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;\r\n\t\tvar sharewxdata = this._sharewx({title:title,tolink:sharelink,pic:sharepic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\tvar link = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+(sharewxdata.path).split('?')[0];\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query,\r\n\t\t\tlink:link\r\n\t\t}\r\n\t},\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tconsole.log(this.opt);\r\n\t\tif(this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t    switch_shop_pay:function()\r\n\t\t{\r\n\t\t\tthis.is_shop_pay = !this.is_shop_pay;\r\n\t\t},\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tvar thisurl = '';\r\n\t\t\tif(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\r\n\t\t\t\tthisurl = location.href;\r\n\t\t\t}\r\n\t\t\tapp.post('ApiPay/pay', {orderid: that.opt.id,thisurl:thisurl,tourl:that.tourl,scene:app.globalData.scene}, function (res) {\r\n\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.error(res.msg);\r\n\r\n          if(res.msg == '未实名认证！')\r\n          {\r\n            setTimeout(function () {\r\n              app.goto(res.url);\r\n            }, 2000)\r\n          }\r\n\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n                \r\n\t\t\t\tthat.san_pay = res.san_pay;\r\n\t\t\t\tthat.sandepay = res.sandepay;\r\n\t\t\t\tthat.wxpay = res.wxpay;\r\n\t\t\t\tthat.wxpay_type = res.wxpay_type;\r\n\t\t\t\tthat.alipay = res.alipay;\r\n\t\t\t\tthat.baidupay = res.baidupay;\r\n\t\t\t\tthat.toutiaopay = res.toutiaopay;\r\n\t\t\t\tthat.cancod = res.cancod;\r\n\t\t\t\tthat.codtxt = res.codtxt;\r\n\t\t\t\tthat.daifu = res.daifu;\r\n\t\t\t\tthat.daifu_txt = res.daifu_txt;\r\n\t\t\t\tthat.pay_money = res.pay_money;\r\n\t\t\t\tthat.pay_money_txt = res.pay_money_txt;\r\n\t\t\t\tthat.moneypay = res.moneypay;\r\n\t\t\t\tthat.heipay = res.heipay;\r\n\t\t\t\tthat.pay_transfer = res.pay_transfer;\r\n\t\t\t\tthat.pay_transfer_info = res.pay_transfer_info;\r\n\t\t\t\tthat.pay_month = res.pay_month;\r\n\t\t\t\tthat.pay_month_txt = res.pay_month_txt;\r\n\t\t\t\tthat.payorder = res.payorder;\r\n\t\t\t\tthat.bid = that.payorder['bid'];\r\n\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\tthat.s_base_info = res.s_base_info;\r\n\t\t\t\tthat.charge_status = res.charge_status;\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(that.s_base_info);\r\n\t\t\t\tif(that.s_base_info.length!=0&&that.charge_status!=0)\r\n\t\t\t\t{\r\n\t\t\t\t\t that.typeid = 2;\r\n\t\t\t\t\t that.is_shop_pay = true;\r\n\t\t\t\t}\r\n\t\t\t\tthat.give_coupon_list = res.give_coupon_list;\r\n\t\t\t\tif(that.give_coupon_list){\r\n\t\t\t\t\tfor(var i in that.give_coupon_list){\r\n\t\t\t\t\t\tthat.give_coupon_num += that.give_coupon_list[i]['givenum'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.detailurl = res.detailurl;\r\n\t\t\t\tthat.tourl = res.tourl;\r\n        \r\n\t\t\t\tthat.paypal = res.paypal || 0;\r\n\t\t\t\t\r\n\t\t\t\tthat.more_alipay = res.more_alipay;\r\n\t\t\t\tthat.alipay2 = res.alipay2;\r\n\t\t\t\tthat.alipay3 = res.alipay3;\r\n\t\t\t\tthat.yuanbao_money = res.yuanbao_money;\r\n\t\t\t\tthat.total_yuanbao = res.total_yuanbao;\r\n\t\t\t\tthat.yuanbao_msg   = res.yuanbao_msg;\r\n\t\t\t\tthat.yuanbaopay    = res.yuanbaopay;\r\n\t\t\t\tif(that.wxpay){\r\n\t\t\t\t\tif(that.wxpay_type == 22){\r\n\t\t\t\t\t\tthat.typeid = 22;\t\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.typeid = 2;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(that.moneypay){\r\n\t\t\t\t\tthat.typeid = 1;\r\n\t\t\t\t}else if(that.alipay){\r\n\t\t\t\t\tthat.typeid = 3;\r\n\t\t\t\t\tif(that.alipay == 2){\r\n\t\t\t\t\t\tthat.typeid = 23;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(that.more_alipay){\r\n\t\t\t\t\tif(that.alipay2){\r\n\t\t\t\t\t\tthat.typeid = 31;\r\n\t\t\t\t\t}\r\n                if(that.alipay3){\r\n\t\t\t\t\t\tthat.typeid = 32;\r\n                }\r\n\t\t\t\t}else if(that.baidupay){\r\n\t\t\t\t\tthat.typeid = 11;\r\n\t\t\t\t}else if(that.toutiaopay){\r\n\t\t\t\t\tthat.typeid = 12;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.payorder.money==0 && that.payorder.score>0){\r\n\t\t\t\t\tthat.typeid = 1;\r\n\t\t\t\t}\r\n\t\t\t\tif(res.invite_free){\r\n\t\t\t\t\t\tthat.invite_free = res.invite_free;\r\n\t\t\t\t}\r\n\t\t\t\tif(res.free_tmplids){\r\n\t\t\t\t\t\tthat.free_tmplids = res.free_tmplids;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\t\r\n\t\t\t\tif(that.opt && that.opt.paypal == 'success'){\r\n\t\t\t\t\tthat.typeid = 51;\r\n\t\t\t\t\tapp.showLoading('支付中');\r\n          app.post('ApiPay/paypalRedirect', {orderid: that.opt.id,paymentId:that.opt.paymentId,PayerID:that.opt.PayerID}, function (res) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\t\tif(that.invite_free){\r\n\t\t\t\t\t\t\t\t\tthat.invite_status = true;\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\t\tif (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.give_coupon_show = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthat.give_coupon_close_url = that.tourl;\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t//uni.navigateBack();\r\n\t\t\t\t\t\t\t\t\t\t\tthat.gotourl(that.tourl,'reLaunch');\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}else if (res.status == 0){\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n          });\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    getpwd: function (done, val) {\r\n\t\t\tthis.paypwd = val;\r\n\t\t\tthis.topay({currentTarget:{dataset:{typeid:1}}});\r\n    },\r\n    changeradio: function (e) {\r\n      var that = this;\r\n      var typeid = e.currentTarget.dataset.typeid;\r\n      that.typeid = typeid;\r\n\t\t\tconsole.log(typeid)\r\n    },\r\n\tcheck_base_info:function()\r\n\t{\r\n\t\tlet info = [];\r\n\t\tfor(let j=0;j<this.s_base_info.length;j++)\r\n\t\t{\r\n\t\t\tlet item = this.s_base_info[j];\r\n\t\t\tif(item['pay_total']>item['balance'])\r\n\t\t\t{\r\n\t\t\t\t info.push(item);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn info;\r\n\t},\r\n    topay: function (e) {\r\n      var that = this;\r\n      var typeid = that.typeid;\r\n      var orderid = this.payorder.id;\r\n\t  \r\n\t  \r\n\t  if(that.is_shop_pay==true)\r\n\t  {\r\n\t\t  let infos = that.check_base_info();\r\n\t\t  if(infos.length!=0)\r\n\t\t  {\r\n\t\t\t    app.error(\"对应店铺余额不足,请前去充值!\");\r\n\t\t\t\t// uni.showToast({\r\n\t\t\t\t// \ttitle: \"对应店铺余额不足,请前去充值!\",\r\n\t\t\t\t// \ticon: 'none',\r\n\t\t\t\t// \tduration: 1500\r\n\t\t\t\t// });\r\n\t\t\t\t\r\n\t\t\t   uni.showModal({\r\n\t\t\t\ttitle: '对应店铺余额不足,请前去充值!',\r\n\t\t\t\tcontent: \"\",\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl:\"/pagesExt/maidan/pay?bid=\"+that.bid\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t  \r\n\t\t\t  return ;\r\n\t\t  }\r\n        //商店支付;        \r\n\t\tapp.post('ApiPay/pay', { \r\n\t\t\t\t        op:'submit',\r\n\t\t\t\t\t\torderid: orderid,\r\n\t\t\t\t\t    typeid: typeid,paypwd: that.paypwd,\r\n\t\t\t\t\t\tpay_type:that.pay_type,\r\n\t\t\t\t\t\t'is_shop_pay':that.is_shop_pay?that.is_shop_pay:false\r\n\t\t\t\t\t}, function (res) {\r\n\t             app.showLoading(false);\r\n\t             if (res.status == 0) {\r\n\t                 app.error(res.msg);\r\n\t                 return;\r\n\t             }\r\n\t             if (res.status == 2) {\r\n\t                 app.success(res.msg);\r\n\t                 that.subscribeMessage(function () {\r\n\t                     if(that.invite_free){\r\n\t                         that.invite_status = true;\r\n\t                     }else{\r\n\t                         setTimeout(function () {\r\n\t                             if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n\t                                 that.give_coupon_show = true;\r\n\t                                 that.give_coupon_close_url = that.tourl;\r\n\t                             } else {\r\n\t                                 that.gotourl(that.tourl,'reLaunch');\r\n\t                             }\r\n\t                         }, 1000);\r\n\t                     }\r\n\t                 });\r\n\t               return;\r\n\t             }\r\n\t         });\r\n\t\t\t \r\n\t\t\t return '';\r\n\t  }\r\n\t  \r\n      if (typeid == 1) { //余额支付\r\n        if(that.userinfo.haspwd && that.paypwd==''){\r\n\t\t\t\t\tthat.$refs.dialogInput.open();return;\r\n        }\r\n        app.confirm('确定用' + that.t('余额') + '支付吗?', function () {\r\n\t\t\tapp.showLoading('提交中');\r\n            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid,paypwd: that.paypwd,pay_type:that.pay_type}, function (res) {\r\n                app.showLoading(false);\r\n                if (res.status == 0) {\r\n                    app.error(res.msg);\r\n                    return;\r\n                }\r\n                if (res.status == 2) {\r\n                    app.success(res.msg);\r\n                    that.subscribeMessage(function () {\r\n                        if(that.invite_free){\r\n                            that.invite_status = true;\r\n                        }else{\r\n                            setTimeout(function () {\r\n                                if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                    that.give_coupon_show = true;\r\n                                    that.give_coupon_close_url = that.tourl;\r\n                                } else {\r\n                                    that.gotourl(that.tourl,'reLaunch');\r\n                                }\r\n                            }, 1000);\r\n                        }\r\n                    });\r\n                  return;\r\n                }\r\n            });\r\n        });\r\n      }\r\n\t   else if(typeid == 88)\r\n\t  {\r\n\t\t  app.confirm('确定用' + that.t('黑积分') + '支付吗?', function () {\r\n\t\t  \tapp.showLoading('提交中');\r\n\t\t      \r\n\t\t\t  app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid,paypwd: that.paypwd,pay_type:that.pay_type}, function (res) {\r\n\t\t          app.showLoading(false);\r\n\t\t          if (res.status == 0) {\r\n\t\t              app.error(res.msg);\r\n\t\t              return;\r\n\t\t          }\r\n\t\t          if (res.status == 2) {\r\n\t\t              app.success(res.msg);\r\n\t\t              that.subscribeMessage(function () {\r\n\t\t                  if(that.invite_free){\r\n\t\t                      that.invite_status = true;\r\n\t\t                  }else{\r\n\t\t                      setTimeout(function () {\r\n\t\t                          if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n\t\t                              that.give_coupon_show = true;\r\n\t\t                              that.give_coupon_close_url = that.tourl;\r\n\t\t                          } else {\r\n\t\t                              that.gotourl(that.tourl,'reLaunch');\r\n\t\t                          }\r\n\t\t                      }, 1000);\r\n\t\t                  }\r\n\t\t              });\r\n\t\t            return;\r\n\t\t          }\r\n\t\t      });\r\n\t\t  });\r\n\t  }\r\n\t  else if (typeid == 2) \r\n\t  { //微信支付\r\n\t\tconsole.log(app)\r\n\t\tapp.showLoading('提交中');\r\n        app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n\t\t\tapp.showLoading(false);\r\n          if (res.status == 0) {\r\n            app.error(res.msg);\r\n            return;\r\n          }\r\n          if (res.status == 2) {\r\n            //无需付款\r\n            app.success(res.msg);\r\n            that.subscribeMessage(function () {\r\n                if(that.invite_free){\r\n                    that.invite_status = true;\r\n                }else{\r\n                  setTimeout(function () {\r\n                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                      that.give_coupon_show = true;\r\n                      that.give_coupon_close_url = that.tourl;\r\n                    } else {\r\n                      that.gotourl(that.tourl,'reLaunch');\r\n                    }\r\n                  }, 1000);\r\n                }\r\n            });\r\n            return;\r\n          }\r\n          var opt = res.data;\r\n          if (app.globalData.platform == 'wx') {\r\n\t\t\t\t\t\tif(that.payorder.type == 'shop' || that.wxpay_type == 2){\r\n\t\t\t\t\t\t\tif(opt.orderInfo){\r\n\t\t\t\t\t\t\t\tconsole.log('requestOrderPayment1');\r\n\t\t\t\t\t\t\t\twx.requestOrderPayment({\r\n\t\t\t\t\t\t\t\t\t'timeStamp': opt.timeStamp,\r\n\t\t\t\t\t\t\t\t\t'nonceStr': opt.nonceStr,\r\n\t\t\t\t\t\t\t\t\t'package': opt.package,\r\n\t\t\t\t\t\t\t\t\t'signType': opt.signType ? opt.signType : 'MD5',\r\n\t\t\t\t\t\t\t\t\t'paySign': opt.paySign,\r\n\t\t\t\t\t\t\t\t\t'orderInfo':opt.orderInfo,\r\n\t\t\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                            if(that.invite_free){\r\n                                                that.invite_status = true;\r\n                                            }else{\r\n                                                setTimeout(function () {\r\n                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                        that.give_coupon_show = true;\r\n                                                        that.give_coupon_close_url = that.tourl;\r\n                                                    } else {\r\n                                                        that.gotourl(that.tourl,'reLaunch');\r\n                                                    }\r\n                                                }, 1000);\r\n                                            }\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tconsole.log('requestOrderPayment2');\r\n\t\t\t\t\t\t\t\twx.requestOrderPayment({\r\n\t\t\t\t\t\t\t\t\t'timeStamp': opt.timeStamp,\r\n\t\t\t\t\t\t\t\t\t'nonceStr': opt.nonceStr,\r\n\t\t\t\t\t\t\t\t\t'package': opt.package,\r\n\t\t\t\t\t\t\t\t\t'signType': opt.signType ? opt.signType : 'MD5',\r\n\t\t\t\t\t\t\t\t\t'paySign': opt.paySign,\r\n\t\t\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                            if(that.invite_free){\r\n                                                that.invite_status = true;\r\n                                            }else{\r\n                                                setTimeout(function () {\r\n                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                        that.give_coupon_show = true;\r\n                                                        that.give_coupon_close_url = that.tourl;\r\n                                                    } else {\r\n                                                        that.gotourl(that.tourl,'reLaunch');\r\n                                                    }\r\n                                                }, 1000);\r\n                                            }\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\t'provider':'wxpay',\r\n\t\t\t\t\t\t\t\t'timeStamp': opt.timeStamp,\r\n\t\t\t\t\t\t\t\t'nonceStr': opt.nonceStr,\r\n\t\t\t\t\t\t\t\t'package': opt.package,\r\n\t\t\t\t\t\t\t\t'signType': opt.signType ? opt.signType : 'MD5',\r\n\t\t\t\t\t\t\t\t'paySign': opt.paySign,\r\n\t\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                        if(that.invite_free){\r\n                                            that.invite_status = true;\r\n                                        }else{\r\n                                            setTimeout(function () {\r\n                                                if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                    that.give_coupon_show = true;\r\n                                                    that.give_coupon_close_url = that.tourl;\r\n                                                } else {\r\n                                                    that.gotourl(that.tourl,'reLaunch');\r\n                                                }\r\n                                            }, 1000);\r\n                                        }\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else if(app.globalData.platform == 'mp'){\r\n\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tfunction jsApiCall(){\r\n\t\t\t\t\t\t\tWeixinJSBridge.invoke('getBrandWCPayRequest',opt,function(res){\r\n\t\t\t\t\t\t\t\t\tif(res.err_msg == \"get_brand_wcpay_request:ok\" ) {\r\n\t\t\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                            if(that.invite_free){\r\n                                                that.invite_status = true;\r\n                                            }else{\r\n                                                setTimeout(function () {\r\n                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                        that.give_coupon_show = true;\r\n                                                        that.give_coupon_close_url = that.tourl;\r\n                                                    } else {\r\n                                                        that.gotourl(that.tourl,'reLaunch');\r\n                                                    }\r\n                                                }, 1000);\r\n                                            }\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (typeof WeixinJSBridge == \"undefined\"){\r\n\t\t\t\t\t\t\tif( document.addEventListener ){\r\n\t\t\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', jsApiCall, false);\r\n\t\t\t\t\t\t\t}else if (document.attachEvent){\r\n\t\t\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', jsApiCall); \r\n\t\t\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', jsApiCall);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tjsApiCall();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t/*\r\n\t\t\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\t\t\tjweixin.chooseWXPay({\r\n\t\t\t\t\t\t\ttimestamp: opt.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符\r\n\t\t\t\t\t\t\tnonceStr: opt.nonceStr, // 支付签名随机串，不长于 32 位\r\n\t\t\t\t\t\t\tpackage: opt.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\\*\\*\\*）\r\n\t\t\t\t\t\t\tsignType: opt.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'\r\n\t\t\t\t\t\t\tpaySign: opt.paySign, // 支付签名\r\n\t\t\t\t\t\t\tsuccess: function (res2) {\r\n\t\t\t\t\t\t\t\t// 支付成功后的回调函数\r\n\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\t\t\tif (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.give_coupon_show = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthat.give_coupon_close_url = that.tourl;\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.gotourl(that.tourl,'reLaunch');\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t*/\r\n\t\t\t\t\t}else if(app.globalData.platform == 'h5'){\r\n\t\t\t\t\t\tlocation.href = opt.wx_url  + '&redirect_url='+encodeURIComponent(location.href.split('#')[0] + '#'+that.tourl);\r\n\t\t\t\t\t}else if(app.globalData.platform == 'app'){\r\n\t\t\t\t\t\tconsole.log(opt)\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t'provider':'wxpay',\r\n\t\t\t\t\t\t\t'orderInfo': opt,\r\n\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                    if(that.invite_free){\r\n                                        that.invite_status = true;\r\n                                    }else{\r\n                                        setTimeout(function () {\r\n                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                that.give_coupon_show = true;\r\n                                                that.give_coupon_close_url = that.tourl;\r\n                                            } else {\r\n                                                that.gotourl(that.tourl,'reLaunch');\r\n                                            }\r\n                                        }, 1000);\r\n                                    }\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else if(app.globalData.platform == 'qq'){\r\n\t\t\t\t\t\tqq.requestWxPayment({\r\n\t\t\t\t\t\t\turl: opt.wx_url,\r\n\t\t\t\t\t\t\treferer: opt.referer,\r\n\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                    if(that.invite_free){\r\n                                        that.invite_status = true;\r\n                                    }else{\r\n                                        setTimeout(function () {\r\n                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                that.give_coupon_show = true;\r\n                                                that.give_coupon_close_url = that.tourl;\r\n                                            } else {\r\n                                                that.gotourl(that.tourl,'reLaunch');\r\n                                            }\r\n                                        }, 1000);\r\n                                    }\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail(res) { }\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} else if (typeid == 29) { //杉德支付\r\n\t\tconsole.log(app)\r\n\t\tapp.showLoading('提交中');\r\n        app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n\t\t\tapp.showLoading(false);\r\n          if (res.status == 0) {\r\n            app.error(res.msg);\r\n            return;\r\n          }\r\n          if (res.status == 2) {\r\n            //无需付款\r\n            app.success(res.msg);\r\n            that.subscribeMessage(function () {\r\n                if(that.invite_free){\r\n                    that.invite_status = true;\r\n                }else{\r\n                  setTimeout(function () {\r\n                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                      that.give_coupon_show = true;\r\n                      that.give_coupon_close_url = that.tourl;\r\n                    } else {\r\n                      that.gotourl(that.tourl,'reLaunch');\r\n                    }\r\n                  }, 1000);\r\n                }\r\n            });\r\n            return;\r\n          }\r\n\r\n\t\t\t\t})\r\n\t\t\t} else if (typeid == 3 || typeid == 31 || typeid == 32) { //支付宝支付\r\n\t\t\t\tapp.showLoading('提交中');\r\n                app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n                            console.log(res)\r\n                            app.showLoading(false);\r\n                  if (res.status == 0) {\r\n                    app.error(res.msg);\r\n                    return;\r\n                  }\r\n                  if (res.status == 2) {\r\n                    //无需付款\r\n                    app.success(res.msg);\r\n                    that.subscribeMessage(function () {\r\n                        if(that.invite_free){\r\n                            that.invite_status = true;\r\n                        }else{\r\n                          setTimeout(function () {\r\n                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                              that.give_coupon_show = true;\r\n                              that.give_coupon_close_url = that.tourl;\r\n                            } else {\r\n                              that.gotourl(that.tourl,'reLaunch');\r\n                            }\r\n                          }, 1000);\r\n                        }\r\n                    });\r\n                    return;\r\n                  }\r\n                  var opt = res.data;\r\n\t\t\t\t\tif (app.globalData.platform == 'alipay') {\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t'provider':'alipay',\r\n\t\t\t\t\t\t\t'orderInfo': opt.trade_no,\r\n\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t\t\tif(res2.resultCode == '6001'){\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                    if(that.invite_free){\r\n                                        that.invite_status = true;\r\n                                    }else{\r\n                                        setTimeout(function () {\r\n                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                that.give_coupon_show = true;\r\n                                                that.give_coupon_close_url = that.tourl;\r\n                                            } else {\r\n                                                that.gotourl(that.tourl,'reLaunch');\r\n                                            }\r\n                                        }, 1000);\r\n                                    }\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else if(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\r\n\t\t\t\t\t\tdocument.body.innerHTML = res.data;\r\n\t\t\t\t\t\tdocument.forms['alipaysubmit'].submit();\r\n\t\t\t\t\t}else if(app.globalData.platform == 'app'){\r\n\t\t\t\t\t\tconsole.log('------------alipay----------')\r\n\t\t\t\t\t\tconsole.log(opt)\r\n\t\t\t\t\t\tconsole.log('------------alipay end----------')\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t'provider':'alipay',\r\n\t\t\t\t\t\t\t'orderInfo': opt,\r\n\t\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\t\tconsole.log('------------success----------')\r\n\t\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                    if(that.invite_free){\r\n                                        that.invite_status = true;\r\n                                    }else{\r\n                                        setTimeout(function () {\r\n                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                that.give_coupon_show = true;\r\n                                                that.give_coupon_close_url = that.tourl;\r\n                                            } else {\r\n                                                that.gotourl(that.tourl,'reLaunch');\r\n                                            }\r\n                                        }, 1000);\r\n                                    }\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n      } else if (typeid == '11') {\r\n\t\t\tapp.showLoading('提交中');\r\n            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tswan.requestPolymerPayment({\r\n\t\t\t\t\t\t'orderInfo': res.orderInfo,\r\n\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                if(that.invite_free){\r\n                                    that.invite_status = true;\r\n                                }else{\r\n                                    setTimeout(function () {\r\n                                        if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                            that.give_coupon_show = true;\r\n                                            that.give_coupon_close_url = that.tourl;\r\n                                        } else {\r\n                                            that.gotourl(that.tourl,'reLaunch');\r\n                                        }\r\n                                    }, 1000);\r\n                                }\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\tif(res2.errCode!=2){\r\n\t\t\t\t\t\t\t\tapp.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t});\r\n\t\t} else if (typeid == '12') {\r\n\t\t\tapp.showLoading('提交中');\r\n            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tconsole.log(res.orderInfo);\r\n\t\t\t\t\ttt.pay({\r\n\t\t\t\t\t\t'service':5,\r\n\t\t\t\t\t\t'orderInfo': res.orderInfo,\r\n\t\t\t\t\t\t'success': function (res2) {\r\n\t\t\t\t\t\t\tif (res2.code === 0) {\r\n\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                    if(that.invite_free){\r\n                                        that.invite_status = true;\r\n                                    }else{\r\n                                        setTimeout(function () {\r\n                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                that.give_coupon_show = true;\r\n                                                that.give_coupon_close_url = that.tourl;\r\n                                            } else {\r\n                                                that.gotourl(that.tourl,'reLaunch');\r\n                                            }\r\n                                        }, 1000);\r\n                                    }\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'fail': function (res2) {\r\n\t\t\t\t\t\t\tapp.alert(JSON.stringify(res2))\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t} else if (typeid == '22') {\r\n\t\t\t\tif (app.globalData.platform == 'wx') {\r\n\t\t\t\t\twx.login({\r\n\t\t\t\t\t\tsuccess:function(res){\r\n\t\t\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\t\t\t\t\tapp.post('ApiPay/getYunMpauthParams',{jscode: res.code},function(res){\r\n\t\t\t\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\t\t\t\tapp.post('https://showmoney.cn/scanpay/fixed/mpauth',res.params,function(res2){\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(res2.sessionKey);\r\n\t\t\t\t\t\t\t\t\t\tapp.post('ApiPay/getYunUnifiedParams',{orderid: orderid,sessionKey:res2.sessionKey},function(res3){\r\n\t\t\t\t\t\t\t\t\t\t\tapp.post('https://showmoney.cn/scanpay/unified',res3.params,function(res4){\r\n\t\t\t\t\t\t\t\t\t\t\t\tif(res4.respcd == '09'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twx.requestPayment({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttimeStamp: res4.timeStamp,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnonceStr: res4.nonceStr,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpackage: res4.package,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsignType: res4.mpSignType,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpaySign: res4.mpSign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function success(result) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tapp.success('付款完成');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n                                                                if(that.invite_free){\r\n                                                                    that.invite_status = true;\r\n                                                                }else{\r\n                                                                    setTimeout(function () {\r\n                                                                        if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                                                                            that.give_coupon_show = true;\r\n                                                                            that.give_coupon_close_url = that.tourl;\r\n                                                                        } else {\r\n                                                                            that.gotourl(that.tourl,'reLaunch');\r\n                                                                        }\r\n                                                                    }, 1000);\r\n                                                                }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfail: function (res5) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//app.alert(JSON.stringify(res5))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert(res4.errorDetail);\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.log('登录失败！' + res.errMsg)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;\r\n\t\t\t\t\turl += '&op=submit&orderid='+orderid+'&typeid=22';\r\n\t\t\t\t\tlocation.href = url;\r\n\t\t\t\t}\r\n\t\t\t} else if (typeid == '23') {\r\n\t\t\t\t//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;\r\n\t\t\t\t//url += '&op=submit&orderid='+orderid+'&typeid=23';\r\n\t\t\t\t//location.href = url;\r\n\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\tthat.$refs.dialogPayconfirm.open();\r\n\t\t\t\t}, 1000);\r\n\r\n\t\t\t\tapp.goto('/pages/index/webView2?orderid='+orderid+'&typeid=23'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id);\r\n\t\t\t\treturn ;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 23},function(res){\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tapp.goto('url::'+res.url);\r\n\t\t\t\t});\r\n\t\t\t} else if (typeid == '24') {\r\n\t\t\t\t//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;\r\n\t\t\t\t//url += '&op=submit&orderid='+orderid+'&typeid=23';\r\n\t\t\t\t//location.href = url;\r\n\r\n\t\t\t\tapp.goto('/pages/index/webView2?orderid='+orderid+'&typeid=24');\r\n\t\t\t\treturn ;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 24},function(res){\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tapp.goto('url::'+res.url);\r\n\t\t\t\t});\r\n\t\t\t}else if (typeid == 'yuanbao') { \r\n                //元宝支付\r\n\t\t\t\tconsole.log(app)\r\n                \r\n                var total_yuanbao = that.total_yuanbao-0;\r\n                var u_yuanbao     = that.userinfo.yuanbao-0;\r\n                if(total_yuanbao>u_yuanbao){\r\n                    app.alert(that.t('元宝')+'不足' );\r\n                    return;\r\n                }\r\n                that.open_pay = true;\r\n                that.pay_type = 'yuanbao';\r\n\t\t\t} else if (typeid == '51') {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\tif(app.globalData.platform == 'app'){\r\n\t\t\t\t\t\t\tconst wv = plus.webview.create(\"\",\"custom-webview\",{\r\n\t\t\t\t\t\t\t\ttop: uni.getSystemInfoSync().statusBarHeight + 44\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\twv.loadURL(res.data)\r\n\t\t\t\t\t\t\tvar currentWebview = that.$scope.$getAppWebview();\r\n\t\t\t\t\t\t\tcurrentWebview.append(wv);\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.goto('url::'+res.data);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\ttopay2:function(){\r\n\t\t\tvar that = this;\r\n            var orderid = this.payorder.id;\r\n\t\t\tapp.confirm('确定要' + that.codtxt + '吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 4}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\t\t//无需付款\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tthat.gotourl(that.tourl,'reLaunch');\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\ttopayMonth:function(){\r\n\t\t\tvar that = this;\r\n            var orderid = this.payorder.id;\r\n\t\t\tapp.confirm('确定要' + that.pay_month_txt + '支付吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 41}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.status == 2) {\r\n\t\t\t\t\t\t//无需付款\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\t\tthat.gotourl(that.tourl,'reLaunch');\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\ttopayTransfer:function(e){\r\n\t\t\tvar that = this;\r\n            var orderid = this.payorder.id;\r\n\t\t\tapp.confirm('确定要' + e.currentTarget.dataset.text + '吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 5}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\t//需审核付款\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tthat.gotourl('/pagesExt/order/orderlist','reLaunch');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else if (res.status == 2) {\r\n\t\t\t\t\t\t//无需付款\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.subscribeMessage(function () {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t\tthat.gotourl('transfer?id='+orderid,'reLaunch');\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else{\r\n                        app.error(res.msg);\r\n                        return;\r\n                    }\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\tgive_coupon_close:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar tourl = e.currentTarget.dataset.url;\r\n\t\t\tthis.give_coupon_show = false;\r\n\t\t\tthat.gotourl(tourl,'reLaunch');\r\n\t\t},\r\n\t\tgotourl:function(tourl, opentype){\r\n\t\t\tvar that = this;\r\n\t\t\tif(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){\r\n\t\t\t\tif (tourl.indexOf('miniProgram::') === 0) {\r\n\t\t\t\t\t//其他小程序\r\n\t\t\t\t\ttourl = tourl.slice(13);\r\n\t\t\t\t\tvar tourlArr = tourl.split('|');\r\n\t\t\t\t\tconsole.log(tourlArr)\r\n\t\t\t\t\tthat.showOpenWeapp();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tapp.goto(tourl, opentype);\r\n\t\t},\r\n\t\tshowOpenWeapp:function(){\r\n\t\t\tthis.$refs.dialogOpenWeapp.open();\r\n\t\t},\r\n\t\tcloseOpenWeapp:function(){\r\n\t\t\tthis.$refs.dialogOpenWeapp.close();\r\n\t\t},\r\n\t\tPayconfirmFun:function(){\r\n\t\t\tthis.gotourl(this.tourl,'reLaunch');\r\n\t\t},\r\n        close_pay:function(){\r\n            var that = this;\r\n            that.open_pay = false;\r\n        },\r\n        closeInvite:function(){\r\n           var that = this;\r\n           that.invite_status = false;\r\n           setTimeout(function () {\r\n               if (that.give_coupon_list && that.give_coupon_list.length > 0) {\r\n                   that.give_coupon_show = true;\r\n                   that.give_coupon_close_url = that.tourl;\r\n               } else {\r\n                   that.gotourl(that.tourl,'reLaunch');\r\n               }\r\n           }, 1000);\r\n        },\r\n        gotoInvite:function(){\r\n            var that = this;\r\n            var free_tmplids = that.free_tmplids;\r\n            if(free_tmplids && free_tmplids.length > 0){\r\n            \tuni.requestSubscribeMessage({\r\n            \t\ttmplIds: free_tmplids,\r\n            \t\tsuccess:function(res) {\r\n            \t\t\tconsole.log(res)\r\n            \t\t},\r\n            \t\tfail:function(res){\r\n            \t\t\tconsole.log(res)\r\n            \t\t}\r\n            \t})\r\n            }\r\n            app.goto('/pagesExt/invite_free/index','reLaunch')\r\n        },\r\n\t\t\t\ttodaifu:function(e){\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar platform = app.getplatform()\r\n\t\t\t\t\tvar id = that.payorder.id\r\n\t\t\t\t\tif(platform == 'mp' || platform == 'h5'){\r\n\t\t\t\t\t\tvar sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;\r\n\t\t\t\t\t\tthis._sharemp({title:\"您有一份好友代付待查收，请尽快处理~\",link:sharelink,pic:that.sharepic})\r\n\t\t\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\t\t\t}else if(platform == 'app'){\r\n\t\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t\t\t\t\t\tsuccess: function (res){\r\n\t\t\t\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\t\t\t\tsharedata.title = '您的好友向您发出了代付请求';\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = '您有一份好友代付待查收，请尽快处理~';\r\n\t\t\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = '';\r\n\t\t\t\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.error('该终端不支持此操作');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n\r\n.top{width:100%;display:flex;flex-direction:column;align-items:center;padding-top:60rpx}\r\n.top .f1{height:60rpx;line-height:60rpx;color:#939393;font-size:24rpx;}\r\n.top .f2{color:#101010;font-weight:bold;font-size:72rpx;height:120rpx;line-height:120rpx}\r\n.top .f2 .t1{font-size:44rpx}\r\n.top .f2 .t3{font-size:50rpx}\r\n.top .f3{color:#FC5729;font-size:26rpx;height:70rpx;line-height:70rpx}\r\n\r\n.paytype{width:94%;margin:20rpx 3% 80rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}\r\n.paytype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}\r\n\r\n.paytype .f2{padding:0 30rpx}\r\n.paytype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}\r\n.paytype .f2 .item:last-child{border-bottom:0}\r\n.paytype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#222222;font-size:30rpx;font-weight:bold}\r\n.paytype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}\r\n\r\n.paytype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\r\n.paytype .f2 .item .radio .radio-img{width:100%;height:100%}\r\n\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:10rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n.daifu-btn{background: #fc5729;}\r\n.op{width:94%;margin:20rpx 3%;display:flex;align-items:center;margin-top:40rpx}\r\n.op .btn{flex:1;height:100rpx;line-height:100rpx;background:#07C160;width:90%;margin:0 10rpx;border-radius:10rpx;color: #fff;font-size:28rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}\r\n.op .btn .img{width:48rpx;height:48rpx;margin-right:20rpx}\r\n\r\n.give-coupon { width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 2000; background-color: rgba(0, 0, 0, 0.5); }\r\n.give-coupon .coupon-block { margin-top: -140rpx; position: relative; }\r\n.give-coupon .coupon-info { width: 559rpx; border-radius: 0 0 30rpx 30rpx; background-color: #fff; margin-top: -20rpx; padding: 20rpx 0 20rpx 0; }\r\n.give-coupon .coupon-one { width: 100%; height: 164rpx; margin-bottom: 10rpx; }\r\n.give-coupon .coupon-bg { width: 514rpx; height: 164rpx; border-radius: 10rpx; box-shadow: 2rpx 2rpx 30rpx #ddd; padding: 14rpx; }\r\n.give-coupon .coupon-bg-1 { width: 100%; height: 100%; border: 2rpx #ff4544 dashed; border-radius: 10rpx; padding: 0 20rpx; }\r\n.give-coupon .coupon-del { position: absolute; right: 34rpx; top: 224rpx; width: 90rpx; height: 90rpx; }\r\n.give-coupon .coupon-del image{ width: 30rpx; height: 30rpx; }\r\n.give-coupon .coupon-text { color: #707070; margin-top: 24rpx; margin-bottom: 34rpx; font-size: 9pt; }\r\n.give-coupon .coupon-text::before { content: ' '; margin-right: 32rpx; width: 50rpx; height: 1rpx; background-color: #707070; overflow: hidden; margin-top: 21rpx; }\r\n.give-coupon .coupon-text::after { content: ' '; margin-left: 32rpx; width: 50rpx; height: 1rpx; background-color: #707070; overflow: hidden; margin-top: 21rpx; }\r\n.give-coupon .coupon-btn { position: relative;margin:0 auto;width:340rpx;height:70rpx;line-height:70rpx;background:linear-gradient(90deg,#F9475F,#EF155B);color:#fff;border-radius:40rpx;margin-top:20rpx}\r\n.give-coupon .coupon-btn image { width: 374rpx; height: 96rpx; }\r\n.give-coupon .coupon-get{ margin-top: 4rpx; margin-bottom: 20rpx; color: #ff4544; font-size: 13pt; }\r\n\r\n.give-coupon .coupon-coupon{width:100%;display:flex;padding:0 20rpx;margin:10rpx 0;position: relative;}\r\n.give-coupon .coupon-coupon .pt_img1{background:url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM3M2FmNjA7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7nu7/oibJfMjwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMzkuNSw0Ny43NlY2MS42MmE1LDUsMCwwLDEsMCw5LjgydjMuMzhhNSw1LDAsMCwxLDAsOS44MlY4OGE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM5YTUsNSwwLDAsMSwwLDkuODJ2My4zOWE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM4YTUsNSwwLDAsMSwwLDkuODJ2MTMuODdoMjd2LTEzMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zOS41IC00Ny43NikiLz48L3N2Zz4=);background-size: 147%;height:140rpx;border-bottom-left-radius:16rpx;border-top-left-radius:16rpx;width:4%}\r\n.give-coupon .coupon-coupon .pt_img2{background:url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM2ZmM1ZmE7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7ok53oibJfMTwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMCwwVjEzLjg2YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCwyMy42OHYzLjM5QTUsNSwwLDAsMSw0LjEsMzIsNSw1LDAsMCwxLDAsMzYuODl2My4zOWE1LDUsMCwwLDEsNC4xLDQuOTFBNSw1LDAsMCwxLDAsNTAuMXYzLjM5QTUsNSwwLDAsMSw0LjEsNTguNCw1LDUsMCwwLDEsMCw2My4zMXYzLjM4QTUsNSwwLDAsMSw0LjEsNzEuNiw1LDUsMCwwLDEsMCw3Ni41MVY3OS45YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCw4OS43MnYzLjM5QTUsNSwwLDAsMSw0LjEsOTgsNSw1LDAsMCwxLDAsMTAyLjkzdjMuMzlhNSw1LDAsMCwxLDQuMSw0LjkxQTUsNSwwLDAsMSwwLDExNi4xNFYxMzBIMjdWMFoiLz48L3N2Zz4=);background-size: 147%;height:140rpx;border-bottom-left-radius:16rpx;border-top-left-radius:16rpx;width:4%}\r\n.give-coupon .coupon-coupon .pt_left{background: #73af60;height:140rpx;color: #FFF;padding-bottom:20rpx;padding-right:10rpx;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.give-coupon .coupon-coupon .pt_left.bg2{background:#6fc5fa}\r\n.give-coupon .coupon-coupon .pt_left .f1{font-size:34rpx;text-align:center}\r\n.give-coupon .coupon-coupon .pt_left .t0{padding-right:10rpx;}\r\n.give-coupon .coupon-coupon .pt_left .t1{font-size:46rpx;}\r\n.give-coupon .coupon-coupon .pt_left .t2{padding-left:10rpx}\r\n.give-coupon .coupon-coupon .pt_left .f2{font-size:24rpx;text-align:center;overflow:hidden}\r\n.give-coupon .coupon-coupon .pt_right{background: #fff;width:66%;display:flex;height:140rpx;text-align: left;padding:20rpx 30rpx;border-top-right-radius:16rpx;border-bottom-right-radius:16rpx;position:relative}\r\n.give-coupon .coupon-coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}\r\n.give-coupon .coupon-coupon .pt_right .f1 .t1{font-size:36rpx;color:#2c3e50;height:40rpx;line-height: 40rpx;overflow: hidden;}\r\n.give-coupon .coupon-coupon .pt_right .f1 .t2{height:60rpx;line-height:60rpx;font-size:24rpx;color:#727272;}\r\n.give-coupon .coupon-coupon .pt_right .f1 .t2_1{height:40rpx;line-height:40rpx}\r\n.give-coupon .coupon-coupon .pt_right .f1 .t3{font-size:24rpx;color:#2c3e50}\r\n.give-coupon .coupon-coupon .pt_right .f1 .t4{font-size:24rpx;color:#555555}\r\n.give-coupon .coupon-coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-25rpx;border-radius:25rpx;width:140rpx;height:50rpx;line-height:50rpx;background:#07c160;color:#fff;}\r\n\r\n.give-coupon .coupon_num{position:absolute;top:50rpx;right:30rpx;font-size:30rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115056359\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}