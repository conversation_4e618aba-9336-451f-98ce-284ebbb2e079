{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?ec92", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?bdcc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?bdfb", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?b114", "uni-app:///pagesExt/cycle/orderList.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?252e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/orderList.vue?aea5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "keyword", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "toCycleList", "app", "toDetail", "changetab", "console", "uni", "scrollTop", "duration", "getdata", "that", "toclose", "orderid", "setTimeout", "todel", "orderCollect", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqFlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACA;MACAC;IACA;IACAC;MACA;MACAD;IACA;IACAE;MACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MAEA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAR;QAAAf;QAAAE;QAAAG;MAAA;QACAkB;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAT;QACAA;QACAA;UAAAU;QAAA;UACAV;UACAA;UACAW;YACAH;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAZ;QACAA;QACAA;UAAAU;QAAA;UACAV;UACAA;UACAW;YACAH;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;MACAb;QACAA;QACAA;UAAAU;QAAA;UACAV;UACAA;UACAW;YACAH;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3NA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/orderList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/orderList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderList.vue?vue&type=template&id=66fb49df&\"\nvar renderjs\nimport script from \"./orderList.vue?vue&type=script&lang=js&\"\nexport * from \"./orderList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/orderList.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=template&id=66fb49df&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          [1, 2, 3].includes(item.status) &&\n          item.invoice &&\n          item.team &&\n          item.team.status == 2\n        var m0 = item.status == 0 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['全部','待付款','待发货','已发货','已完成','退款']\" :itemst=\"['all','0','1','2','3','10']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t<!-- #ifndef H5 || APP-PLUS -->\n\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<!--  #endif -->\n\t\t<view class=\"order-content\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t<view class=\"order-box\" @tap.stop=\"goto\" :data-url=\"'orderdetail?id=' + item.id\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<text class=\"flex1\">订单号：{{item.ordernum}}</text>\n\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\n\t\t\t\t\t<block v-if=\"item.status==1\">\n\t\t\t\t\t\t\n\t\t\t\t\t\t<block >\n\t\t\t\t\t\t\t<text v-if=\"item.freight_type!=1\" class=\"st1\">待发货</text>\n\t\t\t\t\t\t\t<text v-if=\"item.freight_type==1\" class=\"st1\">待取货</text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\n\n\t\t\t\t\t<text v-if=\"item.status==2 && item.freight_type ==1\" class=\"st2\">已取货</text>\r\n\t\t\t\t\t<text v-if=\"item.status==2 && (item.freight_type ==2 || item.freight_type ==0)\" class=\"st2\">已发货</text>\n\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\n\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"content\" style=\"border-bottom:none\">\n\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'product?id=' + item.proid\">\n\t\t\t\t\t\t<image :src=\"item.propic\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t<text class=\"t1\">{{item.proname}}</text>\n\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.qsnum}}期</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t<text>共{{item.qsnum}}期，每期{{item.num}}件商品 实付:￥{{item.totalprice}} </text>\n\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red\"> 退款中￥{{item.refund_money}}</text>\n\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red\"> 已退款￥{{item.refund_money}}</text>\n\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red\"> 退款申请已驳回</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<block v-if=\"([1,2,3]).includes(item.status) && item.invoice && item.team && item.team.status==2\">\r\n\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/invoice?type=collage&orderid=' + item.id\">发票</view>\r\n\t\t\t\t\t</block>\n\t\t\t\t\t<view @click=\"toDetail\" :data-id=\"item.id\" class=\"btn2\">详情</view>\n\t\t\t\t\t<block v-if=\"item.status==0\">\n\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\n\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + item.payorderid\">去付款</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.status==1\">\n\t\t\t\t\t\t<view v-if=\"item.refund_status==0 || item.refund_status==3\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\n\t\t\t\t\t\t<view @click=\"toCycleList\" :data-id=\"item.id\" class=\"btn2\">配送周期</view>\r\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.status==2\">\n\t\t\t\t\t\t<view v-if=\"item.refund_status==0 || item.refund_status==3\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refund?orderid=' + item.id + '&price=' + item.totalprice\">申请退款</view>\n\t\t\t\t\t\t<!-- <view class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/week/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" v-if=\"item.freight_type!=3 && item.freight_type!=4\">查看物流</view> -->\n\t\t\t\t\t\t<!-- <view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"orderCollect\" :data-id=\"item.id\">确认收货</view> -->\r\n\t\t\t\t\t\t<view @click=\"toCycleList\" :data-id=\"item.id\" class=\"btn2\">配送周期</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.status==4\">\n\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t\topt:{},\n\t\t\t\tloading:false,\n\t\t\t\tisload: false,\n\t\t\t\tmenuindex:-1,\n\t\t\t\tst: 'all',\n\t\t\t\tdatalist: [],\n\t\t\t\tpagenum: 1,\n\t\t\t\tnomore: false,\n\t\t\t\tnodata: false,\n\t\t\t\tkeyword:'',\n\t\t};\n\t},\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n\t},\r\n\tonShow() {\r\n\t\tthis.getdata();\r\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n\tmethods: {\r\n\t\ttoCycleList:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\tvar url='/pagesExt/cycle/planList?id='+id\r\n\t\t\tapp.goto(url);\r\n\t\t},\r\n\t\ttoDetail:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\tapp.goto('/pagesExt/cycle/orderDetail?id='+id);\r\n\t\t},\n\t\tchangetab: function (st) {\r\n\t\t\tconsole.log(st,'st');\n\t\t  this.st = st;\n\t\t  uni.pageScrollTo({\n\t\t\tscrollTop: 0,\n\t\t\tduration: 0\n\t\t  });\n\t\t  this.getdata();\n\t\t},\n\t\tgetdata: function (loadmore) {\r\n\t\t\t\n\t\t\t\tif(!loadmore){\n\t\t\t\t\tthis.pagenum = 1;\n\t\t\t\t\tthis.datalist = [];\n\t\t\t\t}\n\t\t  var that = this;\n\t\t  var pagenum = that.pagenum;\n\t\t  var st = that.st;\n\t\t\t\tthat.nodata = false;\n\t\t\t\tthat.nomore = false;\n\t\t\t\tthat.loading = true;\n\t\t  app.post('ApiCycle/orderlist', {st: st,pagenum: pagenum,keyword:that.keyword}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\tvar data = res.datalist;\n\t\t\tif (pagenum == 1) {\n\t\t\t\t\t\tthat.datalist = data;\n\t\t\t  if (data.length == 0) {\n\t\t\t\tthat.nodata = true;\n\t\t\t  }\n\t\t\tthat.loaded();\n\t\t\t}else{\n\t\t\t  if (data.length == 0) {\n\t\t\t\tthat.nomore = true;\n\t\t\t  } else {\n\t\t\t\tvar datalist = that.datalist;\n\t\t\t\tvar newdata = datalist.concat(data);\n\t\t\t\tthat.datalist = newdata;\n\t\t\t  }\n\t\t\t}\n\t\t  });\n\t\t},\n\t\ttoclose: function (e) {\n\t\t  var that = this;\n\t\t  var orderid = e.currentTarget.dataset.id;\n\t\t  app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiCycle/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t  app.success(data.msg);\n\t\t\t  setTimeout(function () {\n\t\t\t\tthat.getdata();\n\t\t\t  }, 1000);\n\t\t\t});\n\t\t  });\n\t\t},\n\t\ttodel: function (e) {\n\t\t  var that = this;\n\t\t  var orderid = e.currentTarget.dataset.id;\n\t\t  app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\t\tapp.showLoading('删除中');\n\t\t\tapp.post('ApiCycle/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t  app.success(data.msg);\n\t\t\t  setTimeout(function () {\n\t\t\t\tthat.getdata();\n\t\t\t  }, 1000);\n\t\t\t});\n\t\t  });\n\t\t},\n\t\torderCollect: function (e) {\n\t\t  var that = this;\n\t\t  var orderid = e.currentTarget.dataset.id;\n\t\t  app.confirm('确定要收货吗?', function () {\n\t\t\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiCycle/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t  app.success(data.msg);\n\t\t\t  setTimeout(function () {\n\t\t\t\tthat.getdata();\n\t\t\t  }, 1000);\n\t\t\t});\n\t\t  });\n\t\t},\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n\t\t\tthis.getdata(false);\n\t\t}\n\t}\n};\r\n</script>\r\n<style>\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px;border: none;}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#333}\n.order-box .head .f1 image{width:34rpx;height:34rpx;margin-right:4px}\n.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\n.order-box .head .st1{ width: 204rpx; color: #ffc702; text-align: right; }\n.order-box .head .st2{ width: 204rpx; color: #ff4246; text-align: right; }\n.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\n.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.order-box .content .detail .x1{ flex:1}\n.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n\n.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n\n.btn1{margin-left:20rpx;margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098336\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}