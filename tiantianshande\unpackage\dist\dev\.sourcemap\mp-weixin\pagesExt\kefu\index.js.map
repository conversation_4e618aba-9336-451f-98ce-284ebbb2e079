{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?1354", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?c916", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?55d8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?6e6b", "uni-app:///pagesExt/kefu/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?74c5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/kefu/index.vue?d11c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "isload", "loading", "pre_url", "msgtipsShow", "msgtips", "bid", "token", "nowtime", "pagenum", "datalist", "message", "trimMessage", "faceshow", "nomore", "sss", "onLoad", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "interval0", "updateMessageTime", "prevtime", "getdatalist", "sendMessage", "uni", "title", "icon", "duration", "aid", "mid", "msgtype", "content", "platform", "console", "type", "sendimg", "receiveMessage", "setTimeout", "toggleFaceBox", "scrollToBottom", "scrollTop", "onInputFocus", "onPageScroll", "messageChange", "transformMsgHtml", "selectface", "getTime", "todaystart"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoD9wB;AACA;AACA;AACA;AAAA,eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IAEA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAC;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAE;UACAF;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;QACA;QACA;QACA;UACAC;QACA;QACA;UACAf;QACA;UACAA;QACA;MACA;MACA;IACA;IACAgB;MACA;MACA;MACAL;MACAC;QAAAhB;QAAAG;MAAA;QACAY;QACA;QACA;UACA;YACAX;UACA;UACAW;UACAA;UACA;YACAA;UACA,QAEA;UACAA;QACA;UACAA;QACA;MACA;IACA;IACAM;MACA;MACA;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;QACA;UACA;YACAC;YACAC;YACA3B;YACA4B;YACAC;YACAC;YACAjC;UACA;UACAkC;UACAf;YAAAgB;YAAAvC;UAAA;UACAsB;UACAA;UACAA;QACA;MACA;IACA;IACAkB;MACA;MACAjB;QACA;UACA;UACA;YACAU;YACAC;YACA3B;YACA4B;YACAC;YACAC;YACAjC;UACA;UACAmB;YAAAgB;YAAAvC;UAAA;QACA;MACA;IACA;IACAyC;MACA;MACAH;MACAA;MACA;QACA;QACA1B;QACAU;QACA;QACAgB;QACAI;QACA;QACA;QACA;UAAA;UACA;UACA;YACAN;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;YACAA;UACA;UACAxB;UACAU;UACAA;UACAoB;YACApB;UACA;QACA;MACA;MACA;IACA;IACAqB;MACA;IACA;IACAC;MACA;MACAF;QACAb;UACAgB;UACAb;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACAb;MACA;MACA;QACAA;MACA;MACA;IACA;IACAc;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAC;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvSA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/kefu/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/kefu/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=cb707140&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/kefu/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=cb707140&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    wxface: function () {\n      return import(\n        /* webpackChunkName: \"components/wxface/wxface\" */ \"@/components/wxface/wxface.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.trimMessage ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"message-list\" style=\"padding-bottom: 200rpx;\">\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"message-time\" v-if=\"item.formatTime\">{{item.formatTime}}</view>\r\n\t\t\t\t<view class=\"message-item\" v-if=\"item.isreply==1\">\r\n\t\t\t\t\t<image class=\"message-avatar\" mode=\"aspectFill\" :src=\"item.uheadimg\"></image>\r\n\t\t\t\t\t<view class=\"message-text-left\">\r\n\t\t\t\t\t\t<view class=\"arrow-box arrow-left\">\r\n\t\t\t\t\t\t\t<image class=\"arrow-icon\" :src=\"pre_url+'/static/img/arrow-white.png'\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"message-text\">\r\n\t\t\t\t\t\t\t<parse :content=\"item.content\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"message-item\" style=\"justify-content:flex-end\" v-else>\r\n\t\t\t\t\t<view class=\"message-text-right\">\r\n\t\t\t\t\t\t<view class=\"arrow-box arrow-right\">\r\n\t\t\t\t\t\t\t<image class=\"arrow-icon\" :src=\"pre_url+'/static/img/arrow-green.png'\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"message-text\">\r\n\t\t\t\t\t\t\t<parse :content=\"item.content\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image class=\"message-avatar\" mode=\"aspectFill\" :src=\"item.headimg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<view class=\"input-box notabbarbot\" id=\"input-box\">\r\n\t\t\t<view class=\"input-form\">\r\n\t\t\t\t<image @tap=\"sendimg\" class=\"pic-icon\" :src=\"pre_url+'/static/img/msg-pic.png'\"></image>\r\n\t\t\t\t<input @confirm=\"sendMessage\" @focus=\"onInputFocus\" @input=\"messageChange\" class=\"input\" :confirmHold=\"true\" confirmType=\"send\" cursorSpacing=\"20\" type=\"text\" :value=\"message\" maxlength=\"-1\"/>\r\n\t\t\t\t<image @tap=\"toggleFaceBox\" class=\"face-icon\" :src=\"pre_url+'/static/img/face-icon.png'\"></image>\r\n\t\t\t\t<!-- <button class=\"send-button\" v-if=\"!trimMessage\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t\t发送\r\n\t\t\t\t</button> -->\r\n\t\t\t\t<button @tap=\"sendMessage\" class=\"send-button-active\" v-if=\"trimMessage\" :style=\"{background:t('color1')}\">\r\n\t\t\t\t\t发送\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view>{{sss}}</view> -->\r\n\t\t\t<wxface v-if=\"faceshow\" @selectface=\"selectface\"></wxface>\r\n\t\t</view>\r\n\t\t<view :class=\"'anit ' + (msgtipsShow == 1?'show':(msgtipsShow == 2?'hide':''))\" @tap=\"goto\" :data-url=\"'index?bid='+msgtips.bid\">{{msgtips.unickname}}：{{msgtips.content}}</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nvar interval0;\r\nvar interval1;\r\nvar interval2;\r\n\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n      isload: false,\r\n      loading: false,\r\n\t\t\tpre_url: '',\r\n\t\t\t\r\n\t\t\tmsgtipsShow:0,\r\n\t\t\tmsgtips:{},\r\n\t\t\tbid:0,\r\n\t\t\ttoken:'',\r\n\t\t\tnowtime:'',\r\n      pagenum: 1,\r\n      datalist: [],\r\n      message: \"\",\r\n      trimMessage: \"\",\r\n      faceshow: false,\r\n      nomore: false,\r\n\t\t\tsss:0,\r\n    };\r\n  },\r\n  onLoad: function (opt){\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.bid = this.opt.bid || 0;\r\n\t\tthis.pre_url = app.globalData.pre_url;\r\n\t\t\r\n\t\tthis.getdata();\r\n  },\r\n  onUnload: function () {\r\n    clearInterval(interval0);\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKefu/index',{},function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.token = res.token;\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tinterval0 = setInterval(function () {\r\n\t\t\t\t\tthat.nowtime++;\r\n\t\t\t\t}, 1000);\r\n\t\t\t});\r\n\t\t},\r\n    updateMessageTime: function () {\r\n      var that = this;\r\n      var datalist = this.datalist;\r\n\t\t\tfor(var i in datalist){\r\n        var thistime = parseInt(datalist[i].createtime);\r\n        var prevtime = 0;\r\n        if (i > 0) {\r\n          prevtime = parseInt(datalist[i - 1].createtime);\r\n        }\r\n        if (thistime - prevtime > 600) {\r\n          datalist[i].formatTime = that.getTime(thistime);\r\n        } else {\r\n          datalist[i].formatTime = '';\r\n        }\r\n      }\r\n      this.datalist = datalist;\r\n    },\r\n    getdatalist: function () {\r\n      var that = this;\r\n      if (that.loading) return;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiKefu/getmessagelist',{bid:that.bid,pagenum: that.pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar datalist = res.data;\r\n\t\t\t\tif (datalist.length > 0) {\r\n\t\t\t\t\tfor (var i in datalist) {\r\n\t\t\t\t\t\tdatalist[i].content = that.transformMsgHtml(datalist[i].msgtype, datalist[i].content);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.datalist = datalist.concat(that.datalist);\r\n\t\t\t\t\tthat.updateMessageTime();\r\n\t\t\t\t\tif (that.pagenum == 1) {\r\n\t\t\t\t\t\tthat.scrollToBottom();\r\n\t\t\t\t\t} else {\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.pagenum = that.pagenum + 1;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t}\r\n\t\t\t});\r\n    },\r\n    sendMessage: function (e) {\r\n      var that = this;\r\n      var message = this.message;\r\n      if (message.length > 2000) {\r\n        uni.showToast({\r\n          title: \"单条消息不能超过2000字\",\r\n          icon: \"none\",\r\n          duration: 1000\r\n        });\r\n      } else {\r\n        if (message.replace(/^\\s*|\\s*$/g, \"\")) {\r\n          var msgdata = {\r\n            aid: app.globalData.aid,\r\n            mid: app.globalData.mid,\r\n            bid: that.bid,\r\n            msgtype: 'text',\r\n            content: message,\r\n\t\t\t\t\t\tplatform:app.globalData.platform,\r\n\t\t\t\t\t\tpre_url:app.globalData.pre_url\r\n          };\r\n\t\t\t\t\tconsole.log(msgdata)\r\n          app.sendSocketMessage({type: 'tokefu',data:msgdata});\r\n          that.message = \"\";\r\n          that.trimMessage = \"\";\r\n          that.faceshow = false\r\n        }\r\n      }\r\n    },\r\n    sendimg: function () {\r\n      var that = this;\r\n      app.chooseImage(function (data) {\r\n        for (var i = 0; i < data.length; i++) {\r\n          var message = data[i];\r\n          var msgdata = {\r\n            aid: app.globalData.aid,\r\n            mid: app.globalData.mid,\r\n            bid: that.bid,\r\n            msgtype: 'image',\r\n            content: message,\r\n\t\t\t\t\t\tplatform:app.globalData.platform,\r\n\t\t\t\t\t\tpre_url:app.globalData.pre_url\r\n          };\r\n          app.sendSocketMessage({type: 'tokefu',data: msgdata});\r\n        }\r\n      }, 3);\r\n    },\r\n    receiveMessage: function (data) {\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log('zz-**')\r\n\t\t\tconsole.log(data)\r\n\t\t\tif((data.type == 'tokehu' || data.type == 'tokefu') && that.bid == data.data.bid ) {\r\n\t\t\t\tvar message = data.data\r\n\t\t\t\tmessage.content = that.transformMsgHtml(message.msgtype, message.content);\r\n\t\t\t\tthat.datalist = that.datalist.concat([message]);\r\n\t\t\t\t//that.sss = Math.random();\r\n\t\t\t\tconsole.log('11-----------')\r\n\t\t\t\tsetTimeout(that.updateMessageTime, 100);\r\n\t\t\t\tthis.scrollToBottom();\r\n\t\t\t\treturn true;\r\n\t\t\t\tif (that.bid != message.bid) { //其他商家发来的信息\r\n\t\t\t\t\tvar content = message.content;\r\n\t\t\t\t\tif (message.msgtype == 'image') {\r\n\t\t\t\t\t\tcontent = '[图片]';\r\n\t\t\t\t\t} else if (message.msgtype == 'voice') {\r\n\t\t\t\t\t\tcontent = '[语音]';\r\n\t\t\t\t\t} else if (message.msgtype == 'video') {\r\n\t\t\t\t\t\tcontent = '[小视频]';\r\n\t\t\t\t\t} else if (message.msgtype == 'music') {\r\n\t\t\t\t\t\tcontent = '[音乐]';\r\n\t\t\t\t\t} else if (message.msgtype == 'news') {\r\n\t\t\t\t\t\tcontent = '[图文]';\r\n\t\t\t\t\t} else if (message.msgtype == 'link') {\r\n\t\t\t\t\t\tcontent = '[链接]';\r\n\t\t\t\t\t} else if (message.msgtype == 'miniprogrampage') {\r\n\t\t\t\t\t\tcontent = '[小程序]';\r\n\t\t\t\t\t} else if (message.msgtype == 'location') {\r\n\t\t\t\t\t\tcontent = '[地理位置]';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tmessage.content = content;\r\n\t\t\t\t\tthat.msgtipsShow = 1;\r\n\t\t\t\t\tthat.msgtips = message;\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.msgtipsShow = 2;\r\n\t\t\t\t\t}, 10000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn false;\r\n    },\r\n    toggleFaceBox: function () {\r\n      this.faceshow = !this.faceshow\r\n    },\r\n    scrollToBottom: function () {\r\n      var that = this;\r\n      setTimeout(function () {\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: 10000,\r\n\t\t\t\t\tduration:0\r\n\t\t\t\t});\r\n      },300);\r\n    },\r\n    onInputFocus: function (e) {\r\n      this.faceshow = false\r\n    },\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY == 0 && !that.nomore) {\r\n\t\t\t\tthis.getdatalist();\r\n\t\t\t}\r\n\t\t},\r\n    messageChange: function (e) {\r\n      this.message = e.detail.value;\r\n      this.trimMessage = e.detail.value.trim();\r\n    },\r\n    transformMsgHtml: function (msgtype, content) {\r\n      if (msgtype == 'miniprogrampage') {\r\n        var contentdata = JSON.parse(content);\r\n        content = '<div style=\"font-size:16px;font-weight:bold;height:25px;line-height:25px\">' + contentdata.Title + '</div><img src=\"' + contentdata.ThumbUrl + '\" style=\"width:400rpx\"/>';\r\n      }\r\n      if (msgtype == 'image') {\r\n        content = '<img src=\"' + content + '\" style=\"width:400rpx\"/>';\r\n      }\r\n      return content;\r\n    },\r\n    selectface: function (face) {\r\n      this.message = \"\" + this.message + face;\r\n\t\t\tthis.trimMessage = this.message.trim();\r\n    },\r\n    getTime: function (createtime) {\r\n      var t = this.nowtime - createtime;\r\n      if (t > 0) {\r\n        var todaystart = new Date(this.dateFormat(this.nowtime, \"Y-m-d 00:00:00\")).getTime();\r\n        todaystart = todaystart / 1000;\r\n        var lastdaystart = todaystart - 86400;\r\n        if (t <= 180) {\r\n          return '刚刚';\r\n        }\r\n        if (createtime > todaystart) {\r\n          return this.dateFormat(createtime, \"H:i\");\r\n        }\r\n        if (createtime > lastdaystart) {\r\n          return '昨天' + this.dateFormat(createtime, \"H:i\");\r\n        }\r\n        return this.dateFormat(createtime, 'Y年m月d日 H:i:s');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{display: block;min-height: 100%;\tbox-sizing: border-box;\tbackground: #f4f4f4;color: #222;}\r\n.message-list {\tpadding-left: 25rpx;\tpadding-right: 25rpx;\tpadding-bottom: 20rpx;padding-bottom:180rpx;}\r\n.message-item {display:flex;padding:20rpx 0}\r\n.message-time {\twidth:100%;padding-top: 20rpx;\tpadding-bottom: 10rpx;\ttext-align: center;display: inline-block;\tcolor: #999;\tfont-size: 24rpx;}\r\n.message-avatar {\twidth: 90rpx;\theight: 90rpx;\tborder-radius: 50%;}\r\n.message-text {\tmax-width: 525rpx;\tmin-height: 64rpx;\tline-height: 50rpx;\tfont-size: 30rpx;\tpadding: 20rpx 30rpx;word-break:break-all}\r\n.message-text-left {\tposition: relative;\tbackground-color: #fff;\tmargin-left: 20rpx;\tborder-radius: 12rpx;\tborder: 1rpx solid #dddddd;}\r\n.message-text-right {\tposition: relative;\tbackground-color: #9AE966;\tmargin-right: 20rpx;\tborder-radius: 12rpx;\tborder: 1rpx solid #6DBF58;}\r\n.arrow-box {\tposition: absolute;\twidth: 16rpx;\theight: 24rpx;\ttop: 35rpx;}\r\n.arrow-left {left: -14rpx;}\r\n.arrow-right {right: -14rpx;}\r\n.arrow-icon {\tdisplay: block;\twidth: 100%;\theight: 100%;}\r\n\r\n.input-box {\tposition: fixed;\tz-index: 100;\tbottom: 0;\twidth: 96%;\tmin-height: 100rpx;\tpadding: 15rpx 2%;\tbackground-color: #fff;box-sizing:content-box}\r\n.input-form {\twidth: 100%;\theight: 100%;\tdisplay: flex;\tflex-direction: row;\talign-items: center;}\r\n.input {\tflex: 1;\theight: 66rpx;\tborder: 1rpx solid #ddd;\tpadding: 5rpx 10rpx;\tbackground-color: #fff;\tfont-size: 30rpx;\tborder-radius: 12rpx;}\r\n.pic-icon {\twidth: 54rpx;\theight: 54rpx;\tmargin-right: 18rpx;}\r\n.face-icon {\twidth: 60rpx;\theight: 60rpx;\tmargin-left: 18rpx;}\r\n.faces-box {\twidth: 100%;\theight: 500rpx;}\r\n.single-face {\twidth: 48rpx;\theight: 48rpx;\tmargin: 10rpx;}\r\n.send-button {\twidth: 100rpx;\theight: 62rpx;\tmargin-left: 18rpx;\tborder-radius: 8rpx;\ttext-align: center;\tline-height: 62rpx;\tfont-size: 28rpx;\tborder: 1rpx solid #dddddd;\tcolor: #fff;}\r\n.send-button-active {\twidth: 100rpx;\theight: 62rpx;\tmargin-left: 20rpx;\tborder-radius: 8rpx;\ttext-align: center;\tline-height: 62rpx;\tfont-size: 28rpx;\tborder: 1rpx solid #dddddd;\tcolor: #fff;}\r\n.send-icon {\twidth: 56rpx;\theight: 56rpx;}\r\n\r\n.anit{width: 100%;height: 70rpx;background:#555;position: absolute;color:#fff;font-size: 30rpx;line-height: 70rpx;top: -70rpx;text-align: left;padding:0 20rpx;overflow:hidden}\r\n.show{top: 0rpx;animation: show 0.2s;animation-timing-function:ease;}\r\n@keyframes show{from {top:-70rpx;}to {top:0rpx;}}\r\n.hide{top: -70rpx;animation: hide 0.2s;animation-timing-function:ease;}\r\n@keyframes hide{from {top:0rpx;}to {top:-70rpx;}}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093338\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}