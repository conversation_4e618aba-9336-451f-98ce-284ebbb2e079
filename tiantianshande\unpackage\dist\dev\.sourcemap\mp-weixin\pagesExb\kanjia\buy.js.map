{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?8512", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?87d2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?7924", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?ae2a", "uni-app:///pagesExb/kanjia/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?abea", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/kanjia/buy.vue?74b8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "editorFormdata", "test", "productList", "freightList", "couponList", "couponrid", "coupontype", "address", "needaddress", "linkman", "tel", "freightkey", "freight_price", "pstimetext", "freight_time", "usescore", "totalprice", "product_price", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "joinid", "storedata", "inputLinkman", "inputTel", "<PERSON><PERSON><PERSON><PERSON>", "calculatePrice", "scoredk_money", "scoredk", "changeFreight", "chooseCoupon", "coupon_money", "choosePstime", "itemlist", "uni", "itemList", "success", "pstimeRadioChange", "hidePstimeDialog", "choosestore", "topay", "formdata", "newformdata", "freightid", "storeid", "addressid", "showCouponList", "handleClickMask", "openLocation", "latitude", "longitude", "name", "scale", "openMendian", "editorChooseImage", "console", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "doStoreShowAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoL5wB;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,qDACA,oDACA,sDACA,qDACA,sDACA,wDACA,yDACA,gEACA,2DACA,oDACA,qDACA,qDACA,mDACA,qDACA,sDACA,0DACA,mDACA,sDACA,wDACA;EAEA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAC;YACA;YACA;YAEA;cACA;gBACA;gBAEA;kBACA;oBACA;sBACA;sBACAE;oBACA;kBACA;kBAEAA;oBACA;kBACA;kBAEA;oBACA;sBACAA;oBACA;kBACA;kBAEAtB;gBACA;cACA;YACA;YACAmB;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAL;IACA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAP;MACA;MAEA;MACA;QAAA;QACA;QACA;QACA;UACAQ;QACA;QACA;UAAA;UACAA;QACA;MACA;QACA;MACA;MACA;;MAEA;MACAlB;MACAI;MACAM;MACAA;IACA;IACA;IACAS;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAV;IACA;IACAW;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;MACA;QACAb;QACA;MACA;MACA;QACAD;QACAA;MACA;QACAe;UACAC;UACAC;YACA;cACA;cACAjB;cACAA;YACA;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAvC;MACA;IACA;IACA;IACAwC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;QACApB;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;MACA;MACA;MACA;QACA;UACAA;UAAA;QACA;QACA;UACAqB;QACA;QACAC;MACA;MAEAtB;MACAA;QAAAC;QAAAsB;QAAAhC;QAAAiC;QAAAC;QAAAvC;QAAAC;QAAAK;QAAA6B;MAAA;QACArB;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAd;QACAe;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAjC;IACA;IACAkC;MACA;MACA;MACA;MACA;MACA;MACAlC;QACAvB;QACA0D;QACApC;QACAA;MACA;IACA;IACAqC;MACA;MACA;MACA;MACA;MACA;MACA3D;MACA0D;MACA;MACA;IACA;IACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpiBA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/kanjia/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/kanjia/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=994e4d32&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/kanjia/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=994e4d32&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.freightList, function (item, idx2) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.freightkey == idx2 ? _vm.t(\"color1\") : null\n        var m1 = _vm.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g0 =\n    _vm.isload &&\n    _vm.freightList[_vm.freightkey].minpriceset == 1 &&\n    _vm.freightList[_vm.freightkey].minprice > 0 &&\n    _vm.freightList[_vm.freightkey].minprice * 1 > _vm.product_price * 1\n      ? (_vm.freightList[_vm.freightkey].minprice - _vm.product_price).toFixed(\n          2\n        )\n      : null\n  var l1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].storedata,\n          function (item, idx) {\n            var $orig = _vm.__get_orig(item)\n            var m2 =\n              (idx < 5 || _vm.storeshowall == true) &&\n              _vm.freightList[_vm.freightkey].storekey == idx\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m2: m2,\n            }\n          }\n        )\n      : null\n  var g1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.storeshowall == false &&\n        _vm.freightList[_vm.freightkey].storedata.length > 5\n      : null\n  var m3 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l2 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m7 = _vm.freight_time == item.value ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m7: m7,\n            }\n          }\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"topay\">\r\n\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\r\n\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pages/address/address?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\r\n\t\t\t<view class=\"f1\"><image class=\"img\" src=\"/static/img/address.png\"/></view>\r\n\t\t\t<view class=\"f2 flex1\" v-if=\"address.name\">\r\n\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\r\n\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"/>\r\n\t\t</view>\r\n\t\t<view class=\"buydata\">\r\n\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t<view class=\"img\"><image class=\"img\" :src=\"product.pic\"/></view>\r\n\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">原价: ￥{{product.sell_price}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f3\">￥{{joininfo.now_price}}<text class=\"kanjia_icon\">砍后价</text> × 1</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"freight\">\r\n\t\t\t\t\t<view class=\"f1\">配送方式</view>\r\n\t\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t <block v-for=\"(item, idx2) in freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t <view class=\"freight-li\" :style=\"freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeFreight\" :data-index=\"idx2\">{{item.name}}</view>\r\n\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].minpriceset==1 && freightList[freightkey].minprice > 0 && freightList[freightkey].minprice*1 > product_price*1\">满{{freightList[freightkey].minprice}}元起送，还差{{(freightList[freightkey].minprice - product_price).toFixed(2)}}元</view>\r\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"price\" v-if=\"freightList[freightkey].pstimeset==1\">\r\n\t\t\t\t\t<view class=\"f1\">{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\">{{pstimetext==''?'请选择时间':pstimetext}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"storeitem\" v-if=\"freightList[freightkey].pstype==1\">\r\n\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-freightkey=\"freightkey\" :data-storekey=\"freightList[freightkey].storekey\"><text class=\"iconfont icondingwei\"></text>{{freightList[freightkey].storedata[freightList[freightkey].storekey].name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-for=\"(item, idx) in freightList[freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"freightList[freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view v-if=\"storeshowall==false && (freightList[freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<text class=\"f1\">商品金额</text>\r\n\t\t\t\t\t<text class=\"f2\">¥{{product_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"price\" v-if=\"leadermoney*1>0\">\r\n\t\t\t\t\t<text class=\"f1\">团长优惠</text>\r\n\t\t\t\t\t<text class=\"f2\">-¥{{leadermoney}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<text class=\"f1\">运费</text>\r\n\t\t\t\t\t<text class=\"f2\">+¥{{freight_price}}</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in freightList[freightkey].formdata\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t<textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+idx\">\r\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"scoredk\" v-if=\"userinfo.score2money>0\">\r\n\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\r\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtype==1\">最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t</view>\r\n\t\t\t</checkbox-group>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width: 100%; height:110rpx;\"></view>\r\n\t\t<view class=\"footer flex notabbarbot\">\r\n\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</button>\r\n\t\t</view>\r\n\t\t</form>\r\n\r\n\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in freightList[freightkey].pstimeArr\" :key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\r\n\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :style=\"freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\teditorFormdata:[],\r\n\t\t\ttest:'test',\r\n\t\t\r\n      productList: [],\r\n      freightList: [],\r\n      couponList: [],\r\n      couponrid: 0,\r\n      coupontype: 1,\r\n      address: [],\r\n      needaddress: 1,\r\n      linkman: '',\r\n      tel: '',\r\n      freightkey: 0,\r\n      freight_price: 0,\r\n      pstimetext: '',\r\n      freight_time: '',\r\n      usescore: 0,\r\n      totalprice: '0.00',\r\n      product_price: 0,\r\n      isload: 0,\r\n      storedata: [],\r\n      storeid: '',\r\n      storename: '',\r\n      latitude: '',\r\n      longitude: '',\r\n      leadermoney: 0,\r\n      couponvisible: false,\r\n      pstimeDialogShow: false,\r\n      pstimeIndex: -1,\r\n      product: \"\",\r\n      userinfo: \"\",\r\n      joininfo: \"\",\r\n      weight: \"\",\r\n      goodsnum: 0,\r\n      scorebdkyf: \"\",\r\n      havetongcheng: \"\",\r\n      beizhu: \"\",\r\n      couponkey: 0,\r\n\t\t\tstoreshowall:false,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this; //获取产品信息\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKanjia/buy', {joinid: that.opt.joinid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar freightList = res.freightList;\r\n\t\t\t\tvar userinfo = res.userinfo;\r\n\t\t\t\tvar couponList = res.couponList;\r\n\t\t\t\tvar joininfo = res.joininfo;\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.freightList = freightList;\r\n\t\t\t\tthat.userinfo = userinfo;\r\n\t\t\t\tthat.couponList = couponList;\r\n\t\t\t\tthat.joininfo = joininfo;\r\n\t\t\t\tthat.product_price = joininfo['now_price'];\r\n\t\t\t\tthat.weight = product.weight;\r\n\t\t\t\tthat.goodsnum = 1;\r\n\t\t\t\tthat.address = res.address;\r\n\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\r\n\t\t\t\tthat.havetongcheng = res.havetongcheng;\r\n\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t\tthat.loaded();\r\n\r\n\t\t\t\tif (res.needLocation == 1) {\r\n\t\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\tvar longitude = res.longitude;\r\n\r\n\t\t\t\t\t\tfor (var j in freightList) {\r\n\t\t\t\t\t\t\tif (freightList[j].pstype == 1) {\r\n\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\r\n\r\n\t\t\t\t\t\t\t\tif (storedata) {\r\n\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);\r\n\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\tstoredata.sort(function (a, b) {\r\n\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\r\n\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\r\n\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\tfreightList[j].storedata = storedata;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.freightList = freightList;\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    inputLinkman: function (e) {\r\n      this.linkman = e.detail.value\r\n    },\r\n    inputTel: function (e) {\r\n      this.tel = e.detail.value\r\n    },\r\n    //选择收货地址\r\n    chooseAddress: function () {\r\n      app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\r\n    },\r\n    //计算价格\r\n    calculatePrice: function () {\r\n      var that = this;\r\n      var product_price = parseFloat(that.product_price); //+商品总价\r\n      var address = that.address; //算运费\r\n      var freightdata = that.freightList[that.freightkey];\r\n      if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\r\n        var needaddress = 1;\r\n      } else {\r\n        var needaddress = 0;\r\n      }\r\n      that.needaddress = needaddress;\r\n      var freight_price = freightdata.freight_price;\r\n\r\n\t\t\tvar totalprice = product_price + freight_price;\r\n      if (that.usescore) { //使用积分抵扣\r\n        var scoredk_money = parseFloat(that.userinfo.scoredk_money);\r\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\t\t\t\tif (scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > totalprice * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t\tscoredk_money = totalprice * scoredkmaxpercent * 0.01;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.scorebdkyf=='1' && scoredk_money > 0 && totalprice - scoredk_money < freight_price){ //积分不抵扣运费\r\n\t\t\t\t\tscoredk_money = totalprice - freight_price;\r\n\t\t\t\t}\r\n      } else {\r\n        var scoredk_money = 0;\r\n      }\r\n      var totalprice = totalprice - scoredk_money; //-积分抵扣\r\n\t\t\t\r\n      if (totalprice < 0) totalprice = 0;\r\n      freight_price = freight_price.toFixed(2);\r\n      totalprice = totalprice.toFixed(2);\r\n      that.totalprice = totalprice;\r\n      that.freight_price = freight_price;\r\n    },\r\n\t\t//积分抵扣\r\n\t\tscoredk: function (e) {\r\n\t\t\tvar usescore = e.detail.value[0];\r\n\t\t\tif (!usescore) usescore = 0\r\n\t\t\tthis.usescore = usescore;\r\n\t\t\tthis.calculatePrice();\r\n\t\t},\r\n    changeFreight: function (e) {\r\n      var that = this;\r\n      var index = e.currentTarget.dataset.index;\r\n\t\t\tthis.freightkey = index;\r\n\t\t\tthat.calculatePrice();\r\n    },\r\n    chooseCoupon: function (e) {\r\n\t\t\tvar couponrid = e.rid;\r\n      var couponkey = e.key;\r\n\r\n      if (couponrid == this.couponrid) {\r\n        this.couponkey = 0;\r\n        this.couponrid = 0;\r\n        this.coupontype = 1;\r\n        this.coupon_money = 0;\r\n        this.couponvisible = false;\r\n      } else {\r\n        var couponList = this.couponList;\r\n        var coupon_money = couponList[couponkey]['money'];\r\n        var coupontype = couponList[couponkey]['type'];\r\n        if (coupontype == 4) {\r\n          coupon_money = this.freightprice;\r\n        }\r\n        this.couponkey = couponkey;\r\n        this.couponrid = couponrid;\r\n        this.coupontype = coupontype;\r\n        this.coupon_money = coupon_money;\r\n        this.couponvisible = false;\r\n      }\r\n      this.calculatePrice();\r\n    },\r\n    choosePstime: function () {\r\n      var that = this;\r\n      var freightkey = this.freightkey;\r\n      var freightList = this.freightList;\r\n      var freight = freightList[freightkey];\r\n      var pstimeArr = freightList[freightkey].pstimeArr;\r\n      var itemlist = [];\r\n\r\n      for (var i = 0; i < pstimeArr.length; i++) {\r\n        itemlist.push(pstimeArr[i].title);\r\n      }\r\n      if (itemlist.length == 0) {\r\n        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\r\n        return;\r\n      }\r\n      if (itemlist.length > 6) {\r\n        that.pstimeDialogShow = true;\r\n        that.pstimeIndex = -1;\r\n      } else {\r\n        uni.showActionSheet({\r\n          itemList: itemlist,\r\n          success: function (res) {\r\n\t\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\t\tvar choosepstime = pstimeArr[res.tapIndex];\r\n\t\t\t\t\t\t\tthat.pstimetext = choosepstime.title;\r\n\t\t\t\t\t\t\tthat.freight_time = choosepstime.value;\r\n\t\t\t\t\t\t}\r\n          }\r\n        });\r\n      }\r\n    },\r\n    pstimeRadioChange: function (e) {\r\n      var pstimeIndex = e.currentTarget.dataset.index;\r\n\t\t\tvar freightkey = this.freightkey;\r\n      var freightList = this.freightList;\r\n      var freight = freightList[freightkey];\r\n      var pstimeArr = freightList[freightkey].pstimeArr;\r\n      var choosepstime = pstimeArr[pstimeIndex];\r\n      this.pstimetext = choosepstime.title;\r\n\t\t\tthis.freight_time = choosepstime.value;\r\n      this.pstimeDialogShow = false;\r\n    },\r\n    hidePstimeDialog: function () {\r\n      this.pstimeDialogShow = false\r\n    },\r\n    choosestore: function (e) {\r\n      var storekey = e.currentTarget.dataset.index;\r\n\t\t\tvar freightkey = this.freightkey\r\n\t\t\tvar freightList = this.freightList\r\n\t\t\tfreightList[freightkey].storekey = storekey\r\n      this.freightList = freightList;\r\n    },\r\n    //提交并支付\r\n    topay: function (e) {\r\n      var that = this;\r\n      var freightkey = this.freightkey;\r\n      var freightid = this.freightList[freightkey].id;\r\n      var joinid = this.opt.joinid;\r\n      var addressid = this.address.id;\r\n      var linkman = this.linkman;\r\n      var tel = this.tel;\r\n\t\t\tvar usescore = this.usescore\r\n      if(this.freightList[freightkey].pstype==1){\r\n\t\t\t\tvar storekey = this.freightList[freightkey].storekey\r\n\t\t\t\tvar storeid = this.freightList[freightkey].storedata[storekey].id;\r\n\t\t\t}else{\r\n\t\t\t\tvar storeid = 0;\r\n\t\t\t}\r\n      var freight_time = that.freight_time;\r\n      var needaddress = that.needaddress;\r\n      if (needaddress == 0) addressid = 0;\r\n      if (needaddress == 1 && addressid == undefined) {\r\n        app.error('请选择收货地址');\r\n        return;\r\n      }\r\n      if (this.freightList[freightkey].pstimeset == 1 && freight_time == '') {\r\n        app.error('请选择' + (this.freightList[freightkey].pstype == 0 ? '配送' : '提货') + '时间');\r\n        return;\r\n      }\r\n\r\n\t\t\tvar formdataSet = this.freightList[freightkey].formdata;\r\n      var formdata = e.detail.value;\r\n\t\t\tvar newformdata = {};\r\n\t\t\tfor (var i = 0; i < formdataSet.length;i++){\r\n\t\t\t\tif (formdataSet[i].val3 == 1 && (formdata['form' + i] === '' || formdata['form' + i] === undefined || formdata['form' + i].length==0)){\r\n\t\t\t\t\t\tapp.alert(formdataSet[i].val1+' 必填');return;\r\n\t\t\t\t}\r\n\t\t\t\tif (formdataSet[i].key == 'selector') {\r\n\t\t\t\t\t\tformdata['form' + i] = formdataSet[i].val2[formdata['form' + i]]\r\n\t\t\t\t}\r\n\t\t\t\tnewformdata['form'+i] = formdata['form' + i];\r\n\t\t\t}\r\n\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post('ApiKanjia/createOrder', {joinid: joinid,freightid: freightid,freight_time: freight_time,storeid: storeid,addressid: addressid,linkman: linkman,tel: tel,usescore:usescore,formdata:newformdata}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.error(data.msg);\r\n          return;\r\n        }\r\n        app.goto('/pages/pay/pay?id=' + data.payorderid);\r\n      });\r\n    },\r\n    showCouponList: function () {\r\n      this.couponvisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.couponvisible = false;\r\n    },\r\n\t\topenLocation:function(e){\r\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\tvar frightinfo = this.freightList[freightkey]\r\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\tvar latitude = parseFloat(storeinfo.latitude);\r\n\t\t\tvar longitude = parseFloat(storeinfo.longitude);\r\n\t\t\tvar address = storeinfo.name;\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t\t})\r\n\t\t},\r\n\t\topenMendian: function(e) {\r\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\tvar frightinfo = this.freightList[freightkey]\r\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\tapp.goto('/shopPackage/shop/mendian?id=' + storeinfo.id);\r\n\t\t},\r\n\t\teditorChooseImage: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t})\r\n\t\t},\r\n\t\teditorBindPickerChange:function(e){\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\teditorFormdata[idx] = val;\r\n\t\t\tconsole.log(editorFormdata)\r\n\t\t\tthis.editorFormdata = editorFormdata\r\n\t\t\tthis.test = Math.random();\r\n\t\t},\r\n\t\tdoStoreShowAll:function(){\r\n\t\t\tthis.storeshowall = true;\r\n\t\t},\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\r\n.address-add .f1{margin-right:20rpx}\r\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\r\n.address-add .f2{ color: #666; }\r\n.address-add .f3{ width: 26rpx; height: 26rpx;}\r\n\r\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\r\n.linkitem .f1{width:160rpx;color:#111111}\r\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\r\n\r\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\r\n\r\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\r\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\r\n\r\n.bcontent{width:100%;padding:0 20rpx}\r\n\r\n.product{width:100%;border-bottom:1px solid #f4f4f4} \r\n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\r\n.product .item:last-child{border:none}\r\n.product .info{padding-left:20rpx;}\r\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .info .f2{color: #999999; font-size:24rpx}\r\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\r\n.product .img{ width:140rpx;height:140rpx}\r\n.collage_icon{ color:#fe7203;border:1px solid #feccaa;display:flex;align-items:center;font-size:20rpx;padding:0 6rpx;margin-left:6rpx}\r\n\r\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\r\n.freight .f1{color:#333;margin-bottom:10rpx}\r\n.freight .f2{color: #111111;text-align:right;flex:1}\r\n.freight .f3{width: 24rpx;height:28rpx;}\r\n.freighttips{color:red;font-size:24rpx;}\r\n\r\n.freight-ul{width:100%;display:flex;}\r\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\r\n\r\n\r\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\r\n.price .f1{color:#333}\r\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\r\n.price .f3{width: 24rpx;height:24rpx;}\r\n\r\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\r\n.scoredk .f1{color:#333333}\r\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\r\n\r\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\r\n.remark .f1{color:#333;width:200rpx}\r\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\r\n\r\n.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\r\n.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1  text{color: #e94745;font-size: 32rpx;}\r\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\r\n\r\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\r\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\r\n.storeitem .panel .f1{color:#333}\r\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\r\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\r\n.storeitem .radio-item:last-child{border:0}\r\n.storeitem .radio-item .f1{color:#666;flex:1}\r\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\r\n.storeitem .radio .radio-img{width:100%;height:100%}\r\n\r\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.pstime-item .radio .radio-img{width:100%;height:100%}\r\n\r\n.cuxiao-desc{width:100%}\r\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\r\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\r\n\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024854\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}