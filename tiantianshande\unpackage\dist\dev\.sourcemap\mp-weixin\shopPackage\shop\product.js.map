{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?df08", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?9999", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?7048", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?62e6", "uni-app:///shopPackage/shop/product.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?106b", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product.vue?3c53"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "priceIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opt", "loading", "isload", "menuindex", "textset", "computed", "hasVisibleMenuItems", "visibleMenuItemsCount", "f1ContainerWidth", "onLoad", "onShow", "uni", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "console", "imageUrl", "query", "onUnload", "clearInterval", "methods", "getSweepstakesStatus", "that", "pay_type", "app", "getSweepstakesList", "res", "length", "goEdit", "url", "savePhoto", "success", "filePath", "fail", "icon", "showLinkChange", "getdata", "id", "desc", "setTimeout", "view0", "size", "rect", "scrollOffset", "view1", "view2", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "shareScheme", "schemeDialogClose", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "changetoptab", "scroll", "sharemp", "shareapp", "itemList", "scene", "sharedata", "globalData", "sharelink", "showsubqrcode", "closesubqrcode", "addcart", "showgg1Dialog", "closegg1Dialog", "showgg2Dialog", "closegg2Dialog", "onClickGame", "keyName", "item", "path", "inputLuckyPrice", "value", "e", "getProbability", "onCloseLucky", "pickerChangePrice", "onLuckyNow", "hidePriceLink", "goToCustomLink", "currentTarget", "dataset", "addfavorite2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3gBA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqzBhxB;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,kDACA,wDACA,uDACA,oDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,2DACA,4DACA,sDACA,0DACA,0DACA,2DACA,0DACA,sDACA,4DACA,4DACA,4DACA,4DACA,sDACA,0DACA,yDACA,uDACA,wDACA,oEA+BA,sDACA,uDACA,8DACA;EAEA;EACAC;IACA;IACAC;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QACA;MACA;;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAF;MACAC;IACA;IACA;IACAE;IACAA;IACA;MACAH;MACAI;MACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;MACAC;MACA;QACAC;MACA;;MACAC;QACA;UACAA;UACA;QACA;QACAF;QACAA;MACA;IACA;IACA;AACA;AACA;IACAG;MACA;MACAH;MACAE;QAAA;UAAA;YAAA;cAAA;gBAAA;kBACAF;kBAAA,MACAI;oBAAA;oBAAA;kBAAA;kBACAF;kBAAA;gBAAA;kBAGAF;kBACA;oBACAA;sBAAA;oBAAA;oBACAA;oBACAA;oBACAA;sBACAK;oBACA;sBAAA;oBAAA;oBACAL;kBACA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CACA;QAAA;UAAA;QAAA;MAAA;IACA;IAEAM;MAEAnB;QACAoB;MACA;IACA;IAEAC;MACA;MACA;MACArB;QACAoB;QACAE;UACA;YACA;YACAtB;cACAuB;cACAD;gBACAtB;kBACAG;gBACA;cACA;cACAqB;gBACAxB;kBACAG;kBACAsB;gBACA;cACA;YACA;UACA;YACAzB;cACAG;cACAsB;YACA;UACA;QACA;QACAD;UACAxB;YACAG;YACAsB;UACA;QACA;MACA;IAEA;IAEAC;MACA;IACA;IACAC;MAEA;MACA;MACAd;MACAE;QACAa;MACA;QAEAtB;QAEAO;QACA;UACAE;UACA;QACA;QACAF;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACAb;UACAG;QACA;QAEAU;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UACAV;UACAC;UACAyB;QACA;QAEAC;UACA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;YACA5B;YACAO;UACA;UACA;UACAsB;YACAH;YAAA;YACAC;YAAA;YACAC;UACA;YACA5B;YACAO;UACA;UACA;UACAuB;YACAJ;YAAA;YACAC;YAAA;YACAC;UACA;YACA5B;YACAO;UACA;QACA;MAEA;IACA;IACAwB;MACA;MACAxB;IACA;IACAyB;MACA;MACAtC;IACA;IACAuC;MACA;MACAvC;IACA;IACAwC;MACA;QACA;QACA;UACA;QACA;QACA;QAAA,KACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA1B;QACA2B;QACAC;MACA;QACA;UACA9B;QACA;QACAE;MACA;IACA;IACA6B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAjC;MACAA;MACAE;MACAA;QACA2B;MACA;QACA3B;QACA;UACAA;QACA;UACAF;QACA;MACA;IACA;IAEAkC;MACA;MACAhC;MACAA;QACA2B;MACA;QACA3B;QACA;UACAA;QACA;UACAF;UACAA;QACA;MACA;IACA;IAEAmC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAC;MACA;MACA;MACA;MACA;MACAlD;IACA;IACAmD;MACA;MACA;MACA;MACA;QACA5C;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACAP;MACAA;MACAA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAoD;MACA3C;MACA;IACA;IACA4C;MACA;MACA9C;MACAb;QACA4D;QACAtC;UACA;YACA;YACA;cACAuC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA,wEACA,8EACAC;YACAD;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAE,kDACAD;oBACA;oBACA;sBACAC,2DACA;oBACA;oBACAF;kBACA;gBACA;cACA;YACA;YACA9D;UACA;QACA;MACA;IACA;IACAiE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA7D;MACA;IACA;IACA8D;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA,IACAC,UAEAC,KAFAD;QACAE,OACAD,KADAC;MAEA;MACA;QACA;UACA;UACA;UACA;QACA;UACA5D;UACA;MAAA;IAEA;IACA;AACA;AACA;IACA6D;MACA,IACAC,QACAC,SADAD;MAEA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAnE,kCACA,6FACA;IAEA;IACAoE;MACApE;IACA;IACA;IACAqE;MACA;QACA;UAAAC;YAAAC;cAAAlE;YAAA;UAAA;QAAA;MACA;QACA;UAAAiE;YAAAC;cAAAlE;YAAA;UAAA;QAAA;MACA;IACA;IACA;IACAmE;MACA;MACA;QACA;QACAxE;UAAA2B;UAAAC;QAAA;UACA;YACA9B;UACA;UACAE;QACA;MACA;QACA;QACA;UACA;YAAAK;UAAA;YAAAA;UAAA;QACA;QACA;UACA;YAAAA;UAAA;YAAAA;UAAA;QACA;QACA;UACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAL;QACA;UACA;YACAA;YACA;UACA;UACAA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACn+CA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=6bf535ba&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/product.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=template&id=6bf535ba&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var m2 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.tjdatalist.length\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    g1 > 0 &&\n    _vm.toptabbar_index == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g2 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g3 = _vm.isload\n    ? _vm.showtoptabbar == 1 && _vm.couponlist.length > 0\n    : null\n  var l0 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m10 = _vm.t(\"color1rgb\")\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 = _vm.isload && _vm.product.hide_price == 1 ? _vm.t(\"color1\") : null\n  var m13 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.is_newcustom == 1 &&\n    _vm.product.is_member_yh == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    !(_vm.product.is_newcustom == 1 && _vm.product.is_member_yh == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    !(_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.xunjia_text\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m18 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m19 =\n    _vm.isload && _vm.product.buyselect_commission > 0 ? _vm.t(\"佣金\") : null\n  var m20 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m21 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m22 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m23 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m24 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m25 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m26 =\n    _vm.isload && _vm.product.score_deduction_message ? _vm.t(\"积分\") : null\n  var g4 = _vm.isload\n    ? _vm.cuxiaolist.length > 0 ||\n      _vm.couponlist.length > 0 ||\n      _vm.fuwulist.length > 0 ||\n      _vm.product.discount_tips != \"\"\n    : null\n  var g5 = _vm.isload && g4 ? _vm.fuwulist.length : null\n  var g6 = _vm.isload && g4 ? _vm.cuxiaolist.length : null\n  var l1 =\n    _vm.isload && g4 && g6 > 0\n      ? _vm.__map(_vm.cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m27 = _vm.t(\"color1rgb\")\n          var m28 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m27: m27,\n            m28: m28,\n          }\n        })\n      : null\n  var g7 =\n    _vm.isload && g4\n      ? _vm.couponlist.length > 0 && _vm.showtoptabbar == 0\n      : null\n  var l2 =\n    _vm.isload && g4 && g7\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m29 = _vm.t(\"color1rgb\")\n          var m30 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m29: m29,\n            m30: m30,\n          }\n        })\n      : null\n  var m31 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g8 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var g9 = _vm.isload ? _vm.notes && _vm.notes.length > 0 : null\n  var l3 =\n    _vm.isload && g9\n      ? _vm.__map(_vm.notes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g10 = index % 3 === 0 ? item.pics && item.pics.length > 0 : null\n          return {\n            $orig: $orig,\n            g10: g10,\n          }\n        })\n      : null\n  var l4 =\n    _vm.isload && g9\n      ? _vm.__map(_vm.notes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g11 = index % 3 === 1 ? item.pics && item.pics.length > 0 : null\n          return {\n            $orig: $orig,\n            g11: g11,\n          }\n        })\n      : null\n  var l5 =\n    _vm.isload && g9\n      ? _vm.__map(_vm.notes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g12 = index % 3 === 2 ? item.pics && item.pics.length > 0 : null\n          return {\n            $orig: $orig,\n            g12: g12,\n          }\n        })\n      : null\n  var m32 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m33 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m34 = _vm.isload ? _vm.isEmpty(_vm.product.paramdata) : null\n  var g13 = _vm.isload ? _vm.tjdatalist.length : null\n  var l6 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.visibleMenuItemsCount > 0 &&\n    _vm.shopdetail_menudataList\n      ? _vm.__map(_vm.shopdetail_menudataList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m35 =\n            item.isShow == 1 && item.menuType == 2 && _vm.cartnum > 0\n              ? _vm.t(\"color1rgb\")\n              : null\n          return {\n            $orig: $orig,\n            m35: m35,\n          }\n        })\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.visibleMenuItemsCount > 0 &&\n    !_vm.shopdetail_menudataList &&\n    _vm.cartnum > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m37 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m38 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1 &&\n    _vm.product.show_buybtn !== 0\n      ? _vm.t(\"color1\")\n      : null\n  var m39 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    _vm.product.hide_price == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m40 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.hide_price == 1) &&\n    _vm.product.price_type == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m41 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.hide_price == 1) &&\n    !(_vm.product.price_type == 1) &&\n    _vm.product.show_addcartbtn !== 0 &&\n    _vm.product.freighttype != 3 &&\n    _vm.product.freighttype != 4\n      ? _vm.t(\"color2\")\n      : null\n  var m42 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.hide_price == 1) &&\n    !(_vm.product.price_type == 1) &&\n    _vm.product.show_buybtn !== 0\n      ? _vm.t(\"color1\")\n      : null\n  var m43 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m44 =\n    _vm.isload && _vm.sharetypevisible && !(m43 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m45 =\n    _vm.isload && _vm.sharetypevisible && !(m43 == \"app\") && !(m44 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m46 =\n    _vm.isload && _vm.sharetypevisible\n      ? _vm.getplatform() == \"wx\" && _vm.xcx_scheme\n      : null\n  var m47 = _vm.isload && _vm.showposter ? _vm.t(\"color1\") : null\n  var m48 = _vm.isload && _vm.showposter ? _vm.t(\"color1rgb\") : null\n  var m49 = _vm.isload && _vm.showposter ? _vm.getplatform() : null\n  var m50 =\n    _vm.isload && _vm.showposter && m49 == \"app\" ? _vm.t(\"color1\") : null\n  var m51 =\n    _vm.isload && _vm.showposter && m49 == \"app\" ? _vm.t(\"color1rgb\") : null\n  var m52 =\n    _vm.isload && _vm.showposter && !(m49 == \"app\") ? _vm.getplatform() : null\n  var m53 =\n    _vm.isload && _vm.showposter && !(m49 == \"app\") && m52 == \"mp\"\n      ? _vm.t(\"color1\")\n      : null\n  var m54 =\n    _vm.isload && _vm.showposter && !(m49 == \"app\") && m52 == \"mp\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m55 =\n    _vm.isload && _vm.showposter && !(m49 == \"app\") && !(m52 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m56 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m49 == \"app\") &&\n    !(m52 == \"mp\") &&\n    m55 == \"h5\"\n      ? _vm.t(\"color1\")\n      : null\n  var m57 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m49 == \"app\") &&\n    !(m52 == \"mp\") &&\n    m55 == \"h5\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m58 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m49 == \"app\") &&\n    !(m52 == \"mp\") &&\n    !(m55 == \"h5\")\n      ? _vm.t(\"color1\")\n      : null\n  var m59 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m49 == \"app\") &&\n    !(m52 == \"mp\") &&\n    !(m55 == \"h5\")\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m60 = _vm.isload && _vm.showScheme ? _vm.t(\"color1\") : null\n  var m61 = _vm.isload && _vm.showScheme ? _vm.t(\"color1rgb\") : null\n  var m62 =\n    _vm.isload && _vm.showLinkStatus && _vm.business.tel\n      ? _vm.t(\"color1\")\n      : null\n  var m63 = _vm.getProbability()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.product.addcart_link_url\n        ? _vm.goToCustomLink(\"addcart\")\n        : _vm.buydialogChange(null, 1)\n    }\n    _vm.e1 = function ($event) {\n      _vm.product.buybtn_link_url\n        ? _vm.goToCustomLink(\"buybtn\")\n        : _vm.buydialogChange(null, 2)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g1: g1,\n        m8: m8,\n        m9: m9,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        l1: l1,\n        g7: g7,\n        l2: l2,\n        m31: m31,\n        g8: g8,\n        g9: g9,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        g13: g13,\n        l6: l6,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        m57: m57,\n        m58: m58,\n        m59: m59,\n        m60: m60,\n        m61: m61,\n        m62: m62,\n        m63: m63,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<block v-if=\"isload\">\n\t\t\t<block v-if=\"sysset.showgzts\">\n\t\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\n\t\t\t\t<view class=\"follow_topbar\">\n\t\t\t\t\t<view class=\"headimg\">\n\t\t\t\t\t\t<image :src=\"sysset.logo\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info\">\n\t\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\n\t\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\n\t\t\t\t</view>\n\t\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\n\t\t\t\t\t<view class=\"qrcodebox\">\n\t\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\" />\n\t\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\n\t\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\t</block>\n\n\t\t\t<view\n\t\t\t\tstyle=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\"\n\t\t\t\tv-if=\"bboglist.length>0\">\n\t\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\"\n\t\t\t\t\t:vertical=\"true\">\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\"\n\t\t\t\t\t\t:data-url=\"'/shopPackage/shop/product?id=' + item.proid\" class=\"flex-y-center\">\n\t\t\t\t\t\t<image :src=\"item.headimg\"\n\t\t\t\t\t\t\tstyle=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\" />\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tstyle=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">\n\t\t\t\t\t\t\t{{item.nickname}} {{item.showtime}}购买了该商品\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</swiper-item>\n\t\t\t\t</swiper>\n\t\t\t</view>\n\n\t\t\t<view class=\"toptabbar_tab\" v-if=\"showtoptabbar==1 && toptabbar_show==1\">\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==0?'on':''\"\n\t\t\t\t\t:style=\"{color:toptabbar_index==0?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"0\">商品<view\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==1?'on':''\"\n\t\t\t\t\t:style=\"{color:toptabbar_index==1?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"1\">评价<view\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==2?'on':''\"\n\t\t\t\t\t:style=\"{color:toptabbar_index==2?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"2\">详情<view\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item\" v-if=\"tjdatalist.length > 0\" :class=\"toptabbar_index==3?'on':''\"\n\t\t\t\t\t:style=\"{color:toptabbar_index==3?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"3\">推荐<view\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<scroll-view @scroll=\"scroll\" :scrollIntoView=\"scrollToViewId\" :scrollTop=\"scrollTop\" :scroll-y=\"true\"\n\t\t\t\tstyle=\"height:100%;overflow:scroll\">\n\t\t\t\t<view id=\"scroll_view_tab0\">\n\t\t\t\t\t<view class=\"game\" v-if=\"isGameOpen\">\n\t\t\t\t\t\t<view class=\"game-grid\" v-for=\"item in gameList\" :key=\"item.keyName\" @click=\"onClickGame(item)\">\n\t\t\t\t\t\t\t<image class=\"game-img\" :src=\"item.imgUrl\" mode=\"widthFix\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"swiper-container\" v-if=\"isplay==0\">\n\n\t\t\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\"\n\t\t\t\t\t\t\t@change=\"swiperChange\">\n\t\t\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\n\t\t\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"swiper-item-view\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item\" mode=\"widthFix\" @tap=\"previewImage\"\n\t\t\t\t\t\t\t\t\t\t\t:data-url=\"item\" />\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</swiper>\n\t\t\t\t\t\t<view class=\"imageCount\" v-if=\"product.diypics\" @tap=\"goto\"\n\t\t\t\t\t\t\t:data-url=\"'/pagesExt/shop/diylight?id='+product.id\" style=\"bottom: 92rpx; width: 140rpx;\">\n\t\t\t\t\t\t\t自助试灯</view>\n\t\t\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\n\t\t\t\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\">\n\t\t\t\t\t\t\t<image src=\"/static/img/video.png\" />\n\t\t\t\t\t\t\t<view class=\"txt\">{{product.video_duration}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"videobox\" v-if=\"isplay==1\">\n\t\t\t\t\t\t<video autoplay=\"true\" class=\"video\" id=\"video\" :src=\"product.video\"></video>\n\t\t\t\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">退出播放</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"showtoptabbar==1 && couponlist.length>0\"\n\t\t\t\t\t\tstyle=\"background:#fff;padding:0 16rpx\">\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\"\n\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\"\n\t\t\t\t\t\t\t\t\tstyle=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view style=\"background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0\"\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao1\">\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao1\" style=\"width:100%;height:auto\" mode=\"widthFix\"\n\t\t\t\t\t\t\tv-if=\"shopset.detail_guangao1\" @tap=\"showgg1Dialog\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-popup id=\"gg1Dialog\" ref=\"gg1Dialog\" type=\"dialog\"\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao1 && shopset.detail_guangao1_t\">\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao1_t\" @tap=\"previewImage\"\n\t\t\t\t\t\t\t:data-url=\"shopset.detail_guangao1_t\" class=\"img\" mode=\"widthFix\"\n\t\t\t\t\t\t\tstyle=\"width:600rpx;height:auto;border-radius:10rpx;\" />\n\t\t\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg1Dialog\">\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-popup>\n\n\t\t\t\t\t<view class=\"header\">\n\t\t\t\t\t\t<block v-if=\"product.hide_price == 1\">\n\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">{{product.hide_price_detail_text}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else-if=\"product.price_type != 1 || product.min_price > 0\">\n\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"product.is_newcustom == 1 && product.is_member_yh == 0\" class=\"f1\"\n\t\t\t\t\t\t\t\t\t\t:style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"product.is_newcustom == 1\"\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"font-size:36rpx\">￥</text>{{product.yh_price}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view v-else class=\"f1\" :style=\"{color:t('color1')}\">\n\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{product.min_price}}<text\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">\n\t\t\t\t\t\t\t\t\t\t￥{{product.market_price}}<text\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"product.max_price!=product.min_price\">起</text></view>\n\t\t\t\t\t\t\t\t\t<view v-if=\"product.huang_dx.types\" class=\"huang_bz\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"huang_i\"></view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"huang_nums\">可用红包：{{product.huang_dx.nums}}</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"product.yuanbao\"\n\t\t\t\t\t\t\t\tstyle=\"margin: 0;font-size: 26rpx;margin-bottom: 10rpx;\">\n\t\t\t\t\t\t\t\t<view class=\"f2\">元宝价：{{product.yuanbao}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view v-if=\"product.xunjia_text\" class=\"price_share\">\n\t\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">询价</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"price_share\">\n\t\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\n\t\t\t\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\n\t\t\t\t\t\t<view class=\"sales_stock\"\n\t\t\t\t\t\t\tv-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">\n\t\t\t\t\t\t\t<!-- <view class=\"f1\">奖励消费值：{{product.xiaofeizhi2}} </view> -->\n\t\t\t\t\t\t\t<view class=\"f1\">奖励创业值：{{product.chuangyezhi}} </view>\n\t\t\t\t\t\t\t<view class=\"f2\">推广佣金：{{product.commission}}元</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"shopset.hide_sales != 1 || shopset.hide_stock != 1\">\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"shopset.hide_sales != 1\">销量：{{product.sales}} </view>\n\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"shopset.hide_stock != 1\">库存：{{product.stock}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"\n\t\t\t\t\t\t\tv-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">\n\t\t\t\t\t\t\t分享好友购买预计可得{{t('佣金')}}：<text\n\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"margin:20rpx 0;color:#333;font-size:22rpx\" v-if=\"product.balance_price > 0\">\n\t\t\t\t\t\t\t首付款金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\n\t\t\t\t\t\t<view style=\"margin:20rpx 0;color:#666;font-size:22rpx\" v-if=\"product.buyselect_commission > 0\">\n\t\t\t\t\t\t\t下单被选奖励预计可得{{t('佣金')}}：<text\n\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px\">{{product.buyselect_commission}}</text>元</view>\n\n\t\t\t\t\t\t<view class=\"upsavemoney\"\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\"\n\t\t\t\t\t\t\tv-if=\"product.upsavemoney > 0\">\n\t\t\t\t\t\t\t<view class=\"flex1\">升级到 {{product.nextlevelname}} 预计可节省<text\n\t\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney}}</text>元\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tstyle=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\"\n\t\t\t\t\t\t\t\t@tap=\"goto\" data-url=\"/pagesExa/my/levelup\">立即升级\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrowright2.png\" style=\"width:30rpx;height:30rpx\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\n\t\t\t\t\t\t<view class=\"f0\">规格</view>\n\t\t\t\t\t\t<view class=\"f1 flex1\">\n\t\t\t\t\t\t\t<block v-if=\"product.price_type == 1\">查看规格</block>\n\t\t\t\t\t\t\t<block v-else>请选择商品规格及数量</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image class=\"f2\" src=\"/static/img/arrowright.png\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.xiaofeizhi2 > 0\">\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('消费值')}}</view>\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('消费值')}}{{product.xiaofeizhi2}} 个</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.chuangyezhi > 0\">\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('创业值')}}</view>\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx;\">购买可得{{t('创业值')}}{{product.chuangyezhi}} 个</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.givescore > 0\">\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('积分')}}</view>\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('积分')}}{{product.givescore}}个</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.score_deduction_message\">\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\n\t\t\t\t\t\t\t<view class=\"f0\">抵扣{{ t('积分') }}</view>\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">{{ product.score_deduction_message }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\n\t\t\t\t\t<view class=\"cuxiaodiv\"\n\t\t\t\t\t\tv-if=\"cuxiaolist.length>0 || couponlist.length>0 || fuwulist.length>0 || product.discount_tips!=''\">\n\t\t\t\t\t\t<view class=\"fuwupoint cuxiaoitem\" v-if=\"fuwulist.length>0\">\n\t\t\t\t\t\t\t<view class=\"f0\">服务</view>\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"cuxiaolist.length>0\">\n\t\t\t\t\t\t\t<view class=\"f0\">促销</view>\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"t\"\n\t\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text\n\t\t\t\t\t\t\t\t\t\tclass=\"t0\">{{item.tip}}</text><text class=\"t1\">{{item.name}}</text></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"product.discount_tips!=''\">\n\t\t\t\t\t\t\t<view class=\"f0\">折扣</view>\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"padding-left:10rpx\">{{product.discount_tips}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExa/my/levelinfo\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"couponlist.length>0 && showtoptabbar==0\">\n\t\t\t\t\t\t\t<view class=\"f0\">优惠</view>\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\"\n\t\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text\n\t\t\t\t\t\t\t\t\t\tclass=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\n\t\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\n\t\t\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\n\t\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\t\t\t\**********=\"hidefuwudetail\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"showcuxiaodialog\" class=\"popup__container\">\n\t\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidecuxiaodetail\"></view>\n\t\t\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\n\t\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\t\t\t\**********=\"hidecuxiaodetail\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"service-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"suffix\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#333;margin-left:20rpx\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<couponlist :couponlist=\"couponlist\" @getcoupon=\"getcoupon\"></couponlist>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view style=\"width:100%;height:auto;padding:20rpx 0 0\" v-if=\"shopset.detail_guangao2\">\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao2\" style=\"width:100%;height:auto\" mode=\"widthFix\"\n\t\t\t\t\t\t\tv-if=\"shopset.detail_guangao2\" @tap=\"showgg2Dialog\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-popup id=\"gg2Dialog\" ref=\"gg2Dialog\" type=\"dialog\"\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao2 && shopset.detail_guangao2_t\">\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao2_t\" @tap=\"previewImage\"\n\t\t\t\t\t\t\t:data-url=\"shopset.detail_guangao2_t\" class=\"img\" mode=\"widthFix\"\n\t\t\t\t\t\t\tstyle=\"width:600rpx;height:auto;border-radius:10rpx;\" />\n\t\t\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg2Dialog\">\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</uni-popup>\n\n\t\t\t\t</view>\n\n\t\t\t\t<view id=\"scroll_view_tab1\">\n\n\t\t\t\t\t<view class=\"commentbox\" v-if=\"shopset.comment==1 && commentcount > 0\">\n\t\t\t\t\t\t<view class=\"title\">\n\t\t\t\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评率 <text\n\t\t\t\t\t\t\t\t\t:style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text>\n\t\t\t\t\t\t\t\t<image style=\"width:32rpx;height:32rpx;\" src=\"/static/img/arrowright.png\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"comment\">\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\" />\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"\n\t\t\t\t\t\t\t\t\t\t\t:src=\"'/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'\" />\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:data-urls=\"commentlist[0].content_pic\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 笔记列表 -->\n\t\t\t\t<view class=\"notes-section\" v-if=\"notes && notes.length > 0\">\n\t\t\t\t\t<view class=\"notes-title\">\n\t\t\t\t\t\t<view class=\"f1\">笔记({{notes_total}})</view>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'notelist?proid=' + product.id\">\n\t\t\t\t\t\t\t查看全部\n\t\t\t\t\t\t\t<image style=\"width:32rpx;height:32rpx;\" src=\"/static/img/arrowright.png\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"notes-list\">\n\t\t\t\t\t\t<view class=\"waterfall-wrapper\">\n\t\t\t\t\t\t\t<!-- 第一列 -->\n\t\t\t\t\t\t\t<view class=\"waterfall-column\">\n\t\t\t\t\t\t\t\t<view class=\"note-item\" v-for=\"(item, index) in notes\" :key=\"index\" \n\t\t\t\t\t\t\t\t\tv-if=\"index % 3 === 0\" \n\t\t\t\t\t\t\t\t\t@tap=\"goto\" \n\t\t\t\t\t\t\t\t\t:data-url=\"'/daihuobiji/detail/index?id=' + item.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"note-images\" v-if=\"item.pics && item.pics.length > 0\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pics[0]\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-content\">{{item.title}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-user\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.headimg\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{item.nickname}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"note-likes\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/like.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t\t\t<text>{{item.likes}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 第二列 -->\n\t\t\t\t\t\t\t<view class=\"waterfall-column\">\n\t\t\t\t\t\t\t\t<view class=\"note-item\" v-for=\"(item, index) in notes\" :key=\"index\"\n\t\t\t\t\t\t\t\t\tv-if=\"index % 3 === 1\"\n\t\t\t\t\t\t\t\t\t@tap=\"goto\"\n\t\t\t\t\t\t\t\t\t:data-url=\"'/daihuobiji/detail/index?id=' + item.id\">\n\t\t\t\t\t\t\t\t\t<!-- 与第一列相同的内部结构 -->\n\t\t\t\t\t\t\t\t\t<view class=\"note-images\" v-if=\"item.pics && item.pics.length > 0\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pics[0]\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-content\">{{item.title}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-user\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.headimg\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{item.nickname}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"note-likes\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/like.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t\t\t<text>{{item.likes}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 第三列 -->\n\t\t\t\t\t\t\t<view class=\"waterfall-column\">\n\t\t\t\t\t\t\t\t<view class=\"note-item\" v-for=\"(item, index) in notes\" :key=\"index\"\n\t\t\t\t\t\t\t\t\tv-if=\"index % 3 === 2\"\n\t\t\t\t\t\t\t\t\t@tap=\"goto\"\n\t\t\t\t\t\t\t\t\t:data-url=\"'/daihuobiji/detail/index?id=' + item.id\">\n\t\t\t\t\t\t\t\t\t<!-- 与第一列相同的内部结构 -->\n\t\t\t\t\t\t\t\t\t<view class=\"note-images\" v-if=\"item.pics && item.pics.length > 0\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pics[0]\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-content\">{{item.title}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"note-user\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.headimg\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{item.nickname}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"note-likes\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/img/like.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t\t\t\t\t\t<text>{{item.likes}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view id=\"scroll_view_tab2\">\n\n\t\t\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\n\t\t\t\t\t\t<image :src=\"business.logo\" class=\"p1\" />\n\t\t\t\t\t\t<view class=\"p2 flex1\">\n\t\t\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\n\t\t\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"p4\"\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\n\t\t\t\t\t\t\t@tap=\"goto\"\n\t\t\t\t\t\t\t:data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\"\n\t\t\t\t\t\t\tdata-opentype=\"reLaunch\">进入首页</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-if=\"!isEmpty(product.paramdata)\">\n\t\t\t\t\t\t<view class=\"detail_title\">\n\t\t\t\t\t\t\t<view class=\"t1\"></view>\n\t\t\t\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t\t\t\t<view class=\"t0\">商品参数</view>\n\t\t\t\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t\t\t\t<view class=\"t1\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"background:#fff;padding:20rpx 40rpx;\" class=\"paraminfo\">\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in product.paramdata\" class=\"paramitem\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{index}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\">{{item}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\n\t\t\t\t\t<view class=\"detail_title\">\n\t\t\t\t\t\t<view class=\"t1\"></view>\n\t\t\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t\t\t<view class=\"t0\">商品描述</view>\n\t\t\t\t\t\t<view class=\"t2\"></view>\n\t\t\t\t\t\t<view class=\"t1\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\n\t\t\t\t\t\t<image v-if=\"bottomImg\" class=\"bottomimg\" :src=\"bottomImg\" mode=\"widthFix\" />\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\n\t\t\t\t<view id=\"scroll_view_tab3\">\n\n\t\t\t\t\t<view v-if=\"tjdatalist.length > 0\">\n\t\t\t\t\t\t<view class=\"xihuan\">\n\t\t\t\t\t\t\t<view class=\"xihuan-line\"></view>\n\t\t\t\t\t\t\t<view class=\"xihuan-text\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/xihuan.png\" class=\"img\" />\n\t\t\t\t\t\t\t\t<text class=\"txt\">为您推荐</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"xihuan-line\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"prolist\">\n\t\t\t\t\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\"\n\t\t\t\t\t\t\t\t:menuindex=\"menuindex\"></dp-product-item>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\n\t\t\t\t<view style=\"width:100%;height:140rpx;\"></view>\n\n\t\t\t</scroll-view>\n\n\t\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\"\n\t\t\t\tv-if=\"product.status==1&&!showcuxiaodialog\">\n\t\t\t\t<view class=\"f1\" :style=\"{width: f1ContainerWidth}\" v-if=\"visibleMenuItemsCount > 0\">\n\t\t\t\t\t<!-- 自定义菜单 -->\n\t\t\t\t\t<template v-if=\"shopdetail_menudataList\">\n\t\t\t\t\t\t<block v-for=\"(item,index) in shopdetail_menudataList\" :key=\"index\" v-if=\"item.isShow == 1\">\n\t\t\t\t\t\t\t<block v-if='item.menuType == 1'>\n\t\t\t\t\t\t\t\t<block v-if=\"item.useSystem == 1\">\n\t\t\t\t\t\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t<button class=\"item\" open-type=\"contact\"  v-if=\"item.pagePath == 'contact::'\" show-message-card=\"true\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\" v-else>\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/kefu.png'\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'客服'}}</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if='item.menuType == 2'>\n\t\t\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\"\n\t\t\t\t\t\t\t\t\t\t:src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/gwc.png'\" />\n\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text ? item.text:'购物车'}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if='item.menuType == 3'>\n\t\t\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.iconPath ? item.iconPath:pre_url+'/static/img/shoucang.png'\"\tv-if='!isfavorite' />\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.selectedIconPath ? item.selectedIconPath:pre_url+'/static/img/shoucang.png'\"\tv-else />\n\t\t\t\t\t\t\t\t\t<view class=\"t1\" v-if='item.selectedtext && item.text'>{{isfavorite ? item.selectedtext:item.text}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"t1\" v-else>{{isfavorite?'已收藏':'收藏'}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-if='item.menuType == 4'>\n\t\t\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite2(item)\">\n\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item.iconPath\" />\n\t\t\t\t\t\t\t\t\t<view class=\"t1\">{{item.text}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</template>\n\t\t\t\t\t<!-- 为空数据默认展示 -->\n\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\n\t\t\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\n\t\t\t\t\t\t\t<view class=\"t1\">客服</view>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"goto\" data-url=\"/pages/shop/cart\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/gwc.png'\"/>\n\t\t\t\t\t\t\t<view class=\"t1\">购物车</view>\n\t\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\n\t\t\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</template>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"op2\" v-if=\"showjiesheng==1\">\n\t\t\t\t\t<view class=\"tocart2\" :style=\"{background:t('color2')}\" @tap=\"shareClick\"><text>分享赚钱</text><text\n\t\t\t\t\t\t\tstyle=\"font-size:24rpx\">赚￥{{product.commission}}</text></view>\n\t\t\t\t\t<view class=\"tobuy2\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\" v-if=\"product.show_buybtn !== 0\">\n\t\t\t\t\t\t<text>立即购买</text><text style=\"font-size:24rpx\"\n\t\t\t\t\t\t\tv-if=\"product.jiesheng_money > 0\">省￥{{product.jiesheng_money}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"op\" v-else>\n\t\t\t\t\t<block v-if=\"product.hide_price == 1\">\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\n\t\t\t\t\t\t\t@tap=\"hidePriceLink\" data-btntype=\"2\">\n\t\t\t\t\t\t\t{{product.hide_price_detail_text ? product.hide_price_detail_text : '咨询'}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else-if=\"product.price_type == 1\">\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\n\t\t\t\t\t\t\t@tap=\"showLinkChange\" data-btntype=\"2\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\"\n\t\t\t\t\t\t\t@tap=\"product.addcart_link_url ? goToCustomLink('addcart') : buydialogChange(null, 1)\"\n\t\t\t\t\t\t\tv-if=\"(product.show_addcartbtn !== 0) && product.freighttype!=3 && product.freighttype!=4\">\n\t\t\t\t\t\t\t{{product.addcart_name || '加入购物车'}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\n\t\t\t\t\t\t\t@tap=\"product.buybtn_link_url ? goToCustomLink('buybtn') : buydialogChange(null, 2)\"\n\t\t\t\t\t\t\tv-if=\"product.show_buybtn !== 0\">\n\t\t\t\t\t\t\t{{product.buybtn_name || '立即购买'}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\" @buydialogChange=\"buydialogChange\"\n\t\t\t\t@showLinkChange=\"showLinkChange\" :menuindex=\"menuindex\" @addcart=\"addcart\"></buydialog>\n\n\t\t\t<view class=\"scrolltop\" v-show=\"scrolltopshow\" @tap=\"changetoptab\" data-index=\"0\">\n\t\t\t\t<image class=\"image\" src=\"/static/img/gotop.png\" />\n\t\t\t</view>\n\n\t\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\n\t\t\t\t\t<!-- <view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t\t</view> -->\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<view class=\"sharetypecontent\">\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/sharepic.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareScheme\" v-if=\"getplatform() == 'wx' && xcx_scheme\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">小程序链接</text>\n\t\t\t\t\t\t\t</view>\n\n\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goEdit\">\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/edit2.png\" />\n\t\t\t\t\t\t\t\t<text class=\"t1\">发布笔记</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"posterDialog\" v-if=\"showposter\">\n\t\t\t\t<view class=\"main\">\n\t\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\">\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/close.png\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 0 10px 10px 10px;\">\n\n\n\t\t\t\t\t\t<button class=\"pp4\" @tap=\"savePhoto\"\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保存</button>\n\t\t\t \n\t\t\t\t\t\t <button class=\"pp4\" v-if=\"getplatform() == 'app'\" @tap=\"shareapp\"\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\n\t\t\t\t\t\t \n\t\t\t\t\t\t <button class=\"pp4\" v-else-if=\"getplatform() == 'mp'\"  @tap=\"sharemp\"\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\n\t\t\t\t\t\t \n\t\t\t\t\t\t <button class=\"pp4\" v-else-if=\"getplatform() == 'h5'\"  @tap=\"sharemp\"\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\n\t\t\t\t\t\t \n\t\t\t\t\t\t <button class=\"pp4\" open-type=\"share\" v-else\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\n\t\t\t\t\t\t \n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\t\t\t</view>\n\n\n\t\t\t<view class=\"posterDialog schemeDialog\" v-if=\"showScheme\">\n\t\t\t\t<view class=\"main\">\n\t\t\t\t\t<view class=\"schemecon\">\n\t\t\t\t\t\t<view style=\"line-height: 60rpx;\">{{product.name}} </view>\n\t\t\t\t\t\t<view>购买链接：<text style=\"color: #00A0E9;\">{{schemeurl}}</text></view>\n\t\t\t\t\t\t<view class=\"copybtn\"\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\n\t\t\t\t\t\t\**********=\"copy\" :data-text=\"product.name+'购买链接：'+schemeurl\"> 一键复制 </view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\n\t\t\t<view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\n\t\t\t\t<view class=\"main\">\n\t\t\t\t\t<view class=\"close\" @tap=\"showLinkChange\">\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/close.png\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<view class=\"title\">{{sysset.name}}</view>\n\t\t\t\t\t\t<view class=\"row\" v-if=\"product.bid > 0\">\n\t\t\t\t\t\t\t<view class=\"f1\">店铺名称</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+product.bid\">\n\t\t\t\t\t\t\t\t{{business.name}}\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"image\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"row\" v-if=\"business.tel\">\n\t\t\t\t\t\t\t<view class=\"f1\">联系电话</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'tel::'+business.tel\" :style=\"{color:t('color1')}\">\n\t\t\t\t\t\t\t\t{{business.tel}}\n\t\t\t\t\t\t\t\t<image src=\"/static/img/copy.png\" class=\"copyicon\" @tap.stop=\"copy\"\n\t\t\t\t\t\t\t\t\t:data-text=\"business.tel\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</block>\n\t\t<loading v-if=\"loading\"></loading>\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t\t<!-- 幸运夺宝 -->\n\t\t<uni-popup ref=\"popupLucky\">\n\t\t\t<view class=\"model show\">\n\t\t\t\t<view class=\"luckBao-header\">\n\t\t\t\t\t<image @tap=\"onCloseLucky\" class=\"luckBao-img-close\" src=\"@/game/static/game/lucky/icon_close.png\"\n\t\t\t\t\t\tmode=\"widthFix\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"luckBao\">\n\t\t\t\t\t<view class=\"luckBao-text\">中奖概率：{{ getProbability() }}%</view>\n\t\t\t\t\t<view class=\"luckBao-input-wrap\">\n\t\t\t\t\t\t<text>幸运价</text>\n\t\t\t\t\t\t<input class=\"luckBao-input-class\" :value=\"luckyPrice\" @input=\"inputLuckyPrice\"></input>\n\t\t\t\t\t\t<picker :value=\"priceIndex\" :range=\"luckyPriceArray\" @change=\"pickerChangePrice\">\n\t\t\t\t\t\t\t<image class=\"img-cursor-2\" src=\"@/game/static/game/lucky/icon_cursor.png\"\n\t\t\t\t\t\t\t\tmode=\"widthFix\" />\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"luckBao-tips-wrap1\">点击输入框手动填写价格</view>\n\t\t\t\t\t<view class=\"luckBao-tips-wrap2\">\n\t\t\t\t\t\t<text>或者点击</text>\n\t\t\t\t\t\t<image class=\"img-cursor-1\" src=\"@/game/static/game/lucky/icon_cursor.png\" mode=\"widthFix\" />\n\t\t\t\t\t\t<text>选择价格</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"lucky-btn-now\" @tap=\"onLuckyNow\">立即夺宝</view>\n\t\t\t\t<view class=\"lucky-btn-origin-price\" @tap=\"buydialogChange\">原价购买</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\n\tvar app = getApp();\n\tvar interval = null;\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpriceIndex: 0,\n\t\t\t\tluckyPriceArray: [],\n\t\t\t\topt: {},\n\t\t\t\tloading: false,\n\t\t\t\tisload: false,\n\t\t\t\tmenuindex: -1,\n\t\t\t\ttextset: {},\n\t\t\t\tisload: false,\n\t\t\t\tbuydialogShow: false,\n\t\t\t\tbtntype: 1,\n\t\t\t\tisfavorite: false,\n\t\t\t\tcurrent: 0,\n\t\t\t\tisplay: 0,\n\t\t\t\tshowcuxiaodialog: false,\n\t\t\t\tshowfuwudialog: false,\n\t\t\t\tbusiness: \"\",\n\t\t\t\tproduct: [],\n\t\t\t\tcartnum: \"\",\n\t\t\t\tcommentlist: \"\",\n\t\t\t\tcommentcount: \"\",\n\t\t\t\tcuxiaolist: \"\",\n\t\t\t\tcouponlist: \"\",\n\t\t\t\tfuwulist: [],\n\t\t\t\tnotes: [], // 笔记列表\n\t\t\t\tnotes_total: 0, // 笔记总数\n\t\t\t\tpagecontent: \"\",\n\t\t\t\tshopset: {},\n\t\t\t\tsysset: {},\n\t\t\t\ttitle: \"\",\n\t\t\t\tbboglist: \"\",\n\t\t\t\tsharepic: \"\",\n\t\t\t\tsharetypevisible: false,\n\t\t\t\tshowposter: false,\n\t\t\t\tposterpic: \"\",\n\t\t\t\tscrolltopshow: false,\n\t\t\t\tkfurl: '',\n\t\t\t\tshowLinkStatus: false,\n\t\t\t\tshowjiesheng: 0,\n\t\t\t\ttjdatalist: [],\n\t\t\t\tshowtoptabbar: 0,\n\t\t\t\ttoptabbar_show: 0,\n\t\t\t\ttoptabbar_index: 0,\n\t\t\t\tscrollToViewId: \"\",\n\t\t\t\tscrollTop: 0,\n\t\t\t\tscrolltab0Height: 0,\n\t\t\t\tscrolltab1Height: 0,\n\t\t\t\tscrolltab2Height: 0,\n\t\t\t\tscrolltab3Height: 0,\n\t\t\t\txcx_scheme: false,\n\t\t\t\tshowScheme: false,\n\t\t\t\tschemeurl: '',\n\t\t\t\tisGameOpen: false, // 游戏开启状态\n\t\t\t\tgameList: [],\n\t\t\t\t// gameList: [{\n\t\t\t\t// \t\tid: 2,\n\t\t\t\t// \t\tkey: 'happy',\n\t\t\t\t// \t\timg: '/static/game/happy.png',\n\t\t\t\t// \t\tname: '幸运大转盘',\n\t\t\t\t// \t\tamount: 0.98\n\t\t\t\t// \t},\n\t\t\t\t// \t{\n\t\t\t\t// \t\tid: 1,\n\t\t\t\t// \t\tkey: 'lucky',\n\t\t\t\t// \t\timg: '/static/game/lucky.png',\n\t\t\t\t// \t\tname: '幸运夺宝',\n\t\t\t\t// \t\tamount: 0.88\n\n\t\t\t\t// \t},\n\t\t\t\t// \t{\n\t\t\t\t// \t\tid: 3,\n\t\t\t\t// \t\tkey: 'free',\n\t\t\t\t// \t\timg: '/static/game/free.png',\n\t\t\t\t// \t\tname: '免单',\n\t\t\t\t// \t\tamount: 0.88\n\t\t\t\t// \t},\n\t\t\t\t// \t{\n\t\t\t\t// \t\tid: 4,\n\t\t\t\t// \t\tkey: 'cat',\n\t\t\t\t// \t\timg: '/static/game/cat.png',\n\t\t\t\t// \t\tname: '猫',\n\t\t\t\t// \t\tamount: 0.88\n\t\t\t\t// \t}\n\t\t\t\t// ],\n\t\t\t\tshopdetail_menudataList: [], // 自定义菜单数据列表\n\t\t\t\tbottomImg: '', // 公共底部图片\n\t\t\t\tluckyPrice: 0.00,\n\t\t\t\tcurrentGameInfo: {}\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t// 判断是否有可见的菜单项\n\t\t\thasVisibleMenuItems() {\n\t\t\t\tif (!this.shopdetail_menudataList || !Array.isArray(this.shopdetail_menudataList)) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn this.shopdetail_menudataList.some(item => item.isShow == 1);\n\t\t\t},\n\t\t\t// 计算可见菜单项的数量\n\t\t\tvisibleMenuItemsCount() {\n\t\t\t\tif (!this.shopdetail_menudataList || !Array.isArray(this.shopdetail_menudataList)) {\n\t\t\t\t\treturn 3; // 默认3个菜单项（客服、购物车、收藏）\n\t\t\t\t}\n\t\t\t\treturn this.shopdetail_menudataList.filter(item => item.isShow == 1).length;\n\t\t\t},\n\t\t\t// 动态计算f1容器的宽度\n\t\t\tf1ContainerWidth() {\n\t\t\t\tconst itemWidth = 90; // 每个菜单项宽度 100rpx (与CSS中.item的width一致)\n\t\t\t\tconst margin = 20; // 右边距 20rpx (调整以平衡左右空间)\n\t\t\t\treturn (this.visibleMenuItemsCount * itemWidth) + margin + 'rpx';\n\t\t\t}\n\t\t},\n\t\tonLoad: function(opt) {\n\t\t\tthis.opt = app.getopts(opt);\n\t\t\tthis.getdata();\n\t\t\tthis.getSweepstakesStatus()\n\t\t},\n\t\tonShow: function(e) {\n\t\t\tuni.$emit('getglassrecord');\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.getdata();\n\t\t},\n\t\tonShareAppMessage: function() {\n\t\t\treturn this._sharewx({\n\t\t\t\ttitle: this.product.sharetitle || this.product.name,\n\t\t\t\tpic: this.product.sharepic || this.product.pic\n\t\t\t});\n\t\t},\n\t\tonShareTimeline: function() {\n\t\t\tvar sharewxdata = this._sharewx({\n\t\t\t\ttitle: this.product.sharetitle || this.product.name,\n\t\t\t\tpic: this.product.sharepic || this.product.pic\n\t\t\t});\n\t\t\tvar query = (sharewxdata.path).split('?')[1];\n\t\t\tconsole.log(sharewxdata)\n\t\t\tconsole.log(query)\n\t\t\treturn {\n\t\t\t\ttitle: sharewxdata.title,\n\t\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\t\tquery: query\n\t\t\t}\n\t\t},\n\t\tonUnload: function() {\n\t\t\tclearInterval(interval);\n\t\t\tthis.$refs.popupLucky && this.$refs.popupLucky.close();\n\t\t},\n\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 获取抽奖总状态\n\t\t\t */\n\t\t\tgetSweepstakesStatus() {\n\t\t\t\tlet that = this;\n\t\t\t\tthat.loading = true;\n\t\t\t\tconst params = {\n\t\t\t\t\tpay_type: 2 //支付方式，1->线上支付，2->积分\n\t\t\t\t}\n\t\t\t\tapp.get('ApiSweepstakes/getSet', {}, function(res) {\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthat.isGameOpen = !!res.is_open\n\t\t\t\t\tthat.getSweepstakesList()\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 抽奖列表\n\t\t\t */\n\t\t\tgetSweepstakesList() {\n\t\t\t\tlet that = this;\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.get('ApiSweepstakes/getList', {}, async function(res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthat.gameList = res.list\n\t\t\t\t\tif (that.isGameOpen && that.gameList && that.gameList.length > 0) {\n\t\t\t\t\t\tthat.currentGameInfo = that.gameList.find(item => item.keyName === 'lucky')\n\t\t\t\t\t\tthat.luckyPrice = that.currentGameInfo.amount\n\t\t\t\t\t\tthat.luckyPriceArray = []\n\t\t\t\t\t\tthat.luckyPriceArray = Array.from({\n\t\t\t\t\t\t\tlength: 33\n\t\t\t\t\t\t}, (_, i) => (i + 8) / 10);\n\t\t\t\t\t\tthat.$refs.popupLucky.open()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\tgoEdit() {\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/daihuobiji/detail/fatie?goodid=' + this.opt.id\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\tsavePhoto(){\n\t\t\t\tlet that = this\n\t\t\t\t// 首先下载图片\n\t\t\t\tuni.downloadFile({\n\t\t\t\t    url: that.posterpic,\n\t\t\t\t    success: downloadResult => {\n\t\t\t\t        if (downloadResult.statusCode === 200) {\n\t\t\t\t            // 下载成功，保存图片到系统相册\n\t\t\t\t            uni.saveImageToPhotosAlbum({\n\t\t\t\t                filePath: downloadResult.tempFilePath,\n\t\t\t\t                success: () => {\n\t\t\t\t                    uni.showToast({\n\t\t\t\t                        title: '图片保存成功'\n\t\t\t\t                    });\n\t\t\t\t                },\n\t\t\t\t                fail: () => {\n\t\t\t\t                    uni.showToast({\n\t\t\t\t                        title: '图片保存失败',\n\t\t\t\t                        icon: 'none'\n\t\t\t\t                    });\n\t\t\t\t                }\n\t\t\t\t            });\n\t\t\t\t        } else {\n\t\t\t\t            uni.showToast({\n\t\t\t\t                title: '图片下载失败',\n\t\t\t\t                icon: 'none'\n\t\t\t\t            });\n\t\t\t\t        }\n\t\t\t\t    },\n\t\t\t\t    fail: () => {\n\t\t\t\t        uni.showToast({\n\t\t\t\t            title: '图片下载失败',\n\t\t\t\t            icon: 'none'\n\t\t\t\t        });\n\t\t\t\t    }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t},\n\n\t\t\tshowLinkChange: function() {\n\t\t\t\tthis.showLinkStatus = !this.showLinkStatus;\n\t\t\t},\n\t\t\tgetdata: function() {\n\t\t\t\t\n\t\t\t\tvar that = this;\n\t\t\t\tvar id = this.opt.id || 0;\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.get('ApiShop/product', {\n\t\t\t\t\tid: id\n\t\t\t\t}, function(res) {\n\n\t\t\t\t\tconsole.log(res);\n\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\t\tvar product = res.product;\n\t\t\t\t\tvar pagecontent = JSON.parse(product.detail);\n\t\t\t\t\tthat.business = res.business;\n\t\t\t\t\tthat.product = product;\n\t\t\t\t\tthat.cartnum = res.cartnum;\n\t\t\t\t\tthat.commentlist = res.commentlist;\n\t\t\t\t\tthat.commentcount = res.commentcount;\n\t\t\t\t\tthat.cuxiaolist = res.cuxiaolist;\n\t\t\t\t\tthat.couponlist = res.couponlist;\n\t\t\t\t\tthat.fuwulist = res.fuwulist;\n\t\t\t\t\tthat.notes = product.notes || [];\n\t\t\t\t\tthat.notes_total = product.notes_total || 0;\n\t\t\t\t\tthat.pagecontent = pagecontent;\n\t\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\t\tthat.title = product.name;\n\t\t\t\t\tthat.isfavorite = res.isfavorite;\n\t\t\t\t\tthat.showjiesheng = res.showjiesheng || 0;\n\t\t\t\t\tthat.tjdatalist = res.tjdatalist || [];\n\t\t\t\t\tthat.showtoptabbar = res.showtoptabbar || 0;\n\t\t\t\t\tthat.bboglist = res.bboglist;\n\t\t\t\t\tthat.sharepic = product.pics[0];\n\t\t\t\t\tthat.xcx_scheme = res.xcx_scheme\n\t\t\t\t\t// 处理自定义菜单数据\n\t\t\t\t\tif(res.shopdetail_menudata){\n\t\t\t\t\t\tthat.shopdetail_menudataList = res.shopdetail_menudata.list;\n\t\t\t\t\t\tthat.bottomImg = res.shopdetail_menudata.bottomImg;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthat.shopdetail_menudataList = false;\n\t\t\t\t\t\tthat.bottomImg = '';\n\t\t\t\t\t}\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: product.name\n\t\t\t\t\t});\n\n\t\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid=' + product.bid;\n\t\t\t\t\tif (app.globalData.initdata.kfurl != '') {\n\t\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\n\t\t\t\t\t}\n\t\t\t\t\tif (that.business && that.business.kfurl) {\n\t\t\t\t\t\tthat.kfurl = that.business.kfurl;\n\t\t\t\t\t}\n\t\t\t\t\tthat.loaded({\n\t\t\t\t\t\ttitle: product.sharetitle || product.name,\n\t\t\t\t\t\tpic: product.sharepic || product.pic,\n\t\t\t\t\t\tdesc: product.sharedesc || product.sellpoint\n\t\t\t\t\t});\n\n\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')\n\t\t\t\t\t\tview0.fields({\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tthat.scrolltab0Height = res.height\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t\tlet view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')\n\t\t\t\t\t\tview1.fields({\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tthat.scrolltab1Height = res.height\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t\tlet view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')\n\t\t\t\t\t\tview2.fields({\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\n\t\t\t\t\t\t}, (res) => {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tthat.scrolltab2Height = res.height\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t}, 500)\n\n\t\t\t\t});\n\t\t\t},\n\t\t\tswiperChange: function(e) {\n\t\t\t\tvar that = this;\n\t\t\t\tthat.current = e.detail.current\n\t\t\t},\n\t\t\tpayvideo: function() {\n\t\t\t\tthis.isplay = 1;\n\t\t\t\tuni.createVideoContext('video').play();\n\t\t\t},\n\t\t\tparsevideo: function() {\n\t\t\t\tthis.isplay = 0;\n\t\t\t\tuni.createVideoContext('video').stop();\n\t\t\t},\n\t\t\tbuydialogChange: function(e, btnType) {\n\t\t\t\tif (!this.buydialogShow) {\n\t\t\t\t\t// 如果是直接传入btnType参数的调用方式\n\t\t\t\t\tif (btnType) {\n\t\t\t\t\t\tthis.btntype = btnType;\n\t\t\t\t\t}\n\t\t\t\t\t// 如果是事件触发的调用方式\n\t\t\t\t\telse if (e && e.currentTarget && e.currentTarget.dataset) {\n\t\t\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t\t},\n\t\t\t//收藏操作\n\t\t\taddfavorite: function() {\n\t\t\t\tvar that = this;\n\t\t\t\tvar proid = that.product.id;\n\t\t\t\tapp.post('ApiShop/addfavorite', {\n\t\t\t\t\tproid: proid,\n\t\t\t\t\ttype: 'shop'\n\t\t\t\t}, function(data) {\n\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\tthat.isfavorite = !that.isfavorite;\n\t\t\t\t\t}\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t});\n\t\t\t},\n\t\t\tshareClick: function() {\n\t\t\t\tthis.sharetypevisible = true;\n\t\t\t},\n\t\t\thandleClickMask: function() {\n\t\t\t\tthis.sharetypevisible = false\n\t\t\t},\n\t\t\tshowPoster: function() {\n\t\t\t\tvar that = this;\n\t\t\t\tthat.showposter = true;\n\t\t\t\tthat.sharetypevisible = false;\n\t\t\t\tapp.showLoading('生成海报中');\n\t\t\t\tapp.post('ApiShop/getposter', {\n\t\t\t\t\tproid: that.product.id\n\t\t\t\t}, function(data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif (data.status == 0) {\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.posterpic = data.poster;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tshareScheme: function() {\n\t\t\t\tvar that = this;\n\t\t\t\tapp.showLoading();\n\t\t\t\tapp.post('ApiShop/getwxScheme', {\n\t\t\t\t\tproid: that.product.id\n\t\t\t\t}, function(data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif (data.status == 0) {\n\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.showScheme = true;\n\t\t\t\t\t\tthat.schemeurl = data.openlink\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tschemeDialogClose: function() {\n\t\t\t\tthis.showScheme = false;\n\t\t\t},\n\n\t\t\tposterDialogClose: function() {\n\t\t\t\tthis.showposter = false;\n\t\t\t},\n\t\t\tshowfuwudetail: function() {\n\t\t\t\tthis.showfuwudialog = true;\n\t\t\t},\n\t\t\thidefuwudetail: function() {\n\t\t\t\tthis.showfuwudialog = false\n\t\t\t},\n\t\t\tshowcuxiaodetail: function() {\n\t\t\t\tthis.showcuxiaodialog = true;\n\t\t\t},\n\t\t\thidecuxiaodetail: function() {\n\t\t\t\tthis.showcuxiaodialog = false\n\t\t\t},\n\t\t\tgetcoupon: function() {\n\t\t\t\tthis.showcuxiaodialog = false;\n\t\t\t\tthis.getdata();\n\t\t\t},\n\t\t\tonPageScroll: function(e) {\n\t\t\t\t//var that = this;\n\t\t\t\t//var scrollY = e.scrollTop;     \n\t\t\t\t//if (scrollY > 200) {\n\t\t\t\t//\tthat.scrolltopshow = true;\n\t\t\t\t//}\n\t\t\t\t//if(scrollY < 150) {\n\t\t\t\t//\tthat.scrolltopshow = false\n\t\t\t\t//}\n\t\t\t\t//if (scrollY > 100) {\n\t\t\t\t//\tthat.toptabbar_show = true;\n\t\t\t\t//}\n\t\t\t\t//if(scrollY < 50) {\n\t\t\t\t//\tthat.toptabbar_show = false\n\t\t\t\t//}\n\t\t\t},\n\t\t\tchangetoptab: function(e) {\n\t\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\t\tthis.scrollToViewId = 'scroll_view_tab' + index;\n\t\t\t\tthis.toptabbar_index = index;\n\t\t\t\tif (index == 0) this.scrollTop = 0;\n\t\t\t\tconsole.log(index);\n\t\t\t},\n\t\t\tscroll: function(e) {\n\t\t\t\tvar scrollTop = e.detail.scrollTop;\n\t\t\t\t//console.log(e)\n\t\t\t\tvar that = this;\n\t\t\t\tif (scrollTop > 200) {\n\t\t\t\t\tthat.scrolltopshow = true;\n\t\t\t\t}\n\t\t\t\tif (scrollTop < 150) {\n\t\t\t\t\tthat.scrolltopshow = false\n\t\t\t\t}\n\t\t\t\tif (scrollTop > 100) {\n\t\t\t\t\tthat.toptabbar_show = true;\n\t\t\t\t}\n\t\t\t\tif (scrollTop < 50) {\n\t\t\t\t\tthat.toptabbar_show = false\n\t\t\t\t}\n\t\t\t\tvar height0 = that.scrolltab0Height;\n\t\t\t\tvar height1 = that.scrolltab0Height + that.scrolltab1Height;\n\t\t\t\tvar height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;\n\t\t\t\t//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;\n\t\t\t\tconsole.log('-----------------------');\n\t\t\t\tconsole.log(scrollTop);\n\t\t\t\tconsole.log(height2);\n\t\t\t\tif (scrollTop >= 0 && scrollTop < height0) {\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab0';\n\t\t\t\t\tthis.toptabbar_index = 0;\n\t\t\t\t} else if (scrollTop >= height0 && scrollTop < height1) {\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab1';\n\t\t\t\t\tthis.toptabbar_index = 1;\n\t\t\t\t} else if (scrollTop >= height1 && scrollTop < height2) {\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab2';\n\t\t\t\t\tthis.toptabbar_index = 2;\n\t\t\t\t} else if (scrollTop >= height2) {\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab3';\n\t\t\t\t\tthis.toptabbar_index = 3;\n\t\t\t\t}\n\t\t\t},\n\t\t\tsharemp: function() {\n\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\t\tthis.sharetypevisible = false\n\t\t\t},\n\t\t\tshareapp: function() {\n\t\t\t\tvar that = this;\n\t\t\t\tthat.sharetypevisible = false;\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\n\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\t\tsharedata.title = that.product.sharetitle || that.product.name;\n\t\t\t\t\t\t\tsharedata.summary = that.product.sharedesc || that.product.sellpoint;\n\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +\n\t\t\t\t\t\t\t\t'.html#/shopPackage/shop/product?scene=id_' + that.product.id + '-pid_' + app\n\t\t\t\t\t\t\t\t.globalData.mid;\n\t\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\n\t\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\t\tif (sharelist) {\n\t\t\t\t\t\t\t\tfor (var i = 0; i < sharelist.length; i++) {\n\t\t\t\t\t\t\t\t\tif (sharelist[i]['indexurl'] == '/shopPackage/shop/product') {\n\t\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\t\tif (sharelist[i].url) {\n\t\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\t\tif (sharelink.indexOf('/') === 0) {\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url + '/h5/' + app\n\t\t\t\t\t\t\t\t\t\t\t\t\t.globalData.aid + '.html#' + sharelink;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tif (app.globalData.mid > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +\n\t\t\t\t\t\t\t\t\t\t\t\t\t'pid=' + app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tshowsubqrcode: function() {\n\t\t\t\tthis.$refs.qrcodeDialog.open();\n\t\t\t},\n\t\t\tclosesubqrcode: function() {\n\t\t\t\tthis.$refs.qrcodeDialog.close();\n\t\t\t},\n\t\t\taddcart: function(e) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tthis.cartnum = this.cartnum + e.num;\n\t\t\t},\n\t\t\tshowgg1Dialog: function() {\n\t\t\t\tthis.$refs.gg1Dialog.open();\n\t\t\t},\n\t\t\tclosegg1Dialog: function() {\n\t\t\t\tthis.$refs.gg1Dialog.close();\n\t\t\t},\n\t\t\tshowgg2Dialog: function() {\n\t\t\t\tthis.$refs.gg2Dialog.open();\n\t\t\t},\n\t\t\tclosegg2Dialog: function() {\n\t\t\t\tthis.$refs.gg2Dialog.close();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 点击游戏\n\t\t\t * @param {Object} item\n\t\t\t */\n\t\t\tonClickGame(item) {\n\t\t\t\tconst {\n\t\t\t\t\tkeyName,\n\t\t\t\t\tpath\n\t\t\t\t} = item\n\t\t\t\tthis.currentGameInfo = Object.assign({}, item)\n\t\t\t\tswitch (keyName) {\n\t\t\t\t\tcase 'lucky':\n\t\t\t\t\t\tthis.luckyPrice = item.amount\n\t\t\t\t\t\tthis.$refs.popupLucky.open('top');\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tapp.goto(`/game/${keyName}?id=${this.opt.id}&gameId=${item.id}&amount=${item.amount}`)\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * 输入幸运价\n\t\t\t */\n\t\t\tinputLuckyPrice(e) {\n\t\t\t\tconst {\n\t\t\t\t\tvalue\n\t\t\t\t} = e.detail;\n\t\t\t\tthis.luckyPrice = value\n\t\t\t},\n\t\t\t/**\n\t\t\t * 中奖概率\n\t\t\t */\n\t\t\tgetProbability() {\n\t\t\t\tlet num = this.luckyPrice / this.product.min_price;\n\t\t\t\treturn ((num && num * 100) || 0).toFixed(2);\n\t\t\t},\n\t\t\tonCloseLucky() {\n\t\t\t\tthis.currentGameInfo = {}\n\t\t\t\tthis.$refs.popupLucky.close();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 幸运夺宝-修改价格\n\t\t\t */\n\t\t\tpickerChangePrice(e) {\n\t\t\t\tthis.luckyPrice = this.luckyPriceArray[e.detail.value]\n\t\t\t},\n\t\t\t/**\n\t\t\t * 立即夺宝\n\t\t\t */\n\t\t\tonLuckyNow() {\n\t\t\t\tthis.$refs.popupLucky.close();\n\t\t\t\tapp.goto(\n\t\t\t\t\t`/game/lucky?id=${this.opt.id}&gameId=${this.currentGameInfo.id}&amount=${this.luckyPrice}`\n\t\t\t\t)\n\n\t\t\t},\n\t\t\thidePriceLink() {\n\t\t\t\tapp.goto(this.product.hide_price_link)\n\t\t\t},\n\t\t\t// 添加自定义链接跳转方法\n\t\t\tgoToCustomLink: function(type) {\n\t\t\t\tif (type === 'buybtn' && this.product.buybtn_link_url) {\n\t\t\t\t\tthis.goto({currentTarget: {dataset: {url: this.product.buybtn_link_url}}});\n\t\t\t\t} else if (type === 'addcart' && this.product.addcart_link_url) {\n\t\t\t\t\tthis.goto({currentTarget: {dataset: {url: this.product.addcart_link_url}}});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 处理自定义菜单点击\n\t\t\taddfavorite2: function (item) {\n\t\t\t\tvar that = this;\n\t\t\t\tif(item.pagePath == 'addfavorite::'){\n\t\t\t\t\tvar proid = that.product.id;\n\t\t\t\t\tapp.post('ApiShop/addfavorite', {proid: proid,type: 'shop'}, function (data) {\n\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\tthat.isfavorite = !that.isfavorite;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tlet url = '';\n\t\t\t\t\tif(item.menuType == 2) {\n\t\t\t\t\t\tif(item.pagePath){url = item.pagePath}else{url = '/pages/shop/cart'}\n\t\t\t\t\t}\n\t\t\t\t\tif(item.menuType == 1) {\n\t\t\t\t\t\tif(item.pagePath){url = item.pagePath}else{url = 'pages/kefu/index'}\n\t\t\t\t\t}\n\t\t\t\t\tif(item.menuType == 3 || item.menuType == 4){\n\t\t\t\t\t\turl = item.pagePath\n\t\t\t\t\t}\n\t\t\t\t\t// 判断是否为基础页面\n\t\t\t\t\tif(url == '/pages/shop/cart') return app.goto(url);\n\t\t\t\t\tif(url == '/pages/my/usercenter') return app.goto(url);\n\t\t\t\t\tif(url == '/pages/shop/classify') return app.goto(url);\n\t\t\t\t\tif(url == '/pages/shop/prolist') return app.goto(url);\n\t\t\t\t\tif(url == '/pages/index/index') return app.goto(url);\n\t\t\t\t\tif(url.split('?')[1] && (url.split('?')[1].split('=')[0] == 'bid')){\n\t\t\t\t\t\tapp.goto(url)\n\t\t\t\t\t}else{\n\t\t\t\t\t\tif(url.indexOf('tel:') === 0){\n\t\t\t\t\t\t\tapp.goto(url);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tapp.goto(url + '?bid=' + that.product.bid)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t};\n</script>\n<style>\n\tpage {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.container {\n\t\theight: 100%;\n\t}\n\n\t/* 顶部关注栏 */\n\t.follow_topbar {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: rgba(0, 0, 0, 0.8);\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tz-index: 13;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.follow_topbar .headimg {\n\t\twidth: 64rpx;\n\t\theight: 64rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.follow_topbar .info {\n\t\tflex: 1;\n\t}\n\n\t.follow_topbar .info .i {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ccc;\n\t\tline-height: 28rpx;\n\t}\n\n\t.follow_topbar .sub {\n\t\theight: 48rpx;\n\t\tpadding: 0 20rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #fff;\n\t\tline-height: 48rpx;\n\t\tborder-radius: 6rpx;\n\t}\n\n\t/* 二维码弹窗 */\n\t.qrcodebox {\n\t\tbackground: #fff;\n\t\tpadding: 50rpx;\n\t\tborder-radius: 20rpx;\n\t\ttext-align: center;\n\t}\n\n\t.qrcodebox .img {\n\t\twidth: 400rpx;\n\t\theight: 400rpx;\n\t}\n\n\t.qrcodebox .txt {\n\t\tmargin-top: 20rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t}\n\n\t.qrcodebox .close {\n\t\tposition: absolute;\n\t\tbottom: -100rpx;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tborder: 1rpx solid rgba(255,255,255,0.5);\n\t\tborder-radius: 50%;\n\t\tpadding: 8rpx;\n\t}\n\n\t/* 轮播图 */\n\t.swiper-container {\n\t\theight: 750rpx;\n\t\tposition: relative;\n\t}\n\n\t.swiper,\n\t.swiper-item-view {\n\t\twidth: 100%;\n\t\theight: 750rpx;\n\t}\n\n\t.swiper .img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.imageCount {\n\t\tposition: absolute;\n\t\tright: 26rpx;\n\t\tbottom: 20rpx;\n\t\twidth: 100rpx;\n\t\theight: 50rpx;\n\t\tline-height: 50rpx;\n\t\ttext-align: center;\n\t\tbackground: rgba(0,0,0,0.3);\n\t\tborder-radius: 40rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 26rpx;\n\t}\n\n\t/* 视频播放 */\n\t.provideo {\n\t\tposition: absolute;\n\t\tbottom: 30rpx;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 160rpx;\n\t\theight: 54rpx;\n\t\tpadding: 0 20rpx 0 4rpx;\n\t\tbackground: rgba(255,255,255,0.7);\n\t\tborder-radius: 27rpx;\n\t}\n\n\t.provideo image {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t}\n\n\t.provideo .txt {\n\t\tflex: 1;\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tpadding-left: 10rpx;\n\t}\n\n\t.videobox {\n\t\twidth: 100%;\n\t\theight: 750rpx;\n\t\tbackground: #000;\n\t\ttext-align: center;\n\t}\n\n\t.videobox .video {\n\t\twidth: 100%;\n\t\theight: 650rpx;\n\t}\n\n\t.videobox .parsevideo {\n\t\twidth: 140rpx;\n\t\theight: 40rpx;\n\t\tline-height: 40rpx;\n\t\tmargin: 20rpx auto 0;\n\t\tbackground: #ccc;\n\t\tborder-radius: 25rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t}\n\n\t/* 商品信息 */\n\t.header {\n\t\tpadding: 20rpx 3%;\n\t\tbackground: #fff;\n\t}\n\n\t.header .price_share {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\theight: 100rpx;\n\t}\n\n\t.header .price {\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t}\n\n\t.header .price .f1 {\n\t\tfont-size: 50rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.header .price .f2 {\n\t\tfont-size: 26rpx;\n\t\tcolor: #C2C2C2;\n\t\ttext-decoration: line-through;\n\t\tmargin-left: 30rpx;\n\t\tpadding-bottom: 5rpx;\n\t}\n\n\t.header .share {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmin-width: 60rpx;\n\t}\n\n\t.header .share .img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tmargin-bottom: 2rpx;\n\t}\n\n\t.header .share .txt {\n\t\tfont-size: 20rpx;\n\t\tcolor: #333;\n\t}\n\n\t.header .title {\n\t\tfont-size: 32rpx;\n\t\tline-height: 42rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #000;\n\t}\n\n\t.header .sellpoint {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tpadding-top: 20rpx;\n\t}\n\n\t.header .sales_stock {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t\tmargin-top: 30rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #777;\n\t}\n\n\t.header .commission {\n\t\tdisplay: inline-block;\n\t\tmargin-top: 20rpx;\n\t\tmargin-bottom: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 20rpx;\n\t\theight: 44rpx;\n\t\tline-height: 44rpx;\n\t\tpadding: 0 20rpx\n\t}\n\n\t.header .upsavemoney {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 20rpx;\n\t\tmargin-bottom: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 20rpx;\n\t\theight: 70rpx;\n\t\tpadding: 0 20rpx\n\t}\n\n\t.choose {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\theight: 88rpx;\n\t\tline-height: 88rpx;\n\t\tpadding: 0 3%;\n\t\tcolor: #333;\n\t}\n\n\t.choose .f0 {\n\t\tcolor: #555;\n\t\tfont-weight: bold;\n\t\theight: 32rpx;\n\t\tfont-size: 24rpx;\n\t\tpadding-right: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center\n\t}\n\n\t.choose .f2 {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.cuxiaodiv {\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 0 3%;\n\t}\n\n\t.fuwupoint {\n\t\twidth: 100%;\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\theight: 88rpx;\n\t\tline-height: 88rpx;\n\t\tpadding: 12rpx 0;\n\t\tdisplay: flex;\n\t\talign-items: center\n\t}\n\n\t.fuwupoint .f0 {\n\t\tcolor: #555;\n\t\tfont-weight: bold;\n\t\theight: 32rpx;\n\t\tfont-size: 24rpx;\n\t\tpadding-right: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center\n\t}\n\n\t.fuwupoint .f1 {\n\t\tmargin-right: 20rpx;\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-wrap: nowrap;\n\t\toverflow: hidden\n\t}\n\n\t.fuwupoint .f1 .t {\n\t\tpadding: 4rpx 20rpx 4rpx 0;\n\t\tcolor: #777;\n\t\tflex-shrink: 0\n\t}\n\n\t.fuwupoint .f1 .t:before {\n\t\tcontent: \"\";\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tmargin-top: -4rpx;\n\t\tmargin-right: 10rpx;\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\n\t\tbackground-size: 24rpx auto;\n\t}\n\n\t.fuwupoint .f2 {\n\t\tflex-shrink: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.fuwupoint .f2 .img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.fuwudialog-content {\n\t\tfont-size: 24rpx\n\t}\n\n\t.fuwudialog-content .f1 {\n\t\tcolor: #333;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tfont-weight: bold\n\t}\n\n\t.fuwudialog-content .f1:before {\n\t\tcontent: \"\";\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tmargin-top: -4rpx;\n\t\tmargin-right: 10rpx;\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\n\t\tbackground-size: 24rpx auto;\n\t}\n\n\t.fuwudialog-content .f2 {\n\t\tcolor: #777\n\t}\n\n\t.cuxiaopoint {\n\t\twidth: 100%;\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\theight: 88rpx;\n\t\tline-height: 88rpx;\n\t\tpadding: 12rpx 0;\n\t\tdisplay: flex;\n\t\talign-items: center\n\t}\n\n\t.cuxiaopoint .f0 {\n\t\tcolor: #555;\n\t\tfont-weight: bold;\n\t\theight: 32rpx;\n\t\tfont-size: 24rpx;\n\t\tpadding-right: 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center\n\t}\n\n\t.cuxiaopoint .f1 {\n\t\tmargin-right: 20rpx;\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-wrap: nowrap;\n\t\toverflow: hidden\n\t}\n\n\t.cuxiaopoint .f1 .t {\n\t\tmargin-left: 10rpx;\n\t\tborder-radius: 3px;\n\t\tfont-size: 24rpx;\n\t\theight: 40rpx;\n\t\tline-height: 40rpx;\n\t\tpadding-right: 10rpx;\n\t\tflex-shrink: 0;\n\t\toverflow: hidden\n\t}\n\n\t.cuxiaopoint .f1 .t0 {\n\t\tdisplay: inline-block;\n\t\tpadding: 0 5px;\n\t}\n\n\t.cuxiaopoint .f1 .t1 {\n\t\tpadding: 0 4px\n\t}\n\n\t.cuxiaopoint .f2 {\n\t\tflex-shrink: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.cuxiaopoint .f2 .img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.cuxiaodiv .cuxiaoitem {\n\t\tborder-bottom: 1px solid #E6E6E6;\n\t}\n\n\t.cuxiaodiv .cuxiaoitem:last-child {\n\t\tborder-bottom: 0\n\t}\n\n\t.popup__container {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tz-index: 10;\n\t\tbackground: #fff\n\t}\n\n\t.popup__overlay {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 11;\n\t\topacity: 0.3;\n\t\tbackground: #000\n\t}\n\n\t.popup__modal {\n\t\twidth: 100%;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tcolor: #3d4145;\n\t\toverflow-x: hidden;\n\t\toverflow-y: hidden;\n\t\topacity: 1;\n\t\tpadding-bottom: 20rpx;\n\t\tbackground: #fff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tz-index: 12;\n\t\tmin-height: 600rpx;\n\t\tmax-height: 1000rpx;\n\t}\n\n\t.popup__title {\n\t\ttext-align: center;\n\t\tpadding: 30rpx;\n\t\tposition: relative;\n\t\tposition: relative\n\t}\n\n\t.popup__title-text {\n\t\tfont-size: 32rpx\n\t}\n\n\t.popup__close {\n\t\tposition: absolute;\n\t\ttop: 34rpx;\n\t\tright: 34rpx\n\t}\n\n\t.popup__content {\n\t\twidth: 100%;\n\t\tmax-height: 880rpx;\n\t\toverflow-y: scroll;\n\t\tpadding: 20rpx 0;\n\t}\n\n\t.service-item {\n\t\tdisplay: flex;\n\t\tpadding: 0 40rpx 20rpx 40rpx;\n\t}\n\n\t.service-item .prefix {\n\t\tpadding-top: 2px;\n\t}\n\n\t.service-item .suffix {\n\t\tpadding-left: 10rpx;\n\t}\n\n\t.service-item .suffix .type-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #49aa34;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\n\t.shop {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 20rpx 3%;\n\t\tposition: relative;\n\t\tmin-height: 100rpx;\n\t}\n\n\t.shop .p1 {\n\t\twidth: 90rpx;\n\t\theight: 90rpx;\n\t\tborder-radius: 6rpx;\n\t\tflex-shrink: 0\n\t}\n\n\t.shop .p2 {\n\t\tpadding-left: 10rpx\n\t}\n\n\t.shop .p2 .t1 {\n\t\twidth: 100%;\n\t\theight: 40rpx;\n\t\tline-height: 40rpx;\n\t\toverflow: hidden;\n\t\tcolor: #111;\n\t\tfont-weight: bold;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.shop .p2 .t2 {\n\t\twidth: 100%;\n\t\theight: 30rpx;\n\t\tline-height: 30rpx;\n\t\toverflow: hidden;\n\t\tcolor: #999;\n\t\tfont-size: 24rpx;\n\t\tmargin-top: 8rpx\n\t}\n\n\t.shop .p4 {\n\t\theight: 64rpx;\n\t\tline-height: 64rpx;\n\t\tcolor: #FFFFFF;\n\t\tborder-radius: 32rpx;\n\t\tmargin-left: 20rpx;\n\t\tflex-shrink: 0;\n\t\tpadding: 0 30rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold\n\t}\n\n\t.detail {\n\t\tmin-height: 200rpx;\n\t}\n\n\t.bottomimg {\n\t\twidth: 100%;\n\t}\n\n\t.detail_title {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 60rpx;\n\t\tmargin-bottom: 30rpx\n\t}\n\n\t.detail_title .t0 {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #222222;\n\t\tmargin: 0 20rpx\n\t}\n\n\t.detail_title .t1 {\n\t\twidth: 12rpx;\n\t\theight: 12rpx;\n\t\tbackground: rgba(253, 74, 70, 0.2);\n\t\ttransform: rotate(45deg);\n\t\tmargin: 0 4rpx;\n\t\tmargin-top: 6rpx\n\t}\n\n\t.detail_title .t2 {\n\t\twidth: 18rpx;\n\t\theight: 18rpx;\n\t\tbackground: rgba(253, 74, 70, 0.4);\n\t\ttransform: rotate(45deg);\n\t\tmargin: 0 4rpx\n\t}\n\n\t.commentbox {\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\tpadding: 0 3%;\n\t\tmargin-top: 20rpx\n\t}\n\n\t.commentbox .title {\n\t\theight: 90rpx;\n\t\tline-height: 90rpx;\n\t\tborder-bottom: 1px solid #DDDDDD;\n\t\tdisplay: flex\n\t}\n\n\t.commentbox .title .f1 {\n\t\tflex: 1;\n\t\tcolor: #111111;\n\t\tfont-weight: bold;\n\t\tfont-size: 30rpx\n\t}\n\n\t.commentbox .title .f2 {\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\talign-items: center\n\t}\n\n\t.commentbox .nocomment {\n\t\theight: 100rpx;\n\t\tline-height: 100rpx\n\t}\n\n\t.comment {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmin-height: 200rpx;\n\t}\n\n\t.comment .item {\n\t\tbackground-color: #fff;\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.comment .item .f1 {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\talign-items: center;\n\t\tpadding: 10rpx 0;\n\t}\n\n\t.comment .item .f1 .t1 {\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-radius: 50%;\n\t}\n\n\t.comment .item .f1 .t2 {\n\t\tpadding-left: 10rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.comment .item .f1 .t3 {\n\t\ttext-align: right;\n\t}\n\n\t.comment .item .f1 .t3 .img {\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\tmargin-left: 10rpx\n\t}\n\n\t.comment .item .score {\n\t\tfont-size: 24rpx;\n\t\tcolor: #f99716;\n\t}\n\n\t.comment .item .score image {\n\t\twidth: 140rpx;\n\t\theight: 50rpx;\n\t\tvertical-align: middle;\n\t\tmargin-bottom: 6rpx;\n\t\tmargin-right: 6rpx;\n\t}\n\n\t.comment .item .f2 {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: 100%;\n\t\tpadding: 10rpx 0;\n\t}\n\n\t.comment .item .f2 .t1 {\n\t\tcolor: #333;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.comment .item .f2 .t2 {\n\t\tdisplay: flex;\n\t\twidth: 100%\n\t}\n\n\t.comment .item .f2 .t2 image {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tmargin: 10rpx;\n\t}\n\n\t.comment .item .f2 .t3 {\n\t\tcolor: #aaa;\n\t\tfont-size: 24rpx;\n\t}\n\n\t.comment .item .f3 {\n\t\tmargin: 20rpx auto;\n\t\tpadding: 0 30rpx;\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t\tborder: 1px solid #E6E6E6;\n\t\tborder-radius: 30rpx;\n\t\tcolor: #111111;\n\t\tfont-weight: bold;\n\t\tfont-size: 26rpx\n\t}\n\n\t.bottombar {\n\t\twidth: 94%;\n\t\tposition: fixed;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t\tbackground: #fff;\n\t\tdisplay: flex;\n\t\theight: 100rpx;\n\t\tpadding: 0 4% 0 2%;\n\t\talign-items: center;\n\t\tbox-sizing: content-box\n\t}\n\n\t.bottombar .f1 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: 30rpx\n\t}\n\n\t.bottombar .f1 .item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\twidth: 100rpx;\n\t\tposition: relative\n\t}\n\n\t.bottombar .f1 .item .img {\n\t\twidth: 44rpx;\n\t\theight: 44rpx\n\t}\n\n\t.bottombar .f1 .item .t1 {\n\t\tfont-size: 18rpx;\n\t\tcolor: #222222;\n\t\theight: 30rpx;\n\t\tline-height: 30rpx;\n\t\tmargin-top: 6rpx\n\t}\n\n\t.bottombar .op {\n\t\tflex: 1;\n\t\tborder-radius: 36rpx;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t}\n\n\t.bottombar .tocart {\n\t\tflex: 1;\n\t\theight: 72rpx;\n\t\tline-height: 72rpx;\n\t\tcolor: #fff;\n\t\tborder-radius: 0px;\n\t\tborder: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold\n\t}\n\n\t.bottombar .tobuy {\n\t\tflex: 1;\n\t\theight: 72rpx;\n\t\tline-height: 72rpx;\n\t\tcolor: #fff;\n\t\tborder-radius: 0px;\n\t\tborder: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold\n\t}\n\n\t.bottombar .cartnum {\n\t\tposition: absolute;\n\t\tright: 4rpx;\n\t\ttop: -4rpx;\n\t\tcolor: #fff;\n\t\tborder-radius: 50%;\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tline-height: 32rpx;\n\t\ttext-align: center;\n\t\tfont-size: 22rpx;\n\t}\n\n\t.bottombar .op2 {\n\t\twidth: 60%;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t}\n\n\t.bottombar .tocart2 {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #fa938a;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.bottombar .tobuy2 {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #df2e24;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\n\t.sharetypecontent {\n\t\theight: 250rpx;\n\t\twidth: 710rpx;\n\t\tmargin: 20rpx;\n\t\tdisplay: flex;\n\t\tpadding: 50rpx;\n\t\talign-items: flex-end\n\t}\n\n\t.sharetypecontent .f1 {\n\t\tcolor: #51c332;\n\t\twidth: 50%;\n\t\theight: 150rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tbackground: #fff;\n\t\tfont-size: 28rpx;\n\t\tborder: 0\n\t}\n\n\t.sharetypecontent button::after {\n\t\tborder: 0\n\t}\n\n\t.sharetypecontent .f1 .img {\n\t\twidth: 90rpx;\n\t\theight: 90rpx\n\t}\n\n\t.sharetypecontent .f2 {\n\t\tcolor: #51c332;\n\t\twidth: 50%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center\n\t}\n\n\t.sharetypecontent .f2 .img {\n\t\twidth: 90rpx;\n\t\theight: 90rpx\n\t}\n\n\t.sharetypecontent .t1 {\n\t\theight: 60rpx;\n\t\tline-height: 60rpx;\n\t\tcolor: #666\n\t}\n\n\t.posterDialog {\n\t\tposition: fixed;\n\t\tz-index: 9;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.8);\n\t\ttop: var(--window-top);\n\t\tleft: 0\n\t}\n\n\t.posterDialog .main {\n\t\twidth: 80%;\n\t\tmargin: 60rpx 10% 30rpx 10%;\n\t\tbackground: #fff;\n\t\tposition: relative;\n\t\tborder-radius: 20rpx\n\t}\n\n\t.posterDialog .close {\n\t\tposition: absolute;\n\t\tpadding: 20rpx;\n\t\ttop: 0;\n\t\tright: 0\n\t}\n\n\t.posterDialog .close .img {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.posterDialog .content {\n\t\twidth: 100%;\n\t\tpadding: 70rpx 20rpx 30rpx 20rpx;\n\t\tcolor: #333;\n\t\tfont-size: 30rpx;\n\t\ttext-align: center\n\t}\n\n\t.posterDialog .content .img {\n\t\twidth: 540rpx;\n\t\theight: 960rpx\n\t}\n\n\t.linkDialog {\n\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\tz-index: 11;\n\t}\n\n\t.linkDialog .main {\n\t\twidth: 90%;\n\t\tposition: fixed;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\tmargin: 0;\n\t\t-webkit-transform: translate(-50%, -50%);\n\t\ttransform: translate(-50%, -50%);\n\t}\n\n\t.linkDialog .title {\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.linkDialog .row {\n\t\tdisplay: flex;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tpadding: 0 16rpx;\n\t}\n\n\t.linkDialog .row .f1 {\n\t\twidth: 40%;\n\t\ttext-align: left;\n\t}\n\n\t.linkDialog .row .f2 {\n\t\twidth: 60%;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\ttext-align: right;\n\t\talign-items: center;\n\t}\n\n\t.linkDialog .image {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tmargin-left: 8rpx;\n\t\tmargin-top: 2rpx;\n\t}\n\n\t.linkDialog .copyicon {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tmargin-left: 8rpx;\n\t\tposition: relative;\n\t\ttop: 4rpx;\n\t}\n\n\t.paramitem {\n\t\tdisplay: flex;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\t\tpadding: 20rpx\n\t}\n\n\t.paramitem .f1 {\n\t\twidth: 30%;\n\t\tcolor: #666\n\t}\n\n\t.paramitem .f2 {\n\t\tcolor: #333\n\t}\n\n\t.paramitem:last-child {\n\t\tborder-bottom: 0\n\t}\n\n\t.xihuan {\n\t\theight: auto;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: 20rpx 160rpx;\n\t\tmargin-top: 20rpx\n\t}\n\n\t.xihuan-line {\n\t\theight: auto;\n\t\tpadding: 0;\n\t\toverflow: hidden;\n\t\tflex: 1;\n\t\theight: 0;\n\t\tborder-top: 1px solid #eee\n\t}\n\n\t.xihuan-text {\n\t\tpadding: 0 32rpx;\n\t\ttext-align: center;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\t.xihuan-text .txt {\n\t\tcolor: #111;\n\t\tfont-size: 30rpx\n\t}\n\n\t.xihuan-text .img {\n\t\ttext-align: center;\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tmargin-right: 12rpx\n\t}\n\n\t.prolist {\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tpadding: 8rpx 20rpx;\n\t}\n\n\t.toptabbar_tab {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\theight: 90rpx;\n\t\tbackground: #fff;\n\t\ttop: var(--window-top);\n\t\tz-index: 11;\n\t\tposition: fixed;\n\t\tborder-bottom: 1px solid #f3f3f3\n\t}\n\n\t.toptabbar_tab .item {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\ttext-align: center;\n\t\tcolor: #666;\n\t\theight: 90rpx;\n\t\tline-height: 90rpx;\n\t\toverflow: hidden;\n\t\tposition: relative\n\t}\n\n\t.toptabbar_tab .item .after {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tleft: 50%;\n\t\tmargin-left: -16rpx;\n\t\tbottom: 10rpx;\n\t\theight: 3px;\n\t\tborder-radius: 1.5px;\n\t\twidth: 32rpx\n\t}\n\n\t.toptabbar_tab .on {\n\t\tcolor: #323233;\n\t}\n\n\t.toptabbar_tab .on .after {\n\t\tdisplay: block\n\t}\n\n\t/* 回到顶部按钮 */\n\t.scrolltop {\n\t  position: fixed;\n\t  right: 20rpx;\n\t  bottom: 120rpx;\n\t  width: 80rpx;\n\t  height: 80rpx;\n\t  background: rgba(0,0,0,0.4);\n\t  border-radius: 50%;\n\t  display: flex;\n\t  align-items: center;\n\t  justify-content: center;\n\t}\n\n\t.scrolltop .image {\n\t  width: 40rpx;\n\t  height: 40rpx;\n\t}\n\n\t/* 瀑布流布局 */\n\t.waterfall-wrapper {\n\t  display: flex;\n\t  justify-content: space-between;\n\t  padding: 10rpx;\n\t}\n\n\t.waterfall-column {\n\t  width: 32%;\n\t}\n\n\t/* 笔记项 */\n\t.note-item {\n\t  background: #fff;\n\t  border-radius: 12rpx;\n\t  margin-bottom: 20rpx;\n\t  overflow: hidden;\n\t}\n\n\t.note-images {\n\t  width: 100%;\n\t  border-radius: 8rpx 8rpx 0 0;\n\t}\n\n\t.note-images image {\n\t  width: 100%;\n\t  height: auto;\n\t  display: block;\n\t}\n\n\t.note-content {\n\t  padding: 16rpx;\n\t  font-size: 26rpx;\n\t  color: #333;\n\t  line-height: 1.4;\n\t}\n\n\t.note-user {\n\t  display: flex;\n\t  align-items: center;\n\t  padding: 16rpx;\n\t  border-top: 1rpx solid #f5f5f5;\n\t}\n\n\t.avatar {\n\t  width: 48rpx;\n\t  height: 48rpx;\n\t  border-radius: 50%;\n\t  margin-right: 12rpx;\n\t}\n\n\t.user-info {\n\t  flex: 1;\n\t}\n\n\t.nickname {\n\t  font-size: 24rpx;\n\t  color: #666;\n\t}\n\n\t.note-likes {\n\t  display: flex;\n\t  align-items: center;\n\t}\n\n\t.note-likes image {\n\t  width: 32rpx;\n\t  height: 32rpx;\n\t  margin-right: 8rpx;\n\t}\n\n\t.note-likes text {\n\t  font-size: 24rpx;\n\t  color: #999;\n\t}\n\n\t/* 弹窗关闭按钮 */\n\t.ggdiaplog_close {\n\t  position: absolute;\n\t  bottom: -100rpx;\n\t  left: 50%;\n\t  margin-left: -25rpx;\n\t  border: 1rpx solid rgba(255,255,255,0.5);\n\t  border-radius: 50%;\n\t  width: 50rpx;\n\t  height: 50rpx;\n\t  padding: 8rpx;\n\t}\n\n\t/* 方案弹窗 */\n\t.schemeDialog {\n\t  font-size: 26rpx;\n\t  z-index: 12;\n\t  line-height: 1.6;\n\t  padding: 16rpx;\n\t}\n\n\t.schemeDialog .main {\n\t  position: absolute;\n\t  top: 50%;\n\t  left: 50%;\n\t  transform: translate(-50%, -50%);\n\t  width: 90%;\n\t  background: #fff;\n\t  border-radius: 20rpx;\n\t  padding: 40rpx 30rpx;\n\t}\n\n\t/* 复制按钮 */\n\t.copybtn {\n\t  text-align: center;\n\t  padding: 15rpx 20rpx;\n\t  border-radius: 50rpx;\n\t  color: #fff;\n\t  margin-top: 20rpx;\n\t}\n\n\t.huang_nums {\n\t\tpadding: 2px 5px;\n\t\tbackground: #97e29d;\n\t\tborder-radius: 10px;\n\t\tcolor: #fff;\n\t\tfont-size: 10px;\n\t}\n\n\t.note-user .user-info .nickname {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\t\n\t.note-user .user-info .title {\n\t\tposition: absolute;\n\t\tcolor: #999;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 60%;\n\t\tmargin-top: 4rpx;\n\t\tz-index: 10;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t\tpadding-top: 50px;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.game .game-grid {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tmargin-right: 6rpx;\n\t\ttext-align: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tfont-size: 24rpx;\n\t}\n\n\t.game .game-img {\n\t\tdisplay: block;\n\t\twidth: 90px;\n\t\theight: 70px;\n\t}\n\n\t.model {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\ttext-align: center;\n\t\ttransform-origin: right top;\n\t\ttransition: all 1s;\n\t}\n\n\t/* .luckBao {\n\t\twidth: 305px;\n\t\theight: 305px;\n\t\tcolor: #fff;\n\t\tpadding-top: 99px;\n\t\tbox-sizing: border-box;\n\t\tbackground: url('../../game/static/game/lucky/lucky_pre.png') no-repeat;\n\t\tbackground-size: 100% 100%;\n\t} */\n\n\t.luckBao-text {\n\t\tfont-size: 18px;\n\t\tcolor: #fff;\n\t}\n\n\t.luckBao-input-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 60%;\n\t\tmargin: 20px auto 0;\n\t\tposition: relative;\n\t}\n\n\t.luckBao-input-class {\n\t\toutline: none;\n\t\tborder: 1px solid #ed8b56;\n\t\twidth: 66px;\n\t\theight: 35px;\n\t\tborder-radius: 35px;\n\t\ttext-align: center;\n\t\tfont-size: 18px;\n\t\tcolor: #F04F30;\n\t\tbackground: #fff;\n\t}\n\n\t.luckBao-tips-wrap1 {\n\t\tmargin-top: 20px;\n\t\ttext-align: center;\n\t}\n\n\t.luckBao-tips-wrap2 {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttext-align: center;\n\t}\n\n\t.img-cursor-1 {\n\t\tmargin: 0 5px;\n\t\twidth: 15px;\n\t\theight: 15px;\n\t}\n\n\t.img-cursor-2 {\n\t\twidth: 36px;\n\t\theight: 36px;\n\t}\n\n\t.lucky-btn-now {\n\t\tbackground: linear-gradient(90deg, #934FFE, #C936DF);\n\t\tborder-radius: 39px;\n\t\twidth: 150px;\n\t\theight: 40px;\n\t\tline-height: 40px;\n\t\tcolor: #fff;\n\t\ttext-align: center;\n\t\tfont-size: 18px;\n\t\tmargin: 20px auto 10px;\n\t}\n\n\t.luckBao-header {\n\t\tmargin-bottom: 10px;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.luckBao-img-close {\n\t\twidth: 25px;\n\t\theight: 25px;\n\t}\n\n\t.lucky-btn-origin-price {\n\t\tfont-size: 18px;\n\t\tcolor: #FFB320;\n\t\tletter-spacing: 3px;\n\t}\n\t\n\t.pp4 {\n\t    height: 32px;\n\t    line-height: 32px;\n\t    color: #FFFFFF;\n\t    border-radius: 16px;\n\t    margin-left: 10px;\n\t    flex-shrink: 0;\n\t    padding: 0 15px;\n\t    font-size: 12px;\n\t    font-weight: bold;\n\t\twidth: 45%;\n\t\ttext-align: center;\n\t}\n\t\t/* 笔记列表样式 */\n\t\t.notes-section {\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 20rpx 3%;\n\t}\n\t\n\t.notes-title {\n\t\theight: 90rpx;\n\t\tline-height: 90rpx;\n\t\tborder-bottom: 1px solid #DDDDDD;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.notes-title .f1 {\n\t\tcolor: #111111;\n\t\tfont-weight: bold;\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t.notes-title .f2 {\n\t\tcolor: #333;\n\t\tfont-size: 28rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.notes-list {\n\t\tpadding: 0;\n\t}\n\t\n\t.waterfall-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.waterfall-column {\n\t\twidth: 31%; /* 略小于33.33%以留出间距 */\n\t}\n\t\n\t.note-item {\n\t\tbackground: #fff;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 20rpx;\n\t\toverflow: hidden;\n\t}\n\t\n\t.note-images {\n\t\twidth: 100%;\n\t\tborder-radius: 8rpx 8rpx 0 0;\n\t}\n\t\n\t.note-images image {\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tdisplay: block;\n\t}\n\t\n\t.note-content {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tpadding: 16rpx;\n\t\tline-height: 1.4;\n\t}\n\t\n\t.note-user {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 16rpx;\n\t\tborder-top: 1rpx solid #f5f5f5;\n\t}\n\t\n\t.avatar {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 12rpx;\n\t}\n\t\n\t.user-info {\n\t\tflex: 1;\n\t}\n\t\n\t.nickname {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.note-likes {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.note-likes image {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tmargin-right: 8rpx;\n\t}\n\t\n\t.note-likes text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115009074\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}