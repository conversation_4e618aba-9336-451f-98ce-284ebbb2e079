{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?1674", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?6efa", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?cbae", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?a6ef", "uni-app:///pagesExt/sign/uni-calendar/uni-calendar.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?3f7c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/uni-calendar/uni-calendar.vue?5ac9"], "names": ["lunarInfo", "solarMonth", "Gan", "<PERSON><PERSON>", "Animals", "solarTerm", "sTermInfo", "nStr1", "nStr2", "nStr3", "lYearDays", "sum", "leapMonth", "leapDays", "monthDays", "solarDays", "toGanZhiYear", "to<PERSON><PERSON>", "toGanZhi", "getTerm", "parseInt", "_info", "to<PERSON>hina<PERSON><PERSON>", "s", "toChinaDay", "getAnimal", "solar2lunar", "temp", "offset", "i", "isToday", "nWeek", "isLeap", "gzM", "isTerm", "Term", "lunar2solar", "_day", "leap", "isAdd", "Calendar", "date", "selected", "startDate", "endDate", "range", "before", "after", "data", "dd", "fullDate", "year", "month", "day", "dateArr", "lunar", "disable", "full", "disableBefore", "disableAfter", "multiplesStatus", "checked", "multiple", "beforeMultiple", "after<PERSON><PERSON><PERSON><PERSON>", "isDay", "db", "de", "k", "arr", "lastMonthDays", "currentMonthDys", "nextMonthDays", "weeks", "dates", "canlender", "t", "components", "calendarItem", "emits", "props", "type", "default", "insert", "showMonth", "clearDate", "backColor", "fontColor", "show", "calendar", "nowDate", "aniMaskShow", "computed", "okText", "cancelText", "todayText", "monText", "TUEText", "WEDText", "THUText", "FRIText", "SATText", "SUNText", "watch", "created", "methods", "clean", "bindDateChange", "console", "init", "open", "setTimeout", "close", "confirm", "change", "monthSwitch", "setEmit", "extraInfo", "fulldate", "choiceDate", "backtoday", "pre", "next", "setDate"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACq7BrxB;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAz3BA;EAEA;AACA;AACA;AACA;AACA;EACAA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;EACA;EAAA;;EAEA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;EACAC,kHACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA,sGACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;IAAA;IACA;MAAAC;IAAA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IAAA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;MACA;IACA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;MAAA;IAAA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;MAAA;IAAA;IACA;IACA;MAAA;MACA;IACA;MACA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IACA;MAAA;IAAA;IACA;MAAA;IAAA;IACA;IACA,aACAC,iDACAA,iDACAA,kDACAA,kDACAA,kDACAA,iDACA;IACA,eACAC,uBACAA,uBACAA,uBACAA,uBAEAA,uBACAA,uBACAA,uBACAA,uBAEAA,uBACAA,uBACAA,uBACAA,uBAEAA,uBACAA,uBACAA,uBACAA,uBAEAA,uBACAA,uBACAA,uBACAA,uBAEAA,uBACAA,uBACAA,uBACAA,sBACA;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IAAA;IACA;MAAA;IAAA;IACA;IACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAC;IAAA;IACA;IACA;MACA;QACAD;QAAA;MACA;QACAA;QAAA;QACA;MACA;QACAA;QAAA;QACA;MACA;QACAA;QACAA;IAAA;IAEA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;EACAE;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;IAAA;IACA;IACA;MACA;IACA;IACA;IACA;MACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;IAAA;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;MACAC;IACA;IACA;MACAA;MAAAC;IACA;;IAEA;IACA;IACA;IACA;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;MACA;QACA;QACAC;QAAAL;MACA;QACAA;MACA;MACA;MACA;QAAAK;MAAA;MACAJ;IACA;IACA;IACA;MACA;QACAI;MACA;QACAA;QAAA;MACA;IACA;IACA;MACAJ;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;MACAK;IACA;;IAEA;IACA;IACA;IACA;MACAC;MACAC;IACA;IACA;MACAD;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;EACA;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MAAA;IAAA;IACA;MAAA;IAAA;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IACA;MAAA;IAAA;;IAEA;IACA;IACA;MACAT;IACA;IACA;IAAA;IACA;MACAU;MACA;QAAA;QACA;UACAV;UAAAW;QACA;MACA;MACAX;IACA;IACA;IACA;MAAAA;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;EACA;AACA;AAAA,IAGAY;EACA,oBAMA;IAAA;MALAC;MACAC;MACAC;MACAC;MACAC;IAAA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;AACA;AACA;AACA;EAHA;IAAA;IAAA,OAIA;MACA;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;QACAC;QACAC;QACAC;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACA;IAEA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MAAA;MAAA;MACA;QACAP;MACA;MACA;QACAA;MACA;MACA;MACA;QACA;UACAQ;UACA;QACA;UACA;YACAA;UACA;YACAA;UACA;;UACA;QACA;UACAA;UACA;MAAA;MAEA;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAX;QACAY;MACA;IACA;;IAGA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACA;QACA;QACAC;UACAb;UACAW;UACAG;UACAC;QACA;MACA;MACA;IACA;IACA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MAAA;MACA;MACA;MAAA,2BACA3B;QACA;QACA,mDACA4B,2CACA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;QACA;;QAEA;QACA;QACA;QACA;UACA;UACA;UACAC;QACA;QAEA;UACA;UACA;UACAC;QACA;QACA;QACA;QACA;QACA;UACA;YACAC;cACA;YACA;UACA;UACA;YACAC;UACA;QACA;QACA;UACAX;UACAC;UACAV;UACAqB;UACAC;UACAC;UACAZ;UACAG;UACAC;UACAS;QACA;QACA;UACAjB;QACA;QAEAM;MAAA;MAzDA;QAAA;MA0DA;MACA;IACA;IACA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACA;QACAA;UACAb;UACAW;UACAG;UACAC;QACA;MACA;MACA;IACA;;IAEA;AACA;AACA;AACA;EAHA;IAAA;IAAA,OAIA;MAAA;MACA;QACAf;MACA;MACA;QAAA;MAAA;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACAE;MACA;MACAC;MACA;QACA;MACA;QACA;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACAE;MACA;MACAC;MACA;QACA;MACA;QACA;MACA;IACA;;IAGA;AACA;AACA;AACA;AACA;EAJA;IAAA;IAAA,OAKA;MACA;MACA;MACA;MACA;MACAmB;MACA;MACAC;MACA;MACA;MACA;QACAC;QACAC;MACA;MACA;IACA;IACA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;IACA;IACA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA;MACA;IACA;;IAEA;AACA;AACA;EAFA;IAAA;IAAA,OAGA;MACA,2BAGA;QAFAvB;QACAC;MAGA;MACA;QACA;QACA;QACA;MACA;QACA;UACA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;MACA;IACA;;IAEA;AACA;AACA;AACA;EAHA;IAAA;IAAA,OAIA;MACA,oBAMA;QALAG;QACAC;QACAC;QACAX;QACAY;MAEA;MACA;MACA;QACAiB;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;MACA;MACA;MACAC;MACAC;MACA;MACA;MACA;QACA;UACAF;QACA;QACAA;MACA;MACA;MACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAAA;AAAA,KAIA;AAAA;EAAA;IAAA;EAAA;AAAA;AAOA;EAAAG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,gBAoBA;EACAC;IACAC;EACA;EACAC;EACAC;IACAvC;MACAwC;MACAC;IACA;IACAxC;MACAuC;MACAC;QACA;MACA;IACA;IACA3B;MACA0B;MACAC;IACA;IACAvC;MACAsC;MACAC;IACA;IACAtC;MACAqC;MACAC;IACA;IACArC;MACAoC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;IACAC;EACA;EACAvC;IACA;MACAwC;MACAf;MACAgB;MACAC;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA9D;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAF;MACA;MACA;IACA;EACA;EACA8D;IACA;IACA;MACA;MACA9D;MACAC;MACAC;MACAC;IACA;IACA;IACA;IACA;IACA;EACA;;EACA4D;IACA;IACAC;IACAC;MACA;MACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAD;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA,oBAGA;QAFAhE;QACAC;MAEA;QACAD;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAgE;MACA,qBAOA;QANAjE;QACAC;QACAX;QACAS;QACAK;QACA8D;MAEA;QACAxE;QACAM;QACAC;QACAX;QACA6E;QACA/D;QACA8D;MACA;IACA;IACA;AACA;AACA;AACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACAZ;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAa;MACA;MACA;MACA;IAEA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC7tCA;AAAA;AAAA;AAAA;AAAg8C,CAAgB,24CAAG,EAAC,C;;;;;;;;;;;ACAp9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/sign/uni-calendar/uni-calendar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-calendar.vue?vue&type=template&id=3e2b29b1&scoped=true&\"\nvar renderjs\nimport script from \"./uni-calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-calendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-calendar.vue?vue&type=style&index=0&id=3e2b29b1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e2b29b1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/sign/uni-calendar/uni-calendar.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-calendar.vue?vue&type=template&id=3e2b29b1&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-calendar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-calendar\">\r\n\t\t<view v-if=\"!insert&&show\" class=\"uni-calendar__mask\" :class=\"{'uni-calendar--mask-show':aniMaskShow}\" @click=\"clean\"></view>\r\n\t\t<view v-if=\"insert || show\" class=\"uni-calendar__content\" :class=\"{'uni-calendar--fixed':!insert,'uni-calendar--ani-show':aniMaskShow}\">\r\n\t\t\t<view v-if=\"!insert\" class=\"uni-calendar__header uni-calendar--fixed-top\">\r\n\t\t\t\t<view class=\"uni-calendar__header-btn-box\" @click=\"close\">\r\n\t\t\t\t\t<text class=\"uni-calendar__header-text uni-calendar--fixed-width\">{{cancelText}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-calendar__header-btn-box\" @click=\"confirm\">\r\n\t\t\t\t\t<text class=\"uni-calendar__header-text uni-calendar--fixed-width\">{{okText}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-calendar__header\">\r\n\t\t\t\t<view class=\"uni-calendar__header-btn-box\" @click.stop=\"pre\">\r\n\t\t\t\t\t<view class=\"uni-calendar__header-btn uni-calendar--left\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<picker mode=\"date\" :value=\"date\" fields=\"month\" @change=\"bindDateChange\">\r\n\t\t\t\t\t<text class=\"uni-calendar__header-text\">{{ (nowDate.year||'') +' / '+( nowDate.month||'')}}</text>\r\n\t\t\t\t</picker>\r\n\t\t\t\t<view class=\"uni-calendar__header-btn-box\" @click.stop=\"next\">\r\n\t\t\t\t\t<view class=\"uni-calendar__header-btn uni-calendar--right\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <text class=\"uni-calendar__backtoday\" @click=\"backtoday\">{{todayText}}</text> -->\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-calendar__box\">\r\n\t\t\t\t<view v-if=\"showMonth\" class=\"uni-calendar__box-bg\">\r\n\t\t\t\t\t<text class=\"uni-calendar__box-bg-text\">{{nowDate.month}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-calendar__weeks\">\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{SUNText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{monText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{TUEText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{WEDText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{THUText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{FRIText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-day\">\r\n\t\t\t\t\t\t<text class=\"uni-calendar__weeks-day-text\">{{SATText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-calendar__weeks\" v-for=\"(item,weekIndex) in weeks\" :key=\"weekIndex\">\r\n\t\t\t\t\t<view class=\"uni-calendar__weeks-item\" v-for=\"(weeks,weeksIndex) in item\" :key=\"weeksIndex\">\r\n\t\t\t\t\t\t<calendar-item class=\"uni-calendar-item--hook\" :weeks=\"weeks\" :calendar=\"calendar\" :selected=\"selected\" :lunar=\"lunar\" :backColor= \"backColor\" :fontColor= \"fontColor\" @change=\"choiceDate\"></calendar-item>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar CALENDAR = {\r\n\r\n\t\t/**\r\n\t\t\t\t* 农历1900-2100的润大小信息表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @return Hex\r\n\t\t\t\t*/\r\n\t\tlunarInfo: [0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, // 1900-1909\r\n\t\t\t0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, // 1910-1919\r\n\t\t\t0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, // 1920-1929\r\n\t\t\t0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, // 1930-1939\r\n\t\t\t0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, // 1940-1949\r\n\t\t\t0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, // 1950-1959\r\n\t\t\t0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, // 1960-1969\r\n\t\t\t0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, // 1970-1979\r\n\t\t\t0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, // 1980-1989\r\n\t\t\t0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, // 1990-1999\r\n\t\t\t0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, // 2000-2009\r\n\t\t\t0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, // 2010-2019\r\n\t\t\t0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, // 2020-2029\r\n\t\t\t0x05aa0, 0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, // 2030-2039\r\n\t\t\t0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, // 2040-2049\r\n\t\t\t/** <NAME_EMAIL>**/\r\n\t\t\t0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, // 2050-2059\r\n\t\t\t0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, // 2060-2069\r\n\t\t\t0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, // 2070-2079\r\n\t\t\t0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, // 2080-2089\r\n\t\t\t0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, // 2090-2099\r\n\t\t\t0x0d520], // 2100\r\n\r\n\t\t/**\r\n\t\t\t\t* 公历每个月份的天数普通表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @return Number\r\n\t\t\t\t*/\r\n\t\tsolarMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\r\n\r\n\t\t/**\r\n\t\t\t\t* 天干地支之天干速查表\r\n\t\t\t\t* @Array Of Property trans[\"甲\",\"乙\",\"丙\",\"丁\",\"戊\",\"己\",\"庚\",\"辛\",\"壬\",\"癸\"]\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tGan: ['\\u7532', '\\u4e59', '\\u4e19', '\\u4e01', '\\u620a', '\\u5df1', '\\u5e9a', '\\u8f9b', '\\u58ec', '\\u7678'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 天干地支之地支速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans[\"子\",\"丑\",\"寅\",\"卯\",\"辰\",\"巳\",\"午\",\"未\",\"申\",\"酉\",\"戌\",\"亥\"]\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tZhi: ['\\u5b50', '\\u4e11', '\\u5bc5', '\\u536f', '\\u8fb0', '\\u5df3', '\\u5348', '\\u672a', '\\u7533', '\\u9149', '\\u620c', '\\u4ea5'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 天干地支之地支速查表<=>生肖\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans[\"鼠\",\"牛\",\"虎\",\"兔\",\"龙\",\"蛇\",\"马\",\"羊\",\"猴\",\"鸡\",\"狗\",\"猪\"]\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tAnimals: ['\\u9f20', '\\u725b', '\\u864e', '\\u5154', '\\u9f99', '\\u86c7', '\\u9a6c', '\\u7f8a', '\\u7334', '\\u9e21', '\\u72d7', '\\u732a'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 24节气速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans[\"小寒\",\"大寒\",\"立春\",\"雨水\",\"惊蛰\",\"春分\",\"清明\",\"谷雨\",\"立夏\",\"小满\",\"芒种\",\"夏至\",\"小暑\",\"大暑\",\"立秋\",\"处暑\",\"白露\",\"秋分\",\"寒露\",\"霜降\",\"立冬\",\"小雪\",\"大雪\",\"冬至\"]\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tsolarTerm: ['\\u5c0f\\u5bd2', '\\u5927\\u5bd2', '\\u7acb\\u6625', '\\u96e8\\u6c34', '\\u60ca\\u86f0', '\\u6625\\u5206', '\\u6e05\\u660e', '\\u8c37\\u96e8', '\\u7acb\\u590f', '\\u5c0f\\u6ee1', '\\u8292\\u79cd', '\\u590f\\u81f3', '\\u5c0f\\u6691', '\\u5927\\u6691', '\\u7acb\\u79cb', '\\u5904\\u6691', '\\u767d\\u9732', '\\u79cb\\u5206', '\\u5bd2\\u9732', '\\u971c\\u964d', '\\u7acb\\u51ac', '\\u5c0f\\u96ea', '\\u5927\\u96ea', '\\u51ac\\u81f3'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 1900-2100各年的24节气日期速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @return 0x string For splice\r\n\t\t\t\t*/\r\n\t\tsTermInfo: ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',\r\n\t\t\t'97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n\t\t\t'97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',\r\n\t\t\t'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e', '97b6b97bd19801ec95f8c965cc920f',\r\n\t\t\t'97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd197c36c9210c9274c91aa',\r\n\t\t\t'97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',\r\n\t\t\t'9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bcf97c3598082c95f8e1cfcc920f',\r\n\t\t\t'97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n\t\t\t'97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',\r\n\t\t\t'97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n\t\t\t'97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd07f595b0b6fc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f',\r\n\t\t\t'97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',\r\n\t\t\t'97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\r\n\t\t\t'9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bd07f1487f595b0b0bc920fb0722',\r\n\t\t\t'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n\t\t\t'97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',\r\n\t\t\t'97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',\r\n\t\t\t'97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',\r\n\t\t\t'9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9274c91aa',\r\n\t\t\t'97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n\t\t\t'9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n\t\t\t'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '977837f0e37f149b0723b0787b0721',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c35b0b6fc9210c8dc2',\r\n\t\t\t'977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',\r\n\t\t\t'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',\r\n\t\t\t'7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',\r\n\t\t\t'977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n\t\t\t'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5',\r\n\t\t\t'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',\r\n\t\t\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',\r\n\t\t\t'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721',\r\n\t\t\t'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd',\r\n\t\t\t'7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35',\r\n\t\t\t'7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',\r\n\t\t\t'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5', '7f07e7f0e37f14998082b0787b0721',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35', '665f67f0e37f14898082b0723b02d5',\r\n\t\t\t'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66a449801e9808297c35',\r\n\t\t\t'665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',\r\n\t\t\t'7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',\r\n\t\t\t'7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35', '665f67f0e37f1489801eb072297c35',\r\n\t\t\t'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 数字转中文速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans ['日','一','二','三','四','五','六','七','八','九','十']\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tnStr1: ['\\u65e5', '\\u4e00', '\\u4e8c', '\\u4e09', '\\u56db', '\\u4e94', '\\u516d', '\\u4e03', '\\u516b', '\\u4e5d', '\\u5341'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 日期转农历称呼速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans ['初','十','廿','卅']\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tnStr2: ['\\u521d', '\\u5341', '\\u5eff', '\\u5345'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 月份转农历称呼速查表\r\n\t\t\t\t* @Array Of Property\r\n\t\t\t\t* @trans ['正','一','二','三','四','五','六','七','八','九','十','冬','腊']\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\tnStr3: ['\\u6b63', '\\u4e8c', '\\u4e09', '\\u56db', '\\u4e94', '\\u516d', '\\u4e03', '\\u516b', '\\u4e5d', '\\u5341', '\\u51ac', '\\u814a'],\r\n\r\n\t\t/**\r\n\t\t\t\t* 返回农历y年一整年的总天数\r\n\t\t\t\t* @param lunar Year\r\n\t\t\t\t* @return Number\r\n\t\t\t\t* @eg:var count = calendar.lYearDays(1987) ;//count=387\r\n\t\t\t\t*/\r\n\t\tlYearDays: function (y) {\r\n\t\t\tvar i; var sum = 348\r\n\t\t\tfor (i = 0x8000; i > 0x8; i >>= 1) { sum += (this.lunarInfo[y - 1900] & i) ? 1 : 0 }\r\n\t\t\treturn (sum + this.leapDays(y))\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\r\n\t\t\t\t* @param lunar Year\r\n\t\t\t\t* @return Number (0-12)\r\n\t\t\t\t* @eg:var leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\r\n\t\t\t\t*/\r\n\t\tleapMonth: function (y) { // 闰字编码 \\u95f0\r\n\t\t\treturn (this.lunarInfo[y - 1900] & 0xf)\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 返回农历y年闰月的天数 若该年没有闰月则返回0\r\n\t\t\t\t* @param lunar Year\r\n\t\t\t\t* @return Number (0、29、30)\r\n\t\t\t\t* @eg:var leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\r\n\t\t\t\t*/\r\n\t\tleapDays: function (y) {\r\n\t\t\tif (this.leapMonth(y)) {\r\n\t\t\t\treturn ((this.lunarInfo[y - 1900] & 0x10000) ? 30 : 29)\r\n\t\t\t}\r\n\t\t\treturn (0)\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 返回农历y年m月（非闰月）的总天数，计算m为闰月时的天数请使用leapDays方法\r\n\t\t\t\t* @param lunar Year\r\n\t\t\t\t* @return Number (-1、29、30)\r\n\t\t\t\t* @eg:var MonthDay = calendar.monthDays(1987,9) ;//MonthDay=29\r\n\t\t\t\t*/\r\n\t\tmonthDays: function (y, m) {\r\n\t\t\tif (m > 12 || m < 1) { return -1 }// 月份参数从1至12，参数错误返回-1\r\n\t\t\treturn ((this.lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29)\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 返回公历(!)y年m月的天数\r\n\t\t\t\t* @param solar Year\r\n\t\t\t\t* @return Number (-1、28、29、30、31)\r\n\t\t\t\t* @eg:var solarMonthDay = calendar.leapDays(1987) ;//solarMonthDay=30\r\n\t\t\t\t*/\r\n\t\tsolarDays: function (y, m) {\r\n\t\t\tif (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1\r\n\t\t\tvar ms = m - 1\r\n\t\t\tif (ms == 1) { // 2月份的闰平规律测算后确认返回28或29\r\n\t\t\t\treturn (((y % 4 == 0) && (y % 100 != 0) || (y % 400 == 0)) ? 29 : 28)\r\n\t\t\t} else {\r\n\t\t\t\treturn (this.solarMonth[ms])\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t * 农历年份转换为干支纪年\r\n\t\t\t * @param  lYear 农历年的年份数\r\n\t\t\t * @return Cn string\r\n\t\t\t */\r\n\t\ttoGanZhiYear: function (lYear) {\r\n\t\t\tvar ganKey = (lYear - 3) % 10\r\n\t\t\tvar zhiKey = (lYear - 3) % 12\r\n\t\t\tif (ganKey == 0) ganKey = 10// 如果余数为0则为最后一个天干\r\n\t\t\tif (zhiKey == 0) zhiKey = 12// 如果余数为0则为最后一个地支\r\n\t\t\treturn this.Gan[ganKey - 1] + this.Zhi[zhiKey - 1]\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t * 公历月、日判断所属星座\r\n\t\t\t * @param  cMonth [description]\r\n\t\t\t * @param  cDay [description]\r\n\t\t\t * @return Cn string\r\n\t\t\t */\r\n\t\ttoAstro: function (cMonth, cDay) {\r\n\t\t\tvar s = '\\u9b54\\u7faf\\u6c34\\u74f6\\u53cc\\u9c7c\\u767d\\u7f8a\\u91d1\\u725b\\u53cc\\u5b50\\u5de8\\u87f9\\u72ee\\u5b50\\u5904\\u5973\\u5929\\u79e4\\u5929\\u874e\\u5c04\\u624b\\u9b54\\u7faf'\r\n\t\t\tvar arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22]\r\n\t\t\treturn s.substr(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), 2) + '\\u5ea7'// 座\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入offset偏移量返回干支\r\n\t\t\t\t* @param offset 相对甲子的偏移量\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t*/\r\n\t\ttoGanZhi: function (offset) {\r\n\t\t\treturn this.Gan[offset % 10] + this.Zhi[offset % 12]\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入公历(!)y年获得该年第n个节气的公历日期\r\n\t\t\t\t* @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起\r\n\t\t\t\t* @return day Number\r\n\t\t\t\t* @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春\r\n\t\t\t\t*/\r\n\t\tgetTerm: function (y, n) {\r\n\t\t\tif (y < 1900 || y > 2100) { return -1 }\r\n\t\t\tif (n < 1 || n > 24) { return -1 }\r\n\t\t\tvar _table = this.sTermInfo[y - 1900]\r\n\t\t\tvar _info = [\r\n\t\t\t\tparseInt('0x' + _table.substr(0, 5)).toString(),\r\n\t\t\t\tparseInt('0x' + _table.substr(5, 5)).toString(),\r\n\t\t\t\tparseInt('0x' + _table.substr(10, 5)).toString(),\r\n\t\t\t\tparseInt('0x' + _table.substr(15, 5)).toString(),\r\n\t\t\t\tparseInt('0x' + _table.substr(20, 5)).toString(),\r\n\t\t\t\tparseInt('0x' + _table.substr(25, 5)).toString()\r\n\t\t\t]\r\n\t\t\tvar _calday = [\r\n\t\t\t\t_info[0].substr(0, 1),\r\n\t\t\t\t_info[0].substr(1, 2),\r\n\t\t\t\t_info[0].substr(3, 1),\r\n\t\t\t\t_info[0].substr(4, 2),\r\n\r\n\t\t\t\t_info[1].substr(0, 1),\r\n\t\t\t\t_info[1].substr(1, 2),\r\n\t\t\t\t_info[1].substr(3, 1),\r\n\t\t\t\t_info[1].substr(4, 2),\r\n\r\n\t\t\t\t_info[2].substr(0, 1),\r\n\t\t\t\t_info[2].substr(1, 2),\r\n\t\t\t\t_info[2].substr(3, 1),\r\n\t\t\t\t_info[2].substr(4, 2),\r\n\r\n\t\t\t\t_info[3].substr(0, 1),\r\n\t\t\t\t_info[3].substr(1, 2),\r\n\t\t\t\t_info[3].substr(3, 1),\r\n\t\t\t\t_info[3].substr(4, 2),\r\n\r\n\t\t\t\t_info[4].substr(0, 1),\r\n\t\t\t\t_info[4].substr(1, 2),\r\n\t\t\t\t_info[4].substr(3, 1),\r\n\t\t\t\t_info[4].substr(4, 2),\r\n\r\n\t\t\t\t_info[5].substr(0, 1),\r\n\t\t\t\t_info[5].substr(1, 2),\r\n\t\t\t\t_info[5].substr(3, 1),\r\n\t\t\t\t_info[5].substr(4, 2)\r\n\t\t\t]\r\n\t\t\treturn parseInt(_calday[n - 1])\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入农历数字月份返回汉语通俗表示法\r\n\t\t\t\t* @param lunar month\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t* @eg:var cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\r\n\t\t\t\t*/\r\n\t\ttoChinaMonth: function (m) { // 月 => \\u6708\r\n\t\t\tif (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1\r\n\t\t\tvar s = this.nStr3[m - 1]\r\n\t\t\ts += '\\u6708'// 加上月字\r\n\t\t\treturn s\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入农历日期数字返回汉字表示法\r\n\t\t\t\t* @param lunar day\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t* @eg:var cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\r\n\t\t\t\t*/\r\n\t\ttoChinaDay: function (d) { // 日 => \\u65e5\r\n\t\t\tvar s\r\n\t\t\tswitch (d) {\r\n\t\t\t\tcase 10:\r\n\t\t\t\t\ts = '\\u521d\\u5341'; break\r\n\t\t\t\tcase 20:\r\n\t\t\t\t\ts = '\\u4e8c\\u5341'; break\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 30:\r\n\t\t\t\t\ts = '\\u4e09\\u5341'; break\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault :\r\n\t\t\t\t\ts = this.nStr2[Math.floor(d / 10)]\r\n\t\t\t\t\ts += this.nStr1[d % 10]\r\n\t\t\t}\r\n\t\t\treturn (s)\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是“立春”\r\n\t\t\t\t* @param y year\r\n\t\t\t\t* @return Cn string\r\n\t\t\t\t* @eg:var animal = calendar.getAnimal(1987) ;//animal='兔'\r\n\t\t\t\t*/\r\n\t\tgetAnimal: function (y) {\r\n\t\t\treturn this.Animals[(y - 4) % 12]\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\r\n\t\t\t\t* @param y  solar year\r\n\t\t\t\t* @param m  solar month\r\n\t\t\t\t* @param d  solar day\r\n\t\t\t\t* @return JSON object\r\n\t\t\t\t* @eg:console.log(calendar.solar2lunar(1987,11,01));\r\n\t\t\t\t*/\r\n\t\tsolar2lunar: function (y, m, d) { // 参数区间1900.1.31~2100.12.31\r\n\t\t\t// 年份限定、上限\r\n\t\t\tif (y < 1900 || y > 2100) {\r\n\t\t\t\treturn -1// undefined转换为数字变为NaN\r\n\t\t\t}\r\n\t\t\t// 公历传参最下限\r\n\t\t\tif (y == 1900 && m == 1 && d < 31) {\r\n\t\t\t\treturn -1\r\n\t\t\t}\r\n\t\t\t// 未传参  获得当天\r\n\t\t\tif (!y) {\r\n\t\t\t\tvar objDate = new Date()\r\n\t\t\t} else {\r\n\t\t\t\tvar objDate = new Date(y, parseInt(m) - 1, d)\r\n\t\t\t}\r\n\t\t\tvar i; var leap = 0; var temp = 0\r\n\t\t\t// 修正ymd参数\r\n\t\t\tvar y = objDate.getFullYear()\r\n\t\t\tvar m = objDate.getMonth() + 1\r\n\t\t\tvar d = objDate.getDate()\r\n\t\t\tvar offset = (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) - Date.UTC(1900, 0, 31)) / 86400000\r\n\t\t\tfor (i = 1900; i < 2101 && offset > 0; i++) {\r\n\t\t\t\ttemp = this.lYearDays(i)\r\n\t\t\t\toffset -= temp\r\n\t\t\t}\r\n\t\t\tif (offset < 0) {\r\n\t\t\t\toffset += temp; i--\r\n\t\t\t}\r\n\r\n\t\t\t// 是否今天\r\n\t\t\tvar isTodayObj = new Date()\r\n\t\t\tvar isToday = false\r\n\t\t\tif (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {\r\n\t\t\t\tisToday = true\r\n\t\t\t}\r\n\t\t\t// 星期几\r\n\t\t\tvar nWeek = objDate.getDay()\r\n\t\t\tvar cWeek = this.nStr1[nWeek]\r\n\t\t\t// 数字表示周几顺应天朝周一开始的惯例\r\n\t\t\tif (nWeek == 0) {\r\n\t\t\t\tnWeek = 7\r\n\t\t\t}\r\n\t\t\t// 农历年\r\n\t\t\tvar year = i\r\n\t\t\tvar leap = this.leapMonth(i) // 闰哪个月\r\n\t\t\tvar isLeap = false\r\n\r\n\t\t\t// 效验闰月\r\n\t\t\tfor (i = 1; i < 13 && offset > 0; i++) {\r\n\t\t\t\t// 闰月\r\n\t\t\t\tif (leap > 0 && i == (leap + 1) && isLeap == false) {\r\n\t\t\t\t\t--i\r\n\t\t\t\t\tisLeap = true; temp = this.leapDays(year) // 计算农历闰月天数\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttemp = this.monthDays(year, i)// 计算农历普通月天数\r\n\t\t\t\t}\r\n\t\t\t\t// 解除闰月\r\n\t\t\t\tif (isLeap == true && i == (leap + 1)) { isLeap = false }\r\n\t\t\t\toffset -= temp\r\n\t\t\t}\r\n\t\t\t// 闰月导致数组下标重叠取反\r\n\t\t\tif (offset == 0 && leap > 0 && i == leap + 1) {\r\n\t\t\t\tif (isLeap) {\r\n\t\t\t\t\tisLeap = false\r\n\t\t\t\t} else {\r\n\t\t\t\t\tisLeap = true; --i\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (offset < 0) {\r\n\t\t\t\toffset += temp; --i\r\n\t\t\t}\r\n\t\t\t// 农历月\r\n\t\t\tvar month = i\r\n\t\t\t// 农历日\r\n\t\t\tvar day = offset + 1\r\n\t\t\t// 天干地支处理\r\n\t\t\tvar sm = m - 1\r\n\t\t\tvar gzY = this.toGanZhiYear(year)\r\n\r\n\t\t\t// 当月的两个节气\r\n\t\t\t// bugfix-2017-7-24 11:03:38 use lunar Year Param `y` Not `year`\r\n\t\t\tvar firstNode = this.getTerm(y, (m * 2 - 1))// 返回当月「节」为几日开始\r\n\t\t\tvar secondNode = this.getTerm(y, (m * 2))// 返回当月「节」为几日开始\r\n\r\n\t\t\t// 依据12节气修正干支月\r\n\t\t\tvar gzM = this.toGanZhi((y - 1900) * 12 + m + 11)\r\n\t\t\tif (d >= firstNode) {\r\n\t\t\t\tgzM = this.toGanZhi((y - 1900) * 12 + m + 12)\r\n\t\t\t}\r\n\r\n\t\t\t// 传入的日期的节气与否\r\n\t\t\tvar isTerm = false\r\n\t\t\tvar Term = null\r\n\t\t\tif (firstNode == d) {\r\n\t\t\t\tisTerm = true\r\n\t\t\t\tTerm = this.solarTerm[m * 2 - 2]\r\n\t\t\t}\r\n\t\t\tif (secondNode == d) {\r\n\t\t\t\tisTerm = true\r\n\t\t\t\tTerm = this.solarTerm[m * 2 - 1]\r\n\t\t\t}\r\n\t\t\t// 日柱 当月一日与 1900/1/1 相差天数\r\n\t\t\tvar dayCyclical = Date.UTC(y, sm, 1, 0, 0, 0, 0) / 86400000 + 25567 + 10\r\n\t\t\tvar gzD = this.toGanZhi(dayCyclical + d - 1)\r\n\t\t\t// 该日期所属的星座\r\n\t\t\tvar astro = this.toAstro(m, d)\r\n\r\n\t\t\treturn { 'lYear': year, 'lMonth': month, 'lDay': day, 'Animal': this.getAnimal(year), 'IMonthCn': (isLeap ? '\\u95f0' : '') + this.toChinaMonth(month), 'IDayCn': this.toChinaDay(day), 'cYear': y, 'cMonth': m, 'cDay': d, 'gzYear': gzY, 'gzMonth': gzM, 'gzDay': gzD, 'isToday': isToday, 'isLeap': isLeap, 'nWeek': nWeek, 'ncWeek': '\\u661f\\u671f' + cWeek, 'isTerm': isTerm, 'Term': Term, 'astro': astro }\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t\t\t* 传入农历年月日以及传入的月份是否闰月获得详细的公历、农历object信息 <=>JSON\r\n\t\t\t\t* @param y  lunar year\r\n\t\t\t\t* @param m  lunar month\r\n\t\t\t\t* @param d  lunar day\r\n\t\t\t\t* @param isLeapMonth  lunar month is leap or not.[如果是农历闰月第四个参数赋值true即可]\r\n\t\t\t\t* @return JSON object\r\n\t\t\t\t* @eg:console.log(calendar.lunar2solar(1987,9,10));\r\n\t\t\t\t*/\r\n\t\tlunar2solar: function (y, m, d, isLeapMonth) { // 参数区间1900.1.31~2100.12.1\r\n\t\t\tvar isLeapMonth = !!isLeapMonth\r\n\t\t\tvar leapOffset = 0\r\n\t\t\tvar leapMonth = this.leapMonth(y)\r\n\t\t\tvar leapDay = this.leapDays(y)\r\n\t\t\tif (isLeapMonth && (leapMonth != m)) { return -1 }// 传参要求计算该闰月公历 但该年得出的闰月与传参的月份并不同\r\n\t\t\tif (y == 2100 && m == 12 && d > 1 || y == 1900 && m == 1 && d < 31) { return -1 }// 超出了最大极限值\r\n\t\t\tvar day = this.monthDays(y, m)\r\n\t\t\tvar _day = day\r\n\t\t\t// bugFix 2016-9-25\r\n\t\t\t// if month is leap, _day use leapDays method\r\n\t\t\tif (isLeapMonth) {\r\n\t\t\t\t_day = this.leapDays(y, m)\r\n\t\t\t}\r\n\t\t\tif (y < 1900 || y > 2100 || d > _day) { return -1 }// 参数合法性效验\r\n\r\n\t\t\t// 计算农历的时间差\r\n\t\t\tvar offset = 0\r\n\t\t\tfor (var i = 1900; i < y; i++) {\r\n\t\t\t\toffset += this.lYearDays(i)\r\n\t\t\t}\r\n\t\t\tvar leap = 0; var isAdd = false\r\n\t\t\tfor (var i = 1; i < m; i++) {\r\n\t\t\t\tleap = this.leapMonth(y)\r\n\t\t\t\tif (!isAdd) { // 处理闰月\r\n\t\t\t\t\tif (leap <= i && leap > 0) {\r\n\t\t\t\t\t\toffset += this.leapDays(y); isAdd = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\toffset += this.monthDays(y, i)\r\n\t\t\t}\r\n\t\t\t// 转换闰月农历 需补充该年闰月的前一个月的时差\r\n\t\t\tif (isLeapMonth) { offset += day }\r\n\t\t\t// 1900年农历正月一日的公历时间为1900年1月30日0时0分0秒(该时间也是本农历的最开始起始点)\r\n\t\t\tvar stmap = Date.UTC(1900, 1, 30, 0, 0, 0)\r\n\t\t\tvar calObj = new Date((offset + d - 31) * 86400000 + stmap)\r\n\t\t\tvar cY = calObj.getUTCFullYear()\r\n\t\t\tvar cM = calObj.getUTCMonth() + 1\r\n\t\t\tvar cD = calObj.getUTCDate()\r\n\r\n\t\t\treturn this.solar2lunar(cY, cM, cD)\r\n\t\t}\r\n\t}\r\n\r\n\r\n\tclass Calendar {\r\n\t\tconstructor({\r\n\t\t\tdate,\r\n\t\t\tselected,\r\n\t\t\tstartDate,\r\n\t\t\tendDate,\r\n\t\t\trange\r\n\t\t} = {}) {\r\n\t\t\t// 当前日期\r\n\t\t\tthis.date = this.getDate(new Date()) // 当前初入日期\r\n\t\t\t// 打点信息\r\n\t\t\tthis.selected = selected || [];\r\n\t\t\t// 范围开始\r\n\t\t\tthis.startDate = startDate\r\n\t\t\t// 范围结束\r\n\t\t\tthis.endDate = endDate\r\n\t\t\tthis.range = range\r\n\t\t\t// 多选状态\r\n\t\t\tthis.cleanMultipleStatus()\r\n\t\t\t// 每周日期\r\n\t\t\tthis.weeks = {}\r\n\t\t\t// this._getWeek(this.date.fullDate)\r\n\t\t}\r\n\t\t/**\r\n\t\t * 设置日期\r\n\t\t * @param {Object} date\r\n\t\t */\r\n\t\tsetDate(date) {\r\n\t\t\tthis.selectDate = this.getDate(date)\r\n\t\t\tthis._getWeek(this.selectDate.fullDate)\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 清理多选状态\r\n\t\t */\r\n\t\tcleanMultipleStatus() {\r\n\t\t\tthis.multipleStatus = {\r\n\t\t\t\tbefore: '',\r\n\t\t\t\tafter: '',\r\n\t\t\t\tdata: []\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 重置开始日期\r\n\t\t */\r\n\t\tresetSatrtDate(startDate) {\r\n\t\t\t// 范围开始\r\n\t\t\tthis.startDate = startDate\r\n\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 重置结束日期\r\n\t\t */\r\n\t\tresetEndDate(endDate) {\r\n\t\t\t// 范围结束\r\n\t\t\tthis.endDate = endDate\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 获取任意时间\r\n\t\t */\r\n\t\tgetDate(date, AddDayCount = 0, str = 'day') {\r\n\t\t\tif (!date) {\r\n\t\t\t\tdate = new Date()\r\n\t\t\t}\r\n\t\t\tif (typeof date !== 'object') {\r\n\t\t\t\tdate = date.replace(/-/g, '/')\r\n\t\t\t}\r\n\t\t\tconst dd = new Date(date)\r\n\t\t\tswitch (str) {\r\n\t\t\t\tcase 'day':\r\n\t\t\t\t\tdd.setDate(dd.getDate() + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'month':\r\n\t\t\t\t\tif (dd.getDate() === 31) {\r\n\t\t\t\t\t\tdd.setDate(dd.getDate() + AddDayCount)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdd.setMonth(dd.getMonth() + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'year':\r\n\t\t\t\t\tdd.setFullYear(dd.getFullYear() + AddDayCount) // 获取AddDayCount天后的日期\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t\tconst y = dd.getFullYear()\r\n\t\t\tconst m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1 // 获取当前月份的日期，不足10补0\r\n\t\t\tconst d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate() // 获取当前几号，不足10补0\r\n\t\t\treturn {\r\n\t\t\t\tfullDate: y + '-' + m + '-' + d,\r\n\t\t\t\tyear: y,\r\n\t\t\t\tmonth: m,\r\n\t\t\t\tdate: d,\r\n\t\t\t\tday: dd.getDay()\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t/**\r\n\t\t * 获取上月剩余天数\r\n\t\t */\r\n\t\t_getLastMonthDays(firstDay, full) {\r\n\t\t\tlet dateArr = []\r\n\t\t\tfor (let i = firstDay; i > 0; i--) {\r\n\t\t\t\tconst beforeDate = new Date(full.year, full.month - 1, -i + 1).getDate()\r\n\t\t\t\tdateArr.push({\r\n\t\t\t\t\tdate: beforeDate,\r\n\t\t\t\t\tmonth: full.month - 1,\r\n\t\t\t\t\tlunar: this.getlunar(full.year, full.month - 1, beforeDate),\r\n\t\t\t\t\tdisable: true\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn dateArr\r\n\t\t}\r\n\t\t/**\r\n\t\t * 获取本月天数\r\n\t\t */\r\n\t\t_currentMonthDys(dateData, full) {\r\n\t\t\tlet dateArr = []\r\n\t\t\tlet fullDate = this.date.fullDate\r\n\t\t\tfor (let i = 1; i <= dateData; i++) {\r\n\t\t\t\tlet isinfo = false\r\n\t\t\t\tlet nowDate = full.year + '-' + (full.month < 10 ?\r\n\t\t\t\t\tfull.month : full.month) + '-' + (i < 10 ?\r\n\t\t\t\t\t'0' + i : i)\r\n\t\t\t\t// 是否今天\r\n\t\t\t\tlet isDay = fullDate === nowDate\r\n\t\t\t\t// 获取打点信息\r\n\t\t\t\tlet info = this.selected && this.selected.find((item) => {\r\n\t\t\t\t\tif (this.dateEqual(nowDate, item.date)) {\r\n\t\t\t\t\t\treturn item\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 日期禁用\r\n\t\t\t\tlet disableBefore = true\r\n\t\t\t\tlet disableAfter = true\r\n\t\t\t\tif (this.startDate) {\r\n\t\t\t\t\t// let dateCompBefore = this.dateCompare(this.startDate, fullDate)\r\n\t\t\t\t\t// disableBefore = this.dateCompare(dateCompBefore ? this.startDate : fullDate, nowDate)\r\n\t\t\t\t\tdisableBefore = this.dateCompare(this.startDate, nowDate)\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.endDate) {\r\n\t\t\t\t\t// let dateCompAfter = this.dateCompare(fullDate, this.endDate)\r\n\t\t\t\t\t// disableAfter = this.dateCompare(nowDate, dateCompAfter ? this.endDate : fullDate)\r\n\t\t\t\t\tdisableAfter = this.dateCompare(nowDate, this.endDate)\r\n\t\t\t\t}\r\n\t\t\t\tlet multiples = this.multipleStatus.data\r\n\t\t\t\tlet checked = false\r\n\t\t\t\tlet multiplesStatus = -1\r\n\t\t\t\tif (this.range) {\r\n\t\t\t\t\tif (multiples) {\r\n\t\t\t\t\t\tmultiplesStatus = multiples.findIndex((item) => {\r\n\t\t\t\t\t\t\treturn this.dateEqual(item, nowDate)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (multiplesStatus !== -1) {\r\n\t\t\t\t\t\tchecked = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tfullDate: nowDate,\r\n\t\t\t\t\tyear: full.year,\r\n\t\t\t\t\tdate: i,\r\n\t\t\t\t\tmultiple: this.range ? checked : false,\r\n\t\t\t\t\tbeforeMultiple: this.dateEqual(this.multipleStatus.before, nowDate),\r\n\t\t\t\t\tafterMultiple: this.dateEqual(this.multipleStatus.after, nowDate),\r\n\t\t\t\t\tmonth: full.month,\r\n\t\t\t\t\tlunar: this.getlunar(full.year, full.month, i),\r\n\t\t\t\t\tdisable: !(disableBefore && disableAfter),\r\n\t\t\t\t\tisDay\r\n\t\t\t\t}\r\n\t\t\t\tif (info) {\r\n\t\t\t\t\tdata.extraInfo = info\r\n\t\t\t\t}\r\n\r\n\t\t\t\tdateArr.push(data)\r\n\t\t\t}\r\n\t\t\treturn dateArr\r\n\t\t}\r\n\t\t/**\r\n\t\t * 获取下月天数\r\n\t\t */\r\n\t\t_getNextMonthDays(surplus, full) {\r\n\t\t\tlet dateArr = []\r\n\t\t\tfor (let i = 1; i < surplus + 1; i++) {\r\n\t\t\t\tdateArr.push({\r\n\t\t\t\t\tdate: i,\r\n\t\t\t\t\tmonth: Number(full.month) + 1,\r\n\t\t\t\t\tlunar: this.getlunar(full.year, Number(full.month) + 1, i),\r\n\t\t\t\t\tdisable: true\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn dateArr\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 获取当前日期详情\r\n\t\t * @param {Object} date\r\n\t\t */\r\n\t\tgetInfo(date) {\r\n\t\t\tif (!date) {\r\n\t\t\t\tdate = new Date()\r\n\t\t\t}\r\n\t\t\tconst dateInfo = this.canlender.find(item => item.fullDate === this.getDate(date).fullDate)\r\n\t\t\treturn dateInfo\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 比较时间大小\r\n\t\t */\r\n\t\tdateCompare(startDate, endDate) {\r\n\t\t\t// 计算截止时间\r\n\t\t\tstartDate = new Date(startDate.replace('-', '/').replace('-', '/'))\r\n\t\t\t// 计算详细项的截止时间\r\n\t\t\tendDate = new Date(endDate.replace('-', '/').replace('-', '/'))\r\n\t\t\tif (startDate <= endDate) {\r\n\t\t\t\treturn true\r\n\t\t\t} else {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 比较时间是否相等\r\n\t\t */\r\n\t\tdateEqual(before, after) {\r\n\t\t\t// 计算截止时间\r\n\t\t\tbefore = new Date(before.replace('-', '/').replace('-', '/'))\r\n\t\t\t// 计算详细项的截止时间\r\n\t\t\tafter = new Date(after.replace('-', '/').replace('-', '/'))\r\n\t\t\tif (before.getTime() - after.getTime() === 0) {\r\n\t\t\t\treturn true\r\n\t\t\t} else {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t/**\r\n\t\t * 获取日期范围内所有日期\r\n\t\t * @param {Object} begin\r\n\t\t * @param {Object} end\r\n\t\t */\r\n\t\tgeDateAll(begin, end) {\r\n\t\t\tvar arr = []\r\n\t\t\tvar ab = begin.split('-')\r\n\t\t\tvar ae = end.split('-')\r\n\t\t\tvar db = new Date()\r\n\t\t\tdb.setFullYear(ab[0], ab[1] - 1, ab[2])\r\n\t\t\tvar de = new Date()\r\n\t\t\tde.setFullYear(ae[0], ae[1] - 1, ae[2])\r\n\t\t\tvar unixDb = db.getTime() - 24 * 60 * 60 * 1000\r\n\t\t\tvar unixDe = de.getTime() - 24 * 60 * 60 * 1000\r\n\t\t\tfor (var k = unixDb; k <= unixDe;) {\r\n\t\t\t\tk = k + 24 * 60 * 60 * 1000\r\n\t\t\t\tarr.push(this.getDate(new Date(parseInt(k))).fullDate)\r\n\t\t\t}\r\n\t\t\treturn arr\r\n\t\t}\r\n\t\t/**\r\n\t\t * 计算阴历日期显示\r\n\t\t */\r\n\t\tgetlunar(year, month, date) {\r\n\t\t\treturn CALENDAR.solar2lunar(year, month, date)\r\n\t\t}\r\n\t\t/**\r\n\t\t * 设置打点\r\n\t\t */\r\n\t\tsetSelectInfo(data, value) {\r\n\t\t\tthis.selected = value\r\n\t\t\tthis._getWeek(data)\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t *  获取多选状态\r\n\t\t */\r\n\t\tsetMultiple(fullDate) {\r\n\t\t\tlet {\r\n\t\t\t\tbefore,\r\n\t\t\t\tafter\r\n\t\t\t} = this.multipleStatus\r\n\r\n\t\t\tif (!this.range) return\r\n\t\t\tif (before && after) {\r\n\t\t\t\tthis.multipleStatus.before = ''\r\n\t\t\t\tthis.multipleStatus.after = ''\r\n\t\t\t\tthis.multipleStatus.data = []\r\n\t\t\t} else {\r\n\t\t\t\tif (!before) {\r\n\t\t\t\t\tthis.multipleStatus.before = fullDate\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.multipleStatus.after = fullDate\r\n\t\t\t\t\tif (this.dateCompare(this.multipleStatus.before, this.multipleStatus.after)) {\r\n\t\t\t\t\t\tthis.multipleStatus.data = this.geDateAll(this.multipleStatus.before, this.multipleStatus.after);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.multipleStatus.data = this.geDateAll(this.multipleStatus.after, this.multipleStatus.before);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis._getWeek(fullDate)\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * 获取每周数据\r\n\t\t * @param {Object} dateData\r\n\t\t */\r\n\t\t_getWeek(dateData) {\r\n\t\t\tconst {\r\n\t\t\t\tfullDate,\r\n\t\t\t\tyear,\r\n\t\t\t\tmonth,\r\n\t\t\t\tdate,\r\n\t\t\t\tday\r\n\t\t\t} = this.getDate(dateData)\r\n\t\t\tlet firstDay = new Date(year, month - 1, 1).getDay()\r\n\t\t\tlet currentDay = new Date(year, month, 0).getDate()\r\n\t\t\tlet dates = {\r\n\t\t\t\tlastMonthDays: this._getLastMonthDays(firstDay, this.getDate(dateData)), // 上个月末尾几天\r\n\t\t\t\tcurrentMonthDys: this._currentMonthDys(currentDay, this.getDate(dateData)), // 本月天数\r\n\t\t\t\tnextMonthDays: [], // 下个月开始几天\r\n\t\t\t\tweeks: []\r\n\t\t\t}\r\n\t\t\tlet canlender = []\r\n\t\t\tconst surplus = 42 - (dates.lastMonthDays.length + dates.currentMonthDys.length)\r\n\t\t\tdates.nextMonthDays = this._getNextMonthDays(surplus, this.getDate(dateData))\r\n\t\t\tcanlender = canlender.concat(dates.lastMonthDays, dates.currentMonthDys, dates.nextMonthDays)\r\n\t\t\tlet weeks = {}\r\n\t\t\t// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天\r\n\t\t\tfor (let i = 0; i < canlender.length; i++) {\r\n\t\t\t\tif (i % 7 === 0) {\r\n\t\t\t\t\tweeks[parseInt(i / 7)] = new Array(7)\r\n\t\t\t\t}\r\n\t\t\t\tweeks[parseInt(i / 7)][i % 7] = canlender[i]\r\n\t\t\t}\r\n\t\t\tthis.canlender = canlender\r\n\t\t\tthis.weeks = weeks\r\n\t\t}\r\n\r\n\t\t//静态方法\r\n\t\t// static init(date) {\r\n\t\t// \tif (!this.instance) {\r\n\t\t// \t\tthis.instance = new Calendar(date);\r\n\t\t// \t}\r\n\t\t// \treturn this.instance;\r\n\t\t// }\r\n\t}\r\n\r\n\t\r\n\t//import Calendar from './util.js';\r\n\r\n\timport calendarItem from './uni-calendar-item.vue'\r\n\timport {\r\n\tinitVueI18n\r\n\t} from '@dcloudio/uni-i18n'\r\n\timport messages from './i18n/index.js'\r\n\tconst {\tt\t} = initVueI18n(messages)\r\n\t/**\r\n\t * Calendar 日历\r\n\t * @description 日历组件可以查看日期，选择任意范围内的日期，打点操作。常用场景如：酒店日期预订、火车机票选择购买日期、上下班打卡等\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=56\r\n\t * @property {String} date 自定义当前时间，默认为今天\r\n\t * @property {Boolean} lunar 显示农历\r\n\t * @property {String} startDate 日期选择范围-开始日期\r\n\t * @property {String} endDate 日期选择范围-结束日期\r\n\t * @property {Boolean} range 范围选择\r\n\t * @property {Boolean} insert = [true|false] 插入模式,默认为false\r\n\t * \t@value true 弹窗模式\r\n\t * \t@value false 插入模式\r\n\t * @property {Boolean} clearDate = [true|false] 弹窗模式是否清空上次选择内容\r\n\t * @property {Array} selected 打点，期待格式[{date: '2019-06-27', info: '签到', data: { custom: '自定义信息', name: '自定义消息头',xxx:xxx... }}]\r\n\t * @property {Boolean} showMonth 是否选择月份为背景\r\n\t * @event {Function} change 日期改变，`insert :ture` 时生效\r\n\t * @event {Function} confirm 确认选择`insert :false` 时生效\r\n\t * @event {Function} monthSwitch 切换月份时触发\r\n\t * @example <uni-calendar :insert=\"true\":lunar=\"true\" :start-date=\"'2019-3-2'\":end-date=\"'2019-5-20'\"@change=\"change\" />\r\n\t */\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tcalendarItem\r\n\t\t},\r\n\t\temits:['close','confirm','change','monthSwitch'],\r\n\t\tprops: {\r\n\t\t\tdate: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tselected: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlunar: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tstartDate: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tendDate: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\trange: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tinsert: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowMonth: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tclearDate: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tbackColor: \"\",\r\n\t\t\tfontColor: \"\"\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: false,\r\n\t\t\t\tweeks: [],\r\n\t\t\t\tcalendar: {},\r\n\t\t\t\tnowDate: '',\r\n\t\t\t\taniMaskShow: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\t/**\r\n\t\t\t * for i18n\r\n\t\t\t */\r\n\r\n\t\t\tokText() {\r\n\t\t\t\treturn t(\"uni-calender.ok\")\r\n\t\t\t},\r\n\t\t\tcancelText() {\r\n\t\t\t\treturn t(\"uni-calender.cancel\")\r\n\t\t\t},\r\n\t\t\ttodayText() {\r\n\t\t\t\treturn t(\"uni-calender.today\")\r\n\t\t\t},\r\n\t\t\tmonText() {\r\n\t\t\t\treturn t(\"uni-calender.MON\")\r\n\t\t\t},\r\n\t\t\tTUEText() {\r\n\t\t\t\treturn t(\"uni-calender.TUE\")\r\n\t\t\t},\r\n\t\t\tWEDText() {\r\n\t\t\t\treturn t(\"uni-calender.WED\")\r\n\t\t\t},\r\n\t\t\tTHUText() {\r\n\t\t\t\treturn t(\"uni-calender.THU\")\r\n\t\t\t},\r\n\t\t\tFRIText() {\r\n\t\t\t\treturn t(\"uni-calender.FRI\")\r\n\t\t\t},\r\n\t\t\tSATText() {\r\n\t\t\t\treturn t(\"uni-calender.SAT\")\r\n\t\t\t},\r\n\t\t\tSUNText() {\r\n\t\t\t\treturn t(\"uni-calender.SUN\")\r\n\t\t\t},\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdate(newVal) {\r\n\t\t\t\t// this.cale.setDate(newVal)\r\n\t\t\t\tthis.init(newVal)\r\n\t\t\t},\r\n\t\t\tstartDate(val){\r\n\t\t\t\tthis.cale.resetSatrtDate(val)\n\t\t\t\tthis.cale.setDate(this.nowDate.fullDate)\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t},\r\n\t\t\tendDate(val){\r\n\t\t\t\tthis.cale.resetEndDate(val)\n\t\t\t\tthis.cale.setDate(this.nowDate.fullDate)\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t},\r\n\t\t\tselected(newVal) {\r\n\t\t\t\tthis.cale.setSelectInfo(this.nowDate.fullDate, newVal)\r\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 获取日历方法实例\r\n\t\t\tthis.cale = new Calendar({\r\n\t\t\t\t// date: new Date(),\r\n\t\t\t\tselected: this.selected,\r\n\t\t\t\tstartDate: this.startDate,\r\n\t\t\t\tendDate: this.endDate,\r\n\t\t\t\trange: this.range,\r\n\t\t\t})\r\n\t\t\t// 选中某一天\r\n\t\t\t// this.cale.setDate(this.date)\r\n\t\t\tthis.init(this.date)\r\n\t\t\t// this.setDay\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 取消穿透\r\n\t\t\tclean() {},\r\n\t\t\tbindDateChange(e) {\r\n\t\t\t\tconst value = e.detail.value + '-1'\r\n\t\t\t\tconsole.log(this.cale.getDate(value));\r\n\t\t\t\tthis.init(value)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 初始化日期显示\r\n\t\t\t * @param {Object} date\r\n\t\t\t */\r\n\t\t\tinit(date) {\r\n\t\t\t\tthis.cale.setDate(date)\r\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t\tthis.nowDate = this.calendar = this.cale.getInfo(date)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开日历弹窗\r\n\t\t\t */\r\n\t\t\topen() {\r\n\t\t\t\t// 弹窗模式并且清理数据\r\n\t\t\t\tif (this.clearDate && !this.insert) {\r\n\t\t\t\t\tthis.cale.cleanMultipleStatus()\r\n\t\t\t\t\t// this.cale.setDate(this.date)\r\n\t\t\t\t\tthis.init(this.date)\r\n\t\t\t\t}\r\n\t\t\t\tthis.show = true\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.aniMaskShow = true\r\n\t\t\t\t\t}, 50)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 关闭日历弹窗\r\n\t\t\t */\r\n\t\t\tclose() {\r\n\t\t\t\tthis.aniMaskShow = false\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.show = false\r\n\t\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t\t}, 300)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 确认按钮\r\n\t\t\t */\r\n\t\t\tconfirm() {\r\n\t\t\t\tthis.setEmit('confirm')\r\n\t\t\t\tthis.close()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 变化触发\r\n\t\t\t */\r\n\t\t\tchange() {\r\n\t\t\t\tif (!this.insert) return\r\n\t\t\t\tthis.setEmit('change')\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 选择月份触发\r\n\t\t\t */\r\n\t\t\tmonthSwitch() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tyear,\r\n\t\t\t\t\tmonth\r\n\t\t\t\t} = this.nowDate\r\n\t\t\t\tthis.$emit('monthSwitch', {\r\n\t\t\t\t\tyear,\r\n\t\t\t\t\tmonth: Number(month)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 派发事件\r\n\t\t\t * @param {Object} name\r\n\t\t\t */\r\n\t\t\tsetEmit(name) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tyear,\r\n\t\t\t\t\tmonth,\r\n\t\t\t\t\tdate,\r\n\t\t\t\t\tfullDate,\r\n\t\t\t\t\tlunar,\r\n\t\t\t\t\textraInfo\r\n\t\t\t\t} = this.calendar\r\n\t\t\t\tthis.$emit(name, {\r\n\t\t\t\t\trange: this.cale.multipleStatus,\r\n\t\t\t\t\tyear,\r\n\t\t\t\t\tmonth,\r\n\t\t\t\t\tdate,\r\n\t\t\t\t\tfulldate: fullDate,\r\n\t\t\t\t\tlunar,\r\n\t\t\t\t\textraInfo: extraInfo || {}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 选择天触发\r\n\t\t\t * @param {Object} weeks\r\n\t\t\t */\r\n\t\t\tchoiceDate(weeks) {\r\n\t\t\t\tif (weeks.disable) return\r\n\t\t\t\tthis.calendar = weeks\r\n\t\t\t\t// 设置多选\r\n\t\t\t\tthis.cale.setMultiple(this.calendar.fullDate)\r\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t\tthis.change()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 回到今天\r\n\t\t\t */\r\n\t\t\tbacktoday() {\r\n\t\t\t\tconsole.log(this.cale.getDate(new Date()).fullDate);\r\n\t\t\t\tlet date = this.cale.getDate(new Date()).fullDate\r\n\t\t\t\t// this.cale.setDate(date)\r\n\t\t\t\tthis.init(date)\r\n\t\t\t\tthis.change()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 上个月\r\n\t\t\t */\r\n\t\t\tpre() {\r\n\t\t\t\tconst preDate = this.cale.getDate(this.nowDate.fullDate, -1, 'month').fullDate\r\n\t\t\t\tthis.setDate(preDate)\r\n\t\t\t\tthis.monthSwitch()\r\n\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 下个月\r\n\t\t\t */\r\n\t\t\tnext() {\r\n\t\t\t\tconst nextDate = this.cale.getDate(this.nowDate.fullDate, +1, 'month').fullDate\r\n\t\t\t\tthis.setDate(nextDate)\r\n\t\t\t\tthis.monthSwitch()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置日期\r\n\t\t\t * @param {Object} date\r\n\t\t\t */\r\n\t\t\tsetDate(date) {\r\n\t\t\t\tthis.cale.setDate(date)\r\n\t\t\t\tthis.weeks = this.cale.weeks\r\n\t\t\t\tthis.nowDate = this.cale.getInfo(date)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.uni-calendar {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.uni-calendar__mask {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbackground-color: $uni-bg-color-mask;\r\n\t\ttransition-property: opacity;\r\n\t\ttransition-duration: 0.3s;\r\n\t\topacity: 0;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 99;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-calendar--mask-show {\r\n\t\topacity: 1\r\n\t}\r\n\r\n\t.uni-calendar--fixed {\r\n\t\tposition: fixed;\r\n\t\tbottom: calc(var(--window-bottom));\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttransition-property: transform;\r\n\t\ttransition-duration: 0.3s;\r\n\t\ttransform: translateY(460px);\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 99;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-calendar--ani-show {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\r\n\t.uni-calendar__content {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.uni-calendar__header {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 50px;\r\n\t}\r\n\r\n\t.uni-calendar--fixed-top {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-top-color: $uni-border-color;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 1px;\r\n\t}\r\n\r\n\t.uni-calendar--fixed-width {\r\n\t\twidth: 50px;\r\n\t\t// padding: 0 15px;\r\n\t}\r\n\r\n\t.uni-calendar__backtoday {\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 25rpx;\r\n\t\tpadding: 0 5px;\r\n\t\tpadding-left: 10px;\r\n\t\theight: 25px;\r\n\t\tline-height: 25px;\r\n\t\tfont-size: 12px;\r\n\t\tborder-top-left-radius: 25px;\r\n\t\tborder-bottom-left-radius: 25px;\r\n\t\tcolor: $uni-text-color;\r\n\t\tbackground-color: $uni-bg-color-hover;\r\n\t}\r\n\r\n\t.uni-calendar__header-text {\r\n\t\ttext-align: center;\r\n\t\twidth: 100px;\r\n\t\tfont-size: $uni-font-size-base;\r\n\t\tcolor: $uni-text-color;\r\n\t}\r\n\r\n\t.uni-calendar__header-btn-box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t}\r\n\r\n\t.uni-calendar__header-btn {\r\n\t\twidth: 10px;\r\n\t\theight: 10px;\r\n\t\tborder-left-color: $uni-text-color-placeholder;\r\n\t\tborder-left-style: solid;\r\n\t\tborder-left-width: 2px;\r\n\t\tborder-top-color: $uni-color-subtitle;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 2px;\r\n\t}\r\n\r\n\t.uni-calendar--left {\r\n\t\ttransform: rotate(-45deg);\r\n\t}\r\n\r\n\t.uni-calendar--right {\r\n\t\ttransform: rotate(135deg);\r\n\t}\r\n\r\n\r\n\t.uni-calendar__weeks {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-calendar__weeks-item {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.uni-calendar__weeks-day {\r\n\t\tflex: 1;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 45px;\r\n\t\tborder-bottom-color: #F5F5F5;\r\n\t\tborder-bottom-style: solid;\r\n\t\tborder-bottom-width: 1px;\r\n\t}\r\n\r\n\t.uni-calendar__weeks-day-text {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.uni-calendar__box {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.uni-calendar__box-bg {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.uni-calendar__box-bg-text {\r\n\t\tfont-size: 200px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: $uni-text-color-grey;\r\n\t\topacity: 0.1;\r\n\t\ttext-align: center;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tline-height: 1;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-calendar.vue?vue&type=style&index=0&id=3e2b29b1&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-calendar.vue?vue&type=style&index=0&id=3e2b29b1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102565\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}