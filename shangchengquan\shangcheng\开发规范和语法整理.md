# 开发规范和语法整理

## 📋 Coze系统页面状态说明

### ✅ 正常可用的页面
- **API配置**: `http://localhost/?s=/coze/config` - 配置Coze API密钥和基础设置
- **工作流管理**: `http://localhost/?s=/coze/workflow` - 管理工作流，支持直接测试执行
- **执行日志**: `http://localhost/?s=/coze/workflowLog` - 查看工作流执行记录，支持状态查询（已修复样式问题）
- **工作流演示**: `http://localhost/?s=/coze/workflowDemo` - 工作流演示页面

### ❌ 已移除的页面（解决报错问题）
- ~~API日志~~: `http://localhost/?s=/coze/logs` - 已移除（功能重复）
- ~~响应日志~~: `http://localhost/?s=/coze/responseLogs` - 已移除（未完全实现）
- ~~对话列表~~: `http://localhost/?s=/coze/conversations` - 已移除（当前系统不使用对话功能）

### 🎯 核心功能
1. **工作流管理** - 创建、编辑、删除工作流配置
2. **直接测试执行** - 在工作流列表页面直接测试同步/异步执行
3. **参数配置** - 支持JSON格式的默认参数配置
4. **状态查询** - 支持手动查询异步执行状态
5. **执行日志** - 完整的执行记录和结果查看

---

## 🏗️ 项目结构规范

### 后端控制器结构
```
app/controller/
├── Xxx.php          # 后台管理控制器 (extends Common)
└── ApiXxx.php       # 前端接口控制器 (extends ApiCommon)
```

### 视图文件结构
```
app/home/<USER>/
├── index.html       # 列表页面
├── edit.html        # 编辑页面
└── detail.html      # 详情页面
```

### 数据库表前缀
- 统一使用前缀：`ddwx_`

## 📝 代码开发标准

### 1. 后端控制器规范

#### 基础结构
```php
<?php
namespace app\controller;
use think\facade\Db;
use think\facade\View;

class ModuleName extends Common
{
    public function index()
    {
        define('aid', $this->aid);
        
        if(request()->isAjax()){
            // AJAX数据处理
            return json(['code'=>0,'msg'=>'','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
}
```

#### API控制器规范
```php
<?php
namespace app\controller;
use think\facade\Db;

class ApiModuleName extends ApiCommon
{
    public function index()
    {
        $mid = $this->mid; // 用户ID
        
        // 业务逻辑
        
        return json(['code'=>1,'msg'=>'操作成功','data'=>$data]);
    }
}
```

### 2. 数据库操作规范

#### 查询操作
```php
// 列表查询
$list = Db::name('table_name')
    ->where($where)
    ->field('id,name,status,create_time')
    ->page($page, $limit)
    ->order('id desc')
    ->select()
    ->toArray();

// 单条查询
$info = Db::name('table_name')
    ->where('id', $id)
    ->find();
```

#### 数据操作
```php
// 新增
$data = [
    'aid' => aid,
    'name' => $name,
    'create_time' => time(),
    'update_time' => time()
];
$result = Db::name('table_name')->insert($data);

// 更新
$data['update_time'] = time();
$result = Db::name('table_name')
    ->where('aid', aid)
    ->where('id', $id)
    ->update($data);

// 删除
$result = Db::name('table_name')
    ->where('aid', aid)
    ->where('id', $id)
    ->delete();
```

### 3. 返回数据规范

#### 后台AJAX返回
```php
// 成功
return json(['status'=>1, 'msg'=>'操作成功', 'url'=>(string)url('index')]);

// 失败
return json(['status'=>0, 'msg'=>'操作失败']);

// 列表数据
return json(['code'=>0, 'msg'=>'', 'count'=>$count, 'data'=>$data]);
```

#### 前端API返回
```php
// 成功
return json(['code'=>1, 'msg'=>'操作成功', 'data'=>$data]);

// 失败
return json(['code'=>0, 'msg'=>'操作失败']);
```

### 4. 视图模板规范

#### HTML结构
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>页面标题</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    {include file="public/css"/}
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-card layui-col-md12">
                <div class="layui-card-header">
                    <i class="fa fa-list"></i> 页面标题
                </div>
                <div class="layui-card-body" pad15>
                    <!-- 页面内容 -->
                </div>
            </div>
        </div>
    </div>
    {include file="public/js"/}
    <script>
    // JavaScript代码
    </script>
</body>
</html>
```

#### Layui表格配置
```javascript
layui.use(['table', 'form'], function(){
    var table = layui.table;
    var form = layui.form;
    
    window.tableIns = table.render({
        elem: '#LAY-user-manage',
        url: '{:url("index")}',
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '名称'},
            {field: 'status_text', title: '状态', width: 100},
            {field: 'create_time_text', title: '创建时间', width: 180},
            {title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
        ]],
        page: true,
        limit: 20,
        height: 'full-220',
        text: {none: '暂无相关数据'}
    });
});
```

### 5. 表单提交规范

#### 表单HTML
```html
<form class="layui-form" lay-filter="component-form-group">
    <div class="layui-form-item">
        <label class="layui-form-label">字段名：</label>
        <div class="layui-input-inline" style="width:400px">
            <input type="text" name="info[field]" lay-verify="required" lay-verType="tips" class="layui-input" value="{$info.field}" placeholder="请输入内容">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="formSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeself()">取消</button>
        </div>
    </div>
</form>
```

#### 表单提交处理
```javascript
form.on('submit(formSubmit)', function(data){
    var field = data.field;
    var index = layer.load();
    $.post("{:url('save')}", field, function(data){
        layer.close(index);
        if(data.status==1){
            layer.msg(data.msg, {icon: 1});
            setTimeout(function(){
                parent.layer.close(parent.layer.getFrameIndex(window.name));
                parent.tableIns.reload();
            }, 1000);
        }else{
            layer.msg(data.msg, {icon: 2});
        }
    });
    return false;
});
```

### 6. 操作日志规范

```php
// 在关键操作位置添加日志
\app\common\System::plog('操作描述');

// 示例
\app\common\System::plog('新增工作流');
\app\common\System::plog('编辑工作流');
\app\common\System::plog('删除工作流');
```

### 7. 菜单配置规范

在 `app/common/Menu.php` 中添加菜单：

```php
$component_module[] = [
    'name' => '模块名称',
    'path' => 'controller/method',
    'authdata' => 'Controller/*,controller/method'
];
```

## 🎨 前端开发规范

### 1. 颜色配置
```html
<!-- 动态颜色 -->
<view :style="{color:t('color1')}">文本</view>

<!-- 渐变背景 -->
<view :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">内容</view>
```

### 2. 图片资源引用
```html
<!-- 使用域名前缀 -->
<image :src="pre_url+'/static/img/icon.png'"/>
```

### 3. 接口调用规范
```javascript
// 统一接口调用格式
this.$http.post('api/method', data).then(res => {
    if(res.code == 1){
        // 成功处理
    }else{
        // 失败处理
    }
});
```

## 🔧 开发工具和命令

### Git提交规范
```bash
git add .
git commit -m "功能描述

- 具体修改点1
- 具体修改点2
- 具体修改点3"
```

### 数据库操作
```sql
-- 表结构修改
ALTER TABLE `ddwx_table_name` 
ADD COLUMN `field_name` VARCHAR(255) COMMENT '字段说明' AFTER `existing_field`;

-- 数据更新
UPDATE `ddwx_table_name` SET `field` = 'value' WHERE `condition` = 'value';
```

## 🎨 列表页面样式修复标准

### 问题识别和解决思路

当遇到列表页面样式问题时，按以下步骤进行诊断和修复：

#### 1. 问题诊断（5-7个可能原因）
- **页面结构问题**：layui-card结构不标准，嵌套层级过深
- **搜索区域样式**：未使用标准的layui-form结构和样式类
- **工具栏按钮样式**：按钮样式不统一，未使用layuiadmin-btn-list
- **表格配置问题**：列宽度设置不合理，模板函数过于复杂
- **JavaScript逻辑错误**：事件处理逻辑有误，缺少标准变量
- **操作按钮样式**：未使用统一的table-btn样式
- **响应式布局问题**：在不同屏幕尺寸下显示异常

#### 2. 修复标准（1-2个核心解决方案）
1. **页面结构标准化**：参考现有正常列表页面（如article/index.html）调整结构
2. **JavaScript逻辑优化**：修复事件处理逻辑，添加标准变量和函数

#### 3. 具体修复步骤
```html
<!-- 标准页面结构 -->
<div class="layui-fluid">
  <div class="layui-row layui-col-space15">
    <div class="layui-card layui-col-md12">
      <div class="layui-card-header"><i class="fa fa-list"></i> 页面标题</div>
      <div class="layui-card-body" pad15>
        <!-- 工具栏 -->
        <div class="layui-col-md4" style="padding-bottom:10px">
          <button class="layui-btn layui-btn-primary layuiadmin-btn-list">按钮</button>
        </div>
        <!-- 搜索区域 -->
        <div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
          <div class="layui-inline layuiadmin-input-useradmin">
            <label class="layui-form-label" style="width:60px">标签</label>
            <div class="layui-input-block" style="width:120px;margin-left:90px">
              <input type="text" name="field" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <button class="layui-btn layuiadmin-btn-replys" lay-submit="">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
          </div>
        </div>
        <!-- 表格 -->
        <div class="layui-col-md12">
          <table id="tabledata" lay-filter="tabledata"></table>
        </div>
      </div>
    </div>
  </div>
</div>
```

```javascript
// 标准JavaScript结构
var table = layui.table;
var datawhere = {};

// 表格渲染
var tableIns = table.render({
  elem: '#tabledata',
  url: '{:url("method")}',
  page: true,
  cols: [[
    {type:"checkbox"},
    {field: 'id', title: 'ID', sort: true, width:80},
    {field: 'operation', title: '操作', templet:function(d){
      var html = '';
      html += '<button class="table-btn" onclick="method('+d.id+')">操作</button>';
      return html;
    }, width:150}
  ]]
});

// 标准排序处理
table.on('sort(tabledata)', function(obj){
  datawhere.field = obj.field;
  datawhere.order = obj.type;
  tableIns.reload({
    initSort: obj,
    where: datawhere
  });
});

// 标准搜索处理
layui.form.on('submit(LAY-app-search)', function(obj){
  var field = obj.field;
  var olddatawhere = datawhere;
  datawhere = field;
  datawhere.field = olddatawhere.field;
  datawhere.order = olddatawhere.order;
  tableIns.reload({
    where: datawhere,
    page: {curr: 1}
  });
});
```

## 📋 注意事项

1. **代码一致性**：严格按照现有代码结构和风格编写
2. **字段验证**：所有用户输入必须进行验证和过滤
3. **错误处理**：完善的异常捕获和错误提示
4. **安全考虑**：防止SQL注入、XSS等安全问题
5. **性能优化**：合理使用索引、避免N+1查询
6. **文档更新**：及时更新相关文档和注释
7. **样式一致性**：确保所有列表页面使用统一的样式结构和交互逻辑

## 🔧 常见问题修复指南

### 按钮点击无响应问题

**问题现象**：列表页面中的操作按钮（如测试、配置、编辑等）点击后无任何反应

**可能原因**：
1. JavaScript函数未定义或名称不匹配
2. 模板语法错误导致onclick事件无效
3. 数据获取方式错误（如使用不存在的tableIns.config.data）
4. 函数中的数据查询逻辑有误

**解决方案**：

1. **检查模板语法**：
```html
<!-- 正确的模板语法 -->
<script type="text/html" id="operationTpl">
	<button class="table-btn" onclick="testFunction('{{d.id}}')">测试</button>
	<button class="table-btn" onclick="configFunction('{{d.id}}')">配置</button>
</script>
```

2. **修复数据获取方式**：
```javascript
// 错误的方式（可能导致数据获取失败）
function getRowDataById(id) {
	var tableData = tableIns.config.data; // 这个属性可能不存在
	// ...
}

// 正确的方式（使用Ajax同步请求）
function getRowDataById(id) {
	var result = null;
	$.ajax({
		url: '{:url("index")}',
		type: 'GET',
		async: false,
		data: {id: id},
		success: function(res) {
			if(res.data && res.data.length > 0) {
				result = res.data[0];
			}
		}
	});
	return result;
}
```

3. **确保函数定义完整**：
```javascript
// 确保所有在模板中调用的函数都已定义
function testFunction(id) {
	var data = getRowDataById(id);
	if(!data) {
		layer.msg('未找到数据');
		return;
	}
	// 执行具体逻辑
}

function configFunction(id) {
	var data = getRowDataById(id);
	if(!data) {
		layer.msg('未找到数据');
		return;
	}
	// 执行具体逻辑
}
```

**修复步骤**：
1. 检查浏览器控制台是否有JavaScript错误
2. 验证模板中的函数调用语法是否正确
3. 确认所有被调用的函数都已定义
4. 测试数据获取逻辑是否正常工作
5. 逐步调试，使用console.log输出调试信息

---

## 🎯 列表页面全面优化标准

### 8.1 页面结构优化要点

#### 核心优化原则
1. **统一页面结构**：使用标准的layui-card布局
2. **优化操作按钮**：统一按钮样式和图标
3. **改进搜索表单**：标准化表单布局和样式
4. **增强表格功能**：添加排序、分页、状态显示
5. **完善事件处理**：标准化Ajax请求和错误处理
6. **添加用户体验**：加载动画、确认对话框、成功提示

#### 标准HTML结构
```html
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
            <div class="layui-card-header">
                <i class="fa fa-icon"></i> {:t('页面标题')}
            </div>
            <div class="layui-card-body" pad15>
                <!-- 操作按钮区域 -->
                <div class="layui-row" style="margin-bottom: 15px;">
                    <div class="layui-col-md4">
                        <button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">
                            <i class="layui-icon layui-icon-delete"></i> {:t('批量删除')}
                        </button>
                    </div>
                    <div class="layui-col-md8">
                        <form class="layui-form" style="text-align: right;">
                            <!-- 搜索表单内容 -->
                        </form>
                    </div>
                </div>
                <!-- 数据表格 -->
                <table class="layui-hide" id="table-id" lay-filter="table-filter"></table>
            </div>
        </div>
    </div>
</div>
```

### 8.2 JavaScript优化标准

#### 表格渲染配置
```javascript
var tableIns = table.render({
    elem: '#table-id',
    url: '{:url("index")}',
    cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', width: 80, title: 'ID', sort: true},
        {field: 'status', title: '{:t("状态")}', width: 120, templet: '#status-template', align: 'center'},
        {title: '{:t("操作")}', width: 200, align: 'center', fixed: 'right', toolbar: '#toolbar-template'}
    ]],
    page: true,
    limit: 20,
    limits: [10, 20, 30, 50, 100],
    height: 'full-220',
    text: '{:t("对不起，加载出现异常！")}',
    even: true,
    loading: true
});
```

#### 事件处理标准
```javascript
// 工具条事件处理
table.on('tool(table-filter)', function(obj){
    var data = obj.data;
    var layEvent = obj.event;

    if(layEvent === 'del'){
        layer.confirm('{:t("确定要删除这条记录吗？")}', {
            icon: 3,
            title: '{:t("删除确认")}'
        }, function(index){
            var loadIndex = layer.load(2);
            $.post('{:url("del")}', {id: data.id}, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    obj.del();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function(){
                layer.close(loadIndex);
                layer.msg('{:t("网络请求失败")}', {icon: 2});
            });
            layer.close(index);
        });
    }
});
```

### 8.3 后端控制器优化

#### 标准控制器方法结构
```php
public function index(){
    if(request()->isAjax()){
        // 分页参数
        $page = input('param.page');
        $limit = input('param.limit');
        $order = input('param.field') && input('param.order') ?
                input('param.field').' '.input('param.order') : 'id desc';

        // 查询条件
        $where = [['aid','=',aid]];
        if(input('param.keyword')) $where[] = ['name','like','%'.input('param.keyword').'%'];

        // 查询数据
        $count = Db::name('table_name')->where($where)->count();
        $data = Db::name('table_name')->where($where)->page($page,$limit)->order($order)->select()->toArray();

        // 数据处理
        foreach($data as $k=>$v){
            $data[$k]['status_text'] = $v['status'] == 1 ? '已完成' : '处理中';
            $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
        }

        return json(['code'=>0,'msg'=>'','count'=>$count,'data'=>$data]);
    }
    return View::fetch();
}
```

---

**版本**：v1.3
**更新时间**：2025-08-02
**最新更新**：添加列表页面全面优化标准和梦想启蒙记录页面优化实例
