{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?30e5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?ab9a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?d3a1", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?e979", "uni-app:///pagesExt/maidanpays/pay.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?477a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/maidanpays/pay.vue?ca9e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pre_url", "opt", "loading", "isload", "menuindex", "bid", "ymid", "hiddenmodalput", "wxpayst", "alipay", "paypwd", "moneypay", "mdlist", "name", "userinfo", "couponList", "couponrid", "coupontype", "usescore", "money", "disprice", "dkmoney", "couponmoney", "paymoney", "mdkey", "couponvisible", "couponkey", "logo", "ordernum", "KeyboardKeys", "keyHidden", "selectmdDialogShow", "onLoad", "app", "uni", "console", "jine", "shoporderid", "onPullDownRefresh", "methods", "handleHiddenKey", "handleShowKey", "handle<PERSON>ey", "that", "getdata", "modalinput", "selectmd", "itemlist", "itemList", "success", "selectmdRadioChange", "hideSelectmdDialog", "scoredk", "inputMoney", "cancel", "calculatePrice", "key", "icon", "title", "mdid", "Math", "s"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8H5wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IAEA;MACAC;MACAC;IACA;;IAEA;IACA;IACA;IAEAC;MACAC;MACAC;IACA;IAEA;EACA;;EACAC;IACA;EACA;EACAC;IAEAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;UACAC;QACA;MACA;QACA;QACA;QACAR;QACA;UACAQ;QACA;MACA;MACA;IACA;IAGAC;MACA;MACAD;MACAV;QACA5B;MACA;QACAsC;QACA;UACAV;YACAA;UACA;UACA;QACA;QACA;QACA;QACA;QACAU;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEA;UACAV;YACA;YACA;YACA;YACA;YAEA;cACArB;YACA;YAEAA;YACAuB;YACAQ;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;YACAA;YACAA;YACAR;UACA;YACAA;UACA;QACA;QAEAA;MACA;IACA;IACAU;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAJ;MACA;QACAT;UACAc;UACAC;YACA;cACAN;YACA;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACAlB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAmB;MACA;IACA;IACA;IACAC;MACA;MAEA;MACA;QACApC;MACA;QACAA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA,wEACAE;QACAA;MACA;MAEA;MACA;MACAE;MACAoB;MACAA;IACA;EAAA,2EACAa;IACA;IACArB;EACA,4DAEA;IACA;IACA;IAEA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;MACA;MACA;MACA;QACAb;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA,qDACA;IACA;IACA;IACA;IACA;IAEA;MACA;IACA;MACA;IACA;;IAEA;IACA;MACAY;QACAuB;QACAC;MACA;MACA;IACA;IAEAzB;MACA5B;MACAC;MACAa;MACAH;MACAE;MACAyC;MACAtB;IACA;MACA;QACAJ;QACA;MACA;MACAA;IACA;EACA,8DACA;IACA;EACA,+DACA;IACA;EACA,2DACA;IACA;IACA;IACA;IACA;IACA,uGACA2B;IACAC;IACAA;IACA;EACA,uDACA;IACA;MACA;MACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACzcA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/maidanpays/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/maidanpays/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=5c7cb3f4&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/maidanpays/pay.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=template&id=5c7cb3f4&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.userinfo.discount > 0 && _vm.userinfo.discount < 10\n      ? _vm.t(\"会员\")\n      : null\n  var m1 = _vm.isload && _vm.keyHidden ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l0 =\n    _vm.isload && _vm.selectmdDialogShow\n      ? _vm.__map(_vm.mdlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = index == _vm.mdkey ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<block v-if=\"isload\">\n\t\t\t<view class=\"container\">\n\t\t\t\t<view class=\"header\">\n\t\t\t\t\t<view class=\"flex-y-center\">\n\t\t\t\t\t\t<image class=\"header_icon\" :src=\"logo\"></image>\n\t\t\t\t\t\t<view class=\"flex1\">\n\t\t\t\t\t\t\t<view class=\"header_name\">{{name}}</view>\n\t\t\t\t\t\t\t<view class=\"header_shop\">\n\t\t\t\t\t\t\t\t<text>选择门店:</text><text @tap=\"selectmd\">{{mdlist[mdkey].name}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"page\">\n\t\t\t\t<view class=\"page_module flex-y-center\" @click=\"handleShowKey\">\n\t\t\t\t    <text class=\"page_tag\">￥</text>\n\t\t\t\t    <view class=\"page_price flex-y-center\">\n\t\t\t\t        <text v-if=\"keyHidden && !money\" class=\"page_notice\">请输入金额</text>\n\t\t\t\t        <text v-if=\"money\" :class=\"{ 'editable-money': !keyHidden }\">{{ money }}</text>\n\t\t\t\t        <view v-if=\"!keyHidden\" class=\"page_cursor\"></view>\n\t\t\t\t    </view>\n\t\t\t\t</view>\n\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"info-box\">\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex\" v-if=\"userinfo.discount>0 && userinfo.discount<10\">\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" style=\"color: #e94745;\">-￥{{disprice}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- <view class=\"dkdiv-item flex-y-center\">\n\t\t\t\t\t\t\t\t<text class=\"f1\">{{t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-if=\"couponList.length>0\" @tap=\"showCouponList\" style=\"color:#e94745\">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\n\t\t\t\t\t\t\t\t<image class=\"f3\" src=\"/static/img/arrowright.png\"></image>\n\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t<!-- \t<view class=\"dkdiv-item flex\" v-if=\"userinfo.scoredkmaxpercent > 0\">\n\t\t\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.dkmoney*1}}</text> 元</view>\n\t\t\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">\n\t\t\t\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t<view class=\"dkdiv-item flex flex-bt\">\n\t\t\t\t\t\t\t\t<text class=\"t1\">实付金额:</text>\n\t\t\t\t\t\t\t\t<text class=\"t2\">￥{{paymoney}}</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view v-if=\"keyHidden\" class=\"op\">\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"topay\" :style=\"{background:t('color1')}\">去支付</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\n\t\t\t</view>\n\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\" :selectedrid=\"couponrid\"\n\t\t\t\t\t\t\t@chooseCoupon=\"chooseCoupon\"></couponlist>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"!keyHidden\" class=\"keyboard_page\">\n\t\t\t\t<view @click=\"handleHiddenKey\" class=\"keyboard_none\"></view>\n\t\t\t\t<view class=\"keyboard_key hind_box\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\n\t\t\t\t\t<image @click=\"handleHiddenKey\" class=\"key-down\" :src=\"pre_url+'/static/img/pack_up.png'\" mode=\"\"></image>\n\t\t\t\t\t<view class=\"key-box\">\n\t\t\t\t\t\t<view class=\"number-box clearfix\">\n\t\t\t\t\t\t\t<view v-for=\"(item,index) in KeyboardKeys\" :key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"index === 9 ? 'key key-zero' : 'key'\" hover-class=\"number-box-hover\"\n\t\t\t\t\t\t\t\t@click=\"handleKey(item)\">{{item}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"btn-box\">\n\t\t\t\t\t\t\t<!-- TODO: 需要替换成删除icon -->\n\t\t\t\t\t\t\t<view class=\"key\" hover-class=\"number-box-hover\" data-key=\"X\" @click=\"handleKey('X')\">×\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view :class=\"money ? 'key pay_btn' : 'key pay_btn pay-btn-display'\"\n\t\t\t\t\t\t\t\thover-class=\"pay-btn-hover\" @tap=\"topay\">付款</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"selectmdDialogShow\" class=\"popup__container\">\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideSelectmdDialog\"></view>\n\t\t\t\t<view class=\"popup__modal\">\n\t\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择门店</text>\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hideSelectmdDialog\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in mdlist\" :key=\"index\" @tap=\"selectmdRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\n\t\t\t\t\t\t\t<view style=\"color:#999;font-size:24rpx;margin-right:10rpx\">{{item.juli ? ' 距离:' + item.juli + '千米' : ''}}</view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"index==mdkey ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</block>\n\t\t<loading v-if=\"loading\"></loading>\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t\t<popmsg ref=\"popmsg\"></popmsg>\n\t</view>\n</template>\n<script>\n    var app = getApp();\n\n    export default {\n        data() {\n            return {\n                pre_url: app.globalData.pre_url,\n                opt: {},\n                loading: false,\n                isload: false,\n                menuindex: -1,\n\n                bid: 0,\n                ymid: 0,\n                hiddenmodalput: true,\n                wxpayst: '',\n                alipay: '',\n                paypwd: '',\n                moneypay: '',\n                mdlist: \"\",\n                name: \"\",\n                userinfo: \"\",\n                couponList: [],\n                couponrid: 0,\n                coupontype: 1,\n                usescore: 0,\n                money: '',\n                disprice: 0,\n                dkmoney: 0,\n                couponmoney: 0,\n                paymoney: 0,\n                mdkey: 0,\n                couponvisible: false,\n                couponkey: 0,\n                logo:\"\",\n                ordernum: '', // 添加 ordernum\n\n                KeyboardKeys: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.'],\n                keyHidden: false,\n                selectmdDialogShow: false,\n            };\n        },\n\n        onLoad: function(opt) {\n            this.opt = app.getopts(opt);\n            this.bid = this.opt.bid || 0;\n            this.ymid = this.opt.ymid || 0;\n\n            if(this.opt.ymid){\n                app.globalData.pid = this.opt.ymid;\n                uni.setStorageSync('pid', this.opt.ymid);\n            }\n\n            // 读取 URL 参数\n            this.jine = this.opt.jine ? parseFloat(this.opt.jine).toFixed(2) : '';\n            this.ordernum = this.opt.ordernum || '';\n\n            console.log('Parsed Parameters:', {\n                jine: this.jine,\n                shoporderid: this.ordernum\n            });\n\n            this.getdata(opt); // 传递 opt 参数到 getdata 方法\n        },\n        onPullDownRefresh: function() {\n            this.getdata(this.opt);\n        },\n        methods: {\n\n            handleHiddenKey() {\n                this.keyHidden = true;\n            },\n            // 显示键盘\n            handleShowKey() {\n                this.keyHidden = false;\n            },\n            // 键盘输入\n            handleKey(key) {\n                const that = this\n                const { money } = this\n                // 删除金额\n                if (key === 'X') {\n                    if (money !== '') {\n                        const payMoney = money.slice(0, money.length - 1)\n                        that.money = payMoney\n                    }\n                } else {\n                    // 添加金额\n                    const payMoney = money + key\n                    console.log(/^(\\d+\\.?\\d{0,2})$/.test(payMoney), payMoney, 'payMoney')\n                    if (/^(\\d+\\.?\\d{0,2})$/.test(payMoney)) {\n                        that.money = payMoney\n                    }\n                }\n                this.calculatePrice();\n            },\n\n\n            getdata: function(opt) {\n                var that = this; //获取产品信息\n                that.loading = true;\n                app.get('ApiMaidan/maidan', {\n                    bid: that.bid\n                }, function(res) {\n                    that.loading = false;\n                    if (res.status == 0) {\n                        app.alert(res.msg, function() {\n                            app.goback();\n                        });\n                        return;\n                    }\n                    var userinfo = res.userinfo;\n                    var couponList = res.couponList;\n                    var mdlist = res.mdlist;\n                    that.wxpayst = res.wxpayst;\n                    that.alipay = res.alipay;\n                    that.couponList = res.couponList;\n                    that.mdlist = res.mdlist;\n                    that.moneypay = res.moneypay;\n                    that.name = res.name;\n                    that.userinfo = res.userinfo;\n                    that.logo = res.logo;\n                    that.loaded();\n\n                    if (mdlist.length > 0) {\n                        app.getLocation(function(res) {\n                            var latitude = res.latitude;\n                            var longitude = res.longitude;\n                            var speed = res.speed;\n                            var accuracy = res.accuracy;\n\n                            for (var i in mdlist) {\n                                mdlist[i].juli = that.GetDistance(latitude, longitude, mdlist[i].latitude, mdlist[i].longitude);\n                            }\n\n                            mdlist = mdlist.sort(that.compare('juli'));\n                            console.log('Sorted mdlist:', mdlist);\n                            that.mdlist = mdlist;\n                        });\n                    }\n\n                    // 设置金额，如果 URL 中存在 jine 参数\n                    if (opt && opt.jine) {\n                        // 确保 jine 是有效的数字\n                        var jine = parseFloat(opt.jine);\n                        if (!isNaN(jine) && jine >= 0) {\n                            that.money = jine.toFixed(2);\n                            that.calculatePrice();\n                            console.log('Money set to:', that.money);\n                        } else {\n                            console.warn('无效的金额参数:', opt.jine);\n                        }\n                    }\n\n                    console.log('Order Number:', that.ordernum);\n                });\n            },\n            modalinput: function() {\n                this.$refs.dialogInput.open()\n            },\n            //选择门店\n            selectmd: function(e) {\n                var that = this;\n                var itemlist = [];\n                var mdlist = this.mdlist;\n                for (var i = 0; i < mdlist.length; i++) {\n                    itemlist.push(mdlist[i].name + (mdlist[i].juli ? ' 距离:' + mdlist[i].juli + '千米' : ''));\n                }\n                if (itemlist.length > 6) {\n                    that.selectmdDialogShow = true;\n                } else {\n                    uni.showActionSheet({\n                        itemList: itemlist,\n                        success: function(res) {\n                            if (res.tapIndex >= 0) {\n                                that.mdkey = res.tapIndex;\n                            }\n                        }\n                    });\n                }\n            },\n            selectmdRadioChange: function (e) {\n                this.mdkey = e.currentTarget.dataset.index;\n                this.selectmdDialogShow = false;\n            },\n            hideSelectmdDialog: function () {\n                this.selectmdDialogShow = false\n            },\n            //积分抵扣\n            scoredk: function(e) {\n                var usescore = e.detail.value[0];\n                if (!usescore) usescore = 0;\n                this.usescore = usescore;\n                this.calculatePrice();\n            },\n            inputMoney: function(e) {\n                console.log(e);\n                var money = e.detail.value;\n                if (!money) money = 0;\n                var money = parseFloat(money);\n                if (money <= 0) money = 0;\n                this.money = money;\n                this.calculatePrice();\n            },\n            cancel: function() {\n                this.hiddenmodalput = true;\n            },\n            //计算价格\n            calculatePrice: function() {\n                var that = this;\n\n                var money = ''\n                if (that.money == '') {\n                    money = 0;\n                } else {\n                    money = parseFloat(that.money);\n                }\n                if (that.userinfo.discount > 0 && that.userinfo.discount < 10) {\n                    var disprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100; //-会员折扣\n                } else {\n                    var disprice = 0;\n                }\n                var couponmoney = parseFloat(that.couponmoney); //-优惠券抵扣 \n                if (that.usescore) {\n                    var dkmoney = parseFloat(that.userinfo.dkmoney); //-积分抵扣\n                } else {\n                    var dkmoney = 0;\n                }\n                var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\n                if (dkmoney > 0 && scoredkmaxpercent >= 0 && scoredkmaxpercent < 100 &&\n                    dkmoney > (money - disprice - couponmoney) * scoredkmaxpercent * 0.01) {\n                    dkmoney = (money - disprice - couponmoney) * scoredkmaxpercent * 0.01;\n                }\n\n                var paymoney = money - disprice - couponmoney - dkmoney; // 商品金额 - 会员折扣 - 优惠券抵扣 - 积分抵扣\n                if (paymoney < 0) paymoney = 0;\n                paymoney = paymoney.toFixed(2);\n                that.paymoney = paymoney;\n                that.disprice = disprice;\n            },\n\t\t\thandleKey(key) {\n\t\t\t    // 禁用键盘操作\n\t\t\t    console.warn(\"键盘输入已禁用\");\n\t\t\t},\n\n            chooseCoupon: function(e) {\n                var couponrid = e.rid;\n                var couponkey = e.key;\n\n                if (couponrid == this.couponrid) {\n                    this.couponkey = 0;\n                    this.couponrid = 0;\n                    this.coupontype = 1;\n                    this.couponmoney = 0;\n                    this.couponvisible = false;\n                } else {\n                    var couponList = this.couponList;\n                    var couponmoney = couponList[couponkey]['money'];\n                    var coupontype = couponList[couponkey]['type'];\n                    if (coupontype == 4) {\n                        couponmoney = this.freightprice;\n                    }\n                    this.couponkey = couponkey;\n                    this.couponrid = couponrid;\n                    this.coupontype = coupontype;\n                    this.couponmoney = couponmoney;\n                    this.couponvisible = false;\n                }\n                this.calculatePrice();\n            },\n            topay: function(e) {\n                var that = this;\n                var money = that.money;\n                var couponrid = that.couponrid;\n                var usescore = that.usescore;\n\n                if (that.mdlist.length > 0) {\n                    var mdid = that.mdlist[that.mdkey].id;\n                } else {\n                    var mdid = 0;\n                }\n\n                // 检查是否提供了 ordernum\n                if (!that.ordernum) {\n                    uni.showToast({\n                        icon: 'none',\n                        title: '订单编号不存在'\n                    });\n                    return;\n                }\n\n                app.post('ApiMaidan/maidan', {\n                    bid: that.bid,\n                    ymid: that.ymid,\n                    money: money,\n                    couponrid: couponrid,\n                    usescore: usescore,\n                    mdid: mdid,\n                    shoporderid: that.ordernum // 添加 ordernum 参数\n                }, function(res) {\n                    if (res.status == 0) {\n                        app.error(res.msg);\n                        return;\n                    }\n                    app.goto('/pages/pay/pay?id=' + res.payorderid + '&tourl=/pagesExt/maidanpays/pay?bid=' + that.bid + '&ordernum=' + that.ordernum);\n                });\n            },\n            showCouponList: function() {\n                this.couponvisible = true;\n            },\n            handleClickMask: function() {\n                this.couponvisible = false;\n            },\n            GetDistance: function(lat1, lng1, lat2, lng2) {\n                var radLat1 = lat1 * Math.PI / 180.0;\n                var radLat2 = lat2 * Math.PI / 180.0;\n                var a = radLat1 - radLat2;\n                var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n                var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *\n                    Math.pow(Math.sin(b / 2), 2)));\n                s = s * 6378.137; // EARTH_RADIUS;\n                s = Math.round(s * 100) / 100;\n                return s;\n            },\n            compare: function(property) {\n                return function(a, b) {\n                    var value1 = a[property];\n                    var value2 = b[property];\n                    return value1 - value2;\n                };\n            }\n        }\n    }\n</script>\n\n<style>\n\tpage {\n\t\tbackground: #f0f0f0;\n\t}\n\n\t.container {\n\t\tposition: fixed;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t\tz-index: 5;\n\t}\n\n\t.header {\n\t\tposition: relative;\n\t\tpadding: 30rpx;\n\t}\n\n\t.header_text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.header_name {\n\t\tfont-size: 36rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.header_icon {\n\t\tposition: relative;\n\t\theight: 85rpx;\n\t\twidth: 85rpx;\n\t\tmargin-right: 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground: #37b053;\n\t}\n\n\t.header_shop {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.page {\n\t\tposition: relative;\n\t\tpadding: 20rpx 50rpx 20rpx 50rpx;\n\t\tborder-radius: 30rpx 30rpx 0 0;\n\t\tbackground: #fff;\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\theight: calc(100% - 185rpx);\n\t}\n\n\t.page_title {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t}\n\n\t.page_module {\n\t\tposition: relative;\n\t\theight: 125rpx;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.page_notice {\n\t\tcolor: #999;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: normal;\n\t}\n\n\t.page_tag {\n\t\tfont-size: 58rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.page_price {\n\t\tmargin-left: 20rpx;\n\t\tfont-size: 54rpx;\n\t\tcolor: #333;\n\t\tfont-weight: bold;\n\t}\n\n\t.page_cursor {\n\t\twidth: 4rpx;\n\t\theight: 70rpx;\n\t\tbackground: #1AAD19;\n\t\tborder-radius: 6rpx;\n\t\tanimation: twinkling 1.5s infinite;\n\t}\n\n\t@keyframes twinkling {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t}\n\n\t\t90% {\n\t\t\topacity: .8;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.info-box {\n\t\tposition: relative;\n\t\tbackground: #fff;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-bottom: 1px #f3f3f3 solid;\n\t}\n\n\t.info-item:last-child {\n\t\tborder: none\n\t}\n\n\t.info-item .t1 {\n\t\twidth: 200rpx;\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tcolor: #000;\n\t}\n\n\t.info-item .t2 {\n\t\theight: 120rpx;\n\t\tline-height: 120rpx;\n\t\tcolor: #000;\n\t\ttext-align: right;\n\t\tflex: 1;\n\t\tfont-size: 28rpx\n\t}\n\n\t.info-item .t2 input {\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tborder: 1px solid #f5f5f5;\n\t\tpadding: 0 5px;\n\t\twidth: 240rpx;\n\t\tfont-size: 30rpx;\n\t\tmargin-right: 10rpx\n\t}\n\n\t.dkdiv {\n\t\tmargin-top: 20rpx\n\t}\n\n\t.dkdiv-item {\n\t\twidth: 100%;\n\t\tpadding: 30rpx 0;\n\t\tbackground: #fff;\n\t\tborder-bottom: 1px #ededed solid;\n\t}\n\n\t.dkdiv-item:last-child {\n\t\tborder: none;\n\t}\n\n\t.dkdiv-item .f1 {}\n\n\t.dkdiv-item .f2 {\n\t\ttext-align: right;\n\t\tflex: 1\n\t}\n\n\t.dkdiv-item .f3 {\n\t\twidth: 30rpx;\n\t\theight: 30rpx;\n\t}\n\n\t.fpay-btn {\n\t\twidth: 90%;\n\t\tmargin: 0 5%;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tmargin-top: 40rpx;\n\t\tfloat: left;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #1aac19;\n\t\tborder: none;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.fpay-btn2 {\n\t\twidth: 90%;\n\t\tmargin: 0 5%;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tmargin-top: 20rpx;\n\t\tfloat: left;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tbackground: #e2cc05;\n\t\tborder: none;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.mendian {\n\t\twidth: 90%;\n\t\tline-height: 60rpx;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 30rpx 5%;\n\t\theight: 800rpx;\n\t\toverflow-y: scroll;\n\t\tborder: none;\n\t\tborder-radius: 5px;\n\t\t-webkit-animation-duration: .5s;\n\t\tanimation-duration: .5s;\n\t}\n\n\t.mendian label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\t\tpadding: 20rpx 0;\n\t\tcolor: #333\n\t}\n\n\t.mendian input {\n\t\tmargin-right: 10rpx\n\t}\n\n\t.submit {\n\t\ttext-align: center\n\t}\n\n\t.mendian button {\n\t\tpadding: 20rpx 60rpx;\n\t\tborder-radius: 40rpx;\n\t\tborder: 0;\n\t\tmargin-top: 20rpx;\n\t\tcolor: #fff;\n\t\tbackground: #31C88E\n\t}\n\n\t.i-as {\n\t\tposition: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: #f7f7f8;\n\t\ttransform: translate3d(0, 100%, 0);\n\t\ttransform-origin: center;\n\t\ttransition: all .2s ease-in-out;\n\t\tz-index: 900;\n\t\tvisibility: hidden\n\t}\n\n\t.i-as-show {\n\t\ttransform: translate3d(0, 0, 0);\n\t\tvisibility: visible\n\t}\n\n\t.i-as-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, .7);\n\t\tz-index: 900;\n\t\ttransition: all .2s ease-in-out;\n\t\topacity: 0;\n\t\tvisibility: hidden\n\t}\n\n\t.i-as-mask-show {\n\t\topacity: 1;\n\t\tvisibility: visible\n\t}\n\n\t.i-as-header {\n\t\tbackground: #fff;\n\t\ttext-align: center;\n\t\tposition: relative;\n\t\tfont-size: 30rpx;\n\t\tcolor: #555;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx\n\t}\n\n\t.i-as-header::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 200%;\n\t\theight: 200%;\n\t\ttransform: scale(.5);\n\t\ttransform-origin: 0 0;\n\t\tpointer-events: none;\n\t\tbox-sizing: border-box;\n\t\tborder: 0 solid #e9eaec;\n\t\tborder-bottom-width: 1px\n\t}\n\n\t.i-as-cancel {\n\t\tmargin-top: 20rpx\n\t}\n\n\t.i-as-cancel button {\n\t\tborder: 0\n\t}\n\n\t.i-as-cancel button::after {\n\t\tborder: 0;\n\t}\n\n\t.i-as-content {\n\t\theight: 700rpx;\n\t\twidth: 710rpx;\n\t\tmargin: 20rpx;\n\t}\n\n\n\t.op {\n\t\twidth: 96%;\n\t\tmargin: 20rpx 2%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 40rpx\n\t}\n\n\t.op .btn {\n\t\tflex: 1;\n\t\theight: 100rpx;\n\t\tline-height: 100rpx;\n\t\tbackground: #07C160;\n\t\twidth: 90%;\n\t\tmargin: 0 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center\n\t}\n\n\t.op .btn .img {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-right: 20rpx\n\t}\n\n\n\t.keyboard_page {\n\t\tposition: fixed;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: 999;\n\t}\n\n\t.keyboard_none {\n\t\tposition: absolute;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\ttop: 0;\n\t\tleft: 0;\n\t}\n\n\t.keyboard_key {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\theight: 0;\n\t\tz-index: 10;\n\t\tbackground: #f7f7f7;\n\t\tz-index: 9999999999;\n\t\ttransition: height 0.3s;\n\t\tpadding: 20rpx 0 0 0;\n\t}\n\n\t.hind_box {\n\t\theight: 515rpx;\n\t}\n\n\t.key-box {\n\t\tdisplay: flex;\n\t\tpadding-left: 16rpx;\n\t\tpadding-bottom: 16rpx;\n\t\tpadding-bottom: calc(16rpx + constant(safe-area-inset-bottom));\n\t\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n\t}\n\t\n\t.key-down{\n\t\theight: 50rpx;\n\t\twidth: 50rpx;\n\t\tdisplay: block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.number-box {\n\t\tflex: 3;\n\t}\n\n\t.number-box .key {\n\t\tfloat: left;\n\t\tmargin: 16rpx 16rpx 0 0;\n\t\twidth: calc(100% / 3 - 16rpx);\n\t\theight: 90rpx;\n\t\tborder-radius: 10rpx;\n\t\tline-height: 90rpx;\n\t\ttext-align: center;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tbackground-color: #fff;\n\t}\n\n\t.number-box .key.key-zero {\n\t\twidth: calc((100% / 3) * 2 - 16rpx);\n\t}\n\n\t.keyboard .number-box-hover {\n\t\t/* 临时定义颜色 */\n\t\tbackground-color: #e1e1e1 !important;\n\t}\n\n\t.btn-box {\n\t\tflex: 1;\n\t}\n\n\t.btn-box .key {\n\t\tmargin: 16rpx 16rpx 0 0;\n\t\theight: 90rpx;\n\t\tborder-radius: 10rpx;\n\t\tline-height: 90rpx;\n\t\ttext-align: center;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tbackground-color: #fff;\n\t}\n\n\t.btn-box .pay_btn {\n\t\theight: 298rpx;\n\t\tline-height: 298rpx;\n\t\tfont-weight: normal;\n\t\tbackground-color: #1AAD19;\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.btn-box .pay_btn.pay-btn-display {\n\t\tbackground-color: #9ED99D !important;\n\t}\n\n\t.btn-box .pay_btn.pay-btn-hover {\n\t\tbackground-color: #179B16;\n\t}\n\t.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\n</style>\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115104654\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}