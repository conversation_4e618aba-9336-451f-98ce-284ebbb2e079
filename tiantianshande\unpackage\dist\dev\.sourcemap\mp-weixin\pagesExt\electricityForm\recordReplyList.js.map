{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?e509", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?75f0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?1134", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?389c", "uni-app:///pagesExt/electricityForm/recordReplyList.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?ad3d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/electricityForm/recordReplyList.vue?ca98"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "pagenum", "nomore", "is_more", "nodata", "keyword", "more_data", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "more_one", "confirm", "uni", "title", "content", "success", "that", "url", "close", "changetab", "scrollTop", "duration", "getdata", "app", "id", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAowB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6GxxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACAC;UACAC;UACAC;YACAC;UACA;QACA;MACA;MACA,qGACA;MACAJ;QACAK;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAP;QACAQ;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAN;MACAA;MACAA;MACAO;QACAC;QACA3B;QACA;MACA;QACAmB;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACA;MACA;IACA;;IACAS;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAAolC,CAAgB,gkCAAG,EAAC,C;;;;;;;;;;;ACAxmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/electricityForm/recordReplyList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/electricityForm/recordReplyList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recordReplyList.vue?vue&type=template&id=aeb5c04e&\"\nvar renderjs\nimport script from \"./recordReplyList.vue?vue&type=script&lang=js&\"\nexport * from \"./recordReplyList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recordReplyList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/electricityForm/recordReplyList.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordReplyList.vue?vue&type=template&id=aeb5c04e&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordReplyList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordReplyList.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\r\n\t\t\t<view class=\"order-content\">\r\n\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"order-box\">\r\n\t\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.userInfo.realname != null\" class=\"st2\">服务人员</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.userInfo.realname == null\" class=\"st3\">评论</text>\r\n\t\t\t\t\t\t\t<!-- <block v-if=\"item.status==1\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.buytype!=1\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.team.status==1\" class=\"st1\">拼团中</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.team.status==2 && item.freight_type!=1\" class=\"st1\">拼团成功,待发货</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.team.status==2 && item.freight_type==1\" class=\"st1\">拼团成功,待提货</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.team.status==3\" class=\"st4\">拼团失败,已退款</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.freight_type!=1\" class=\"st1\">待发货</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.freight_type==1\" class=\"st1\">待提货</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block> -->\r\n\r\n\t\t\t\t\t\t\t<!-- <text v-if=\"item.status==2\" class=\"st2\">待收货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text> -->\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"content\" style=\"border-bottom:none\">\r\n\t\t\t\t\t\t\t<!-- <view @tap.stop=\"goto\" :data-url=\"'product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.propic\"></image>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" style=\"display: block;\">{{item.content}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.userInfo.realname == null\">{{item.userInfo.nickname}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.userInfo.realname != null\">{{item.userInfo.realname}}</text>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"x2\">×{{item.num}}</text></view> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom\" style=\"text-align: right;\">\r\n\t\t\t\t\t\t\t<text>{{item.createtime}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"bottom\">\r\n\t\t\t\t\t\t\t<text>共计{{item.num}}件商品 实付:￥{{item.totalprice}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red\"> 退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red\"> 已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red\"> 退款申请已驳回</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t\t<!-- <block v-if=\"([1,2,3]).includes(item.status) && item.invoice && item.team.status==2\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t\t\t:data-url=\"'/pagesExt/order/invoice?type=lucky_collage&orderid=' + item.id\">发票\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block> -->\r\n\t\t\t\t\t\t\t<!-- <view v-if=\"is_more==1&&item.status!=0\" @tap.stop=\"more_one\" :data-id=\"item.proid\"\r\n\t\t\t\t\t\t\t\tclass=\"btn2\" :data-num=\"item.num\" :data-buytype=\"item.buytype\" :data-ggid=\"item.ggid\">\r\n\t\t\t\t\t\t\t\t再来一单</view> -->\r\n\t\t\t\t\t\t\t<!-- <view @tap.stop=\"goto\" :data-url=\"'recordDetail?id=' + item.id\" class=\"btn2\">详情</view> -->\r\n\t\t\t\t\t\t\t<!-- <view @tap.stop=\"goto\" :data-url=\"'team?teamid=' + item.teamid\" class=\"btn2\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.buytype!=1\">查看团</view> -->\r\n\t\t\t\t\t\t\t<!-- <block v-if=\"item.status==0\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"toclose\" :data-id=\"item.id\">关闭订单</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\"\r\n\t\t\t\t\t\t\t\t\t:data-url=\"'/pages/pay/pay?id=' + item.payorderid\">去付款</view>\r\n\t\t\t\t\t\t\t</block> -->\r\n\r\n\t\t\t\t\t\t\t<!-- <block v-if=\"item.status==2\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"goto\"\r\n\t\t\t\t\t\t\t\t\t:data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.freight_type!=3 && item.freight_type!=4\">查看物流</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"orderCollect\"\r\n\t\t\t\t\t\t\t\t\t:data-id=\"item.id\">确认收货</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.status==4\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" @tap.stop=\"todel\" :data-id=\"item.id\">删除订单</view>\r\n\t\t\t\t\t\t\t</block> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\r\n\t\t<uni-popup ref=\"more_one\" type=\"center\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" message=\"成功消息\" :duration=\"2000\" valueType=\"number\" :before-close=\"true\"\r\n\t\t\t\t@close=\"close\" @confirm=\"confirm\"></uni-popup-dialog>\r\n\t\t\t<!-- <view class=\"uni-popup-dialog\">\r\n\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t<text>test</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t<input type=\"number\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t <view></view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t</uni-popup>\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tdatalist: [],\r\n\t\t\t\tpagenum: 1,\r\n\t\t\t\tnomore: false,\r\n\t\t\t\tis_more: 0,\r\n\t\t\t\tnodata: false,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tmore_data: {}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (!this.nodata && !this.nomore) {\r\n\t\t\t\tthis.pagenum = this.pagenum + 1;\r\n\t\t\t\tthis.getdata(true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonNavigationBarSearchInputConfirmed: function(e) {\r\n\t\t\tthis.searchConfirm({\r\n\t\t\t\tdetail: {\r\n\t\t\t\t\tvalue: e.text\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmore_one: function(e) {\r\n\t\t\t\t// console.log(e);\r\n\t\t\t\tlet data = e.target.dataset;\r\n\t\t\t\t// let url = \"/activity/luckycollage/buy\"+\"?proid=\"+data['id']+\"&num=\"+data['num']+\"&buytype=\"+data['buytype']+\"&ggid=\"+data['ggid'];\r\n\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t// \turl:url,\r\n\t\t\t\t// })\r\n\t\t\t\tthis.more_data = data;\r\n\t\t\t\tthis.$refs.more_one.open();\r\n\r\n\t\t\t},\r\n\t\t\tconfirm(value1, value2) {\r\n\t\t\t\treturn \r\n\t\t\t\tlet data = this.more_data;\r\n\t\t\t\t//console.log(value2);\r\n\t\t\t\tlet num = value2;\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (num == \"\" || num == 0) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: \"提示\",\r\n\t\t\t\t\t\tcontent: \"请输入数量\",\r\n\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\tthat.$refs.more_one.close();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tlet url = \"/activity/luckycollage/buy\" + \"?proid=\" + data['id'] + \"&num=\" + num + \"&buytype=\" + data[\r\n\t\t\t\t\t'buytype'] + \"&ggid=\" + data['ggid'];\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t})\r\n\t\t\t\tthis.$refs.more_one.close();\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$refs.more_one.close();\r\n\t\t\t},\r\n\t\t\tchangetab: function(st) {\r\n\t\t\t\tthis.st = st;\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: 0,\r\n\t\t\t\t\tduration: 0\r\n\t\t\t\t});\r\n\t\t\t\tthis.getdata();\r\n\t\t\t},\r\n\t\t\tgetdata: function(loadmore) {\r\n\t\t\t\tif (!loadmore) {\r\n\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t}\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar pagenum = that.pagenum;\r\n\t\t\t\tvar id = that.opt.id;\r\n\t\t\t\tthat.nodata = false;\r\n\t\t\t\tthat.nomore = false;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiElectricityForm/getRecordDetail', {\r\n\t\t\t\t\tid: id,\r\n\t\t\t\t\tpagenum: pagenum,\r\n\t\t\t\t\t// keyword: that.keyword\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar data = res.list;\r\n\t\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// that.is_more = res['is_more'];\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsearchConfirm: function(e) {\r\n\t\t\t\tthis.keyword = e.detail.value;\r\n\t\t\t\tthis.getdata(false);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.container {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.topsearch {\r\n\t\twidth: 94%;\r\n\t\tmargin: 10rpx 3%;\r\n\t}\r\n\r\n\t.topsearch .f1 {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 0;\r\n\t\tbackground-color: #fff;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.topsearch .f1 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 10px\r\n\t}\r\n\r\n\t.topsearch .f1 input {\r\n\t\theight: 100%;\r\n\t\tflex: 1;\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.order-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column\r\n\t}\r\n\r\n\t.order-box {\r\n\t\twidth: 94%;\r\n\t\tmargin: 10rpx 3%;\r\n\t\tpadding: 6rpx 3%;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 8px\r\n\t}\r\n\r\n\t.order-box .head {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #f4f4f4 solid;\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.order-box .head .f1 {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.order-box .head .f1 image {\r\n\t\twidth: 34rpx;\r\n\t\theight: 34rpx;\r\n\t\tmargin-right: 4px\r\n\t}\r\n\r\n\t.order-box .head .st0 {\r\n\t\twidth: 140rpx;\r\n\t\tcolor: #ff8758;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-box .head .st1 {\r\n\t\twidth: 204rpx;\r\n\t\tcolor: #ffc702;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-box .head .st2 {\r\n\t\twidth: 204rpx;\r\n\t\tcolor: #ff4246;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-box .head .st3 {\r\n\t\twidth: 140rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-box .head .st4 {\r\n\t\twidth: 140rpx;\r\n\t\tcolor: #bbb;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-box .content {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tpadding: 16rpx 0px;\r\n\t\tborder-bottom: 1px #f4f4f4 dashed;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.order-box .content:last-child {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.order-box .content image {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t}\r\n\r\n\t.order-box .content .detail {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-left: 14rpx;\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.order-box .content .detail .t1 {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.order-box .content .detail .t2 {\r\n\t\theight: 46rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tcolor: #999;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.order-box .content .detail .t3 {\r\n\t\tdisplay: flex;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #ff4246;\r\n\t}\r\n\r\n\t.order-box .content .detail .x1 {\r\n\t\tflex: 1\r\n\t}\r\n\r\n\t.order-box .content .detail .x2 {\r\n\t\twidth: 100rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: right;\r\n\t\tmargin-right: 8rpx\r\n\t}\r\n\r\n\t.order-box .bottom {\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0px;\r\n\t\tborder-top: 1px #f4f4f4 solid;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\t.order-box .op {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0px;\r\n\t\tborder-top: 1px #f4f4f4 solid;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\t.btn1 {\r\n\t\tmargin-left: 20rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 3px;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.btn2 {\r\n\t\tmargin-left: 20rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid #cdcdcd;\r\n\t\tborder-radius: 3px;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t/*.order-pin{ border: 1px #ffc702 solid; border-radius: 5px; color: #ffc702; float: right; padding: 0 5px; height: 23px; line-height: 23px; margin-left: 5px; font-size: 14px; position: absolute; bottom: 10px; right: 10px; background: #fff; }*/\r\n\t.order-pin {\r\n\t\tborder: 1px #ffc702 solid;\r\n\t\tborder-radius: 5px;\r\n\t\tcolor: #ffc702;\r\n\t\tfloat: right;\r\n\t\tpadding: 0 5px;\r\n\t\theight: 23px;\r\n\t\tline-height: 23px;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\r\n\t.zan-tex {\r\n\t\tclear: both;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tcolor: #565656;\r\n\t\tfont-size: 12px;\r\n\t\theight: 30px;\r\n\t\tline-height: 30px;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.ind-bot {\r\n\t\twidth: 100%;\r\n\t\tfloat: left;\r\n\t\ttext-align: center;\r\n\t\theight: 50px;\r\n\t\tline-height: 50px;\r\n\t\tfont-size: 13px;\r\n\t\tcolor: #ccc;\r\n\t\tbackground: #F2F2F2\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordReplyList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./recordReplyList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093170\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}