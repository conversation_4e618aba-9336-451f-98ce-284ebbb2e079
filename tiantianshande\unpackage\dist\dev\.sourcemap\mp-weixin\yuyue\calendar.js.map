{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?4d3e", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?e636", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?b31f", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?c46d", "uni-app:///yuyue/calendar.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?6889", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/calendar.vue?9fa9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pre_url", "showform", "currentYear", "currentMonth", "weekdays", "days", "worker_id", "showOrderList", "selectedDate", "selected<PERSON>ay", "dayOrders", "selectedStatus", "statusOptions", "label", "value", "showComment", "commentData", "loading", "isload", "onLoad", "onPullDownRefresh", "setTimeout", "uni", "methods", "t", "getWorkerInfo", "app", "that", "getCalendarData", "year", "month", "_t", "generateCalendar", "date", "day", "isToday", "count", "i", "status_count", "changeMonth", "showDayOrders", "getDayOrders", "params", "console", "closeOrderList", "filterByStatus", "viewOrderDetail", "setOrderStatus", "statusText", "title", "content", "success", "order_id", "status", "viewComment", "closeComment", "getLevelText", "previewImage", "urls", "current", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8PjxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MACA;MACA;MACAF;QACApB;QACAuB;QACAC;QACAC;MACA;QACA;UACA;UACAJ;QACA;UACAD;QACA;MACA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;;MAEA;MACA;QACA3B;UACA4B;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MAAA,2BACAC;QACA;QACA;UAAA;QAAA;UAAAD;UAAAE;QAAA;QAEAjC;UACA4B;UACAC;UACAC;UACAC;UACAE;QACA;MAAA;MAVA;QAAA;MAWA;;MAEA;MACA;MACA;MACA;QACAjC;UACA4B;UACAC;UACAC;UACAC;QACA;MACA;MAEA;IACA;IACAG;MACA;MACA;MAEA;QACAT;QACAD;MACA;QACAC;QACAD;MACA;MAEA;MACA;MACA;IACA;IACAW;MACA;MAEA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MAEA;MACA;MACA;QACAnC;QACA2B;QACAF;MACA;MAEA;QACAW;MACA;MAEAhB;QACA;UACAC;UACAgB;QACA;UACAjB;UACAC;QACA;MACA;IACA;IACAiB;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACApB;IACA;IACAqB;MACA;MACA;MAEA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;MAAA;MAGA1B;QACA2B;QACAC;QACAC;UACA;YACAzB;cACA0B;cACAC;YACA;cACA;gBACA3B;gBACA;gBACAC;cACA;gBACAD;cACA;YACA;UACA;QACA;MACA;IACA;IACA4B;MACA;MACA;MACA5B;QACA0B;QACArB;MACA;QACA;UACAJ;UACAA;QACA;UACAD;QACA;MACA;IACA;IACA6B;MACA;MACA;IACA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IACAC;MACAnC;QACAoC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;MACAnC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChgBA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/calendar.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/calendar.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./calendar.vue?vue&type=template&id=6b87c824&\"\nvar renderjs\nimport script from \"./calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./calendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./calendar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/calendar.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./calendar.vue?vue&type=template&id=6b87c824&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.days, function (day, index) {\n    var $orig = _vm.__get_orig(day)\n    var m0 = day.isToday ? _vm.t(\"color1rgb\") : null\n    var m1 = day.isToday ? _vm.t(\"color1\") : null\n    var m2 = day.count > 0 ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var l1 = _vm.showOrderList\n    ? _vm.__map(_vm.statusOptions, function (status, index) {\n        var $orig = _vm.__get_orig(status)\n        var m3 = _vm.selectedStatus === status.value ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m3: m3,\n        }\n      })\n    : null\n  var g0 = _vm.showOrderList ? _vm.dayOrders.length : null\n  var l2 = _vm.showOrderList\n    ? _vm.__map(_vm.dayOrders, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m4 = _vm.t(\"color1\")\n        var m5 = item.status == 1 ? _vm.t(\"color1\") : null\n        var m6 = item.status == 2 ? _vm.t(\"color1\") : null\n        var m7 = item.status == 3 ? _vm.t(\"color1\") : null\n        var m8 = item.status == 4 || item.status == 5 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 =\n    _vm.showComment &&\n    !(\n      !_vm.commentData ||\n      (!_vm.commentData.worker_comment && !_vm.commentData.product_comment)\n    ) &&\n    _vm.commentData.worker_comment\n      ? _vm.getLevelText(_vm.commentData.worker_comment.level)\n      : null\n  var g1 =\n    _vm.showComment &&\n    !(\n      !_vm.commentData ||\n      (!_vm.commentData.worker_comment && !_vm.commentData.product_comment)\n    ) &&\n    _vm.commentData.worker_comment\n      ? _vm.commentData.worker_comment.content_pic_array &&\n        _vm.commentData.worker_comment.content_pic_array.length > 0\n      : null\n  var g2 =\n    _vm.showComment &&\n    !(\n      !_vm.commentData ||\n      (!_vm.commentData.worker_comment && !_vm.commentData.product_comment)\n    ) &&\n    _vm.commentData.product_comment\n      ? _vm.commentData.product_comment.content_pic_array &&\n        _vm.commentData.product_comment.content_pic_array.length > 0\n      : null\n  var m10 =\n    _vm.showComment &&\n    !(\n      !_vm.commentData ||\n      (!_vm.commentData.worker_comment && !_vm.commentData.product_comment)\n    ) &&\n    _vm.commentData.product_comment &&\n    _vm.commentData.product_comment.reply_content\n      ? _vm.t(\"color1\")\n      : null\n  var m11 = _vm.t(\"color1\")\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, day) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        day = _temp2.day\n      var _temp, _temp2\n      day.date && _vm.showDayOrders(day)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        g0: g0,\n        l2: l2,\n        m9: m9,\n        g1: g1,\n        g2: g2,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./calendar.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"calendar-page\">\r\n    <view class=\"header\">\r\n      <view class=\"month-selector\">\r\n        <view class=\"selector-btn prev\" @tap=\"changeMonth(-1)\">\r\n          <text>上一个月</text>\r\n        </view>\r\n        <view class=\"current-month\">{{currentYear}}年{{currentMonth}}月</view>\r\n        <view class=\"selector-btn next\" @tap=\"changeMonth(1)\">\r\n          <text>下一个月</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"calendar\">\r\n      <view class=\"calendar-header\">\r\n        <view class=\"weekday\" v-for=\"(day, index) in weekdays\" :key=\"index\">{{day}}</view>\r\n      </view>\r\n      <view class=\"calendar-body\">\r\n        <view \r\n          class=\"calendar-day\" \r\n          v-for=\"(day, index) in days\" \r\n          :key=\"index\"\r\n          :class=\"{\r\n            'empty': !day.date, \r\n            'today': day.isToday,\r\n            'has-orders': day.count > 0\r\n          }\"\r\n          :style=\"day.isToday ? 'background-color:rgba(' + t('color1rgb') + ',0.1)' : ''\"\r\n          @tap=\"day.date && showDayOrders(day)\"\r\n        >\r\n          <text class=\"day-number\" :style=\"day.isToday ? 'color:' + t('color1') : ''\">{{day.day}}</text>\r\n          <view v-if=\"day.count > 0\" class=\"order-dots\">\r\n            <view \r\n              v-if=\"day.status_count && day.status_count['1'] > 0\" \r\n              class=\"dot status-1\"\r\n              :style=\"'opacity:' + (day.status_count['1'] > 0 ? 1 : 0.3)\"\r\n            ></view>\r\n            <view \r\n              v-if=\"day.status_count && day.status_count['2'] > 0\" \r\n              class=\"dot status-2\"\r\n              :style=\"'opacity:' + (day.status_count['2'] > 0 ? 1 : 0.3)\"\r\n            ></view>\r\n            <view \r\n              v-if=\"day.status_count && day.status_count['3'] > 0\" \r\n              class=\"dot status-3\"\r\n              :style=\"'opacity:' + (day.status_count['3'] > 0 ? 1 : 0.3)\"\r\n            ></view>\r\n            <view \r\n              v-if=\"day.status_count && (day.status_count['4'] > 0 || day.status_count['5'] > 0)\" \r\n              class=\"dot status-4\"\r\n              :style=\"'opacity:' + ((day.status_count['4'] > 0 || day.status_count['5'] > 0) ? 1 : 0.3)\"\r\n            ></view>\r\n          </view>\r\n          <view v-if=\"day.count > 0\" class=\"order-count\" :style=\"'background-color:' + t('color1')\">{{day.count}}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"legend\">\r\n      <view class=\"legend-item\">\r\n        <view class=\"dot status-1\"></view>\r\n        <text>已支付</text>\r\n      </view>\r\n      <view class=\"legend-item\">\r\n        <view class=\"dot status-2\"></view>\r\n        <text>已派单</text>\r\n      </view>\r\n      <view class=\"legend-item\">\r\n        <view class=\"dot status-3\"></view>\r\n        <text>已完成</text>\r\n      </view>\r\n      <view class=\"legend-item\">\r\n        <view class=\"dot status-4\"></view>\r\n        <text>评价</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view v-if=\"showOrderList\" class=\"order-list-popup\">\r\n      <view class=\"popup-header\">\r\n        <text class=\"date-text\">{{selectedDate}}</text>\r\n        <text class=\"close-btn\" @tap=\"closeOrderList\">×</text>\r\n      </view>\r\n      <view class=\"status-filter\">\r\n        <view \r\n          v-for=\"(status, index) in statusOptions\" \r\n          :key=\"index\" \r\n          class=\"filter-item\" \r\n          :class=\"{active: selectedStatus === status.value}\"\r\n          :style=\"selectedStatus === status.value ? 'background-color:' + t('color1') : ''\"\r\n          @tap=\"filterByStatus(status.value)\"\r\n        >\r\n          {{status.label}}\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 订单列表，参考jdorderlist样式 -->\r\n      <scroll-view scroll-y class=\"order-list\">\r\n        <view v-if=\"dayOrders.length === 0\" class=\"no-orders\">\r\n          <text>当天没有订单</text>\r\n        </view>\r\n        \r\n        <block v-for=\"(item, index) in dayOrders\" :key=\"item.id\">\r\n          <view class=\"order-box\" @tap=\"viewOrderDetail(item.id)\">\r\n            <view class=\"head\">\r\n              <view class=\"f1\">\r\n                <image :src=\"pre_url+'/static/peisong/ps_time.png'\" class=\"img\"/>\r\n                <text v-if=\"item.status == 1\">已支付</text>\r\n                <text v-else-if=\"item.status == 2\">已派单</text>\r\n                <text v-else-if=\"item.status == 3\">已完成</text>\r\n                <text v-else-if=\"item.status == 4\">待评价</text>\r\n                <text v-else-if=\"item.status == 5\">已评价</text>\r\n                <text v-else>未知状态</text>\r\n                <text class=\"t1\" :style=\"'color:' + t('color1')\">{{item.yy_time}}</text>\r\n              </view>\r\n              <view class=\"flex1\"></view>\r\n              <view class=\"f2\"><text class=\"t1\">{{item.product_price}}</text>元</view>\r\n            </view>\r\n            \r\n            <view class=\"content\">\r\n              <view class=\"content-info\">\r\n                <view class=\"info-item\">\r\n                  <text class=\"label\">订单号：</text>\r\n                  <text class=\"value\">{{item.ordernum}}</text>\r\n                </view>\r\n                <view class=\"info-item\">\r\n                  <text class=\"label\">商品名称：</text>\r\n                  <text class=\"value\">{{item.product_name}}</text>\r\n                </view>\r\n                <view class=\"info-item\">\r\n                  <text class=\"label\">客户信息：</text>\r\n                  <text class=\"value\">{{item.member_name}} {{item.member_tel}}</text>\r\n                </view>\r\n                <view class=\"info-item\">\r\n                  <text class=\"label\">地址：</text>\r\n                  <text class=\"value\">{{item.address}}</text>\r\n                </view>\r\n                <view class=\"info-item\">\r\n                  <text class=\"label\">下单时间：</text>\r\n                  <text class=\"value\">{{item.create_time_formatted}}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"op\">\r\n              <view class=\"t1\" v-if=\"item.status == 1\">等待服务</view>\r\n              <view class=\"t1\" v-if=\"item.status == 2\">服务中</view>\r\n              <view class=\"t1\" v-if=\"item.status == 3\">已完成</view>\r\n              <view class=\"t1\" v-if=\"item.status == 4 || item.status == 5\">已评价</view>\r\n              <view class=\"flex1\"></view>\r\n              \r\n              <!-- 所有按钮都改为跳转到相应页面 -->\r\n              <view class=\"btn1\" v-if=\"item.status == 1\" @tap.stop=\"viewOrderDetail(item.id)\" :style=\"'background:' + t('color1')\">开始服务</view>\r\n              <view class=\"btn1\" v-if=\"item.status == 2\" @tap.stop=\"viewOrderDetail(item.id)\" :style=\"'background:' + t('color1')\">完成服务</view>\r\n              <view class=\"btn1\" v-if=\"item.status == 3\" @tap.stop=\"viewOrderDetail(item.id)\" :style=\"'background:' + t('color1')\">查看详情</view>\r\n              <view class=\"btn1\" v-if=\"item.status == 4 || item.status == 5\" @tap.stop=\"viewOrderDetail(item.id)\" :style=\"'background:' + t('color1')\">查看评价</view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 评价详情弹窗 -->\r\n    <view v-if=\"showComment\" class=\"comment-popup\">\r\n      <view class=\"popup-header\">\r\n        <text class=\"title\">评价详情</text>\r\n        <text class=\"close-btn\" @tap=\"closeComment\">×</text>\r\n      </view>\r\n      <scroll-view scroll-y class=\"comment-content\">\r\n        <view v-if=\"!commentData || (!commentData.worker_comment && !commentData.product_comment)\" class=\"no-comment\">\r\n          <text>暂无评价信息</text>\r\n        </view>\r\n        <view v-else>\r\n          <view v-if=\"commentData.worker_comment\" class=\"comment-section\">\r\n            <view class=\"section-title\">服务评价</view>\r\n            <view class=\"comment-stars\">\r\n              <text v-for=\"n in 5\" :key=\"n\" class=\"star\" :class=\"{active: n <= commentData.worker_comment.star}\">★</text>\r\n              <text class=\"level-text\">{{getLevelText(commentData.worker_comment.level)}}</text>\r\n            </view>\r\n            <view class=\"comment-text\">{{commentData.worker_comment.content}}</view>\r\n            <view v-if=\"commentData.worker_comment.content_pic_array && commentData.worker_comment.content_pic_array.length > 0\" class=\"comment-images\">\r\n              <image \r\n                v-for=\"(img, idx) in commentData.worker_comment.content_pic_array\" \r\n                :key=\"idx\" \r\n                :src=\"img\" \r\n                mode=\"aspectFill\"\r\n                @tap=\"previewImage(commentData.worker_comment.content_pic_array, idx)\"\r\n              ></image>\r\n            </view>\r\n          </view>\r\n          \r\n          <view v-if=\"commentData.product_comment\" class=\"comment-section\">\r\n            <view class=\"section-title\">商品评价</view>\r\n            <view class=\"comment-stars\">\r\n              <text v-for=\"n in 5\" :key=\"n\" class=\"star\" :class=\"{active: n <= commentData.product_comment.score}\">★</text>\r\n            </view>\r\n            <view class=\"comment-text\">{{commentData.product_comment.content}}</view>\r\n            <view v-if=\"commentData.product_comment.content_pic_array && commentData.product_comment.content_pic_array.length > 0\" class=\"comment-images\">\r\n              <image \r\n                v-for=\"(img, idx) in commentData.product_comment.content_pic_array\" \r\n                :key=\"idx\" \r\n                :src=\"img\" \r\n                mode=\"aspectFill\"\r\n                @tap=\"previewImage(commentData.product_comment.content_pic_array, idx)\"\r\n              ></image>\r\n            </view>\r\n            <view v-if=\"commentData.product_comment.reply_content\" class=\"reply-section\">\r\n              <view class=\"reply-title\" :style=\"'color:' + t('color1')\">商家回复</view>\r\n              <view class=\"reply-text\">{{commentData.product_comment.reply_content}}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <view class=\"tabbar\">\r\n      <view class=\"tabbar-bot\"></view>\r\n      <view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\r\n        <view @tap=\"goto\" data-url=\"dating\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n          <view class=\"tabbar-image-box\">\r\n            <image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/home.png'\"></image>\r\n          </view>\r\n          <view class=\"tabbar-text\">大厅</view>\r\n        </view>\r\n        <view @tap=\"goto\" data-url=\"jdorderlist\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n          <view class=\"tabbar-image-box\">\r\n            <image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/order.png'\"></image>\r\n          </view>\r\n          <view class=\"tabbar-text\">订单</view>\r\n        </view>\r\n        <view @tap=\"goto\" data-url=\"jdorderlist?st=3\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n          <view class=\"tabbar-image-box\">\r\n            <image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/orderwc.png'\"></image>\r\n          </view>\r\n          <view class=\"tabbar-text\">已完成</view>\r\n        </view>\r\n        <view v-if=\"showform\" @tap=\"goto\" data-url=\"formlog\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n          <view class=\"tabbar-image-box\">\r\n            <image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/dangan.png'\"></image>\r\n          </view>\r\n          <view class=\"tabbar-text\">档案</view>\r\n        </view>\r\n        <view @tap=\"goto\" data-url=\"calendar\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\r\n          <view class=\"tabbar-image-box\">\r\n            <image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/calendar.png'\"></image>\r\n          </view>\r\n          <view class=\"tabbar-text active\" :style=\"'color:' + t('color1')\">日历</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      pre_url: app.globalData.pre_url,\r\n      showform: 0,\r\n      currentYear: new Date().getFullYear(),\r\n      currentMonth: new Date().getMonth() + 1,\r\n      weekdays: ['日', '一', '二', '三', '四', '五', '六'],\r\n      days: [],\r\n      worker_id: 0,\r\n      showOrderList: false,\r\n      selectedDate: '',\r\n      selectedDay: null,\r\n      dayOrders: [],\r\n      selectedStatus: '', // 筛选状态\r\n      statusOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '已支付', value: '1' },\r\n        { label: '已派单', value: '2' },\r\n        { label: '已完成', value: '3' },\r\n        { label: '评价', value: '4,5' }\r\n      ],\r\n      showComment: false,\r\n      commentData: null,\r\n      loading: false,\r\n      isload: true\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n    this.getWorkerInfo();\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getCalendarData();\r\n    setTimeout(function () {\r\n      uni.stopPullDownRefresh();\r\n    }, 1000);\r\n  },\r\n  methods: {\r\n    t: function(name) {\r\n      return app.t(name);\r\n    },\r\n    getWorkerInfo() {\r\n      var that = this;\r\n      app.get('ApiYuyueWorker/my', {}, function (res) {\r\n        that.worker_id = res.worker.id;\r\n        that.showform = res.showform;\r\n        that.getCalendarData();\r\n      });\r\n    },\r\n    getCalendarData() {\r\n      var that = this;\r\n      const timestamp = new Date().getTime();\r\n      app.get('ApiYuyue/worker_calendar', {\r\n        worker_id: that.worker_id,\r\n        year: that.currentYear,\r\n        month: that.currentMonth,\r\n        _t: timestamp\r\n      }, function (res) {\r\n        if (res.status == 1) {\r\n          // 生成日历数据\r\n          that.generateCalendar(res.data);\r\n        } else {\r\n          app.alert(res.msg);\r\n        }\r\n      });\r\n    },\r\n    generateCalendar(calendarData) {\r\n      const year = this.currentYear;\r\n      const month = this.currentMonth;\r\n      const today = new Date();\r\n      const firstDay = new Date(year, month - 1, 1);\r\n      const lastDay = new Date(year, month, 0);\r\n      const daysInMonth = lastDay.getDate();\r\n      const startingDay = firstDay.getDay(); // 0 (Sunday) to 6 (Saturday)\r\n\r\n      let days = [];\r\n\r\n      // 填充月份开始前的空白\r\n      for (let i = 0; i < startingDay; i++) {\r\n        days.push({\r\n          date: '',\r\n          day: '',\r\n          isToday: false,\r\n          count: 0\r\n        });\r\n      }\r\n\r\n      // 填充日期\r\n      for (let i = 1; i <= daysInMonth; i++) {\r\n        const dateStr = `${year}-${month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`;\r\n        const dayData = calendarData.find(item => item.date === dateStr) || { count: 0, status_count: {} };\r\n        \r\n        days.push({\r\n          date: dateStr,\r\n          day: i,\r\n          isToday: year === today.getFullYear() && month === today.getMonth() + 1 && i === today.getDate(),\r\n          count: dayData.count || 0,\r\n          status_count: dayData.status_count || {}\r\n        });\r\n      }\r\n\r\n      // 填充月份结束后的空白，补齐42个格子（6行7列）\r\n      const totalSlots = 42;\r\n      const remainingSlots = totalSlots - days.length;\r\n      for (let i = 0; i < remainingSlots; i++) {\r\n        days.push({\r\n          date: '',\r\n          day: '',\r\n          isToday: false,\r\n          count: 0\r\n        });\r\n      }\r\n\r\n      this.days = days;\r\n    },\r\n    changeMonth(diff) {\r\n      let year = this.currentYear;\r\n      let month = this.currentMonth + diff;\r\n\r\n      if (month > 12) {\r\n        month = 1;\r\n        year++;\r\n      } else if (month < 1) {\r\n        month = 12;\r\n        year--;\r\n      }\r\n\r\n      this.currentYear = year;\r\n      this.currentMonth = month;\r\n      this.getCalendarData();\r\n    },\r\n    showDayOrders(day) {\r\n      if (!day.date || day.count === 0) return;\r\n      \r\n      this.selectedDate = day.date;\r\n      this.selectedDay = day;\r\n      this.selectedStatus = '';\r\n      this.getDayOrders();\r\n      this.showOrderList = true;\r\n    },\r\n    getDayOrders() {\r\n      if (!this.selectedDate) return;\r\n      \r\n      var that = this;\r\n      const timestamp = new Date().getTime();\r\n      const params = {\r\n        worker_id: that.worker_id,\r\n        date: that.selectedDate,\r\n        _t: timestamp\r\n      };\r\n      \r\n      if (that.selectedStatus) {\r\n        params.status = that.selectedStatus;\r\n      }\r\n      \r\n      app.get('ApiYuyue/worker_date_orders', params, function (res) {\r\n        if (res.status == 1) {\r\n          that.dayOrders = res.data || [];\r\n          console.log('获取日期订单成功：', that.dayOrders);\r\n        } else {\r\n          app.alert(res.msg);\r\n          that.dayOrders = [];\r\n        }\r\n      });\r\n    },\r\n    closeOrderList() {\r\n      this.showOrderList = false;\r\n      this.dayOrders = [];\r\n    },\r\n    filterByStatus(status) {\r\n      this.selectedStatus = status;\r\n      this.getDayOrders();\r\n    },\r\n    viewOrderDetail(orderId) {\r\n      app.goto('jdorderdetail?id=' + orderId);\r\n    },\r\n    setOrderStatus(orderId, status) {\r\n      let that = this;\r\n      let statusText = '';\r\n      \r\n      switch(status) {\r\n        case 2:\r\n          statusText = '开始服务';\r\n          break;\r\n        case 3:\r\n          statusText = '完成服务';\r\n          break;\r\n        default:\r\n          statusText = '更新状态';\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要' + statusText + '吗？',\r\n        success: function(res) {\r\n          if (res.confirm) {\r\n            app.post('ApiYuyueWorker/updateOrderStatus', {\r\n              order_id: orderId,\r\n              status: status\r\n            }, function(response) {\r\n              if (response.status == 1) {\r\n                app.toast(statusText + '成功');\r\n                // 更新订单状态后重新获取订单列表\r\n                that.getDayOrders();\r\n              } else {\r\n                app.alert(response.msg || statusText + '失败');\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    viewComment(orderId) {\r\n      var that = this;\r\n      const timestamp = new Date().getTime();\r\n      app.get('ApiYuyue/order_comment', {\r\n        order_id: orderId,\r\n        _t: timestamp\r\n      }, function (res) {\r\n        if (res.status == 1) {\r\n          that.commentData = res.data;\r\n          that.showComment = true;\r\n        } else {\r\n          app.alert(res.msg || '获取评价失败');\r\n        }\r\n      });\r\n    },\r\n    closeComment() {\r\n      this.showComment = false;\r\n      this.commentData = null;\r\n    },\r\n    getLevelText(level) {\r\n      switch (level) {\r\n        case 1: return '好评';\r\n        case 2: return '中评';\r\n        case 3: return '差评';\r\n        default: return '';\r\n      }\r\n    },\r\n    previewImage(images, index) {\r\n      uni.previewImage({\r\n        urls: images,\r\n        current: images[index]\r\n      });\r\n    },\r\n    goto: function (e) {\r\n      let url;\r\n      if (typeof e === 'string') {\r\n        url = e;\r\n      } else {\r\n        url = e.currentTarget.dataset.url;\r\n      }\r\n      let opentype = e.currentTarget.dataset.opentype || '';\r\n      if (!url) return false;\r\n      app.goto(url, opentype);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.calendar-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  position: relative;\r\n  font-family: \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\r\n}\r\n\r\n.header {\r\n  padding: 30rpx 30rpx;\r\n  background: #fff;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n  border-radius: 0 0 24rpx 24rpx;\r\n}\r\n\r\n.month-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 90rpx;\r\n}\r\n\r\n.selector-btn {\r\n  padding: 0 20rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #f5f7fa;\r\n  border-radius: 30rpx;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.03);\r\n  font-size: 24rpx;\r\n  color: #606266;\r\n}\r\n\r\n.selector-btn:active {\r\n  transform: scale(0.95);\r\n  background-color: #eef0f6;\r\n}\r\n\r\n.current-month {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 2rpx;\r\n  background: linear-gradient(120deg, #2c3e50, #4a5568);\r\n  -webkit-background-clip: text;\r\n  color: transparent;\r\n  position: relative;\r\n}\r\n\r\n.current-month::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -8rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 4rpx;\r\n  border-radius: 4rpx;\r\n  background: linear-gradient(90deg, rgba(0,0,0,0), rgba(0,0,0,0.1), rgba(0,0,0,0));\r\n}\r\n\r\n.calendar {\r\n  background-color: #fff;\r\n  border-radius: 24rpx;\r\n  margin: 0 20rpx 30rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);\r\n  border: 1rpx solid rgba(235, 238, 245, 0.8);\r\n}\r\n\r\n.calendar-header {\r\n  display: flex;\r\n  padding: 24rpx 0;\r\n  border-bottom: 1rpx solid rgba(235, 238, 245, 0.8);\r\n  background: linear-gradient(to bottom, #fafbfc, #fff);\r\n}\r\n\r\n.weekday {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 26rpx;\r\n  color: #909399;\r\n  font-weight: 500;\r\n}\r\n\r\n.calendar-body {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 10rpx 0;\r\n}\r\n\r\n.calendar-day {\r\n  width: 14.28%;\r\n  height: 130rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  padding: 10rpx 0;\r\n  transition: all 0.2s ease;\r\n  overflow: hidden;\r\n}\r\n\r\n.calendar-day::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 8rpx;\r\n  left: 8rpx;\r\n  right: 8rpx;\r\n  bottom: 8rpx;\r\n  border-radius: 16rpx;\r\n  z-index: -1;\r\n  transition: all 0.3s ease;\r\n  opacity: 0;\r\n}\r\n\r\n.calendar-day:active::before {\r\n  opacity: 0.05;\r\n  background-color: #000;\r\n}\r\n\r\n.has-orders::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n  z-index: -2;\r\n}\r\n\r\n.day-number {\r\n  font-size: 30rpx;\r\n  margin-bottom: 12rpx;\r\n  font-weight: 500;\r\n  position: relative;\r\n  z-index: 1;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.today .day-number {\r\n  font-weight: 600;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.empty .day-number {\r\n  opacity: 0;\r\n}\r\n\r\n.has-orders {\r\n  background-color: rgba(250, 250, 252, 0.7);\r\n}\r\n\r\n.order-dots {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 5rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.dot {\r\n  width: 10rpx;\r\n  height: 10rpx;\r\n  border-radius: 50%;\r\n  margin: 0 3rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.dot::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  border-radius: 50%;\r\n  opacity: 0.2;\r\n  background-color: inherit;\r\n  transform: scale(1.6);\r\n  z-index: -1;\r\n}\r\n\r\n.status-1 {\r\n  background-color: #ff9800; /* 已支付 */\r\n}\r\n\r\n.status-2 {\r\n  background-color: #2196f3; /* 已派单 */\r\n}\r\n\r\n.status-3 {\r\n  background-color: #4caf50; /* 已完成 */\r\n}\r\n\r\n.status-4 {\r\n  background-color: #9c27b0; /* 评价 */\r\n}\r\n\r\n.order-count {\r\n  position: absolute;\r\n  top: 8rpx;\r\n  right: 8rpx;\r\n  min-width: 32rpx;\r\n  height: 32rpx;\r\n  line-height: 32rpx;\r\n  text-align: center;\r\n  color: #fff;\r\n  border-radius: 16rpx;\r\n  font-size: 20rpx;\r\n  padding: 0 6rpx;\r\n  font-weight: 600;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 2;\r\n}\r\n\r\n.legend {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 26rpx 30rpx;\r\n  background-color: #fff;\r\n  margin: 0 20rpx 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.legend-item .dot {\r\n  margin-right: 10rpx;\r\n  width: 14rpx;\r\n  height: 14rpx;\r\n}\r\n\r\n.legend-item text {\r\n  font-size: 24rpx;\r\n  color: #606266;\r\n}\r\n\r\n.order-list-popup, .comment-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  z-index: 999;\r\n  display: flex;\r\n  flex-direction: column;\r\n  animation: slideUp 0.3s ease;\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(5%);\r\n    opacity: 0.8;\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.popup-header {\r\n  height: 110rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.date-text, .title {\r\n  font-size: 34rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.close-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 44rpx;\r\n  color: #909399;\r\n  border-radius: 50%;\r\n  background-color: #f5f7fa;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.close-btn:active {\r\n  transform: scale(0.95);\r\n  background-color: #eef0f5;\r\n}\r\n\r\n.status-filter {\r\n  display: flex;\r\n  padding: 24rpx 20rpx;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n  overflow-x: auto;\r\n  white-space: nowrap;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.filter-item {\r\n  padding: 12rpx 30rpx;\r\n  background-color: #f5f7fa;\r\n  border-radius: 30rpx;\r\n  margin-right: 20rpx;\r\n  font-size: 26rpx;\r\n  white-space: nowrap;\r\n  transition: all 0.2s ease;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.filter-item:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.filter-item.active {\r\n  color: #fff;\r\n  font-weight: 500;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.order-list, .comment-content {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n  height: 0; /* 确保flex布局下能正确计算高度 */\r\n  overflow: hidden; /* 防止内容溢出 */\r\n}\r\n\r\n.no-orders, .no-comment {\r\n  text-align: center;\r\n  padding: 120rpx 0;\r\n  color: #909399;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.no-orders::before, .no-comment::before {\r\n  content: \"📅\";\r\n  display: block;\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n  opacity: 0.5;\r\n}\r\n\r\n/* 订单列表样式，参考jdorderlist */\r\n.order-box {\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1rpx solid rgba(235, 238, 245, 0.8);\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.order-box:active {\r\n  transform: scale(0.99);\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.head {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 24rpx;\r\n  background: linear-gradient(to bottom, #fafbfc, #fff);\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n}\r\n\r\n.head .f1 {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 28rpx;\r\n  color: #303133;\r\n}\r\n\r\n.head .img {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.head .t1 {\r\n  margin-left: 10rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.flex1 {\r\n  flex: 1;\r\n}\r\n\r\n.head .f2 {\r\n  font-size: 28rpx;\r\n  color: #ff5722;\r\n  font-weight: 500;\r\n}\r\n\r\n.head .f2 .t1 {\r\n  font-weight: 600;\r\n  font-size: 34rpx;\r\n}\r\n\r\n.content {\r\n  padding: 24rpx;\r\n}\r\n\r\n.content-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  margin-bottom: 14rpx;\r\n}\r\n\r\n.info-item .label {\r\n  width: 170rpx;\r\n  color: #909399;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.info-item .value {\r\n  flex: 1;\r\n  color: #303133;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.op {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 24rpx;\r\n  border-top: 1rpx solid #f0f2f5;\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.op .t1 {\r\n  font-size: 28rpx;\r\n  color: #606266;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.op .btn1 {\r\n  padding: 14rpx 32rpx;\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  border-radius: 30rpx;\r\n  font-weight: 500;\r\n  letter-spacing: 1rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.op .btn1:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 评价详情样式 */\r\n.comment-section {\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n  border: 1rpx solid rgba(235, 238, 245, 0.8);\r\n}\r\n\r\n.section-title {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 20rpx;\r\n  color: #303133;\r\n  border-bottom: 1rpx solid #f0f2f5;\r\n  padding-bottom: 16rpx;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.comment-stars {\r\n  margin-bottom: 24rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.star {\r\n  font-size: 38rpx;\r\n  color: #dcdfe6;\r\n  margin-right: 5rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.star.active {\r\n  color: #ffba00;\r\n  text-shadow: 0 2rpx 8rpx rgba(255, 186, 0, 0.3);\r\n}\r\n\r\n.level-text {\r\n  margin-left: 20rpx;\r\n  font-size: 26rpx;\r\n  color: #ff9800;\r\n  padding: 6rpx 18rpx;\r\n  background-color: rgba(255, 152, 0, 0.08);\r\n  border-radius: 20rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.comment-text {\r\n  font-size: 28rpx;\r\n  color: #303133;\r\n  line-height: 1.6;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.comment-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.comment-images image {\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  margin-right: 12rpx;\r\n  margin-bottom: 12rpx;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.comment-images image:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.reply-section {\r\n  margin-top: 24rpx;\r\n  background-color: #f9f9fc;\r\n  padding: 20rpx;\r\n  border-radius: 16rpx;\r\n  border: 1rpx solid rgba(235, 238, 245, 0.8);\r\n}\r\n\r\n.reply-title {\r\n  font-size: 26rpx;\r\n  margin-bottom: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.reply-text {\r\n  font-size: 26rpx;\r\n  color: #606266;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Tabbar样式 */\r\n.tabbar {\r\n  position: fixed;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  z-index: 10;\r\n}\r\n\r\n.tabbar-bot {\r\n  width: 100%;\r\n  height: 20rpx;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.tabbar-bar {\r\n  width: 100%;\r\n  height: 110rpx;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  border-top: 1rpx solid rgba(235, 238, 245, 0.8);\r\n}\r\n\r\n.tabbar-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.tabbar-item:active {\r\n  transform: scale(0.96);\r\n}\r\n\r\n.tabbar-image-box {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.tabbar-icon {\r\n  width: 44rpx;\r\n  height: 44rpx;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.tabbar-text {\r\n  font-size: 22rpx;\r\n  font-weight: 400;\r\n  color: #909399;\r\n  margin-top: 4rpx;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.tabbar-text.active {\r\n  font-weight: 500;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./calendar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./calendar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115045173\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}