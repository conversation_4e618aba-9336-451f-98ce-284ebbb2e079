{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?8f59", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?1905", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?ee92", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?79d4", "uni-app:///pagesExt/order/orderzhangdan.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?abc4", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/orderzhangdan.vue?dc35"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "codtxt", "canrefund", "express_content", "selectExpressShow", "hexiao_qr", "keyword", "is_more", "cart_info", "loop_point", "loop_length", "billTypes", "billType", "billTypeLabel", "years", "months", "selected<PERSON>ear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalAmount", "orderCount", "showBillSummary", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "methods", "onBillTypeChange", "onYearChange", "onMonthChange", "add_cart", "app", "uni", "url", "that", "more_one", "obj", "proInfo", "confirm", "title", "content", "success", "close", "getdata", "params", "changetab", "scrollTop", "duration", "toclose", "orderid", "setTimeout", "tui<PERSON><PERSON>", "todel", "orderCollect", "logistics", "console", "express_oglist", "hideSelectExpressDialog", "showhxqr", "closeHxqr", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkJtxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACAC,4BACA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,GACA;QACA;UACA;YACAC;cACAC;YACA;YACA;UACA;YACAC;YACAA;UACA;QACA;MACA,EACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;MAEA;QACA;QACAC;QACAA;QACAA;QACAC;MACA;MAEA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACAN;UACAO;UACAC;UACAC;YACAP;UACA;QACA;MACA;MACA;MACAF;QACAC;MACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAT;MACAA;MACAA;;MAEA;MACA;QACAzC;QACAE;QACAQ;MACA;MACA;QACAyC;MACA;QACAA;QACAA;MACA;MAEAb;QACAG;QACA;QACA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;IACA;IACAW;MACA;MACAb;QACAc;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAjB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACApB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACArB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACAtB;QACAA;QACAA;UAAAkB;QAAA;UACAlB;UACAA;UACAmB;YACAhB;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;QACAxB;MACA;QACA/B;QACA;UACA;YACA;YACAuD;YACA;YACA;cACA;gBACAC;cACA;YACA;YACAxD;UACA;QACA;QACA;QACAuD;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9dA;AAAA;AAAA;AAAA;AAAklC,CAAgB,8jCAAG,EAAC,C;;;;;;;;;;;ACAtmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/orderzhangdan.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/orderzhangdan.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderzhangdan.vue?vue&type=template&id=34c56ca4&\"\nvar renderjs\nimport script from \"./orderzhangdan.vue?vue&type=script&lang=js&\"\nexport * from \"./orderzhangdan.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderzhangdan.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/orderzhangdan.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderzhangdan.vue?vue&type=template&id=34c56ca4&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderzhangdan.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderzhangdan.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t  <!-- 新增：年度和月度账单选择器 -->\r\n\t\t\t      <view class=\"bill-select\">\r\n\t\t\t        <picker mode=\"selector\" :range=\"billTypes\" @change=\"onBillTypeChange\">\r\n\t\t\t          <view class=\"picker\">\r\n\t\t\t            <text>{{ billTypeLabel }}</text>\r\n\t\t\t          </view>\r\n\t\t\t        </picker>\r\n\t\t\t       <!-- 年份选择器 -->\r\n\t\t\t       <picker v-if=\"billType === 'annual' || billType === 'monthly'\" mode=\"selector\" :range=\"years\" @change=\"onYearChange\">\r\n\t\t\t         <view class=\"picker\">\r\n\t\t\t           <text>{{ selectedYearLabel }}</text>\r\n\t\t\t         </view>\r\n\t\t\t       </picker>\r\n\r\n\t\t\t        <!-- 月份选择器 -->\r\n\t\t\t        <picker v-if=\"billType === 'monthly'\" mode=\"selector\" :range=\"months\" @change=\"onMonthChange\">\r\n\t\t\t          <view class=\"picker\">\r\n\t\t\t            <text>{{ selectedMonthLabel }}</text>\r\n\t\t\t          </view>\r\n\t\t\t        </picker>\r\n\t\t\t      </view>\r\n\t\t\t\r\n\t\t\t      <!-- 显示累计消费金额和购买次数 -->\r\n\t\t\t      <view v-if=\"showBillSummary\" class=\"bill-summary\">\r\n\t\t\t        <text>累计消费金额：{{ totalAmount }} 元</text>\r\n\t\t\t        <text>购买次数：{{ orderCount }} 次</text>\r\n\t\t\t      </view>\r\n\r\n\t\t\t\r\n\t\t\t<view class=\"order-content\">\r\n\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'detail?id=' + item.id\">\r\n\t\t\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.bid!=0\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + item.bid\"><image src=\"/static/img/ico-shop.png\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-else><image :src=\"item.binfo.logo\" class=\"logo-row\"></image> {{item.binfo.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==0\" class=\"st0\">待付款</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type!=1\" class=\"st1\">待发货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==1 && item.freight_type==1\" class=\"st1\">待提货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==2\" class=\"st2\">待收货</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==3\" class=\"st3\">已完成</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.status==4\" class=\"st4\">已关闭</text>\r\n\t\t\t\t\t\t</view>\r\n\t\r\n\t\t\t\t\t\t<block v-for=\"(item2, idx) in item.prolist\" :key=\"idx\">\r\n\t\t\t\t\t\t\t<!-- 这里进行修改 -->\r\n\t\t\t\t\t\t\t<view v-if=\"item2.is_yh == 0\"  class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item2.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.num}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item2.is_yh == 1\" class=\"content\" :style=\"idx+1==item.procount?'border-bottom:none':''\">\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item2.proid\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item2.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item2.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item2.yh_prices}}(优惠价格)</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"x2\">×{{item2.yh_nums}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view  class=\"bottom\"><text class=\"t2\">{{item.typenamename}}</text></view>\r\n\t\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t\t<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <span v-if=\"item.balance_price > 0 && item.balance_pay_status == 0\"  style=\"display: block; float: right;\">尾款：￥{{item.balance_price}}</span></text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==1\" style=\"color:red;padding-left:6rpx\">退款中￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==2\" style=\"color:red;padding-left:6rpx\">已退款￥{{item.refund_money}}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.refund_status==3\" style=\"color:red;padding-left:6rpx\">退款申请已驳回</text>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom\" v-if=\"item.tips!=''\">\r\n\t\t\t\t\t\t\t<text style=\"color:red\">{{item.tips}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\r\n\t\t\t\r\n\t\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t\t<image :src=\"hexiao_qr\" @tap=\"previewImage\" :data-url=\"hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\r\n\t\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\r\n\t\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\r\n\t\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\r\n\t\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t\r\n\t\t<uni-popup ref=\"more_one\" type=\"center\" >\r\n\t\t\t<uni-popup-dialog mode=\"input\" message=\"成功消息\" :duration=\"2000\" valueType=\"number\" :before-close=\"true\"  @close=\"close\" @confirm=\"confirm\"></uni-popup-dialog>\r\n\t\t\t<!-- <view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text>test</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<input type=\"number\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t <view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t</uni-popup>\r\n\t</view>\r\n\t</template>\r\n\t\r\n\t<script>\r\n\tvar app = getApp();\r\n\t\r\n\texport default {\r\n\t  data() {\r\n\t    return {\r\n\t      opt: {},\r\n\t      loading: false,\r\n\t      isload: false,\r\n\t      menuindex: -1,\r\n\t\r\n\t      st: 'all',\r\n\t      datalist: [],\r\n\t      pagenum: 1,\r\n\t      nomore: false,\r\n\t      nodata: false,\r\n\t      codtxt: \"\",\r\n\t      canrefund: 1,\r\n\t      express_content: '',\r\n\t      selectExpressShow: false,\r\n\t      hexiao_qr: '',\r\n\t      keyword: '',\r\n\t      is_more: 0,\r\n\t      cart_info: [],\r\n\t      loop_point: 0,\r\n\t      loop_length: 0,\r\n\t\r\n\t      // 新增数据\r\n\t      billTypes: ['年度账单', '月度账单'],\r\n\t      billType: '', // 'annual' 或 'monthly'\r\n\t      billTypeLabel: '请选择账单类型',\r\n\t      years: [],\r\n\t      months: [],\r\n\t      selectedYear: '',\r\n\t      selectedYearLabel: '请选择年份',\r\n\t      selectedMonth: '',\r\n\t      selectedMonthLabel: '请选择月份',\r\n\t      totalAmount: null,\r\n\t      orderCount: null,\r\n\t      showBillSummary: false,\r\n\t    };\r\n\t  },\r\n\t\r\n\t  onLoad: function (opt) {\r\n\t    this.opt = app.getopts(opt);\r\n\t    if (this.opt && this.opt.st) {\r\n\t      this.st = this.opt.st;\r\n\t    }\r\n\t    this.getdata();\r\n\t    // 初始化年份和月份选择器数据\r\n\t    const currentYear = new Date().getFullYear();\r\n\t    for (let i = 0; i < 5; i++) {\r\n\t      this.years.push(currentYear - i + '年');\r\n\t    }\r\n\t    for (let i = 1; i <= 12; i++) {\r\n\t      this.months.push(i + '月');\r\n\t    }\r\n\t  },\r\n\t  onPullDownRefresh: function () {\r\n\t    this.getdata();\r\n\t  },\r\n\t  onReachBottom: function () {\r\n\t    if (!this.nodata && !this.nomore) {\r\n\t      this.pagenum = this.pagenum + 1;\r\n\t      this.getdata(true);\r\n\t    }\r\n\t  },\r\n\t  onNavigationBarSearchInputConfirmed: function (e) {\r\n\t    this.searchConfirm({ detail: { value: e.text } });\r\n\t  },\r\n\t  methods: {\r\n\t    // 新增方法：账单类型选择\r\n\t    onBillTypeChange(e) {\r\n\t      const index = e.detail.value;\r\n\t      const types = ['annual', 'monthly'];\r\n\t      this.billType = types[index];\r\n\t      this.billTypeLabel = this.billTypes[index];\r\n\t      // 重置选择\r\n\t      this.selectedYear = '';\r\n\t      this.selectedYearLabel = '请选择年份';\r\n\t      this.selectedMonth = '';\r\n\t      this.selectedMonthLabel = '请选择月份';\r\n\t      this.totalAmount = null;\r\n\t      this.orderCount = null;\r\n\t      this.showBillSummary = false;\r\n\t      // 重新获取数据\r\n\t      this.getdata(false);\r\n\t    },\r\n\t    // 新增方法：年份选择\r\n\t    onYearChange(e) {\r\n\t      const index = e.detail.value;\r\n\t      this.selectedYearLabel = this.years[index];\r\n\t      this.selectedYear = this.years[index].replace('年', '');\r\n\t      this.showBillSummary = true;\r\n\t      // 获取数据\r\n\t      this.getdata(false);\r\n\t    },\r\n\t    // 新增方法：月份选择\r\n\t    onMonthChange(e) {\r\n\t      const index = e.detail.value;\r\n\t      this.selectedMonthLabel = this.months[index];\r\n\t      this.selectedMonth = (index + 1).toString().padStart(2, '0');\r\n\t      this.showBillSummary = true;\r\n\t      // 获取数据\r\n\t      this.getdata(false);\r\n\t    },\r\n\t    add_cart(data) {\r\n\t      let that = this;\r\n\t      app.post('ApiShop/addcart',\r\n\t        { 'ggid': data['ggid'], 'num': data['num'], 'proid': data['id'], 'tid': 0, 'glass_record_id': 0 },\r\n\t        function (res) {\r\n\t          if (res.status == 1) {\r\n\t            if (that.loop_point == that.loop_length - 1) {\r\n\t              uni.redirectTo({\r\n\t                url: '/shopPackage/shop/cart'\r\n\t              });\r\n\t              return;\r\n\t            } else {\r\n\t              that.loop_point++;\r\n\t              that.add_cart(that.cart_info[that.loop_point]);\r\n\t            }\r\n\t          }\r\n\t        }\r\n\t      );\r\n\t    },\r\n\t    more_one: function (e) {\r\n\t      let data = e.target.dataset;\r\n\t      let proInfo = [];\r\n\t      let o_index = data['key'];\r\n\t      let order = this.datalist[o_index];\r\n\t\r\n\t      let prolist = order['prolist'];\r\n\t\r\n\t      for (let i = 0; i < prolist.length; i++) {\r\n\t        let obj = {};\r\n\t        obj['id'] = prolist[i]['proid'];\r\n\t        obj['num'] = prolist[i]['num'];\r\n\t        obj['ggid'] = prolist[i]['ggid'];\r\n\t        proInfo.push(obj);\r\n\t      }\r\n\t\r\n\t      this.loop_length = proInfo.length;\r\n\t      this.loop_point = 0;\r\n\t      this.cart_info = proInfo;\r\n\t      this.add_cart(proInfo[0]);\r\n\t    },\r\n\t    confirm(value1, value2) {\r\n\t      let data = this.more_data;\r\n\t      let num = value2;\r\n\t      let that = this;\r\n\t\r\n\t      if (num == \"\" || num == 0) {\r\n\t        uni.showModal({\r\n\t          title: \"提示\",\r\n\t          content: \"请输入数量\",\r\n\t          success() {\r\n\t            that.$refs.more_one.close();\r\n\t          }\r\n\t        })\r\n\t      }\r\n\t      let url = \"/shopPackage/shop/buy?\" + \"prodata=\" + data['id'] + \",\" + data['ggid'] + \",\" + num;\r\n\t      uni.redirectTo({\r\n\t        url: url,\r\n\t      })\r\n\t      this.$refs.more_one.close();\r\n\t    },\r\n\t    close() {\r\n\t      this.$refs.more_one.close();\r\n\t    },\r\n\t    getdata: function (loadmore) {\r\n\t      if (!loadmore) {\r\n\t        this.pagenum = 1;\r\n\t        this.datalist = [];\r\n\t      }\r\n\t      var that = this;\r\n\t      var pagenum = that.pagenum;\r\n\t      var st = that.st;\r\n\t      that.nodata = false;\r\n\t      that.nomore = false;\r\n\t      that.loading = true;\r\n\t\r\n\t      // 构造请求参数\r\n\t      let params = {\r\n\t        st: st,\r\n\t        pagenum: pagenum,\r\n\t        keyword: that.keyword,\r\n\t      };\r\n\t      if (that.billType === 'annual' && that.selectedYear) {\r\n\t        params.year = that.selectedYear;\r\n\t      } else if (that.billType === 'monthly' && that.selectedYear && that.selectedMonth) {\r\n\t        params.year = that.selectedYear;\r\n\t        params.month = that.selectedMonth;\r\n\t      }\r\n\t\r\n\t      app.post('ApiOrder/orderzhangdan', params, function (res) {\r\n\t        that.loading = false;\r\n\t        var data = res.datalist;\r\n\t        if (pagenum == 1) {\r\n\t          that.codtxt = res.codtxt;\r\n\t          that.canrefund = res.canrefund;\r\n\t          that.datalist = data;\r\n\t          if (data.length == 0) {\r\n\t            that.nodata = true;\r\n\t          }\r\n\t          that.loaded();\r\n\t        } else {\r\n\t          if (data.length == 0) {\r\n\t            that.nomore = true;\r\n\t          } else {\r\n\t            var datalist = that.datalist;\r\n\t            var newdata = datalist.concat(data);\r\n\t            that.datalist = newdata;\r\n\t          }\r\n\t        }\r\n\t        // 更新累计消费金额和购买次数\r\n\t        if (res.totalAmount !== undefined) {\r\n\t          that.totalAmount = res.totalAmount;\r\n\t        }\r\n\t        if (res.orderCount !== undefined) {\r\n\t          that.orderCount = res.orderCount;\r\n\t        }\r\n\t      });\r\n\t    },\r\n\t    changetab: function (st) {\r\n\t      this.st = st;\r\n\t      uni.pageScrollTo({\r\n\t        scrollTop: 0,\r\n\t        duration: 0\r\n\t      });\r\n\t      this.getdata();\r\n\t    },\r\n\t    toclose: function (e) {\r\n\t      var that = this;\r\n\t      var orderid = e.currentTarget.dataset.id;\r\n\t      app.confirm('确定要关闭该订单吗?', function () {\r\n\t        app.showLoading('提交中');\r\n\t        app.post('ApiOrder/closeOrder', { orderid: orderid }, function (data) {\r\n\t          app.showLoading(false);\r\n\t          app.success(data.msg);\r\n\t          setTimeout(function () {\r\n\t            that.getdata();\r\n\t          }, 1000);\r\n\t        });\r\n\t      });\r\n\t    },\r\n\t    tuikuang: function (e) {\r\n\t      var that = this;\r\n\t      var orderid = e.currentTarget.dataset.id;\r\n\t      app.confirm('确定退框吗?', function () {\r\n\t        app.showLoading('退框中');\r\n\t        app.post('ApiOrder/delOrders', { orderid: orderid }, function (data) {\r\n\t          app.showLoading(false);\r\n\t          app.success(data.msg);\r\n\t          setTimeout(function () {\r\n\t            that.getdata();\r\n\t          }, 1000);\r\n\t        });\r\n\t      });\r\n\t    },\r\n\t    todel: function (e) {\r\n\t      var that = this;\r\n\t      var orderid = e.currentTarget.dataset.id;\r\n\t      app.confirm('确定要删除该订单吗?', function () {\r\n\t        app.showLoading('删除中');\r\n\t        app.post('ApiOrder/delOrder', { orderid: orderid }, function (data) {\r\n\t          app.showLoading(false);\r\n\t          app.success(data.msg);\r\n\t          setTimeout(function () {\r\n\t            that.getdata();\r\n\t          }, 1000);\r\n\t        });\r\n\t      });\r\n\t    },\r\n\t    orderCollect: function (e) {\r\n\t      var that = this;\r\n\t      var orderid = e.currentTarget.dataset.id;\r\n\t      app.confirm('确定要收货吗?', function () {\r\n\t        app.showLoading('提交中');\r\n\t        app.post('ApiOrder/orderCollect', { orderid: orderid }, function (data) {\r\n\t          app.showLoading(false);\r\n\t          app.success(data.msg);\r\n\t          setTimeout(function () {\r\n\t            that.getdata();\r\n\t          }, 1000);\r\n\t        });\r\n\t      });\r\n\t    },\r\n\t    logistics: function (e) {\r\n\t      var index = e.currentTarget.dataset.index;\r\n\t      var orderinfo = this.datalist[index];\r\n\t      var express_com = orderinfo.express_com;\r\n\t      var express_no = orderinfo.express_no;\r\n\t      var express_content = orderinfo.express_content;\r\n\t      var express_type = orderinfo.express_type;\r\n\t      var prolist = orderinfo.prolist;\r\n\t      console.log(express_content);\r\n\t      if (!express_content) {\r\n\t        app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no + '&type=' + express_type);\r\n\t      } else {\r\n\t        express_content = JSON.parse(express_content);\r\n\t        for (var i in express_content) {\r\n\t          if (express_content[i].express_ogids) {\r\n\t            var express_ogids = (express_content[i].express_ogids).split(',');\r\n\t            console.log(express_ogids);\r\n\t            var express_oglist = [];\r\n\t            for (var j in prolist) {\r\n\t              if (app.inArray(prolist[j].id + '', express_ogids)) {\r\n\t                express_oglist.push(prolist[j]);\r\n\t              }\r\n\t            }\r\n\t            express_content[i].express_oglist = express_oglist;\r\n\t          }\r\n\t        }\r\n\t        this.express_content = express_content;\r\n\t        console.log(express_content);\r\n\t        this.$refs.dialogSelectExpress.open();\r\n\t      }\r\n\t    },\r\n\t    hideSelectExpressDialog: function () {\r\n\t      this.$refs.dialogSelectExpress.close();\r\n\t    },\r\n\t    showhxqr: function (e) {\r\n\t      this.hexiao_qr = e.currentTarget.dataset.hexiao_qr;\r\n\t      this.$refs.dialogHxqr.open();\r\n\t    },\r\n\t    closeHxqr: function () {\r\n\t      this.$refs.dialogHxqr.close();\r\n\t    },\r\n\t    searchConfirm: function (e) {\r\n\t      this.keyword = e.detail.value;\r\n\t      this.getdata(false);\r\n\t    }\r\n\t  }\r\n\t};\r\n\t</script>\r\n\r\n\t<style>\r\n\t.container{ width:100%;}\r\n\t.topsearch{width:94%;margin:10rpx 3%;}\r\n\t.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n\t.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n\t.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\t.order-content{display:flex;flex-direction:column}\r\n\t.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n\t.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}\r\n\t.order-box .head .f1{display:flex;align-items:center;color:#333}\r\n\t.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}\r\n\t.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }\r\n\t.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }\r\n\t.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }\r\n\t.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }\r\n\t.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }\r\n\t\r\n\t.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}\r\n\t.order-box .content:last-child{ border-bottom: 0; }\r\n\t.order-box .content image{ width: 140rpx; height: 140rpx;}\r\n\t.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n\t.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n\t.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n\t.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n\t.order-box .content .detail .x1{ flex:1}\r\n\t.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\t\r\n\t.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\t.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\r\n\t\r\n\t.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\r\n\t.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\r\n\t\r\n\t.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n\t.hxqrbox .img{width:400rpx;height:400rpx}\r\n\t.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n\t.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\t\r\n\t/* 新增：账单选择器样式 */\r\n\t.bill-select {\r\n\t  display: flex;\r\n\t  flex-direction: row;\r\n\t  align-items: center;\r\n\t  padding: 10rpx 3%;\r\n\t  background-color: #fff;\r\n\t}\r\n\t\r\n\t.bill-select .picker {\r\n\t  flex: 1;\r\n\t  padding: 20rpx;\r\n\t  margin-right: 10rpx;\r\n\t  background-color: #f5f5f5;\r\n\t  border-radius: 10rpx;\r\n\t}\r\n\t\r\n\t.bill-summary {\r\n\t  padding: 20rpx 3%;\r\n\t  background-color: #fff;\r\n\t  margin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.bill-summary text {\r\n\t  display: block;\r\n\t  font-size: 28rpx;\r\n\t  color: #333;\r\n\t  margin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.topsearch {\r\n\t  width: 94%;\r\n\t  margin: 10rpx 3%;\r\n\t}\r\n\t\r\n\t.topsearch .f1 {\r\n\t  height: 60rpx;\r\n\t  border-radius: 30rpx;\r\n\t  border: 0;\r\n\t  background-color: #fff;\r\n\t  flex: 1;\r\n\t}\r\n\t\r\n\t.topsearch .f1 .img {\r\n\t  width: 24rpx;\r\n\t  height: 24rpx;\r\n\t  margin-left: 10px;\r\n\t}\r\n\t\r\n\t.topsearch .f1 input {\r\n\t  height: 100%;\r\n\t  flex: 1;\r\n\t  padding: 0 20rpx;\r\n\t  font-size: 28rpx;\r\n\t  color: #333;\r\n\t}\r\n\t\r\n\t.order-content {\r\n\t  display: flex;\r\n\t  flex-direction: column;\r\n\t}\r\n\t\r\n\t.order-box {\r\n\t  width: 94%;\r\n\t  margin: 10rpx 3%;\r\n\t  padding: 6rpx 3%;\r\n\t  background: #fff;\r\n\t  border-radius: 8px;\r\n\t}\r\n\t</style>\r\n\t", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderzhangdan.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderzhangdan.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103547\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}