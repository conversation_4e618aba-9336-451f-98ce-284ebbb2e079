require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/paidui/paidui"],{"0b83":function(t,n,o){"use strict";o.d(n,"b",(function(){return a})),o.d(n,"c",(function(){return e})),o.d(n,"a",(function(){return i}));var i={ddTab:function(){return o.e("components/dd-tab/dd-tab").then(o.bind(null,"caa1"))},nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},nomore:function(){return o.e("components/nomore/nomore").then(o.bind(null,"3892"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},a=function(){var t=this,n=t.$createElement,o=(t._self._c,t.isload?t.t("color1"):null),i=t.isload?t.t("排队中"):null,a=t.isload?t.t("已完成"):null,e=t.isload?t.t("商品"):null,s=t.isload?t.t("门店"):null,u=t.isload?t.t("买单"):null,r=t.isload?t.t("独立排队"):null,c=t.isload?t.datalist&&t.datalist.length>0:null,l=t.isload&&c?t.__map(t.datalist,(function(n,o){var i=t.__get_orig(n),a=0==n.status?t.t("color1"):null,e=1==n.status?t.t("color1"):null,s=2==n.status?t.t("color1"):null,u=n.name?t.timestampToDate(n.createtime):null,r=Number(n.bili).toFixed(0);return{$orig:i,m7:a,m8:e,m9:s,m10:u,g1:r}})):null;t.$mp.data=Object.assign({},{$root:{m0:o,m1:i,m2:a,m3:e,m4:s,m5:u,m6:r,g0:c,l0:l}})},e=[]},3659:function(t,n,o){"use strict";o.r(n);var i=o("8a78"),a=o.n(i);for(var e in i)["default"].indexOf(e)<0&&function(t){o.d(n,t,(function(){return i[t]}))}(e);n["default"]=a.a},3670:function(t,n,o){"use strict";(function(t,n){var i=o("47a9");o("06e9");i(o("3240"));var a=i(o("39ec"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"39ec":function(t,n,o){"use strict";o.r(n);var i=o("0b83"),a=o("3659");for(var e in a)["default"].indexOf(e)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(e);o("e881");var s=o("828b"),u=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=u.exports},"8a78":function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),i={data:function(){return{opt:{},pageinfo:[],loading:!1,isload:!1,modalVisible:!1,selectedOption:null,menuindex:-1,showModal:!1,nodata:!1,nomore:!1,datalist:[],textset:{},pagenum:1,userinfo:[],set:[],st:1,st1:1,primary_color:"",options:[],secondary_color:"",xuanze:""}},onLoad:function(t){this.opt=o.getopts(t),this.st=this.opt.st||1;this.getdata(),this.primary_color=o.getCache("primary_color"),this.secondary_color=o.getCache("secondary_color")},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{numFilter:function(t){return parseFloat(t).toFixed(2)},timestampToDate:function(t){var n=new Date(1e3*t),o=n.getFullYear(),i=(n.getMonth()+1).toString().padStart(2,"0"),a=n.getDate().toString().padStart(2,"0"),e=n.getHours().toString().padStart(2,"0"),s=n.getMinutes().toString().padStart(2,"0");n.getSeconds().toString().padStart(2,"0");return"".concat(o,"-").concat(i,"-").concat(a," ").concat(e,":").concat(s)},getdata:function(n){n||(this.pagenum=1,this.datalist=[]);var i=this,a=i.st,e=i.st1,s=i.pagenum;i.loading=!0,i.nodata=!1,i.nomore=!1,o.post("ApiPaidui/paidui",{st:a,st1:e,pagenum:s},(function(n){i.loading=!1;var a=n.data;i.userinfo=n.userinfo,i.set=n.set;var e=[];if(i.set.paidui_duihuan_money>0){var u={value:"reason1",text:"余额",amount:"¥ 0.05",description:"当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。"};e.push(u)}if(i.set.paidui_duihuan>0){u={value:"reason2",text:"积分",amount:"100积分",description:"当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。"};e.push(u)}if(""!=i.set.paidui_duihuan_choujiang){u={value:"reason3",text:"抽奖",amount:"1次",description:"当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。"};e.push(u)}if(i.options=e,1==s)i.textset=o.globalData.textset,t.setNavigationBarTitle({title:i.t("排队补贴")}),i.datalist=a,0==a.length&&(i.nodata=!0),i.loaded();else if(0==a.length)i.nomore=!0;else{var r=i.datalist,c=r.concat(a);i.datalist=c}console.log("tg",i.datalist)}))},showExplanation:function(){this.showModal=!0},closeExplanation:function(){this.showModal=!1},duihuantixian:function(t){var n=this;n.loading=!0,o.post("ApiPaidui/tixian",{id:t},(function(t){n.loading=!1,n.getdata(),0!=t.status?o.error("兑换成功"):o.error(t.msg)}))},showModal2:function(){this.modalVisible=!0},hideModal:function(){this.modalVisible=!1,this.selectedOption=null},selectOption2:function(t,n,o,i,a,e){this.xuanze=o,"reason1"==o?t.amount="¥"+i:"reason2"==o?t.amount=a+"积分":"reason3"==o&&(t.amount=e+"次"),this.selectedOption=t},backToOptions:function(){this.selectedOption=null},confirmEndQueue:function(t){this.hideModal();"reason1"==this.xuanze?this.duihuantixian2(t):"reason2"==this.xuanze?this.duihuantixian(t):"reason3"==this.xuanze&&this.duihuantixian3(t)},duihuantixian2:function(t){var n=this;n.loading=!0,o.post("ApiPaidui/tixian2",{id:t},(function(t){n.loading=!1,n.getdata(),0!=t.status?o.error("兑换成功"):o.error(t.msg)}))},duihuantixian3:function(t){var n=this;n.loading=!0,o.post("ApiPaidui/tixian3",{id:t},(function(t){n.loading=!1,n.getdata(),0!=t.status?(o.success(t.msg),setTimeout((function(){o.goto(t.data)}),1e3)):o.error(t.msg)}))},topay:function(t){var n=this;n.loading=!0,o.post("ApiPaidui/zhuanhua",{},(function(t){n.loading=!1,n.getdata(),0!=t.status?o.error("转化成功"):o.error(t.msg)}))},changetab:function(n){this.st=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},changetab2:function(n){this.st1=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},showDistributionFlow:function(t){var n=this;o.post("ApiPaidui/paiduiDistributionFlow",{paidui_id:t.id},(function(t){1==t.status?n.showDistributionModal(t.data):o.error(t.msg||"获取分红流向失败")}))},showDistributionModal:function(n){var o="【分红流向详情】\n\n";o+="排队进度："+n.progress_info.progress_percent+"%\n",o+="目标金额：￥"+n.progress_info.target_amount+"\n",o+="已获得：￥"+n.progress_info.received_amount+"\n",o+="剩余：￥"+n.progress_info.remaining_amount+"\n\n",o+="分红记录：\n",n.flow_records&&n.flow_records.length>0?n.flow_records.forEach((function(t){o+="• "+t.time_text+" +￥"+t.money+"\n",o+="  来源："+t.source_type+"\n"})):o+="暂无分红记录\n",t.showModal({title:"分红流向详情",content:o,showCancel:!1,confirmText:"知道了"})},checkDataConsistency:function(){var n=this;n.loading=!0,o.post("ApiPaidui/checkDataConsistency",{},(function(i){n.loading=!1,1==i.status?i.need_fix?t.showModal({title:"数据不一致",content:i.msg+"\n\n是否立即修复？",showCancel:!0,confirmText:"立即修复",cancelText:"稍后处理",success:function(t){t.confirm&&n.syncPaiduiData()}}):t.showToast({title:"数据一致性正常",icon:"success"}):o.error(i.msg||"检查失败")}),(function(){n.loading=!1,o.error("网络请求失败")}))},syncPaiduiData:function(){var n=this;n.loading=!0,o.post("ApiPaidui/syncPaiduiData",{},(function(i){n.loading=!1,1==i.status?t.showModal({title:"修复完成",content:i.msg,showCancel:!1,confirmText:"确定",success:function(){n.getdata()}}):o.error(i.msg||"修复失败")}),(function(){n.loading=!1,o.error("网络请求失败")}))},showDataFixMenu:function(){var n=this;t.showActionSheet({itemList:["检查数据一致性","修复数据","取消"],success:function(o){0==o.tapIndex?n.checkDataConsistency():1==o.tapIndex&&t.showModal({title:"确认修复",content:"此操作将修复分红记录与排队记录的数据不一致问题，是否继续？",showCancel:!0,confirmText:"确认修复",cancelText:"取消",success:function(t){t.confirm&&n.syncPaiduiData()}})}})}}};n.default=i}).call(this,o("df3c")["default"])},d4b0:function(t,n,o){},e881:function(t,n,o){"use strict";var i=o("d4b0"),a=o.n(i);a.a}},[["3670","common/runtime","common/vendor"]]]);