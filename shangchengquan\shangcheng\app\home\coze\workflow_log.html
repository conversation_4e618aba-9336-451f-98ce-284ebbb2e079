<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>工作流执行日志</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> 工作流执行日志</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">批量删除</button>
							<button class="layui-btn layui-btn-warm layuiadmin-btn-list" id="batchQuery">批量查询状态</button>
							<button class="layui-btn layui-btn-normal layuiadmin-btn-list" id="refreshTable">刷新</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">工作流</label>
								<div class="layui-input-block" style="width:150px;margin-left:90px">
									<select name="workflow_id" lay-search="">
										<option value="">选择工作流</option>
										{volist name="workflows" id="workflow"}
										<option value="{$workflow.workflow_id}">{$workflow.name} ({$workflow.workflow_id})</option>
										{/volist}
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部状态</option>
										<option value="completed">已完成</option>
										<option value="running">运行中</option>
										<option value="failed">失败</option>
										<option value="unknown">未知</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-content-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<!-- 调试按钮 -->
							<button type="button" class="layui-btn layui-btn-normal" onclick="debugTableData()" style="margin-bottom: 10px;">调试：查看表格数据</button>
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>

	{include file="public/js"/}
	<script type="text/html" id="statusTpl">
		{{# if(d.status == 'completed') { }}
			<span class="layui-badge layui-bg-green">已完成</span>
		{{# } else if(d.status == 'running' || d.status == 'Running') { }}
			<span class="layui-badge layui-bg-blue">运行中</span>
		{{# } else if(d.status == 'failed') { }}
			<span class="layui-badge layui-bg-red">失败</span>
		{{# } else { }}
			<span class="layui-badge layui-bg-gray">{{d.status || '未知'}}</span>
		{{# } }}
	</script>
	<script type="text/html" id="asyncTpl">
		{{# if(d.is_async) { }}
			<span class="layui-badge layui-bg-blue">异步</span>
		{{# } else { }}
			<span class="layui-badge layui-bg-green">同步</span>
		{{# } }}
	</script>
	<script>
  var table = layui.table;
	var datawhere = {};
	var currentTableData = []; // 全局变量存储当前表格数据
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,id: 'tabledata' // 添加表格ID，用于获取数据
    ,url: '{:url("getWorkflowHistory")}' //数据接口
    ,page: true //开启分页
    ,done: function(res, curr, count){
			// 表格渲染完成后，保存数据到全局变量
			console.log('表格渲染完成，数据:', res.data);
			currentTableData = res.data || [];
		}
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'workflow_id', title: '工作流ID',width:150},
      {field: 'execute_id', title: '执行ID',width:150},
      {field: 'status', title: '状态',width:100,templet: '#statusTpl'},
      {field: 'is_async', title: '类型',width:80,templet: '#asyncTpl'},
      {field: 'parameters_text', title: '参数',width:120,templet:function(d){
				if(d.parameters_text && d.parameters_text !== '无参数'){
					return '<a href="javascript:;" onclick="showParams('+d.id+')" class="layui-text">查看参数</a>';
				}else{
					return '<span class="layui-text layui-text-muted">无参数</span>';
				}
      }},
      {field: 'output_text', title: '输出',width:120,templet:function(d){
				if(d.output_text && d.output_text !== '无输出' && d.output_text !== '无结果'){
					return '<a href="javascript:;" onclick="showOutput('+d.id+')" class="layui-text">查看输出</a>';
				}else{
					return '<span class="layui-text layui-text-muted">'+(d.output_text||'无输出')+'</span>';
				}
      }},
      {field: 'debug_url', title: '调试',width:100,templet:function(d){
				if(d.debug_url){
					return '<a href="'+d.debug_url+'" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal"><i class="layui-icon layui-icon-link"></i>调试</a>';
				}else{
					return '<span class="layui-text layui-text-muted">无</span>';
				}
      }},
      {field: 'create_time_format', title: '创建时间',width:160},
      {field: 'update_time_format', title: '更新时间',width:160},
      {field: 'operation', title: '操作',templet:function(d){
				var html = '';
				html += '<button class="table-btn" onclick="showDetail('+d.id+')">详情</button>';
				if(d.status == 'running' || d.status == 'Running'){
					html += '<button class="table-btn" onclick="queryWorkflowStatus('+d.id+')">查询状态</button>';
				}
				html += '<button class="table-btn" onclick="datadel(\''+d.id+'\')">删除</button>';
				return html;
      },width:200}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-content-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})

	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("{:url('delWorkflowLog')}",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.code);
				tableIns.reload()
			})
		});
	}

	// 刷新表格
	$('#refreshTable').on('click', function(){
		tableIns.reload();
	});

	// 批量查询状态
	$('#batchQuery').on('click', function(){
		var loadIndex = layer.load(2, {content: '正在查询状态...'});

		$.post('{:url("batchQueryStatus")}', {}, function(res){
			layer.close(loadIndex);
			if(res.code == 1){
				layer.msg('批量查询完成，共更新 ' + (res.data.updated_count||0) + ' 条记录', {icon: 1});
				tableIns.reload();
			} else {
				layer.msg(res.msg, {icon: 2});
			}
		}).fail(function(){
			layer.close(loadIndex);
			layer.msg('请求失败', {icon: 2});
		});
	});

	// 显示详情
	function showDetail(id) {
		console.log('=== 显示详情，查找ID:', id, '类型:', typeof id);

		var data = null;

		// 方法1: 使用全局变量currentTableData（最可靠）
		console.log('方法1 - currentTableData:', currentTableData);
		if(currentTableData && currentTableData.length > 0){
			for(var i = 0; i < currentTableData.length; i++){
				console.log('检查全局数据[' + i + ']:', currentTableData[i]);
				if(currentTableData[i].id == id || currentTableData[i].id === String(id) || currentTableData[i].id === Number(id)){
					console.log('从全局数据找到:', currentTableData[i]);
					data = currentTableData[i];
					break;
				}
			}
		}

		// 方法2: 使用table.cache获取数据
		if(!data) {
			try {
				var cacheData = table.cache.tabledata;
				console.log('方法2 - table.cache.tabledata:', cacheData);
				if(cacheData && cacheData.length > 0){
					for(var i = 0; i < cacheData.length; i++){
						if(cacheData[i].id == id || cacheData[i].id === String(id) || cacheData[i].id === Number(id)){
							console.log('从cache找到数据:', cacheData[i]);
							data = cacheData[i];
							break;
						}
					}
				}
			} catch(e) {
				console.log('方法2失败:', e);
			}
		}

		// 方法3: 使用tableIns.config.data获取数据（备用）
		if(!data) {
			try {
				var configData = tableIns.config.data || [];
				console.log('方法3 - tableIns.config.data:', configData);
				for(var i = 0; i < configData.length; i++){
					if(configData[i].id == id || configData[i].id === String(id) || configData[i].id === Number(id)){
						console.log('从config找到数据:', configData[i]);
						data = configData[i];
						break;
					}
				}
			} catch(e) {
				console.log('方法3失败:', e);
			}
		}

		if(!data) {
			console.log('=== 未找到数据，ID:', id, ' ===');
			console.log('当前全局数据:', currentTableData);
			layer.msg('数据不存在，请刷新页面后重试', {icon: 2});
			return;
		}

		console.log('找到数据，显示详情:', data);

		var content = '<div style="padding: 20px;">' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">工作流ID:</label>' +
			'<div class="layui-input-block">' + (data.workflow_id || '无') + '</div>' +
			'</div>' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">执行ID:</label>' +
			'<div class="layui-input-block">' + (data.execute_id || '无') + '</div>' +
			'</div>' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">执行状态:</label>' +
			'<div class="layui-input-block">' + (data.status || '未知') + '</div>' +
			'</div>' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">执行类型:</label>' +
			'<div class="layui-input-block">' + (data.is_async ? '异步' : '同步') + '</div>' +
			'</div>' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">创建时间:</label>' +
			'<div class="layui-input-block">' + (data.create_time_format || '无') + '</div>' +
			'</div>' +
			'<div class="layui-form-item">' +
			'<label class="layui-form-label">更新时间:</label>' +
			'<div class="layui-input-block">' + (data.update_time_format || '无') + '</div>' +
			'</div>';

		if(data.debug_url) {
			content += '<div class="layui-form-item">' +
				'<label class="layui-form-label">调试链接:</label>' +
				'<div class="layui-input-block"><a href="' + data.debug_url + '" target="_blank">打开调试页面</a></div>' +
				'</div>';
		}

		content += '</div>';

		layer.open({
			type: 1,
			title: '执行详情',
			content: content,
			area: ['600px', '500px'],
			btn: ['关闭'],
			yes: function(index) {
				layer.close(index);
			}
		});
	}

	// 显示参数
	function showParams(id) {
		console.log('=== 显示参数，查找ID:', id, '类型:', typeof id);

		// 直接使用前端数据，但使用更可靠的获取方法
		showParamsFromFrontend(id);
	}

	// 从前端表格数据获取参数
	function showParamsFromFrontend(id) {
		console.log('=== 从前端获取参数，查找ID:', id);

		var data = null;

		// 方法1: 使用全局变量currentTableData（最可靠）
		console.log('方法1 - currentTableData:', currentTableData);
		if(currentTableData && currentTableData.length > 0){
			for(var i = 0; i < currentTableData.length; i++){
				console.log('检查全局数据[' + i + ']:', currentTableData[i]);
				if(currentTableData[i].id == id || currentTableData[i].id === String(id) || currentTableData[i].id === Number(id)){
					console.log('从全局数据找到:', currentTableData[i]);
					data = currentTableData[i];
					break;
				}
			}
		}

		// 方法2: 使用table.cache获取数据
		if(!data) {
			try {
				var cacheData = table.cache.tabledata;
				console.log('方法2 - table.cache.tabledata:', cacheData);
				if(cacheData && cacheData.length > 0){
					for(var i = 0; i < cacheData.length; i++){
						console.log('检查缓存数据[' + i + ']:', cacheData[i]);
						if(cacheData[i].id == id || cacheData[i].id === String(id) || cacheData[i].id === Number(id)){
							console.log('从cache找到数据:', cacheData[i]);
							data = cacheData[i];
							break;
						}
					}
				}
			} catch(e) {
				console.log('方法2失败:', e);
			}
		}

		// 方法3: 使用tableIns.config.data获取数据
		if(!data) {
			try {
				var configData = tableIns.config.data || [];
				console.log('方法3 - tableIns.config.data:', configData);
				for(var i = 0; i < configData.length; i++){
					console.log('检查配置数据[' + i + ']:', configData[i]);
					if(configData[i].id == id || configData[i].id === String(id) || configData[i].id === Number(id)){
						console.log('从config找到数据:', configData[i]);
						data = configData[i];
						break;
					}
				}
			} catch(e) {
				console.log('方法3失败:', e);
			}
		}

		if(!data) {
			console.log('=== 未找到数据，ID:', id, ' ===');
			console.log('当前全局数据:', currentTableData);
			console.log('表格缓存数据:', table.cache);
			console.log('表格配置数据:', tableIns.config);
			layer.msg('数据不存在，请刷新页面后重试', {icon: 2});
			return;
		}

		console.log('找到数据，显示参数:', data.parameters_text);
		layer.open({
			type: 1,
			title: '执行参数',
			content: '<div style="padding: 20px;"><pre>' + (data.parameters_text || '无参数') + '</pre></div>',
			area: ['600px', '400px'],
			btn: ['关闭'],
			yes: function(index) {
				layer.close(index);
			}
		});
	}

	// 显示输出
	function showOutput(id) {
		console.log('=== 显示输出，查找ID:', id, '类型:', typeof id);

		var data = null;

		// 方法1: 使用全局变量currentTableData（最可靠）
		console.log('方法1 - currentTableData:', currentTableData);
		if(currentTableData && currentTableData.length > 0){
			for(var i = 0; i < currentTableData.length; i++){
				if(currentTableData[i].id == id || currentTableData[i].id === String(id) || currentTableData[i].id === Number(id)){
					data = currentTableData[i];
					break;
				}
			}
		}

		// 方法2: 使用table.cache获取数据
		if(!data) {
			try {
				var cacheData = table.cache.tabledata;
				if(cacheData && cacheData.length > 0){
					for(var i = 0; i < cacheData.length; i++){
						if(cacheData[i].id == id || cacheData[i].id === String(id) || cacheData[i].id === Number(id)){
							data = cacheData[i];
							break;
						}
					}
				}
			} catch(e) {
				console.log('方法2失败:', e);
			}
		}

		if(!data) {
			console.log('=== 未找到数据，ID:', id, ' ===');
			layer.msg('数据不存在，请刷新页面后重试', {icon: 2});
			return;
		}

		layer.open({
			type: 1,
			title: '执行输出',
			content: '<div style="padding: 20px;"><pre>' + (data.output_text || '无输出') + '</pre></div>',
			area: ['600px', '400px'],
			btn: ['关闭'],
			yes: function(index) {
				layer.close(index);
			}
		});
	}

	// 查询工作流状态
	function queryWorkflowStatus(id) {
		console.log('=== 查询工作流状态，查找ID:', id, '类型:', typeof id);

		var data = null;

		// 方法1: 使用全局变量currentTableData（最可靠）
		console.log('方法1 - currentTableData:', currentTableData);
		if(currentTableData && currentTableData.length > 0){
			for(var i = 0; i < currentTableData.length; i++){
				if(currentTableData[i].id == id || currentTableData[i].id === String(id) || currentTableData[i].id === Number(id)){
					data = currentTableData[i];
					break;
				}
			}
		}

		// 方法2: 使用table.cache获取数据
		if(!data) {
			try {
				var cacheData = table.cache.tabledata;
				if(cacheData && cacheData.length > 0){
					for(var i = 0; i < cacheData.length; i++){
						if(cacheData[i].id == id || cacheData[i].id === String(id) || cacheData[i].id === Number(id)){
							data = cacheData[i];
							break;
						}
					}
				}
			} catch(e) {
				console.log('方法2失败:', e);
			}
		}

		if(!data) {
			console.log('=== 未找到数据，ID:', id, ' ===');
			layer.msg('数据不存在，请刷新页面后重试', {icon: 2});
			return;
		}

		if(!data.workflow_id || !data.execute_id) {
			layer.msg('缺少必要的查询参数', {icon: 2});
			return;
		}

		var loadIndex = layer.load(2, {content: '正在查询状态...'});

		$.post('{:url("queryWorkflowStatus")}', {
			id: data.id,
			workflow_id: data.workflow_id,
			execute_id: data.execute_id
		}, function(res){
			layer.close(loadIndex);
			if(res.code == 1){
				layer.msg('状态查询成功', {icon: 1});
				tableIns.reload(); // 刷新表格显示最新状态

				// 显示查询结果
				if(res.data && res.data.status) {
					var statusText = res.data.status === 'Success' ? '已完成' :
									 res.data.status === 'Failed' ? '失败' :
									 res.data.status === 'Running' ? '运行中' : res.data.status;
					layer.msg('当前状态: ' + statusText, {icon: 1, time: 3000});
				}
			} else {
				layer.msg(res.msg, {icon: 2});
			}
		}).fail(function(){
			layer.close(loadIndex);
			layer.msg('查询请求失败', {icon: 2});
		});
	}

	// 调试函数：显示当前表格数据状态
	function debugTableData() {
		var debugInfo = '=== 表格数据调试信息 ===\n';
		debugInfo += '1. 全局变量 currentTableData:\n';
		debugInfo += JSON.stringify(currentTableData, null, 2) + '\n\n';

		try {
			debugInfo += '2. table.cache.tabledata:\n';
			debugInfo += JSON.stringify(table.cache.tabledata, null, 2) + '\n\n';
		} catch(e) {
			debugInfo += '2. table.cache.tabledata: 获取失败 - ' + e.message + '\n\n';
		}

		try {
			debugInfo += '3. tableIns.config.data:\n';
			debugInfo += JSON.stringify(tableIns.config.data, null, 2) + '\n\n';
		} catch(e) {
			debugInfo += '3. tableIns.config.data: 获取失败 - ' + e.message + '\n\n';
		}

		try {
			debugInfo += '4. table.getData("tabledata"):\n';
			debugInfo += JSON.stringify(table.getData('tabledata'), null, 2) + '\n\n';
		} catch(e) {
			debugInfo += '4. table.getData: 获取失败 - ' + e.message + '\n\n';
		}

		debugInfo += '5. 表格实例信息:\n';
		debugInfo += 'tableIns存在: ' + (tableIns ? '是' : '否') + '\n';
		debugInfo += 'table对象存在: ' + (table ? '是' : '否') + '\n';

		// 显示在弹窗中
		layer.open({
			type: 1,
			title: '表格数据调试信息',
			content: '<div style="padding: 20px;"><pre style="max-height: 400px; overflow-y: auto;">' + debugInfo + '</pre></div>',
			area: ['800px', '600px'],
			btn: ['关闭'],
			yes: function(index) {
				layer.close(index);
			}
		});

		// 同时输出到控制台
		console.log(debugInfo);
	}
	</script>
	{include file="public/copyright"/}
</body>
</html>
