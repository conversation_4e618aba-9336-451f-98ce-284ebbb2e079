{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?93ed", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?2ee4", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?793c", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?b68c", "uni-app:///yuyue/usercalendar.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?b0ac", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/usercalendar.vue?9481"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "year", "month", "weekDays", "calendarDays", "selectedDate", "selectedDayText", "selectedDayWorkers", "product", "orderList", "pagenum", "nomore", "nodata", "selectedOrderId", "orderSelected", "calendarData", "showOrderPanel", "showDatePanel", "onLoad", "onPullDownRefresh", "methods", "getOrderList", "that", "app", "console", "getCalendarData", "params", "day", "generateCalendar", "disabled", "i", "date", "weekday", "weekdayText", "available", "isToday", "orders", "available_worker_count", "specialPrice", "selectDate", "uni", "scrollTop", "duration", "confirmDate", "url", "fail", "selectOrder", "prevMonth", "nextMonth", "getStatusText", "goToPurchase", "closeOrderPanel", "closeDatePanel", "closeAllPanels", "selectAndShowCalendar"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwKrxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;MACA;IACA;MACA;MACA;IACA;IAEA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACA;MAEAC;MACAA;MACAA;MAEAC;QAAAb;MAAA;QACAY;QACA;QAEAE;;QAEA;QACA;UACA;QACA;QAEA;UACAF;UAEA;YACAA;UACA;;UAEA;UACA;YACA;cACA;gBACAA;gBACA;cACA;YACA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;QACAxB;QACAC;MACA;;MAEA;MACA;QACAwB;MACA;MACA;MAAA,KACA;QACAA;MACA;MAEAJ;MACAC;QACAD;QAEAE;QAEA;UACA;UACAF;;UAEA;UACAA;;UAEA;UACAA;YACA;cACAK;YACA;UACA;;UAEA;UACAL;QACA;UACAC;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;MACA;;MAEA;;MAEA;MACA;QACAxB;UACAuB;UACAE;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MAAA,2BACAC;QACA;;QAEA;QACA;UAAA;QAAA;QAEA;;QAEA;QACA;QACA;QAEA1B;UACAuB;UACAI;UACAC;UACAC;UACAC;UACAL;UACAM;UACAC;UACAC;UACAC;QACA;MAAA;MAvBA;QAAA;MAwBA;;MAEA;MACA;MACA;QACA;UACAlC;YACAuB;YACAE;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAU;MACA;QACA;MACA;;MAEA;MACA;QACAhB;QACA;MACA;MAEA;MACA;MACA;MAEAC;;MAEA;MACA;QACA;MACA;QACAD;QACA;QACAiB;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACApB;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEAC;;MAEA;MACA;;MAEA;MACA;MACAA;MAEA;QACAD;MACA;QACAC;QACA;QACAgB;UACAI;UACAC;YACArB;YACAD;UACA;QACA;MACA;IACA;IAEA;IACAuB;MACAtB;MAEA;MACA;;MAEA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAuB;MACA;QACA;QACA;MACA;QACA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA3B;IACA;IAEA;IACA4B;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA9B;MAEA;;MAEA;MACA;QACAA;QACA;MACA;QACA;QACA;;QAEA;QACAgB;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAAilC,CAAgB,6jCAAG,EAAC,C;;;;;;;;;;;ACArmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/usercalendar.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/usercalendar.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./usercalendar.vue?vue&type=template&id=1387398e&\"\nvar renderjs\nimport script from \"./usercalendar.vue?vue&type=script&lang=js&\"\nexport * from \"./usercalendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./usercalendar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/usercalendar.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./usercalendar.vue?vue&type=template&id=1387398e&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.orderList.length : null\n  var l0 =\n    _vm.isload && g0 > 0\n      ? _vm.__map(_vm.orderList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getStatusText(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.showOrderPanel\n      ? _vm.__map(_vm.orderList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.getStatusText(item)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var g1 = _vm.isload && _vm.showOrderPanel ? _vm.orderList.length : null\n  var m2 = _vm.isload && _vm.showDatePanel ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n        g1: g1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./usercalendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./usercalendar.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n  <block v-if=\"isload\">\n    <!-- 顶部标题栏 -->\n    <view class=\"page-header\">\n      <view class=\"title\">\n        <text class=\"title-text\">服务日程</text>\n      </view>\n    </view>\n    \n    <!-- 日历标题 -->\n    <view class=\"calendar-header\">\n      <view class=\"month-nav\">\n        <view class=\"prev-month ripple\" @tap=\"prevMonth\">\n          <text class=\"iconfont iconleft\"></text>\n          <text>上个月</text>\n        </view>\n        <view class=\"current-month\">\n          <text class=\"month-text\">{{year}}年{{month}}月</text>\n        </view>\n        <view class=\"next-month ripple\" @tap=\"nextMonth\">\n          <text>下个月</text>\n          <text class=\"iconfont iconright\"></text>\n        </view>\n      </view>\n      \n      <!-- 日历周标题 -->\n      <view class=\"week-header\">\n        <view class=\"week-day\" v-for=\"(day, index) in weekDays\" :key=\"index\">\n          <text :class=\"{'weekend': index === 0 || index === 6}\">{{day}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 日历主体 -->\n    <view class=\"calendar-body\">\n      <view \n        class=\"calendar-day\" \n        v-for=\"(day, index) in calendarDays\" \n        :key=\"index\"\n        :class=\"{\n          'empty': !day.day,\n          'disabled': day.disabled,\n          'available': day.available && !day.disabled,\n          'selected': selectedDate === day.date,\n          'today': day.isToday\n        }\"\n        @tap=\"selectDate(day)\"\n      >\n        <text class=\"day-number\">{{day.day || ''}}</text>\n        <view class=\"day-marker\" v-if=\"day.available\">\n          <text v-if=\"day.specialPrice\" class=\"special-price\"></text>\n          <text v-if=\"day.available_worker_count > 0\" class=\"has-service\"></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 图例说明 -->\n    <view class=\"calendar-legend\">\n      <view class=\"legend-item\">\n        <view class=\"legend-marker special-price-marker\"></view>\n        <text>特价</text>\n      </view>\n      <view class=\"legend-item\">\n        <view class=\"legend-marker has-service-marker\"></view>\n        <text>有服务</text>\n      </view>\n    </view>\n    \n    <!-- 未预约订单列表 -->\n    <view class=\"order-section\" v-if=\"orderList.length > 0\">\n      <view class=\"section-title\">可预约订单</view>\n      <view class=\"order-list\">\n        <view class=\"order-item\" \n          v-for=\"(item, index) in orderList\" \n          :key=\"index\" \n          :class=\"{'selected-order': selectedOrderId === item.id}\"\n          @tap=\"selectAndShowCalendar(item)\"\n        >\n          <view class=\"order-left\">\n            <image :src=\"item.propic\" mode=\"aspectFill\"></image>\n            <view class=\"order-badge\" v-if=\"item.status == 5 || item.status == 6\">待预约</view>\n          </view>\n          <view class=\"order-center\">\n            <text class=\"order-name\">{{item.proname}}</text>\n            <view class=\"order-info\">\n              <text class=\"order-number\">订单号：{{item.ordernum}}</text>\n              <text class=\"order-status-label\" :class=\"'status-'+item.status\">{{getStatusText(item)}}</text>\n            </view>\n            <view class=\"order-bottom\">\n              <text class=\"order-price\">¥{{item.totalprice}}</text>\n            </view>\n          </view>\n          <view class=\"order-right\">\n            <view class=\"select-btn-wrap\" :style=\"selectedOrderId === item.id ? 'background: #56aaff' : 'background: #f0f7ff'\">\n              <text class=\"select-btn\" :style=\"selectedOrderId === item.id ? 'color: #fff' : 'color: #56aaff'\">选择</text>\n              <text class=\"iconfont iconright\" :style=\"selectedOrderId === item.id ? 'color: #fff' : 'color: #56aaff'\"></text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 无预约订单时显示 -->\n    <view class=\"no-order-container\" v-else>\n      <view class=\"no-order-tip\">\n        <text>您暂时没有可预约的服务项目~</text>\n      </view>\n      <button class=\"purchase-btn\" :style=\"{background:'#56aaff'}\" @tap=\"goToPurchase\">购买服务</button>\n    </view>\n    \n    <!-- 选择订单界面 -->\n    <view class=\"order-panel\" v-if=\"showOrderPanel\">\n      <view class=\"order-panel-header\">\n        <text>选择订单进行预约</text>\n        <text class=\"close-btn\" @tap=\"closeOrderPanel\">×</text>\n      </view>\n      \n      <scroll-view class=\"order-list\" scroll-y=\"true\">\n        <view class=\"order-item\" v-for=\"(item, index) in orderList\" :key=\"index\" @tap=\"selectOrder(item)\">\n          <view class=\"order-left\">\n            <image :src=\"item.propic\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"order-center\">\n            <text class=\"order-name\">{{item.proname}}</text>\n            <text class=\"order-number\">订单号：{{item.ordernum}}</text>\n            <text class=\"order-price\">¥{{item.totalprice}}</text>\n          </view>\n          <view class=\"order-right\">\n            <text :class=\"['order-status', 'status-'+item.status]\">{{getStatusText(item)}}</text>\n          </view>\n        </view>\n      </scroll-view>\n      \n      <view class=\"no-data\" v-if=\"orderList.length === 0\">\n        <text>暂无可预约订单</text>\n      </view>\n    </view>\n    \n    <!-- 日期选择面板 -->\n    <view class=\"date-panel\" v-if=\"showDatePanel\">\n      <view class=\"date-panel-header\">\n        <text>选择预约日期</text>\n        <text class=\"close-btn\" @tap=\"closeDatePanel\">×</text>\n      </view>\n      \n      <view class=\"date-content\">\n        <view class=\"selected-date-info\">\n          <text>已选择：{{selectedDate}} ({{selectedDayText}})</text>\n          <text>可用服务人员：{{selectedDayWorkers}}人</text>\n        </view>\n        \n        <button class=\"confirm-btn\" :style=\"{background:t('color1')}\" @tap=\"confirmDate\">确认预约</button>\n      </view>\n    </view>\n    \n    <view class=\"mask\" v-if=\"showOrderPanel || showDatePanel\" @tap=\"closeAllPanels\"></view>\n    \n    <nomore v-if=\"nomore\"></nomore>\n    <nodata v-if=\"nodata\"></nodata>\n  </block>\n  <loading v-if=\"loading\"></loading>\n  <dp-tabbar :opt=\"opt\" :active=\"3\"></dp-tabbar>\n  <popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      opt: {},\n      loading: false,\n      isload: false,\n      \n      // 日历数据\n      year: new Date().getFullYear(),\n      month: new Date().getMonth() + 1,\n      weekDays: ['日', '一', '二', '三', '四', '五', '六'],\n      calendarDays: [],\n      selectedDate: '',\n      selectedDayText: '',\n      selectedDayWorkers: 0,\n      \n      // 商品数据\n      product: {},\n      \n      // 订单数据\n      orderList: [],\n      pagenum: 1,\n      nomore: false,\n      nodata: false,\n      selectedOrderId: null,\n      orderSelected: false,\n      \n      // 日历原始数据\n      calendarData: [],\n      \n      // 面板控制\n      showOrderPanel: false,\n      showDatePanel: false\n    };\n  },\n\n  onLoad: function (opt) {\n    this.opt = app.getopts(opt);\n    \n    // 如果传入了订单ID，设置为当前选中的订单\n    if (opt.id) {\n      this.selectedOrderId = opt.id;\n    }\n    \n    // 如果传入了商品ID，直接查询该商品的日历\n    if (opt.proid) {\n      this.getCalendarData(opt.proid);\n    } else {\n      this.getCalendarData();\n      this.getOrderList();\n    }\n    \n    this.isload = true;\n  },\n  \n  onPullDownRefresh: function () {\n    this.getCalendarData();\n    this.getOrderList();\n  },\n  \n  methods: {\n    // 获取订单列表\n    getOrderList: function (loadmore) {\n      if (!loadmore) {\n        this.pagenum = 1;\n        this.orderList = [];\n      }\n      \n      var that = this;\n      var pagenum = that.pagenum;\n      \n      that.nodata = false;\n      that.nomore = false;\n      that.loading = true;\n      \n      app.post('ApiYuyue/orderlistrili', {pagenum: pagenum}, function (res) {\n        that.loading = false;\n        var data = res.datalist || [];\n        \n        console.log('获取到订单列表:', data);\n        \n        // 筛选可预约的订单\n        var appointableOrders = data.filter(function(item) {\n          return item.status == 1 || item.status == 5 || item.status == 6;\n        });\n        \n        if (pagenum == 1) {\n          that.orderList = appointableOrders;\n          \n          if (appointableOrders.length == 0) {\n            that.nodata = true;\n          }\n          \n          // 如果有selectedOrderId，选中对应订单\n          if (that.selectedOrderId) {\n            for (let i = 0; i < appointableOrders.length; i++) {\n              if (appointableOrders[i].id == that.selectedOrderId) {\n                that.selectOrder(appointableOrders[i]);\n                break;\n              }\n            }\n          }\n        } else {\n          if (appointableOrders.length == 0) {\n            that.nomore = true;\n          } else {\n            var orderList = that.orderList;\n            var newdata = orderList.concat(appointableOrders);\n            that.orderList = newdata;\n          }\n        }\n      });\n    },\n    \n    // 获取日历数据\n    getCalendarData: function (proid) {\n      var that = this;\n      var params = {\n        year: that.year,\n        month: that.month\n      };\n      \n      // 如果传入了商品ID，优先使用\n      if (proid) {\n        params.proid = proid;\n      } \n      // 如果选择了订单，使用订单中的商品ID\n      else if (that.selectedOrderId) {\n        params.id = that.selectedOrderId;\n      }\n      \n      that.loading = true;\n      app.post('ApiYuyue/userCalendar', params, function (res) {\n        that.loading = false;\n        \n        console.log('获取到日历数据:', res);\n        \n        if (res.status == 1) {\n          // 保存商品信息\n          that.product = res.product || {};\n          \n          // 保存日历数据\n          that.calendarData = res.calendar || [];\n          \n          // 为每个日期添加随机特价标记（仅用于演示）\n          that.calendarData.forEach(function(day) {\n            if (day.available) {\n              day.specialPrice = Math.random() > 0.7;\n            }\n          });\n          \n          // 生成日历视图\n          that.generateCalendar();\n        } else {\n          app.error(res.msg || '获取日历数据失败');\n        }\n      });\n    },\n    \n    // 生成日历视图\n    generateCalendar: function () {\n      const firstDay = new Date(this.year, this.month - 1, 1).getDay(); // 当月第一天是星期几\n      const totalDays = new Date(this.year, this.month, 0).getDate(); // 当月总天数\n      \n      let calendarDays = [];\n      \n      // 添加上个月末尾的空白天数\n      for (let i = 0; i < firstDay; i++) {\n        calendarDays.push({\n          day: 0,\n          disabled: true\n        });\n      }\n      \n      // 获取今天的日期字符串用于比较\n      const today = new Date();\n      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;\n      \n      // 添加当月的天数\n      for (let i = 1; i <= totalDays; i++) {\n        const dateStr = `${this.year}-${String(this.month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;\n        \n        // 查找当天在日历数据中的数据\n        const dayData = this.calendarData.find(item => item.date === dateStr) || {};\n        \n        const isToday = dateStr === todayStr;\n        \n        // 如果日期已过，或者没有服务人员可用，则禁用该日期\n        const isPastDate = new Date(dateStr) < new Date(todayStr);\n        const isDisabled = isPastDate || dayData.disabled === true || !dayData.available || !dayData.available_worker_count;\n        \n        calendarDays.push({\n          day: i,\n          date: dateStr,\n          weekday: new Date(dateStr).getDay(),\n          weekdayText: this.weekDays[new Date(dateStr).getDay()],\n          available: !isDisabled && dayData.available,\n          disabled: isDisabled,\n          isToday: isToday,\n          orders: dayData.orders || 0,\n          available_worker_count: dayData.available_worker_count || 0,\n          specialPrice: dayData.specialPrice || false\n        });\n      }\n      \n      // 添加足够的行，使每行都有7天\n      const remaining = 7 - (calendarDays.length % 7);\n      if (remaining < 7) {\n        for (let i = 0; i < remaining; i++) {\n          calendarDays.push({\n            day: 0,\n            disabled: true\n          });\n        }\n      }\n      \n      this.calendarDays = calendarDays;\n    },\n    \n    // 选择日期\n    selectDate: function (day) {\n      if (!day.day || day.disabled) {\n        return;\n      }\n      \n      // 只有可预约的日期才能点击\n      if (!day.available) {\n        app.error('该日期不可预约');\n        return;\n      }\n      \n      this.selectedDate = day.date;\n      this.selectedDayText = this.weekDays[day.weekday] + '（' + this.month + '月' + day.day + '日）';\n      this.selectedDayWorkers = day.available_worker_count;\n      \n      console.log('选择的日期:', this.selectedDate);\n      \n      // 如果已选择订单，则显示日期确认面板\n      if (this.selectedOrderId) {\n        this.showDatePanel = true;\n      } else {\n        app.alert('请先选择一个订单');\n        // 滚动到订单列表位置\n        uni.pageScrollTo({\n          scrollTop: 500,\n          duration: 300\n        });\n      }\n    },\n    \n    // 确认预约选中的日期\n    confirmDate: function () {\n      if (!this.selectedDate) {\n        app.alert('请选择预约日期');\n        return;\n      }\n      \n      if (!this.selectedOrderId) {\n        app.alert('请先选择一个订单');\n        return;\n      }\n      \n      console.log('准备跳转到预约页面，订单ID:', this.selectedOrderId, '选中日期:', this.selectedDate);\n      \n      // 确保日期格式正确 (YYYY-MM-DD)\n      let dateParam = this.selectedDate;\n      \n      // 跳转到预约页面，传递订单ID和选择的日期\n      const url = `/yuyue/appoint?id=${this.selectedOrderId}&date=${dateParam}`;\n      console.log('跳转URL:', url);\n      \n      try {\n        app.goto(url);\n      } catch (error) {\n        console.error('跳转失败:', error);\n        // 尝试备用方式跳转\n        uni.navigateTo({\n          url: url,\n          fail: function(err) {\n            console.error('备用跳转也失败:', err);\n            app.error('页面跳转失败，请稍后重试');\n          }\n        });\n      }\n    },\n    \n    // 选择订单\n    selectOrder: function (order) {\n      console.log('选择订单:', order);\n      \n      this.selectedOrderId = order.id;\n      this.showOrderPanel = false;\n      \n      // 如果已选择日期，则显示日期确认面板\n      if (this.selectedDate) {\n        this.showDatePanel = true;\n      } else {\n        // 重新获取日历数据\n        this.getCalendarData();\n      }\n    },\n    \n    // 上个月\n    prevMonth: function () {\n      if (this.month === 1) {\n        this.year--;\n        this.month = 12;\n      } else {\n        this.month--;\n      }\n      \n      this.selectedDate = '';\n      this.selectedDayText = '';\n      this.getCalendarData();\n    },\n    \n    // 下个月\n    nextMonth: function () {\n      if (this.month === 12) {\n        this.year++;\n        this.month = 1;\n      } else {\n        this.month++;\n      }\n      \n      this.selectedDate = '';\n      this.selectedDayText = '';\n      this.getCalendarData();\n    },\n    \n    // 获取订单状态文本\n    getStatusText: function (item) {\n      if (item.status == 0) return '待付款';\n      if (item.status == 1) {\n        if (item.refund_status == 1) return '退款审核中';\n        if (item.appointment_status === 0 || !item.appointment_status) return '待预约';\n        if (item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id)) return '待选择服务人员';\n        return '派单中';\n      }\n      if (item.status == 2) return '服务中';\n      if (item.status == 3) return '已完成';\n      if (item.status == 4) return '已取消';\n      if (item.status == 5) return '待选择时间';\n      if (item.status == 6) return '待分配服务';\n      \n      return '未知状态';\n    },\n    \n    // 前往购买服务页面\n    goToPurchase: function() {\n      app.goto('/pages/index/index');\n    },\n    \n    // 关闭订单选择面板\n    closeOrderPanel: function() {\n      this.showOrderPanel = false;\n    },\n    \n    // 关闭日期选择面板\n    closeDatePanel: function() {\n      this.showDatePanel = false;\n    },\n    \n    // 关闭所有面板\n    closeAllPanels: function() {\n      this.showOrderPanel = false;\n      this.showDatePanel = false;\n    },\n    \n    // 选择订单并显示日历\n    selectAndShowCalendar: function (order) {\n      console.log('选择订单并展示日历:', order);\n      \n      this.selectedOrderId = order.id;\n      \n      // 如果已选择日期，则显示日期确认面板\n      if (this.selectedDate) {\n        console.log('已选中日期:', this.selectedDate);\n        this.showDatePanel = true;\n      } else {\n        // 重新获取日历数据\n        this.getCalendarData();\n        \n        // 滚动到日历位置\n        uni.pageScrollTo({\n          scrollTop: 0,\n          duration: 300\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style>\n.container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 顶部标题 */\n.page-header {\n  padding: 30rpx 0;\n  background-color: #fff;\n  text-align: center;\n  margin-bottom: 20rpx;\n}\n\n.title-text {\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n/* 日历标题 */\n.calendar-header {\n  background-color: #fff;\n  padding: 0 0 10rpx 0;\n  margin: 0 20rpx;\n}\n\n.month-nav {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.current-month {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.month-text {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: bold;\n}\n\n.prev-month, .next-month {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #666;\n  padding: 10rpx;\n}\n\n.iconleft {\n  margin-right: 4rpx;\n}\n\n.iconright {\n  margin-left: 4rpx;\n}\n\n/* 周标题 */\n.week-header {\n  display: flex;\n  padding: 16rpx 0 8rpx;\n}\n\n.week-day {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.week-day text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.weekend {\n  color: #999;\n}\n\n/* 日历主体 */\n.calendar-body {\n  display: flex;\n  flex-wrap: wrap;\n  background-color: #fff;\n  padding: 10rpx 0 20rpx 0;\n  margin: 0 20rpx 20rpx;\n}\n\n.calendar-day {\n  width: calc(100% / 7);\n  height: 90rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  box-sizing: border-box;\n  padding: 8rpx 0;\n}\n\n.day-number {\n  font-size: 30rpx;\n  color: #333;\n  margin-bottom: 4rpx;\n  z-index: 2;\n}\n\n.day-marker {\n  display: flex;\n  justify-content: center;\n  margin-top: 2rpx;\n  z-index: 2;\n}\n\n.special-price, .has-service {\n  width: 6rpx;\n  height: 6rpx;\n  border-radius: 3rpx;\n  margin: 0 2rpx;\n}\n\n.special-price {\n  background-color: #FF9800;\n}\n\n.has-service {\n  background-color: #4CAF50;\n}\n\n.calendar-day.empty {\n  background-color: #fff;\n}\n\n.calendar-day.disabled .day-number {\n  color: #ccc;\n}\n\n.calendar-day.available {\n  cursor: pointer;\n}\n\n.calendar-day.today .day-number {\n  color: #007AFF;\n}\n\n/* 保留图片中的选中日期样式 */\n.calendar-day.selected {\n  position: relative;\n}\n\n.calendar-day.selected::after {\n  content: \"\";\n  display: block;\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 12rpx;\n  background-color: #7E7DFA;\n  position: absolute;\n  z-index: 1;\n  top: 45%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.calendar-day.selected .day-number {\n  color: #fff;\n  font-weight: bold;\n  z-index: 3;\n}\n\n.calendar-day.selected::before {\n  content: '';\n  position: absolute;\n  width: 6rpx;\n  height: 6rpx;\n  background-color: #fff;\n  border-radius: 50%;\n  bottom: 22rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 3;\n}\n\n/* 图例说明 */\n.calendar-legend {\n  display: flex;\n  padding: 16rpx 20rpx;\n  background-color: #fff;\n  margin: 20rpx 20rpx 0;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n}\n\n.legend-marker {\n  width: 12rpx;\n  height: 12rpx;\n  border-radius: 6rpx;\n  margin-right: 8rpx;\n}\n\n.special-price-marker {\n  background-color: #FF9800;\n}\n\n.has-service-marker {\n  background-color: #4CAF50;\n}\n\n.legend-item text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n/* 订单部分 */\n.order-section {\n  margin: 20rpx 20rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 16rpx;\n  padding: 16rpx;\n  background: #fff;\n}\n\n.order-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.order-item {\n  display: flex;\n  padding: 20rpx;\n  margin-bottom: 16rpx;\n  background-color: #fff;\n  border-left: 4rpx solid transparent;\n}\n\n.order-item:last-child {\n  margin-bottom: 0;\n}\n\n.order-item.selected-order {\n  border-left: 4rpx solid #7E7DFA;\n  background-color: #f9f9ff;\n}\n\n.order-left {\n  width: 120rpx;\n  height: 120rpx;\n  margin-right: 16rpx;\n  border-radius: 6rpx;\n  overflow: hidden;\n  position: relative;\n}\n\n.order-badge {\n  position: absolute;\n  top: 0;\n  left: 0;\n  background: rgba(255, 73, 73, 0.9);\n  color: #fff;\n  font-size: 20rpx;\n  padding: 2rpx 8rpx;\n  border-radius: 0 0 6rpx 0;\n}\n\n.order-left image {\n  width: 100%;\n  height: 100%;\n  border-radius: 6rpx;\n}\n\n.order-center {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  padding: 4rpx 0;\n  min-width: 0;\n}\n\n.order-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.order-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 12rpx;\n}\n\n.order-number {\n  font-size: 24rpx;\n  color: #999;\n  max-width: 65%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.order-status-label {\n  font-size: 22rpx;\n  padding: 2rpx 10rpx;\n  border-radius: 10rpx;\n  color: #fff;\n  background: #bbb;\n  text-align: center;\n}\n\n.status-0 {\n  background: #ff9500;\n}\n\n.status-1, .status-5, .status-6 {\n  background: #ff3b30;\n}\n\n.status-2 {\n  background: #5856d6;\n}\n\n.status-3 {\n  background: #34c759;\n}\n\n.status-4 {\n  background: #8e8e93;\n}\n\n.order-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.order-price {\n  font-size: 30rpx;\n  color: #ff3b30;\n  font-weight: bold;\n}\n\n.order-right {\n  display: flex;\n  align-items: center;\n  padding-left: 16rpx;\n}\n\n.select-btn-wrap {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  background: #f0f7ff;\n}\n\n.selected-order .select-btn-wrap {\n  background: #7E7DFA;\n}\n\n.select-btn {\n  font-size: 24rpx;\n  color: #7E7DFA;\n}\n\n.selected-order .select-btn {\n  color: #fff;\n}\n\n.iconright {\n  font-size: 22rpx;\n  color: #7E7DFA;\n}\n\n.selected-order .iconright {\n  color: #fff;\n}\n\n/* 无订单时的容器 */\n.no-order-container {\n  margin: 20rpx;\n  background-color: #fff;\n  padding: 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.no-order-tip {\n  margin: 40rpx 0;\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.purchase-btn {\n  width: 90%;\n  height: 80rpx;\n  line-height: 80rpx;\n  border-radius: 40rpx;\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  background: #7E7DFA !important;\n}\n\n/* 日期选择面板 */\n.date-panel {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  border-radius: 20rpx 20rpx 0 0;\n  z-index: 1001;\n  padding-bottom: calc(30rpx + 100rpx); /* 增加底部padding，确保内容不被底部导航栏遮挡 */\n  max-height: 70vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.date-panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.date-panel-header text {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.close-btn {\n  font-size: 36rpx;\n  color: #999;\n}\n\n.date-content {\n  padding: 20rpx;\n}\n\n.selected-date-info {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 20rpx;\n  padding: 16rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n}\n\n.selected-date-info text {\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.confirm-btn {\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  border-radius: 40rpx;\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  background: #7E7DFA !important;\n}\n\n/* 遮罩层 */\n.mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.4);\n  z-index: 999;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./usercalendar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./usercalendar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115044092\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}