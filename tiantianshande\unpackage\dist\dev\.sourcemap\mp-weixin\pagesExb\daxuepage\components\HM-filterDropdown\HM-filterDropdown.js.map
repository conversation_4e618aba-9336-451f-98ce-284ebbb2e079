{"version": 3, "sources": ["webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?d9ad", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?e114", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?82cd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?a18d", "uni-app:///pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?5da0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue?56b6"], "names": ["name", "data", "menuData", "subData", "menu", "showPage", "pageState", "activeMenuArr", "shadowActiveMenuArr", "defaultActive", "triangleDeg", "isShowMask", "maskVisibility", "firstScrollInto", "secondScrollInto", "thirdScrollInto", "componentTop", "isReadNewSelect", "props", "menuTop", "value", "default", "filterData", "defaultSelected", "updateMenuName", "dataFormat", "watch", "handler", "console", "immediate", "deep", "methods", "initMenu", "tmpMenu", "type", "tmpMenuActiveArr", "tmpitem", "setMenuName", "tmpsub", "showMoreSub", "selectHierarchyMenu", "tmpMemu", "<PERSON><PERSON><PERSON><PERSON>", "setFilterData", "resetFilterData", "tmpArr", "level", "selectFilterLabel", "selectRadioLabel", "togglePage", "setTimeout", "hideMenu", "showMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showMaskLayer", "hide<PERSON><PERSON>u<PERSON><PERSON>er", "showMenuLayer", "confirm", "index", "item", "s", "submenu", "reloadActiveMenuArr", "processPage", "processActive", "processSubMenu", "getMaxFloor", "max", "each", "discard"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACc;;;AAG9E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAswB,CAAgB,ixBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuI1xB;EACAA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACAJ;MACAK;QACAC;QACA;QACA;MACA;;MACAC;MACAC;IACA;IACAP;MAEA;QACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAQ;IACAC;MAAA;MAEA;MACA;MACA;QACA;QACAC;UACA;UACAjC;UACAkC;QACA;QACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACAC;QACA;MACA;MACA;MACA;MACAD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAE;MACA;QACA;QACA;UACA;YACA;YACA;cACAC;cACA;gBACAA;cACA;YACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAZ;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;QACAa;QACA;UACA;QACA;UACA;UACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;QACA;QACA;UACA;QACA;QACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MAEA;MACA;QACA;QACA;MACA;QACA;UACA;QACA;QAEA;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;QACA;MACA;QACA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;IAEA;IACA;IACAC;MAAA;MACA;MACAH;QACA;MACA;IACA;IACA;IACAI;MAAA;MACA;MACA;QACAJ;UACA;QACA;MACA;IACA;IACA;IACAK;MAAA;MACA;MACA;MACA;QACAL;UACA;QACA;MACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAM;MAAA;MACA;MACA;MACA;QACAN;UACA;QACA;MACA;MACA;IACA;IACAO;MAAA;MACA;MACA;;MAEA;MACAC;QACA;UACA;UACAC;YACA;cACAC;gBACA;cACA;cACAD;cACAC;gBACAxC;gBACA;kBACAA;kBACAsC;gBACA;cACA;YACA;UACA;QACA;UACA;UACAtC;UACA;YACA;cACAyC;cACAzC;YACA;cACAA;YACA;YACA;cACA;gBACAyC;gBACAzC;cACA;gBACAA;cACA;YACA;UACA;QACA;QACAsC;MAEA;MACA;MACA;QACAA;QACAtC;MACA;IACA;IAEA0C;MACA;QACA;QACA;QACA1B;QACA;UACA;UACA;UACA;QACA;MACA;MACA;MACA;IACA;IACA2B;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;QACA;UACAb;YACA;YACA;YACA;UACA;QACA;MACA;QACA;UACAA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;QACA;QACA;QACA;UACA;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;QACA;QACA;UACAnB;UACAC;QACA;MACA;QACA;QACA;UACAD;UACAC;QACA;MACA;QACA;QACA;UACAD;UACAC;QACA;MACA;MACA;IACA;IACAmB;MACA;QACA;UACA7D;QACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACA8D;MACA;MACA;MACA;QACAjE;UACAkE;UACA;YACAC;UACA;QACA;MACA;MACAA;MACA;IACA;IACAC,6BAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5kBA;AAAA;AAAA;AAAA;AAA66C,CAAgB,w3CAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./HM-filterDropdown.vue?vue&type=template&id=2fc4c8d5&\"\nvar renderjs\nimport script from \"./HM-filterDropdown.vue?vue&type=script&lang=js&\"\nexport * from \"./HM-filterDropdown.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HM-filterDropdown.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/daxuepage/components/HM-filterDropdown/HM-filterDropdown.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./HM-filterDropdown.vue?vue&type=template&id=2fc4c8d5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l6 = _vm.__map(_vm.subData, function (page, page_index) {\n    var $orig = _vm.__get_orig(page)\n    var g0 =\n      (page.type == \"hierarchy\" || page.type == \"hierarchy-column\") &&\n      page.submenu.length > 0\n    var g1 = g0 ? _vm.activeMenuArr[page_index].length : null\n    var g2 = g0 ? _vm.activeMenuArr[page_index].length : null\n    var l2 =\n      g0 && page.type == \"hierarchy\"\n        ? _vm.__map(page.submenu, function (sub, index) {\n            var $orig = _vm.__get_orig(sub)\n            var g3 =\n              _vm.activeMenuArr[page_index][0] == index &&\n              sub.submenu.length > 0\n            var l1 = g3\n              ? _vm.__map(sub.submenu, function (sub_second, second_index) {\n                  var $orig = _vm.__get_orig(sub_second)\n                  var g4 =\n                    sub_second.submenu &&\n                    sub.submenu.length > 0 &&\n                    sub_second.submenu.length > 0\n                  var l0 = g4\n                    ? _vm.__map(\n                        sub_second.submenu,\n                        function (sub2, sub2_index) {\n                          var $orig = _vm.__get_orig(sub2)\n                          var g5 =\n                            sub_second.showAllSub != true &&\n                            sub2_index == 8 &&\n                            sub_second.submenu.length > 9\n                          return {\n                            $orig: $orig,\n                            g5: g5,\n                          }\n                        }\n                      )\n                    : null\n                  return {\n                    $orig: $orig,\n                    g4: g4,\n                    l0: l0,\n                  }\n                })\n              : null\n            return {\n              $orig: $orig,\n              g3: g3,\n              l1: l1,\n            }\n          })\n        : null\n    var l3 =\n      g0 && !(page.type == \"hierarchy\") && page.type == \"hierarchy-column\"\n        ? _vm.__map(page.submenu, function (sub, index) {\n            var $orig = _vm.__get_orig(sub)\n            var g6 =\n              _vm.activeMenuArr[page_index][0] == index &&\n              sub.submenu.length > 0\n            return {\n              $orig: $orig,\n              g6: g6,\n            }\n          })\n        : null\n    var l5 =\n      g0 && !(page.type == \"hierarchy\") && page.type == \"hierarchy-column\"\n        ? _vm.__map(page.submenu, function (sub, index) {\n            var $orig = _vm.__get_orig(sub)\n            var l4 = _vm.__map(\n              sub.submenu,\n              function (sub_second, second_index) {\n                var $orig = _vm.__get_orig(sub_second)\n                var g7 =\n                  _vm.activeMenuArr[page_index][0] == index &&\n                  _vm.activeMenuArr[page_index][1] == second_index &&\n                  sub_second.submenu.length > 0\n                return {\n                  $orig: $orig,\n                  g7: g7,\n                }\n              }\n            )\n            return {\n              $orig: $orig,\n              l4: l4,\n            }\n          })\n        : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n      l2: l2,\n      l3: l3,\n      l5: l5,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l6: l6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./HM-filterDropdown.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./HM-filterDropdown.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"HMfilterDropdown\" :class=\"{'setDropdownBottom':maskVisibility}\" :style=\"{'top':menuTop+'rpx'}\" @touchmove.stop.prevent=\"discard\" @tap.stop=\"discard\">\r\n\t\t<!-- 顶部菜单 -->\r\n\t\t<view class=\"nav\">\r\n\t\t\t<block v-for=\"(item,index) in menu\" :key=\"index\">\r\n\t\t\t\t<view class=\"first-menu\" :class=\"{'on':showPage==index}\" @tap=\"togglePage(index)\">\r\n\t\t\t\t\t<text class=\"name\">{{item.name}}</text>\r\n\t\t\t\t\t<text class=\"iconfont triangle\" :style=\"'transform:rotate('+triangleDeg[index]+'deg);'\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<slot name=\"body\"></slot>\r\n\t\t</view>\r\n\t\t<!-- 遮罩层 -->\r\n\t\t<view class=\"mask\" :class=\"{'show':isShowMask,'hide':maskVisibility!=true}\" @tap=\"hideMenu(false)\"></view>\r\n\t\t<block v-for=\"(page,page_index) in subData\" :key=\"page_index\">\r\n\t\t\t<view class=\"sub-menu-class\" :class=\"{'show':showPage==page_index,'hide':pageState[page_index]!=true}\">\r\n\t\t\t\t<!-- 多级菜单 -->\r\n\t\t\t\t<block v-if=\"(page.type=='hierarchy'||page.type=='hierarchy-column')&& page.submenu.length>0\">\r\n\t\t\t\t\t<!-- 第一级菜单 -->\r\n\t\t\t\t\t<scroll-view class=\"sub-menu-list\" :class=\"{'first':activeMenuArr[page_index].length>1,'alone':activeMenuArr[page_index].length<=1}\"\r\n\t\t\t\t\t :scroll-y=\"true\" :scroll-into-view=\"'first_id'+firstScrollInto\">\r\n\t\t\t\t\t\t<block v-for=\"(sub,index) in page.submenu\" :key=\"sub.value\">\r\n\t\t\t\t\t\t\t<view class=\"sub-menu\" :id=\"'first_id'+index\" :class=\"{'on':activeMenuArr[page_index][0]==index}\" @tap=\"selectHierarchyMenu(page_index,index,null,null)\">\r\n\t\t\t\t\t\t\t\t<view class=\"menu-name\">\r\n\t\t\t\t\t\t\t\t\t<text>{{sub.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont selected\"></text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<block v-if=\"page.type=='hierarchy'\">\r\n\t\t\t\t\t\t<block v-for=\"(sub,index) in page.submenu\" :key=\"sub.value\">\r\n\t\t\t\t\t\t\t<!-- 第二级菜单 -->\r\n\t\t\t\t\t\t\t<scroll-view class=\"sub-menu-list not-first\" :scroll-y=\"true\" v-if=\"activeMenuArr[page_index][0]==index&&sub.submenu.length>0\"\r\n\t\t\t\t\t\t\t :scroll-into-view=\"'second_id'+secondScrollInto\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sub_second,second_index) in sub.submenu\" :key=\"sub_second.value\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"sub-menu\" :id=\"'second_id'+second_index\" :class=\"{'on':activeMenuArr[page_index][1]==second_index}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"menu-name\" @tap=\"selectHierarchyMenu(page_index,activeMenuArr[page_index][0],second_index,null)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{sub_second.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont selected\"></text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 第三级菜单 -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"more-sub-menu\" v-if=\"sub_second.submenu&&sub.submenu.length>0&&sub_second.submenu.length>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(sub2,sub2_index) in sub_second.submenu\" :key=\"sub2.value\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"sub_second.showAllSub || (sub2_index<8)\" :class=\"{'on':activeMenuArr[page_index][1]==second_index&&activeMenuArr[page_index][2]==sub2_index}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t @tap.stop=\"selectHierarchyMenu(page_index,activeMenuArr[page_index][0],second_index,sub2_index)\">{{sub2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"sub_second.showAllSub!=true && sub2_index==8 && sub_second.submenu.length>9\" @tap.stop=\"showMoreSub(second_index)\">更多<text\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"iconfont triangle\"></text></text>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 二三级菜单都是行形态 -->\r\n\t\t\t\t\t<block v-else-if=\"page.type=='hierarchy-column'\">\r\n\t\t\t\t\t\t<!-- 第二级菜单 -->\r\n\t\t\t\t\t\t<block v-for=\"(sub,index) in page.submenu\" :key=\"index\">\r\n\t\t\t\t\t\t\t<scroll-view class=\"sub-menu-list not-first\" :scroll-y=\"true\" v-if=\"activeMenuArr[page_index][0]==index&&sub.submenu.length>0\"\r\n\t\t\t\t\t\t\t :scroll-into-view=\"'second_id'+secondScrollInto\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(sub_second,second_index) in sub.submenu\" :key=\"second_index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"sub-menu\" :id=\"'second_id'+second_index\" :class=\"{'on':activeMenuArr[page_index][1]==second_index}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"menu-name\" @tap=\"selectHierarchyMenu(page_index,activeMenuArr[page_index][0],second_index,null)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{sub_second.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<!-- 第三级菜单 -->\r\n\t\t\t\t\t\t<block v-for=\"(sub,index) in page.submenu\">\r\n\t\t\t\t\t\t\t<block v-for=\"(sub_second,second_index) in sub.submenu\">\r\n\t\t\t\t\t\t\t\t<scroll-view class=\"sub-menu-list not-first third\" :scroll-y=\"true\" v-if=\"activeMenuArr[page_index][0]==index&&activeMenuArr[page_index][1]==second_index&&sub_second.submenu.length>0\"\r\n\t\t\t\t\t\t\t\t:scroll-into-view=\"'third_id'+thirdScrollInto\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(sub2,sub2_index) in sub_second.submenu\" :key=\"sub2_index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"sub-menu\" :id=\"'third_id'+sub2_index\" :class=\"{'on':activeMenuArr[page_index][2]==sub2_index}\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"menu-name\" @tap=\"selectHierarchyMenu(page_index,activeMenuArr[page_index][0],second_index,sub2_index)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{sub2.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!-- 多选筛选 -->\r\n\t\t\t\t<block v-if=\"page.type=='filter'\">\r\n\t\t\t\t\t<view class=\"filter\">\r\n\t\t\t\t\t\t<scroll-view class=\"menu-box\" :scroll-y=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"box\" v-for=\"(box,box_index) in page.submenu\" :key=\"box_index\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{box.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"labels\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(label,label_index) in box.submenu\" :key=\"label_index\" @tap=\"selectFilterLabel(page_index,box_index,label_index)\"\r\n\t\t\t\t\t\t\t\t\t :class=\"{'on':label.selected}\">{{label.name}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t<view class=\"btn-box\">\r\n\t\t\t\t\t\t\t<view class=\"reset\" @tap=\"resetFilterData(page_index)\">重置</view>\r\n\t\t\t\t\t\t\t<view class=\"submit\" @tap=\"setFilterData(page_index)\">确定</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 单选筛选 -->\r\n\t\t\t\t<block v-if=\"page.type=='radio'\">\r\n\t\t\t\t\t<view class=\"filter\">\r\n\t\t\t\t\t\t<scroll-view class=\"menu-box\" :scroll-y=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"box\" v-for=\"(box,box_index) in page.submenu\" :key=\"box_index\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{box.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"labels\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(label,label_index) in box.submenu\" :key=\"label_index\" @tap=\"selectRadioLabel(page_index,box_index,label_index)\"\r\n\t\t\t\t\t\t\t\t\t :class=\"{'on':label.selected}\">{{label.name}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t<view class=\"btn-box\">\r\n\t\t\t\t\t\t\t<view class=\"reset\" @tap=\"resetFilterData(page_index)\">重置</view>\r\n\t\t\t\t\t\t\t<view class=\"submit\" @tap=\"setFilterData(page_index)\">确定</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tname: 'HM-filterDropdown',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuData:[],\r\n\t\t\t\tsubData: [], //菜单数据\r\n\t\t\t\tmenu: [], //顶部菜单数据\r\n\t\t\t\tshowPage: -1, //菜单页面显示/隐藏动画控制\r\n\t\t\t\tpageState: [], //页面的状态\r\n\t\t\t\tactiveMenuArr: [], //UI状态\r\n\t\t\t\tshadowActiveMenuArr: [], //记录选中\r\n\t\t\t\tdefaultActive:[],\r\n\t\t\t\ttriangleDeg: [], //小三角形的翻转动画控制\r\n\t\t\t\tisShowMask: false, //遮罩层显示/隐藏动画控制\r\n\t\t\t\tmaskVisibility: false, //遮罩层显示/隐藏状态\r\n\t\t\t\t\r\n\t\t\t\t//滚动区域定位\r\n\t\t\t\tfirstScrollInto: 0,\r\n\t\t\t\tsecondScrollInto: 0,\r\n\t\t\t\tthirdScrollInto:0,\r\n\t\t\t\tcomponentTop:0\t,//组件top\r\n\t\t\t\tisReadNewSelect:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmenuTop:{\r\n\t\t\t\tvalue: Number,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tfilterData: {\r\n\t\t\t\tvalue: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\tdefaultSelected:{\r\n\t\t\t\tvalue: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\tupdateMenuName:{\r\n\t\t\t\tvalue: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tdataFormat:{\r\n\t\t\t\tvalue: String,\r\n\t\t\t\tdefault: 'Array'\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tfilterData: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tconsole.log('watch filterData');\r\n\t\t\t\t\tthis.menuData = JSON.parse(JSON.stringify(newVal));\r\n\t\t\t\t\tthis.initMenu(); //filterData重新赋值初始化菜单\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true,\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tdefaultSelected(newVal) {\r\n\r\n\t\t\t\tif(newVal.length==0){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.defaultActive = JSON.parse(JSON.stringify(newVal));\r\n\t\t\t\tthis.activeMenuArr = JSON.parse(JSON.stringify(newVal));\r\n\t\t\t\tthis.shadowActiveMenuArr = JSON.parse(JSON.stringify(newVal));\r\n\t\t\t\tif(this.updateMenuName){\r\n\t\t\t\t\tthis.setMenuName();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinitMenu() {\r\n\r\n\t\t\t\tlet tmpMenuActiveArr=[];\r\n\t\t\t\tlet tmpMenu=[];\r\n\t\t\t\tfor (let i = 0; i < this.menuData.length; i++) {\r\n\t\t\t\t\tlet tmpitem = this.menuData[i];\r\n\t\t\t\t\ttmpMenu.push({\r\n\t\t\t\t\t\t//如果没有设置name，则取第一个菜单作为menu.name,filter类型则将\"筛选\"作为menu.name\r\n\t\t\t\t\t\tname: tmpitem.name || (tmpitem.type == \"filter\" ? \"筛选\" : tmpitem.submenu[0].name),\r\n\t\t\t\t\t\ttype: tmpitem.type\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//初始化选中项数组-ui状态\r\n\t\t\t\t\ttmpMenuActiveArr.push(this.processActive(tmpitem));\r\n\t\t\t\t\t//初始化角度数组\r\n\t\t\t\t\tthis.triangleDeg.push(0);\r\n\t\t\t\t\t//初始化控制显示状态数组\r\n\t\t\t\t\tthis.pageState.push(false);\r\n\t\t\t\t\t//递归处理子菜单数据\r\n\t\t\t\t\ttmpitem = this.processSubMenu(tmpitem);\r\n\t\t\t\t\tthis.menuData[i] = tmpitem;\r\n\t\t\t\t}\r\n\t\t\t\tthis.menu = tmpMenu;\r\n\t\t\t\t//初始化选中项数组\r\n\t\t\t\ttmpMenuActiveArr = this.defaultActive.length>0?this.defaultActive:this.activeMenuArr.length>0?this.activeMenuArr:tmpMenuActiveArr;\r\n\t\t\t\tthis.defaultActive = [];\r\n\t\t\t\tthis.activeMenuArr.splice(0,this.activeMenuArr.length,...JSON.parse(JSON.stringify(tmpMenuActiveArr)));\r\n\t\t\t\t// this.activeMenuArr = JSON.parse(JSON.stringify(tmpMenuActiveArr));\r\n\t\t\t\tthis.shadowActiveMenuArr = JSON.parse(JSON.stringify(tmpMenuActiveArr));\r\n\t\t\t\t//加载菜单数据\r\n\t\t\t\tthis.subData = this.menuData;\r\n\t\t\t\t//设定顶部菜单名字\r\n\t\t\t\tif(this.updateMenuName){\r\n\t\t\t\t\tthis.setMenuName();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetMenuName(){\r\n\t\t\t\tfor(var i=0;i<this.activeMenuArr.length;i++){\r\n\t\t\t\t\tlet row = this.activeMenuArr[i];\r\n\t\t\t\t\tif(this.subData[i].type=='hierarchy' || this.subData[i].type=='hierarchy-column'){\r\n\t\t\t\t\t\tif (typeof(row[0]) == 'number'){\r\n\t\t\t\t\t\t\tlet tmpsub = this.subData[i].submenu[row[0]];\r\n\t\t\t\t\t\t\tif(row.length>1){\r\n\t\t\t\t\t\t\t\ttmpsub = tmpsub.submenu[row[1]]||tmpsub;\r\n\t\t\t\t\t\t\t\tif(row.length>2){\r\n\t\t\t\t\t\t\t\t\ttmpsub = tmpsub.submenu[row[2]]||tmpsub;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.menu[i].name = tmpsub.name;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.menu[i].name = this.subData[i].name;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//展开更多\r\n\t\t\tshowMoreSub(index) {\r\n\t\t\t\tthis.subData[this.showPage].submenu[this.activeMenuArr[this.showPage][0]].submenu[index].showAllSub = true;\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\t//选中\r\n\t\t\tselectHierarchyMenu(page_index, level1_index, level2_index, level3_index) {\r\n\t\t\t\tconsole.log('selectHierarchyMenu');\r\n\t\t\t\t//读取记录\r\n\t\t\t\tif (level2_index == null && level3_index == null && this.shadowActiveMenuArr[page_index].length>0 && this.shadowActiveMenuArr[page_index][0] == level1_index) {\r\n\t\t\t\t\tthis.activeMenuArr.splice(page_index, 1, JSON.parse(JSON.stringify(this.shadowActiveMenuArr[page_index])));\r\n\t\t\t\t}\r\n\t\t\t\t/////////\r\n\t\t\t\tlet tmpArr = new Array(this.activeMenuArr[page_index].length).fill(null);\r\n\t\t\t\tthis.activeMenuArr[page_index].splice(0, this.activeMenuArr[page_index].length, ...tmpArr);\r\n\t\t\t\t////////\r\n\t\t\t\tthis.activeMenuArr[page_index].splice(0, 1, level1_index);\r\n\t\t\t\tlet tmpMemu = this.subData[page_index].submenu[level1_index];\r\n\t\t\t\tif(tmpMemu.submenu.length==0){\r\n\t\t\t\t\tthis.selectedMemu(page_index, level1_index, level2_index, level3_index);\r\n\t\t\t\t}else if(level2_index!=null){\r\n\t\t\t\t\tthis.activeMenuArr[page_index].splice(1, 1, level2_index);\r\n\t\t\t\t\ttmpMemu = tmpMemu.submenu[level2_index];\r\n\t\t\t\t\tif(tmpMemu.submenu.length==0 || (this.menu[page_index].type == 'hierarchy' && level3_index==null)){\r\n\t\t\t\t\t\tthis.selectedMemu(page_index, level1_index, level2_index, level3_index);\r\n\t\t\t\t\t}else if(level3_index!=null){\r\n\t\t\t\t\t\tthis.activeMenuArr[page_index].splice(2, 1, level3_index);\r\n\t\t\t\t\t\ttmpMemu = tmpMemu.submenu[level3_index];\r\n\t\t\t\t\t\tthis.selectedMemu(page_index, level1_index, level2_index, level3_index);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselectedMemu(page_index, level1_index, level2_index, level3_index){\r\n\t\t\t\tlet sub = this.subData[page_index].submenu[level1_index].submenu[level2_index];\r\n\t\t\t\tif(this.updateMenuName){\r\n\t\t\t\t\tthis.menu[page_index].name = (level3_index != null && sub.submenu[level3_index].name) || (level2_index != null && sub.name) || this.subData[page_index].submenu[level1_index].name;\r\n\t\t\t\t}\r\n\t\t\t\tthis.shadowActiveMenuArr[page_index] = JSON.parse(JSON.stringify(this.activeMenuArr[page_index]));\r\n\t\t\t\tthis.hideMenu(true);\r\n\t\t\t},\r\n\t\t\t//写入结果，筛选\r\n\t\t\tsetFilterData(page_index) {\r\n\t\t\t\tthis.shadowActiveMenuArr[page_index] = JSON.parse(JSON.stringify(this.activeMenuArr[page_index]));\r\n\t\t\t\tthis.hideMenu(true);\r\n\t\t\t},\r\n\t\t\t//重置结果和ui，筛选\r\n\t\t\tresetFilterData(page_index) {\r\n\t\t\t\tlet tmpArr = [];\r\n\t\t\t\tlet level = this.shadowActiveMenuArr[page_index].length;\r\n\t\t\t\twhile (level > 0) {\r\n\t\t\t\t\ttmpArr.push([]);\r\n\t\t\t\t\tlet box = this.subData[page_index].submenu[level - 1].submenu;\r\n\t\t\t\t\tfor (let i = 0; i < box.length; i++) {\r\n\t\t\t\t\t\tthis.subData[page_index].submenu[level - 1].submenu[i].selected = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlevel--;\r\n\t\t\t\t}\r\n\t\t\t\tthis.activeMenuArr[page_index] = JSON.parse(JSON.stringify(tmpArr));\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\t//选中筛选类label-UI状态\r\n\t\t\tselectFilterLabel(page_index, box_index, label_index) {\r\n\t\t\t\tlet find_index = this.activeMenuArr[page_index][box_index].indexOf(label_index);\r\n\t\t\t\tif (find_index > -1) {\r\n\t\t\t\t\tthis.activeMenuArr[page_index][box_index].splice(find_index, 1);\r\n\t\t\t\t\tthis.subData[page_index].submenu[box_index].submenu[label_index].selected = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.activeMenuArr[page_index][box_index].push(label_index);\r\n\t\t\t\t\tthis.subData[page_index].submenu[box_index].submenu[label_index].selected = true;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\t//选中单选类label-UI状态\r\n\t\t\tselectRadioLabel(page_index, box_index, label_index) {\r\n\t\t\t\t\r\n\t\t\t\tlet activeIndex = this.activeMenuArr[page_index][box_index][0];\r\n\t\t\t\tif(activeIndex == label_index){\r\n\t\t\t\t\tthis.subData[page_index].submenu[box_index].submenu[activeIndex].selected = false;\r\n\t\t\t\t\tthis.activeMenuArr[page_index][box_index][0] = null;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(activeIndex!=null && activeIndex<this.subData[page_index].submenu[box_index].submenu.length){\r\n\t\t\t\t\t\tthis.subData[page_index].submenu[box_index].submenu[activeIndex].selected = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.subData[page_index].submenu[box_index].submenu[label_index].selected = true;\r\n\t\t\t\t\tthis.activeMenuArr[page_index][box_index][0] = label_index;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\t//菜单开关\r\n\t\t\ttogglePage(index) {\r\n\t\t\t\tif(this.isToggleing){return;}\r\n\t\t\t\tthis.isToggleing = true;\r\n\t\t\t\tif (index == this.showPage) {\r\n\t\t\t\t\tthis.hideMenu();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showMenu(index)\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.isToggleing = false;\r\n\t\t\t\t},150)\r\n\t\t\t},\r\n\t\t\t//hide菜单\r\n\t\t\thideMenu(isTriggerConfirm){\r\n\t\t\t\tthis.hideMenuLayer(true);\r\n\t\t\t\tthis.hideMaskLayer();\r\n\t\t\t\tthis.showPage = -1;\r\n\t\t\t\tif(isTriggerConfirm){\r\n\t\t\t\t\tthis.confirm()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowMenu(index){\r\n\t\t\t\tif(this.showPage>-1){\r\n\t\t\t\t\tthis.hideMenuLayer(false);\r\n\t\t\t\t}\r\n\t\t\t\tthis.showMenuLayer(index);\r\n\t\t\t\tthis.showMaskLayer();\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t//hide遮罩层\r\n\t\t\thideMaskLayer() {\r\n\t\t\t\tthis.isShowMask = false;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.maskVisibility = false;\r\n\t\t\t\t}, 200);\r\n\t\t\t},\r\n\t\t\t//show遮罩层\r\n\t\t\tshowMaskLayer() {\r\n\t\t\t\tthis.maskVisibility = true;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthis.isShowMask = true;\r\n\t\t\t\t\t},0)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//hide菜单页\r\n\t\t\thideMenuLayer(isAnimation) {\r\n\t\t\t\tthis.triangleDeg[this.showPage] = 0;\r\n\t\t\t\tlet tmpIndex = this.showPage;\r\n\t\t\t\tif (isAnimation) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.pageState.splice(tmpIndex, 1, false);\r\n\t\t\t\t\t}, 200);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.pageState.splice(tmpIndex, 1, false)\r\n\t\t\t\t}\r\n\t\t\t\tthis.firstScrollInto = null;\r\n\t\t\t\tthis.secondScrollInto = null;\r\n\t\t\t},\r\n\t\t\t//show菜单页\r\n\t\t\tshowMenuLayer(index) {\r\n\t\t\t\tthis.processPage(index);\r\n\t\t\t\tthis.pageState.splice(index, 1, true);\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthis.showPage = index;\r\n\t\t\t\t\t},0)\r\n\t\t\t\t})\r\n\t\t\t\tthis.triangleDeg[index] = 180;\r\n\t\t\t},\r\n\t\t\tconfirm() {\r\n\t\t\t\tlet index = JSON.parse(JSON.stringify(this.shadowActiveMenuArr));\r\n\t\t\t\tlet value = JSON.parse(JSON.stringify(this.shadowActiveMenuArr));\r\n\t\t\t\t\r\n\t\t\t\t//对结果做一下处理\r\n\t\t\t\tindex.forEach((item, i) => {\r\n\t\t\t\t\tif (typeof(item[0]) == 'object') {\r\n\t\t\t\t\t\t//针对筛选结果过一个排序\r\n\t\t\t\t\t\titem.forEach((s, j) => {\r\n\t\t\t\t\t\t\tif(s!=null){\r\n\t\t\t\t\t\t\t\ts.sort((val1, val2) => {\r\n\t\t\t\t\t\t\t\t\treturn val1 - val2;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\titem[j] = s;\r\n\t\t\t\t\t\t\t\ts.forEach((v, k) => {\r\n\t\t\t\t\t\t\t\t\tvalue[i][j][k] = (v==null||v>=this.subData[i].submenu[j].submenu.length)?null:this.subData[i].submenu[j].submenu[v].value;\r\n\t\t\t\t\t\t\t\t\tif(this.subData[i].type == 'radio' && value[i][j][k] == null){\r\n\t\t\t\t\t\t\t\t\t\tvalue[i][j] = [];\r\n\t\t\t\t\t\t\t\t\t\tindex[i][j] = [];\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tlet submenu = this.subData[i].submenu[item[0]];\r\n\t\t\t\t\t\tvalue[i][0] = submenu.value;\r\n\t\t\t\t\t\tif(value[i].length>=2  && item[1]!=null){\r\n\t\t\t\t\t\t\tif(submenu.submenu.length>0){\r\n\t\t\t\t\t\t\t\tsubmenu = submenu.submenu[item[1]];\r\n\t\t\t\t\t\t\t\tvalue[i][1] = submenu.hasOwnProperty('value')?submenu.value:null;\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tvalue[i][1] = null\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif(value[i].length>=3 && item[2]!=null){\r\n\t\t\t\t\t\t\t\tif(submenu.submenu.length>0){\r\n\t\t\t\t\t\t\t\t\tsubmenu = submenu.submenu[item[2]];\r\n\t\t\t\t\t\t\t\t\tvalue[i][2] = submenu.hasOwnProperty('value')?submenu.value:null;\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tvalue[i][2] = null;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tindex[i] = item;\r\n\t\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t\t// 输出\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tindex: index,\r\n\t\t\t\t\tvalue: value\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\treloadActiveMenuArr(){\r\n\t\t\t\tfor (let i = 0; i < this.menuData.length; i++) {\r\n\t\t\t\t\tlet tmpitem = this.menuData[i];\r\n\t\t\t\t\tlet tmpArr = this.processActive(tmpitem);\r\n\t\t\t\t\ttmpitem = this.processSubMenu(tmpitem);\r\n\t\t\t\t\tif(this.activeMenuArr[i].length!=tmpArr.length){\r\n\t\t\t\t\t\tthis.menuData[i] = tmpitem;\r\n\t\t\t\t\t\tthis.activeMenuArr.splice(i, 1, JSON.parse(JSON.stringify(tmpArr)));\r\n\t\t\t\t\t\tthis.shadowActiveMenuArr.splice(i, 1, JSON.parse(JSON.stringify(tmpArr)));\r\n\t\t\t\t\t}\r\n\t\t\t\t} \r\n\t\t\t\tthis.subData = this.menuData;\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\tprocessPage(index) {\r\n\t\t\t\t//check UI控制数组，结果数组,防止传入数据层级和UI控制数组不同步\r\n\t\t\t\tthis.reloadActiveMenuArr();\r\n\t\t\t\t//重置UI控制数组\r\n\t\t\t\tthis.activeMenuArr.splice(index, 1, JSON.parse(JSON.stringify(this.shadowActiveMenuArr[index])));\r\n\t\t\t\tif (this.menu[index].type == 'filter') {\r\n\t\t\t\t\t//重载筛选页选中状态\r\n\t\t\t\t\tlet level = this.shadowActiveMenuArr[index].length;\r\n\t\t\t\t\tfor (let i = 0; i < level; i++) {\r\n\t\t\t\t\t\tlet box = this.subData[index].submenu[i].submenu;\r\n\t\t\t\t\t\tfor (let j = 0; j < box.length; j++) {\r\n\t\t\t\t\t\t\tif (this.shadowActiveMenuArr[index][i].indexOf(j) > -1) {\r\n\t\t\t\t\t\t\t\tthis.subData[index].submenu[i].submenu[j].selected = true;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.subData[index].submenu[i].submenu[j].selected = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.menu[index].type == 'hierarchy') {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t//滚动到选中项\r\n\t\t\t\t\t\t\tthis.firstScrollInto = parseInt(this.activeMenuArr[index][0]);\r\n\t\t\t\t\t\t\tthis.secondScrollInto = parseInt(this.activeMenuArr[index][1]);\r\n\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t})\r\n\t\t\t\t}else if (this.menu[index].type == 'hierarchy-column') {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t//滚动到选中项\r\n\t\t\t\t\t\t\tthis.firstScrollInto = parseInt(this.activeMenuArr[index][0]);\r\n\t\t\t\t\t\t\tthis.secondScrollInto = parseInt(this.activeMenuArr[index][1]);\r\n\t\t\t\t\t\t\tthis.thirdScrollInto = parseInt(this.activeMenuArr[index][2]);\r\n\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.menu[index].type == 'radio') {\r\n\t\t\t\t\t//重载筛选页选中状态\r\n\t\t\t\t\tlet level = this.shadowActiveMenuArr[index].length;\r\n\t\t\t\t\tfor (let i = 0; i < level; i++) {\r\n\t\t\t\t\t\tlet box = this.subData[index].submenu[i].submenu;\r\n\t\t\t\t\t\tfor (let j = 0; j < box.length; j++) {\r\n\t\t\t\t\t\t\tif (this.shadowActiveMenuArr[index][i].indexOf(j) > -1) {\r\n\t\t\t\t\t\t\t\tthis.subData[index].submenu[i].submenu[j].selected = true;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.subData[index].submenu[i].submenu[j].selected = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} \r\n\t\t\t},\r\n\t\t\tprocessActive(tmpitem) {\r\n\t\t\t\tlet tmpArr = []\r\n\t\t\t\tif ((tmpitem.type == 'hierarchy'||tmpitem.type == 'hierarchy-column')&&tmpitem.hasOwnProperty('submenu')&&tmpitem.submenu.length>0) {\r\n\t\t\t\t\tlet level = this.getMaxFloor(tmpitem.submenu);\r\n\t\t\t\t\twhile (level > 0) {\r\n\t\t\t\t\t\ttmpArr.push(null);\r\n\t\t\t\t\t\tlevel--;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (tmpitem.type == 'filter') {\r\n\t\t\t\t\tlet level = tmpitem.submenu.length;\r\n\t\t\t\t\twhile (level > 0) {\r\n\t\t\t\t\t\ttmpArr.push([]);\r\n\t\t\t\t\t\tlevel--;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (tmpitem.type == 'radio') {\r\n\t\t\t\t\tlet level = tmpitem.submenu.length;\r\n\t\t\t\t\twhile (level > 0) {\r\n\t\t\t\t\t\ttmpArr.push([]);\r\n\t\t\t\t\t\tlevel--;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn tmpArr;\r\n\t\t\t},\r\n\t\t\tprocessSubMenu(menu) {\r\n\t\t\t\tif (menu.hasOwnProperty('submenu') && menu.submenu.length > 0) {\r\n\t\t\t\t\tfor (let i = 0; i < menu.submenu.length; i++) {\r\n\t\t\t\t\t\tmenu.submenu[i] = this.processSubMenu(menu.submenu[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tmenu.submenu = [];\r\n\t\t\t\t}\r\n\t\t\t\treturn menu;\r\n\t\t\t},\r\n\t\t\t//计算菜单层级\r\n\t\t\tgetMaxFloor(treeData) {\r\n\t\t\t\tlet floor = 0\r\n\t\t\t\tlet max = 0\r\n\t\t\t\tfunction each(data, floor) {\r\n\t\t\t\t\tdata.forEach(e => {\r\n\t\t\t\t\t\tmax = floor > max ? floor : max;\r\n\t\t\t\t\t\tif (e.hasOwnProperty('submenu') && e.submenu.length > 0) {\r\n\t\t\t\t\t\t\teach(e.submenu, floor + 1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\teach(treeData, 1)\r\n\t\t\t\treturn max;\r\n\t\t\t},\r\n\t\t\tdiscard() {\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.HMfilterDropdown {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 100%;\r\n\t\tposition: fixed;\r\n\t\t// position: sticky;\r\n\t\tz-index: 997;\r\n\t\tflex-wrap: nowrap;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\ttop: var(--window-top);\r\n\t\tleft:0;\r\n\t\t// top:100px;\r\n\t\toverflow-y: hidden;\r\n\t\t&.setDropdownBottom{\r\n\t\t\t// height: 345px;\r\n\t\t\tbottom: 0;\r\n\t\t}\r\n\t\tview {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: nowrap;\r\n\t\t}\r\n\t}\r\n\t.region {\r\n\t\tflex: 1;\r\n\t\theight: 44px;\r\n\t}\r\n\t.nav {\r\n\t\twidth: 100%;\r\n\t\theight: 44px;\r\n\t\tborder-bottom: solid 1rpx #eee;\r\n\t\tz-index: 12;\r\n\t\tbackground-color: #ffffff;\r\n\t\tflex-direction: row;\r\n\t\t.first-menu {\r\n\t\t\twidth: 100%;\r\n\t\t\tfont-size: 13px;\r\n\t\t\tcolor: #757575;\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: color .2s linear;\r\n\r\n\t\t\t&.on {\r\n\t\t\t\tcolor: #007BFF;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tcolor: #007BFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.name {\r\n\t\t\t\theight: 20px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\ttext-overflow: clip;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\t\t\t.iconfont {\r\n\t\t\t\twidth: 13px;\r\n\t\t\t\theight: 13px;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttransition: transform .2s linear, color .2s linear;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.sub-menu-class {\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttransform: translate3d(0, - 100%, 0);\r\n\t\tmax-height: 345px;\r\n\t\tbackground-color: #ffffff;\r\n\t\tz-index: 11;\r\n\t\tbox-shadow: 0 5px 5px rgba(0, 0, 0, .1);\r\n\t\toverflow: hidden;\r\n\t\tflex-direction: row;\r\n\t\ttransition: transform .15s linear;\r\n\t\t&.hide {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t&.show {\r\n\t\t\ttransform: translate3d(0, calc(44px + 1rpx), 0);\r\n\t\t}\r\n\t}\r\n\t.sub-menu-list {\r\n\t\twidth: 100%;\r\n\t\theight: 345px;\r\n\t\tflex-direction: column;\r\n\t\t.sub-menu {\r\n\t\t\tmin-height: 44px;\r\n\t\t\tfont-size: 13px;\r\n\t\t\tflex-direction: column;\r\n\t\t\tpadding-right: 15px;\r\n\t\t\t>.menu-name {\r\n\t\t\t\theight: 44px;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t>.iconfont {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\tcolor: #007BFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.first {\r\n\t\t\tflex-shrink: 0;\r\n\t\t\twidth: 300rpx;\r\n\t\t\tbackground-color: #f0f0f0;\r\n\t\t\t.sub-menu {\r\n\t\t\t\tpadding-left: 15px;\r\n\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tbox-shadow: 5rpx 0 5rpx rgba($color: #000000, $alpha: 0.1);\r\n\t\t}\r\n\t\t&.alone {\r\n\t\t\tmax-height: 345px;\r\n\t\t\tmin-height: 170px;\r\n\t\t\theight: auto;\r\n\t\t\t.sub-menu {\r\n\t\t\t\tmin-height: calc(44px - 1rpx);\r\n\t\t\t\tmargin-left: 15px;\r\n\t\t\t\tborder-bottom: solid 1rpx #e5e5e5;\r\n\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tcolor: #007BFF;\r\n\r\n\t\t\t\t\t>.menu-name {\r\n\t\t\t\t\t\t>.iconfont {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.third{\r\n\t\t\t// box-shadow: 5rpx 0 20rpx rgba($color: #000000, $alpha: 0.2) inset;\r\n\t\t}\r\n\t\t&.not-first {\r\n\t\t\tbox-shadow: 5rpx 0 5rpx rgba($color: #000000, $alpha: 0.1);\r\n\t\t\t.sub-menu {\r\n\t\t\t\tmin-height: calc(44px - 1rpx);\r\n\t\t\t\tmargin-left: 15px;\r\n\t\t\t\tborder-bottom: solid 1rpx #e5e5e5;\r\n\t\t\t\t>.menu-name {\r\n\t\t\t\t\theight: calc(44px - 1rpx);\r\n\t\t\t\t\t>.iconfont {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\t\tcolor: #007BFF;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tcolor: #007BFF;\r\n\t\t\t\t\t>.menu-name {\r\n\t\t\t\t\t\t>.iconfont {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.more-sub-menu {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\tpadding-bottom: 9px;\r\n\t\t\t\t\t>text {\r\n\t\t\t\t\t\theight: 30px;\r\n\t\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\t\tcolor: #9b9b9b;\r\n\t\t\t\t\t\tmargin-bottom: 6px;\r\n\t\t\t\t\t\tmargin-right: 6px;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tline-height: 30px;\r\n\t\t\t\t\t\tborder: solid #f5f5f5 1rpx;\r\n\t\t\t\t\t\tflex: 0 0 calc(33.33% - 6px);\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\t\t&:nth-child(3n) {\r\n\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&.on {\r\n\t\t\t\t\t\t\tborder-color: #a9e8f6;\r\n\t\t\t\t\t\t\tcolor: #007BFF;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #9b9b9b;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.filter {\r\n\t\twidth: 100%;\r\n\t\theight: 345px;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\t.menu-box {\r\n\t\t\twidth: 698rpx;\r\n\t\t\theight: calc(345px - 75px);\r\n\t\t\tflex-shrink: 1;\r\n\t\t\t.box {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tpadding-top: 16px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-size: 13px;\r\n\t\t\t\t\tcolor: #888;\r\n\t\t\t\t}\r\n\t\t\t\t.labels {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.on {\r\n\t\t\t\t\t\tborder-color: #007BFF;\r\n\t\t\t\t\t\tbackground-color: #007BFF;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t>view {\r\n\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\twidth: calc((698rpx - 30rpx * 3) / 4);\r\n\t\t\t\t\t\theight: 30px;\r\n\t\t\t\t\t\tborder: solid 1rpx #adadad;\r\n\t\t\t\t\t\tborder-radius: 2px;\r\n\t\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t\t\tmargin-top: 8px;\r\n\t\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t&:nth-child(4n) {\r\n\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.btn-box {\r\n\t\t\tflex-shrink: 0;\r\n\t\t\twidth: 698rpx;\r\n\t\t\theight: 75px;\r\n\t\t\tflex-direction: row !important;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t>view {\r\n\t\t\t\twidth: 320rpx;\r\n\t\t\t\theight: 40px;\r\n\t\t\t\tborder-radius: 40px;\r\n\t\t\t\tborder: solid 1rpx #007BFF;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t}\r\n\t\t\t.reset {\r\n\t\t\t\tcolor: #007BFF;\r\n\t\t\t}\r\n\t\t\t.submit {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground-color: #007BFF;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.mask {\r\n\t\tz-index: 10;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0);\r\n\t\ttransition: background-color .15s linear;\r\n\t\t&.show {\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\t}\r\n\t\t&.hide {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t}\r\n\t/* 字体图标 */\r\n\t@font-face {\r\n\t\tfont-family: \"HM-FD-font\";\r\n\t\tsrc: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALAAAsAAAAABpQAAAJzAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgp4gQIBNgIkAwwLCAAEIAWEbQc5G8sFERWMIbIfCbbzqA4hp7InSBibVsYGb4J42o82b3e/nJlHMw/NHbGOlwKJRCRpwzPtpAECCOZubdqxjYpQLMlVg+70/08edrgQOtx2ukpVyApZn+dyehPoQObHo3O85rYx9vOjXoBxQIHugW2yIkqIW2QXcScu4jwE8CSWbKSmrqUHFwOaJoCsLM5P4haSGIxRcRHshrUGucLCVcfqI3AZfV/+USguKCwNmtsxVztDxU/n55C+3W0Z4QQpEOTNFqCBbMCAjDUWB9CIwWk87aa70cYgqLkyd3dEmm+18R8eKATEBrV7A5CulBT8dKiWOYZk412XNcDdKSEKSGODnyKIDl+dmVt9/Dx4pu/xyeutkMlHISGPTsPCnoTNP9nOT6wTtDdlO6dPr47efvj942lkYuQzrhMKEjq9N6y98P3340gmlJ/RStUD6F31CAEEPtUW94/7rf+7XgaAz57X0ZHXAGsFFwVgw38yALuMb0IBbVyNamFYEw4oKMDTj3AHRQP5Pt4dci9VwSVkRNQh5r7CLskZadhsWHhRDBsXczk8ZYk3ewnCxmQeQKa3BOHvA8XXO2j+vqRhf7CE+sPmn4anvoL29JLa4qqaUQkmoK+QG2osCckq7txi2leK86aIPyJ3eQZ8xytXYmyQ51jQndJAxIJlqiGSLsOqImiZCjTiZCJt6Lq26U2OoXqwUo0hRaAE0K5AziANy/uLVeXzWyjVqyjcoeupjxDr5MMDn8MDkLG9Aenu5ZrOSSoghAUsRmogkkahSoWAtnlUARnCkY3It0Iu7mWhdmd9Z/19BwBP6GidEi0G56opckXTGZVSPxgAAAA=');\r\n\t}\r\n\t.iconfont {\r\n\t\tfont-family: \"HM-FD-font\" !important;\r\n\t\tfont-size: 13px;\r\n\t\tfont-style: normal;\r\n\t\tcolor: #757575;\r\n\t\t&.triangle {\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: \"\\e65a\";\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.selected {\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: \"\\e607\";\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./HM-filterDropdown.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./HM-filterDropdown.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115033987\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}