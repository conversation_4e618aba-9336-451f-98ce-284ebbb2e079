{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?6026", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?fe7f", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?df4a", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?ab7b", "uni-app:///restaurant/deposit/orderdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?0e9c", "webpack:///D:/qianhouduankaifabao/tiantianshande/restaurant/deposit/orderdetail.vue?ae8d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "pagenum", "boxShow", "nomore", "nodata", "bid", "orderid", "num", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "st", "handleClickMask", "takeout", "disabledScroll", "takeouts", "setTimeout", "formSubmit", "numbers"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgEpxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAd;QAAAI;MAAA;QACAQ;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;MACA;MACAL;QACAA;UAAAT;UAAAC;QAAA;UACA;YACAQ;YAAA;UACA;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IAEAQ;MACA;MACA;MACA;;MAEAP;QAAAT;QAAAC;QAAAgB;MAAA;QACA;UACAR;UAAA;QACA;QACAA;QACAM;UACAP;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrLA;AAAA;AAAA;AAAA;AAAglC,CAAgB,4jCAAG,EAAC,C;;;;;;;;;;;ACApmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "restaurant/deposit/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './restaurant/deposit/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=32c83c6a&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"restaurant/deposit/orderdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetail.vue?vue&type=template&id=32c83c6a&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.dateFormat(item.createtime)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.boxShow ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topbg\"></view>\r\n\t\t<view class=\"order-content\">\r\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\r\n\t\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'orderlog?id=' + item.id\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"img\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t<text class=\"t2\">数量：{{item.num}}</text>\r\n\t\t\t\t\t\t<text class=\"t2\">存入时间：{{dateFormat(item.createtime)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.status==0\" class=\"takeout st0\">审核中</view>\r\n\t\t\t\t\t<view v-if=\"item.status==1\" class=\"takeout\" @tap.stop=\"takeout\" :data-orderid=\"item.id\" :data-num=\"item.num\"><image src=\"/static/img/deposit_takeout.png\" class=\"img\"/>取出</view>\r\n\t\t\t\t\t<view v-if=\"item.status==2\" class=\"takeout st2\" :data-orderid=\"item.id\">已取走</view>\r\n\t\t\t\t\t<view v-if=\"item.status==3\" class=\"takeout st3\" :data-orderid=\"item.id\">未通过</view>\r\n\t\t\t\t\t<view v-if=\"item.status==4\" class=\"takeout st3\">已过期</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\r\n\t\t\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<view class=\"btn2\" @tap=\"takeouts\" data-orderid=\"0\">一键取出</view>\r\n\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'add?bid='+bid\">我要寄存</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\r\n\t<!-- 弹框 -->\r\n\t<view v-if=\"boxShow\" class=\"\" @touchmove.stop.prevent=\"disabledScroll\">\r\n\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t<view class=\"popup__modal\">\r\n\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t<text class=\"popup__title-text\">请输入取出数量</text>\r\n\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"popup__content takeoutBox\">\r\n\t\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">取出数量</text>\r\n\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"请输入要取出的数量\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"numbers\" :value=\"num\"></input>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"btn\" form-type=\"submit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t\t\t\r\n\t\t\t\t</form>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\r\n      datalist: [],\r\n      pagenum: 1,\r\n\t    boxShow:false,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n\t\t\tbid:0,\r\n\t\t\torderid:0,\r\n\t\t\tnum:1,\r\n \r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tif(this.opt && this.opt.bid){\r\n\t\t\tthis.bid = this.opt.bid;\r\n\t\t}\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.loading = true;\r\n      app.post('ApiRestaurantDeposit/orderdetail', {st: st,pagenum: pagenum,bid:that.opt.bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.datalist;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\t\thandleClickMask: function() {\t\r\n\t\t\tthis.boxShow = !this.boxShow;\r\n\t\t},\r\n    takeout: function (e) {\r\n       var that = this;\r\n       this.orderid = e.currentTarget.dataset.orderid;\r\n\t\t\t this.boxShow = true; //显示弹框\r\n\t\t\t this.num = e.currentTarget.dataset.num;\r\n    },\r\n\t\tdisabledScroll: function (e) {\r\n\t\t\treturn false;\r\n\t\t},\r\n\t\r\n\t\ttakeouts: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar orderid = e.currentTarget.dataset.orderid;\r\n\t\t\t\tapp.confirm('确定要全部取出吗?', function () {\r\n\t\t\t\tapp.post('ApiRestaurantDeposit/takeout', {bid:that.bid,orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tif(data.status== 0){\r\n\t\t\t\t\t\t\tapp.alert(data.msg);return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\r\n\tformSubmit: function (e) {\r\n\t\t var that = this;\r\n\t\t var formdata = e.detail.value;\r\n\t\t //alert(formdata.numbers);\r\n\t\t\r\n\t\tapp.post('ApiRestaurantDeposit/takeout', {bid:that.bid,orderid:that.orderid,numbers:formdata.numbers}, function (data) {\r\n\t\t\tif(data.status== 0){\r\n\t\t\t\tapp.alert(data.msg);return;\r\n\t\t\t}\r\n\t\t  app.success(data.msg);\r\n\t\t  setTimeout(function () {\r\n\t\t\t  that.boxShow = false; //隐藏弹框\r\n\t\t\t\tthat.getdata();\r\n\t\t  }, 1000);\r\n\t\t});\r\n\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ width:100%;}\r\n.topbg{width: 94%;margin:20rpx 3%;border-radius:8rpx;overflow:hidden}\r\n.topbg .img{width:100%;height:auto}\r\n.order-content{display:flex;flex-direction:column}\r\n.order-box{ width: 94%;margin:0 3%;margin-bottom:20rpx;padding:10rpx 0 10rpx 20rpx; background: #fff;border-radius:8px;display:flex;position:relative}\r\n.order-box .pic{ width: 120rpx; height: 120rpx;}\r\n.order-box .pic .img{ width: 120rpx; height: 120rpx;}\r\n.order-box .detail{display:flex;flex-direction:column;margin-left:20rpx;flex:1;margin-top:6rpx}\r\n.order-box .detail .t1{font-size:28rpx;font-weight:bold;height:40rpx;line-height:40rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .detail .t2{height: 36rpx;line-height: 36rpx;color: #999;overflow: hidden;font-size: 22rpx;}\r\n.order-box .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.order-box .takeout{display:flex;align-items:center;justify-content:center;padding:0 24rpx;height:52rpx;position:absolute;top:50%;margin-top:-26rpx;right:0;border-radius:26rpx 0 0 26rpx;background:#FFE8E1;color:#222222;font-size:24rpx;font-weight:bold}\r\n.order-box .takeout .img{width:28rpx;height:28rpx;margin-right:6rpx}\r\n.order-box .takeout.st0{color:#f55}\r\n.order-box .takeout.st2{background:#F7F7F7;color:#BBBBBB}\r\n.order-box .takeout.st3{background:#F7F7F7;color:#888}\r\n\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:0px dashed #ededed;overflow:hidden}\r\n.orderinfo .item{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-between;align-items:center;\r\nbox-shadow: 0px 10px 15px 0px rgba(0, 0, 0, 0.06);}\r\n.btn1{margin-left:20rpx;width:370rpx;height:88rpx;line-height:88rpx;color:#fff;border-radius:44rpx;text-align:center;font-weight:bold}\r\n.btn2{margin-left:20rpx;width:280rpx;height:88rpx;line-height:88rpx;color:#333;background:#fff;border:1px solid #cdcdcd;font-weight:bold;border-radius:44rpx;text-align:center}\r\n\r\n.takeoutBox .btn {border-radius:44rpx; margin: 0 auto; width: 96%; color: #FFF;}\r\n.takeoutBox { padding-bottom: 30rpx;}\r\n\r\n.popup__modal{ min-height: 0;position: fixed;} \r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115065721\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}