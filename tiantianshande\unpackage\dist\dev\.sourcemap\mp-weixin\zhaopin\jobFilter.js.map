{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?003c", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?6031", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?4a44", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?723e", "uni-app:///zhaopin/jobFilter.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?df9b", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobFilter.vue?862b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoading", "loadRetryCount", "selectedCities", "areaList", "provinceList", "cityList", "districtList", "provinceIndex", "cityIndex", "districtIndex", "currentProv<PERSON>ce", "currentCity", "currentDistrict", "currentProvinceName", "currentCityName", "currentDistrictName", "showAreaPicker", "salaryOptions", "value", "label", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "salaryRange", "salaryStartIndex", "salaryEndIndex", "salaryStartOptions", "length", "salaryEndOptions", "workModeOptions", "selectedWorkMode", "paymentOptions", "selectedPayment", "workTimeOptions", "start", "end", "selectedWorkTime", "workTimeRange", "ageV<PERSON><PERSON>", "watch", "jobCategories", "handler", "console", "immediate", "onLoad", "onShow", "onReady", "methods", "getJobCategories", "uni", "title", "mask", "app", "pid", "that", "id", "name", "description", "icon", "children", "sort", "setTimeout", "onProvinceChange", "onCityChange", "onDistrictChange", "addSelectedCity", "showCityPicker", "getAreaList", "removeCity", "selectSalary", "handleSalaryPickerChange", "selectWorkMode", "selectPayment", "selectWorkTime", "validateAndSubmit", "submitFilter", "workMode", "payment", "cities", "salary", "workTime", "age", "url", "closeAreaPicker", "formatSalary", "handleAgeInputChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwOlxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QAAAC;MAAA;QAAA;MAAA;QAAA;MAAA;MAAA;MACAC;QAAAD;MAAA;QAAA;MAAA;QAAA;MAAA;MACAE,kBACA;QAAAX;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAW;MACAC,iBACA;QAAAb;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAa;MACAC,kBACA;QAAAf;QAAAC;QAAAe;QAAAC;MAAA,GACA;QAAAjB;QAAAC;QAAAe;QAAAC;MAAA,GACA;QAAAjB;QAAAC;QAAAe;QAAAC;MAAA,GACA;QAAAjB;QAAAC;QAAAe;QAAAC;MAAA,EACA;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACAC;MACAC;QACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACAF;IACA;IACA;IACA;EACA;EAEAG;IACAH;IACA;MACAA;MACA;MACA;IACA;EACA;EAEAI;IACAJ;IACA;IACA;MACAA;MACA;IACA;EACA;EAEAK;IACAC;MACA;MACA;MAEAC;QACAC;QACAC;MACA;MAEAT;MACAU;QACAC;MACA;QACAJ;QACAK;QAEAZ;QAEA;UACA;YAAA;YACAA;YACA;cACAa;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;YAAA;UAAA;UAEAN;UACAZ;;UAEA;UACAY;;UAEA;UACAO;YACA;cACAnB;cACAO;gBACAC;gBACAQ;cACA;YACA;UACA;QACA;UACAhB;UACAO;YACAC;YACAQ;UACA;QACA;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QAEA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MAEA;MAEA;QACAhB;UACAC;UACAQ;QACA;QACA;MACA;MAEA;QACA;QACAT;UACAC;UACAQ;QACA;MACA;QACAT;UACAC;UACAQ;QACA;MACA;IACA;IAEAQ;MACA;QACAjB;UACAC;UACAQ;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEAS;MACA;MACAlB;QACAC;QACAC;MACA;MAEAC;QACAH;QACA;UACAK;UACAA;;UAEA;UACA;YACAA;YACAA;YACAA;YAEA;cACAA;cACAA;cACAA;cAEA;gBACAA;gBACAA;cACA;YACA;UACA;QACA;UACAL;YACAC;YACAQ;UACA;QACA;MACA;IACA;IAEAU;MACA;IACA;IAEAC;MACA;MACA;QAAA;MAAA;MACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;QAAA;QACA;QACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;QAAA;QACA;QACA;QACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;MAEA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACAzB;UACAC;UACAQ;QACA;QACA;MACA;MAEA;QACAT;UACAC;UACAQ;QACA;QACA;MACA;MAEA;IACA;IAEAiB;MAAA;QAAA;QAAA;QAAA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA,sDACA,8FACA;MACA,oEACA;QAAA;MAAA,0FACA;;MAEA;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAhC;QACAC;QACAC;MACA;MAEA;MACAC;QACAH;QAEA;UACAA;UACAA;UAEAA;YACAiC;UACA;QACA;UACAjC;YACAC;YACAQ;UACA;QACA;MACA;IACA;IAEAyB;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjrBA;AAAA;AAAA;AAAA;AAAq6C,CAAgB,g3CAAG,EAAC,C;;;;;;;;;;;ACAz7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/jobFilter.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/jobFilter.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jobFilter.vue?vue&type=template&id=618b5421&\"\nvar renderjs\nimport script from \"./jobFilter.vue?vue&type=script&lang=js&\"\nexport * from \"./jobFilter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jobFilter.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/jobFilter.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobFilter.vue?vue&type=template&id=618b5421&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var g0 = _vm.selectedCities.length\n  var m1 = _vm.t(\"color1\")\n  var m2 = _vm.t(\"color1\")\n  var l0 = _vm.__map(_vm.workModeOptions, function (mode, index) {\n    var $orig = _vm.__get_orig(mode)\n    var m3 = _vm.selectedWorkMode === mode.value ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m3: m3,\n    }\n  })\n  var m4 = _vm.t(\"color1\")\n  var l1 = _vm.__map(_vm.paymentOptions, function (payment, index) {\n    var $orig = _vm.__get_orig(payment)\n    var m5 = _vm.selectedPayment === payment.value ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m5: m5,\n    }\n  })\n  var m6 = _vm.t(\"color1\")\n  var l2 = _vm.__map(_vm.workTimeOptions, function (time, index) {\n    var $orig = _vm.__get_orig(time)\n    var m7 = _vm.selectedWorkTime === time.value ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m7: m7,\n    }\n  })\n  var m8 = _vm.t(\"color1\")\n  var m9 = _vm.formatSalary(_vm.salaryRange[0])\n  var m10 = _vm.formatSalary(_vm.salaryRange[1])\n  var g1 = _vm.salaryStartOptions.map(function (val) {\n    return _vm.formatSalary(val)\n  })\n  var m11 = _vm.formatSalary(_vm.salaryRange[0])\n  var g2 = _vm.salaryEndOptions.map(function (val) {\n    return _vm.formatSalary(val)\n  })\n  var m12 = _vm.formatSalary(_vm.salaryRange[1])\n  var l3 = _vm.__map(_vm.salaryOptions, function (salary, index) {\n    var $orig = _vm.__get_orig(salary)\n    var m13 = _vm.selectedSalary === salary.value ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m13: m13,\n    }\n  })\n  var m14 = _vm.t(\"color1\")\n  var m15 = _vm.t(\"color1\")\n  var m16 = _vm.t(\"color1\")\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e) {\n      return _vm.handleSalaryPickerChange(e, 0)\n    }\n    _vm.e1 = function (e) {\n      return _vm.handleSalaryPickerChange(e, 1)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        l0: l0,\n        m4: m4,\n        l1: l1,\n        m6: m6,\n        l2: l2,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        g1: g1,\n        m11: m11,\n        g2: g2,\n        m12: m12,\n        l3: l3,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobFilter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobFilter.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"job-filter\">\r\n    <scroll-view scroll-y class=\"filter-content\">\r\n      <!-- 期望城市移到第一个位置 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>期望城市</text>\r\n        </view>\r\n        <view class=\"location-row\">\r\n          <text class=\"location-hint\">选择你想要工作的区域</text>\r\n          <view class=\"location-selectors\">\r\n            <view class=\"selector-item\">\r\n              <picker @change=\"onProvinceChange\" :value=\"provinceIndex\" :range=\"provinceList\" range-key=\"name\">\r\n                <view class=\"picker-content\">\r\n                  <text>{{currentProvinceName || '江苏省'}}</text>\r\n                  <text class=\"arrow-down\">▼</text>\r\n                </view>\r\n              </picker>\r\n            </view>\r\n            <view class=\"selector-item\">\r\n              <picker @change=\"onCityChange\" :value=\"cityIndex\" :range=\"cityList\" range-key=\"name\" :disabled=\"!currentProvince\">\r\n                <view class=\"picker-content\">\r\n                  <text>{{currentCityName || '苏州市'}}</text>\r\n                  <text class=\"arrow-down\">▼</text>\r\n                </view>\r\n              </picker>\r\n            </view>\r\n            <view class=\"selector-item\">\r\n              <picker @change=\"onDistrictChange\" :value=\"districtIndex\" :range=\"districtList\" range-key=\"name\" :disabled=\"!currentCity\">\r\n                <view class=\"picker-content\">\r\n                  <text>{{currentDistrictName || '常熟市'}}</text>\r\n                  <text class=\"arrow-down\">▼</text>\r\n                </view>\r\n              </picker>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"city-list\" v-if=\"selectedCities.length > 0\">\r\n            <view \r\n              v-for=\"(city, index) in selectedCities\" \r\n              :key=\"index\"\r\n              class=\"city-tag\"\r\n            >\r\n              {{ city }}\r\n              <text class=\"delete-icon\" @tap=\"removeCity(index)\">×</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"add-city-btn\" @tap=\"addSelectedCity\" :style=\"{\r\n            backgroundColor: t('color1'),\r\n            color: '#ffffff'\r\n          }\">\r\n            <text class=\"add-icon\" style=\"color: #ffffff;\">+</text>\r\n            <text>确定</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 工作形式和结算方式 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>工作形式</text>\r\n        </view>\r\n        <view class=\"option-list\">\r\n          <view \r\n            v-for=\"(mode, index) in workModeOptions\" \r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n            :class=\"{ active: selectedWorkMode === mode.value }\"\r\n            @tap=\"selectWorkMode(mode.value)\"\r\n            :style=\"{\r\n              background: selectedWorkMode === mode.value \r\n                ? t('color1')\r\n                : '#f8f8f8',\r\n              color: selectedWorkMode === mode.value ? '#fff' : '#666'\r\n            }\"\r\n          >\r\n            {{ mode.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>结算方式</text>\r\n        </view>\r\n        <view class=\"option-list\">\r\n          <view \r\n            v-for=\"(payment, index) in paymentOptions\" \r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n            :class=\"{ active: selectedPayment === payment.value }\"\r\n            @tap=\"selectPayment(payment.value)\"\r\n            :style=\"{\r\n              background: selectedPayment === payment.value \r\n                ? t('color1')\r\n                : '#f8f8f8',\r\n              color: selectedPayment === payment.value ? '#fff' : '#666'\r\n            }\"\r\n          >\r\n            {{ payment.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 工作时间 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>工作时间</text>\r\n        </view>\r\n        <view class=\"worktime-options\">\r\n          <view \r\n            v-for=\"(time, index) in workTimeOptions\" \r\n            :key=\"index\"\r\n            class=\"worktime-item\"\r\n            :class=\"{ active: selectedWorkTime === time.value }\"\r\n            @tap=\"selectWorkTime(time.value)\"\r\n            :style=\"{\r\n              background: selectedWorkTime === time.value \r\n                ? t('color1')\r\n                : '#f8f8f8',\r\n              color: selectedWorkTime === time.value ? '#fff' : '#666'\r\n            }\"\r\n          >\r\n            {{ time.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n    \r\n\r\n      <!-- 薪资范围 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>薪资范围</text>\r\n        </view>\r\n        <view class=\"salary-selector\">\r\n          <view class=\"range-text\">{{formatSalary(salaryRange[0])}} - {{formatSalary(salaryRange[1])}}</view>\r\n          <view class=\"selector-group\">\r\n            <view class=\"selector-container\">\r\n              <picker \r\n                @change=\"(e) => handleSalaryPickerChange(e, 0)\" \r\n                :value=\"salaryStartIndex\" \r\n                :range=\"salaryStartOptions.map(val => formatSalary(val))\"\r\n              >\r\n                <view class=\"selector-content\">\r\n                  <text>{{formatSalary(salaryRange[0])}}</text>\r\n                  <text class=\"arrow-down\">▼</text>\r\n                </view>\r\n              </picker>\r\n            </view>\r\n            <view class=\"selector-separator\">至</view>\r\n            <view class=\"selector-container\">\r\n              <picker \r\n                @change=\"(e) => handleSalaryPickerChange(e, 1)\" \r\n                :value=\"salaryEndIndex\" \r\n                :range=\"salaryEndOptions.map(val => formatSalary(val))\"\r\n              >\r\n                <view class=\"selector-content\">\r\n                  <text>{{formatSalary(salaryRange[1])}}</text>\r\n                  <text class=\"arrow-down\">▼</text>\r\n                </view>\r\n              </picker>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"salary-options\">\r\n          <view \r\n            v-for=\"(salary, index) in salaryOptions\" \r\n            :key=\"index\"\r\n            class=\"salary-item\"\r\n            :class=\"{ active: selectedSalary === salary.value }\"\r\n            @tap=\"selectSalary(salary.value)\"\r\n            :style=\"{\r\n              background: selectedSalary === salary.value \r\n                ? t('color1')\r\n                : '#f8f8f8',\r\n              color: selectedSalary === salary.value ? '#fff' : '#666'\r\n            }\"\r\n          >\r\n            {{ salary.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n\r\n        <!-- 年龄移动到这里作为最后一项 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-title\">\r\n          <view class=\"title-line\" :style=\"{backgroundColor: t('color1')}\"></view>\r\n          <text>年龄</text>\r\n        </view>\r\n        <view class=\"location-row\">\r\n          <text class=\"location-hint\">请输入您身份证上面的年龄</text>\r\n        <view class=\"age-input-container\">\r\n          <view class=\"age-input-wrapper single-age\">\r\n            <input \r\n              type=\"number\" \r\n              v-model=\"ageValue\" \r\n              placeholder=\"请输入年龄\" \r\n              class=\"age-input\"\r\n              maxlength=\"2\"\r\n            />\r\n            <text class=\"age-label\">岁</text>\r\n          </view>\r\n        </view>\r\n      </view></view>\r\n    </scroll-view>\r\n\r\n    <!-- 底部按钮 -->\r\n    <view class=\"footer safe-area-bottom\">\r\n      <view class=\"btn-group\">\r\n        <button \r\n          class=\"next-btn full-width\" \r\n          @tap=\"submitFilter\"\r\n          :style=\"{\r\n            background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`\r\n          }\"\r\n        >\r\n          开始匹配\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp()\r\nexport default {\r\n  data() {\r\n    return {\r\n      isLoading: true,\r\n      loadRetryCount: 0,\r\n      selectedCities: [],\r\n      areaList: [],\r\n      // 省市区镇选择相关数据\r\n      provinceList: [],\r\n      cityList: [],\r\n      districtList: [],\r\n      provinceIndex: 0,\r\n      cityIndex: 0,\r\n      districtIndex: 0,\r\n      currentProvince: null,\r\n      currentCity: null,\r\n      currentDistrict: null,\r\n      currentProvinceName: '',\r\n      currentCityName: '',\r\n      currentDistrictName: '',\r\n      showAreaPicker: false,\r\n      salaryOptions: [\r\n        { value: 1, label: '3千~5千', min: 3000, max: 5000 },\r\n        { value: 2, label: '4千~6千', min: 4000, max: 6000 },\r\n        { value: 3, label: '5千~7千', min: 5000, max: 7000 },\r\n        { value: 4, label: '6千~8千', min: 6000, max: 8000 },\r\n        { value: 5, label: '7千~9千', min: 7000, max: 9000 },\r\n        { value: 6, label: '8千~1万', min: 8000, max: 10000 },\r\n        { value: 7, label: '9千~1.1万', min: 9000, max: 11000 },\r\n        { value: 8, label: '1万~1.2万', min: 10000, max: 12000 },\r\n        { value: 9, label: '1.2万~1.5万', min: 12000, max: 15000 },\r\n        { value: 10, label: '不限', min: 2000, max: 100000 }\r\n      ],\r\n      selectedSalary: null,\r\n      salaryRange: [2000, 100000],\r\n      salaryStartIndex: 0, // 默认2000元\r\n      salaryEndIndex: 98, // 默认10万元\r\n      salaryStartOptions: Array.from({ length: 99 }, (_, i) => i * 1000 + 2000).filter(val => val <= 100000), // 从2000元到10万元\r\n      salaryEndOptions: Array.from({ length: 99 }, (_, i) => i * 1000 + 2000).filter(val => val <= 100000),\r\n      workModeOptions: [\r\n        { value: 1, label: '长期工' },\r\n        { value: 2, label: '短期工' },\r\n        { value: 3, label: '临时工' }\r\n      ],\r\n      selectedWorkMode: null,\r\n      paymentOptions: [\r\n        { value: 1, label: '月结' },\r\n        { value: 2, label: '周结' },\r\n        { value: 3, label: '日结' },\r\n        { value: 4, label: '完工结' }\r\n      ],\r\n      selectedPayment: null,\r\n      workTimeOptions: [\r\n        { value: 1, label: '白班', start: 8, end: 17 },\r\n        { value: 2, label: '夜班', start: 18, end: 8 },\r\n        { value: 3, label: '两班倒', start: 0, end: 24 },\r\n        { value: 4, label: '不限', start: 0, end: 24 }\r\n      ],\r\n      selectedWorkTime: null,\r\n      workTimeRange: [9, 18],\r\n      \r\n      // 年龄相关数据 - 简化\r\n      ageValue: '',\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    jobCategories: {\r\n      handler(newVal) {\r\n        console.log('jobCategories数据变化：', newVal)\r\n        if (newVal && newVal.length > 0) {\r\n          this.isLoading = false\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    console.log('页面加载')\r\n    this.getJobCategories()\r\n    // 加载地区数据\r\n    this.getAreaList()\r\n  },\r\n\r\n  onShow() {\r\n    console.log('页面显示')\r\n    if (this.jobCategories.length === 0 && !this.isLoading && this.loadRetryCount < 3) {\r\n      console.log('数据为空，尝试重新加载')\r\n      this.loadRetryCount++\r\n      this.getJobCategories()\r\n    }\r\n  },\r\n\r\n  onReady() {\r\n    console.log('页面就绪')\r\n    // 确保组件完全挂载后数据依然存在\r\n    if (this.jobCategories.length === 0 && !this.isLoading) {\r\n      console.log('页面就绪时数据为空，尝试重新加载')\r\n      this.getJobCategories()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getJobCategories() {\r\n      const that = this\r\n      this.isLoading = true\r\n      \r\n      uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n      })\r\n      \r\n      console.log('开始获取职位类别数据')\r\n      app.post('apiZhaopin/getTypeList', {\r\n        pid: -1\r\n      }, function(res) {\r\n        uni.hideLoading()\r\n        that.isLoading = false\r\n        \r\n        console.log('接口返回数据：', res)\r\n        \r\n        if (res.status === 1 && res.data && Array.isArray(res.data)) {\r\n          const processedData = res.data.map(item => {\r\n            console.log('处理类别项：', item)\r\n            return {\r\n              id: item.id,\r\n              name: item.name,\r\n              description: item.children?.length ? `包含${item.children.length}个细分职位` : '暂无子分类',\r\n              icon: `/static/icons/job-${item.id}.png`,\r\n              children: item.children || [],\r\n              sort: item.sort || 0\r\n            }\r\n          }).sort((a, b) => b.sort - a.sort)\r\n          \r\n          that.jobCategories = processedData\r\n          console.log('处理后的职位类型数据：', that.jobCategories)\r\n          \r\n          // 强制更新视图\r\n          that.$forceUpdate()\r\n          \r\n          // 在下一个时间片段检查数据\r\n          setTimeout(() => {\r\n            if (that.jobCategories.length === 0) {\r\n              console.error('数据加载异常：数据为空')\r\n              uni.showToast({\r\n                title: '数据加载异常，请重试',\r\n                icon: 'none'\r\n              })\r\n            }\r\n          }, 100)\r\n        } else {\r\n          console.error('获取职位类型失败：', res.msg)\r\n          uni.showToast({\r\n            title: res.msg || '获取职位类型失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 省市区镇选择器相关方法\r\n    onProvinceChange(e) {\r\n      const index = e.detail.value;\r\n      this.provinceIndex = index;\r\n      this.currentProvince = this.provinceList[index];\r\n      this.currentProvinceName = this.currentProvince.name;\r\n      \r\n      // 重置城市和区域\r\n      this.cityList = this.currentProvince.children || [];\r\n      this.cityIndex = 0;\r\n      this.currentCity = null;\r\n      this.currentCityName = '';\r\n      this.districtList = [];\r\n      this.districtIndex = 0;\r\n      this.currentDistrict = null;\r\n      this.currentDistrictName = '';\r\n      \r\n      if (this.cityList.length > 0) {\r\n        this.currentCity = this.cityList[0];\r\n        this.currentCityName = this.currentCity.name;\r\n        this.districtList = this.currentCity.children || [];\r\n        \r\n        if (this.districtList.length > 0) {\r\n          this.currentDistrict = this.districtList[0];\r\n          this.currentDistrictName = this.currentDistrict.name;\r\n        }\r\n      }\r\n    },\r\n    \r\n    onCityChange(e) {\r\n      const index = e.detail.value;\r\n      this.cityIndex = index;\r\n      this.currentCity = this.cityList[index];\r\n      this.currentCityName = this.currentCity.name;\r\n      \r\n      // 重置区域\r\n      this.districtList = this.currentCity.children || [];\r\n      this.districtIndex = 0;\r\n      this.currentDistrict = null;\r\n      this.currentDistrictName = '';\r\n      \r\n      if (this.districtList.length > 0) {\r\n        this.currentDistrict = this.districtList[0];\r\n        this.currentDistrictName = this.currentDistrict.name;\r\n      }\r\n    },\r\n    \r\n    onDistrictChange(e) {\r\n      const index = e.detail.value;\r\n      this.districtIndex = index;\r\n      this.currentDistrict = this.districtList[index];\r\n      this.currentDistrictName = this.currentDistrict.name;\r\n    },\r\n    \r\n    addSelectedCity() {\r\n      if (!this.currentProvince || !this.currentCity || !this.currentDistrict) return;\r\n      \r\n      let fullAddress = `${this.currentProvinceName}${this.currentCityName}${this.currentDistrictName}`;\r\n      \r\n      if (this.selectedCities.length >= 5) {\r\n        uni.showToast({\r\n          title: '最多选择5个城市',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.selectedCities.includes(fullAddress)) {\r\n        this.selectedCities.push(fullAddress);\r\n        uni.showToast({\r\n          title: '添加成功',\r\n          icon: 'none'\r\n        });\r\n      } else {\r\n        uni.showToast({\r\n          title: '该地址已添加',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n\r\n    showCityPicker() {\r\n      if (this.selectedCities.length >= 5) {\r\n        uni.showToast({\r\n          title: '最多选择5个城市',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 如果还没有加载过地区数据，先加载\r\n      if (this.provinceList.length === 0) {\r\n        this.getAreaList();\r\n      }\r\n    },\r\n\r\n    getAreaList() {\r\n      const that = this;\r\n      uni.showLoading({\r\n        title: '加载中...',\r\n        mask: true\r\n      });\r\n      \r\n      app.get('apiZhaopin/getAreaList', {}, function(res) {\r\n        uni.hideLoading();\r\n        if (res.status === 1) {\r\n          that.areaList = res.data;\r\n          that.provinceList = res.data;\r\n          \r\n          // 设置默认值\r\n          if (that.provinceList.length > 0) {\r\n            that.currentProvince = that.provinceList[0];\r\n            that.currentProvinceName = that.currentProvince.name;\r\n            that.cityList = that.currentProvince.children || [];\r\n            \r\n            if (that.cityList.length > 0) {\r\n              that.currentCity = that.cityList[0];\r\n              that.currentCityName = that.currentCity.name;\r\n              that.districtList = that.currentCity.children || [];\r\n              \r\n              if (that.districtList.length > 0) {\r\n                that.currentDistrict = that.districtList[0];\r\n                that.currentDistrictName = that.currentDistrict.name;\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取地区列表失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    removeCity(index) {\r\n      this.selectedCities.splice(index, 1)\r\n    },\r\n\r\n    selectSalary(value) {\r\n      this.selectedSalary = value\r\n      const option = this.salaryOptions.find(item => item.value === value)\r\n      if (option) {\r\n        this.salaryRange = [option.min, option.max]\r\n        this.salaryStartIndex = this.salaryStartOptions.indexOf(option.min)\r\n        this.salaryEndIndex = this.salaryEndOptions.indexOf(option.max)\r\n      }\r\n    },\r\n\r\n    handleSalaryPickerChange(e, index) {\r\n      const value = this.salaryStartOptions[e.detail.value];\r\n      \r\n      if (index === 0) { // 起始薪资\r\n        this.salaryStartIndex = e.detail.value;\r\n        if (value < this.salaryRange[1]) {\r\n          this.salaryRange.splice(0, 1, value);\r\n        } else {\r\n          // 如果起始薪资大于等于结束薪资，调整结束薪资\r\n          const newEndSalary = Math.min(value + 5000, 100000);\r\n          this.salaryRange.splice(1, 1, newEndSalary);\r\n          this.salaryEndIndex = this.salaryEndOptions.indexOf(newEndSalary);\r\n        }\r\n      } else if (index === 1) { // 结束薪资\r\n        this.salaryEndIndex = e.detail.value;\r\n        const endValue = this.salaryEndOptions[e.detail.value];\r\n        if (endValue > this.salaryRange[0]) {\r\n          this.salaryRange.splice(1, 1, endValue);\r\n        } else {\r\n          // 如果结束薪资小于等于起始薪资，调整起始薪资\r\n          const newStartSalary = Math.max(endValue - 5000, 2000);\r\n          this.salaryRange.splice(0, 1, newStartSalary);\r\n          this.salaryStartIndex = this.salaryStartOptions.indexOf(newStartSalary);\r\n        }\r\n      }\r\n      \r\n      this.selectedSalary = null; // 清除预设选项的选中状态\r\n    },\r\n\r\n    selectWorkMode(value) {\r\n      this.selectedWorkMode = value\r\n    },\r\n\r\n    selectPayment(value) {\r\n      this.selectedPayment = value\r\n    },\r\n\r\n    selectWorkTime(value) {\r\n      this.selectedWorkTime = value;\r\n      const option = this.workTimeOptions.find(item => item.value === value);\r\n      if (option) {\r\n        this.workTimeRange = [option.start, option.end];\r\n      }\r\n    },\r\n\r\n    validateAndSubmit() {\r\n      if (this.selectedWorkMode === null || this.selectedPayment === null) {\r\n        uni.showToast({\r\n          title: '请完善工作要求信息',\r\n          icon: 'none'\r\n        })\r\n        return false\r\n      }\r\n      \r\n      if (this.selectedCities.length === 0) {\r\n        uni.showToast({\r\n          title: '请至少选择期望城市',\r\n          icon: 'none'\r\n        })\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n\r\n    submitFilter() {\r\n      if (!this.validateAndSubmit()) return\r\n      \r\n      // 获取选中项的文字\r\n      const workModeText = this.workModeOptions.find(item => item.value === this.selectedWorkMode)?.label || '';\r\n      const paymentText = this.paymentOptions.find(item => item.value === this.selectedPayment)?.label || '';\r\n      const salaryRangeText = this.selectedSalary \r\n        ? `${this.formatSalary(this.salaryRange[0])}-${this.formatSalary(this.salaryRange[1])}` \r\n        : '不限';\r\n      const workTimeText = this.selectedWorkTime \r\n        ? this.workTimeOptions.find(item => item.value === this.selectedWorkTime)?.label \r\n        : '不限';\r\n      \r\n      // 添加年龄信息\r\n      const ageRangeText = `${this.ageValue}岁`\r\n\r\n      const filterData = {\r\n        workMode: workModeText,\r\n        payment: paymentText,\r\n        cities: this.selectedCities,\r\n        salary: salaryRangeText,\r\n        workTime: workTimeText,\r\n        age: ageRangeText\r\n      }\r\n\r\n      uni.showLoading({\r\n        title: '匹配中...',\r\n        mask: true\r\n      })\r\n\r\n      const that = this\r\n      app.post('apiZhaopin/jobMatch', filterData, function(res) {\r\n        uni.hideLoading()\r\n        \r\n        if (res.status === 1) {\r\n          uni.setStorageSync('jobMatchFilterData', filterData)\r\n          uni.setStorageSync('jobMatchResult', res)\r\n          \r\n          uni.navigateTo({\r\n            url: '/zhaopin/jobMatch?from=filter'\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '匹配失败，请重试',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    closeAreaPicker() {\r\n      this.showAreaPicker = false\r\n      this.currentProvince = null\r\n      this.currentCity = null\r\n      this.currentDistrict = null\r\n    },\r\n\r\n    formatSalary(value) {\r\n      if (value >= 10000) {\r\n        return (value / 10000).toFixed(1).replace(/\\.0$/, '') + '万元';\r\n      } else {\r\n        return value + '元';\r\n      }\r\n    },\r\n\r\n    handleAgeInputChange(index, value) {\r\n      const ageValue = parseInt(value);\r\n      if (!isNaN(ageValue)) {\r\n        // 确保年龄在合理范围内\r\n        const validAge = Math.min(Math.max(ageValue, 16), 70);\r\n        this.ageRange.splice(index, 1, validAge);\r\n        \r\n        // 确保最小年龄小于最大年龄\r\n        if (index === 0 && this.ageRange[0] > this.ageRange[1]) {\r\n          this.ageRange.splice(1, 1, this.ageRange[0]);\r\n        } else if (index === 1 && this.ageRange[1] < this.ageRange[0]) {\r\n          this.ageRange.splice(0, 1, this.ageRange[1]);\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.job-filter {\r\n  min-height: 100vh;\r\n  background-color: #f9f9f9;\r\n  display: flex;\r\n  flex-direction: column;\r\n  \r\n  .filter-content {\r\n    flex: 1;\r\n    height: calc(100vh - 120rpx);\r\n    padding: 20rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .filter-section {\r\n    margin-bottom: 20rpx;\r\n    background: #ffffff;\r\n    border-radius: 8rpx;\r\n    padding: 24rpx;\r\n    border: 1rpx solid #eaeaea;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n    \r\n    .section-title {\r\n      font-size: 30rpx;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      margin-bottom: 20rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      position: relative;\r\n      padding-left: 20rpx;\r\n      \r\n      .title-line {\r\n        display: inline-block;\r\n        position: relative;\r\n        left: -10rpx;\r\n        margin-right: 10rpx;\r\n        width: 6rpx;\r\n        height: 28rpx;\r\n        border-radius: 3rpx;\r\n        opacity: 0.9;\r\n      }\r\n    }\r\n    \r\n    .option-list {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 16rpx;\r\n      \r\n      .option-item {\r\n        padding: 16rpx 32rpx;\r\n        background-color: #f5f5f5;\r\n        border-radius: 4rpx;\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n        border: 1rpx solid #eaeaea;\r\n        \r\n        &:active {\r\n          opacity: 0.8;\r\n        }\r\n        \r\n        &.active {\r\n          color: #ffffff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .age-selector,\r\n  .salary-selector,\r\n  .worktime-selector {\r\n    padding: 16rpx 0;\r\n    \r\n    .range-text {\r\n      text-align: center;\r\n      font-size: 30rpx;\r\n      color: #333333;\r\n      margin-bottom: 24rpx;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    .selector-group {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 0 20rpx;\r\n      \r\n      .selector-container {\r\n        flex: 1;\r\n        max-width: 45%;\r\n        \r\n        .selector-content,\r\n        .age-picker-content {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding: 16rpx 24rpx;\r\n          background-color: #f8f8f8;\r\n          border-radius: 4rpx;\r\n          font-size: 28rpx;\r\n          color: #333333;\r\n          border: 1rpx solid #eaeaea;\r\n        }\r\n        \r\n        .arrow-down {\r\n          margin-left: 8rpx;\r\n          font-size: 22rpx;\r\n          color: #999999;\r\n        }\r\n      }\r\n      \r\n      .selector-separator {\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .salary-options,\r\n  .age-options {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 16rpx;\r\n    margin-top: 24rpx;\r\n    \r\n    .salary-item,\r\n    .age-item {\r\n      text-align: center;\r\n      padding: 16rpx;\r\n      background-color: #f5f5f5;\r\n      border-radius: 4rpx;\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      border: 1rpx solid #eaeaea;\r\n      \r\n      &:active {\r\n        opacity: 0.8;\r\n      }\r\n      \r\n      &.active {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .worktime-options {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 16rpx;\r\n    margin-top: 16rpx;\r\n    \r\n    .worktime-item {\r\n      padding: 12rpx 24rpx;\r\n      border-radius: 4rpx;\r\n      font-size: 26rpx;\r\n      background-color: #f5f5f5;\r\n      color: #333333;\r\n      border: 1rpx solid #eaeaea;\r\n      min-width: 140rpx;\r\n      text-align: center;\r\n      \r\n      &:active {\r\n        opacity: 0.8;\r\n      }\r\n      \r\n      &.active {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .location-row {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16rpx;\r\n    \r\n    .location-hint {\r\n      font-size: 26rpx;\r\n      color: #999999;\r\n      margin-bottom: 12rpx;\r\n    }\r\n    \r\n    .location-selectors {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 16rpx;\r\n      margin-bottom: 16rpx;\r\n      \r\n      .selector-item {\r\n        position: relative;\r\n        flex: 1;\r\n        min-width: 160rpx;\r\n        \r\n        .picker-content {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding: 16rpx 24rpx;\r\n          background-color: #ffffff;\r\n          border-radius: 4rpx;\r\n          font-size: 26rpx;\r\n          color: #333333;\r\n          border: 1rpx solid #ddd;\r\n        }\r\n        \r\n        .arrow-down {\r\n          margin-left: 8rpx;\r\n          font-size: 20rpx;\r\n          color: #999999;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .location-near {\r\n      display: none; /* 隐藏定位附近按钮 */\r\n    }\r\n  }\r\n  \r\n  .city-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 16rpx;\r\n    \r\n    .city-tag {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12rpx 24rpx;\r\n      background-color: #f5f5f5;\r\n      border-radius: 4rpx;\r\n      font-size: 26rpx;\r\n      color: #333333;\r\n      border: 1rpx solid #eaeaea;\r\n      \r\n      .delete-icon {\r\n        margin-left: 12rpx;\r\n        font-size: 28rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n\r\n  .footer {\r\n    padding: 20rpx;\r\n    background-color: #fff;\r\n    border-top: 1rpx solid #eaeaea;\r\n    \r\n    &.safe-area-bottom {\r\n      padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));\r\n      padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n    }\r\n    \r\n    .btn-group {\r\n      display: flex;\r\n      \r\n      button {\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        border-radius: 4rpx;\r\n        font-size: 30rpx;\r\n        font-weight: 500;\r\n        \r\n        &:active {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n      \r\n      .next-btn {\r\n        flex: 1;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .add-city-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 16rpx 24rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 4rpx;\r\n    font-size: 26rpx;\r\n    color: #333333;\r\n    border: 1rpx solid #ddd;\r\n    margin-bottom: 16rpx;\r\n    width: fit-content;\r\n    \r\n    .add-icon {\r\n      margin-right: 8rpx;\r\n      font-size: 30rpx;\r\n      color: #666;\r\n    }\r\n    \r\n    &:active {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n\r\n  .age-input-container {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 20rpx 0;\r\n    \r\n    .age-input-wrapper {\r\n      display: flex;\r\n      align-items: center;\r\n      background-color: #f8f8f8;\r\n      border-radius: 4rpx;\r\n      padding: 0 20rpx;\r\n      border: 1rpx solid #eaeaea;\r\n      flex: 1;\r\n      \r\n      &.single-age {\r\n        max-width: 60%;\r\n        justify-content: center;\r\n      }\r\n      \r\n      .age-input {\r\n        height: 80rpx;\r\n        flex: 1;\r\n        font-size: 28rpx;\r\n        text-align: center;\r\n      }\r\n      \r\n      .age-label {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        margin-left: 8rpx;\r\n      }\r\n    }\r\n    \r\n    .age-separator {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 375px) {\r\n  .job-filter {\r\n    .filter-content {\r\n      padding: 16rpx;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobFilter.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobFilter.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115048278\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}