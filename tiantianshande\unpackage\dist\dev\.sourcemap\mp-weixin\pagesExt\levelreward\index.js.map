{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?b1e7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?1faf", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?102d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?d9a1", "uni-app:///pagesExt/levelreward/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?9fb8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/levelreward/index.vue?eab2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "rewardRule", "pagenum", "datalist", "topbackhide", "nomore", "nodata", "statsData", "myR<PERSON>errer", "myReferrals", "referralsCount", "referralsPagenum", "referralsNomore", "referralsNodata", "onLoad", "onPullDownRefresh", "onReachBottom", "onPageScroll", "uni", "onShareAppMessage", "title", "that", "getdata", "app", "id", "pic", "changetab", "scrollTop", "duration", "getDataList", "limit", "getReferralsList", "formatTime", "goBack", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6L9wB;AAAA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MAAAC;IAAA;EACA;AAAA,wEACA;EACA;EACA;EACA;IACAC;EACA;EACA;IACAA;EACA;AACA,oEACA;EACAC;IACA;IACA;IACAD;IACAE;MAAAC;IAAA;MACAH;MACA;QACAA;QACAA;UAAAD;UAAAK;QAAA;QACAP;UACAE;QACA;QACAC;MACA;QACAE;MACA;IACA;EACA;EACAG;IACA;IACA;IACA;MACA;MACA;MACA;MACA;IACA;MACA;MACA;MACA;MACA;IACA;IACAR;MACAS;MACAC;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;IACA;IACA;IACAR;IACAA;IACAA;IAEA;MACAA;MACAA;IACA;MACAE;QAAArB;QAAA4B;MAAA;QACAT;QACAH;QACA;QACA;UACAG;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;MACAE;QACAF;QAEAE;UACAF;UAEA;YACAA;YACAA;UACA;YACAA;YACAH;UACA;QACA;MACA;IACA;EACA;EACAa;IACA;IACA;IACAV;IAEAE;MAAArB;MAAA4B;IAAA;MACAT;MACAH;MACA;MACAG;MAEA;QACAA;QACA;UACAA;QACA;MACA;QACA;UACAA;QACA;UACA;UACA;UACAA;QACA;MACA;IACA;EACA;EACAW;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;EACA;EACAC;IACAf;MACAgB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClYA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/levelreward/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/levelreward/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7401d09c&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/levelreward/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=7401d09c&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var g0 =\n    _vm.isload && _vm.rewardRule.name ? _vm.rewardRule.name.substr(0, 1) : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.rewardRule.from_level_names, function (levelName, idx) {\n          var $orig = _vm.__get_orig(levelName)\n          var g1 =\n            _vm.rewardRule.from_level_names &&\n            _vm.rewardRule.from_level_names.length > 0\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var l1 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.rewardRule.to_level_names, function (levelName, idx) {\n          var $orig = _vm.__get_orig(levelName)\n          var g2 =\n            _vm.rewardRule.to_level_names &&\n            _vm.rewardRule.to_level_names.length > 0\n          return {\n            $orig: $orig,\n            g2: g2,\n          }\n        })\n      : null\n  var l2 =\n    _vm.isload && _vm.st == 0\n      ? _vm.__map(_vm.rewardRule.reward_rules, function (rule, idx) {\n          var $orig = _vm.__get_orig(rule)\n          var m4 = _vm.t(\"color1\")\n          var m5 = rule.reward_type === \"balance\" ? _vm.t(\"余额\") : null\n          var m6 =\n            !(rule.reward_type === \"balance\") && rule.reward_type === \"points\"\n              ? _vm.t(\"积分\")\n              : null\n          var m7 =\n            !(rule.reward_type === \"balance\") &&\n            !(rule.reward_type === \"points\") &&\n            rule.reward_type === \"commission\"\n              ? _vm.t(\"佣金\")\n              : null\n          var m8 =\n            !(rule.reward_type === \"balance\") &&\n            !(rule.reward_type === \"points\") &&\n            !(rule.reward_type === \"commission\") &&\n            rule.reward_type === \"xianjinquan\"\n              ? _vm.t(\"现金券\")\n              : null\n          var m9 =\n            !(rule.reward_type === \"balance\") &&\n            !(rule.reward_type === \"points\") &&\n            !(rule.reward_type === \"commission\") &&\n            !(rule.reward_type === \"xianjinquan\")\n              ? _vm.t(\"贡献值\")\n              : null\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n            m9: m9,\n          }\n        })\n      : null\n  var m10 =\n    _vm.isload && _vm.st == 0 ? _vm.formatTime(_vm.rewardRule.createtime) : null\n  var g3 = _vm.isload && _vm.st == 1 ? _vm.datalist.length : null\n  var l3 =\n    _vm.isload && _vm.st == 1 && g3 > 0\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.t(\"color1\")\n          var m12 =\n            item.reward_type_name ||\n            (item.reward_type === \"balance\"\n              ? _vm.t(\"余额\")\n              : item.reward_type === \"points\"\n              ? _vm.t(\"积分\")\n              : item.reward_type === \"commission\"\n              ? _vm.t(\"佣金\")\n              : item.reward_type === \"xianjinquan\"\n              ? _vm.t(\"现金券\")\n              : _vm.t(\"贡献值\"))\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n          }\n        })\n      : null\n  var g4 = _vm.isload && _vm.st == 2 ? _vm.myReferrals.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        m10: m10,\n        g3: g3,\n        l3: l3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n    <block v-if=\"isload\">\r\n        <view class=\"container nodiydata\">\r\n            <view class=\"topcontent\">\r\n                <view class=\"logo\">\r\n                    <view class=\"level-icon\" :style=\"{background:t('color1')}\">{{rewardRule.name ? rewardRule.name.substr(0,1) : 'R'}}</view>\r\n                </view>\r\n                <view class=\"title\">{{rewardRule.name}}</view>\r\n                <view class=\"desc\">\r\n                    <view class=\"status-tag\" :class=\"rewardRule.status == 1 ? 'active' : 'inactive'\">\r\n                        {{rewardRule.status == 1 ? '启用中' : '已禁用'}}\r\n                    </view>\r\n                </view>\r\n            </view>\r\n\r\n            <view class=\"contentbox\">\r\n                <view class=\"shop_tab\">\r\n                    <view :class=\"'cptab_text ' + (st==0?'cptab_current':'')\" @tap=\"changetab\" data-st=\"0\">规则详情<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n                    <view :class=\"'cptab_text ' + (st==1?'cptab_current':'')\" @tap=\"changetab\" data-st=\"1\">奖励记录<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n                    <view :class=\"'cptab_text ' + (st==2?'cptab_current':'')\" @tap=\"changetab\" data-st=\"2\">统计数据<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n                </view>\r\n\r\n                <!-- 规则详情 -->\r\n                <view class=\"cp_detail\" v-if=\"st==0\">\r\n                    <view class=\"detail-section\">\r\n                        <view class=\"section-title\">推荐人等级</view>\r\n                        <view class=\"section-content\">\r\n                            <view class=\"level-tag\" v-if=\"rewardRule.from_level_names && rewardRule.from_level_names.length > 0\" \r\n                                  v-for=\"(levelName, idx) in rewardRule.from_level_names\" :key=\"'from_'+idx\">\r\n                                {{levelName}}\r\n                            </view>\r\n                            <view class=\"level-tag\" v-else>{{rewardRule.from_level_name}}</view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"detail-section\">\r\n                        <view class=\"section-title\">被推荐人等级</view>\r\n                        <view class=\"section-content\">\r\n                            <view class=\"level-tag\" v-if=\"rewardRule.to_level_names && rewardRule.to_level_names.length > 0\" \r\n                                  v-for=\"(levelName, idx) in rewardRule.to_level_names\" :key=\"'to_'+idx\">\r\n                                {{levelName}}\r\n                            </view>\r\n                            <view class=\"level-tag\" v-else>{{rewardRule.to_level_name}}</view>\r\n                        </view>\r\n                    </view>\r\n                    \r\n                    <view class=\"detail-section\">\r\n                        <view class=\"section-title\">奖励模式</view>\r\n                        <view class=\"section-content\">\r\n                            <view class=\"reward-mode-tag\" :class=\"rewardRule.reward_mode\">\r\n                                <text class=\"reward-mode-icon\">\r\n                                    <text class=\"iconfont\" :class=\"rewardRule.reward_mode === 'regular' ? 'icon-gift' : 'icon-chart-line'\"></text>\r\n                                </text>\r\n                                <text class=\"reward-mode-text\">\r\n                                    {{rewardRule.reward_mode === 'regular' ? '常规奖励' : '累计创客奖励'}}\r\n                                </text>\r\n                                <view class=\"reward-mode-desc\">\r\n                                    {{rewardRule.reward_mode === 'regular' ? '每推荐一个人获得一次奖励' : '累计达到一定人数时发放奖励'}}\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"detail-section\">\r\n                        <view class=\"section-title\">奖励规则</view>\r\n                        <view class=\"section-content\">\r\n                            <view class=\"reward-rule-item\" v-for=\"(rule, idx) in rewardRule.reward_rules\" :key=\"idx\">\r\n                                <view class=\"rule-icon\" :style=\"{background:t('color1')}\">{{idx+1}}</view>\r\n                                <view class=\"rule-content\">\r\n                                    <view class=\"rule-text\">\r\n                                        <text v-if=\"rewardRule.reward_mode === 'regular'\">每推荐 <text class=\"highlight\">{{rule.recommend_count}}</text> 个人</text>\r\n                                        <text v-else>累计达到 <text class=\"highlight\">{{rule.recommend_count}}</text> 人</text>\r\n                                    </view>\r\n                                    <view class=\"rule-text\">奖励 <text class=\"highlight\">{{rule.reward_amount}}</text> \r\n                                        {{rule.reward_type === 'balance' ? t('余额') : \r\n                                          rule.reward_type === 'points' ? t('积分') : \r\n                                          rule.reward_type === 'commission' ? t('佣金') : \r\n                                          rule.reward_type === 'xianjinquan' ? t('现金券') : t('贡献值')}}\r\n                                    </view>\r\n                                </view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n\r\n                    <view class=\"detail-section\">\r\n                        <view class=\"section-title\">创建时间</view>\r\n                        <view class=\"section-content\">\r\n                            <view class=\"time-text\">{{formatTime(rewardRule.createtime)}}</view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n\r\n                <!-- 奖励记录 -->\r\n                <view class=\"cp_detail\" v-if=\"st==1\">\r\n                    <view class=\"rewards-list\">\r\n                        <block v-if=\"datalist.length > 0\">\r\n                            <view class=\"reward-record\" v-for=\"(item, index) in datalist\" :key=\"index\">\r\n                                <view class=\"record-header\">\r\n                                    <view class=\"user-info\">\r\n                                        <image class=\"avatar\" :src=\"item.to_headimg || '/static/img/default_avatar.png'\"></image>\r\n                                        <view class=\"user-details\">\r\n                                            <view class=\"nickname\">{{item.to_nickname}}</view>\r\n                                            <view class=\"tel\">{{item.to_tel}}</view>\r\n                                        </view>\r\n                                    </view>\r\n                                    <view class=\"reward-amount\" :style=\"{color:t('color1')}\">\r\n                                        +{{item.reward_amount}} {{item.reward_type_name || \r\n                                          (item.reward_type === 'balance' ? t('余额') : \r\n                                           item.reward_type === 'points' ? t('积分') : \r\n                                           item.reward_type === 'commission' ? t('佣金') : \r\n                                           item.reward_type === 'xianjinquan' ? t('现金券') : t('贡献值'))}}\r\n                                    </view>\r\n                                </view>\r\n                                <view class=\"record-content\">\r\n                                    <view class=\"level-info\">\r\n                                        <text>推荐人等级: {{item.from_level_name}}</text>\r\n                                        <text class=\"separator\">|</text>\r\n                                        <text>被推荐人等级: {{item.to_level_name}}</text>\r\n                                    </view>\r\n                                    <view class=\"record-time\">{{item.createtime_format}}</view>\r\n                                </view>\r\n                            </view>\r\n                        </block>\r\n                        <nomore v-if=\"nomore\"></nomore>\r\n                        <nodata v-if=\"nodata\"></nodata>\r\n                    </view>\r\n                </view>\r\n\r\n                <!-- 统计数据 -->\r\n                <view class=\"cp_detail\" v-if=\"st==2\">\r\n                    <view class=\"stats-cards\">\r\n                        <view class=\"stats-card\">\r\n                            <view class=\"stats-value\">{{statsData.total_recommend || 0}}</view>\r\n                            <view class=\"stats-label\">总推荐人数</view>\r\n                        </view>\r\n                        <view class=\"stats-card\">\r\n                            <view class=\"stats-value\">{{statsData.today_recommend || 0}}</view>\r\n                            <view class=\"stats-label\">今日推荐</view>\r\n                        </view>\r\n                        <view class=\"stats-card\">\r\n                            <view class=\"stats-value\">{{statsData.total_reward || 0}}</view>\r\n                            <view class=\"stats-label\">总奖励金额</view>\r\n                        </view>\r\n                    </view>\r\n                    \r\n                    <view class=\"my-referrer\" v-if=\"myReferrer\">\r\n                        <view class=\"section-title\">我的推荐人</view>\r\n                        <view class=\"referrer-card\">\r\n                            <image class=\"referrer-avatar\" :src=\"myReferrer.headimg || '/static/img/default_avatar.png'\"></image>\r\n                            <view class=\"referrer-info\">\r\n                                <view class=\"referrer-name\">{{myReferrer.nickname}}</view>\r\n                                <view class=\"referrer-tel\">{{myReferrer.tel}}</view>\r\n                                <view class=\"referrer-level\">等级: {{myReferrer.level_name}}</view>\r\n                            </view>\r\n                        </view>\r\n                    </view>\r\n                    \r\n                    <view class=\"my-referrals\">\r\n                        <view class=\"section-title\">我推荐的会员 ({{referralsCount || 0}})</view>\r\n                        <view class=\"referrals-list\" v-if=\"myReferrals.length > 0\">\r\n                            <view class=\"referral-item\" v-for=\"(item, index) in myReferrals\" :key=\"index\">\r\n                                <image class=\"referral-avatar\" :src=\"item.headimg || '/static/img/default_avatar.png'\"></image>\r\n                                <view class=\"referral-info\">\r\n                                    <view class=\"referral-name\">{{item.nickname}}</view>\r\n                                    <view class=\"referral-level\">{{item.level_name}}</view>\r\n                                </view>\r\n                                <view class=\"referral-time\">{{item.createtime_format}}</view>\r\n                            </view>\r\n                        </view>\r\n                        <nomore v-if=\"referralsNomore\"></nomore>\r\n                        <nodata v-if=\"referralsNodata\"></nodata>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n            \r\n            <view class=\"covermy\" style=\"top:75vh;background:rgba(0,0,0,0.7)\" @tap=\"goBack\">\r\n                <text style=\"padding:0 4rpx;height:36rpx;line-height:36rpx\">返回</text>\r\n                <text style=\"padding:0 4rpx;height:36rpx;line-height:36rpx\">上一页</text>\r\n            </view>\r\n        </view>\r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n    data() {\r\n        return {\r\n            opt:{},\r\n            loading:false,\r\n            isload: false,\r\n            menuindex:-1,\r\n            pre_url:app.globalData.pre_url,\r\n\r\n            st: 0,\r\n            rewardRule:{},\r\n            pagenum: 1,\r\n            datalist: [],\r\n            topbackhide: false,\r\n            nomore: false,\r\n            nodata:false,\r\n            \r\n            statsData: {},\r\n            myReferrer: null,\r\n            myReferrals: [],\r\n            referralsCount: 0,\r\n            referralsPagenum: 1,\r\n            referralsNomore: false,\r\n            referralsNodata: false\r\n        }\r\n    },\r\n    onLoad: function (opt) {\r\n        this.opt = app.getopts(opt);\r\n        this.st = this.opt.st || 0;\r\n        this.getdata();\r\n    },\r\n    onPullDownRefresh: function () {\r\n        this.pagenum = 1;\r\n        this.referralsPagenum = 1;\r\n        this.getdata();\r\n    },\r\n    onReachBottom: function () {\r\n        if (this.st == 1 && !this.nodata && !this.nomore) {\r\n            this.pagenum = this.pagenum + 1;\r\n            this.getDataList(true);\r\n        } else if (this.st == 2 && !this.referralsNodata && !this.referralsNomore) {\r\n            this.referralsPagenum = this.referralsPagenum + 1;\r\n            this.getReferralsList(true);\r\n        }\r\n    },\r\n    onPageScroll: function (e) {\r\n        uni.$emit('onPageScroll',e);\r\n    },\r\n    onShareAppMessage:function(){\r\n        return this._sharewx({title:this.rewardRule.name || '等级推荐奖励'});\r\n    },\r\n    onPageScroll: function (e) {\r\n        var that = this;\r\n        var scrollY = e.scrollTop;\r\n        if (scrollY > 200 && !that.topbackhide) {\r\n            that.topbackhide = true;\r\n        }\r\n        if (scrollY < 150 && that.topbackhide) {\r\n            that.topbackhide = false;\r\n        }\r\n    },\r\n    methods: {\r\n        getdata: function () {\r\n            var that = this;\r\n            var id = that.opt.id || 0;\r\n            that.loading = true;\r\n            app.get('ApiLevelreward/getlist', {id: id}, function (res) {\r\n                that.loading = false;\r\n                if(res.data && res.data.length > 0) {\r\n                    that.rewardRule = res.data[0]; // 获取规则详情\r\n                    that.loaded({title:that.rewardRule.name,pic:''});\r\n                    uni.setNavigationBarTitle({\r\n                        title: that.rewardRule.name\r\n                    });\r\n                    that.getDataList();\r\n                } else {\r\n                    app.alert('未找到奖励规则');\r\n                }\r\n            });\r\n        },\r\n        changetab: function (e) {\r\n            var st = e.currentTarget.dataset.st;\r\n            this.st = st;\r\n            if (st == 1) {\r\n                this.pagenum = 1;\r\n                this.datalist = [];\r\n                this.nomore = false;\r\n                this.nodata = false;\r\n            } else if (st == 2) {\r\n                this.referralsPagenum = 1;\r\n                this.myReferrals = [];\r\n                this.referralsNomore = false;\r\n                this.referralsNodata = false;\r\n            }\r\n            uni.pageScrollTo({\r\n                scrollTop: 0,\r\n                duration: 0\r\n            });\r\n            this.getDataList();\r\n        },\r\n        getDataList: function (loadmore) {\r\n            if(!loadmore){\r\n                this.pagenum = 1;\r\n                this.datalist = [];\r\n            }\r\n            var that = this;\r\n            var pagenum = that.pagenum;\r\n            var st = that.st;\r\n            that.loading = true;\r\n            that.nodata = false;\r\n            that.nomore = false;\r\n            \r\n            if(st == 0) {\r\n                that.loading = false;\r\n                that.nodata = false;\r\n            } else if(st == 1) {\r\n                app.post('ApiLevelreward/getrecord', {pagenum: pagenum, limit: 20}, function (res) {\r\n                    that.loading = false;\r\n                    uni.stopPullDownRefresh();\r\n                    var data = res.data;\r\n                    if (pagenum == 1) {\r\n                        that.datalist = data;\r\n                        if (data.length == 0) {\r\n                            that.nodata = true;\r\n                        }\r\n                    } else {\r\n                        if (data.length == 0) {\r\n                            that.nomore = true;\r\n                        } else {\r\n                            var datalist = that.datalist;\r\n                            var newdata = datalist.concat(data);\r\n                            that.datalist = newdata;\r\n                        }\r\n                    }\r\n                });\r\n            } else if(st == 2) {\r\n                app.post('ApiLevelreward/getstats', {}, function (res) {\r\n                    that.statsData = res.data;\r\n                    \r\n                    app.post('ApiLevelreward/getmyreferrer', {}, function (refRes) {\r\n                        that.myReferrer = refRes.data;\r\n                        \r\n                        if (!loadmore) {\r\n                            that.referralsPagenum = 1;\r\n                            that.getReferralsList(false);\r\n                        } else {\r\n                            that.loading = false;\r\n                            uni.stopPullDownRefresh();\r\n                        }\r\n                    });\r\n                });\r\n            }\r\n        },\r\n        getReferralsList: function(loadmore) {\r\n            var that = this;\r\n            var pagenum = that.referralsPagenum;\r\n            that.loading = true;\r\n            \r\n            app.post('ApiLevelreward/getmyreferrals', {pagenum: pagenum, limit: 10}, function (refsRes) {\r\n                that.loading = false;\r\n                uni.stopPullDownRefresh();\r\n                var data = refsRes.data;\r\n                that.referralsCount = refsRes.count;\r\n                \r\n                if (pagenum == 1) {\r\n                    that.myReferrals = data;\r\n                    if (data.length == 0) {\r\n                        that.referralsNodata = true;\r\n                    }\r\n                } else {\r\n                    if (data.length == 0) {\r\n                        that.referralsNomore = true;\r\n                    } else {\r\n                        var currentReferrals = that.myReferrals;\r\n                        var newReferrals = currentReferrals.concat(data);\r\n                        that.myReferrals = newReferrals;\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        formatTime: function(timestamp) {\r\n            if (!timestamp) return '';\r\n            var date = new Date(timestamp * 1000);\r\n            var year = date.getFullYear();\r\n            var month = ('0' + (date.getMonth() + 1)).slice(-2);\r\n            var day = ('0' + date.getDate()).slice(-2);\r\n            var hours = ('0' + date.getHours()).slice(-2);\r\n            var minutes = ('0' + date.getMinutes()).slice(-2);\r\n            \r\n            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;\r\n        },\r\n        goBack() {\r\n            uni.navigateBack({\r\n                delta: 1\r\n            });\r\n        }\r\n    }\r\n}\r\n</script>\r\n<style>\r\n.container{position:relative}\r\n.nodiydata{display:flex;flex-direction:column}\r\n\r\n/* 顶部内容区域改进 */\r\n.nodiydata .topcontent{\r\n    width:94%;\r\n    margin-left:3%;\r\n    padding: 40rpx 24rpx; \r\n    border-bottom:none;\r\n    margin-bottom:30rpx; \r\n    background: linear-gradient(to right, #f7f9ff, #eef2ff);\r\n    display:flex;\r\n    flex-direction:column;\r\n    align-items:center;\r\n    border-radius:24rpx;\r\n    position:relative;\r\n    z-index:2;\r\n    box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.nodiydata .topcontent .logo{\r\n    width:140rpx;\r\n    height:140rpx;\r\n    margin-top:20rpx;\r\n    border:none;\r\n    border-radius:70rpx;\r\n    overflow:hidden;\r\n    box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.1);\r\n    transition: transform 0.3s;\r\n}\r\n\r\n.nodiydata .topcontent .logo:active {\r\n    transform: scale(0.96);\r\n}\r\n\r\n.nodiydata .topcontent .logo .level-icon{\r\n    width:100%;\r\n    height:100%;\r\n    display:flex;\r\n    align-items:center;\r\n    justify-content:center;\r\n    color:#fff;\r\n    font-size:56rpx;\r\n    font-weight:bold;\r\n    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\r\n}\r\n\r\n.nodiydata .topcontent .title {\r\n    color:#222222;\r\n    font-size:40rpx;\r\n    font-weight:700;\r\n    margin-top:24rpx;\r\n    text-align:center;\r\n    padding:0 10rpx;\r\n    letter-spacing: 1rpx;\r\n}\r\n\r\n.nodiydata .topcontent .desc {\r\n    display:flex;\r\n    align-items:center;\r\n    margin-top:20rpx;\r\n}\r\n\r\n.nodiydata .topcontent .status-tag {\r\n    padding:8rpx 24rpx;\r\n    border-radius:30rpx;\r\n    font-size:24rpx;\r\n    margin-bottom:20rpx;\r\n    font-weight:500;\r\n    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\r\n    animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from { opacity: 0; transform: translateY(10rpx); }\r\n    to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.nodiydata .topcontent .active {\r\n    background:linear-gradient(to right, #E6F7E6, #d7f5d7);\r\n    color:#07C160;\r\n}\r\n\r\n.nodiydata .topcontent .inactive {\r\n    background:linear-gradient(to right, #F5F5F5, #ebebeb);\r\n    color:#999;\r\n}\r\n\r\n/* 内容区域改进 */\r\n.nodiydata .contentbox{\r\n    width:94%;\r\n    margin-left:3%;\r\n    background: #fff;\r\n    border-radius:24rpx;\r\n    margin-bottom:32rpx;\r\n    overflow:hidden;\r\n    box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n/* 标签页改进 */\r\n.nodiydata .shop_tab{\r\n    display:flex;\r\n    width: 100%;\r\n    height:100rpx;\r\n    border-bottom:1px solid #f0f0f0;\r\n    background: #fff;\r\n}\r\n\r\n.nodiydata .shop_tab .cptab_text{\r\n    flex:1;\r\n    text-align:center;\r\n    color:#646566;\r\n    height:100rpx;\r\n    line-height:100rpx;\r\n    position:relative;\r\n    font-size: 28rpx;\r\n    transition: color 0.3s;\r\n}\r\n\r\n.nodiydata .shop_tab .cptab_current{\r\n    color: #323233;\r\n    font-weight: 600;\r\n}\r\n\r\n.nodiydata .shop_tab .after{\r\n    display:none;\r\n    position:absolute;\r\n    left:50%;\r\n    margin-left:-20rpx;\r\n    bottom:10rpx;\r\n    height:4rpx;\r\n    border-radius:2rpx;\r\n    width:40rpx;\r\n    transition: width 0.3s;\r\n}\r\n\r\n.nodiydata .shop_tab .cptab_current .after{\r\n    display:block;\r\n    animation: tabIndicator 0.3s ease-out;\r\n}\r\n\r\n@keyframes tabIndicator {\r\n    from { width: 0; margin-left: 0; }\r\n    to { width: 40rpx; margin-left: -20rpx; }\r\n}\r\n\r\n.nodiydata .cp_detail{\r\n    min-height:500rpx;\r\n    padding:40rpx 30rpx;\r\n}\r\n\r\n/* 规则详情样式改进 */\r\n.detail-section {\r\n    margin-bottom:40rpx;\r\n    animation: sectionFadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes sectionFadeIn {\r\n    from { opacity: 0; transform: translateY(20rpx); }\r\n    to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.section-title {\r\n    font-size:32rpx;\r\n    font-weight:700;\r\n    color:#333;\r\n    margin-bottom:24rpx;\r\n    position:relative;\r\n    padding-left:24rpx;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.section-title:before {\r\n    content:'';\r\n    width:6rpx;\r\n    height:32rpx;\r\n    background:linear-gradient(to bottom, #007AFF, #0063cc);\r\n    position:absolute;\r\n    left:0;\r\n    top:6rpx;\r\n    border-radius:3rpx;\r\n}\r\n\r\n.section-content {\r\n    padding:0 10rpx;\r\n}\r\n\r\n.level-tag {\r\n    display:inline-block;\r\n    background:linear-gradient(to right, #F5F6F8, #eaebee);\r\n    color:#333;\r\n    font-size:26rpx;\r\n    padding:12rpx 24rpx;\r\n    border-radius:8rpx;\r\n    margin-right:16rpx;\r\n    margin-bottom:16rpx;\r\n    box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.03);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.level-tag:active {\r\n    transform: scale(0.96);\r\n}\r\n\r\n.reward-rule-item {\r\n    display:flex;\r\n    align-items:center;\r\n    margin-bottom:24rpx;\r\n    background:linear-gradient(to right, #F9F9F9, #f2f2f2);\r\n    border-radius:16rpx;\r\n    padding:30rpx 24rpx;\r\n    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);\r\n    transition: transform 0.3s;\r\n}\r\n\r\n.reward-rule-item:active {\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.rule-icon {\r\n    width:60rpx;\r\n    height:60rpx;\r\n    border-radius:30rpx;\r\n    color:#fff;\r\n    display:flex;\r\n    align-items:center;\r\n    justify-content:center;\r\n    font-weight:bold;\r\n    font-size:28rpx;\r\n    margin-right:24rpx;\r\n    box-shadow: 0 6rpx 12rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.rule-content {\r\n    flex:1;\r\n}\r\n\r\n.rule-text {\r\n    font-size:28rpx;\r\n    color:#333;\r\n    line-height:44rpx;\r\n}\r\n\r\n.highlight {\r\n    color:#FF6B00;\r\n    font-weight:700;\r\n    padding: 0 4rpx;\r\n}\r\n\r\n.time-text {\r\n    font-size:28rpx;\r\n    color:#666;\r\n    padding: 12rpx 20rpx;\r\n    background: #f5f5f5;\r\n    border-radius: 8rpx;\r\n    display: inline-block;\r\n}\r\n\r\n/* 奖励记录样式改进 */\r\n.rewards-list {\r\n    padding:0 10rpx;\r\n}\r\n\r\n.reward-record {\r\n    background:linear-gradient(to right, #F9F9F9, #f2f2f2);\r\n    border-radius:16rpx;\r\n    padding:24rpx;\r\n    margin-bottom:30rpx;\r\n    box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.03);\r\n    transition: transform 0.3s;\r\n    animation: cardFadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes cardFadeIn {\r\n    from { opacity: 0; transform: translateY(20rpx); }\r\n    to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.reward-record:active {\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.record-header {\r\n    display:flex;\r\n    justify-content:space-between;\r\n    align-items:center;\r\n    margin-bottom:20rpx;\r\n}\r\n\r\n.user-info {\r\n    display:flex;\r\n    align-items:center;\r\n}\r\n\r\n.avatar {\r\n    width:80rpx;\r\n    height:80rpx;\r\n    border-radius:40rpx;\r\n    margin-right:20rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\r\n    border: 2rpx solid #fff;\r\n}\r\n\r\n.user-details {\r\n    display:flex;\r\n    flex-direction:column;\r\n}\r\n\r\n.nickname {\r\n    font-size:30rpx;\r\n    font-weight:600;\r\n    color:#333;\r\n}\r\n\r\n.tel {\r\n    font-size:24rpx;\r\n    color:#999;\r\n    margin-top: 4rpx;\r\n}\r\n\r\n.reward-amount {\r\n    font-size:36rpx;\r\n    font-weight:700;\r\n    padding: 8rpx 16rpx;\r\n    background: rgba(255,255,255,0.6);\r\n    border-radius: 8rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.record-content {\r\n    border-top:1px solid rgba(238,238,238,0.8);\r\n    padding-top:16rpx;\r\n}\r\n\r\n.level-info {\r\n    font-size:26rpx;\r\n    color:#666;\r\n    margin-bottom:12rpx;\r\n    background: rgba(255,255,255,0.6);\r\n    padding: 8rpx 16rpx;\r\n    border-radius: 8rpx;\r\n}\r\n\r\n.separator {\r\n    margin:0 10rpx;\r\n    color:#DDDDDD;\r\n}\r\n\r\n.record-time {\r\n    font-size:24rpx;\r\n    color:#999;\r\n}\r\n\r\n/* 统计数据样式改进 */\r\n.stats-cards {\r\n    display:flex;\r\n    justify-content:space-between;\r\n    margin-bottom:50rpx;\r\n}\r\n\r\n.stats-card {\r\n    flex:1;\r\n    background:linear-gradient(to bottom right, #F9F9F9, #f2f2f2);\r\n    border-radius:16rpx;\r\n    padding:30rpx 20rpx;\r\n    margin:0 10rpx;\r\n    text-align:center;\r\n    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.05);\r\n    transition: transform 0.3s;\r\n    animation: statCardFadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes statCardFadeIn {\r\n    from { opacity: 0; transform: translateY(20rpx); }\r\n    to { opacity: 1; transform: translateY(0); }\r\n    0% { opacity: 0; transform: translateY(20rpx); }\r\n    100% { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.stats-card:nth-child(1) { animation-delay: 0.1s; }\r\n.stats-card:nth-child(2) { animation-delay: 0.2s; }\r\n.stats-card:nth-child(3) { animation-delay: 0.3s; }\r\n\r\n.stats-card:active {\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.stats-value {\r\n    font-size:40rpx;\r\n    font-weight:700;\r\n    color:#333;\r\n    margin-bottom:12rpx;\r\n    text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-label {\r\n    font-size:26rpx;\r\n    color:#999;\r\n    position: relative;\r\n}\r\n\r\n.stats-label:after {\r\n    content: '';\r\n    display: block;\r\n    width: 40rpx;\r\n    height: 4rpx;\r\n    background: #eee;\r\n    margin: 12rpx auto 0;\r\n    border-radius: 2rpx;\r\n}\r\n\r\n.my-referrer, .my-referrals {\r\n    margin-bottom:50rpx;\r\n    animation: sectionFadeIn 0.5s ease-in-out;\r\n    animation-delay: 0.3s;\r\n}\r\n\r\n.referrer-card {\r\n    background:linear-gradient(to right, #F9F9F9, #f2f2f2);\r\n    border-radius:16rpx;\r\n    padding:30rpx 24rpx;\r\n    display:flex;\r\n    align-items:center;\r\n    box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.03);\r\n    transition: transform 0.3s;\r\n}\r\n\r\n.referrer-card:active {\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.referrer-avatar {\r\n    width:90rpx;\r\n    height:90rpx;\r\n    border-radius:45rpx;\r\n    margin-right:24rpx;\r\n    border: 2rpx solid #fff;\r\n    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.referrer-info {\r\n    flex:1;\r\n}\r\n\r\n.referrer-name {\r\n    font-size:30rpx;\r\n    font-weight:600;\r\n    color:#333;\r\n}\r\n\r\n.referrer-tel, .referrer-level {\r\n    font-size:24rpx;\r\n    color:#999;\r\n    margin-top:8rpx;\r\n}\r\n\r\n.referrals-list {\r\n    margin-top:24rpx;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);\r\n}\r\n\r\n.referral-item {\r\n    display:flex;\r\n    align-items:center;\r\n    padding:24rpx 20rpx;\r\n    border-bottom:1px solid rgba(238,238,238,0.6);\r\n    transition: background-color 0.3s;\r\n}\r\n\r\n.referral-item:active {\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.referral-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.referral-avatar {\r\n    width:70rpx;\r\n    height:70rpx;\r\n    border-radius:35rpx;\r\n    margin-right:20rpx;\r\n    border: 2rpx solid #fff;\r\n    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);\r\n}\r\n\r\n.referral-info {\r\n    flex:1;\r\n}\r\n\r\n.referral-name {\r\n    font-size:28rpx;\r\n    font-weight: 500;\r\n    color:#333;\r\n}\r\n\r\n.referral-level {\r\n    font-size:24rpx;\r\n    color:#999;\r\n    margin-top:6rpx;\r\n    background: #f5f5f5;\r\n    display: inline-block;\r\n    padding: 4rpx 12rpx;\r\n    border-radius: 6rpx;\r\n    margin-top: 8rpx;\r\n}\r\n\r\n.referral-time {\r\n    font-size:24rpx;\r\n    color:#999;\r\n    background: rgba(245,245,245,0.6);\r\n    padding: 6rpx 12rpx;\r\n    border-radius: 6rpx;\r\n}\r\n\r\n.nodiydata .covermy{\r\n    position:fixed;\r\n    z-index:99999;\r\n    cursor:pointer;\r\n    display:flex;\r\n    flex-direction:column;\r\n    align-items:center;\r\n    justify-content:center;\r\n    overflow:hidden;\r\n    z-index:9999;\r\n    top:81vh;\r\n    left:82vw;\r\n    color:#fff;\r\n    background-color:rgba(0,122,255,0.85);\r\n    width:120rpx;\r\n    height:120rpx;\r\n    font-size:26rpx;\r\n    border-radius:60rpx;\r\n    box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);\r\n    transition: transform 0.3s, background-color 0.3s;\r\n}\r\n\r\n.nodiydata .covermy:active {\r\n    transform: scale(0.95);\r\n    background-color: rgba(0,100,220,0.9);\r\n}\r\n\r\n/* 奖励模式样式 */\r\n.reward-mode-tag {\r\n    background: linear-gradient(to right, #F9F9F9, #f2f2f2);\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);\r\n    transition: transform 0.3s;\r\n    animation: fadeIn 0.5s ease-in-out;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.reward-mode-tag:active {\r\n    transform: translateY(2rpx);\r\n}\r\n\r\n.reward-mode-tag.regular {\r\n    border-left: 6rpx solid #36BFFA;\r\n}\r\n\r\n.reward-mode-tag.regular:before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n    background: rgba(54, 191, 250, 0.05);\r\n    border-radius: 50%;\r\n    transform: translate(30%, -30%);\r\n}\r\n\r\n.reward-mode-tag.cumulative {\r\n    border-left: 6rpx solid #FF6B00;\r\n}\r\n\r\n.reward-mode-tag.cumulative:before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n    background: rgba(255, 107, 0, 0.05);\r\n    border-radius: 50%;\r\n    transform: translate(30%, -30%);\r\n}\r\n\r\n.reward-mode-icon {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border-radius: 30rpx;\r\n    margin-bottom: 16rpx;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n}\r\n\r\n.reward-mode-tag.regular .reward-mode-icon {\r\n    background: linear-gradient(to bottom right, #36BFFA, #0093D9);\r\n    box-shadow: 0 6rpx 12rpx rgba(54, 191, 250, 0.2);\r\n}\r\n\r\n.reward-mode-tag.cumulative .reward-mode-icon {\r\n    background: linear-gradient(to bottom right, #FF6B00, #E85600);\r\n    box-shadow: 0 6rpx 12rpx rgba(255, 107, 0, 0.2);\r\n}\r\n\r\n.reward-mode-text {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 12rpx;\r\n}\r\n\r\n.reward-mode-desc {\r\n    font-size: 26rpx;\r\n    color: #999;\r\n    line-height: 36rpx;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093156\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}