{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?978f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?c5c7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?410d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?239b", "uni-app:///pagesExt/order/refund.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?ec41", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/refund.vue?8138"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "orderid", "totalprice", "order", "detail", "refundNum", "prolist", "content_pic", "cindex", "cateArr", "type", "money", "reason", "tmplids", "isloading", "totalcanrefundnum", "onLoad", "uni", "title", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "retrun", "temp", "ogid", "num", "console", "picker<PERSON><PERSON><PERSON>", "formSubmit", "refundtotal", "receive", "setTimeout", "gwcplus", "gwcminus", "gwcinput", "calculate", "total", "refundTotalNum", "moneyInput", "reasonInput", "uploadimg", "pics", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsE/wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;YACAA;YAAAE;UACA;QACA;QACAH;QACAA;QACAA;QACAA;QACA;QACAA;QACAA;QACA;QACA;UACAI;YAAAC;YAAAC;UAAA;UACAb;QACA;QACAO;QACAO;QACAP;QACAA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAT;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MACAD;MACAC;MACAA;QAAAtB;QAAAW;QAAAD;QAAAJ;QAAA0B;QAAA5B;QAAAK;MAAA;QACAa;QACAA;QACA;UACAD;YACAY;cACAX;YACA;UACA;QACA;UACAD;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA9B;MACA;MACA;IACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MACA;MACA;MACA/B;MACA;MACA;IACA;IACA;IACAgC;MACA;MACA;MACA;MACA;MACA;MACAR;MACA;MAEA;QACAN;QACA;MACA;MACA;MACAlB;MACA;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;QACA,kDACAC,0DACA;UACAA;QACA;QACAC;MACA;MACA;QACAD;MACA;MACAV;MACAU;MACA;MACAA;MACAjB;MACAA;IACA;IACAmB;MACA;MACA;QACAlB;QACA;MACA;MACA;IACA;IACAmB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACApB;QACA;UACAqB;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAD;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjSA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/refund.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/refund.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./refund.vue?vue&type=template&id=7452504d&\"\nvar renderjs\nimport script from \"./refund.vue?vue&type=script&lang=js&\"\nexport * from \"./refund.vue?vue&type=script&lang=js&\"\nimport style0 from \"./refund.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/refund.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=template&id=7452504d&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.content_pic.length : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t<view class=\"form-content\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">退款商品</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in prolist\" :key=\"index\" class=\"content\">\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t\t<!-- <text class=\"x2\">×{{item.num}}</text> -->\r\n\t\t\t\t\t\t\t<view class=\"num-wrap\">\r\n\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\" :data-index=\"index\" :data-ogid=\"item.id\" :data-num=\"refundNum[index].num\"><image class=\"img\" src=\"/static/img/cart-minus.png\"/></view>\r\n\t\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"refundNum[index].num\" @blur=\"gwcinput\" :data-index=\"index\" :data-ogid=\"item.id\" :data-max=\"item.num-item.refund_num\" :data-num=\"refundNum[index].num\"></input>\r\n\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\" :data-index=\"index\" :data-ogid=\"item.id\" :data-max=\"item.num-item.refund_num\" :data-num=\"refundNum[index].num\"><image class=\"img\" src=\"/static/img/cart-plus.png\"/></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-desc\">申请数量：最多可申请{{item.canRefundNum}}件</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-content\">\r\n\t\t\t\t<view class=\"form-item\" v-if=\"opt.type == 'refund'\" style=\"display:none\">\r\n\t\t\t\t\t<text class=\"label\">货物状态</text>\r\n\t\t\t\t\t<view class=\"input-item\">\r\n\t\t\t\t\t\t<picker @change=\"pickerChange\" :value=\"cindex\" :range=\"cateArr\" name=\"receive\" style=\"height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE\">\r\n\t\t\t\t\t\t\t<view class=\"picker\">{{cindex==-1? '请选择' : cateArr[cindex]}}</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">退款原因</text>\r\n\t\t\t\t\t<view class=\"input-item\"><textarea placeholder=\"请输入退款原因\" placeholder-style=\"color:#999;\" name=\"reason\" @input=\"reasonInput\"></textarea></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"label\">退款金额(元)</text>\r\n\t\t\t\t\t<view class=\"flex\"><input name=\"money\" @input=\"moneyInput\" type=\"digit\" :value=\"money\" placeholder=\"请输入退款金额\" placeholder-style=\"color:#999;\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<view class=\"label\">上传图片(最多三张)</view>\r\n\t\t\t\t\t<view id=\"content_picpreview\" class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in content_pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"content_pic\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"content_pic\" v-if=\"content_pic.length<3\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"btn\" @tap=\"formSubmit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t<view style=\"padding-top:30rpx\"></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tpre_url:'',\r\n      orderid: '',\r\n      totalprice: 0,\r\n\t\t\torder:{},\r\n\t\t\tdetail: {},\r\n\t\t\trefundNum:[],\r\n\t\t\tprolist: [],\r\n\t\t\tcontent_pic:[],\r\n\t\t\tcindex:-1,\r\n\t\t\tcateArr:['未收到货','已收到货'],\r\n\t\t\ttype:'',\r\n\t\t\tmoney:'',\r\n\t\t\treason:'',\r\n\t\t\ttmplids:[],\r\n\t\t\tisloading:0,\r\n\t\t\ttotalcanrefundnum:0,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.orderid = this.opt.orderid;\r\n\t\tthis.pre_url = app.globalData.pre_url;\r\n\t\tthis.type = this.opt.type;\r\n\t\tif(this.type == 'return') {\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t  title: '申请退货退款'\r\n\t\t\t});\r\n\t\t}\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiOrder/refundinit', {id: that.orderid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tapp.goback();retrun;\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthat.tmplids = res.tmplids;\r\n\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\tthat.totalprice = that.detail.returnTotalprice;\r\n\t\t\t\tthat.money = (that.totalprice).toFixed(2);\r\n\t\t\t\tvar temp = [];\r\n\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\tthat.order = res.order;\r\n\t\t\t\tvar totalcanrefundnum = 0;\r\n\t\t\t\tfor(var i in that.prolist) {\r\n\t\t\t\t\ttemp.push({ogid:that.prolist[i].id,num:that.prolist[i].canRefundNum})\r\n\t\t\t\t\ttotalcanrefundnum += that.prolist[i].canRefundNum;\r\n\t\t\t\t}\r\n\t\t\t\tthat.totalcanrefundnum = totalcanrefundnum;\r\n\t\t\t\tconsole.log(temp)\r\n\t\t\t\tthat.refundNum = temp;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tpickerChange: function (e) {\r\n\t\t  this.cindex = e.detail.value;\r\n\t\t},\r\n    formSubmit: function () {\r\n      var that = this;\r\n\t\t\tif(that.isloading) return;\r\n      var orderid = that.orderid;\r\n      var reason = that.reason;\r\n\t\t\tvar receive = that.cindex;\r\n      var money = parseFloat(that.money);\r\n\t\t\tvar refundNum = that.refundNum;\r\n\t\t\tvar content_pic = that.content_pic;\r\n\t\t\tvar refundtotal = 0;\r\n\t\t\tfor(var i in refundNum) {\r\n\t\t\t\trefundtotal += refundNum[i].num;\r\n\t\t\t}\r\n\t\t\tif(refundtotal <= 0) {\r\n\t\t\t\tapp.alert('请选择要退款的商品');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t//if (receive == -1 && that.opt.type == 'refund') {\r\n        //app.alert('请选择货物状态');\r\n        //return;\r\n      //}\r\n      if (reason == '') {\r\n        app.alert('请填写退款原因');\r\n        return;\r\n      }\r\n\t\t\t\r\n      if (money < 0 || money > parseFloat(that.totalprice)) {\r\n        app.alert('退款金额有误');\r\n        return;\r\n      }\r\n\t\t\tthat.isloading = 1;\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post('ApiOrder/refund', {orderid: orderid,reason: reason,money: money,content_pic:content_pic,receive:receive,refundNum:refundNum,type:that.type}, function (res) {\r\n\t\t\t\tapp.showLoading(false);\r\n        app.alert(res.msg);\r\n        if (res.status == 1) {\r\n          that.subscribeMessage(function () {\r\n            setTimeout(function () {\r\n              app.goto('detail?id='+that.orderid);\r\n            }, 1000);\r\n          });\r\n        }else{\r\n\t\t\t\t\tthat.isloading = 0;\r\n        }\r\n      });\r\n    },\r\n\t\t//加\r\n\t\tgwcplus: function (e) {\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  var maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t  var ogid = e.currentTarget.dataset.ogid;\r\n\t\t  var num = parseInt(e.currentTarget.dataset.num);\r\n\t\t  if (num >= maxnum) {\r\n\t\t    return;\r\n\t\t  }\r\n\t\t\tvar refundNum = this.refundNum;\r\n\t\t\trefundNum[index].num++;\r\n\t\t\tthis.refundNum = refundNum\r\n\t\t\tthis.calculate();\r\n\t\t},\r\n\t\t//减\r\n\t\tgwcminus: function (e) {\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  var maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t  var ogid = e.currentTarget.dataset.ogid;\r\n\t\t  var num = parseInt(e.currentTarget.dataset.num);\r\n\t\t  if (num == 0) return;\r\n\t\t\tvar refundNum = this.refundNum;\r\n\t\t\trefundNum[index].num--;\r\n\t\t\tthis.refundNum = refundNum\r\n\t\t\tthis.calculate();\r\n\t\t},\r\n\t\t//输入\r\n\t\tgwcinput: function (e) {\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  var maxnum = parseInt(e.currentTarget.dataset.max);\r\n\t\t  var ogid = e.currentTarget.dataset.ogid;\r\n\t\t  var num = parseInt(e.currentTarget.dataset.num);\r\n\t\t  var newnum = parseInt(e.detail.value);\r\n\t\t  console.log(num + '--' + newnum);\r\n\t\t  if (num == newnum) return;\r\n\t\t\r\n\t\t  if (newnum > maxnum) {\r\n\t\t    app.error('请输入正确数量');\r\n\t\t    return;\r\n\t\t  }\r\n\t\t\tvar refundNum = this.refundNum;\r\n\t\t\trefundNum[index].num = newnum;\r\n\t\t\tthis.refundNum = refundNum\r\n\t\t\tthis.calculate();\r\n\t\t},\r\n    calculate: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar total = 0;\r\n\t\t\tvar refundTotalNum = 0;\r\n\t\t\tfor(var i in that.refundNum) {\r\n\t\t\t\tif(that.refundNum[i].num == that.prolist[i].num)\r\n\t\t\t\t\ttotal += parseFloat(that.prolist[i].real_totalprice);\r\n\t\t\t\telse {\r\n\t\t\t\t\ttotal += that.refundNum[i].num * parseFloat(that.prolist[i].real_totalprice) / that.prolist[i].num;\r\n\t\t\t\t}\r\n\t\t\t\trefundTotalNum += that.refundNum[i].num;\r\n\t\t\t}\r\n\t\t\tif(refundTotalNum == that.detail.totalNum || refundTotalNum == that.detail.canRefundNum) {\r\n\t\t\t\ttotal = that.detail.returnTotalprice;\r\n\t\t\t}\r\n\t\t\tconsole.log(total)\r\n\t\t\ttotal = parseFloat(total);\r\n\t\t\tif(total > that.detail.returnTotalprice) total = that.detail.returnTotalprice;\t\t\r\n\t\t\ttotal = total.toFixed(2);\r\n\t\t\tthat.totalprice = total;\t\r\n\t\t\tthat.money = total;\t\r\n\t\t},\r\n\t\tmoneyInput: function (e) {\r\n\t\t\tvar newmoney = parseInt(e.detail.value);\r\n\t\t\tif (newmoney <= 0 || newmoney > parseFloat(this.totalprice)) {\r\n\t\t\t  app.error('最大退款金额:'+this.totalprice);\r\n\t\t\t  return;\r\n\t\t\t}\r\n\t\t\tthis.money = newmoney;\r\n\t\t},\r\n\t\treasonInput: function (e) {\r\n\t\t\tthis.reason = e.detail.value;\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tpics.splice(index,1)\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\t.num-wrap {position: absolute;right: 0;bottom:24rpx;}\r\n\t.num-wrap .text-desc { margin-bottom: -60rpx; color: #999; font-size: 24rpx; text-align: right;}\r\n\t.addnum {position: absolute;right: 0;bottom:0rpx;font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n\t.addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n\t.product .addnum .img{width:24rpx;height:24rpx}\r\n\t.addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}\r\n\t.addnum .input{flex:1;width:70rpx;border:0;text-align:center;color:#2B2B2B;font-size:24rpx}\r\n\t\r\n\t.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}\r\n\t.form-item4 .label{ width:150rpx;}\r\n\t.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n\t.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n\t.layui-imgbox-close image{width:100%;height:100%}\r\n\t.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n\t.layui-imgbox-img>image{max-width:100%;}\r\n\t.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n\t.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\t\r\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed; height: 196rpx;}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content image{ width: 140rpx; height: 140rpx;}\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;height:72rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246; position: relative;}\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.form-content{width:94%;margin:16rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff;overflow:hidden}\r\n.form-item{ width:100%;padding: 32rpx 20rpx;}\r\n.form-item .label{ width:100%;height:60rpx;line-height:60rpx}\r\n.form-item .input-item{ width:100%;}\r\n.form-item textarea{ width:100%;height:200rpx;border: 1px #eee solid;padding: 20rpx;}\r\n.form-item input{ width:100%;border: 1px #f5f5f5 solid;padding: 10rpx;height:80rpx}\r\n.form-item .mid{ height:80rpx;line-height:80rpx;padding:0 20rpx;}\r\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098741\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}