{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?5328", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?7952", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?5536", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?f86f", "uni-app:///yuyue/peolist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?1274", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peolist.vue?ca98"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "keyword", "datalist", "type", "nodata", "curTopIndex", "index", "curCid", "nomore", "pagenum", "longitude", "latitude", "field", "order", "locationFailed", "currentCity", "currentCityId", "provinceId", "cityId", "districtId", "showFilterOptions", "onLoad", "uni", "console", "onUnload", "onPullDownRefresh", "onReachBottom", "methods", "checkTapOutside", "setTimeout", "toggleFilterOptions", "e", "sortClick", "that", "resetFilter", "confirmFilter", "handleCitySelect", "gotoCity", "app", "with_children", "areas", "area_config", "hotareas", "hotareas_str", "hotarea", "switcharearange", "switcharearangeareas_str", "url", "title", "icon", "getdata", "cid", "bid", "isget", "getdatalist", "province_id", "city_id", "district_id", "params", "duration", "switchTopTab", "searchChange", "searchConfirm", "goto", "loaded", "getmenuindex", "retryLocation"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0HhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;;IAEA;IACAC;IAEAC;EACA;EACAC;IACA;IACAF;EACA;EACAG;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACAC;QACA;UACAN;UACA;QACA;MACA;IACA;IAEA;IACAO;MACAP;MACA;QACAQ;MACA;;MACA;IACA;IAEA;IACAC;MACAD;MACA;MACA;MACA;MACAE;MACAA;MACA;IACA;;IAEA;IACAC;MACAH;MACA;MACA;MACA;IACA;;IAEA;IACAI;MACAJ;MACA;MACA;IACA;IACA;IACAK;MACAb;MACA;QACA;QACA;;QAEA;QACA;UACA;YAAA;YACA;YACA;YACA;UACA;YAAA;YACA;YACA;YACA;UACA;YAAA;YACA;YACA;;YAEA;YACA;cACA;YACA;cACA;cACA;YACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;YACA;YACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;QAEAA;UACAN;UACAC;UACAC;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAkB;MACA;MACAC;QACA;QACAC;MACA;QACA;UACA;UACA;YACAC;YACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;UAEAxB;YACAyB;UACA;QACA;UACAzB;YACA0B;YACAC;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACAjB;;MAEA;MACA;QACAX;MACA;MAEAgB;QAAAa;QAAAC;MAAA;QACAnB;QACA;QACAA;QACA;QACA;UACA;YACA;cACAA;cACAA;cACA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAoB;gBACA;cACA;YACA;YACA;UACA;QACA;QACApB;QAEAK;UACAf;UACA;UACA;UACAU;UACAA;UACAA;QACA,GACA;UACAV;UACAU;UACAA;QACA;MACA;IACA;IACAqB;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACArB;MACAA;MACAA;MAEA;MACA;MAEAV;QACAN;QACAC;QACAC;MACA;MAEA;QACAV;QACAR;QACAW;QACAC;QACAsC;QACAC;QACAjD;QACA;QACAoD;QACAC;QACAC;MACA;;MAEA;MACA;QACAC;QACAA;QACAnC;MACA;MAEAe;QACAL;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;QAEA;UACAV;QACA;;QAEA;QACA;UACA;YACA;cACA3B;YACA;UACA;UACA;YACA;YACA0B;cACA0B;cACAC;cACAU;YACA;YACA1B;UACA;QACA;;QAEA;QACAX;MACA;IACA;IACAsC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA7B;MACAA;IACA;IACA8B;MACA;MACAzC;QACAyB;MACA;IACA;IACAiB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAjC;;MAEA;MACAK;QACAf;QACA;QACA;QACAU;QACAA;QACAA;;QAEA;QACAX;UACA0B;UACAC;UACAU;QACA;MACA,GACA;QACApC;QACAU;QACAA;;QAEA;QACAX;UACA0B;UACAC;UACAU;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxhBA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/peolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/peolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peolist.vue?vue&type=template&id=2784c990&\"\nvar renderjs\nimport script from \"./peolist.vue?vue&type=script&lang=js&\"\nexport * from \"./peolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/peolist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=template&id=2784c990&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.showFilterOptions && _vm.field == \"juli\"\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && _vm.showFilterOptions && _vm.field == \"juli\"\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload && _vm.showFilterOptions && _vm.field == \"comment_score\"\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showFilterOptions && _vm.field == \"comment_score\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 = _vm.isload && _vm.showFilterOptions ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m6 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m6: m6,\n        }\n      })\n    : null\n  var m7 = _vm.isload && _vm.locationFailed ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.locationFailed ? _vm.t(\"color1\") : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.jineng && item.jineng.includes(\"、\")\n        var l1 = g0 ? item.jineng.split(\"、\").slice(0, 3) : null\n        var m9 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          g0: g0,\n          l1: l1,\n          m9: m9,\n        }\n      })\n    : null\n  var g1 = _vm.isload\n    ? _vm.datalist.length === 0 && !_vm.loading && _vm.nodata\n    : null\n  var g2 = _vm.isload\n    ? _vm.nodata && _vm.datalist.length === 0 && !_vm.loading\n    : null\n  var g3 = _vm.loading && _vm.datalist.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n        m7: m7,\n        m8: m8,\n        l2: l2,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" @click=\"checkTapOutside\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<!-- 城市选择按钮移到左边 -->\r\n\t\t\t<view class=\"city-select\" @tap=\"gotoCity\" :class=\"{'active': currentCity}\">\r\n\t\t\t\t<text>{{currentCity || '选择城市'}}</text>\r\n\t\t\t\t<text class=\"city-icon\">▼</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t</view>\r\n\t\t\t<!-- 添加筛选按钮 -->\r\n\t\t\t<view class=\"filter-btn\" @tap.stop=\"toggleFilterOptions\">\r\n\t\t\t\t<text class=\"filter-text\">筛选</text>\r\n\t\t\t\t<text class=\"filter-icon\">▼</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 筛选选项浮层 -->\r\n\t\t<view class=\"filter-options\" v-if=\"showFilterOptions\" @click.stop>\r\n\t\t\t<view class=\"filter-overlay\" @tap=\"toggleFilterOptions\"></view>\r\n\t\t\t<view class=\"filter-panel\" @tap.stop>\r\n\t\t\t\t<view class=\"filter-title\">排序方式</view>\r\n\t\t\t\t<view class=\"filter-list\">\r\n\t\t\t\t\t<view :class=\"'filter-item ' + (field == 'juli' ? 'active' : '')\" \r\n\t\t\t\t\t\t@tap=\"sortClick\" \r\n\t\t\t\t\t\tdata-field=\"juli\" \r\n\t\t\t\t\t\tdata-order=\"asc\"\r\n\t\t\t\t\t\t:style=\"field=='juli' ? 'color:'+t('color1')+';border-color:'+t('color1') : ''\">\r\n\t\t\t\t\t\t<text>按距离排序</text>\r\n\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"field == 'juli'\">✓</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view :class=\"'filter-item ' + (field == 'comment_score' ? 'active' : '')\" \r\n\t\t\t\t\t\t@tap=\"sortClick\" \r\n\t\t\t\t\t\tdata-field=\"comment_score\" \r\n\t\t\t\t\t\tdata-order=\"desc\"\r\n\t\t\t\t\t\t:style=\"field=='comment_score' ? 'color:'+t('color1')+';border-color:'+t('color1') : ''\">\r\n\t\t\t\t\t\t<text>按评分排序</text>\r\n\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"field == 'comment_score'\">✓</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"filter-btn-group\">\r\n\t\t\t\t\t<view class=\"filter-reset\" @tap=\"resetFilter\">重置</view>\r\n\t\t\t\t\t<view class=\"filter-confirm\" @tap=\"confirmFilter\" :style=\"{background:t('color1')}\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"order-tab\">\r\n\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t<view :class=\"'item ' + (curTopIndex == -1 ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"-1\" :data-id=\"0\">全部<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\">{{item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 位置获取失败时显示重试按钮 -->\r\n\t\t<view class=\"location-failed flex-center\" v-if=\"locationFailed\">\r\n\t\t\t<view class=\"retry-btn\" @tap=\"retryLocation\" :style=\"{borderColor:t('color1'), color:t('color1')}\">\r\n\t\t\t\t重新获取位置\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t<view class=\"content-list\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content flex\" :data-id=\"item.id\">\r\n\t\t\t\t<view class=\"f1\" @click=\"goto\" :data-url=\"'peodetail?id='+item.id\" >\r\n\t\t\t\t\t<view class=\"headimg\"><image :src=\"item.headimg\" mode=\"aspectFill\" /></view>\r\n\t\t\t\t\t<view class=\"text1\">\t\r\n\t\t\t\t\t\t<view class=\"name-type\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.realname}} </text>\r\n\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.typename\" >{{item.typename}} </text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text2\">{{item.jineng || '暂无技能描述'}}</view>\r\n\t\t\t\t\t\t<view v-if=\"item.jineng && item.jineng.includes('、')\" class=\"tech-tags\">\r\n\t\t\t\t\t\t\t<text class=\"tech-tag\" v-for=\"(tag, tagIndex) in item.jineng.split('、').slice(0,3)\" :key=\"tagIndex\">{{tag}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text3\">\r\n\t\t\t\t\t\t\t<text class=\"t4\">服务<text> {{item.totalnum}}</text> 次</text> \r\n\t\t\t\t\t\t\t<text class=\"t5\">评分 <text>{{item.comment_score}}</text></text>\r\n\t\t\t\t\t\t\t<text class=\"t6\" v-if=\"item.distance && item.distance != '未知'\">距离 <text>{{item.distance}}</text></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"yuyue\"  @click=\"goto\" :data-url=\"'/yuyue/peodetail?id='+item.id\" :style=\"{background:t('color1')}\">预约</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"no-data-placeholder\" v-if=\"datalist.length === 0 && !loading && nodata\">\r\n\t\t\t\t<image src=\"/static/img/empty.png\" mode=\"aspectFit\" class=\"empty-img\"></image>\r\n\t\t\t\t<view class=\"empty-text\">暂无相关技师</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata && datalist.length === 0 && !loading\"></nodata>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<!-- 添加骨架屏加载效果 -->\r\n\t<view class=\"content-list skeleton-list\" v-if=\"loading && datalist.length === 0\">\r\n\t\t<view class=\"content skeleton-card\" v-for=\"i in 3\" :key=\"i\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"skeleton headimg-skeleton\"></view>\r\n\t\t\t\t<view class=\"text1\">\r\n\t\t\t\t\t<view class=\"skeleton title-skeleton\"></view>\r\n\t\t\t\t\t<view class=\"skeleton text-skeleton\"></view>\r\n\t\t\t\t\t<view class=\"skeleton text-skeleton short\"></view>\r\n\t\t\t\t\t<view class=\"skeleton-tags\">\r\n\t\t\t\t\t\t<view class=\"skeleton tag-skeleton\" v-for=\"j in 2\" :key=\"j\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"skeleton btn-skeleton\"></view>\r\n\t\t</view>\r\n\t</view>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t  opt:{},\r\n\t  loading:false,\r\n      isload: false,\r\n\t  menuindex:-1,\r\n      keyword: '',\r\n      datalist: [],\r\n      type: \"\",\r\n\t  nodata:false,\r\n\t  curTopIndex: -1,\r\n\t  index:0,\r\n\t  curCid:0,\r\n\t  nomore: false,\r\n\t  pagenum: 1,\r\n\t  longitude: '',\r\n      latitude: '',\r\n\t  field: '',\r\n      order: '',\r\n\t  locationFailed: false,\r\n\t  // 添加城市相关变量\r\n\t  currentCity: '',\r\n\t  currentCityId: 0,\r\n\t  provinceId: 0,\r\n\t  cityId: 0,\r\n\t  districtId: 0,\r\n\t  // 添加筛选面板控制变量\r\n\t  showFilterOptions: false\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.type = this.opt.type || '';\r\n\t\t\r\n\t\t// 重置地区筛选参数\r\n\t\tthis.provinceId = 0;\r\n\t\tthis.cityId = 0;\r\n\t\tthis.districtId = 0;\r\n\t\tthis.currentCity = '';\r\n\t\tthis.currentCityId = 0;\r\n\t\t\r\n\t\tthis.getdata();\r\n\t\t\r\n\t\t// 监听城市选择结果\r\n\t\tuni.$on('city', this.handleCitySelect);\r\n\t\t\r\n\t\tconsole.log('2023-06-15 11:05:23-INFO-[peolist][onLoad_001] 页面初始化完成');\r\n  },\r\n  onUnload: function() {\r\n\t// 取消监听，防止内存泄漏\r\n\tuni.$off('city', this.handleCitySelect);\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t  if (!this.nodata && !this.nomore) {\r\n\t    this.pagenum = this.pagenum + 1;\r\n\t    this.getdatalist(true);\r\n\t  }\r\n\t},\r\n  methods: {\r\n\t\t// 检查点击是否在筛选面板外部\r\n\t\tcheckTapOutside: function(e) {\r\n\t\t\t// 使用setTimeout确保其他点击事件先执行\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tif (this.showFilterOptions) {\r\n\t\t\t\t\tconsole.log('2023-06-15 15:55:23-INFO-[peolist][checkTapOutside_001] 检测到外部点击，关闭筛选面板');\r\n\t\t\t\t\tthis.showFilterOptions = false;\r\n\t\t\t\t}\r\n\t\t\t}, 10);\r\n\t\t},\r\n\t\t\r\n\t\t// 切换筛选选项面板\r\n\t\ttoggleFilterOptions: function(e) {\r\n\t\t\tconsole.log('2023-06-15 15:55:23-INFO-[peolist][toggleFilterOptions_001] 切换筛选面板状态:', !this.showFilterOptions);\r\n\t\t\tif (e) {\r\n\t\t\t\te.stopPropagation(); // 阻止事件冒泡\r\n\t\t\t}\r\n\t\t\tthis.showFilterOptions = !this.showFilterOptions;\r\n\t\t},\r\n\t\t\r\n\t\t// 排序选择点击\r\n\t\tsortClick: function (e) {\r\n\t\t\te.stopPropagation(); // 阻止事件冒泡\r\n\t\t\tvar that = this;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar order = e.currentTarget.dataset.order;\r\n\t\t\tthat.field = field;\r\n\t\t\tthat.order = order;\r\n\t\t\t// 选择后不立即关闭筛选面板，等用户点击确定按钮\r\n\t\t},\r\n\t\t\r\n\t\t// 重置筛选\r\n\t\tresetFilter: function(e) {\r\n\t\t\te.stopPropagation(); // 阻止事件冒泡\r\n\t\t\tthis.field = '';\r\n\t\t\tthis.order = '';\r\n\t\t\t// 重置后不立即获取数据，等用户点击确定按钮\r\n\t\t},\r\n\t\t\r\n\t\t// 确认筛选并关闭面板\r\n\t\tconfirmFilter: function(e) {\r\n\t\t\te.stopPropagation(); // 阻止事件冒泡\r\n\t\t\tthis.showFilterOptions = false;\r\n\t\t\tthis.getdatalist();\r\n\t\t},\r\n\t\t// 处理城市选择结果\r\n\t\thandleCitySelect: function(city) {\r\n\t\t\tconsole.log('2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_001] 选择的城市：', city);\r\n\t\t\tif(city && city.id) {\r\n\t\t\t\tthis.currentCityId = city.id;\r\n\t\t\t\tthis.currentCity = city.name;\r\n\t\t\t\t\r\n\t\t\t\t// 更新逻辑，根据city对象的level属性更准确地设置地区ID\r\n\t\t\t\tif(city.level !== undefined) {\r\n\t\t\t\t\tif(city.level === 0) { // 省级\r\n\t\t\t\t\t\tthis.provinceId = city.id;\r\n\t\t\t\t\t\tthis.cityId = 0;\r\n\t\t\t\t\t\tthis.districtId = 0;\r\n\t\t\t\t\t} else if(city.level === 1) { // 市级\r\n\t\t\t\t\t\tthis.cityId = city.id;\r\n\t\t\t\t\t\tthis.provinceId = city.parent_id || 0;\r\n\t\t\t\t\t\tthis.districtId = 0;\r\n\t\t\t\t\t} else if(city.level === 2) { // 区级\r\n\t\t\t\t\t\tthis.districtId = city.id;\r\n\t\t\t\t\t\tthis.cityId = city.parent_id || 0;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 尝试获取省份ID，如果无法获取则设置为0\r\n\t\t\t\t\t\tif(city.province_id) {\r\n\t\t\t\t\t\t\tthis.provinceId = city.province_id;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 可能需要额外API调用获取省份ID，但此处简化处理\r\n\t\t\t\t\t\t\tthis.provinceId = 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if(city.parent_id) {\r\n\t\t\t\t\t// 如果有parent_id属性，可以判断其层级\r\n\t\t\t\t\tthis.cityId = city.id;\r\n\t\t\t\t\tthis.provinceId = city.parent_id;\r\n\t\t\t\t\tthis.districtId = 0;\r\n\t\t\t\t} else if(city.first_letter) {\r\n\t\t\t\t\t// 如果有first_letter属性，说明是从字母索引列表选择的城市\r\n\t\t\t\t\t// 判断是否是省级城市（简单判断：城市名称包含\"省\"或\"自治区\"）\r\n\t\t\t\t\tlet name = city.name || '';\r\n\t\t\t\t\tif(name.indexOf('省') > -1 || name.indexOf('自治区') > -1 || name.indexOf('市') === name.length-1 && name.length > 2) {\r\n\t\t\t\t\t\tthis.provinceId = city.id;\r\n\t\t\t\t\t\tthis.cityId = 0;\r\n\t\t\t\t\t\tthis.districtId = 0;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 否则可能是城市，这里设置为城市级别\r\n\t\t\t\t\t\tthis.cityId = city.id;\r\n\t\t\t\t\t\tthis.provinceId = 0; // 无法知道其所属省份ID\r\n\t\t\t\t\t\tthis.districtId = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_002] 设置的地区ID：', {\r\n\t\t\t\t\tprovinceId: this.provinceId,\r\n\t\t\t\t\tcityId: this.cityId,\r\n\t\t\t\t\tdistrictId: this.districtId\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 重新获取数据\r\n\t\t\t\tthis.getdatalist();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到城市选择页面\r\n\t\tgotoCity: function() {\r\n\t\t\t// 获取城市列表数据，使用系统配置的分区模式\r\n\t\t\tapp.get('ApiArea/getActiveAreas', {\r\n\t\t\t\t// 不传入mode参数，让后端从系统配置中获取areamode\r\n\t\t\t\twith_children: true // 获取包含子级的数据\r\n\t\t\t}, (res) => {\r\n\t\t\t\tif(res.status === 1) {\r\n\t\t\t\t\t// 将数据传递给城市选择页面\r\n\t\t\t\t\tlet data = encodeURIComponent(JSON.stringify({\r\n\t\t\t\t\t\tareas: res.data,\r\n\t\t\t\t\t\tarea_config: {\r\n\t\t\t\t\t\t\thotareas: '',\r\n\t\t\t\t\t\t\thotareas_str: '',\r\n\t\t\t\t\t\t\thotarea: 1,\r\n\t\t\t\t\t\t\tswitcharearange: 0,\r\n\t\t\t\t\t\t\tswitcharearangeareas_str: ''\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}));\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/city?data=' + data\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取城市列表失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tvar bid = that.opt.bid || 0;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\t// 显示下拉刷新动画\r\n\t\t\tif(uni.startPullDownRefresh) {\r\n\t\t\t\tuni.startPullDownRefresh();\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiYuyue/peocategory', {cid: nowcid,bid:bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.clist = data;\r\n\t\t\t\t//that.curCid = data[0]['id'];\r\n\t\t\t\tif (nowcid) {\r\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tif (data[i]['id'] == nowcid) {\r\n\t\t\t\t\t\t\tthat.curTopIndex = i;\r\n\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar downcdata = data[i]['child'];\r\n\t\t\t\t\t\tvar isget = 0;\r\n\t\t\t\t\t\tfor (var j = 0; j < downcdata; j++) {\r\n\t\t\t\t\t\t\tif (downcdata[j]['id'] == nowcid) {\r\n\t\t\t\t\t\t\t\tthat.curIndex = i;\r\n\t\t\t\t\t\t\t\tthat.curIndex2 = j;\r\n\t\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\t\tisget = 1;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (isget) break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\t\r\n\t\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\t\tconsole.log('位置获取成功：', res);\r\n\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\t\tthat.getdatalist();\r\n\t\t\t\t},\r\n\t\t\t\tfunction (err) {\r\n\t\t\t\t\tconsole.log('位置获取失败：', err);\r\n\t\t\t\t\tthat.locationFailed = true;\r\n\t\t\t\t\tthat.getdatalist();\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar cid = that.curCid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar order = that.order;\r\n\t\t    var keyword = that.keyword;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\t\r\n\t\t\tvar latitude = that.latitude;\r\n\t\t\tvar longitude = that.longitude;\r\n\t\t\t\r\n\t\t\tconsole.log('2023-06-15 11:05:23-INFO-[peolist][getdatalist_001] 发送请求，地区ID：', {\r\n\t\t\t\tprovinceId: that.provinceId,\r\n\t\t\t\tcityId: that.cityId,\r\n\t\t\t\tdistrictId: that.districtId\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tvar params = {\r\n\t\t\t\tpagenum: pagenum,\r\n\t\t\t\tkeyword: keyword,\r\n\t\t\t\tfield: field,\r\n\t\t\t\torder: order,\r\n\t\t\t\tcid: cid,\r\n\t\t\t\tbid: bid,\r\n\t\t\t\ttype: 'list',\r\n\t\t\t\t// 添加地区筛选参数，确保是数字类型\r\n\t\t\t\tprovince_id: parseInt(that.provinceId || 0),\r\n\t\t\t\tcity_id: parseInt(that.cityId || 0),\r\n\t\t\t\tdistrict_id: parseInt(that.districtId || 0)\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 只有当经纬度存在且没有定位失败标记时才添加位置信息\r\n\t\t\tif (longitude && latitude && !that.locationFailed) {\r\n\t\t\t\tparams.longitude = longitude;\r\n\t\t\t\tparams.latitude = latitude;\r\n\t\t\t\tconsole.log('发送位置信息到API：', longitude, latitude);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.post('ApiYuyue/selectpeople', params, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t  that.datalist = data;\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t  }\r\n\t\t\t\t} else {\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t  } else {\r\n\t\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\t\tthat.datalist = newdata;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (data.length > 0 && data[0].distance) {\r\n\t\t\t\t\tconsole.log('技师列表已按距离排序，最近的技师距离：' + data[0].distance);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 处理返回的数据，将未知距离统一格式化\r\n\t\t\t\tif (data.length > 0) {\r\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tif (!data[i].distance || data[i].distance === '未知') {\r\n\t\t\t\t\t\t\tdata[i].distance = '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.field === 'juli' && that.locationFailed) {\r\n\t\t\t\t\t\t// 如果要按距离排序但获取位置失败，提示用户\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '位置获取失败，无法按距离排序',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.field = ''; // 重置排序字段\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 停止下拉刷新动画\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t});\r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t   var that = this;\r\n\t\t   var id = e.currentTarget.dataset.id;\r\n\t\t   var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t   this.curTopIndex = index;\r\n\t\t   this.curIndex = -1;\r\n\t\t   this.curIndex2 = -1;\r\n\t\t   this.prolist = [];\r\n\t\t   this.nopro = 0;\r\n\t\t   this.curCid = id;\r\n\t\t   this.getdatalist();\r\n\t\t}, \r\n\t\tsearchChange: function (e) {\r\n\t\t  this.keyword = e.detail.value;\r\n\t\t},\r\n\t\tsearchConfirm: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var keyword = e.detail.value;\r\n\t\t  that.keyword = keyword;\r\n\t\t  that.getdata();\r\n\t\t},\r\n\t\tgoto: function(e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t},\r\n\t\tloaded: function() {\r\n\t\t\tthis.isload = true;\r\n\t\t},\r\n\t\tgetmenuindex: function(e) {\r\n\t\t\tthis.menuindex = e;\r\n\t\t},\r\n\t\tretryLocation: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.locationFailed = false;\r\n\t\t\t\r\n\t\t\t// 再次尝试获取位置\r\n\t\t\tapp.getLocation(function (res) {\r\n\t\t\t\tconsole.log('位置获取成功：', res);\r\n\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t\t\r\n\t\t\t\t// 显示获取成功提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '位置获取成功',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfunction (err) {\r\n\t\t\t\tconsole.log('位置获取失败：', err);\r\n\t\t\t\tthat.locationFailed = true;\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t\t\r\n\t\t\t\t// 显示获取失败提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '位置获取失败，请检查位置权限',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.topsearch{width:94%;margin:16rpx 3%;background: #ffffff;border-radius: 16rpx;box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);padding: 8rpx 16rpx;display:flex;align-items:center;}\r\n.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f7f7f7;flex:1;padding: 0 10rpx;margin: 0 10rpx;}\r\n.topsearch .f1 .img{width:30rpx;height:30rpx;margin-left:20rpx}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n/* 城市选择器样式调整 */\r\n.city-select {\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tpadding: 0 20rpx;\r\n\tmargin-right: 15rpx;\r\n\tbackground: #f7f7f7;\r\n\tborder-radius: 35rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\tmax-width: 150rpx;\r\n\toverflow: hidden;\r\n\twhite-space: nowrap;\r\n\ttext-overflow: ellipsis;\r\n\tposition: relative;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n/* 添加筛选按钮样式 */\r\n.filter-btn {\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tpadding: 0 25rpx;\r\n\tbackground: #f7f7f7;\r\n\tborder-radius: 35rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\tposition: relative;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.filter-btn:active {\r\n\topacity: 0.8;\r\n\ttransform: scale(0.98);\r\n\tbackground: #f0f0f0;\r\n}\r\n\r\n.filter-text {\r\n\tmargin-right: 6rpx;\r\n}\r\n\r\n.filter-icon {\r\n\tfont-size: 20rpx;\r\n\tcolor: #7A83EC;\r\n}\r\n\r\n/* 筛选选项浮层样式 */\r\n.filter-options {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 999;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.filter-overlay {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tz-index: -1;\r\n}\r\n\r\n.filter-panel {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tright: 0;\r\n\twidth: 70%;\r\n\theight: 100%;\r\n\tbackground: #fff;\r\n\tpadding: 50rpx 30rpx 30rpx;\r\n\tz-index: 1000;\r\n\tanimation: slideInRight 0.3s ease;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbox-shadow: -5rpx 0 15rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n@keyframes slideInRight {\r\n\tfrom {\r\n\t\ttransform: translateX(100%);\r\n\t}\r\n\tto {\r\n\t\ttransform: translateX(0);\r\n\t}\r\n}\r\n\r\n.filter-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 40rpx;\r\n\tmargin-top: 40rpx;\r\n\tpadding-left: 20rpx;\r\n\tborder-left: 8rpx solid #7A83EC;\r\n}\r\n\r\n.filter-list {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.filter-item {\r\n\tpadding: 15rpx 25rpx;\r\n\tborder: 1px solid #eee;\r\n\tborder-radius: 35rpx;\r\n\tmargin-right: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\ttransition: all 0.3s ease;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmin-width: 180rpx;\r\n}\r\n\r\n.filter-item.active {\r\n\tborder-color: #7A83EC;\r\n\tcolor: #7A83EC;\r\n\tbackground: rgba(122, 131, 236, 0.06);\r\n}\r\n\r\n.check-icon {\r\n\tmargin-left: 10rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.city-select:active {\r\n\topacity: 0.8;\r\n\ttransform: scale(0.98);\r\n\tbackground: #f0f0f0;\r\n}\r\n\r\n.city-select:after {\r\n\tcontent: \"\";\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground: rgba(122, 131, 236, 0.06);\r\n\tborder-radius: 35rpx;\r\n\topacity: 0;\r\n\ttransition: opacity 0.3s ease;\r\n}\r\n\r\n.city-select:active:after {\r\n\topacity: 1;\r\n}\r\n\r\n.city-icon {\r\n\tfont-size: 20rpx;\r\n\tmargin-left: 8rpx;\r\n\tcolor: #7A83EC;\r\n}\r\n\r\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx;box-shadow: 0 6rpx 15rpx rgba(0,0,0,0.03);}\r\n.order-tab2{display:flex;width:auto;min-width:100%;padding: 5rpx 0;}\r\n.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:84rpx; line-height:84rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;transition: all 0.3s ease;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:3px;width:40rpx;transition: all 0.3s ease;}\r\n.order-tab2 .on .after{display:block}\r\n\r\n\r\n.content{\r\n\twidth:94%;\r\n\tmargin:25rpx 3%;\r\n\tbackground:#fff;\r\n\tborder-radius:16rpx;\r\n\tpadding:28rpx 30rpx; \r\n\tjustify-content: space-between;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0,0,0,0.05);\r\n\ttransition: all 0.3s ease;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.content:after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 4rpx;\r\n\tbackground: linear-gradient(to right, #7A83EC, #9E8BF5);\r\n\topacity: 0;\r\n\ttransition: opacity 0.3s ease;\r\n}\r\n\r\n.content:active:after {\r\n\topacity: 1;\r\n}\r\n\r\n.content:active {\r\n\ttransform: translateY(2rpx);\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);\r\n}\r\n\r\n.content .f1{display:flex;align-items:center;flex: 1;}\r\n.content .headimg{\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tborder-radius: 12rpx;\r\n}\r\n.content .f1 image{ \r\n\twidth: 150rpx; \r\n\theight: 150rpx; \r\n\tborder-radius: 12rpx;\r\n\tobject-fit: cover;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\r\n\ttransition: transform 0.5s ease;\r\n}\r\n\r\n.content:active .f1 image {\r\n\ttransform: scale(1.05);\r\n}\r\n\r\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:20rpx;}\r\n.content .f1 .t2{\r\n\tcolor:#7A83EC;\r\n\tbackground: rgba(122, 131, 236, 0.1); \r\n\tmargin-left: 16rpx; \r\n\tpadding:4rpx 20rpx; \r\n\tfont-size: 22rpx; \r\n\tborder-radius: 18rpx;\r\n\tfont-weight: normal;\r\n}\r\n\r\n.name-type {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-wrap: wrap;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.text1{ margin-left: 30rpx; flex: 1; display: flex; flex-direction: column;}\r\n\r\n.text2{ \r\n\tcolor:#666666; \r\n\tfont-size: 26rpx;\r\n\tmargin-top: 8rpx;\r\n\tline-height: 1.4;\r\n\tdisplay: -webkit-box;\r\n\t-webkit-line-clamp: 2;\r\n\t-webkit-box-orient: vertical;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tmax-height: 72rpx;\r\n}\r\n.text3{ \r\n\tcolor:#999999; \r\n\tfont-size: 24rpx;\r\n\tmargin-top: 16rpx;\r\n\tline-height: 1.4;\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n}\r\n.text3 .t5{ margin-left: 24rpx;}\r\n.text3 .t5 text{ color:#7A83EC; font-weight: 600;}\r\n.text3 .t4 text{ color:#7A83EC; font-weight: 600;}\r\n.text3 .t6{ margin-left: 24rpx;}\r\n.text3 .t6 text{ color:#FF9900; font-weight: 600;}\r\n\r\n.yuyue{ \r\n\tbackground: linear-gradient(to right, #7A83EC, #9E8BF5); \r\n\theight: 64rpx; \r\n\tline-height: 64rpx; \r\n\tpadding: 0 25rpx; \r\n\tcolor:#fff; \r\n\tborder-radius:32rpx; \r\n\tmin-width: 110rpx; \r\n\tfont-size: 28rpx; \r\n\ttext-align: center; \r\n\tmargin-top: 20rpx;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 6rpx 15rpx rgba(122, 131, 236, 0.3);\r\n\tfont-weight: 500;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.yuyue:before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(255, 255, 255, 0.1);\r\n\ttransform: translateX(-100%);\r\n\ttransition: transform 0.5s ease;\r\n}\r\n\r\n.yuyue:active:before {\r\n\ttransform: translateX(0);\r\n}\r\n\r\n.yuyue:active {\r\n\ttransform: scale(0.95) translateY(2rpx);\r\n\tbox-shadow: 0 2rpx 6rpx rgba(122, 131, 236, 0.2);\r\n}\r\n\r\n.location-failed {\r\n\tmargin: 20rpx 3%;\r\n\tpadding: 30rpx 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 15rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.retry-btn {\r\n\tpadding: 14rpx 36rpx;\r\n\tborder: 1px solid #7A83EC;\r\n\tborder-radius: 30rpx;\r\n\tfont-size: 28rpx;\r\n\ttransition: all 0.3s ease;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.retry-btn:active {\r\n\ttransform: scale(0.98);\r\n\topacity: 0.9;\r\n\tbackground: rgba(122, 131, 236, 0.06);\r\n}\r\n\r\n.flex-center {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.container {\r\n\tbackground: #f8f9fc;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 50rpx;\r\n}\r\n\r\n.content-list {\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.no-data-placeholder {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 100rpx 0;\r\n}\r\n\r\n.empty-img {\r\n\twidth: 220rpx;\r\n\theight: 220rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n\tcolor: #999999;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n/* 当前已选择城市的状态优化 */\r\n.city-select.active {\r\n\tcolor: #7A83EC;\r\n\tfont-weight: 500;\r\n\tbackground: rgba(122, 131, 236, 0.08);\r\n\tbox-shadow: 0 2rpx 8rpx rgba(122, 131, 236, 0.1);\r\n}\r\n\r\n/* 添加技师标签样式 */\r\n.tech-tags {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-top: 10rpx;\r\n\tmargin-bottom: 4rpx;\r\n}\r\n\r\n.tech-tag {\r\n\tbackground: rgba(255, 153, 0, 0.1);\r\n\tcolor: #FF9900;\r\n\tfont-size: 22rpx;\r\n\tpadding: 4rpx 16rpx;\r\n\tborder-radius: 16rpx;\r\n\tmargin-right: 12rpx;\r\n\tmargin-bottom: 6rpx;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n/* 流式加载动画 */\r\n@keyframes skeleton-loading {\r\n\t0% {\r\n\t\tbackground-position: 100% 50%;\r\n\t}\r\n\t100% {\r\n\t\tbackground-position: 0 50%;\r\n\t}\r\n}\r\n\r\n.skeleton {\r\n\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);\r\n\tbackground-size: 400% 100%;\r\n\tanimation: skeleton-loading 1.4s ease infinite;\r\n\tborder-radius: 6rpx;\r\n}\r\n\r\n/* 列表项滑入动画 */\r\n@keyframes slideInFromBottom {\r\n\t0% {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(30rpx);\r\n\t}\r\n\t100% {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n.content {\r\n\tanimation: slideInFromBottom 0.3s ease;\r\n\tanimation-fill-mode: both;\r\n}\r\n\r\n/* 给每个列表项添加不同的延迟时间，形成瀑布流加载效果 */\r\n.content:nth-child(2) { animation-delay: 0.05s; }\r\n.content:nth-child(3) { animation-delay: 0.1s; }\r\n.content:nth-child(4) { animation-delay: 0.15s; }\r\n.content:nth-child(5) { animation-delay: 0.2s; }\r\n.content:nth-child(6) { animation-delay: 0.25s; }\r\n.content:nth-child(7) { animation-delay: 0.3s; }\r\n.content:nth-child(8) { animation-delay: 0.35s; }\r\n.content:nth-child(9) { animation-delay: 0.4s; }\r\n.content:nth-child(10) { animation-delay: 0.45s; }\r\n\r\n/* 骨架屏样式 */\r\n.skeleton-card {\r\n\tbox-shadow: none;\r\n\tmargin: 16rpx 3%;\r\n\tpadding: 24rpx 30rpx;\r\n\tanimation: none;\r\n}\r\n\r\n.headimg-skeleton {\r\n\twidth: 150rpx;\r\n\theight: 150rpx;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.title-skeleton {\r\n\twidth: 60%;\r\n\theight: 32rpx;\r\n\tmargin-top: 6rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.text-skeleton {\r\n\twidth: 90%;\r\n\theight: 24rpx;\r\n\tmargin-top: 12rpx;\r\n}\r\n\r\n.text-skeleton.short {\r\n\twidth: 60%;\r\n}\r\n\r\n.skeleton-tags {\r\n\tdisplay: flex;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.tag-skeleton {\r\n\twidth: 100rpx;\r\n\theight: 30rpx;\r\n\tborder-radius: 15rpx;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.btn-skeleton {\r\n\twidth: 110rpx;\r\n\theight: 64rpx;\r\n\tborder-radius: 32rpx;\r\n\tmargin-top: 20rpx;\r\n\talign-self: flex-end;\r\n}\r\n\r\n.filter-btn-group {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tborder-top: 1px solid #f5f5f5;\r\n\tpadding-top: 30rpx;\r\n\tmargin-top: auto;\r\n}\r\n\r\n.filter-reset, .filter-confirm {\r\n\twidth: 45%;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 28rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115044216\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}