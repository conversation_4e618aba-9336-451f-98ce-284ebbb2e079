{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?a2dc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?e386", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?917b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?cf62", "uni-app:///pagesExt/coupons/coupongive.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?fb80", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/coupons/coupongive.vue?86ec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "rids", "frommid", "uni", "title", "receiveCoupon", "adUnitId", "rewardedVideoAd", "console", "receiveCouponConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6CnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACAI;UACAC;QACA;QACA;QACAL;QACAA;MACA;IACA;IACAM;MACA;MACA;MACA;QACAL;QACA;UACAA;YAAAM;UAAA;QACA;QACA;QACAC;UAAAP;UAAAO;QAAA;UAAAP;QAAA;QACAO;UACAP;UACAA;UACAQ;UACAD;UACAA;QACA;QACAA;UACAP;UACA;YACA;YACAD;UACA;YACAS;UACA;UACAD;UACAA;QACA;MACA;QACAR;MACA;IACA;IACAU;MACA;MACAT;MACAA;QAAAC;QAAAC;MAAA;QACAF;QACA;UACAA;QACA;UACAA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/coupons/coupongive.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/coupons/coupongive.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupongive.vue?vue&type=template&id=56a35f26&\"\nvar renderjs\nimport script from \"./coupongive.vue?vue&type=script&lang=js&\"\nexport * from \"./coupongive.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupongive.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/coupons/coupongive.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupongive.vue?vue&type=template&id=56a35f26&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m1 = item.type == 10 ? _vm.t(\"color1\") : null\n        var m2 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m3 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m4 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m5 = item.type == 5 ? _vm.t(\"color1\") : null\n        var m6 =\n          item.type == 1 || item.type == 4 || item.type == 5\n            ? _vm.t(\"color1\")\n            : null\n        var m7 = item.type == 1 ? _vm.t(\"color1rgb\") : null\n        var m8 = item.type == 1 ? _vm.t(\"color1\") : null\n        var m9 = item.type == 2 ? _vm.t(\"color1rgb\") : null\n        var m10 = item.type == 2 ? _vm.t(\"color1\") : null\n        var m11 = item.type == 3 ? _vm.t(\"color1rgb\") : null\n        var m12 = item.type == 3 ? _vm.t(\"color1\") : null\n        var m13 = item.type == 4 ? _vm.t(\"color1rgb\") : null\n        var m14 = item.type == 4 ? _vm.t(\"color1\") : null\n        var m15 = item.type == 5 ? _vm.t(\"color1rgb\") : null\n        var m16 = item.type == 5 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n          m10: m10,\n          m11: m11,\n          m12: m12,\n          m13: m13,\n          m14: m14,\n          m15: m15,\n          m16: m16,\n        }\n      })\n    : null\n  var m17 = _vm.isload ? _vm.t(\"color1\") : null\n  var m18 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var g0 = _vm.isload ? _vm.datalist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m17: m17,\n        m18: m18,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupongive.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupongive.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"coupon-list\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"coupon\" @tap.stop=\"goto\" :data-url=\"'coupondetail?rid=' + item.id\" :style=\"(item.isgive == 1 || item.isgive == 2)?'padding-left:40rpx':''\">\r\n\t\t\t\t<view class=\"pt_left\">\r\n\t\t\t\t\t<view class=\"pt_left-content\">\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==1\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==10\"><text class=\"t1\">{{item.discount/10}}</text><text class=\"t0\">折</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==2\">礼品券</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==3\"><text class=\"t1\">{{item.limit_count}}</text><text class=\"t2\">次</text></view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==4\">抵运费</view>\r\n\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"item.type==5\"><text class=\"t0\">￥</text><text class=\"t1\">{{item.money}}</text></view>\r\n\t\t\t\t\t\t<view class=\"f2\" :style=\"{color:t('color1')}\" v-if=\"item.type==1 || item.type==4 || item.type==5\">\r\n\t\t\t\t\t\t\t<text v-if=\"item.minprice>0\">满{{item.minprice}}元可用</text>\r\n\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"pt_right\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.couponname}}</view>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==1\">代金券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==2\">礼品券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==3\">计次券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==4\">运费抵扣券</text>\r\n\t\t\t\t\t\t<text class=\"t2\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"item.type==5\">餐饮券</text>\r\n\t\t\t\t\t\t<view class=\"t4\" v-if=\"item.bid>0\">适用商家：{{item.bname}}</view>\r\n\t\t\t\t\t\t<view class=\"t3\" :style=\"item.bid>0?'margin-top:0':'margin-top:10rpx'\">有效期至 {{item.endtime}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\t\t<view class=\"giveopbox\">\r\n\t\t\t<view class=\"btn-give\" @tap=\"receiveCoupon\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">立即领取({{datalist.length}}张)</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n      st: 0,\r\n      datalist: [],\r\n      pagenum: 1,\r\n      nomore: false,\r\n\t\t\tnodata:false,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.st = this.opt.st || 0;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tthat.nodata = false;\r\n      app.post('ApiLipin2/coupongive', {rids: that.opt.rids,frommid:that.opt.pid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '领取' + that.t('优惠券')\r\n\t\t\t\t});\r\n        var data = res.data;\r\n\t\t\t\tthat.datalist = data;\r\n\t\t\t\tthat.loaded();\r\n      });\r\n    },\r\n\t\treceiveCoupon:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar couponinfo = that.datalist[0];\r\n\t\t\tif (app.globalData.platform == 'wx' && couponinfo && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tif(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){\r\n\t\t\t\t\tapp.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});\r\n\t\t\t\t}\r\n\t\t\t\tvar rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];\r\n\t\t\t\trewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});\r\n\t\t\t\trewardedVideoAd.onError((err) => {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.alert(err.errMsg);\r\n\t\t\t\t\tconsole.log('onError event emit', err)\r\n\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t});\r\n\t\t\t\trewardedVideoAd.onClose(res => {\r\n\t\t\t\t\tapp.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;\r\n\t\t\t\t\tif (res && res.isEnded) {\r\n\t\t\t\t\t\t//app.alert('播放结束 发放奖励');\r\n\t\t\t\t\t\tthat.receiveCouponConfirm(e);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('播放中途退出，不下发奖励');\r\n\t\t\t\t\t}\r\n\t\t\t\t\trewardedVideoAd.offLoad()\r\n\t\t\t\t\trewardedVideoAd.offClose();\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tthat.receiveCouponConfirm(e);\r\n\t\t\t}\r\n\t\t},\r\n\t\treceiveCouponConfirm:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading('领取中');\r\n\t\t\tapp.post('ApiLipin2/receiveCoupon2', {rids: that.opt.rids,frommid:that.opt.pid}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tapp.alert(data.msg,function(){\r\n\t\t\t\t\t\tapp.goto('/pages/my/usercenter');\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\r\n.coupon-list{width:100%;padding:20rpx}\r\n.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden;align-items:center;position:relative;background: #fff;}\r\n.coupon .pt_left{background: #fff;min-height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}\r\n.coupon .pt_left .t0{padding-right:0;}\r\n.coupon .pt_left .t1{font-size:60rpx;}\r\n.coupon .pt_left .t2{padding-left:10rpx;}\r\n.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}\r\n.coupon .pt_right{background: #fff;width:70%;display:flex;min-height:200rpx;text-align: left;padding:20rpx 20rpx;position:relative}\r\n.coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}\r\n.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}\r\n.coupon .pt_right .f1 .t2{height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx; margin-right: 16rpx;}\r\n.coupon .pt_right .f1 .t2:last-child {margin-right: 0;}\r\n.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}\r\n.coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-28rpx;border-radius:28rpx;width:160rpx;height:56rpx;line-height:56rpx;color:#fff}\r\n.coupon .pt_right .sygq{position:absolute;right:30rpx;top:50%;margin-top:-50rpx;width:100rpx;height:100rpx;}\r\n\r\n.coupon .pt_left.bg3{background:#ffffff;color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t1{color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t3{color:#b9b9b9!important}\r\n.coupon .pt_right.bg3 .t4{color:#999999!important}\r\n\r\n.giveopbox{position:fixed;bottom:0;left:0;width:100%;}\r\n.btn-give{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupongive.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupongive.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098354\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}