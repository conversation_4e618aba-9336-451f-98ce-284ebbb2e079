{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?7c22", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?ff91", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?5ae8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?2faa", "uni-app:///pagesExa/tuanzhangadmin/finance/withdrawdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?13fe", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhangadmin/finance/withdrawdetail.vue?6433"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "info", "onLoad", "methods", "getdata", "that", "app", "id", "uni", "title", "shenhenopass", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>yd<PERSON>", "wx<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAmwB,CAAgB,8wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8DvxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAG;UACAC;QACA;QACAJ;MACA;IACA;IACAK;MACA;MACA;MACAJ;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAK;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACAN;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAK;YACAN;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACAP;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAK;YACAN;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAK;YACAN;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAAmlC,CAAgB,+jCAAG,EAAC,C;;;;;;;;;;;ACAvmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhangadmin/finance/withdrawdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhangadmin/finance/withdrawdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdrawdetail.vue?vue&type=template&id=19f36497&\"\nvar renderjs\nimport script from \"./withdrawdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./withdrawdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdrawdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhangadmin/finance/withdrawdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawdetail.vue?vue&type=template&id=19f36497&\"", "var components\ntry {\n  components = {\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n<block v-if=\"isload\">\r\n\t<view class=\"orderinfo\">\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1 flex1\">{{t('会员')}}信息</text>\r\n\t\t\t<view class=\"t2 flex-y-center\" style=\"flex:0\"><image :src=\"info.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"></image> {{info.nickname}}</view>\r\n\t\t</view>\r\n\t</view>\r\n\t<view class=\"orderinfo\">\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1\">提现金额</text>\r\n\t\t\t<text class=\"t2\">￥{{info.txmoney}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1\">打款金额</text>\r\n\t\t\t<text class=\"t2\">￥{{info.money}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1\">提现方式</text>\r\n\t\t\t<text class=\"t2\">{{info.paytype}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\" v-if=\"info.paytype=='支付宝'\">\r\n\t\t\t<text class=\"t1\">支付宝账号</text>\r\n\t\t\t<text class=\"t2\">{{info.aliaccount}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\" v-if=\"info.paytype=='银行卡'\">\r\n\t\t\t<text class=\"t1\">开户行</text>\r\n\t\t\t<text class=\"t2\">{{info.bankname}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\" v-if=\"info.paytype=='银行卡'\">\r\n\t\t\t<text class=\"t1\">持卡人</text>\r\n\t\t\t<text class=\"t2\">{{info.bankcarduser}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"item\" v-if=\"info.paytype=='银行卡'\">\r\n\t\t\t<text class=\"t1\">卡号</text>\r\n\t\t\t<text class=\"t2\">{{info.bankcardnum}}</text>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1\">状态</text>\r\n\t\t\t<text class=\"t2\" v-if=\"info.status==0\">审核中</text>\r\n\t\t\t<text class=\"t2\" v-if=\"info.status==1\">已审核</text>\r\n\t\t\t<text class=\"t2\" v-if=\"info.status==2\">已驳回</text>\r\n\t\t\t<text class=\"t2\" v-if=\"info.status==3\">已打款</text>\r\n\t\t</view>\r\n\t</view>\r\n  <view style=\"width:100%;height:120rpx\"></view>\r\n\r\n  <view class=\"bottom\">\r\n\t\t<view v-if=\"info.status==0\" class=\"btn\" @tap=\"shenhepass\" :data-id=\"info.id\">审核通过</view>\r\n\t\t<view v-if=\"info.status==0\" class=\"btn\" @tap=\"shenhenopass\" :data-id=\"info.id\">审核驳回</view>\r\n\t\t<view v-if=\"info.status==1\" class=\"btn\" @tap=\"setydk\" :data-id=\"info.id\">改为已打款</view>\r\n\t\t<view v-if=\"info.status==1 && (info.paytype=='微信钱包' || info.paytype=='银行卡')\" class=\"btn\" @tap=\"wxdakuan\" :data-id=\"info.id\">微信打款</view>\r\n  </view>\r\n\r\n</block>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\r\n      info: {},\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApituanzhangAdminFinance/withdrawdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: that.t('余额') + '提现详情'\r\n\t\t\t\t});\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    shenhenopass: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定要驳回提现申请吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiAdminFinance/widthdrawnopass', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    shenhepass: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定要审核通过吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiAdminFinance/widthdrawpass', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    setydk: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定已打款吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiAdminFinance/widthdsetydk', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    wxdakuan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定要微信打款吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiAdminFinance/widthdwxdakuan', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\r\n.address{ display:flex;align-items:center;width: 100%; padding: 20rpx 3%; background: #FFF;margin-bottom:20rpx;}\r\n.address .img{width:60rpx}\r\n.address image{width: 50rpx; height: 50rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{ font-weight:bold}\r\n\r\n.product{width:100%; padding: 14rpx 3%;background: #FFF;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;position:relative}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content image{ width: 140rpx; height: 140rpx;}\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.product .content .detail .t1{height: 60rpx;line-height: 30rpx;color: #000;}\r\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.product .content .detail .t3{display:flex;height: 30rpx;line-height: 30rpx;color: #ff4246;}\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px; left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n.bottom .btn{ border-radius:10rpx; padding:10rpx 16rpx;margin-left: 10px; border: 1px #999 solid;color: #555;}\r\n.bottom .pay{ border: 1px #ff8758 solid; color: #ff8758;}\r\n.bottom .del{ border: 1px red solid;color: red;}\r\n.bottom .coll{ border: 1px #ff4246 solid;color: #ff4246;}\r\n.bottom .wul{ border: 1px #06aa53 solid; color: #06aa53; }\r\n.bottom .ref{ border: 1px #999 solid;color: #999;}\r\n.bottom .det{ border: 1px #555 solid;color: #555;}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdrawdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115055899\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}