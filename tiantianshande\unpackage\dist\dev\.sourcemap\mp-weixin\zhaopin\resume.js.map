{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?18fc", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?c897", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?e5a6", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?f3a2", "uni-app:///zhaopin/resume.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?247a", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/resume.vue?f4d2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentStep", "steps", "title", "required", "startDate", "endDate", "genderOptions", "educationLevels", "cityList", "arrivalTimeOptions", "jobTypes", "nationOptions", "cultureOptions", "formData", "name", "phone", "gender", "birthday", "education", "workExperience", "expectedPosition", "expectedSalary", "expectedCity", "arrivalTime", "jobType", "additionalInfo", "avatar", "email", "address", "nation", "age", "culture", "contentHeight", "isSubmitting", "pre_url", "loading", "isload", "isFocused", "computed", "canGoNext", "onLoad", "methods", "onGenderChange", "onBirthChange", "addEducation", "school", "major", "level", "deleteEducation", "app", "id", "that", "onEduLevelChange", "onEduStartChange", "onEduEndChange", "addWork", "company", "position", "description", "deleteWork", "onWorkStartChange", "onWorkEndChange", "onCityChange", "onArrivalTimeChange", "selectJobType", "jumpToStep", "uni", "icon", "onSwiperChange", "prevStep", "nextStep", "submitResume", "work_years", "current_status", "expect_position", "expect_salary", "expect_city", "self_evaluation", "arrival_time", "school_name", "start_time", "end_time", "company_name", "department", "Promise", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "removeAvatar", "previewImage", "urls", "validateEmail", "getResumeData", "loaded", "onNationChange", "onCultureChange", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnRA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACub/wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC,QACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,qBACA,QACA,SACA,UACA,UACA,KACA;MACAC;MACAC,gBACA,gGACA,6FACA,uFACA,uFACA,qBACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;QACA3C;QACAC;MACA;IACA;IACA2C;MACA;MACA;;MAEA;MACA;QACAC;UACAC;QACA;UACA;YACAC;YACAF;UACA;YACAA;UACA;QACA;MACA;QACA;QACAE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACArD;QACAC;QACAqD;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACAV;UACAC;QACA;UACA;YACAC;YACAF;UACA;YACAA;UACA;QACA;MACA;QACA;QACAE;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACAC;UACAhE;UACAiE;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACAtB;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;QACAnC;QACAY;QACAV;QACAC;QACAuD;QAAA;QACAtD;QAAA;QACAH;QACAY;QACA8C;QAAA;QACAC;QACAC;QACAC;QACAC;QACAhD;QACAC;QACAF;QACAG;QACA+C;MACA;MAEA7B;MACA;MACAA;QACA;UACA;;UAEA;UACA;YACA;cACAA;gBACAC;gBACA6B;gBACAjC;gBACA5B;gBACA8D;gBACAC;gBACAvB;cACA;YACA;UACA;;UAEA;UACA;YACA;cACAT;gBACAC;gBACAgC;gBACAzB;gBACA0B;gBACAH;gBACAC;gBACAvB;cACA;YACA;UACA;;UAEA;UACA0B;YACAnC;YACAA;YACAoC;cACApC;YACA;UACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IACAqC;MACA;MACArC;QACA;UACAE;QACA;MACA;IACA;IACAoC;MACA;IACA;IACAC;MACA;MACA;QACAtB;UACAuB;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAxC;MACAF;QACAE;QACA;UACA;UACA;YACA;YACAA;cACArC;cACAC;cACAC;cACAC;cACAS;cACAC;cACAC;cACAC;cACAC;cACAC;cACAb;cAAA;cACAC;cAAA;cACAC;cACAC;cACAC;cACAG;cACAF;cACAC;YACA;;YAEA;YACA2B;cAAA;gBACAD;gBACAL;gBACAC;gBACAC;gBACA3C;gBACAC;gBACAqD;cACA;YAAA;;YAEA;YACAP;cAAA;gBACAD;gBACAM;gBACAC;gBACA0B;gBACA/E;gBACAC;gBACAqD;cACA;YAAA;UACA;UACAP;QACA;MACA;IACA;IACA;IACAyC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACA;QACA7B;UACAhE;UACAiE;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACh1BA;AAAA;AAAA;AAAA;AAA07C,CAAgB,q4CAAG,EAAC,C;;;;;;;;;;;ACA98C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/resume.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/resume.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./resume.vue?vue&type=template&id=e893a35e&scoped=true&\"\nvar renderjs\nimport script from \"./resume.vue?vue&type=script&lang=js&\"\nexport * from \"./resume.vue?vue&type=script&lang=js&\"\nimport style0 from \"./resume.vue?vue&type=style&index=0&id=e893a35e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e893a35e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/resume.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./resume.vue?vue&type=template&id=e893a35e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  var l0 = _vm.__map(_vm.steps, function (step, index) {\n    var $orig = _vm.__get_orig(step)\n    var m2 =\n      _vm.currentStep === index || index < _vm.currentStep\n        ? _vm.t(\"color1\")\n        : null\n    return {\n      $orig: $orig,\n      m2: m2,\n    }\n  })\n  var m3 = _vm.t(\"color1\")\n  var m4 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m5 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m6 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m7 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m8 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m9 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m10 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m11 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m12 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m13 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m14 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m15 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m16 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m17 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m18 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m19 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m20 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m21 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m22 = _vm.t(\"color1\")\n  var m23 = _vm.t(\"color1rgb\")\n  var m24 = _vm.t(\"color1\")\n  var m25 = _vm.t(\"color1rgb\")\n  var g0 = _vm.formData.education.length\n  var l1 = !(g0 === 0)\n    ? _vm.__map(_vm.formData.education, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m26 = _vm.t(\"color1\")\n        var m27 = _vm.t(\"color1rgb\")\n        var m28 = _vm.t(\"color1rgb\")\n        var m29 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m30 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m31 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m32 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m33 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m34 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m35 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m36 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m37 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m38 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m39 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m40 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m26: m26,\n          m27: m27,\n          m28: m28,\n          m29: m29,\n          m30: m30,\n          m31: m31,\n          m32: m32,\n          m33: m33,\n          m34: m34,\n          m35: m35,\n          m36: m36,\n          m37: m37,\n          m38: m38,\n          m39: m39,\n          m40: m40,\n        }\n      })\n    : null\n  var m41 = _vm.t(\"color1\")\n  var m42 = _vm.t(\"color1rgb\")\n  var m43 = _vm.t(\"color1\")\n  var m44 = _vm.t(\"color1rgb\")\n  var m45 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m46 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var g1 = _vm.formData.workExperience.length\n  var l2 = !(g1 === 0)\n    ? _vm.__map(_vm.formData.workExperience, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m47 = _vm.t(\"color1\")\n        var m48 = _vm.t(\"color1rgb\")\n        var m49 = _vm.t(\"color1rgb\")\n        var m50 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m51 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m52 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m53 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m54 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m55 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m56 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m57 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m58 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m59 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        var m60 = _vm.isFocused ? _vm.t(\"color1\") : null\n        var m61 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m47: m47,\n          m48: m48,\n          m49: m49,\n          m50: m50,\n          m51: m51,\n          m52: m52,\n          m53: m53,\n          m54: m54,\n          m55: m55,\n          m56: m56,\n          m57: m57,\n          m58: m58,\n          m59: m59,\n          m60: m60,\n          m61: m61,\n        }\n      })\n    : null\n  var m62 = _vm.t(\"color1\")\n  var m63 = _vm.t(\"color1rgb\")\n  var m64 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m65 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m66 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m67 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m68 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m69 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var m70 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m71 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var l3 = _vm.__map(_vm.jobTypes, function (type, index) {\n    var $orig = _vm.__get_orig(type)\n    var m72 = _vm.formData.jobType === type ? _vm.t(\"color1rgb\") : null\n    var m73 = _vm.formData.jobType === type ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m72: m72,\n      m73: m73,\n    }\n  })\n  var m74 = _vm.isFocused ? _vm.t(\"color1\") : null\n  var m75 = _vm.isFocused ? _vm.t(\"color1rgb\") : null\n  var g2 = _vm.steps.length\n  var m76 = _vm.currentStep < g2 - 1 ? _vm.t(\"color1\") : null\n  var m77 = _vm.currentStep < g2 - 1 ? _vm.t(\"color1rgb\") : null\n  var g3 = _vm.steps.length\n  var m78 = _vm.currentStep === g3 - 1 ? _vm.t(\"color1\") : null\n  var m79 = _vm.currentStep === g3 - 1 ? _vm.t(\"color1rgb\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e, index) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.onEduLevelChange(e, index)\n    }\n    _vm.e1 = function (e, index) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp3 = args[args.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        index = _temp4.index\n      var _temp3, _temp4\n      return _vm.onEduStartChange(e, index)\n    }\n    _vm.e2 = function (e, index) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp5 = args[args.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        index = _temp6.index\n      var _temp5, _temp6\n      return _vm.onEduEndChange(e, index)\n    }\n    _vm.e3 = function (e, index) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp7 = args[args.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        index = _temp8.index\n      var _temp7, _temp8\n      return _vm.onWorkStartChange(e, index)\n    }\n    _vm.e4 = function (e, index) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp9 = args[args.length - 1].currentTarget.dataset,\n        _temp10 = _temp9.eventParams || _temp9[\"event-params\"],\n        index = _temp10.index\n      var _temp9, _temp10\n      return _vm.onWorkEndChange(e, index)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        l0: l0,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        g0: g0,\n        l1: l1,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        g1: g1,\n        l2: l2,\n        m62: m62,\n        m63: m63,\n        m64: m64,\n        m65: m65,\n        m66: m66,\n        m67: m67,\n        m68: m68,\n        m69: m69,\n        m70: m70,\n        m71: m71,\n        l3: l3,\n        m74: m74,\n        m75: m75,\n        g2: g2,\n        m76: m76,\n        m77: m77,\n        g3: g3,\n        m78: m78,\n        m79: m79,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./resume.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./resume.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"resume-page\">\r\n    <view class=\"header\" :style=\"'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\">\r\n      <text class=\"title\">完善简历</text>\r\n      <text class=\"subtitle\">完善您的简历信息，获得更多工作机会</text>\r\n      \r\n      <view class=\"step-nav\">\r\n        <view \r\n          v-for=\"(step, index) in steps\" \r\n          :key=\"index\"\r\n          class=\"step-item\"\r\n          :class=\"{ 'active': currentStep === index, 'completed': index < currentStep }\"\r\n          @tap=\"jumpToStep(index)\"\r\n        >\r\n          <view class=\"step-number\" :style=\"currentStep === index || index < currentStep ? 'background-color:'+t('color1')+';color:#fff' : ''\">\r\n            {{ index + 1 }}\r\n          </view>\r\n          <text class=\"step-text\">{{ step.title }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <swiper \r\n      class=\"content\" \r\n      :style=\"{ height: contentHeight + 'px' }\"\r\n      :current=\"currentStep\"\r\n      @change=\"onSwiperChange\"\r\n      :disable-touch=\"true\"\r\n    >\r\n      <!-- 基本信息 -->\r\n      <swiper-item>\r\n        <scroll-view scroll-y class=\"step-content\">\r\n          <view class=\"section basic-info\">\r\n            <view class=\"section-header\">\r\n              <text class=\"section-title\">\r\n                <view class=\"title-bar\" :style=\"'background:'+t('color1')\"></view>\r\n                基本信息\r\n              </text>\r\n            </view>\r\n            \r\n            <view class=\"avatar-wrapper\">\r\n              <view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n                <view v-if=\"formData.avatar\" class=\"layui-imgbox\">\r\n                  <view class=\"layui-imgbox-close\" @tap=\"removeAvatar\"><image src=\"/static/img/ico-del.png\"></image></view>\r\n                  <view class=\"layui-imgbox-img\">\r\n                    <image :src=\"formData.avatar\" @tap=\"previewImage\" :data-url=\"formData.avatar\" mode=\"aspectFill\"></image>\r\n                  </view>\r\n                </view>\r\n                <view v-else class=\"uploadbtn\" \r\n                  :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" \r\n                  @tap=\"chooseAvatar\">\r\n                </view>\r\n              </view>\r\n              <text class=\"upload-text\">点击上传头像</text>\r\n            </view>\r\n            \r\n            <view class=\"form-group\">\r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label required\">姓名</text>\r\n                <input \r\n                  v-model=\"formData.name\" \r\n                  placeholder=\"请输入您的真实姓名\" \r\n                  class=\"input-item\" \r\n                  maxlength=\"20\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                />\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label required\">手机号码</text>\r\n                <input \r\n                  v-model=\"formData.phone\" \r\n                  type=\"number\" \r\n                  placeholder=\"请输入手机号码\" \r\n                  class=\"input-item\"\r\n                  maxlength=\"11\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                />\r\n              </view>\r\n              \r\n              <view class=\"input-row\">\r\n                <view class=\"input-wrapper half\">\r\n                  <text class=\"input-label required\">性别</text>\r\n                  <picker \r\n                    mode=\"selector\" \r\n                    :range=\"genderOptions\" \r\n                    @change=\"onGenderChange\" \r\n                    class=\"input-item\"\r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  >\r\n                    <view class=\"picker-item\">\r\n                      {{formData.gender || '请选择性别'}}\r\n                    </view>\r\n                  </picker>\r\n                </view>\r\n                \r\n                <view class=\"input-wrapper half\">\r\n                  <text class=\"input-label required\">出生日期</text>\r\n                  <picker \r\n                    mode=\"date\" \r\n                    :start=\"startDate\" \r\n                    :end=\"endDate\" \r\n                    @change=\"onBirthChange\" \r\n                    class=\"input-item\"\r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  >\r\n                    <view class=\"picker-item\">\r\n                      {{formData.birthday || '请选择出生日期'}}\r\n                    </view>\r\n                  </picker>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">邮箱</text>\r\n                <input \r\n                  v-model=\"formData.email\" \r\n                  type=\"text\"\r\n                  placeholder=\"请输入邮箱地址\" \r\n                  class=\"input-item\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                />\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">居住地址</text>\r\n                <input \r\n                  v-model=\"formData.address\" \r\n                  type=\"text\"\r\n                  placeholder=\"请输入居住地址\" \r\n                  class=\"input-item\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                />\r\n              </view>\r\n\r\n              <view class=\"input-row\">\r\n                <view class=\"input-wrapper half\">\r\n                  <text class=\"input-label required\">民族</text>\r\n                  <picker \r\n                    mode=\"selector\" \r\n                    :range=\"nationOptions\" \r\n                    @change=\"onNationChange\" \r\n                    class=\"input-item\"\r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  >\r\n                    <view class=\"picker-item\">\r\n                      {{formData.nation || '请选择民族'}}\r\n                    </view>\r\n                  </picker>\r\n                </view>\r\n                \r\n                <view class=\"input-wrapper half\">\r\n                  <text class=\"input-label required\">年龄</text>\r\n                  <input \r\n                    v-model=\"formData.age\" \r\n                    type=\"number\"\r\n                    placeholder=\"请输入年龄\" \r\n                    class=\"input-item\"\r\n                    maxlength=\"3\"\r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  />\r\n                </view>\r\n              </view>\r\n\r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label required\">文化程度</text>\r\n                <picker \r\n                  mode=\"selector\" \r\n                  :range=\"cultureOptions\" \r\n                  @change=\"onCultureChange\" \r\n                  class=\"input-item\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                >\r\n                  <view class=\"picker-item\">\r\n                    {{formData.culture || '请选择文化程度'}}\r\n                  </view>\r\n                </picker>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </swiper-item>\r\n\r\n      <!-- 教育经历 -->\r\n      <swiper-item>\r\n        <scroll-view scroll-y class=\"step-content\">\r\n          <view class=\"section education\">\r\n            <view class=\"section-header\">\r\n              <text class=\"section-title\">\r\n                <view class=\"title-bar\" :style=\"'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"></view>\r\n                教育经历\r\n              </text>\r\n              <view class=\"add-btn\" @tap=\"addEducation\" :style=\"'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)'\">添加教育经历</view>\r\n            </view>\r\n            <view v-if=\"formData.education.length === 0\" class=\"empty-tip\">\r\n              <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-icon\"></image>\r\n              <text>暂无教育经历，点击上方添加</text>\r\n            </view>\r\n            <view v-else v-for=\"(item, index) in formData.education\" :key=\"index\" class=\"edu-item\">\r\n              <view class=\"item-header\">\r\n                <text class=\"item-index\" :style=\"'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+t('color1rgb')+',0.2);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')\">\r\n                  {{ index + 1 }}\r\n                </text>\r\n                <view class=\"delete-btn\" @tap=\"deleteEducation(index)\">\r\n                  <text class=\"delete-icon\">×</text>\r\n                  <text>删除</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"form-group\">\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">学校名称</text>\r\n                  <input v-model=\"item.school\" placeholder=\"请输入学校名称\" class=\"input-item\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\" />\r\n                </view>\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">专业</text>\r\n                  <input v-model=\"item.major\" placeholder=\"请输入专业名称\" class=\"input-item\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\" />\r\n                </view>\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">学历</text>\r\n                  <picker mode=\"selector\" :range=\"educationLevels\" @change=\"(e) => onEduLevelChange(e, index)\" class=\"input-item\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\">\r\n                    <view class=\"picker-item\">{{item.level || '请选择学历'}}</view>\r\n                  </picker>\r\n                </view>\r\n                <view class=\"date-range\">\r\n                  <view class=\"date-item\">\r\n                    <text class=\"input-label\">入学时间</text>\r\n                    <picker mode=\"date\" :start=\"startDate\" :end=\"endDate\" @change=\"(e) => onEduStartChange(e, index)\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\">\r\n                      <view class=\"picker-item\">{{item.startDate || '请选择入学时间'}}</view>\r\n                    </picker>\r\n                  </view>\r\n                  <view class=\"date-separator\"></view>\r\n                  <view class=\"date-item\">\r\n                    <text class=\"input-label\">毕业时间</text>\r\n                    <picker mode=\"date\" :start=\"startDate\" :end=\"endDate\" @change=\"(e) => onEduEndChange(e, index)\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\">\r\n                      <view class=\"picker-item\">{{item.endDate || '请选择毕业时间'}}</view>\r\n                    </picker>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </swiper-item>\r\n\r\n      <!-- 工作经历 -->\r\n      <swiper-item>\r\n        <scroll-view scroll-y class=\"step-content\">\r\n          <view class=\"section work-exp\">\r\n            <view class=\"section-header\">\r\n              <text class=\"section-title\">\r\n                <view class=\"title-bar\" :style=\"'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"></view>\r\n                工作经历\r\n              </text>\r\n              <view class=\"add-btn\" @tap=\"addWork\" :style=\"'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')\">添加工作经历</view>\r\n            </view>\r\n            <view v-if=\"formData.workExperience.length === 0\" class=\"empty-tip\">\r\n              <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-icon\"></image>\r\n              <text>暂无工作经历，点击上方添加</text>\r\n            </view>\r\n            <view v-else v-for=\"(item, index) in formData.workExperience\" :key=\"index\" class=\"work-item\">\r\n              <view class=\"item-header\">\r\n                <text class=\"item-index\" :style=\"'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+t('color1rgb')+',0.2);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')\">\r\n                  {{ index + 1 }}\r\n                </text>\r\n                <view class=\"delete-btn\" @tap=\"deleteWork(index)\">\r\n                  <text class=\"delete-icon\">×</text>\r\n                  <text>删除</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"form-group\">\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">公司名称</text>\r\n                  <input v-model=\"item.company\" placeholder=\"请输入公司名称\" class=\"input-item\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\" />\r\n                </view>\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">职位</text>\r\n                  <input v-model=\"item.position\" placeholder=\"请输入职位名称\" class=\"input-item\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\" />\r\n                </view>\r\n                <view class=\"date-range\">\r\n                  <view class=\"date-item\">\r\n                    <text class=\"input-label\">入职时间</text>\r\n                    <picker mode=\"date\" :start=\"startDate\" :end=\"endDate\" @change=\"(e) => onWorkStartChange(e, index)\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\">\r\n                      <view class=\"picker-item\">{{item.startDate || '请选择入职时间'}}</view>\r\n                    </picker>\r\n                  </view>\r\n                  <view class=\"date-separator\"></view>\r\n                  <view class=\"date-item\">\r\n                    <text class=\"input-label\">离职时间</text>\r\n                    <picker mode=\"date\" :start=\"startDate\" :end=\"endDate\" @change=\"(e) => onWorkEndChange(e, index)\" :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\">\r\n                      <view class=\"picker-item\">{{item.endDate || '请选择离职时间'}}</view>\r\n                    </picker>\r\n                  </view>\r\n                </view>\r\n                <view class=\"input-wrapper\">\r\n                  <text class=\"input-label\">工作描述</text>\r\n                  <textarea \r\n                    v-model=\"item.description\" \r\n                    placeholder=\"请描述您的工作职责和成果\" \r\n                    class=\"textarea-item\"\r\n                    :maxlength=\"500\"\r\n                    show-count\r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  ></textarea>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </swiper-item>\r\n\r\n      <!-- 求职意向 -->\r\n      <swiper-item>\r\n        <scroll-view scroll-y class=\"step-content\">\r\n          <view class=\"section job-intention\">\r\n            <view class=\"section-header\">\r\n              <text class=\"section-title\">\r\n                <view class=\"title-bar\" :style=\"'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"></view>\r\n                求职意向\r\n              </text>\r\n            </view>\r\n            <view class=\"form-group\">\r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">期望职位</text>\r\n                <input \r\n                  v-model=\"formData.expectedPosition\" \r\n                  placeholder=\"请输入期望职位\" \r\n                  class=\"input-item\" \r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                />\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">期望薪资</text>\r\n                <view class=\"salary-input\">\r\n                  <input \r\n                    v-model=\"formData.expectedSalary\" \r\n                    type=\"digit\"\r\n                    placeholder=\"请输入期望薪资\" \r\n                    class=\"input-item\" \r\n                    :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                  />\r\n                  <text class=\"salary-unit\">元/月</text>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">期望城市</text>\r\n                <picker \r\n                  mode=\"selector\" \r\n                  :range=\"cityList\" \r\n                  @change=\"onCityChange\" \r\n                  class=\"input-item\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                >\r\n                  <view class=\"picker-item\">\r\n                    {{formData.expectedCity || '请选择期望城市'}}\r\n                  </view>\r\n                </picker>\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">到岗时间</text>\r\n                <picker \r\n                  mode=\"selector\" \r\n                  :range=\"arrivalTimeOptions\" \r\n                  @change=\"onArrivalTimeChange\" \r\n                  class=\"input-item\"\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                >\r\n                  <view class=\"picker-item\">\r\n                    {{formData.arrivalTime || '请选择到岗时间'}}\r\n                  </view>\r\n                </picker>\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">工作类型</text>\r\n                <view class=\"job-type-group\">\r\n                  <view \r\n                    v-for=\"(type, index) in jobTypes\" \r\n                    :key=\"index\"\r\n                    class=\"job-type-item\"\r\n                    :class=\"{ 'active': formData.jobType === type }\"\r\n                    @tap=\"selectJobType(type)\"\r\n                    :style=\"formData.jobType === type ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''\"\r\n                  >\r\n                    {{ type }}\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"input-wrapper\">\r\n                <text class=\"input-label\">补充说明</text>\r\n                <textarea \r\n                  v-model=\"formData.additionalInfo\" \r\n                  placeholder=\"请补充说明您的其他期望（如：行业偏好、公司规模等）\" \r\n                  class=\"textarea-item\"\r\n                  :maxlength=\"200\"\r\n                  show-count\r\n                  :style=\"isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''\"\r\n                ></textarea>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </swiper-item>\r\n    </swiper>\r\n\r\n    <view class=\"footer safe-area-bottom\">\r\n      <view class=\"btn-group\">\r\n        <button \r\n          class=\"nav-btn prev-btn\" \r\n          v-if=\"currentStep > 0\"\r\n          @tap=\"prevStep\"\r\n        >上一步</button>\r\n        \r\n        <button \r\n          class=\"nav-btn next-btn\" \r\n          v-if=\"currentStep < steps.length - 1\"\r\n          @tap=\"nextStep\"\r\n          :disabled=\"!canGoNext\"\r\n          :style=\"'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"\r\n        >下一步</button>\r\n        \r\n        <button \r\n          class=\"submit-btn\" \r\n          v-if=\"currentStep === steps.length - 1\"\r\n          @tap=\"submitResume\" \r\n          :disabled=\"isSubmitting\"\r\n          :style=\"'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"\r\n        >\r\n          <text class=\"btn-text\">{{ isSubmitting ? '保存中...' : '保存简历' }}</text>\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentStep: 0,\r\n      steps: [\r\n        { title: '基本信息', required: ['name', 'phone', 'gender', 'birthday'] },\r\n        { title: '教育经历', required: [] },\r\n        { title: '工作经历', required: [] },\r\n        { title: '求职意向', required: ['expectedPosition', 'expectedSalary', 'expectedCity'] }\r\n      ],\r\n      startDate: '1960-01-01',\r\n      endDate: new Date().toISOString().split('T')[0],\r\n      genderOptions: ['男', '女'],\r\n      educationLevels: ['高中', '专科', '本科', '硕士', '博士'],\r\n      cityList: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京'],\r\n      arrivalTimeOptions: [\r\n        '随时到岗',\r\n        '一周内到岗',\r\n        '一个月内到岗', \r\n        '三个月内到岗',\r\n        '待定'\r\n      ],\r\n      jobTypes: ['全职', '兼职', '实习'],\r\n      nationOptions: [\r\n        '汉族','壮族','满族','回族','苗族','维吾尔族','土家族','彝族','蒙古族','藏族','布依族','侗族','瑶族','朝鲜族','白族',\r\n        '哈尼族','哈萨克族','黎族','傣族','畲族','傈僳族','仡佬族','东乡族','高山族','拉祜族','水族','佤族','纳西族','羌族',\r\n        '土族','仫佬族','锡伯族','柯尔克孜族','达斡尔族','景颇族','毛南族','撒拉族','布朗族','塔吉克族','阿昌族','普米族',\r\n        '鄂温克族','怒族','京族','基诺族','德昂族','保安族','俄罗斯族','裕固族','乌孜别克族','门巴族','鄂伦春族','独龙族',\r\n        '塔塔尔族','赫哲族','珞巴族'\r\n      ],\r\n      cultureOptions: ['小学', '初中', '高中', '专科', '本科', '硕士研究生', '博士研究生'],\r\n      formData: {\r\n        name: '',\r\n        phone: '',\r\n        gender: '',\r\n        birthday: '',\r\n        education: [],\r\n        workExperience: [],\r\n        expectedPosition: '',\r\n        expectedSalary: '',\r\n        expectedCity: '',\r\n        arrivalTime: '',\r\n        jobType: '全职',\r\n        additionalInfo: '',\r\n        avatar: '',\r\n        email: '',\r\n        address: '',\r\n        nation: '',\r\n        age: '',\r\n        culture: '',\r\n      },\r\n      contentHeight: 0,\r\n      isSubmitting: false,\r\n      pre_url: '',\r\n      loading: false,\r\n      isload: false,\r\n      isFocused: false\r\n    }\r\n  },\r\n  computed: {\r\n    canGoNext() {\r\n      const currentStepData = this.steps[this.currentStep]\r\n      if (!currentStepData.required.length) return true\r\n      \r\n      return currentStepData.required.every(field => {\r\n        const value = this.formData[field]\r\n        return value && value.toString().trim() !== ''\r\n      })\r\n    }\r\n  },\r\n  onLoad() {\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    this.contentHeight = systemInfo.windowHeight - uni.upx2px(380)\r\n    this.pre_url = app.globalData.pre_url\r\n    this.getResumeData()\r\n  },\r\n  methods: {\r\n    onGenderChange(e) {\r\n      this.formData.gender = this.genderOptions[e.detail.value]\r\n    },\r\n    onBirthChange(e) {\r\n      this.formData.birthday = e.detail.value\r\n    },\r\n    addEducation() {\r\n      this.formData.education.push({\r\n        school: '',\r\n        major: '',\r\n        level: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      })\r\n    },\r\n    deleteEducation(index) {\r\n      var that = this;\r\n      var edu = this.formData.education[index];\r\n      \r\n      // 如果有id，则调用删除接口\r\n      if(edu.id) {\r\n        app.post(\"ApiZhaopin/deleteEducation\", {\r\n          id: edu.id\r\n        }, function(res) {\r\n          if(res.status == 1) {\r\n            that.formData.education.splice(index, 1);\r\n            app.error('删除成功');\r\n          } else {\r\n            app.error(res.msg);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有id，直接从数组中删除\r\n        that.formData.education.splice(index, 1);\r\n      }\r\n    },\r\n    onEduLevelChange(e, index) {\r\n      this.formData.education[index].level = this.educationLevels[e.detail.value]\r\n    },\r\n    onEduStartChange(e, index) {\r\n      this.formData.education[index].startDate = e.detail.value\r\n    },\r\n    onEduEndChange(e, index) {\r\n      this.formData.education[index].endDate = e.detail.value\r\n    },\r\n    addWork() {\r\n      this.formData.workExperience.push({\r\n        company: '',\r\n        position: '',\r\n        startDate: '',\r\n        endDate: '',\r\n        description: ''\r\n      })\r\n    },\r\n    deleteWork(index) {\r\n      var that = this;\r\n      var work = this.formData.workExperience[index];\r\n      \r\n      // 如果有id，则调用删除接口\r\n      if(work.id) {\r\n        app.post(\"ApiZhaopin/deleteWork\", {\r\n          id: work.id\r\n        }, function(res) {\r\n          if(res.status == 1) {\r\n            that.formData.workExperience.splice(index, 1);\r\n            app.error('删除成功');\r\n          } else {\r\n            app.error(res.msg);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有id，直接从数组中删除\r\n        that.formData.workExperience.splice(index, 1);\r\n      }\r\n    },\r\n    onWorkStartChange(e, index) {\r\n      this.formData.workExperience[index].startDate = e.detail.value\r\n    },\r\n    onWorkEndChange(e, index) {\r\n      this.formData.workExperience[index].endDate = e.detail.value\r\n    },\r\n    onCityChange(e) {\r\n      this.formData.expectedCity = this.cityList[e.detail.value]\r\n    },\r\n    onArrivalTimeChange(e) {\r\n      this.formData.arrivalTime = this.arrivalTimeOptions[e.detail.value]\r\n    },\r\n    selectJobType(type) {\r\n      this.formData.jobType = type\r\n    },\r\n    jumpToStep(index) {\r\n      if (index < this.currentStep || this.canGoNext) {\r\n        this.currentStep = index\r\n      } else {\r\n        uni.showToast({\r\n          title: '请完善当前步骤',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    onSwiperChange(e) {\r\n      const { current } = e.detail\r\n      this.currentStep = current\r\n    },\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--\r\n      }\r\n    },\r\n    nextStep() {\r\n      if (this.currentStep < this.steps.length - 1 && this.canGoNext) {\r\n        this.currentStep++\r\n      }\r\n    },\r\n    submitResume() {\r\n      var that = this;\r\n      var formData = this.formData;\r\n      \r\n      // 表单验证\r\n      if (!formData.name) {\r\n        app.error('请填写姓名');\r\n        return false;\r\n      }\r\n      if (!formData.phone) {\r\n        app.error('请填写手机号码');\r\n        return false;\r\n      }\r\n      if (!formData.gender) {\r\n        app.error('请选择性别');\r\n        return false;\r\n      }\r\n      if (!formData.birthday) {\r\n        app.error('请选择出生日期');\r\n        return false;\r\n      }\r\n      if (!formData.avatar) {\r\n        app.error('请上传头像');\r\n        return false;\r\n      }\r\n      if (!formData.nation) {\r\n        app.error('请选择民族');\r\n        return false;\r\n      }\r\n      if (!formData.age) {\r\n        app.error('请输入年龄');\r\n        return false;\r\n      }\r\n      if (!formData.culture) {\r\n        app.error('请选择文化程度');\r\n        return false;\r\n      }\r\n     \r\n      if (formData.email && !this.validateEmail(formData.email)) {\r\n        app.error('请输入正确的邮箱格式');\r\n        return false;\r\n      }\r\n      \r\n      // 构造提交数据\r\n      var submitData = {\r\n        name: formData.name,\r\n        avatar: formData.avatar,\r\n        gender: formData.gender === '男' ? 1 : 2,\r\n        birthday: formData.birthday,\r\n        work_years: 0, // 默认应届生\r\n        education: formData.culture, // 使用文化程度作为最高学历\r\n        phone: formData.phone,\r\n        email: formData.email || '',\r\n        current_status: 0, // 默认随时到岗\r\n        expect_position: formData.expectedPosition,\r\n        expect_salary: formData.expectedSalary,\r\n        expect_city: formData.expectedCity,\r\n        self_evaluation: formData.additionalInfo || '',\r\n        nation: formData.nation,\r\n        age: formData.age,\r\n        address: formData.address,\r\n        culture: formData.culture,\r\n        arrival_time: formData.arrivalTime || ''\r\n      };\r\n      \r\n      app.showLoading('提交中');\r\n      // 保存基本信息\r\n      app.post(\"ApiZhaopin/saveResumeBasic\", submitData, function (res) {\r\n        if(res.status == 1){\r\n          var resume_id = res.data.resume_id;\r\n          \r\n          // 保存教育经历\r\n          var eduPromises = formData.education.map(edu => {\r\n            return new Promise((resolve, reject) => {\r\n              app.post(\"ApiZhaopin/saveEducation\", {\r\n                id: edu.id || 0,\r\n                school_name: edu.school,\r\n                major: edu.major,\r\n                education: edu.level,\r\n                start_time: edu.startDate,\r\n                end_time: edu.endDate,\r\n                description: edu.description || ''\r\n              }, resolve);\r\n            });\r\n          });\r\n          \r\n          // 保存工作经历\r\n          var workPromises = formData.workExperience.map(work => {\r\n            return new Promise((resolve, reject) => {\r\n              app.post(\"ApiZhaopin/saveWork\", {\r\n                id: work.id || 0,\r\n                company_name: work.company,\r\n                position: work.position,\r\n                department: work.department || '',\r\n                start_time: work.startDate,\r\n                end_time: work.endDate,\r\n                description: work.description || ''\r\n              }, resolve);\r\n            });\r\n          });\r\n          \r\n          // 等待所有保存完成\r\n          Promise.all([...eduPromises, ...workPromises]).then(() => {\r\n            app.showLoading(false);\r\n            app.error('保存成功');\r\n            setTimeout(function () {\r\n              app.goto(app.globalData.indexurl);\r\n            }, 1000);\r\n          });\r\n        } else {\r\n          app.showLoading(false);\r\n          app.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n    chooseAvatar() {\r\n      var that = this;\r\n      app.chooseImage(function(urls) {\r\n        if(urls && urls.length > 0) {\r\n          that.formData.avatar = urls[0];\r\n        }\r\n      }, 1)\r\n    },\r\n    removeAvatar() {\r\n      this.formData.avatar = '';\r\n    },\r\n    previewImage(e) {\r\n      var url = e.currentTarget.dataset.url;\r\n      if(url) {\r\n        uni.previewImage({\r\n          urls: [url]\r\n        });\r\n      }\r\n    },\r\n    validateEmail(email) {\r\n      const reg = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      return reg.test(email);\r\n    },\r\n    // 获取简历数据\r\n    getResumeData() {\r\n      var that = this;\r\n      that.loading = true;\r\n      app.get('ApiZhaopin/getResumeDetail', {}, function (res) {\r\n        that.loading = false;\r\n        if (res.status == 1) {\r\n          // 设置表单数据\r\n          if(res.data) {\r\n            // 处理基本信息\r\n            that.formData = {\r\n              name: res.data.name || '',\r\n              phone: res.data.phone || '',\r\n              gender: res.data.gender == 1 ? '男' : '女',\r\n              birthday: res.data.birthday || '',\r\n              avatar: res.data.avatar || '',\r\n              email: res.data.email || '',\r\n              address: res.data.address || '',\r\n              nation: res.data.nation || '',\r\n              age: res.data.age || '',\r\n              culture: res.data.culture || '',\r\n              education: [], // 初始化为空数组\r\n              workExperience: [], // 初始化为空数组\r\n              expectedPosition: res.data.expect_position || '',\r\n              expectedSalary: res.data.expect_salary || '',\r\n              expectedCity: res.data.expect_city || '',\r\n              additionalInfo: res.data.self_evaluation || '',\r\n              arrivalTime: res.data.arrival_time || '',\r\n              jobType: '全职'\r\n            };\r\n\r\n            // 处理教育经历 - 直接赋值而不是追加\r\n            that.formData.education = (res.data.education_list || []).map(item => ({\r\n              id: item.id || 0,\r\n              school: item.school_name,\r\n              major: item.major,\r\n              level: item.education,\r\n              startDate: item.start_time,\r\n              endDate: item.end_time,\r\n              description: item.description\r\n            }));\r\n\r\n            // 处理工作经历 - 直接赋值而不是追加\r\n            that.formData.workExperience = (res.data.work_list || []).map(item => ({\r\n              id: item.id || 0,\r\n              company: item.company_name,\r\n              position: item.position,\r\n              department: item.department,\r\n              startDate: item.start_time,\r\n              endDate: item.end_time,\r\n              description: item.description\r\n            }));\r\n          }\r\n          that.loaded();\r\n        }\r\n      });\r\n    },\r\n    // 加载完成\r\n    loaded() {\r\n      this.isload = true;\r\n    },\r\n    onNationChange(e) {\r\n      this.formData.nation = this.nationOptions[e.detail.value]\r\n    },\r\n    onCultureChange(e) {\r\n      this.formData.culture = this.cultureOptions[e.detail.value]\r\n    }\r\n  },\r\n  watch: {\r\n    'formData.email'(newVal) {\r\n      if (newVal && !this.validateEmail(newVal)) {\r\n        uni.showToast({\r\n          title: '请输入正确的邮箱格式',\r\n          icon: 'none'\r\n        });\r\n      } \r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resume-page {\r\n  min-height: 100vh;\r\n  background-color: #f7f8fa;\r\n  \r\n  .header {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    padding: 40rpx 30rpx 20rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);\r\n    \r\n    .title {\r\n      font-size: 40rpx;\r\n      font-weight: 600;\r\n      color: #ffffff;\r\n      letter-spacing: 1px;\r\n    }\r\n    \r\n    .subtitle {\r\n      font-size: 28rpx;\r\n      color: rgba(255, 255, 255, 0.95);\r\n      margin-top: 12rpx;\r\n      margin-bottom: 36rpx;\r\n      letter-spacing: 0.5px;\r\n    }\r\n    \r\n    .step-nav {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      background: rgba(255, 255, 255, 0.12);\r\n      border-radius: 16rpx;\r\n      padding: 24rpx 20rpx;\r\n      backdrop-filter: blur(10px);\r\n      \r\n      .step-item {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        position: relative;\r\n        \r\n        &:not(:last-child)::after {\r\n          content: '';\r\n          position: absolute;\r\n          right: -50%;\r\n          top: 20rpx;\r\n          width: 100%;\r\n          height: 2rpx;\r\n          background-color: rgba(255, 255, 255, 0.25);\r\n          z-index: 0;\r\n        }\r\n        \r\n        .step-number {\r\n          width: 44rpx;\r\n          height: 44rpx;\r\n          background-color: rgba(255, 255, 255, 0.25);\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 26rpx;\r\n          color: #ffffff;\r\n          margin-bottom: 12rpx;\r\n          position: relative;\r\n          z-index: 1;\r\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .step-text {\r\n          font-size: 24rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n          letter-spacing: 0.5px;\r\n        }\r\n        \r\n        &.active {\r\n          .step-number {\r\n            background-color: #ffffff;\r\n            transform: scale(1.15);\r\n            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n          }\r\n          \r\n          .step-text {\r\n            color: #ffffff;\r\n            font-weight: 500;\r\n            transform: scale(1.05);\r\n          }\r\n        }\r\n        \r\n        &.completed {\r\n          .step-number {\r\n            background-color: #ffffff;\r\n            \r\n            &::after {\r\n              content: '✓';\r\n              position: absolute;\r\n              left: 50%;\r\n              top: 50%;\r\n              transform: translate(-50%, -50%);\r\n              font-size: 24rpx;\r\n            }\r\n          }\r\n          \r\n          &::after {\r\n            background-color: #ffffff;\r\n          }\r\n          \r\n          .step-text {\r\n            color: rgba(255, 255, 255, 0.9);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .content {\r\n    position: fixed;\r\n    top: 240rpx;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 120rpx;\r\n    overflow: hidden;\r\n    \r\n    .step-content {\r\n      height: 100%;\r\n      padding: 24rpx;\r\n      overflow-y: auto;\r\n      -webkit-overflow-scrolling: touch;\r\n      \r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .section {\r\n    background-color: #ffffff;\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n    transition: transform 0.3s ease;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n    \r\n    &:active {\r\n      transform: scale(0.995);\r\n    }\r\n    \r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24rpx;\r\n      \r\n      .section-title {\r\n        font-size: 34rpx;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        position: relative;\r\n        padding-left: 24rpx;\r\n        letter-spacing: 0.5px;\r\n        \r\n        .title-bar {\r\n          position: absolute;\r\n          left: 0;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n          width: 6rpx;\r\n          height: 32rpx;\r\n          border-radius: 3rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .form-group {\r\n    .input-wrapper {\r\n      margin-bottom: 24rpx;\r\n      \r\n      &:last-child {\r\n        margin-bottom: 16rpx;\r\n      }\r\n      \r\n      .input-label {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #2c3e50;\r\n        margin-bottom: 12rpx;\r\n        font-weight: 500;\r\n        \r\n        &.required::before {\r\n          content: '*';\r\n          color: #ff4d4f;\r\n          margin-right: 6rpx;\r\n          font-weight: normal;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .input-item, .picker-item, .textarea-item {\r\n      width: 100%;\r\n      background-color: #f7f8fa;\r\n      border-radius: 12rpx;\r\n      font-size: 28rpx;\r\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n      border: 2rpx solid transparent;\r\n      color: #2c3e50;\r\n      \r\n      &:focus {\r\n        background-color: #ffffff;\r\n        border-color: #1890ff;\r\n        box-shadow: 0 0 0 3rpx rgba(24, 144, 255, 0.1);\r\n      }\r\n      \r\n      &::placeholder {\r\n        color: #a0aec0;\r\n      }\r\n    }\r\n    \r\n    .input-item, .picker-item {\r\n      height: 92rpx;\r\n      line-height: 92rpx;\r\n      padding: 0 24rpx;\r\n    }\r\n    \r\n    .textarea-item {\r\n      height: 200rpx;\r\n      padding: 24rpx;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n  \r\n  .footer {\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    background-color: #ffffff;\r\n    padding: 16rpx 32rpx;\r\n    padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\r\n    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n    \r\n    .btn-group {\r\n      display: flex;\r\n      gap: 24rpx;\r\n      \r\n      .nav-btn {\r\n        flex: 1;\r\n        height: 88rpx;\r\n        line-height: 88rpx;\r\n        font-size: 32rpx;\r\n        border-radius: 44rpx;\r\n        border: none;\r\n        position: relative;\r\n        overflow: hidden;\r\n        font-weight: 500;\r\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        \r\n        &.prev-btn {\r\n          background-color: #f7f8fa;\r\n          color: #64748b;\r\n          \r\n          &:active {\r\n            background-color: #e2e8f0;\r\n            transform: scale(0.98);\r\n          }\r\n        }\r\n        \r\n        &.next-btn {\r\n          background: linear-gradient(135deg, #1890ff, #36b4ff);\r\n          color: #ffffff;\r\n          \r\n          &:active {\r\n            transform: scale(0.98);\r\n          }\r\n          \r\n          &:disabled {\r\n            opacity: 0.7;\r\n            background: #94a3b8;\r\n            transform: none;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .submit-btn {\r\n        flex: 1;\r\n        height: 88rpx;\r\n        line-height: 88rpx;\r\n        background: linear-gradient(135deg, #1890ff, #36b4ff);\r\n        color: #ffffff;\r\n        font-size: 32rpx;\r\n        border-radius: 44rpx;\r\n        border: none;\r\n        position: relative;\r\n        overflow: hidden;\r\n        font-weight: 500;\r\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        \r\n        &:active {\r\n          transform: scale(0.98);\r\n        }\r\n        \r\n        &[disabled] {\r\n          opacity: 0.7;\r\n          background: #94a3b8;\r\n          transform: none;\r\n        }\r\n        \r\n        .btn-text {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.job-intention {\r\n  .form-group {\r\n    .salary-input {\r\n      position: relative;\r\n      \r\n      .input-item {\r\n        padding-right: 120rpx;\r\n      }\r\n      \r\n      .salary-unit {\r\n        position: absolute;\r\n        right: 24rpx;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        color: #64748b;\r\n        font-size: 26rpx;\r\n      }\r\n    }\r\n    \r\n    .job-type-group {\r\n      display: flex;\r\n      gap: 24rpx;\r\n      margin-bottom: 32rpx;\r\n      \r\n      .job-type-item {\r\n        flex: 1;\r\n        height: 84rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background-color: #f7f8fa;\r\n        border-radius: 12rpx;\r\n        font-size: 28rpx;\r\n        color: #64748b;\r\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        position: relative;\r\n        z-index: 1;\r\n        \r\n        &.active {\r\n          background: rgba(24, 144, 255, 0.1);\r\n          color: #1890ff;\r\n          font-weight: 500;\r\n          \r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            left: 20%;\r\n            right: 20%;\r\n            bottom: 0;\r\n            height: 4rpx;\r\n            background: linear-gradient(to right, #1890ff, #36b4ff);\r\n            border-radius: 2rpx;\r\n          }\r\n        }\r\n        \r\n        &:active {\r\n          transform: scale(0.98);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.basic-info {\r\n  .avatar-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 44rpx;\r\n    \r\n    .avatar {\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 90rpx;\r\n      background-color: #f7f8fa;\r\n      margin-bottom: 16rpx;\r\n      border: 4rpx solid #ffffff;\r\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n      transition: all 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.95);\r\n      }\r\n    }\r\n    \r\n    .upload-text {\r\n      font-size: 26rpx;\r\n      color: #64748b;\r\n      letter-spacing: 0.5px;\r\n    }\r\n  }\r\n  \r\n  .form-group {\r\n    .input-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      gap: 24rpx;\r\n      margin-bottom: 32rpx;\r\n      \r\n      .input-wrapper {\r\n        flex: 1;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.education, .work-exp {\r\n  .empty-tip {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60rpx 0;\r\n    color: #64748b;\r\n    font-size: 28rpx;\r\n    \r\n    .empty-icon {\r\n      width: 220rpx;\r\n      height: 220rpx;\r\n      margin-bottom: 24rpx;\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n  \r\n  .add-btn {\r\n    font-size: 28rpx;\r\n    color: #1890ff;\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: rgba(24, 144, 255, 0.1);\r\n    padding: 14rpx 28rpx;\r\n    border-radius: 32rpx;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n    \r\n    &::before {\r\n      content: '+';\r\n      margin-right: 8rpx;\r\n      font-size: 32rpx;\r\n      font-weight: normal;\r\n    }\r\n    \r\n    &:active {\r\n      transform: scale(0.95);\r\n      background-color: rgba(24, 144, 255, 0.15);\r\n    }\r\n  }\r\n  \r\n  .edu-item, .work-item {\r\n    background-color: #ffffff;\r\n    border-radius: 16rpx;\r\n    padding: 32rpx;\r\n    margin-bottom: 24rpx;\r\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n    border: 2rpx solid #f0f0f0;\r\n    transition: all 0.3s ease;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n    \r\n    &:active {\r\n      transform: scale(0.995);\r\n    }\r\n    \r\n    .item-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24rpx;\r\n      \r\n      .item-index {\r\n        width: 44rpx;\r\n        height: 44rpx;\r\n        background: linear-gradient(135deg, #1890ff, #36b4ff);\r\n        border-radius: 22rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #ffffff;\r\n        font-size: 24rpx;\r\n        font-weight: 600;\r\n        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);\r\n      }\r\n      \r\n      .delete-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        color: #ff4d4f;\r\n        font-size: 26rpx;\r\n        padding: 8rpx 16rpx;\r\n        border-radius: 24rpx;\r\n        background-color: rgba(255, 77, 79, 0.1);\r\n        transition: all 0.3s ease;\r\n        \r\n        .delete-icon {\r\n          font-size: 32rpx;\r\n          margin-right: 4rpx;\r\n        }\r\n        \r\n        &:active {\r\n          transform: scale(0.95);\r\n          background-color: rgba(255, 77, 79, 0.15);\r\n        }\r\n      }\r\n    }\r\n    \r\n    .form-group {\r\n      .date-range {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 24rpx;\r\n        margin-bottom: 24rpx;\r\n        \r\n        .date-item {\r\n          flex: 1;\r\n        }\r\n        \r\n        .date-separator {\r\n          width: 44rpx;\r\n          height: 92rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-top: 40rpx;\r\n          \r\n          &::after {\r\n            content: '';\r\n            width: 24rpx;\r\n            height: 2rpx;\r\n            background-color: #94a3b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.layui-imgbox {\r\n  margin-right: 16rpx;\r\n  margin-bottom: 10rpx;\r\n  font-size: 24rpx;\r\n  position: relative;\r\n  \r\n  .layui-imgbox-close {\r\n    position: absolute;\r\n    display: block;\r\n    width: 32rpx;\r\n    height: 32rpx;\r\n    right: -16rpx;\r\n    top: -16rpx;\r\n    z-index: 90;\r\n    color: #999;\r\n    font-size: 32rpx;\r\n    background: #fff;\r\n    border-radius: 50%;\r\n    \r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  \r\n  .layui-imgbox-img {\r\n    display: block;\r\n    width: 180rpx;\r\n    height: 180rpx;\r\n    padding: 2px;\r\n    border: #d3d3d3 1px solid;\r\n    background-color: #f6f6f6;\r\n    overflow: hidden;\r\n    border-radius: 50%;\r\n    \r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 50%;\r\n    }\r\n  }\r\n}\r\n\r\n.uploadbtn {\r\n  position: relative;\r\n  height: 180rpx;\r\n  width: 180rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx dashed #d9d9d9;\r\n  \r\n  &:active {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./resume.vue?vue&type=style&index=0&id=e893a35e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./resume.vue?vue&type=style&index=0&id=e893a35e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115048525\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}