{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?de64", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?94e0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?b760", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?b548", "uni-app:///pagesExt/cycle/prolist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?31d8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/prolist.vue?dc02"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nomore", "nodata", "keyword", "pagenum", "datalist", "history_list", "history_show", "order", "field", "oldcid", "<PERSON>ecid", "catchegid", "cid", "gid", "cid2", "oldcid2", "catchecid2", "clist", "clist2", "glist", "paramlist", "catcheparams", "proparams", "productlisttype", "showfilter", "cpid", "bid", "latitude", "longitude", "test", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "getprolist", "paramClick", "console", "showDrawer", "closeDrawer", "change", "searchChange", "searchbtn", "searchConfirm", "searchproduct", "sortClick", "groupClick", "cateClick", "cate2Click", "filterConfirm", "filterReset", "filterClick", "addHistory", "historylist", "newhistorylist", "historyClick", "deleteSearchHistory"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,uNAEN;AACP,KAAK;AACL;AACA,aAAa,+OAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqGhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;MACAA;MACAC;QAAA1B;QAAAC;QAAAa;QAAAZ;MAAA;QACAuB;QACAA;QACAA;QACAA;QACAA;QACA;UACAC;YACAD;YACAA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;MAEA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAF;MACAA;MACAA;MACAA;MACA;MACAC;QAAAnC;QAAAD;QAAAM;QAAAD;QAAAM;QAAAD;QAAAE;QAAAW;QAAAC;QAAAC;QAAAC;QAAAN;MAAA;QACAe;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEAG;MACAC;MACA;MACA;MACA;MACA;MACAA;IACA;IACA;IACAC;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACAH;MACA;IACA;IACAI;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAT;MACA;QACA;UACAA;UACAC;QACA;UACAD;UACAC;QACA;MACA;IACA;IACAS;MACA;MACA;MACAV;MACAA;IACA;IACAW;MACA;MACAX;MACAA;MACAA;MACAA;IACA;IACAY;MACA;MACA;MACAZ;MACAA;MACAA;IACA;IACAa;MACA;MACA;MACA;MACAb;IACA;IACAc;MACA;MACA;MACA;MACAd;IACA;IACAe;MACA;MACA;MACA;MACAf;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACApB;MACAD;IACA;IACAsB;MACA;MACA;MACA;MACAtB;MACAA;IACA;IACAuB;MACA;MACAvB;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9WA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/prolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/prolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolist.vue?vue&type=template&id=11ce14de&\"\nvar renderjs\nimport script from \"./prolist.vue?vue&type=script&lang=js&\"\nexport * from \"./prolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/prolist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=template&id=11ce14de&\"", "var components\ntry {\n  components = {\n    uniDrawer: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-drawer/uni-drawer\" */ \"@/components/uni-drawer/uni-drawer.vue\"\n      )\n    },\n    dpCycleItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cycle-item/dp-cycle-item\" */ \"@/components/dp-cycle-item/dp-cycle-item.vue\"\n      )\n    },\n    dpCycleItemlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cycle-itemlist/dp-cycle-itemlist\" */ \"@/components/dp-cycle-itemlist/dp-cycle-itemlist.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? !_vm.history_list || _vm.history_list.length == 0 : null\n  var m0 =\n    _vm.isload && (!_vm.field || _vm.field == \"sort\") ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.field == \"sales\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.field == \"sell_price\" ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l0 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 = _vm.catchecid == item.id ? _vm.t(\"color1\") : null\n          var m8 = _vm.catchecid == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var m9 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l1 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist2, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.catchecid2 == item.id ? _vm.t(\"color1\") : null\n          var m12 = _vm.catchecid2 == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n          }\n        })\n      : null\n  var l3 = _vm.isload\n    ? _vm.__map(_vm.paramlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m13 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1\") : null\n        var m14 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1rgb\") : null\n        var l2 = _vm.__map(item.params, function (item2, index) {\n          var $orig = _vm.__get_orig(item2)\n          var m15 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1\") : null\n          var m16 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n          }\n        })\n        return {\n          $orig: $orig,\n          m13: m13,\n          m14: m14,\n          l2: l2,\n        }\n      })\n    : null\n  var m17 = _vm.isload ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l0: l0,\n        m9: m9,\n        m10: m10,\n        l1: l1,\n        l3: l3,\n        m17: m17,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"search-container\" :style=\"history_show?'height:100%;':''\">\n\t\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的商品\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-btn\" @tap=\"searchbtn\">\n\t\t\t\t\t<image src=\"/static/img/show-cascades.png\" style=\"height:36rpx;width:36rpx\" v-if=\"!history_show && productlisttype=='itemlist'\"/>\n\t\t\t\t\t<image src=\"/static/img/show-list.png\" style=\"height:36rpx;width:36rpx\" v-if=\"!history_show && productlisttype=='item2'\"/>\n\t\t\t\t\t<text v-if=\"history_show\">搜索</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-history\" v-show=\"history_show\">\n\t\t\t\t<view>\n\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\n\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\n\t\t\t\t\t\t<image src=\"/static/img/del.png\" style=\"width:36rpx;height:36rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-history-list\">\n\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\" :data-value=\"item\" @tap=\"historyClick\">{{item}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\" class=\"flex-y-center\"><image src=\"/static/img/tanhao.png\" style=\"width:36rpx;height:36rpx;margin-right:10rpx\"/>暂无记录\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-navbar\" v-show=\"!history_show\">\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"(!field||field=='sort')?'color:'+t('color1'):''\" data-field=\"sort\" data-order=\"desc\">综合</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"field=='sales'?'color:'+t('color1'):''\" data-field=\"sales\" data-order=\"desc\">销量</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" data-field=\"sell_price\" :data-order=\"order=='asc'?'desc':'asc'\">\n\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\n\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\" @click.stop=\"showDrawer('showRight')\">筛选 <text :class=\"'iconfont iconshaixuan ' + (showfilter?'active':'')\"></text></view>\n\t\t\t</view>\n\t\t\t<uni-drawer ref=\"showRight\" mode=\"right\" @change=\"change($event,'showRight')\" :width=\"280\">\n\t\t\t\t<view class=\"filter-scroll-view\" style=\"max-height:100%;padding-bottom:110rpx;overflow:hidden auto\">\n\t\t\t\t\t<view class=\"filter-scroll-view-box\">\n\t\t\t\t\t\t<view class=\"search-filter\">\n\t\t\t\t\t\t\t<view class=\"filter-title\">筛选</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<block v-if=\"!bid || bid <=0\">\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"oldcid\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"item.id\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==oldcid2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"oldcid2\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist2\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"item.id\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-for=\"(item, index) in paramlist\">\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">{{item.name}}</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catcheparams[item.name]==''?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"paramClick\" :data-paramkey=\"item.name\" data-paramval=\"\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item2, index) in item.params\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catcheparams[item.name]==item2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"paramClick\" :data-paramkey=\"item.name\" :data-paramval=\"item2\">{{item2}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\n\t\t\t\t\t\t\t<view class=\"search-filter-btn\">\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"filterReset\">重置</view>\n\t\t\t\t\t\t\t\t<view class=\"btn2\" :style=\"{background:t('color1')}\" @tap=\"filterConfirm\">确定</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</uni-drawer>\n\n\t\t\t\n\t\t</view>\n\t\t<view class=\"product-container\">\n\t\t\t<block v-if=\"datalist && datalist.length>0\">\n\t\t\t\t<dp-cycle-item v-if=\"productlisttype=='item2'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-cycle-item>\n\t\t\t\t<dp-cycle-itemlist v-if=\"productlisttype=='itemlist'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-cycle-itemlist>\n\t\t\t</block>\n\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\n\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\n\t\t\t<loading v-if=\"loading\"></loading>\n\t\t</view>\n\t</block>\n\t<view style=\"display:none\">{{test}}</view>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n\t\t\tnomore:false,\n\t\t\tnodata:false,\n      keyword: '',\n      pagenum: 1,\n      datalist: [],\n      history_list: [],\n      history_show: false,\n      order: '',\n\t\t\tfield:'',\n      oldcid: \"\",\n      catchecid: \"\",\n      catchegid: \"\",\n      cid: \"\",\n      gid: '',\n\t\t\tcid2:'',\n      oldcid2: \"\",\n      catchecid2: \"\",\n      clist: [],\n      clist2: [],\n      glist: [],\n\t\t\tparamlist:[],\n\t\t\tcatcheparams:{},\n\t\t\tproparams:{},\n      productlisttype: 'item2',\n      showfilter: \"\",\n\t\t\tcpid:0,\n\t\t\tbid:0,\n\t\t\tlatitude: '',\n\t\t\tlongitude: '',\n\t\t\ttest:'',\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.oldcid = this.opt.cid || '';\n\t\tthis.catchecid = this.opt.cid;\n\t\tthis.cid = this.opt.cid;\n\t\tthis.cid2 = this.opt.cid2 || '';\n\t\tthis.oldcid2 = this.opt.cid2 || '';\n\t\tthis.catchecid2 = this.opt.cid2;\n\t\tthis.gid = this.opt.gid;\n\t\tthis.cpid = this.opt.cpid || 0;\n\t\tthis.bid = this.opt.bid ? this.opt.bid : 0;\n\t\t//console.log(this.bid);\n\t\tif(this.cpid > 0){\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: '可用商品列表'\n\t\t\t});\n\t\t}\n    var productlisttype = app.getCache('productlisttype');\n    if (productlisttype) this.productlisttype = productlisttype;\n\t\tthis.history_list = app.getCache('search_history_list');\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getprolist();\n    }\n  },\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.pagenum = 1;\n\t\t\tthat.datalist = [];\n\t\t\tvar cid = that.opt.cid;\n\t\t\tvar gid = that.opt.gid;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tvar cid2 = that.cid2;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiCycle/prolist', {cid: cid,gid: gid,bid:bid,cid2:cid2}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t  that.clist = res.clist;\n\t\t\t  that.clist2 = res.clist2;\n\t\t\t  that.glist = res.glist;\n\t\t\t\tthat.loaded();\n\t\t\t\tif (that.latitude == '' && that.longitude == '' && res.needlocation) {\n\t\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\t\tthat.latitude = res.latitude;\n\t\t\t\t\t\tthat.longitude = res.longitude;\n\t\t\t\t\t\tthat.getprolist();\n\t\t\t\t\t},function(){\n\t\t\t\t\t\tthat.getprolist();\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tthat.getprolist();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t});\n\t\t},\n    getprolist: function () {\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n      var order = that.order;\n      var field = that.field;\n      var gid = that.gid;\n      var cid = that.cid;\n\t\t\tvar cid2 = that.cid2;\n      var cpid = that.cpid;\n      that.history_show = false;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n      app.post('ApiCycle/getprolist',{pagenum: pagenum,keyword: keyword,field: field,order: order,gid: gid,cid: cid,cid2:cid2,cpid:cpid,bid:bid,latitude: that.latitude,longitude: that.longitude,proparams:that.proparams}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n\t\t\n\t\tparamClick:function(e){\n\t\t\tconsole.log(e)\n\t\t\tvar paramkey = e.currentTarget.dataset.paramkey;\n\t\t\tvar paramval = e.currentTarget.dataset.paramval;\n\t\t\tthis.catcheparams[paramkey] = paramval;\n\t\t\tthis.test = Math.random();\n\t\t\tconsole.log(this.catcheparams);\n\t\t},\n\t\t// 打开窗口\n\t\tshowDrawer(e) {\n\t\t\tconsole.log(e)\n\t\t\tthis.$refs[e].open()\n\t\t},\n\t\t// 关闭窗口\n\t\tcloseDrawer(e) {\n\t\t\tthis.$refs[e].close()\n\t\t},\n\t\t// 抽屉状态发生变化触发\n\t\tchange(e, type) {\n\t\t\tconsole.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));\n\t\t\tthis[type] = e\n\t\t},\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n      if (e.detail.value == '') {\n        this.history_show = true;\n        this.datalist = [];\n      }\n    },\n    searchbtn: function () {\n      var that = this;\n      if (that.history_show) {\n        var keyword = that.keyword;\n        that.searchproduct();\n      } else {\n        if (that.productlisttype == 'itemlist') {\n          that.productlisttype = 'item2';\n          app.setCache('productlisttype', 'item2');\n        } else {\n          that.productlisttype = 'itemlist';\n          app.setCache('productlisttype', 'itemlist');\n        }\n      }\n    },\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.searchproduct();\n    },\n    searchproduct: function () {\n      var that = this;\n      that.pagenum = 1;\n      that.datalist = [];\n      that.addHistory();\n      that.getprolist();\n    },\n    sortClick: function (e) {\n      var that = this;\n      var t = e.currentTarget.dataset;\n      that.field = t.field;\n      that.order = t.order;\n      that.searchproduct();\n    },\n    groupClick: function (e) {\n      var that = this;\n      var gid = e.currentTarget.dataset.gid;\n\t\t\tif(gid === true) gid = '';\n      that.catchegid = gid\n    },\n    cateClick: function (e) {\n      var that = this;\n      var cid = e.currentTarget.dataset.cid;\n\t\t\tif(cid === true) cid = '';\n      that.catchecid = cid;\n    },\n    cate2Click: function (e) {\n      var that = this;\n      var cid2 = e.currentTarget.dataset.cid2;\n\t\t\tif(cid2 === true) cid2 = '';\n      that.catchecid2 = cid2\n    },\n\t\tfilterConfirm(){\n\t\t\tthis.cid = this.catchecid;\n\t\t\tthis.cid2 = this.catchecid2;\n\t\t\tthis.gid = this.catchegid;\n\t\t\tthis.proparams = this.catcheparams;\n\t\t\tthis.searchproduct();\n\t\t\tthis.$refs['showRight'].close()\n\t\t},\n\t\tfilterReset(){\n\t\t\tthis.catchecid = this.oldcid;\n\t\t\tthis.catchecid2 = this.oldcid2;\n\t\t\tthis.catchegid = '';\n\t\t},\n    filterClick: function () {\n      this.showfilter = !this.showfilter\n    },\n    addHistory: function () {\n      var that = this;\n      var keyword = that.keyword;\n      if (app.isNull(keyword)) return;\n      var historylist = app.getCache('search_history_list');\n      if (app.isNull(historylist)) historylist = [];\n      historylist.unshift(keyword);\n      var newhistorylist = [];\n      for (var i in historylist) {\n        if (historylist[i] != keyword || i == 0) {\n          newhistorylist.push(historylist[i]);\n        }\n      }\n      if (newhistorylist.length > 5) newhistorylist.splice(5, 1);\n      app.setCache('search_history_list', newhistorylist);\n      that.history_list = newhistorylist\n    },\n    historyClick: function (e){\n      var that = this;\n      var keyword = e.currentTarget.dataset.value;\n      if (keyword.length == 0) return;\n      that.keyword = keyword;\n      that.searchproduct();\n    },\n    deleteSearchHistory: function () {\n      var that = this;\n      that.history_list = null;\n      app.removeCache(\"search_history_list\");\n    }\n  }\n};\n</script>\n<style>\n.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}\n.topsearch{width:100%;padding:16rpx 20rpx;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}\n.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}\n.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}\n\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\n.search-history {padding: 24rpx 34rpx;}\n.search-history .search-history-title {color: #666;}\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\n.search-history-list {padding: 24rpx 0 0 0;}\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\n\n.filter-scroll-view{margin-top:var(--window-top)}\n.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}\n.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}\n.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}\n.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}\n.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}\n.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}\n.search-filter button .icon{margin-top:6rpx;height:54rpx;}\n.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}\n.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}\n.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}\n\n.product-container {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}\r\n\r\n\r\n\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115097366\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}