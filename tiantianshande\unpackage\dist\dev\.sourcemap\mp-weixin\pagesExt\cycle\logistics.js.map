{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?7ff0", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?95f9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?d5c5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?6124", "uni-app:///pagesExt/cycle/logistics.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?cc95", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cycle/logistics.vue?164c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nodata", "express_com", "express_no", "type", "datalist", "orderinfo", "prolist", "binfo", "psuser", "psorder", "onLoad", "onPullDownRefresh", "getdata", "that", "app", "setTimeout", "call", "uni", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2NlxB;AAAA;EAGAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;AAAA,6EACA;EACA;AACA,oEACA;EACAC;IACA;IACAC;IACAA;IACAA;IACAA;IACAC;MAAAb;MAAAC;MAAAC;IAAA;MACAU;MACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAE;UACAF;QACA;MACA;QACA;QACA;UACAA;QACA;QACAA;MACA;MACAA;IACA;EACA;EACAG;IACA;IACAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChSA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cycle/logistics.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cycle/logistics.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./logistics.vue?vue&type=template&id=6f6e1934&\"\nvar renderjs\nimport script from \"./logistics.vue?vue&type=script&lang=js&\"\nexport * from \"./logistics.vue?vue&type=script&lang=js&\"\nimport style0 from \"./logistics.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cycle/logistics.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=template&id=6f6e1934&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.express_com == \"同城配送\" &&\n    _vm.psorder.status != 0 &&\n    _vm.psorder.starttime\n      ? _vm.dateFormat(_vm.psorder.starttime)\n      : null\n  var m1 =\n    _vm.isload &&\n    _vm.express_com == \"同城配送\" &&\n    _vm.psorder.status != 0 &&\n    _vm.psorder.daodiantime\n      ? _vm.dateFormat(_vm.psorder.daodiantime)\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.express_com == \"同城配送\" &&\n    _vm.psorder.status != 0 &&\n    _vm.psorder.quhuotime\n      ? _vm.dateFormat(_vm.psorder.quhuotime)\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.express_com == \"同城配送\" &&\n    _vm.psorder.status != 0 &&\n    _vm.psorder.endtime\n      ? _vm.dateFormat(_vm.psorder.endtime)\n      : null\n  var m4 =\n    _vm.isload && _vm.express_com == \"同城配送\"\n      ? _vm.dateFormat(_vm.orderinfo.createtime)\n      : null\n  var m5 =\n    _vm.isload && _vm.express_com == \"同城配送\"\n      ? _vm.dateFormat(_vm.orderinfo.paytime)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t<block v-if=\"express_com=='同城配送'\">\r\n\t\t<map v-if=\"psorder.status!=0 && psorder.status!=4\" class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_business.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:psuser.latitude,\r\n\t\t\tlongitude:psuser.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_qishou.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\t\t<map v-else class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_business.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\r\n\t\t<view class=\"order-box\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<block v-if=\"type == 'express_wx'\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.order_status==101\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>分配骑手</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.order_status==102\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>骑手赶往店家</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.order_status==201\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>骑手到店</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.order_status==202\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>取货成功</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.order_status==301\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>配送中</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.order_status==302\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已送达</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>配送中</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==4\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已送达</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.leftminute>0\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/><text class=\"t1\">{{psorder.leftminute}}分钟内</text> 送达</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.yujitime>0\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已超时<text class=\"t1\" style=\"margin-left:10rpx\">{{-psorder.leftminute}}分钟</text></view>\r\n\t\t\t\t\t<view class=\"f1\" v-else><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>配送中</view>\r\n\t\t\t\t</block>\r\n\t\t\t\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view class=\"f2\"><text class=\"t1\">{{orderinfo.freight_price}}</text>元</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" style=\"border-bottom:0\">\r\n\t\t\t\t<block v-if=\"type == 'express_wx'\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"psuser.latitude\">\r\n\t\t\t\t\t\t<view class=\"t1\"><text class=\"x1\">{{psorder.juli}}</text><text class=\"x2\">{{psorder.juli_unit}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\r\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">{{psorder.juli2}}</text><text class=\"x2\">{{psorder.juli2_unit}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<view class=\"t1\"><text class=\"x1\">{{psorder.juli}}</text><text class=\"x2\">{{psorder.juli_unit}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\r\n\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">{{psorder.juli2}}</text><text class=\"x2\">{{psorder.juli2_unit}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">{{binfo.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{binfo.address}}</view>\r\n\t\t\t\t\t<view class=\"t3\">{{orderinfo.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">商品清单({{orderinfo.procount}})</view>\r\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"item\">\r\n\t\t\t\t<text class=\"t1 flex1\">{{item.name}} {{item.ggname}}</text>\r\n\t\t\t\t<text class=\"t2 flex0\">￥{{item.sell_price}} ×{{item.num}} </text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"psorder.status!=0\">\r\n\t\t\t<view class=\"box-title\">配送信息</view>\r\n\t\t\t<view class=\"item\" v-if=\"psuser.realname\">\r\n\t\t\t\t<text class=\"t1\">配送员</text>\r\n\t\t\t\t<text class=\"t2\"><text style=\"font-weight:bold\">{{psuser.realname}}</text>({{psuser.tel}})</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.starttime\">\r\n\t\t\t\t<text class=\"t1\">接单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.starttime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.daodiantime\">\r\n\t\t\t\t<text class=\"t1\">到店时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.daodiantime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.quhuotime\">\r\n\t\t\t\t<text class=\"t1\">取货时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.quhuotime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.endtime\">\r\n\t\t\t\t<text class=\"t1\">送达时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.endtime)}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">订单信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{orderinfo.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.createtime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.paytime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t<text class=\"t2\">{{orderinfo.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">商品金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">备注</text>\r\n\t\t\t\t<text class=\"t2 red\">{{orderinfo.message ? orderinfo.message : '无'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<view class=\"f1\" v-if=\"psorder.status!=0 && psuser.tel\" @tap=\"call\" :data-tel=\"psuser.tel\"><image src=\"/static/peisong/tel1.png\" class=\"img\"/>联系配送员</view>\r\n\t\t\t<view class=\"f2\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"binfo.tel\"><image src=\"/static/peisong/tel2.png\" class=\"img\"/>联系商家</view>\r\n\t\t\t<view class=\"btn1\" v-if=\"psorder.status ==4\" @tap=\"goto\" :data-url=\"'commentps?id='+psorder.id\" >评价配送员</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<block v-else-if=\"express_com=='货运托运'\">\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<block v-if=\"datalist.pic\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">物流单照片</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<image class=\"t2\" :src=\"datalist.pic\" @tap=\"previewImage\" :data-url=\"datalist.pic\" mode=\"widthFix\"/>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<view class=\"item\" v-if=\"datalist.fhname\">\r\n\t\t\t\t<text class=\"t1\">发货人信息</text>\r\n\t\t\t\t<text class=\"t2\">{{datalist.fhname}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"datalist.fhaddress\">\r\n\t\t\t\t<text class=\"t1\">发货地址</text>\r\n\t\t\t\t<text class=\"t2\">{{datalist.fhaddress}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"datalist.shname\">\r\n\t\t\t\t<text class=\"t1\">收货人信息</text>\r\n\t\t\t\t<text class=\"t2\">{{datalist.shname}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"datalist.shaddress\">\r\n\t\t\t\t<text class=\"t1\">收货地址</text>\r\n\t\t\t\t<text class=\"t2\">{{datalist.shaddress}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"datalist.remark\">\r\n\t\t\t\t<text class=\"t1\">备注</text>\r\n\t\t\t\t<text class=\"t2\">{{datalist.remark}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<block v-else>\r\n\t\t<view class=\"expressinfo\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<view class=\"f1\"><image :src=\"pre_url + '/static/img/feiji.png'\"></image></view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">快递公司：<text style=\"color:#333\">{{express_com}}</text></view>\r\n\t\t\t\t\t<view class=\"t2\">快递单号：<text style=\"color:#333\">{{express_no}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" :class=\"'item ' + (index==0?'on':'')\">\r\n\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/dot' + (index==0?'2':'1') + '.png'\"></image></view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<text class=\"t2\">{{item.time}}</text>\r\n\t\t\t\t\t\t<text class=\"t1\">{{item.context}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<nodata v-if=\"nodata\" text=\"暂未查找到物流信息\"></nodata>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tnodata:false,\r\n      express_com: '',\r\n      express_no: '',\r\n\t\t\ttype:'',\r\n      datalist: [],\r\n\r\n      orderinfo: {},\r\n      prolist: [],\r\n      binfo: {},\r\n      psuser: {},\r\n      psorder: {}\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.express_com = that.opt.express_com;\r\n\t\t\tthat.express_no = that.opt.express_no;\r\n\t\t\tthat.type = that.opt.type;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiCycle/logistics', {express_com: that.express_com,express_no: that.express_no,type:that.type}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(that.express_com == '同城配送'){\r\n\t\t\t\t\t\tthat.orderinfo = res.orderinfo;\r\n\t\t\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\t\t\tthat.binfo = res.binfo;\r\n\t\t\t\t\t\tthat.psorder = res.psorder;\r\n\t\t\t\t\t\tthat.psuser = res.psuser;\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t},10000)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar datalist = res.datalist;\r\n\t\t\t\t\tif (datalist.length < 1) {\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.datalist = datalist;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tcall:function(e){\r\n\t\t\tvar tel = e.currentTarget.dataset.tel;\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: tel\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.expressinfo .head { width:100%;background: #fff; margin:20rpx 0;padding: 20rpx 20rpx;display:flex;align-items:center}\r\n.expressinfo .head .f1{ width:120rpx;height:120rpx;margin-right:20rpx}\r\n.expressinfo .head .f1 image{width:100%;height:100%}\r\n.expressinfo .head .f2{display:flex;flex-direction:column;flex:auto;font-size:30rpx;color:#999999}\r\n.expressinfo .head .f2 .t1{margin-bottom:8rpx}\r\n.expressinfo .content{ width: 100%;  background: #fff;display:flex;flex-direction:column;color: #979797;padding:20rpx 40rpx}\r\n.expressinfo .content .on{color: #23aa5e;}\r\n.expressinfo .content .item{display:flex;width: 96%;  margin: 0 2%;border-left: 1px #dadada solid;padding:10rpx 0}\r\n.expressinfo .content .item .f1{ width:40rpx;flex-shrink:0;position:relative}\r\n.expressinfo .content image{width: 30rpx; height: 30rpx; position: absolute; left: -16rpx; top: 22rpx;}\r\n/*.content .on image{ top:-1rpx}*/\r\n.expressinfo .content .item .f1 image{ width: 30rpx; height: 30rpx;}\r\n\r\n.expressinfo .content .item .f2{display:flex;flex-direction:column;flex:auto;}\r\n.expressinfo .content .item .f2 .t1{font-size: 30rpx;}\r\n.expressinfo .content .item .f2 .t1{font-size: 26rpx;}\r\n\r\n\r\n.map{width:100%;height:500rpx;overflow:hidden}\r\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n.ordertop .f1 .t2{font-size:24rpx}\r\n\r\n.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#222222}\r\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}\r\n.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}\r\n.order-box .head .f2{color:#FF6F30}\r\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\r\n\r\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}\r\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\r\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\r\n\r\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\r\n.order-box .content .f2{}\r\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\r\n\r\n.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}\r\n.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}\r\n.orderinfo .item .t1{width:200rpx;color:#161616}\r\n.orderinfo .item .t2{flex:1;text-align:right;color:#222222}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;align-items:center;height:100rpx;}\r\n.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}\r\n.bottom .f1 .img{width:44rpx;height:44rpx}\r\n.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}\r\n.bottom .f2 .img{width:44rpx;height:44rpx}\r\n.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098041\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}