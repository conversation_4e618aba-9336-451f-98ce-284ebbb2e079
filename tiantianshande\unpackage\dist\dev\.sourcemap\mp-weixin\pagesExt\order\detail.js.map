{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?2c67", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?ca03", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?5f3c", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?c208", "uni-app:///pagesExt/order/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?0846", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/order/detail.vue?d9ca"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "fu<PERSON><PERSON><PERSON><PERSON>", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "payorder", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "pay_transfer_info", "invoice", "selectExpressShow", "express_content", "fromfenxiao", "hexiao_code_member", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "item", "total_diy_amount", "interval", "set_hexiao_code_member", "<PERSON><PERSON><PERSON>", "orderid", "setTimeout", "getdjs", "todel", "toclose", "orderCollect", "showhxqr", "closeHxqr", "openLocation", "uni", "latitude", "longitude", "name", "scale", "openMendian", "logistics", "console", "express_oglist", "hideSelectExpressDialog"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgb/wB;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;;QAEA;QACA;QACAA;UACA;YACA;YACAG;YACA;YACAC;UACA;QACA;QACA;QACAJ;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAK;YACAL;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAM;MACA;IACA;IACAC;MACA;MAEAP;MACAC;QAAAO;QAAAf;MAAA;QACAO;QACA;UACAC;UAAA;QACA;QACAA;QACAD;QACAS;UACAT;QACA;MACA;IACA;IACAU;MACA;MACA;MAEA;QACAV;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAQ;YACAR;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACAX;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAQ;YACAT;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACAZ;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAQ;YACAT;UACA;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACArB;IACA;IACAsB;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;QACAvB;MACA;QACAV;QACA;UACA;YACA;YACAiC;YACA;YACA;cACA;gBACAC;cACA;YACA;YACAlC;UACA;QACA;QACA;QACA;MACA;IACA;IACAmC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACroBA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/order/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/order/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=c6eb16b4&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/order/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=c6eb16b4&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.fujiaArr.length : null\n  var g1 = _vm.isload ? _vm.fujiaArr.length : null\n  var m0 = _vm.isload && _vm.detail.disprice > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m2 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload && _vm.detail.is_yuanbao_pay == 1 ? _vm.t(\"元宝\") : null\n  var g2 = _vm.isload ? _vm.detail.formdata.length : null\n  var m4 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 0 &&\n    _vm.detail.paytypeid != 5\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 0 &&\n    _vm.detail.paytypeid == 5 &&\n    _vm.detail.transfer_check == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 0 &&\n    _vm.detail.paytypeid == 5 &&\n    _vm.detail.transfer_check == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 0 &&\n    _vm.detail.paytypeid == 5 &&\n    _vm.detail.transfer_check == -1\n      ? _vm.t(\"color1\")\n      : null\n  var g3 =\n    _vm.isload && _vm.fromfenxiao == 0\n      ? [1, 2, 3].includes(_vm.detail.status) && _vm.invoice\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 2 &&\n    _vm.detail.paytypeid != \"4\" &&\n    (_vm.detail.balance_pay_status == 1 || _vm.detail.balance_price == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.status == 2 &&\n    _vm.detail.balance_pay_status == 0 &&\n    _vm.detail.balance_price > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload &&\n    _vm.fromfenxiao == 0 &&\n    _vm.detail.bid > 0 &&\n    _vm.detail.status == 3 &&\n    _vm.iscommentdp == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m11 = _vm.isload && _vm.detail.hexiao_code_member ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g2: g2,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g3: g3,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"ordertop\" :style=\"'background:url('+(shopset.order_detail_toppic?shopset.order_detail_toppic: pre_url + '/static/img/ordertop.png')+');background-size:100%'\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\r\n\t\t\t\t\t<view class=\"t1\">等待买家付款</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"detail.paytypeid == 5\">\r\n\t\t\t\t\t   <text v-if=\"detail.transfer_check == 1\">转账汇款后请上传付款凭证</text> \r\n\t\t\t\t\t   <text v-if=\"detail.transfer_check == 0\">转账待审核</text>\r\n\t\t\t\t\t   <text v-if=\"detail.transfer_check == -1\">转账已驳回</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\r\n\t\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">我们会尽快为您发货</view>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">请尽快前往自提地点取货</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\r\n\t\t\t\t\t<view class=\"t1\">订单已发货</view>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">发货信息：{{detail.express_com}} {{detail.express_no}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\r\n\t\t\t\t\t<view class=\"t1\">订单已完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\r\n\t\t\t\t\t<view class=\"t1\">订单已取消</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"address\">\r\n\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t<image src=\"/static/img/address3.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} {{detail.tel}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openMendian\" :data-storeinfo=\"storeinfo\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}} </text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\">\r\n\t\t\t\t<image :src=\"detail.binfo.logo\" style=\"width:36rpx;height:36rpx;\"></image>\r\n\t\t\t\t<view class=\"flex1\" decode=\"true\" space=\"true\" style=\"padding-left:16rpx\">{{detail.binfo.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product\">\r\n\t\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"box\">\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2 flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<text>{{item.ggname}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==0 && shopset.comment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">去评价</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && item.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?ogid=' + item.id\">查看评价</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.is_yh == 0\">\r\n\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\">×{{item.num}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.is_yh == 1\">\r\n\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.yh_prices}}(优惠金额)</text><text class=\"x2\">×{{item.yh_nums}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"t3 frame-price\" v-if=\"item.diy_amount && item.diy_amount != 0 && item.diy_amount != 1\">\r\n\t\t\t\t\t\t\t\t<text class=\"frame-label\">框价格:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"frame-value\">￥{{item.diy_amount}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"frame-multiply\">×</text>\r\n\t\t\t\t\t\t\t\t<text class=\"frame-num\">{{item.num}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"frame-equal\">=</text>\r\n\t\t\t\t\t\t\t\t<text class=\"frame-total\">￥{{item.diy_amount_total}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- glassinfo -->\r\n\t\t\t\t\t<view class=\"glassitem\" v-if=\"item.glassrecord\">\r\n\t\t\t\t\t\t<view class=\"gcontent\">\r\n\t\t\t\t\t\t\t<view class=\"glassheader\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.glassrecord.name}}</view>\r\n\t\t\t\t\t\t\t\t<view>{{item.glassrecord.type==1?'近视':'远视'}}-{{item.glassrecord.is_ats==1?'有散光':'无散光'}}-瞳距/{{item.glassrecord.ipd}}mm</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"glassrow bt\">\r\n\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.degress_right}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">右眼度数</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.degress_left}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">左眼度数</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.correction_right}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">矫正右眼</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.correction_left}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">矫正左眼</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.glassrecord.is_ats==1\">\r\n\t\t\t\t\t\t\t\t<view class=\"glassrow bt\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.ats_right}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">柱镜右眼</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.ats_left}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">柱镜左眼</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.ats_zright}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">轴位右眼</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.glassrecord.ats_zleft}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">轴位左眼</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- glassinfo -->\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\" v-if=\"fujiaArr.length != 0\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">组合商品</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product\" v-if=\"fujiaArr.length != 0\">\r\n\t\t\t\t<view v-for=\"(item, idx) in fujiaArr\" :key=\"idx\" class=\"box\">\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.proname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2 flex flex-y-center flex-bt\">\r\n\t\t\t\t\t\t\t\t<text>{{item.ggname}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">×{{item.num}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\r\n\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\r\n\t\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">订单提货码</text>\r\n\t\t\t\t\t<text class=\"t2 pickup-code\" user-select=\"true\" selectable=\"true\">{{detail.pickup_code}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\r\n\t\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.paytypeid\">\r\n\t\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"detail.paytypeid == '5' && detail.transfer_check==1\">\r\n\t\t\t\t\t<view class=\"item\" v-if=\"pay_transfer_info.pay_transfer_account_name\">\r\n\t\t\t\t\t\t<text class=\"t1\">户名</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{pay_transfer_info.pay_transfer_account_name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"pay_transfer_info.pay_transfer_account\">\r\n\t\t\t\t\t\t<text class=\"t1\">账户</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{pay_transfer_info.pay_transfer_account}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"pay_transfer_info.pay_transfer_bank\">\r\n\t\t\t\t\t\t<text class=\"t1\">开户行</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{pay_transfer_info.pay_transfer_bank}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"pay_transfer_info.pay_transfer_desc\">\r\n\t\t\t\t\t\t<text class=\"text-min\">{{pay_transfer_info.pay_transfer_desc}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<text class=\"t1\">付款凭证审核</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{payorder.check_status_label}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" v-if=\"payorder.check_remark\">\r\n\t\t\t\t\t\t<text class=\"t1\">审核备注</text>\r\n\t\t\t\t\t\t<text class=\"t2\">{{payorder.check_remark}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\r\n\t\t\t\t\t<text class=\"t1\">发货时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\r\n\t\t\t\t\t<text class=\"t1\">收货时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">商品金额</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.disprice > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">满减活动</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.invoice_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">发票费用</text>\r\n\t\t\t\t\t<text class=\"t2 red\">+¥{{detail.invoice_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">配送方式</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\r\n\t\t\t\t\t<text class=\"t1\">服务费</text>\r\n\t\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\r\n\t\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\r\n\t\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">配送费</text>\r\n\t\t\t\t\t<text class=\"t2\">¥{{detail.total_freight}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.is_yuanbao_pay==1\">\r\n\t\t\t\t\t<text class=\"t1\">{{t('元宝')}}</text>\r\n\t\t\t\t\t<text class=\"t2 red\">{{detail.total_yuanbao}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">订单状态</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">{{detail.paytypeid==4?'待发货':'已支付'}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2 && detail.express_isbufen==0\">已发货</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2 && detail.express_isbufen==1\">部分发货</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.refundingMoneyTotal>0\">\r\n\t\t\t\t\t<text class=\"t1\">退款中</text>\r\n\t\t\t\t\t<text class=\"t2 red\" @tap=\"goto\" :data-url=\"'refundlist?orderid='+ detail.id\">¥{{detail.refundingMoneyTotal}}</text>\r\n\t\t\t\t\t<text class=\"t3 iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.refundedMoneyTotal>0\">\r\n\t\t\t\t\t<text class=\"t1\">已退款</text>\r\n\t\t\t\t\t<text class=\"t2 red\" @tap=\"goto\" :data-url=\"'refundlist?orderid='+ detail.id\">¥{{detail.refundedMoneyTotal}}</text>\r\n\t\t\t\t\t<text class=\"t3 iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款</text>\r\n\t\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\r\n\t\t\t\t\t<text class=\"t1\">尾款</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.balance_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\r\n\t\t\t\t\t<text class=\"t1\">尾款状态</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==1\">已支付</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==0\">未支付</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"detail.total_diy_amount > 0\">\r\n\t\t\t\t\t<text class=\"t1\">框总价</text>\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.total_diy_amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\" v-if=\"detail.checkmemid\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">所选会员</text>\r\n\t\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t\t<image :src=\"detail.checkmember.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n\t\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.checkmember.nickname}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\r\n\t\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderinfo\" v-if=\"detail.freight_type==11\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">发货地址</text>\r\n\t\t\t\t\t<text class=\"t2\">¥{{detail.freight_content.send_address}} - {{detail.freight_content.send_tel}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">收货地址</text>\r\n\t\t\t\t\t<text class=\"t2\">¥{{detail.freight_content.receive_address}} - {{detail.freight_content.receive_tel}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\r\n\t\t\t<view style=\"width:100%;height:160rpx\"></view>\r\n\t\r\n\t\t\t<view class=\"bottom notabbarbot\" v-if=\"fromfenxiao==0\">\r\n\t\t\t\t<block v-if=\"detail.payaftertourl && detail.payafterbtntext\">\r\n\t\t\t\t\t<view style=\"position:relative\">\r\n\t\t\t\t\t\t<block v-if=\"detail.payafter_username\">\r\n\t\t\t\t\t\t\t<view class=\"btn2\">{{detail.payafterbtntext}}</view>\r\n\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t<wx-open-launch-weapp :username=\"detail.payafter_username\" :path=\"detail.payafter_path\" style=\"position:absolute;top:0;left:0;right:0;bottom:0;z-index:8\">\r\n\t\t\t\t\t\t\t\t<script type=\"text/wxtag-template\">\r\n\t\t\t\t\t\t\t\t\t<div style=\"width:100%;height:40px;\"></div>\r\n\t\t\t\t\t\t\t\t</script>\r\n\t\t\t\t\t\t\t</wx-open-launch-weapp>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"detail.payaftertourl\">{{detail.payafterbtntext}}</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.isworkorder==1\">\r\n\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'/activity/workorder/index?type=1&id='+detail.id\" :data-id=\"detail.id\">发起工单</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.status==0\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">关闭订单</view>\r\n\t\t\t\t\t<view class=\"btn1\" v-if=\"detail.paytypeid != 5\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pages/pay/pay?id=' + detail.payorderid\">去付款</view>\r\n\t\t\t\t\t<block v-if=\"detail.paytypeid==5\">\r\n\t\t\t\t\t\t<view class=\"btn1\" v-if=\"detail.transfer_check == 1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pages/pay/transfer?id=' + detail.payorderid\">上传付款凭证</view>\r\n\t\t\t\t\t\t<view class=\"btn1\" v-if=\"detail.transfer_check == 0\" :style=\"{background:t('color1')}\" >转账待审核</view>\r\n\t\t\t\t\t\t<view class=\"btn1\" v-if=\"detail.transfer_check == -1\" :style=\"{background:t('color1')}\" >转账已驳回</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.status==1\">\r\n\t\t\t\t\t<block v-if=\"detail.paytypeid!='4'\">\r\n\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'refundSelect?orderid=' + detail.id\" v-if=\"shopset.canrefund==1 && detail.refundnum < detail.procount\">退款</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"(detail.status==2 || detail.status==3) && detail.freight_type!=3 && detail.freight_type!=4\">\r\n\t\t\t\t\t\t<view class=\"btn2\" v-if=\"detail.express_type =='express_wx'\" @tap=\"logistics\" :data-express_type=\"detail.express_type\" :data-express_com=\"detail.express_com\" :data-express_no=\"detail.express_no\" :data-express_content=\"detail.express_content\">订单跟踪</view>\r\n\t\t\t\t\t\t<view class=\"btn2\" v-else @tap=\"logistics\" :data-express_type=\"detail.express_type\" :data-express_com=\"detail.express_com\" :data-express_no=\"detail.express_no\" :data-express_content=\"detail.express_content\">查看物流</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"([1,2,3]).includes(detail.status) && invoice\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'invoice?type=shop&orderid=' + detail.id\">发票</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.status==2\">\r\n\t\t\t\t\t<block v-if=\"detail.paytypeid!='4'\">\r\n\t\t\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'refundSelect?orderid=' + detail.id\" v-if=\"shopset.canrefund==1 && detail.refundnum < detail.procount\">退款</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"orderCollect\" :data-id=\"detail.id\" v-if=\"detail.paytypeid!='4' && (detail.balance_pay_status==1 || detail.balance_price==0)\">确认收货</view>\r\n\t\t\t\t\t<!-- <view class=\"btn2\" v-if=\"detail.paytypeid=='4'\">{{codtxt}}</view> -->\r\n\t\t\t\t\t<view v-if=\"detail.balance_pay_status == 0 && detail.balance_price > 0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pages/pay/pay?id=' + detail.balance_pay_orderid\">支付尾款</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"showhxqr\">核销码</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"detail.refundCount\" class=\"btn2\" @tap.stop=\"goto\" :data-url=\"'refundlist?orderid='+ detail.id\">查看退款</view>\r\n\t\t\t\t<block v-if=\"detail.status==3 || detail.status==4\">\r\n\t\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.bid>0 && detail.status==3\">\r\n\t\t\t\t\t<view v-if=\"iscommentdp==0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pagesExt/order/commentdp?orderid=' + detail.id\">评价店铺</view>\r\n\t\t\t\t\t<view v-if=\"iscommentdp==1\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/commentdp?orderid=' + detail.id\">查看评价</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\r\n\t\t\t\t\t<view v-if=\"detail.hexiao_code_member\">\r\n\t\t\t\t\t\t<input type=\"number\" placeholder=\"请输入核销密码\" @input=\"set_hexiao_code_member\" style=\"border: 1px #eee solid;padding: 10rpx;margin:20rpx 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\r\n\t\t\t\t\t\t<button @tap=\"hexiao\" class=\"btn\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t\t\r\n\t\t\t<uni-popup id=\"dialogSelectExpress\" ref=\"dialogSelectExpress\" type=\"dialog\">\r\n\t\t\t\t<view style=\"background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx\" v-if=\"express_content\">\r\n\t\t\t\t\t<view class=\"sendexpress\" v-for=\"(item, index) in express_content\" :key=\"index\" style=\"border-bottom: 1px solid #f5f5f5;padding:20rpx 0;\">\r\n\t\t\t\t\t\t<view class=\"sendexpress-item\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no\" style=\"display: flex;\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#121212\">{{item.express_com}} - {{item.express_no}}</view>\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" style=\"width:30rpx;height:30rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.express_oglist\" style=\"margin-top:20rpx\">\r\n\t\t\t\t\t\t\t<view class=\"oginfo-item\" v-for=\"(item2, index2) in item.express_oglist\" :key=\"index2\" style=\"display: flex;align-items:center;margin-bottom:10rpx\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.pic\" style=\"width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\" style=\"color:#555\">{{item2.name}}({{item2.ggname}})</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n\t</template>\r\n\t\r\n\t<script>\r\n\tvar app = getApp();\r\n\tvar interval = null;\r\n\t\r\n\texport default {\r\n\t  data() {\r\n\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tfujiaArr:{},\r\n\t\t\t\tloading:false,\r\n\t\t  isload: false,\r\n\t\t\t\tmenuindex:-1,\r\n\t\t\t\t\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t  prodata: '',\r\n\t\t  djs: '',\r\n\t\t  iscommentdp: \"\",\r\n\t\t  detail: \"\",\r\n\t\t\t\tpayorder:{},\r\n\t\t  prolist: \"\",\r\n\t\t  shopset: \"\",\r\n\t\t  storeinfo: \"\",\r\n\t\t  lefttime: \"\",\r\n\t\t  codtxt: \"\",\r\n\t\t\t\tpay_transfer_info:{},\r\n\t\t\t\tinvoice:0,\r\n\t\t\t\tselectExpressShow:false,\r\n\t\t\t\texpress_content:'',\r\n\t\t\t\tfromfenxiao:0,\r\n\t\t\t\thexiao_code_member:'',\r\n\t\t};\r\n\t  },\r\n\t\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tif (this.opt && this.opt.fromfenxiao && this.opt.fromfenxiao == '1'){\r\n\t\t\t  this.fromfenxiao = 1;\r\n\t\t}\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t  onUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t  },\r\n\t  methods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiOrder/detail', {id: that.opt.id}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.iscommentdp = res.iscommentdp;\r\n\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 计算每个商品的框价总额\r\n\t\t\t\t\tlet total_diy_amount = 0;\r\n\t\t\t\t\tthat.prolist.forEach(item => {\r\n\t\t\t\t\t\tif(item.diy_amount && item.diy_amount != 0 && item.diy_amount != 1) {\r\n\t\t\t\t\t\t\t// 计算单个商品的框价总额\r\n\t\t\t\t\t\t\titem.diy_amount_total = (item.diy_amount * item.num).toFixed(2);\r\n\t\t\t\t\t\t\t// 累加到订单总框价\r\n\t\t\t\t\t\t\ttotal_diy_amount += parseFloat(item.diy_amount_total);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 设置订单总框价\r\n\t\t\t\t\tthat.detail.total_diy_amount = total_diy_amount.toFixed(2);\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.fujiaArr = res.fujiaArr;\r\n\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\tthat.storeinfo = res.storeinfo;\r\n\t\t\t\t\tthat.lefttime = res.lefttime;\r\n\t\t\t\t\tthat.codtxt = res.codtxt;\r\n\t\t\t\t\tthat.pay_transfer_info =  res.pay_transfer_info;\r\n\t\t\t\t\tthat.payorder = res.payorder;\r\n\t\t\t\t\tthat.invoice = res.invoice;\r\n\t\t\t\t\tif (res.lefttime > 0) {\r\n\t\t\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\r\n\t\t\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tset_hexiao_code_member:function(e){\r\n\t\t\t\tthis.hexiao_code_member = e.detail.value;\r\n\t\t\t},\r\n\t\t\thexiao: function () {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiOrder/hexiao', {orderid: that.opt.id,hexiao_code_member:that.hexiao_code_member}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status != 1){\r\n\t\t\t\t\t\tapp.error(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tthat.closeHxqr();\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t  that.getdata();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\tgetdjs: function () {\r\n\t\t  var that = this;\r\n\t\t  var totalsec = that.lefttime;\r\n\t\r\n\t\t  if (totalsec <= 0) {\r\n\t\t\tthat.djs = '00时00分00秒';\r\n\t\t  } else {\r\n\t\t\tvar houer = Math.floor(totalsec / 3600);\r\n\t\t\tvar min = Math.floor((totalsec - houer * 3600) / 60);\r\n\t\t\tvar sec = totalsec - houer * 3600 - min * 60;\r\n\t\t\tvar djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n\t\t\tthat.djs = djs;\r\n\t\t  }\r\n\t\t},\r\n\t\ttodel: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要删除该订单吗?', function () {\r\n\t\t\t\t\tapp.showLoading('删除中');\r\n\t\t\tapp.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tapp.goback(true);\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\ttoclose: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要关闭该订单吗?', function () {\r\n\t\t\t\t\tapp.showLoading('提交中');\r\n\t\t\tapp.post('ApiOrder/closeOrder', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\torderCollect: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var orderid = e.currentTarget.dataset.id;\r\n\t\t  app.confirm('确定要收货吗?', function () {\r\n\t\t\t\t\tapp.showLoading('收货中');\r\n\t\t\tapp.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t  app.success(data.msg);\r\n\t\t\t  setTimeout(function () {\r\n\t\t\t\tthat.getdata();\r\n\t\t\t  }, 1000);\r\n\t\t\t});\r\n\t\t  });\r\n\t\t},\r\n\t\t\tshowhxqr:function(){\r\n\t\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t\t},\r\n\t\t\tcloseHxqr:function(){\r\n\t\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t\t},\r\n\t\t\topenLocation:function(e){\r\n\t\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\r\n\t\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\r\n\t\t\t\tvar address = e.currentTarget.dataset.address;\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t latitude:latitude,\r\n\t\t\t\t longitude:longitude,\r\n\t\t\t\t name:address,\r\n\t\t\t\t scale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\topenMendian: function(e) {\r\n\t\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\r\n\t\t\t\tapp.goto('/shopPackage/shop/mendian?id=' + storeinfo.id);\r\n\t\t\t},\r\n\t\t\tlogistics:function(e){\r\n\t\t\t\tvar express_com = e.currentTarget.dataset.express_com\r\n\t\t\t\tvar express_no = e.currentTarget.dataset.express_no\r\n\t\t\t\tvar express_content = e.currentTarget.dataset.express_content\r\n\t\t\t\tvar express_type = e.currentTarget.dataset.express_type\r\n\t\t\t\tvar prolist = this.prolist;\r\n\t\t\t\tconsole.log(express_content)\r\n\t\t\t\tif(!express_content){\r\n\t\t\t\t\tapp.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);\r\n\t\t\t\t}else{\r\n\t\t\t\t\texpress_content = JSON.parse(express_content);\r\n\t\t\t\t\tfor(var i in express_content){\r\n\t\t\t\t\t\tif(express_content[i].express_ogids){\r\n\t\t\t\t\t\t\tvar express_ogids = (express_content[i].express_ogids).split(',');\r\n\t\t\t\t\t\t\tconsole.log(express_ogids);\r\n\t\t\t\t\t\t\tvar express_oglist = [];\r\n\t\t\t\t\t\t\tfor(var j in prolist){\r\n\t\t\t\t\t\t\t\tif(app.inArray(prolist[j].id+'',express_ogids)){\r\n\t\t\t\t\t\t\t\t\texpress_oglist.push(prolist[j]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\texpress_content[i].express_oglist = express_oglist;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.express_content = express_content;\r\n\t\t\t\t\tthis.$refs.dialogSelectExpress.open();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thideSelectExpressDialog:function(){\r\n\t\t\t\tthis.$refs.dialogSelectExpress.close();\r\n\t\t\t}\r\n\t  }\r\n\t};\r\n\t</script>\r\n\t<style>\r\n\t\t.text-min { font-size: 24rpx; color: #999;}\r\n\t.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n\t.ordertop .f1{color:#fff}\r\n\t.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n\t.ordertop .f1 .t2{font-size:24rpx}\r\n\t\r\n\t.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\r\n\t.address .img{width:40rpx}\r\n\t.address image{width:40rpx; height:40rpx;}\r\n\t.address .info{flex:1;display:flex;flex-direction:column;}\r\n\t.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n\t.address .info .t2{font-size:24rpx;color:#999}\r\n\t\r\n\t.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n\t.product .box{width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\r\n\t.product .content{display:flex;position:relative;}\r\n\t.product .box:last-child{ border-bottom: 0; }\r\n\t.product .content image{ width: 140rpx; height: 140rpx;}\r\n\t.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n\t\r\n\t.product .content .detail .t1{font-size:26rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n\t.product .content .detail .t2{color: #999;font-size: 26rpx;margin-top: 10rpx;}\r\n\t.product .content .detail .t3{display:flex;color: #ff4246;margin-top: 10rpx;}\r\n\t.product .content .detail .t4{margin-top: 10rpx;}\r\n\t\r\n\t.product .content .detail .x1{ flex:1}\r\n\t.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n\t.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\t.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\t\r\n\t.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n\t.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n\t.orderinfo .item:last-child{ border-bottom: 0;}\r\n\t.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n\t.orderinfo .item .t2{flex:1;text-align:right}\r\n\t.orderinfo .item .t2.pickup-code{color:#ff4246;font-size:36rpx;font-weight:bold}\r\n\t.orderinfo .item .t3{ margin-top: 3rpx;}\r\n\t.orderinfo .item .red{color:red}\r\n\t\r\n\t.bottom{ width: 100%; height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n\t\r\n\t.btn { border-radius: 10rpx;color: #fff;}\r\n\t.btn1{margin-left:20rpx;min-width:160rpx;padding: 0 20rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\r\n\t.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\r\n\t.btn3{font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n\t\r\n\t.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\r\n\t.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\t.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\t\r\n\t.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n\t.hxqrbox .img{width:400rpx;height:400rpx}\r\n\t.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n\t.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\t\r\n\t.glassitem{background:#f5f5f5;display: flex;align-items: center;padding: 10rpx 0;}\r\n\t.glassitem .gcontent{flex:1;padding: 0 20rpx;}\r\n\t.glassheader{display: flex;justify-content: space-between;align-items: center;line-height: 60rpx;}\r\n\t.glassheader .name{font-weight: bold;font-size: 32rpx;}\r\n\t.glassrow{display: flex;padding: 10rpx 0;align-items: center;}\r\n\t.glassrow .glasscol{min-width: 25%;text-align: center;}\r\n\t.glassitem .bt{border-top:1px solid #e3e3e3}\r\n\t\r\n\t/* 框价格样式 */\r\n\t.frame-price {\r\n\t\tfont-size: 24rpx !important;\r\n\t\tcolor: #666 !important;\r\n\t\tmargin-top: 8rpx !important;\r\n\t\tdisplay: flex !important;\r\n\t\talign-items: center !important;\r\n\t}\r\n\t\r\n\t.frame-label {\r\n\t\tcolor: #666;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.frame-value {\r\n\t\tcolor: #ff4246;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.frame-multiply {\r\n\t\tcolor: #666;\r\n\t\tmargin: 0 10rpx;\r\n\t}\r\n\t\r\n\t.frame-num {\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t.frame-equal {\r\n\t\tcolor: #666;\r\n\t\tmargin: 0 10rpx;\r\n\t}\r\n\t\r\n\t.frame-total {\r\n\t\tcolor: #ff4246;\r\n\t}\r\n\t</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098775\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}