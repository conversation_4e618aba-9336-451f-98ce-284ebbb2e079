{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?36e8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?3145", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?2267", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?a3ce", "uni-app:///pagesExt/sign/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?b596", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/sign/index.vue?8dce"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniCalendar", "data", "opt", "loading", "isload", "menuindex", "pre_url", "hassign", "signset", "userinfo", "list", "display", "style", "start_date", "end_date", "selectedDate", "nomore", "nodata", "pagenum", "isdoing", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "videoAd", "adUnitId", "signin", "then", "catch", "confirmsign", "getPaiming"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uQAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0F9wB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAE;YACAC;UACA;UACAD;UACAA;UACAA;YACAF;YACA;cACAA;YACA;cACA;YAAA;UAEA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAJ;MACA;QACAE;UACAA,eACAG;YAAA;UAAA,GACAC;YACAN;UACA;QACA;MACA;QACAA;MACA;IACA;IACAO;MACA;MACAN;QACA;UACAA;UACAD;QACA;UACAC;QACA;QACAD;MACA;IACA;IACAQ;MACA;MACA;QACA;QACA;QACAP;UAAAP;QAAA;UACAM;UACA;UACA;YACAA;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACA;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/sign/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/sign/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=58b2f074&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/sign/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=58b2f074&\"", "var components\ntry {\n  components = {\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-calendar/uni-calendar\" */ \"@/components/uni-calendar/uni-calendar.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.style == 1 && !_vm.hassign ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.style == 2 && !_vm.hassign ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.style == 2 && !_vm.hassign ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload && _vm.style == 2 && !!_vm.hassign ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload && _vm.style == 2 ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? !_vm.nomore && _vm.list && _vm.list.length >= 10 : null\n  var m5 = _vm.isload && g0 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        g0: g0,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template> \r\n  <view class=\"container\">\r\n    <block v-if=\"isload\">\r\n      <view class=\"qd_head\" v-if=\"style == 1\">\r\n        <block v-if=\"signset.bgpic\">\r\n          <image :src=\"signset.bgpic\" class=\"qdbg\"></image>\r\n        </block>\r\n        <block v-else>\r\n          <image :src=\"pre_url + '/static/img/sign-bg.png'\" class=\"qdbg\"></image>\r\n        </block>\r\n\r\n        <view class=\"myscore\">\r\n          <!-- <view class=\"f2\">{{userinfo.score}}{{t('积分')}}</view> -->\r\n          <view class=\"f2\">\r\n            <view class=\"f2\">今日签到分红:{{userinfo.qiandaofh}}</view>\r\n          </view>\r\n          <view class=\"f2\">\r\n            <view class=\"f2\">签到分红总计:{{userinfo.qiandaocount}}</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"signlog\" @tap=\"goto\" data-url=\"signrecord\">签到记录</view>\r\n\r\n        <view class=\"signbtn\" v-if=\"!hassign\">\r\n          <button class=\"btn\" :style=\"{background:t('color1')}\" @tap=\"signin\">立即签到</button>\r\n        </view>\r\n        <view class=\"signbtn\" v-else>\r\n          <button class=\"btn2\">今日已签到</button>\r\n          <view class=\"signtip\">已连续签到{{userinfo.signtimeslx}}天</view>\r\n        </view>\r\n      </view>\r\n      <view class=\"qd_head qd_head2\" v-if=\"style == 2\" :style=\"'background-image:url(' + signset.bgpic + ');'\">\r\n        <!-- <view class=\"myscore\"><view class=\"f1\">{{userinfo.score}}</view><view class=\"f2\">{{t('积分')}}</view></view> -->\r\n        <view class=\"signlog\" @tap=\"goto\" data-url=\"signrecord\">签到记录</view>\r\n\r\n        <view class=\"signbtn\" v-if=\"!hassign\">\r\n          <button class=\"btn\" :style=\"{background:t('color1')}\" @tap=\"signin\">立即签到</button>\r\n          <view class=\"signtip\">当前共{{userinfo.score}}{{t('积分')}}</view>\r\n        </view>\r\n        <view class=\"signbtn\" v-else>\r\n          <button class=\"btn2\">今日已签到</button>\r\n          <view class=\"signtip\">已连续签到{{userinfo.signtimeslx}}天，共{{userinfo.score}}{{t('积分')}}</view>\r\n        </view>\r\n        <view class=\"calendar\">\r\n          <uni-calendar \r\n            :insert=\"true\"\r\n            :lunar=\"false\" \r\n            :start-date=\"start_date\"\r\n            :end-date=\"end_date\"\r\n            :selected='selectedDate'\r\n            :showMonth=\"false\"\r\n            :backColor= \"t('color1')\"\r\n            :fontColor= \"'#fff'\"\r\n          />\r\n        </view>\r\n        <!-- [{date: '2021-11-02', info: '已签'}] -->\r\n      </view>\r\n      \r\n      <view class=\"qd_guize\">\r\n        <view class=\"gztitle\"> — 签到排名 — </view>\r\n        <view class=\"paiming\">\r\n          <view v-for=\"(item, index) in list\" :key=\"index\" class=\"item flex\">\r\n            <view class=\"f1\">\r\n              <text class=\"t1\">{{item.nickname}}</text>\r\n            </view>\r\n            <view class=\"f2\">\r\n              <text class=\"t2\">连续签到</text>\r\n              <text class=\"t1\">{{item.signtimeslx}}</text>\r\n              <text class=\"t2\">天</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"btn-a\" @tap=\"getPaiming\" v-if=\"!nomore && list && list.length >=10\" :style=\"{color:t('color1')}\">查看更多</view>\r\n        <nomore v-if=\"nomore\"></nomore>\r\n      </view>\r\n      \r\n      <view class=\"qd_guize\">\r\n        <view class=\"gztitle\"> — 签到规则 — </view>\r\n        <view class=\"guize_txt\">\r\n          <parse :content=\"signset.guize\" />\r\n        </view>\r\n      </view>\r\n      \r\n    </block>\r\n    <loading v-if=\"loading\"></loading>\r\n    <dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n    <popmsg ref=\"popmsg\"></popmsg>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nlet videoAd = null;\r\nvar app = getApp();\r\nimport uniCalendar from './uni-calendar/uni-calendar.vue';\r\nexport default {\r\n  components: {\r\n    uniCalendar\r\n  },\r\n  data() {\r\n    return {\r\n      opt:{},\r\n      loading:false,\r\n      isload: false,\r\n      menuindex:-1,\r\n      pre_url:app.globalData.pre_url,\r\n      hassign:false,\r\n      signset:{},\r\n      userinfo:{},\r\n      list: [],\r\n      display:false,\r\n      style:1,\r\n      start_date:'',\r\n      end_date:'',\r\n      selectedDate:[], //{date: '2022-08-02', info: '已签'}\r\n      nomore:false,\r\n      nodata:false,\r\n      pagenum:1,\r\n      isdoing:false,\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n    this.opt = app.getopts(opt);\r\n    this.getdata();\r\n  },\r\n  onPullDownRefresh: function () {\r\n    this.getdata();\r\n  },\r\n  methods: {\r\n    getdata: function () {\r\n      var that = this;\r\n      that.loading = true;\r\n      app.get('ApiSign/index', {}, function (res) {\r\n        that.loading = false;\r\n        that.hassign = res.hassign;\r\n        that.signset = res.signset;\r\n        that.userinfo = res.userinfo;\r\n        that.list = res.list || []; // 确保 list 为数组\r\n        that.display = res.signset.display;\r\n        that.style = res.signset.style;\r\n        that.end_date = res.today;\r\n        that.selectedDate = res.selectedDate;\r\n        that.loaded();\r\n        if (app.globalData.platform == 'wx' && res.signset.rewardedvideoad && !videoAd && wx.createRewardedVideoAd) {\r\n          videoAd = wx.createRewardedVideoAd({\r\n            adUnitId: res.signset.rewardedvideoad\r\n          });\r\n          videoAd.onLoad(() => {})\r\n          videoAd.onError((err) => {})\r\n          videoAd.onClose(res2 => {\r\n            that.isdoing = false;\r\n            if (res2 && res2.isEnded) {\r\n              that.confirmsign();\r\n            } else {\r\n              // 用户关闭了广告\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    signin: function () {\r\n      var that = this;\r\n      if (that.isdoing) return;\r\n      that.isdoing = true;\r\n      if (app.globalData.platform == 'wx' && that.signset.rewardedvideoad && videoAd) {\r\n        videoAd.show().catch(() => {\r\n          videoAd.load()\r\n          .then(() => videoAd.show())\r\n          .catch(err => {\r\n            that.confirmsign();\r\n          });\r\n        });\r\n      } else {\r\n        that.confirmsign();\r\n      }\r\n    },\r\n    confirmsign() {\r\n      var that = this;\r\n      app.post('ApiSign/signin', {}, function (data) {\r\n        if (data.status == 1) {\r\n          app.success('+' + data.scoreadd + that.t('积分'));\r\n          that.getdata();\r\n        } else {\r\n          app.alert(data.msg);\r\n        }\r\n        that.isdoing = false;\r\n      });\r\n    },\r\n    getPaiming: function () {\r\n      var that = this;\r\n      if (!this.nodata && !this.nomore) {\r\n        this.pagenum = this.pagenum + 1;\r\n        this.loading = true;\r\n        app.post('ApiSign/getPaiming', { pagenum: this.pagenum }, function (res) {\r\n          that.loading = false;\r\n          var datalist = res.datalist || []; // 确保 datalist 为数组\r\n          if (that.pagenum == 1) {\r\n            that.list = datalist;\r\n            if (datalist.length == 0) {\r\n              that.nodata = true;\r\n            }\r\n          } else {\r\n            if (datalist.length == 0) {\r\n              that.nomore = true;\r\n            } else {\r\n              var list = that.list || [];\r\n              var newdata = list.concat(datalist);\r\n              that.list = newdata;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\npage{background:#f4f4f4}\r\n.qd_head{width: 100%;/* height:940rpx; */position: relative;}\r\n.qdbg{width: 100%;height:940rpx;}\r\n.myscore{position:absolute;top:60rpx;width:100%;display:flex;color:#fff;flex-direction:column;align-items:center;z-index:2}\r\n.myscore .f1{font-size:56rpx;font-weight:bold}\r\n.myscore .f2{font-size:32rpx}\r\n.signlog{position:absolute;top:50rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}\r\n.qd_head .signbtn{position:absolute;top:760rpx;width:100%;display:flex;flex-direction:column;align-items:center;z-index:2}\r\n.qd_head .signbtn .btn{width:440rpx;height:80rpx;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}\r\n.qd_head .signbtn .btn2{width:440rpx;height:80rpx;background:#FCB0B0;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}\r\n.qd_head .signbtn .signtip{color:#999999;margin-top:16rpx}\r\n.btn-a { text-align: center;margin-top: 18rpx;}\r\n\r\n.qd_head2 { padding-bottom: 20rpx;background-position: center;background-repeat: no-repeat;background-size:cover;}\r\n.qd_head2 .calendar { padding-top: 300rpx;width: 96%;margin: 0 2%;}\r\n.qd_head2 .signbtn {/* position:initial;*/top: 136rpx;}\r\n.qd_head2 .signbtn .signtip {color: #fff;}\r\n\r\n.qd_guize{width:100%;margin:0;padding-bottom:20rpx}\r\n.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}\r\n.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}\r\n.paiming{ width:94%;margin:0 3%;background:#fff;border-radius:10px;padding:20rpx 20rpx;}\r\n.paiming .item{ line-height: 80rpx;border-bottom: 1px dashed #eee;}\r\n.paiming .item:last-child{border:0}\r\n.paiming .item .f1{flex:1;display:flex;flex-direction:column}\r\n.paiming .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}\r\n.paiming .item .f1 .t2{color:#666666}\r\n.paiming .item .f1 .t3{color:#666666}\r\n.paiming .item .f2{ flex:1;text-align:right;font-size:30rpx;}\r\n.paiming .item .f2 .t1{color:#03bc01}\r\n.paiming .item .f2 .t2{color:#000000}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115098370\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}