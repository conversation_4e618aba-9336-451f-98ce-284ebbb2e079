{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?bc38", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?31b5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?6302", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?5c5c", "uni-app:///pagesExt/equity_pool/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?c949", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/equity_pool/index.vue?8b5e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "myChart", "pool", "total_amount", "equity_count", "equity_value", "user_equity", "equity_num", "equity_ranking", "chart_data", "onLoad", "onShow", "onPullDownRefresh", "onUnload", "window", "methods", "getdata", "that", "app", "uni", "msg", "isok", "initChart", "dates", "values", "backgroundColor", "title", "text", "left", "textStyle", "color", "fontSize", "fontWeight", "tooltip", "trigger", "xAxis", "type", "axisLabel", "interval", "yAxis", "name", "formatter", "series", "smooth", "showSymbol", "symbolSize", "itemStyle", "lineStyle", "width", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "animation", "formatNumber", "num", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsE9wB;AACA,cAAc,mBAAO,CAAC,+CAAqC;AAA3D,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAF;MACA;MACAG;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACAE;QACA;UACAF;UACAA;UACAA;UACAA;UACAA;;UAEA;UACAA;YACAA;UACA;QACA;UACAA;YACAG;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MAEA;MACA;MACA;MAEA;QACAC;QACAC;MACA;MAEA;QACAC;QACAC;UACAC;UACAC;UACAC;YACAC;YACAC;YACAC;UACA;QACA;QACAC;UACAC;QACA;QACAC;UACAC;UACAxC;UACAyC;YACAC;YACAP;UACA;QACA;QACAQ;UACAH;UACAI;UACAH;YACAI;UACA;QACA;QACAC;UACAF;UACAJ;UACAxC;UACA+C;UACAC;UACAC;UACAC;YACAhB;UACA;UACAiB;YACAC;YACAlB;UACA;UACAmB;YACAnB;cACAM;cACAc;cACAC;cACAC;cACAC;cACAC;gBACAC;gBACAzB;cACA;gBACAyB;gBACAzB;cACA;YACA;UACA;UACA0B;QACA;MACA;MAEAvD;;MAEA;MACAa;QACAb;MACA;;MAEA;MACA;IACA;IAEA;IACAwD;MACA;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAxC;QACAyC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/PA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/equity_pool/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/equity_pool/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=dd54d4d6&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/equity_pool/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=dd54d4d6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.isload ? _vm.formatNumber(_vm.pool.total_amount) : null\n  var m3 = _vm.isload ? _vm.formatNumber(_vm.pool.equity_count) : null\n  var m4 = _vm.isload ? _vm.formatNumber(_vm.pool.equity_value) : null\n  var m5 = _vm.isload ? _vm.formatNumber(_vm.user_equity.equity_num) : null\n  var m6 = _vm.isload ? _vm.formatNumber(_vm.user_equity.equity_value) : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.equity_ranking.slice(0, 5), function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m7 = _vm.formatNumber(item.total_num)\n        var m8 = _vm.formatNumber(item.equity_value)\n        return {\n          $orig: $orig,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"equity-pool-header\">\r\n\t\t\t\t<view class=\"header-card\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t\t<view class=\"card-title\">股权池总览</view>\r\n\t\t\t\t\t<view class=\"data-row\">\r\n\t\t\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t\t\t<view class=\"data-value\">{{formatNumber(pool.total_amount)}}</view>\r\n\t\t\t\t\t\t\t<view class=\"data-label\">股权池总金额</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t\t\t<view class=\"data-value\">{{formatNumber(pool.equity_count)}}</view>\r\n\t\t\t\t\t\t\t<view class=\"data-label\">股权总数量</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t\t\t<view class=\"data-value\">{{formatNumber(pool.equity_value)}}</view>\r\n\t\t\t\t\t\t\t<view class=\"data-label\">单位股权价值</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"my-equity-card\">\r\n\t\t\t\t<view class=\"card-title\">我的股权</view>\r\n\t\t\t\t<view class=\"data-row\">\r\n\t\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t\t<view class=\"data-value\">{{formatNumber(user_equity.equity_num)}}</view>\r\n\t\t\t\t\t\t<view class=\"data-label\">持有股权数量</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"data-item\">\r\n\t\t\t\t\t\t<view class=\"data-value\">{{formatNumber(user_equity.equity_value)}}</view>\r\n\t\t\t\t\t\t<view class=\"data-label\">我的股权价值</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-row\">\r\n\t\t\t\t\t<view class=\"btn\" @tap=\"goto\" data-url=\"/pagesExt/equity_pool/myEquity\">我的股权详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"equity-chart\">\r\n\t\t\t\t<view class=\"card-title\">股权价值走势</view>\r\n\t\t\t\t<view class=\"chart-container\" id=\"equityChart\"></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"equity-ranking\">\r\n\t\t\t\t<view class=\"rank-header\">\r\n\t\t\t\t\t<view class=\"rank-title\">股权持有排行榜</view>\r\n\t\t\t\t\t<view class=\"rank-more\" @tap=\"goto\" data-url=\"/pagesExt/equity_pool/ranking\">查看更多</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rank-list\">\r\n\t\t\t\t\t<view class=\"rank-item\" v-for=\"(item, index) in equity_ranking.slice(0, 5)\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"rank-num\" :class=\"index < 3 ? 'top' + (index + 1) : ''\">{{index + 1}}</view>\r\n\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.headimg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"nickname\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"equity-count\">{{formatNumber(item.total_num)}}</view>\r\n\t\t\t\t\t\t<view class=\"equity-value\">{{formatNumber(item.equity_value)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar echarts = require('./components/echarts/echarts.min.js');\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt: {},\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\tmyChart: null,\r\n\t\t\t\r\n\t\t\tpool: {\r\n\t\t\t\ttotal_amount: 0,\r\n\t\t\t\tequity_count: 0,\r\n\t\t\t\tequity_value: 0\r\n\t\t\t},\r\n\t\t\tuser_equity: {\r\n\t\t\t\tequity_num: 0,\r\n\t\t\t\tequity_value: 0\r\n\t\t\t},\r\n\t\t\tequity_ranking: [],\r\n\t\t\tchart_data: []\r\n\t\t};\r\n\t},\r\n\tonLoad: function(opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShow: function() {\r\n\t\t// 页面显示时重新初始化图表\r\n\t\tif (this.isload && this.chart_data.length > 0) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (this.myChart) {\r\n\t\t\t\t\tthis.myChart.resize();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.initChart();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tonPullDownRefresh: function() {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonUnload: function() {\r\n\t\t// 页面卸载时销毁图表实例\r\n\t\tif (this.myChart) {\r\n\t\t\twindow.removeEventListener('resize', this.myChart.resize);\r\n\t\t\tthis.myChart = null;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiEquityPool/index', {}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tthat.pool = res.data.pool;\r\n\t\t\t\t\tthat.user_equity = res.data.user_equity;\r\n\t\t\t\t\tthat.equity_ranking = res.data.equity_ranking;\r\n\t\t\t\t\tthat.chart_data = res.data.chart_data;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 初始化图表\r\n\t\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\t\tthat.initChart();\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.$refs.popmsg.show({\r\n\t\t\t\t\t\tmsg: res.msg || '获取数据失败，请重试',\r\n\t\t\t\t\t\tisok: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 初始化股权价值走势图表\r\n\t\tinitChart: function() {\r\n\t\t\tif (!this.chart_data || this.chart_data.length === 0) return;\r\n\t\t\t\r\n\t\t\tvar that = this;\r\n\t\t\tvar chartDom = document.getElementById('equityChart');\r\n\t\t\tif (!chartDom) return;\r\n\t\t\t\r\n\t\t\tvar myChart = echarts.init(chartDom);\r\n\t\t\tvar dates = [];\r\n\t\t\tvar values = [];\r\n\t\t\t\r\n\t\t\tfor (var i = 0; i < that.chart_data.length; i++) {\r\n\t\t\t\tdates.push(that.chart_data[i].date);\r\n\t\t\t\tvalues.push(parseFloat(that.chart_data[i].value));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar option = {\r\n\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\ttitle: {\r\n\t\t\t\t\ttext: '股权价值走势',\r\n\t\t\t\t\tleft: 'center',\r\n\t\t\t\t\ttextStyle: {\r\n\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\tfontSize: 14,\r\n\t\t\t\t\t\tfontWeight: 'bold'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\ttooltip: {\r\n\t\t\t\t\ttrigger: 'axis'\r\n\t\t\t\t},\r\n\t\t\t\txAxis: {\r\n\t\t\t\t\ttype: 'category',\r\n\t\t\t\t\tdata: dates,\r\n\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\tinterval: Math.floor(dates.length / 6),\r\n\t\t\t\t\t\tfontSize: 10\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tyAxis: {\r\n\t\t\t\t\ttype: 'value',\r\n\t\t\t\t\tname: '股权价值',\r\n\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\tformatter: '{value}'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tseries: [{\r\n\t\t\t\t\tname: '股权价值',\r\n\t\t\t\t\ttype: 'line',\r\n\t\t\t\t\tdata: values,\r\n\t\t\t\t\tsmooth: true,\r\n\t\t\t\t\tshowSymbol: true,\r\n\t\t\t\t\tsymbolSize: 6,\r\n\t\t\t\t\titemStyle: {\r\n\t\t\t\t\t\tcolor: that.t('color1')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tlineStyle: {\r\n\t\t\t\t\t\twidth: 3,\r\n\t\t\t\t\t\tcolor: that.t('color1')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tareaStyle: {\r\n\t\t\t\t\t\tcolor: {\r\n\t\t\t\t\t\t\ttype: 'linear',\r\n\t\t\t\t\t\t\tx: 0,\r\n\t\t\t\t\t\t\ty: 0,\r\n\t\t\t\t\t\t\tx2: 0,\r\n\t\t\t\t\t\t\ty2: 1,\r\n\t\t\t\t\t\t\tcolorStops: [{\r\n\t\t\t\t\t\t\t\toffset: 0,\r\n\t\t\t\t\t\t\t\tcolor: 'rgba(' + that.t('color1rgb') + ', 0.5)'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\toffset: 1,\r\n\t\t\t\t\t\t\t\tcolor: 'rgba(' + that.t('color1rgb') + ', 0.1)'\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tanimation: true\r\n\t\t\t\t}]\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tmyChart.setOption(option);\r\n\t\t\t\r\n\t\t\t// 监听窗口变化，重绘图表\r\n\t\t\twindow.addEventListener('resize', function() {\r\n\t\t\t\tmyChart.resize();\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 将图表实例保存起来，避免重复创建\r\n\t\t\tthis.myChart = myChart;\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化数字，保留两位小数\r\n\t\tformatNumber: function(num) {\r\n\t\t\tif (typeof num !== 'number') {\r\n\t\t\t\tnum = parseFloat(num) || 0;\r\n\t\t\t}\r\n\t\t\treturn num.toFixed(2);\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到指定页面\r\n\t\tgoto: function(e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.equity-pool-header {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.header-card {\r\n\tborder-radius: 10rpx;\r\n\tcolor: #fff;\r\n\tpadding: 30rpx 20rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.data-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.data-item {\r\n\ttext-align: center;\r\n\tflex: 1;\r\n}\r\n\r\n.data-value {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.data-label {\r\n\tfont-size: 24rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.my-equity-card {\r\n\tmargin: 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 30rpx 20rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.my-equity-card .card-title {\r\n\tcolor: #333;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.my-equity-card .data-item .data-value {\r\n\tcolor: #f0505a;\r\n}\r\n\r\n.my-equity-card .data-item .data-label {\r\n\tcolor: #666;\r\n}\r\n\r\n.btn-row {\r\n\tmargin-top: 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.btn {\r\n\tbackground: linear-gradient(90deg, #f0505a 0%, #f78d94 100%);\r\n\tcolor: #fff;\r\n\tpadding: 15rpx 40rpx;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.equity-chart {\r\n\tmargin: 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 30rpx 20rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.equity-chart .card-title {\r\n\tcolor: #333;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.chart-container {\r\n\twidth: 100%;\r\n\theight: 400rpx;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.equity-ranking {\r\n\tmargin: 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 30rpx 20rpx;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.rank-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.rank-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.rank-more {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.rank-list {\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.rank-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.rank-num {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\ttext-align: center;\r\n\tbackground-color: #f5f5f5;\r\n\tcolor: #999;\r\n\tborder-radius: 30rpx;\r\n\tmargin-right: 20rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.rank-num.top1 {\r\n\tbackground-color: #ff7043;\r\n\tcolor: #fff;\r\n}\r\n\r\n.rank-num.top2 {\r\n\tbackground-color: #ff9800;\r\n\tcolor: #fff;\r\n}\r\n\r\n.rank-num.top3 {\r\n\tbackground-color: #ffc107;\r\n\tcolor: #fff;\r\n}\r\n\r\n.user-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 2;\r\n}\r\n\r\n.avatar {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 40rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.nickname {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tmax-width: 150rpx;\r\n}\r\n\r\n.equity-count {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.equity-value {\r\n\tflex: 1;\r\n\ttext-align: right;\r\n\tfont-size: 28rpx;\r\n\tcolor: #f0505a;\r\n\tfont-weight: bold;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093091\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}