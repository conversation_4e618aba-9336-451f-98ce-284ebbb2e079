{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?b676", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?b1c2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?7b1d", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?1f08", "uni-app:///pagesExa/my/setidcardimg.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?67e5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/my/setidcardimg.vue?4c18"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "realname", "idcard", "textset", "haspwd", "idcard_front", "idcard_back", "onLoad", "app", "console", "_that", "onPullDownRefresh", "methods", "upIdcardHead", "that", "upIdcardBack", "formSubmit", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0ErxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;;IAIA;IAGA;IACAC;MAEAC;MAEA;MACAC;MACAA;MAEA;QACAF;MACA;IAGA;IACA;IACA;IACA;IACA;EACA;;EACAG;IACA;EAAA,CACA;EACAC;IAEAC;MAEA;MACAL;QACA;QACAM;MACA;IACA;IAEAC;MACA;MACAP;QACA;QACAM;MACA;IACA;IAEAE;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAR;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEAA;MACAA;QACAP;QACAC;QACAI;QACAD;MACA;QACAG;QACA;UACAA;UAEAS;YACAT;UACA;QAGA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAAg8C,CAAgB,24CAAG,EAAC,C;;;;;;;;;;;ACAp9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/my/setidcardimg.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/my/setidcardimg.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setidcardimg.vue?vue&type=template&id=72e079c2&scoped=true&\"\nvar renderjs\nimport script from \"./setidcardimg.vue?vue&type=script&lang=js&\"\nexport * from \"./setidcardimg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setidcardimg.vue?vue&type=style&index=0&id=72e079c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72e079c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/my/setidcardimg.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimg.vue?vue&type=template&id=72e079c2&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimg.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\t\t\t\t\t<view class=\"auth\">\r\n\t\t\t\t\t\t<view class=\"infos\">\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>您的姓名</text>\r\n\t\t\t\t\t\t\t\t<input placeholder=\"请输入内容\" \r\n\t\t\t\t\t\t\t\t\tplaceholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" \r\n\t\t\t\t\t\t\t\t\tborder=\"surround\" \r\n\t\t\t\t\t\t\t\t\tname=\"realname\"\r\n\t\t\t\t\t\t\t\t\tv-model=\"realname\"\r\n\t\t\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>您的身份证</text>\r\n\t\t\t\t\t\t\t\t<input placeholder=\"请输入身份证号码\"\r\n\t\t\t\t\t\t\t\t\tplaceholderStyle=\"font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;\" \r\n\t\t\t\t\t\t\t\t\tborder=\"surround\" \r\n\t\t\t\t\t\t\t\t\tname=\"idcard\" v-model=\"idcard\"\r\n\t\t\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>上传身份证头像面</text>\r\n\t\t\t\t\t\t\t\t<view class=\"upload\" @click=\"upIdcardHead\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<image :src=\"idcard_front\"></image>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- \t<text>请保证身份证完整，身份证号清晰</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t\t\t\t<text>上传身份证背面</text>\r\n\t\t\t\t\t\t\t\t<view class=\"upload\" @click=\"upIdcardBack\">\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<image :src=\"idcard_back\"></image>\r\n\t\t\t\t\t\t\t\t<!-- \t<text>请保证身份证完整，身份证号清晰</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"button\" @click=\"formSubmit\">\r\n\t\t\t\t\t\t\t\t<text>提交认证信息</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t<text>根据监管要求身份证照片仅用于实名认证</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n<!-- \t\t\t\t<view class=\"form\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入姓名\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#BBBBBB;font-size:28rpx\" name=\"realname\"\r\n\t\t\t\t\t\t\tv-model=\"realname\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入身份证号码\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#BBBBBB;font-size:28rpx\" name=\"idcard\" v-model=\"idcard\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"set-btn\" form-type=\"submit\"\r\n\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保\r\n\t\t\t\t\t存</button> -->\r\n\t\t\t</form>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\trealname: '',\r\n\t\t\t\tidcard: '',\r\n\t\t\t\ttextset: {},\r\n\t\t\t\thaspwd: 0,\r\n\t\t\t\tidcard_front : '../../static/img/upload.png',\r\n\t\t\t\tidcard_back : '../../static/img/upload.png'\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\t// this.opt = app.getopts(opt);\r\n\t\t\t\r\n\t\t\r\n\t\t\t\r\n\t\t\tthis.loaded();\r\n\t\t\t\r\n\r\n\t\t\tconst _that = this\r\n\t\t\tapp.get('ApiMy/set', {}, function(data) {\r\n\t\t\t\t\r\n\t\t\t\tconsole.log(data)\r\n\t\t\t\t\r\n\t\t\t\tconst user = data.userinfo\r\n\t\t\t\t_that.realname = user.realname\r\n\t\t\t\t_that.idcard = user.usercard || ''\r\n\r\n\t\t\t\tif(user.is_eid_verify){\r\n\t\t\t\t\tapp.goto('/pages/my/seteid');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n               \r\n\t\t\t});\r\n\t\t\t// setTimeout(function() {\r\n\t\t\t// \t// \tapp.goback(true);\r\n\t\t\t// \tapp.goto('/pages/my/seteid');\r\n\t\t\t// }, 2000);\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\t//this.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\tupIdcardHead(){\r\n\t\t\t\t\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\t//console.log(data)\r\n\t\t\t\t\tthat.idcard_front = data[0]\r\n\t\t\t\t})\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tupIdcardBack(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\t//console.log(data)\r\n\t\t\t\t\tthat.idcard_back = data[0]\r\n\t\t\t\t})\t\r\n\t\t\t},\r\n\r\n\t\t\tformSubmit: function() {\r\n\t\t\t\t// var formdata = e.detail.value;\r\n\t\t\t\tvar realname = this.realname\r\n\t\t\t\tvar idcard = this.idcard\r\n\t\t\t\tvar idcard_front = this.idcard_front;\r\n\t\t\t\tvar idcard_back = this.idcard_back;\r\n\t\t\t\tvar is_eid_verify =  this.is_eid_verify;\r\n\t\t\t\t\r\n\t\t\t\tif (realname == '') {\r\n\t\t\t\t\tapp.alert('请输入姓名');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (idcard == '') {\r\n\t\t\t\t\tapp.alert('请输入身份证号码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif(idcard_front == '../../static/img/upload.png'){\r\n\t\t\t\t\tapp.alert('请上传身份证正面');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif(idcard_back== '../../static/img/upload.png'){\r\n\t\t\t\t\tapp.alert('请上传身份证背面');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post(\"ApiMy/setfield2\", {\r\n\t\t\t\t\trealname: realname,\r\n\t\t\t\t\tidcard: idcard,\r\n\t\t\t\t\tidcard_back : idcard_back,\r\n\t\t\t\t\tidcard_front : idcard_front\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tapp.success(data.msg);\r\n\t\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t   app.goto('/pages/my/seteid');\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\r\n\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tapp.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t\r\n\t\t.auth{\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t& text{\r\n\t\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\t\tcolor: #7C7597;\r\n\t\t\t}\r\n\t\t\t.infos{\r\n\t\t\t\t.list{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tpadding: 60rpx 78rpx 0;\r\n\t\t\t\t\t>text{\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #160651;\r\n\t\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\t\tmargin-bottom: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t>input{\r\n\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tpadding-left: 32rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.upload{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tbackground: #F5F5FB;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tpadding: 62rpx 54rpx 30rpx 54rpx;\r\n\t\t\t\t\t\t& image{\r\n\t\t\t\t\t\t\twidth: 248rpx;\r\n\t\t\t\t\t\t\theight: 134rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>text{\r\n\t\t\t\t\t\t\tmargin-top: 38rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.button{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding: 0 82rpx;\r\n\t\t\t\tmargin-top: 78rpx;\r\n\t\t\t\t>text{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 84rpx;\r\n\t\t\t\t\tbackground: #533CD7;\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 30rpx 0rpx rgba(83,60,215,0.4600);\r\n\t\t\t\t\tborder-radius: 43rpx;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 84rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.text{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t.form {\r\n\t\twidth: 94%;\r\n\t\tmargin: 20rpx 3%;\r\n\t\tborder-radius: 5px;\r\n\t\tpadding: 20rpx 20rpx;\r\n\t\tpadding: 0 3%;\r\n\t\tbackground: #FFF;\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tborder-bottom: 1px #ededed solid;\r\n\t\theight: 98rpx;\r\n\t\tline-height: 98rpx;\r\n\t}\r\n\r\n\t.form-item:last-child {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.form-item .label {\r\n\t\tcolor: #000;\r\n\t\twidth: 200rpx;\r\n\t}\r\n\r\n\t.form-item .input {\r\n\t\tflex: 1;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.set-btn {\r\n\t\twidth: 90%;\r\n\t\tmargin: 60rpx 5%;\r\n\t\theight: 96rpx;\r\n\t\tline-height: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimg.vue?vue&type=style&index=0&id=72e079c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./setidcardimg.vue?vue&type=style&index=0&id=72e079c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115071510\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}