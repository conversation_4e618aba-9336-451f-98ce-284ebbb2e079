{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?444e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?0c8b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?5290", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?216d", "uni-app:///pagesExt/cityagent/withdraw.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?137a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/cityagent/withdraw.vue?d88e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "agentInfo", "money", "sysset", "paytype", "tmplids", "accountInfo", "aliaccountname", "aliaccount", "bankcarduser", "bankcardnum", "bankname", "bankaddress", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "getWithdrawSetting", "withdrawmin", "withdrawfee", "withdraw_weixin", "withdraw_aliaccount", "withdraw_bankcard", "loaded", "title", "moneyinput", "changeradio", "formSubmit", "amount", "remark", "account_info", "msg", "console", "goto"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiFjxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACA;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;;MAEA;MACAC;QACAD;QACAE;QAEA;UACAF;UACAA;;UAEA;UACAA;QACA;UACAC;QACA;MACA;IACA;IAEA;IACAE;MACA;MAEAF;QACA;UACA;UACA;UACA;;UAEA;UACAD;YACAI;YACAC;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;UACA;YACArB;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAa;;UAEA;UACAA;QACA;UACA;UACAA;YACAI;YACAC;YACAC;YACAC;YACAC;UACA;UACAR;;UAEA;UACAA;QACA;MACA;QACA;QACAA;UACAI;UACAC;UACAC;UACAC;UACAC;QACA;QACAR;QACAA;MACA;IACA;IAEAS;MACA;MACAP;QACAQ;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAV;MACA;QACAA;MACA;MACA;IACA;IAEA;IACAW;MACA;MACA;MACAZ;IACA;IAEA;IACAa;MACA;MACA;MACA;MAEA;MACA;MACA;QACAZ;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;;MAEA;MACAA;MAEA;QACAa;QACA3B;QACA4B;QACA;QACAC;MACA;MAEAf;QACAA;QAEA;UACA;UACAgB;UACA;YACAA;YACAA;UACA;UACAA;UAEAhB;YACA;YACAA;UACA;QACA;UACAA;QACA;MACA;QACAA;QACAiB;QACAjB;MACA;IACA;IAEA;IACAkB;MACA;MACA;QACAlB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrUA;AAAA;AAAA;AAAA;AAA6kC,CAAgB,yjCAAG,EAAC,C;;;;;;;;;;;ACAjmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/cityagent/withdraw.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/cityagent/withdraw.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdraw.vue?vue&type=template&id=4f34181a&\"\nvar renderjs\nimport script from \"./withdraw.vue?vue&type=script&lang=js&\"\nexport * from \"./withdraw.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdraw.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/cityagent/withdraw.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw.vue?vue&type=template&id=4f34181a&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 =\n    _vm.isload && _vm.sysset.withdraw_weixin == 1 && _vm.paytype == \"微信钱包\"\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload && _vm.sysset.withdraw_aliaccount == 1 && _vm.paytype == \"支付宝\"\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.sysset.withdraw_bankcard == 1 && _vm.paytype == \"银行卡\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"formSubmit\">\n\t\t\t<view class=\"mymoney\" :style=\"{background:t('color1')}\">\n\t\t\t\t<view class=\"f1\">我的可提现余额</view>\n\t\t\t\t<view class=\"f2\"><text style=\"font-size:26rpx\">￥</text>{{(agentInfo && agentInfo.money) || '0.00'}}</view>\n\t\t\t\t<view class=\"f3\" @tap=\"goto\" data-url=\"withdrawlog?st=1\"><text>提现记录</text><text class=\"iconfont iconjiantou\" style=\"font-size:20rpx\"></text></view>\n\t\t\t</view>\n\t\t\t<view class=\"content2\">\n\t\t\t\t<view class=\"item2\"><view class=\"f1\">提现金额(元)</view></view>\n\t\t\t\t<view class=\"item3\"><view class=\"f1\">￥</view><view class=\"f2\"><input class=\"input\" type=\"digit\" name=\"money\" value=\"\" placeholder=\"请输入提现金额\" placeholder-style=\"color:#999;font-size:40rpx\" @input=\"moneyinput\"></input></view></view>\n\t\t\t\t<view class=\"item4\" v-if=\"sysset.withdrawfee>0 || sysset.withdrawmin>0\">\n\t\t\t\t\t<text v-if=\"sysset.withdrawmin>0\" style=\"margin-right:10rpx\">最低提现金额{{sysset.withdrawmin}}元 </text>\n\t\t\t\t\t<text v-if=\"sysset.withdrawfee>0\">提现手续费{{sysset.withdrawfee}}% </text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"withdrawtype\">\n\t\t\t\t<view class=\"f1\">选择提现方式：</view>\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t<view class=\"item\" v-if=\"sysset.withdraw_weixin==1\" @tap.stop=\"changeradio\" data-paytype=\"微信钱包\">\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-weixin.png\"/>微信钱包</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='微信钱包' ? 'background:'+t('color1')+';border:0' :''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_aliaccount==1\" @tap.stop=\"changeradio\" data-paytype=\"支付宝\">\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-alipay.png\"/>支付宝</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='支付宝' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t</label>\n\t\t\t\t\t<label class=\"item\" v-if=\"sysset.withdraw_bankcard==1\" @tap.stop=\"changeradio\" data-paytype=\"银行卡\">\n\t\t\t\t\t\t<view class=\"t1\"><image class=\"img\" src=\"/static/img/withdraw-cash.png\"/>银行卡</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"paytype=='银行卡' ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" src=\"/static/img/checkd.png\"/></view>\n\t\t\t\t\t</label>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 支付宝账号输入 -->\n\t\t\t<view class=\"account-form\" v-if=\"paytype=='支付宝'\">\n\t\t\t\t<view class=\"form\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">账户名</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入支付宝账户名\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.aliaccountname\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">支付宝账号</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入支付宝账号\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.aliaccount\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 银行卡信息输入 -->\n\t\t\t<view class=\"account-form\" v-if=\"paytype=='银行卡'\">\n\t\t\t\t<view class=\"form\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">持卡人姓名</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入持卡人姓名\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.bankcarduser\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">银行卡号</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入银行卡号\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.bankcardnum\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">开户银行</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入开户银行\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.bankname\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"label\">开户地址</view>\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" placeholder=\"请输入开户地址（可选）\" placeholder-style=\"color:#BBBBBB;font-size:28rpx\" v-model=\"accountInfo.bankaddress\"></input>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<button class=\"btn\" :style=\"{background:t('color1')}\" @tap=\"formSubmit\">立即提现</button>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: false,\n\t\t\tisload: false,\n\t\t\tmenuindex: -1,\n\t\t\t\n\t\t\tagentInfo: {},\n\t\t\tmoney: 0,\n\t\t\tsysset: {},\n\t\t\tpaytype: '微信钱包',\n\t\t\ttmplids: [],\n\t\t\t\n\t\t\t// 账号信息\n\t\t\taccountInfo: {\n\t\t\t\t// 支付宝信息\n\t\t\t\taliaccountname: '',\n\t\t\t\taliaccount: '',\n\t\t\t\t// 银行卡信息  \n\t\t\t\tbankcarduser: '',\n\t\t\t\tbankcardnum: '',\n\t\t\t\tbankname: '',\n\t\t\t\tbankaddress: ''\n\t\t\t}\n\t\t};\n\t},\n\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tvar that = this;\n\t\tthis.getdata();\n\t},\n\t\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\t\n\tmethods: {\n\t\t// 获取数据\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\t// 获取代理信息和提现配置\n\t\t\tapp.post('ApiCityAgent/getAgentInfo', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.agentInfo = res.agentinfo;\n\t\t\t\t\tthat.tmplids = [];\n\t\t\t\t\t\n\t\t\t\t\t// 获取提现配置（异步调用，配置获取完成后才显示页面）\n\t\t\t\t\tthat.getWithdrawSetting();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取提现配置\n\t\tgetWithdrawSetting: function () {\n\t\t\tvar that = this;\n\t\t\t\n\t\t\tapp.post('ApiCityAgent/getWithdrawSetting', {}, function (res) {\n\t\t\t\tif (res && res.status == 1) {\n\t\t\t\t\t// 检查返回数据结构\n\t\t\t\t\tvar withdraw_setting = res.withdraw_setting || {};\n\t\t\t\t\tvar convert_setting = res.convert_setting || {};\n\t\t\t\t\t\n\t\t\t\t\t// 使用后台真实配置，如果withdraw_setting不存在，尝试使用convert_setting\n\t\t\t\t\tthat.sysset = {\n\t\t\t\t\t\twithdrawmin: withdraw_setting.withdraw_min || convert_setting.min_convert_amount || 0,\n\t\t\t\t\t\twithdrawfee: withdraw_setting.withdraw_fee || convert_setting.convert_fee_rate || 0,\n\t\t\t\t\t\twithdraw_weixin: withdraw_setting.withdraw_weixin !== undefined ? withdraw_setting.withdraw_weixin : 1,\n\t\t\t\t\t\twithdraw_aliaccount: withdraw_setting.withdraw_aliaccount !== undefined ? withdraw_setting.withdraw_aliaccount : 1,\n\t\t\t\t\t\twithdraw_bankcard: withdraw_setting.withdraw_bankcard !== undefined ? withdraw_setting.withdraw_bankcard : 1\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 设置默认提现方式\n\t\t\t\t\tvar paytype = '微信钱包';\n\t\t\t\t\tif (that.sysset.withdraw_weixin == 1) {\n\t\t\t\t\t\tpaytype = '微信钱包';\n\t\t\t\t\t}\n\t\t\t\t\tif (!that.sysset.withdraw_weixin || that.sysset.withdraw_weixin == 0) {\n\t\t\t\t\t\tpaytype = '支付宝';\n\t\t\t\t\t}\n\t\t\t\t\tif ((!that.sysset.withdraw_weixin || that.sysset.withdraw_weixin == 0) && (!that.sysset.withdraw_aliaccount || that.sysset.withdraw_aliaccount == 0)) {\n\t\t\t\t\t\tpaytype = '银行卡';\n\t\t\t\t\t}\n\t\t\t\t\tthat.paytype = paytype;\n\t\t\t\t\t\n\t\t\t\t\t// 配置获取完成后才显示页面\n\t\t\t\t\tthat.loaded();\n\t\t\t\t} else {\n\t\t\t\t\t// 如果获取失败，使用默认配置\n\t\t\t\t\tthat.sysset = {\n\t\t\t\t\t\twithdrawmin: 0,\n\t\t\t\t\t\twithdrawfee: 0,\n\t\t\t\t\t\twithdraw_weixin: 1,\n\t\t\t\t\t\twithdraw_aliaccount: 1,\n\t\t\t\t\t\twithdraw_bankcard: 1\n\t\t\t\t\t};\n\t\t\t\t\tthat.paytype = '微信钱包';\n\t\t\t\t\t\n\t\t\t\t\t// 即使失败也要显示页面\n\t\t\t\t\tthat.loaded();\n\t\t\t\t}\n\t\t\t}, function(error) {\n\t\t\t\t// 处理网络错误\n\t\t\t\tthat.sysset = {\n\t\t\t\t\twithdrawmin: 0,\n\t\t\t\t\twithdrawfee: 0,\n\t\t\t\t\twithdraw_weixin: 1,\n\t\t\t\t\twithdraw_aliaccount: 1,\n\t\t\t\t\twithdraw_bankcard: 1\n\t\t\t\t};\n\t\t\t\tthat.paytype = '微信钱包';\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\t\n\t\tloaded: function() {\n\t\t\tthis.isload = true;\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: '余额提现'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 金额输入处理\n\t\tmoneyinput: function (e) {\n\t\t\tvar usermoney = parseFloat((this.agentInfo && this.agentInfo.money) || 0);\n\t\t\tvar money = parseFloat(e.detail.value);\n\t\t\tif (money < 0) {\n\t\t\t\tapp.error('必须大于0');\n\t\t\t} else if (money > usermoney) {\n\t\t\t\tapp.error('可提现余额不足');\n\t\t\t}\n\t\t\tthis.money = money;\n\t\t},\n\t\t\n\t\t// 选择提现方式\n\t\tchangeradio: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar paytype = e.currentTarget.dataset.paytype;\n\t\t\tthat.paytype = paytype;\n\t\t},\n\t\t\n\t\t// 表单提交\n\t\tformSubmit: function () {\n\t\t\tvar that = this;\n\t\t\tvar usermoney = parseFloat((this.agentInfo && this.agentInfo.money) || 0);\n\t\t\tvar withdrawmin = parseFloat(this.sysset.withdrawmin || 0);\n\t\t\t\n\t\t\tvar money = parseFloat(that.money);\n\t\t\tvar paytype = this.paytype;\n\t\t\tif (isNaN(money) || money <= 0) {\n\t\t\t\tapp.error('提现金额必须大于0');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (withdrawmin > 0 && money < withdrawmin) {\n\t\t\t\tapp.error('提现金额必须大于¥' + withdrawmin);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (money > usermoney) {\n\t\t\t\tapp.error('余额不足');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 验证账号信息\n\t\t\tif (paytype == '支付宝') {\n\t\t\t\tif (!this.accountInfo.aliaccount || this.accountInfo.aliaccount.trim() == '') {\n\t\t\t\t\tapp.error('请输入支付宝账号');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.accountInfo.aliaccountname || this.accountInfo.aliaccountname.trim() == '') {\n\t\t\t\t\tapp.error('请输入支付宝账户名');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (paytype == '银行卡') {\n\t\t\t\tif (!this.accountInfo.bankcardnum || this.accountInfo.bankcardnum.trim() == '') {\n\t\t\t\t\tapp.error('请输入银行卡号');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.accountInfo.bankcarduser || this.accountInfo.bankcarduser.trim() == '') {\n\t\t\t\t\tapp.error('请输入持卡人姓名');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.accountInfo.bankname || this.accountInfo.bankname.trim() == '') {\n\t\t\t\t\tapp.error('请输入开户银行');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 提交提现申请\n\t\t\tapp.showLoading('提交中');\n\t\t\t\n\t\t\tvar params = {\n\t\t\t\tamount: money,\n\t\t\t\tpaytype: paytype,\n\t\t\t\tremark: '城市代理提现申请',\n\t\t\t\t// 添加账号信息\n\t\t\t\taccount_info: this.accountInfo\n\t\t\t};\n\t\t\t\n\t\t\tapp.post('ApiCityAgent/applyWithdraw', params, function (res) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\t\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tvar msg = '提现申请成功！\\n';\n\t\t\t\t\tmsg += '申请金额：¥' + res.data.amount + '\\n';\n\t\t\t\t\tif (parseFloat(res.data.fee) > 0) {\n\t\t\t\t\t\tmsg += '手续费：¥' + res.data.fee + '\\n';\n\t\t\t\t\t\tmsg += '实际到账：¥' + res.data.actual_amount + '\\n';\n\t\t\t\t\t}\n\t\t\t\t\tmsg += '请等待审核...';\n\t\t\t\t\t\n\t\t\t\t\tapp.alert(msg, function() {\n\t\t\t\t\t\t// 跳转到提现记录页面\n\t\t\t\t\t\tapp.goto('/pagesExt/cityagent/withdrawlog?st=1');\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t}, function(error) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tconsole.log('提现申请失败:', error);\n\t\t\t\tapp.error('网络错误，请重试');\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 页面跳转\n\t\tgoto: function (e) {\n\t\t\tvar url = e.currentTarget.dataset.url;\n\t\t\tif (url) {\n\t\t\t\tapp.goto('/pagesExt/cityagent/' + url);\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container{display:flex;flex-direction:column}\n.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}\n.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}\n.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}\n.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}\n\n/* 账号信息表单样式 */\n.account-form{width:94%;margin:20rpx 3%;border-radius:10rpx;background:#fff;}\n.account-form .form{padding:0 3%;}\n.account-form .form-item{display:flex;align-items:center;width:100%;border-bottom:1px solid #ededed;height:98rpx;line-height:98rpx;}\n.account-form .form-item:last-child{border:0}\n.account-form .form-item .label{color:#000;width:200rpx;font-size:30rpx;}\n.account-form .form-item .input{flex:1;color:#000;font-size:30rpx;}\n\n.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}\n.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}\n.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}\n.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}\n\n.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}\n.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}\n\n.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}\n.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}\n.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}\n.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}\n.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}\n\n.withdrawtype{width:94%;margin:20rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}\n.withdrawtype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}\n\n.withdrawtype .f2{padding:0 30rpx}\n.withdrawtype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}\n.withdrawtype .f2 .item:last-child{border-bottom:0}\n.withdrawtype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#333}\n.withdrawtype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}\n\n.withdrawtype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}\n.withdrawtype .f2 .item .radio .radio-img{width:100%;height:100%}\n\n.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115103019\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}