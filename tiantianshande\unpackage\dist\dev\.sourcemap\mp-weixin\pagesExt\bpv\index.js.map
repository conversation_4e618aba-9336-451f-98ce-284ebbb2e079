{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?f675", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?e226", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?84a8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?ceaf", "uni-app:///pagesExt/bpv/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?a45f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/bpv/index.vue?f079"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "isload", "current_price", "exchange_ratio", "current_amount", "contribution", "exchange_deadline", "bonus_pool", "target_amount", "is_exchangeable", "exchange_deadline_format", "showExchangePopup", "showCashoutPopup", "exchangeAmount", "cashoutAmount", "activeTab", "exchangeRecords", "cashoutRecords", "exchangePagenum", "cashoutPagenum", "exchangeNomore", "cashoutNomore", "pageSize", "computed", "progressPercent", "needContribution", "cashoutMoney", "canExchange", "canCashout", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getData", "that", "app", "uni", "title", "icon", "duration", "resetRecords", "getExchangeRecords", "page", "limit", "getCashoutRecords", "switchTab", "exchangeYellowPoints", "msg", "type", "confirmExchange", "amount", "setTimeout", "cashoutYellowPoints", "confirmCashout", "closeExchangePopup", "closeCashoutPopup", "calcContribution", "calcCashoutMoney", "Math", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAA0vB,CAAgB,qwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmL9wB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MAEAC;QACAD;QACAE;QAEA;UACA;UACAF;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACA;YACAA;UACA;UAEAA;;UAEA;UACAA;UACAA;UACAA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAP;MAEA;QACAA;QACAA;QACAA;MACA;MAEAC;QACAO;QACAC;MACA;QACAT;QAEA;UACA;UAEA;YACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACAV;MAEA;QACAA;QACAA;QACAA;MACA;MAEAC;QACAO;QACAC;MACA;QACAT;QAEA;UACA;UAEA;YACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAM;MACA;IACA;IAEA;IACAC;MACA;QACA;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACAf;MAEAC;QACAe;MACA;QACAhB;QAEA;UACA;UACAE;YACAC;YACAC;YACAC;UACA;;UAEA;UACAL;;UAEA;UACAiB;YACAjB;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAa;MACA;QACA;UACAL;UACAC;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAK;MACA;MAEA;MACAnB;MAEAC;QACAe;MACA;QACAhB;QAEA;UACA;UACAE;YACAC;YACAC;YACAC;UACA;;UAEA;UACAL;;UAEA;UACAiB;YACAjB;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAe;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA,8BACA,qBACAC,+CACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClhBA;AAAA;AAAA;AAAA;AAA0kC,CAAgB,sjCAAG,EAAC,C;;;;;;;;;;;ACA9lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/bpv/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/bpv/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0d8878b2&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/bpv/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0d8878b2&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"黄积分\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m3 = _vm.isload ? _vm.t(\"黄积分\") : null\n  var m4 = _vm.isload ? _vm.t(\"元\") : null\n  var m5 = _vm.isload ? _vm.t(\"现金券\") : null\n  var m6 = _vm.isload ? _vm.t(\"现金券\") : null\n  var m7 = _vm.isload ? _vm.t(\"黄积分\") : null\n  var m8 = _vm.isload ? _vm.t(\"元\") : null\n  var m9 = _vm.isload ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload ? _vm.t(\"color1\") : null\n  var m11 = _vm.isload ? _vm.t(\"黄积分\") : null\n  var m12 = _vm.isload ? _vm.t(\"color1\") : null\n  var m13 = _vm.isload ? _vm.t(\"黄积分\") : null\n  var m14 = _vm.isload && _vm.activeTab === \"exchange\" ? _vm.t(\"color1\") : null\n  var m15 = _vm.isload && _vm.activeTab === \"exchange\" ? _vm.t(\"color1\") : null\n  var m16 = _vm.isload && _vm.activeTab === \"cashout\" ? _vm.t(\"color1\") : null\n  var m17 = _vm.isload && _vm.activeTab === \"cashout\" ? _vm.t(\"color1\") : null\n  var g0 =\n    _vm.isload && _vm.activeTab === \"exchange\"\n      ? _vm.exchangeRecords.length\n      : null\n  var l0 =\n    _vm.isload && _vm.activeTab === \"exchange\" && g0 > 0\n      ? _vm.__map(_vm.exchangeRecords, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m18 = _vm.t(\"黄积分\")\n          var m19 = _vm.t(\"黄积分\")\n          var m20 = _vm.t(\"现金券\")\n          return {\n            $orig: $orig,\n            m18: m18,\n            m19: m19,\n            m20: m20,\n          }\n        })\n      : null\n  var g1 =\n    _vm.isload && _vm.activeTab === \"exchange\"\n      ? _vm.exchangeRecords.length\n      : null\n  var g2 =\n    _vm.isload && _vm.activeTab === \"cashout\" ? _vm.cashoutRecords.length : null\n  var l1 =\n    _vm.isload && _vm.activeTab === \"cashout\" && g2 > 0\n      ? _vm.__map(_vm.cashoutRecords, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m21 = _vm.t(\"黄积分\")\n          var m22 = _vm.t(\"黄积分\")\n          var m23 = _vm.t(\"元\")\n          return {\n            $orig: $orig,\n            m21: m21,\n            m22: m22,\n            m23: m23,\n          }\n        })\n      : null\n  var g3 =\n    _vm.isload && _vm.activeTab === \"cashout\" ? _vm.cashoutRecords.length : null\n  var m24 = _vm.showExchangePopup ? _vm.t(\"黄积分\") : null\n  var m25 = _vm.showExchangePopup ? _vm.t(\"现金券\") : null\n  var m26 = _vm.showExchangePopup ? _vm.t(\"现金券\") : null\n  var m27 = _vm.showExchangePopup ? _vm.t(\"黄积分\") : null\n  var m28 = _vm.showExchangePopup ? _vm.t(\"现金券\") : null\n  var m29 = _vm.showExchangePopup ? _vm.t(\"color1\") : null\n  var m30 = _vm.showCashoutPopup ? _vm.t(\"黄积分\") : null\n  var m31 = _vm.showCashoutPopup ? _vm.t(\"黄积分\") : null\n  var m32 = _vm.showCashoutPopup ? _vm.t(\"元\") : null\n  var m33 = _vm.showCashoutPopup ? _vm.t(\"黄积分\") : null\n  var m34 = _vm.showCashoutPopup ? _vm.t(\"元\") : null\n  var m35 = _vm.showCashoutPopup ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        l1: l1,\n        g3: g3,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"yellow-points-header\">\r\n\t\t\t<view class=\"title\">我的{{t('黄积分')}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"yellow-points-card\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t<view class=\"card-item\">\r\n\t\t\t\t<view class=\"label\">当前持有</view>\r\n\t\t\t\t<view class=\"value\">{{current_amount}} {{t('黄积分')}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card-item\">\r\n\t\t\t\t<view class=\"label\">当前价格</view>\r\n\t\t\t\t<view class=\"value\">{{current_price}} {{t('元')}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card-item\">\r\n\t\t\t\t<view class=\"label\">我的{{t('现金券')}}</view>\r\n\t\t\t\t<view class=\"value\">{{contribution}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"yellow-points-info\">\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<view class=\"info-label\">兑换比例</view>\r\n\t\t\t\t<view class=\"info-value\">{{exchange_ratio}} {{t('现金券')}} = 1 {{t('黄积分')}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\" v-if=\"exchange_deadline > 0\">\r\n\t\t\t\t<view class=\"info-label\">兑换截止时间</view>\r\n\t\t\t\t<view class=\"info-value\">{{exchange_deadline_format}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"yellow-points-progress\">\r\n\t\t\t<view class=\"progress-title\">\r\n\t\t\t\t<span>奖金池进度</span>\r\n\t\t\t\t<span>{{bonus_pool}}/{{target_amount}}{{t('元')}}</span>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t<view class=\"progress-inner\" :style=\"{width: progressPercent+'%', background:t('color1')}\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"yellow-points-actions\">\r\n\t\t\t<button class=\"btn-exchange\" :style=\"{background:t('color1')}\" @tap=\"exchangeYellowPoints\" :disabled=\"!is_exchangeable\">兑换{{t('黄积分')}}</button>\r\n\t\t\t<button class=\"btn-cashout\" :style=\"{background:t('color1')}\" @tap=\"cashoutYellowPoints\">提现{{t('黄积分')}}</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"yellow-points-records\">\r\n\t\t\t<view class=\"record-tabs\">\r\n\t\t\t\t<view :class=\"'tab-item ' + (activeTab === 'exchange' ? 'active' : '')\" \r\n\t\t\t\t\t:style=\"activeTab === 'exchange' ? 'color:'+t('color1')+';border-bottom-color:'+t('color1') : ''\" \r\n\t\t\t\t\t@tap=\"switchTab('exchange')\">兑换记录</view>\r\n\t\t\t\t<view :class=\"'tab-item ' + (activeTab === 'cashout' ? 'active' : '')\" \r\n\t\t\t\t\t:style=\"activeTab === 'cashout' ? 'color:'+t('color1')+';border-bottom-color:'+t('color1') : ''\" \r\n\t\t\t\t\t@tap=\"switchTab('cashout')\">提现记录</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 兑换记录 -->\r\n\t\t\t<view class=\"record-list\" v-if=\"activeTab === 'exchange'\">\r\n\t\t\t\t<block v-if=\"exchangeRecords.length > 0\">\r\n\t\t\t\t\t<view class=\"record-item\" v-for=\"(item, index) in exchangeRecords\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"record-left\">\r\n\t\t\t\t\t\t\t<view class=\"record-title\">{{t('黄积分')}}兑换</view>\r\n\t\t\t\t\t\t\t<view class=\"record-time\">{{item.exchange_time_format}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"record-right\">\r\n\t\t\t\t\t\t\t<view class=\"record-amount\">+{{item.amount}} {{t('黄积分')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"record-desc\">消耗{{t('现金券')}}: {{item.contribution}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<nodata v-if=\"exchangeRecords.length === 0\"></nodata>\r\n\t\t\t\t<nomore v-if=\"exchangeNomore\"></nomore>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 提现记录 -->\r\n\t\t\t<view class=\"record-list\" v-if=\"activeTab === 'cashout'\">\r\n\t\t\t\t<block v-if=\"cashoutRecords.length > 0\">\r\n\t\t\t\t\t<view class=\"record-item\" v-for=\"(item, index) in cashoutRecords\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"record-left\">\r\n\t\t\t\t\t\t\t<view class=\"record-title\">{{t('黄积分')}}提现</view>\r\n\t\t\t\t\t\t\t<view class=\"record-time\">{{item.cashout_time_format}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"record-right\">\r\n\t\t\t\t\t\t\t<view class=\"record-amount\">-{{item.amount}} {{t('黄积分')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"record-desc\">\r\n\t\t\t\t\t\t\t\t<text>提现金额: {{item.money}}{{t('元')}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"status\" :class=\"'status-' + item.status\">{{item.status_text}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<nodata v-if=\"cashoutRecords.length === 0\"></nodata>\r\n\t\t\t\t<nomore v-if=\"cashoutNomore\"></nomore>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!-- 兑换弹窗 -->\r\n\t<view class=\"popup-mask\" v-if=\"showExchangePopup\" @tap=\"closeExchangePopup\"></view>\r\n\t<view class=\"exchange-popup\" v-if=\"showExchangePopup\">\r\n\t\t<view class=\"popup-header\">\r\n\t\t\t<text class=\"popup-title\">兑换{{t('黄积分')}}</text>\r\n\t\t\t<text class=\"popup-close\" @tap=\"closeExchangePopup\">×</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"popup-body\">\r\n\t\t\t<view class=\"info-row\">\r\n\t\t\t\t<text>当前{{t('现金券')}}:</text>\r\n\t\t\t\t<text>{{contribution}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-row\">\r\n\t\t\t\t<text>兑换比例:</text>\r\n\t\t\t\t<text>{{exchange_ratio}} {{t('现金券')}} = 1 {{t('黄积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-row\">\r\n\t\t\t\t<text>兑换数量:</text>\r\n\t\t\t\t<input type=\"number\" v-model=\"exchangeAmount\" min=\"1\" @input=\"calcContribution\"/>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-row highlight\">\r\n\t\t\t\t<text>需要{{t('现金券')}}:</text>\r\n\t\t\t\t<text>{{needContribution}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"popup-footer\">\r\n\t\t\t<button class=\"btn-cancel\" @tap=\"closeExchangePopup\">取消</button>\r\n\t\t\t<button class=\"btn-confirm\" :style=\"{background:t('color1')}\" @tap=\"confirmExchange\" :disabled=\"!canExchange\">确认兑换</button>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<!-- 提现弹窗 -->\r\n\t<view class=\"popup-mask\" v-if=\"showCashoutPopup\" @tap=\"closeCashoutPopup\"></view>\r\n\t<view class=\"cashout-popup\" v-if=\"showCashoutPopup\">\r\n\t\t<view class=\"popup-header\">\r\n\t\t\t<text class=\"popup-title\">提现{{t('黄积分')}}</text>\r\n\t\t\t<text class=\"popup-close\" @tap=\"closeCashoutPopup\">×</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"popup-body\">\r\n\t\t\t<view class=\"info-row\">\r\n\t\t\t\t<text>当前持有:</text>\r\n\t\t\t\t<text>{{current_amount}} {{t('黄积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-row\">\r\n\t\t\t\t<text>当前价格:</text>\r\n\t\t\t\t<text>{{current_price}} {{t('元')}}/{{t('黄积分')}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-row\">\r\n\t\t\t\t<text>提现数量:</text>\r\n\t\t\t\t<input type=\"number\" v-model=\"cashoutAmount\" min=\"1\" :max=\"current_amount\" @input=\"calcCashoutMoney\"/>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-row highlight\">\r\n\t\t\t\t<text>提现金额:</text>\r\n\t\t\t\t<text>{{cashoutMoney}} {{t('元')}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"note\">\r\n\t\t\t\t<text>提示: 提现申请需要管理员审核，审核通过后将发放到您的账户</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"popup-footer\">\r\n\t\t\t<button class=\"btn-cancel\" @tap=\"closeCashoutPopup\">取消</button>\r\n\t\t\t<button class=\"btn-confirm\" :style=\"{background:t('color1')}\" @tap=\"confirmCashout\" :disabled=\"!canCashout\">确认提现</button>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tisload: false,\r\n\t\t\t\r\n\t\t\t// 黄积分数据\r\n\t\t\tcurrent_price: 0,\r\n\t\t\texchange_ratio: 0,\r\n\t\t\tcurrent_amount: 0,\r\n\t\t\tcontribution: 0,\r\n\t\t\texchange_deadline: 0,\r\n\t\t\tbonus_pool: 0,\r\n\t\t\ttarget_amount: 0,\r\n\t\t\tis_exchangeable: 0,\r\n\t\t\texchange_deadline_format: '',\r\n\t\t\t\r\n\t\t\t// 弹窗控制\r\n\t\t\tshowExchangePopup: false,\r\n\t\t\tshowCashoutPopup: false,\r\n\t\t\t\r\n\t\t\t// 表单数据\r\n\t\t\texchangeAmount: 1,\r\n\t\t\tcashoutAmount: 1,\r\n\t\t\t\r\n\t\t\t// 分页数据\r\n\t\t\tactiveTab: 'exchange',\r\n\t\t\texchangeRecords: [],\r\n\t\t\tcashoutRecords: [],\r\n\t\t\texchangePagenum: 1,\r\n\t\t\tcashoutPagenum: 1,\r\n\t\t\texchangeNomore: false,\r\n\t\t\tcashoutNomore: false,\r\n\t\t\tpageSize: 10\r\n\t\t}\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\tprogressPercent() {\r\n\t\t\tif (this.target_amount <= 0) return 0;\r\n\t\t\tconst percent = (this.bonus_pool / this.target_amount) * 100;\r\n\t\t\treturn Math.min(percent, 100);\r\n\t\t},\r\n\t\tneedContribution() {\r\n\t\t\treturn this.exchangeAmount * this.exchange_ratio;\r\n\t\t},\r\n\t\tcashoutMoney() {\r\n\t\t\treturn (this.cashoutAmount * this.current_price).toFixed(2);\r\n\t\t},\r\n\t\tcanExchange() {\r\n\t\t\treturn this.exchangeAmount > 0 && this.needContribution <= this.contribution && this.is_exchangeable;\r\n\t\t},\r\n\t\tcanCashout() {\r\n\t\t\treturn this.cashoutAmount > 0 && this.cashoutAmount <= this.current_amount;\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tthis.getData();\r\n\t},\r\n\t\r\n\tonPullDownRefresh() {\r\n\t\tthis.getData();\r\n\t},\r\n\t\r\n\tonReachBottom() {\r\n\t\tif (this.activeTab === 'exchange' && !this.exchangeNomore) {\r\n\t\t\tthis.exchangePagenum++;\r\n\t\t\tthis.getExchangeRecords(true);\r\n\t\t} else if (this.activeTab === 'cashout' && !this.cashoutNomore) {\r\n\t\t\tthis.cashoutPagenum++;\r\n\t\t\tthis.getCashoutRecords(true);\r\n\t\t}\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t// 获取黄积分数据\r\n\t\tgetData() {\r\n\t\t\tconst that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.get('ApiBpv/getData', {}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1) {\r\n\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\tthat.current_price = data.current_price;\r\n\t\t\t\t\tthat.exchange_ratio = data.exchange_ratio;\r\n\t\t\t\t\tthat.current_amount = data.current_amount;\r\n\t\t\t\t\tthat.contribution = data.contribution;\r\n\t\t\t\t\tthat.exchange_deadline = data.exchange_deadline;\r\n\t\t\t\t\tthat.bonus_pool = data.bonus_pool;\r\n\t\t\t\t\tthat.target_amount = data.target_amount;\r\n\t\t\t\t\tthat.is_exchangeable = data.is_exchangeable;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 格式化日期\r\n\t\t\t\t\tif (that.exchange_deadline > 0) {\r\n\t\t\t\t\t\tconst date = new Date(that.exchange_deadline * 1000);\r\n\t\t\t\t\t\tthat.exchange_deadline_format = that.formatDate(date);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 加载记录\r\n\t\t\t\t\tthat.resetRecords();\r\n\t\t\t\t\tthat.getExchangeRecords();\r\n\t\t\t\t\tthat.getCashoutRecords();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取数据失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 重置记录数据\r\n\t\tresetRecords() {\r\n\t\t\tthis.exchangePagenum = 1;\r\n\t\t\tthis.cashoutPagenum = 1;\r\n\t\t\tthis.exchangeRecords = [];\r\n\t\t\tthis.cashoutRecords = [];\r\n\t\t\tthis.exchangeNomore = false;\r\n\t\t\tthis.cashoutNomore = false;\r\n\t\t},\r\n\t\t\r\n\t\t// 获取兑换记录\r\n\t\tgetExchangeRecords(loadmore) {\r\n\t\t\tconst that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tif (!loadmore) {\r\n\t\t\t\tthat.exchangePagenum = 1;\r\n\t\t\t\tthat.exchangeRecords = [];\r\n\t\t\t\tthat.exchangeNomore = false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiBpv/exchangeRecords', {\r\n\t\t\t\tpage: that.exchangePagenum,\r\n\t\t\t\tlimit: that.pageSize\r\n\t\t\t}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1) {\r\n\t\t\t\t\tconst records = res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (that.exchangePagenum === 1) {\r\n\t\t\t\t\t\tthat.exchangeRecords = records;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (records.length === 0) {\r\n\t\t\t\t\t\t\tthat.exchangeNomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.exchangeRecords = that.exchangeRecords.concat(records);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取兑换记录失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取提现记录\r\n\t\tgetCashoutRecords(loadmore) {\r\n\t\t\tconst that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tif (!loadmore) {\r\n\t\t\t\tthat.cashoutPagenum = 1;\r\n\t\t\t\tthat.cashoutRecords = [];\r\n\t\t\t\tthat.cashoutNomore = false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiBpv/cashoutRecords', {\r\n\t\t\t\tpage: that.cashoutPagenum,\r\n\t\t\t\tlimit: that.pageSize\r\n\t\t\t}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1) {\r\n\t\t\t\t\tconst records = res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (that.cashoutPagenum === 1) {\r\n\t\t\t\t\t\tthat.cashoutRecords = records;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (records.length === 0) {\r\n\t\t\t\t\t\t\tthat.cashoutNomore = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.cashoutRecords = that.cashoutRecords.concat(records);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取提现记录失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换标签页\r\n\t\tswitchTab(tab) {\r\n\t\t\tthis.activeTab = tab;\r\n\t\t},\r\n\t\t\r\n\t\t// 兑换黄积分\r\n\t\texchangeYellowPoints() {\r\n\t\t\tif (!this.is_exchangeable) {\r\n\t\t\t\tthis.$refs.popmsg.show({\r\n\t\t\t\t\tmsg: '当前不可兑换' + this.t('黄积分'),\r\n\t\t\t\t\ttype: 'warning'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.showExchangePopup = true;\r\n\t\t\tthis.exchangeAmount = 1;\r\n\t\t},\r\n\t\t\r\n\t\t// 确认兑换\r\n\t\tconfirmExchange() {\r\n\t\t\tif (!this.canExchange) return;\r\n\t\t\t\r\n\t\t\tconst that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.post('ApiBpv/exchange', {\r\n\t\t\t\tamount: that.exchangeAmount\r\n\t\t\t}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1) {\r\n\t\t\t\t\t// 使用uni.showToast确保提示显示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '兑换成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 关闭弹窗\r\n\t\t\t\t\tthat.closeExchangePopup();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 延迟刷新数据，确保提示显示完成\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.getData();\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '兑换失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 提现黄积分\r\n\t\tcashoutYellowPoints() {\r\n\t\t\tif (this.current_amount <= 0) {\r\n\t\t\t\tthis.$refs.popmsg.show({\r\n\t\t\t\t\tmsg: '当前没有可提现的' + this.t('黄积分'),\r\n\t\t\t\t\ttype: 'warning'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.showCashoutPopup = true;\r\n\t\t\tthis.cashoutAmount = 1;\r\n\t\t},\r\n\t\t\r\n\t\t// 确认提现\r\n\t\tconfirmCashout() {\r\n\t\t\tif (!this.canCashout) return;\r\n\t\t\t\r\n\t\t\tconst that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tapp.post('ApiBpv/cashout', {\r\n\t\t\t\tamount: that.cashoutAmount\r\n\t\t\t}, function(res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1) {\r\n\t\t\t\t\t// 使用uni.showToast确保提示显示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '提现申请成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 关闭弹窗\r\n\t\t\t\t\tthat.closeCashoutPopup();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 延迟刷新数据，确保提示显示完成\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.getData();\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '提现申请失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭兑换弹窗\r\n\t\tcloseExchangePopup() {\r\n\t\t\tthis.showExchangePopup = false;\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭提现弹窗\r\n\t\tcloseCashoutPopup() {\r\n\t\t\tthis.showCashoutPopup = false;\r\n\t\t},\r\n\t\t\r\n\t\t// 计算需要的现金券\r\n\t\tcalcContribution() {\r\n\t\t\t// 限制输入为正整数\r\n\t\t\tthis.exchangeAmount = Math.max(1, parseInt(this.exchangeAmount) || 0);\r\n\t\t},\r\n\t\t\r\n\t\t// 计算提现金额\r\n\t\tcalcCashoutMoney() {\r\n\t\t\t// 限制输入为正整数且不超过持有量\r\n\t\t\tthis.cashoutAmount = Math.min(\r\n\t\t\t\tthis.current_amount,\r\n\t\t\t\tMath.max(1, parseInt(this.cashoutAmount) || 0)\r\n\t\t\t);\r\n\t\t},\r\n\t\t\r\n\t\t// 日期格式化\r\n\t\tformatDate(date) {\r\n\t\t\tconst year = date.getFullYear();\r\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\r\n\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}`;\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.yellow-points-header {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.yellow-points-header .title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.yellow-points-card {\r\n\tmargin: 0 30rpx;\r\n\tpadding: 30rpx;\r\n\tborder-radius: 16rpx;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.card-item {\r\n\ttext-align: center;\r\n}\r\n\r\n.card-item .label {\r\n\tfont-size: 26rpx;\r\n\topacity: 0.8;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.card-item .value {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.yellow-points-info {\r\n\tmargin: 30rpx;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground-color: #f9f9f9;\r\n}\r\n\r\n.info-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 15rpx 0;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.yellow-points-progress {\r\n\tmargin: 30rpx;\r\n}\r\n\r\n.progress-title {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tfont-size: 28rpx;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.progress-bar {\r\n\theight: 18rpx;\r\n\tbackground-color: #f1f1f1;\r\n\tborder-radius: 9rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.progress-inner {\r\n\theight: 100%;\r\n\tborder-radius: 9rpx;\r\n}\r\n\r\n.yellow-points-actions {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin: 30rpx;\r\n}\r\n\r\n.btn-exchange, .btn-cashout {\r\n\twidth: 45%;\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\tpadding: 18rpx 0;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\nbutton[disabled] {\r\n\tbackground-color: #cccccc !important;\r\n\tcolor: #ffffff !important;\r\n}\r\n\r\n.yellow-points-records {\r\n\tmargin: 30rpx;\r\n}\r\n\r\n.record-tabs {\r\n\tdisplay: flex;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 28rpx;\r\n\tposition: relative;\r\n\tborder-bottom: 4rpx solid transparent;\r\n}\r\n\r\n.tab-item.active {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.record-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1rpx solid #f2f2f2;\r\n}\r\n\r\n.record-title {\r\n\tfont-size: 28rpx;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.record-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.record-amount {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\ttext-align: right;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.record-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\ttext-align: right;\r\n}\r\n\r\n.status {\r\n\tmargin-left: 10rpx;\r\n\tpadding: 2rpx 10rpx;\r\n\tborder-radius: 6rpx;\r\n\tfont-size: 22rpx;\r\n}\r\n\r\n.status-0 {\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #666;\r\n}\r\n\r\n.status-1 {\r\n\tbackground-color: #e1f3d8;\r\n\tcolor: #67c23a;\r\n}\r\n\r\n.status-2 {\r\n\tbackground-color: #fef0f0;\r\n\tcolor: #f56c6c;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.popup-mask {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tz-index: 999;\r\n}\r\n\r\n.exchange-popup, .cashout-popup {\r\n\tposition: fixed;\r\n\tleft: 50%;\r\n\ttop: 50%;\r\n\ttransform: translate(-50%, -50%);\r\n\twidth: 85%;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tz-index: 1000;\r\n\toverflow: hidden;\r\n}\r\n\r\n.popup-header {\r\n\tposition: relative;\r\n\tpadding: 30rpx;\r\n\ttext-align: center;\r\n\tborder-bottom: 1rpx solid #f2f2f2;\r\n}\r\n\r\n.popup-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.popup-close {\r\n\tposition: absolute;\r\n\tright: 30rpx;\r\n\ttop: 30rpx;\r\n\tfont-size: 36rpx;\r\n\tline-height: 1;\r\n}\r\n\r\n.popup-body {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.info-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 20rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.highlight {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.input-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.input-row input {\r\n\twidth: 60%;\r\n\tborder: 1rpx solid #ddd;\r\n\tborder-radius: 6rpx;\r\n\tpadding: 10rpx 20rpx;\r\n\ttext-align: right;\r\n}\r\n\r\n.note {\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f9f9f9;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.popup-footer {\r\n\tdisplay: flex;\r\n\tborder-top: 1rpx solid #f2f2f2;\r\n}\r\n\r\n.btn-cancel, .btn-confirm {\r\n\tflex: 1;\r\n\tpadding: 20rpx 0;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.btn-cancel {\r\n\tbackground-color: #f5f5f5;\r\n\tcolor: #666;\r\n}\r\n\r\n.btn-confirm {\r\n\tcolor: #fff;\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115093109\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}