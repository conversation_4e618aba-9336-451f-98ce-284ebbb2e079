{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?4ab9", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?e927", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?9569", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?f46d", "uni-app:///shopPackage/shop/prolist.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?c316", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/prolist.vue?ad06"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "nomore", "nodata", "keyword", "pagenum", "datalist", "history_list", "history_show", "order", "field", "oldcid", "<PERSON>ecid", "catchegid", "cid", "gid", "cid2", "oldcid2", "catchecid2", "clist", "clist2", "glist", "paramlist", "catcheparams", "proparams", "productlisttype", "showfilter", "cpid", "bid", "latitude", "longitude", "topshowcategory", "itemdata", "itemst", "test", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "area_id", "app", "getprolist", "getparamlist", "paramClick", "console", "showDrawer", "closeDrawer", "change", "searchChange", "searchbtn", "searchConfirm", "searchproduct", "sortClick", "groupClick", "cateClick", "cate2Click", "filterConfirm", "filterReset", "filterClick", "addHistory", "historylist", "newhistorylist", "historyClick", "deleteSearchHistory", "changetab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,2PAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgHhxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;MACAA;MAEA;MACA;QACAC;MACA;MAEAC;QAAA9B;QAAAC;QAAAa;QAAAZ;QAAA2B;MAAA;QACAD;QACAA;QACAA;QACAA;QACAA;QACA;UACA;UACA;UACA;YACAV;YACAC;UACA;UACAS;UACAA;QACA;QACAA;QACA;UACAE;YACAF;YACAA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAH;MACAA;MACAA;MACAA;MACA;MACAE;QAAAvC;QAAAD;QAAAM;QAAAD;QAAAM;QAAAD;QAAAE;QAAAW;QAAAC;QAAAC;QAAAC;QAAAN;MAAA;QACAkB;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;MACAF;QAAA9B;QAAAc;MAAA;QACA;UACAc;QACA;UACAA;QACA;QACAA;MACA;IACA;IACAK;MACAC;MACA;MACA;MACA;MACA;MACAA;IACA;IACA;IACAC;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACAH;MACA;IACA;IACAI;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAX;MACA;QACA;UACAA;UACAE;QACA;UACAF;UACAE;QACA;MACA;IACA;IACAU;MACA;MACA;MACAZ;MACAA;IACA;IACAa;MACA;MACAb;MACAA;MACAA;MACAA;IACA;IACAc;MACA;MACA;MACAd;MACAA;MACAA;IACA;IACAe;MACA;MACA;MACA;MACAf;IACA;IACAgB;MACA;MACA;MACA;MACAhB;MACAA;IACA;IACAiB;MACA;MACA;MACA;MACAjB;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACArB;MACAF;IACA;IACAwB;MACA;MACA;MACA;MACAxB;MACAA;IACA;IACAyB;MACA;MACAzB;MACAE;IACA;IACAwB;MACA;MACA1B;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjaA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/prolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/prolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prolist.vue?vue&type=template&id=acdce202&\"\nvar renderjs\nimport script from \"./prolist.vue?vue&type=script&lang=js&\"\nexport * from \"./prolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/prolist.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=template&id=acdce202&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    uniDrawer: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-drawer/uni-drawer\" */ \"@/components/uni-drawer/uni-drawer.vue\"\n      )\n    },\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    dpProductItemlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-itemlist/dp-product-itemlist\" */ \"@/components/dp-product-itemlist/dp-product-itemlist.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.isload && !(_vm.topshowcategory == 1)\n      ? !_vm.history_list || _vm.history_list.length == 0\n      : null\n  var m0 =\n    _vm.isload &&\n    !(_vm.topshowcategory == 1) &&\n    (!_vm.field || _vm.field == \"sort\")\n      ? _vm.t(\"color1\")\n      : null\n  var m1 =\n    _vm.isload && !(_vm.topshowcategory == 1) && _vm.field == \"sales\"\n      ? _vm.t(\"color1\")\n      : null\n  var m2 =\n    _vm.isload && !(_vm.topshowcategory == 1) && _vm.field == \"sell_price\"\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    !(_vm.topshowcategory == 1) &&\n    _vm.field == \"sell_price\" &&\n    _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    !(_vm.topshowcategory == 1) &&\n    _vm.field == \"sell_price\" &&\n    _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 = _vm.isload && _vm.catchegid == \"\" ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.catchegid == \"\" ? _vm.t(\"color1rgb\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.glist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m7 = _vm.catchegid == item.id ? _vm.t(\"color1\") : null\n        var m8 = _vm.catchegid == item.id ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l1 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.catchecid == item.id ? _vm.t(\"color1\") : null\n          var m12 = _vm.catchecid == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n          }\n        })\n      : null\n  var m13 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l2 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist2, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.catchecid2 == item.id ? _vm.t(\"color1\") : null\n          var m16 = _vm.catchecid2 == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n          }\n        })\n      : null\n  var l4 = _vm.isload\n    ? _vm.__map(_vm.paramlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m17 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1\") : null\n        var m18 = _vm.catcheparams[item.name] == \"\" ? _vm.t(\"color1rgb\") : null\n        var l3 = _vm.__map(item.params, function (item2, index) {\n          var $orig = _vm.__get_orig(item2)\n          var m19 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1\") : null\n          var m20 =\n            _vm.catcheparams[item.name] == item2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m19: m19,\n            m20: m20,\n          }\n        })\n        return {\n          $orig: $orig,\n          m17: m17,\n          m18: m18,\n          l3: l3,\n        }\n      })\n    : null\n  var m21 = _vm.isload ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l0: l0,\n        m9: m9,\n        m10: m10,\n        l1: l1,\n        m13: m13,\n        m14: m14,\n        l2: l2,\n        l4: l4,\n        m21: m21,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"search-container\" :style=\"history_show?'height:100%;':''\">\r\n\t\t\t<block v-if=\"topshowcategory==1\">\r\n\t\t\t\t<dd-tab :itemdata=\"itemdata\" :itemst=\"itemst\" :st=\"cid\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/search_ico.png\"></image>\r\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的商品\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-btn\" @tap=\"searchbtn\">\r\n\t\t\t\t\t<image src=\"/static/img/show-cascades.png\" style=\"height:36rpx;width:36rpx\" v-if=\"!history_show && productlisttype=='itemlist'\"/>\r\n\t\t\t\t\t<image src=\"/static/img/show-list.png\" style=\"height:36rpx;width:36rpx\" v-if=\"!history_show && productlisttype=='item2'\"/>\r\n\t\t\t\t\t<text v-if=\"history_show\">搜索</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-history\" v-show=\"history_show\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\r\n\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\r\n\t\t\t\t\t\t<image src=\"/static/img/del.png\" style=\"width:36rpx;height:36rpx\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-history-list\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\" :data-value=\"item\" @tap=\"historyClick\">{{item}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\" class=\"flex-y-center\"><image src=\"/static/img/tanhao.png\" style=\"width:36rpx;height:36rpx;margin-right:10rpx\"/>暂无记录\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-navbar\" v-show=\"!history_show\">\r\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"(!field||field=='sort')?'color:'+t('color1'):''\" data-field=\"sort\" data-order=\"desc\">综合</view>\r\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"field=='sales'?'color:'+t('color1'):''\" data-field=\"sales\" data-order=\"desc\">销量</view>\r\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" data-field=\"sell_price\" :data-order=\"order=='asc'?'desc':'asc'\">\r\n\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\r\n\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\" @click.stop=\"showDrawer('showRight')\">筛选 <text :class=\"'iconfont iconshaixuan ' + (showfilter?'active':'')\"></text></view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<uni-drawer ref=\"showRight\" mode=\"right\" @change=\"change($event,'showRight')\" :width=\"280\">\r\n\t\t\t\t<view class=\"filter-scroll-view\" style=\"max-height:100%;padding-bottom:110rpx;overflow:hidden auto\">\r\n\t\t\t\t\t<view class=\"filter-scroll-view-box\">\r\n\t\t\t\t\t\t<view class=\"search-filter\">\r\n\t\t\t\t\t\t\t<view class=\"filter-title\">筛选</view>\r\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分组</view>\r\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchegid==''?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"groupClick\" data-gid=\"\">全部</view>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in glist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchegid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"groupClick\" :data-gid=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-if=\"!bid || bid <=0\">\r\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\r\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"oldcid\">全部</view>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\r\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==oldcid2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"oldcid2\">全部</view>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-for=\"(item, index) in paramlist\">\r\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catcheparams[item.name]==''?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"paramClick\" :data-paramkey=\"item.name\" data-paramval=\"\">全部</view>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item2, index) in item.params\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catcheparams[item.name]==item2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"paramClick\" :data-paramkey=\"item.name\" :data-paramval=\"item2\">{{item2}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t\t\t<view class=\"search-filter-btn\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"filterReset\">重置</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn2\" :style=\"{background:t('color1')}\" @tap=\"filterConfirm\">确定</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-drawer>\r\n\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"product-container\" :style=\"{'margin-top':(topshowcategory==1 ? '110rpx' : '190rpx')}\">\r\n\t\t\t<block v-if=\"datalist && datalist.length>0\">\r\n\t\t\t\t<dp-product-item v-if=\"productlisttype=='item2'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-product-item>\r\n\t\t\t\t<dp-product-itemlist v-if=\"productlisttype=='itemlist'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-product-itemlist>\r\n\t\t\t</block>\r\n\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\r\n\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\r\n\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t</view>\r\n\t</block>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n\t\t\tnomore:false,\r\n\t\t\tnodata:false,\r\n      keyword: '',\r\n      pagenum: 1,\r\n      datalist: [],\r\n      history_list: [],\r\n      history_show: false,\r\n      order: '',\r\n\t\t\tfield:'',\r\n      oldcid: \"\",\r\n      catchecid: \"\",\r\n      catchegid: \"\",\r\n      cid: \"\",\r\n      gid: '',\r\n\t\t\tcid2:'',\r\n      oldcid2: \"\",\r\n      catchecid2: \"\",\r\n      clist: [],\r\n      clist2: [],\r\n      glist: [],\r\n\t\t\tparamlist:[],\r\n\t\t\tcatcheparams:{},\r\n\t\t\tproparams:{},\r\n      productlisttype: 'item2',\r\n      showfilter: \"\",\r\n\t\t\tcpid:0,\r\n\t\t\tbid:0,\r\n\t\t\tlatitude: '',\r\n\t\t\tlongitude: '',\r\n\t\t\ttopshowcategory:0,\r\n\t\t\titemdata:[],\r\n\t\t\titemst:[],\r\n\t\t\ttest:'',\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.oldcid = this.opt.cid || '';\r\n\t\tthis.catchecid = this.opt.cid;\r\n\t\tthis.cid = this.opt.cid;\r\n\t\tthis.cid2 = this.opt.cid2 || '';\r\n\t\tthis.oldcid2 = this.opt.cid2 || '';\r\n\t\tthis.catchecid2 = this.opt.cid2;\r\n\t\tthis.gid = this.opt.gid;\r\n\t\tthis.cpid = this.opt.cpid || 0;\r\n\t\tthis.bid = this.opt.bid ? this.opt.bid : 0;\r\n\t\t//console.log(this.bid);\r\n\t\tif(this.cpid > 0){\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: '可用商品列表'\r\n\t\t\t});\r\n\t\t}\r\n    var productlisttype = app.getCache('productlisttype');\r\n    if (productlisttype) this.productlisttype = productlisttype;\r\n\t\tthis.history_list = app.getCache('search_history_list');\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getprolist();\r\n    }\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.pagenum = 1;\r\n\t\t\tthat.datalist = [];\r\n\t\t\tvar cid = that.opt.cid;\r\n\t\t\tvar gid = that.opt.gid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar cid2 = that.cid2;\r\n\t\t\tthat.loading = true;\r\n\t\t\t\r\n\t\t\tvar area_id = '';\r\n\t\t\tif(uni.getStorageSync('area_id')){\r\n\t\t\t\tarea_id = uni.getStorageSync('area_id');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tapp.get('ApiShop/prolist', {cid: cid,gid: gid,bid:bid,cid2:cid2,area_id:area_id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t  that.clist = res.clist;\r\n\t\t\t  that.clist2 = res.clist2;\r\n\t\t\t  that.glist = res.glist;\r\n\t\t\t  that.topshowcategory = res.topshowcategory || 0;\r\n\t\t\t\tif(that.clist){\r\n\t\t\t\t\tvar itemdata = [];\r\n\t\t\t\t\tvar itemst = [];\r\n\t\t\t\t\tfor(var i in that.clist){\r\n\t\t\t\t\t\titemdata.push(that.clist[i].name);\r\n\t\t\t\t\t\titemst.push(that.clist[i].id);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.itemdata = itemdata;\r\n\t\t\t\t\tthat.itemst = itemst;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tif (that.latitude == '' && that.longitude == '' && res.needlocation) {\r\n\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\tthat.latitude = res.latitude;\r\n\t\t\t\t\t\tthat.longitude = res.longitude;\r\n\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t},function(){\r\n\t\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.getprolist();\r\n\t\t\t\t}\r\n\t\t\t\tthat.getparamlist();\r\n\t\t\t});\r\n\t\t},\r\n    getprolist: function () {\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n      var keyword = that.keyword;\r\n      var order = that.order;\r\n      var field = that.field;\r\n      var gid = that.gid;\r\n      var cid = that.cid;\r\n\t\t\tvar cid2 = that.cid2;\r\n      var cpid = that.cpid;\r\n      that.history_show = false;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n      that.nomore = false;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n      app.post('ApiShop/getprolist',{pagenum: pagenum,keyword: keyword,field: field,order: order,gid: gid,cid: cid,cid2:cid2,cpid:cpid,bid:bid,latitude: that.latitude,longitude: that.longitude,proparams:that.proparams}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data;\r\n        if (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\t\tgetparamlist:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar cid = that.opt.cid;\r\n\t\t\tif(this.catchecid) cid = this.catchecid;\r\n\t\t\tvar gid = that.opt.gid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tapp.get('ApiShop/getparamlist', {cid: cid,bid:bid}, function (res) {\r\n\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\tthat.paramlist = res.data;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.paramlist = [];\r\n\t\t\t\t}\r\n\t\t\t\tthat.catcheparams = {};\r\n\t\t\t});\r\n\t\t},\r\n\t\tparamClick:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tvar paramkey = e.currentTarget.dataset.paramkey;\r\n\t\t\tvar paramval = e.currentTarget.dataset.paramval;\r\n\t\t\tthis.catcheparams[paramkey] = paramval;\r\n\t\t\tthis.test = Math.random();\r\n\t\t\tconsole.log(this.catcheparams);\r\n\t\t},\r\n\t\t// 打开窗口\r\n\t\tshowDrawer(e) {\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.$refs[e].open()\r\n\t\t},\r\n\t\t// 关闭窗口\r\n\t\tcloseDrawer(e) {\r\n\t\t\tthis.$refs[e].close()\r\n\t\t},\r\n\t\t// 抽屉状态发生变化触发\r\n\t\tchange(e, type) {\r\n\t\t\tconsole.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));\r\n\t\t\tthis[type] = e\r\n\t\t},\r\n    searchChange: function (e) {\r\n      this.keyword = e.detail.value;\r\n      if (e.detail.value == '') {\r\n        this.history_show = true;\r\n        this.datalist = [];\r\n      }\r\n    },\r\n    searchbtn: function () {\r\n      var that = this;\r\n      if (that.history_show) {\r\n        var keyword = that.keyword;\r\n        that.searchproduct();\r\n      } else {\r\n        if (that.productlisttype == 'itemlist') {\r\n          that.productlisttype = 'item2';\r\n          app.setCache('productlisttype', 'item2');\r\n        } else {\r\n          that.productlisttype = 'itemlist';\r\n          app.setCache('productlisttype', 'itemlist');\r\n        }\r\n      }\r\n    },\r\n    searchConfirm: function (e) {\r\n      var that = this;\r\n      var keyword = e.detail.value;\r\n      that.keyword = keyword\r\n      that.searchproduct();\r\n    },\r\n    searchproduct: function () {\r\n      var that = this;\r\n      that.pagenum = 1;\r\n      that.datalist = [];\r\n      that.addHistory();\r\n      that.getprolist();\r\n    },\r\n    sortClick: function (e) {\r\n      var that = this;\r\n      var t = e.currentTarget.dataset;\r\n      that.field = t.field;\r\n      that.order = t.order;\r\n      that.searchproduct();\r\n    },\r\n    groupClick: function (e) {\r\n      var that = this;\r\n      var gid = e.currentTarget.dataset.gid;\r\n\t\t\tif(gid === true) gid = '';\r\n      that.catchegid = gid\r\n    },\r\n    cateClick: function (e) {\r\n      var that = this;\r\n      var cid = e.currentTarget.dataset.cid;\r\n\t\t\tif(cid === true) cid = '';\r\n      that.catchecid = cid;\r\n\t\t\tthat.getparamlist();\r\n    },\r\n    cate2Click: function (e) {\r\n      var that = this;\r\n      var cid2 = e.currentTarget.dataset.cid2;\r\n\t\t\tif(cid2 === true) cid2 = '';\r\n      that.catchecid2 = cid2\r\n    },\r\n\t\tfilterConfirm(){\r\n\t\t\tthis.cid = this.catchecid;\r\n\t\t\tthis.cid2 = this.catchecid2;\r\n\t\t\tthis.gid = this.catchegid;\r\n\t\t\tthis.proparams = this.catcheparams;\r\n\t\t\tthis.searchproduct();\r\n\t\t\tthis.$refs['showRight'].close()\r\n\t\t},\r\n\t\tfilterReset(){\r\n\t\t\tthis.catchecid = this.oldcid;\r\n\t\t\tthis.catchecid2 = this.oldcid2;\r\n\t\t\tthis.catchegid = '';\r\n\t\t},\r\n    filterClick: function () {\r\n      this.showfilter = !this.showfilter\r\n    },\r\n    addHistory: function () {\r\n      var that = this;\r\n      var keyword = that.keyword;\r\n      if (app.isNull(keyword)) return;\r\n      var historylist = app.getCache('search_history_list');\r\n      if (app.isNull(historylist)) historylist = [];\r\n      historylist.unshift(keyword);\r\n      var newhistorylist = [];\r\n      for (var i in historylist) {\r\n        if (historylist[i] != keyword || i == 0) {\r\n          newhistorylist.push(historylist[i]);\r\n        }\r\n      }\r\n      if (newhistorylist.length > 5) newhistorylist.splice(5, 1);\r\n      app.setCache('search_history_list', newhistorylist);\r\n      that.history_list = newhistorylist\r\n    },\r\n    historyClick: function (e){\r\n      var that = this;\r\n      var keyword = e.currentTarget.dataset.value;\r\n      if (keyword.length == 0) return;\r\n      that.keyword = keyword;\r\n      that.searchproduct();\r\n    },\r\n    deleteSearchHistory: function () {\r\n      var that = this;\r\n      that.history_list = null;\r\n      app.removeCache(\"search_history_list\");\r\n    },\r\n\t\tchangetab: function (cid) {\r\n      var that = this;\r\n      that.cid = cid;\r\n      that.getdata();\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}\r\n.topsearch{width:100%;padding:16rpx 20rpx;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}\r\n.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}\r\n.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}\r\n\r\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\r\n.search-history {padding: 24rpx 34rpx;}\r\n.search-history .search-history-title {color: #666;}\r\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\r\n.search-history-list {padding: 24rpx 0 0 0;}\r\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\r\n\r\n.filter-scroll-view{margin-top:var(--window-top)}\r\n.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}\r\n.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}\r\n.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}\r\n.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}\r\n.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}\r\n.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}\r\n.search-filter button .icon{margin-top:6rpx;height:54rpx;}\r\n.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}\r\n.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}\r\n.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}\r\n\r\n.product-container {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./prolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115007772\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}