{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?982f", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?8f33", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?40b0", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?900a", "uni-app:///shopPackage/shop/buy.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?4f0d", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/buy.vue?1bf2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "test", "havetongcheng", "address", "memberList", "checkMem", "usescore", "scoredk_money", "totalprice", "couponvisible", "cuxiaovisible", "membervisible", "memberinfovisible", "selectmemberinfo", "bid", "nowbid", "needaddress", "linkman", "singleFreight", "totalFreight", "tel", "userinfo", "pstimeDialogShow", "pstimeIndex", "manjian_money", "cxid", "cxids", "latitude", "longitude", "allbuydata", "allbuydatawww", "alltotalprice", "cuxiaoin<PERSON>", "cuxiaoList", "type11visible", "type11key", "regiondata", "items", "editorFormdata", "buy_selectmember", "multi_promotion", "storeshowall", "order_change_price", "invoiceShow", "invoice", "invoice_type", "invoice_type_select", "name_type_select", "name_type_personal_disabled", "inputDisabled", "submitDisabled", "pstype3needAddress", "isshowglass", "glassrecordlist", "grid", "curindex", "curindex2", "is_yh", "yh_prices", "yh_nums", "newArr", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "prodata", "uni", "url", "method", "header", "success", "console", "res", "storedata", "scoredk", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "scoredkyu", "inputLinkman", "inputTel", "inputfield", "allbuydata2", "<PERSON><PERSON><PERSON><PERSON>", "inputPrice", "calculatePrice", "freight_price", "allfreight_price", "scorebdkyfhei", "scorebdkyfhuang", "scorebdkyfyu", "changeFreight", "chooseFreight", "itemlist", "itemList", "choosePstime", "pstimeRadioChange", "hidePstimeDialog", "chooseCoupon", "coupons", "couponrids", "coupontype", "coupon_money", "choosestore", "topay", "formdata", "newformdata", "cuxiaoid", "couponrid", "freight_id", "freight_time", "storeid", "buydatatemp", "buydata", "frompage", "addressid", "checkmemid", "usescorehei", "usescoreyu", "usescorehuang", "newarr", "showCouponList", "showInvoice", "changeOrderType", "changeNameType", "invoiceFormSubmit", "handleClickMask", "showCuxiaoList", "changecx", "id", "changecxMulti", "chooseCuxiao", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showType11List", "changetype11", "chooseType11", "product_price", "openMendian", "openLocation", "name", "scale", "editorChooseImage", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "showMemberList", "regionchange2", "memberSearch", "diqu", "checkMember", "showmemberinfo", "mid", "memberinfoClickMask", "doStoreShowAll", "showglass", "pagenum", "listrow", "hideglass", "chooseglass", "product"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClYA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,mwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4wB5wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAEA;IAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MAEAC;MACAC;QACAC;MACA;QACA;QACA;QACAF;QACA;UACA;YACAC;cACA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAC;YACA1E;YACA2E;YACAC;cAAA;YAAA;YACAC;cACAP;YACA;UACA;QACA;QACAA;QACAA;QACA;QACA;;QAEAQ;QACA;QACAR;QACAA;;QAEA;QACA;UACAS;YACA;cACA;cACA;gBACA;kBACAD;kBACAR;kBACAA;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;cACA;YACA;cACAQ;YACA;UACA;QACA;UACAA;QACA;QACA;QACAA;QACAA;QACAR;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;QACA;QACA;QACA;;QAGA;UACAC;YACA;YACA;YACAD;YACAA;YACA;YACA;cACA;cACA;gBACA;kBACA;kBACA;oBACA;sBACA;wBACA;wBACAU;sBACA;oBACA;oBACAA;sBACA;oBACA;oBACA;sBACA;wBACAA;sBACA;oBACA;oBACAF;oBACA5C;kBACA;gBACA;cACA;YACA;YACAoC;UACA;QACA;MACA;IACA;IACA;IACAW;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAJ;MACA;IACA;IACA;IACAK;MACA;MACA;MACA;MACAL;MACA;IACA;IAEA;IACAM;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;IACA;IACA;IACAC;MACAlB;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACApB;QACAC;QACA;MACA;MACAD;MACApC;MACAA;MACA;MACAoC;MACAQ;MACAR;IACA;IACA;IACAqB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAtE;QACA;QACA;UACAA;QACA;QACA;UACAuE;QACA;QACA;QACA;QACA;;QAEA/E;QACAqB;QACA;UACA;UACAA;UACArB;QACA;QACAiE;QACAA;QAEA5C;QACAE;QACAA;QACAyD;MACA;MACAvB;MACA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;QACA;MACA;MAGA;MACAlC;MACA;MACA;MAEA;QACA;QACAA;QACAxB;MACA;MACA;QACA;QACAwB;QACA0D;MACA;MACA;QACA;QACA1D;QACA2D;MACA;MACA;QACA;QACA3D;QACA4D;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;MACA;MACA;MAEA;MACA;MACA;MAGA,kGACApF;QACAA;QACAwB;QACA;MAEA;QAEAxB;QACAwB;QACA;MAEA;;MACA,2GACA0D;QACAA;QACA1D;QACA;MACA;QACA0D;QACA1D;QACA;MACA;;MAEA;MACA,mHACA2D;QACAA;QACA3D;QACA;MACA;QACA2D;QACA3D;QACA;MACA;;MAGA,uGACA4D;QACAA;QACA5D;MACA;QACA4D;QACA5D;MACA;MAEA;MACAA;MACAkC;MACAA;IACA;IACA2B;MACA;MACA;MACA;MACA;MACA;MACA;QACA1B;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACArC;MACAoC;MACAA;MACAA;IACA;IACA4B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;MAEA1B;QACA2B;QACAvB;UACA;YACA3C;YACAoC;YACAA;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAF;MACA;MACA;QACA5B;QACA;MACA;MACAD;MACAA;MACAA;IACA;IACAgC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACApE;MACAA;MACAoC;MACAA;IACA;IACAiC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;YACAC;YACAC;UACA;QACA;MACA;QACAD;QACAC;QACA5B;QACA;UACAA;UACA2B;UACAC;QACA;UACA;YACAnC;YACA;UACA;YACAkC;YACAC;UACA;QACA;MACA;MACA5B;MACAA;MACA5C;MACAA;MACA;MACA;MACA;QACA;UACAyE;QACA;UACAC;QACA;UACAA;QACA;MACA;MACA1E;MACAA;MACA;MACA;MACA;IACA;IACA2E;MACA;MACA;MACA;MACA;MACA;MACA3E;MACA;IACA;IACA;IACA4E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MAEA;QACAvC;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;QACA;UACA;UACA;YACAA;YACA;UACA;UACA/B;QACA;UACA;QACA;QAEA;QACA;QACA;QACA;UACA;UACA;YACA+B;YAAA;UACA;UACA;YACAwC;UACA;UACA;YACAxC;YAAA;UACA;UACAyC;QACA;QAEA;QAEA;UACA7F;UACAqD;UACAyC;UACAC;UACAC;UACAC;UACAC;UACAN;UACAvE;QACA;QAEA;UACA8E;QACA;QACA;UACAA;QACA;QACAC;MACA;MACA;MACAhD;MACAA;QACAiD;QACAD;QACAE;QACAnG;QACAG;QACAiG;QACA/G;QACAgH;QACAC;QACAC;QACA/D;QACAC;QACAC;QACA8D;MACA;QACAvD;QACA;UACA;UACAA;UACA;QACA;QACA;QACA,oBACAA;MACA;IACA;IACAwD;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA3D;QACAA;MACA;QACAA;MACA;MACAA;IACA;IACA4D;MACA;MACA;MACA5D;IACA;IACA6D;MACA;MACA;MACA;QACA5D;QACA;MACA;MACA;QACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;QACA,mCACArC;MACA;MACAoC;MACAA;MACA;MACA;MACAA;IACA;IACA8D;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAxD;MACAR;MACA;QACAA;QACA;MACA;MACA;MACAQ;MACAP;QACAgE;MACA;QACA;UACAxD;QACA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAT;MACA;IACA;IACAkE;MACA;MACA;MACA;MACAlE;MACAQ;MACA;QACAR;QACAA;QACAA;QACA;MACA;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;QACAA;QACAA;QACA;MACA;MACAA;MACA;MACAQ;MACAP;MACAA;QACAgE;MACA;QACA;QACA;QACA;QACA;QACA;UACA;YACA;cAAA;YAAA;YACA;cACA;gBACAxD;cACA;cACA;gBACAA;cACA;cACA;gBACAA;cACA;cACA;gBACAA;cACA;YACA;UACA;QACA;QACAR;QACAD;MACA;IACA;IACAmE;MACA;MACA;MACA;MACA;MACA;MACA3D;MACA;QACA5C;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;QACAA;QACAA;QACA4C;QACA;UACA;YACA;YACAA;YACA;cACA;cACA5C;YACA;cACA;cACAA;YACA;cACA;cACAA;YACA;cACA;cACA;cACA;gBACA;kBACAwG;gBACA;cACA;cACA5D;cACAA;cACA5C;YACA;YACAA;YACAA;YACAA;UACA;UACA4C;UACAA;QACA;UACA;UACAA;UACA;YACA;YACA5C;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;YACA;YACA;YACA;YACA;YACA;YACAA;UACA;UACAA;UACAA;UACAA;QACA;MACA;MACA;MACA;MACA;IACA;IACAyG;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAtE;MACA;IACA;;IACAuE;MACA;MACA;MACA;MACA;MACA;QACAtE;QACA;MACA;MACArC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,8FACA4G;QACAlD;MACA;MACA1D;MAEA;MACA;MACA;IACA;IACA6G;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAxE;IACA;IACAyE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAvE;QACAzC;QACAC;QACAgH;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA5E;QACA5B;QACA;QACA2B;QACAA;MACA;IACA;IACA8E;MACA;MACA;MACA;MACA;MACA;MACA;MACAzG;MACA;MACA2B;MACAA;IACA;IAEA+E;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAhF;QACAiF;MACA;QACAjF;QACA;UACAA;UACA;QACA;QACA;QACAD;MACA;IACA;IACAmF;MACA;MACAnF;MACA;IACA;IACAoF;MACA;MACA;MACAnF;MACAA;QAAAoF;MAAA;QACApF;QACA;UACAA;UACA;QACA;QACAD;QACAA;MACA;IACA;IACAsF;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAhF;MACAA;MACAA;MACA;MACA;QACA;QACAR;QACAC;UAAAwF;UAAAC;QAAA;UACA1F;UACA;UACAQ;UACAR;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAA;QACA;MAEA;QACAA;MACA;MACAA;MACAA;MACAA;IACA;IACA2F;MACA;MACA3F;IACA;IACA4F;MACA;MACA;MACA;MACA;MACA;MACA;MACApF;MACA;MACA;MACA;MACA;QACAqF;QACA7F;MACA;QACA6F;QACA7F;MACA;MAEAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC16DA;AAAA;AAAA;AAAA;AAAwkC,CAAgB,ojCAAG,EAAC,C;;;;;;;;;;;ACA5lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=f0b13d8c&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/buy.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=template&id=f0b13d8c&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l6 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var l0 = _vm.__map(buydata.prodata, function (item, index2) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.product.glassrecord ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n        var l1 = _vm.__map(buydata.freightList, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = buydata.freightkey == idx2 ? _vm.t(\"color1\") : null\n          var m2 = buydata.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n          }\n        })\n        var g0 =\n          buydata.freightList[buydata.freightkey].minpriceset == 1 &&\n          buydata.freightList[buydata.freightkey].minprice > 0 &&\n          buydata.freightList[buydata.freightkey].minprice * 1 >\n            buydata.product_price * 1\n            ? (\n                buydata.freightList[buydata.freightkey].minprice -\n                buydata.product_price\n              ).toFixed(2)\n            : null\n        var l2 =\n          buydata.freightList[buydata.freightkey].pstype == 1\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m3 =\n                    (idx < 5 || _vm.storeshowall == true) &&\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m3: m3,\n                  }\n                }\n              )\n            : null\n        var g1 =\n          buydata.freightList[buydata.freightkey].pstype == 1\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var l3 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m4 =\n                    (idx < 5 || _vm.storeshowall == true) &&\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m4: m4,\n                  }\n                }\n              )\n            : null\n        var g2 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var m5 = buydata.leveldk_money > 0 ? _vm.t(\"会员\") : null\n        var m6 = _vm.t(\"优惠券\")\n        var g3 = buydata.couponCount > 0 ? buydata.coupons.length : null\n        var l4 =\n          buydata.couponCount > 0 && g3 > 0\n            ? _vm.__map(buydata.coupons, function (item, index) {\n                var $orig = _vm.__get_orig(item)\n                var m7 = _vm.t(\"color1\")\n                return {\n                  $orig: $orig,\n                  m7: m7,\n                }\n              })\n            : null\n        var m8 = buydata.couponCount > 0 && !(g3 > 0) ? _vm.t(\"color1\") : null\n        var m9 = !(buydata.couponCount > 0) ? _vm.t(\"优惠券\") : null\n        var g4 =\n          buydata.cuxiaoCount > 0\n            ? buydata.cuxiaonameArr && buydata.cuxiaonameArr.length > 0\n            : null\n        var l5 =\n          buydata.cuxiaoCount > 0 && g4\n            ? _vm.__map(buydata.cuxiaonameArr, function (item, index) {\n                var $orig = _vm.__get_orig(item)\n                var m10 = _vm.t(\"color1\")\n                return {\n                  $orig: $orig,\n                  m10: m10,\n                }\n              })\n            : null\n        var m11 = buydata.cuxiaoCount > 0 && !g4 ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          l0: l0,\n          l1: l1,\n          g0: g0,\n          l2: l2,\n          g1: g1,\n          l3: l3,\n          g2: g2,\n          m5: m5,\n          m6: m6,\n          g3: g3,\n          l4: l4,\n          m8: m8,\n          m9: m9,\n          g4: g4,\n          l5: l5,\n          m11: m11,\n        }\n      })\n    : null\n  var m12 =\n    _vm.isload &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m13 =\n    _vm.isload &&\n    _vm.userinfo.score2money > 0 &&\n    (_vm.userinfo.scoremaxtype == 0 ||\n      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))\n      ? _vm.t(\"积分\")\n      : null\n  var m14 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyhei > 0 &&\n    (_vm.userinfo.scoremaxtypehei == 0 ||\n      (_vm.userinfo.scoremaxtypehei == 1 &&\n        _vm.userinfo.scoredkmaxmoneyhei > 0))\n      ? _vm.t(\"现金券\")\n      : null\n  var m15 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyhei > 0 &&\n    (_vm.userinfo.scoremaxtypehei == 0 ||\n      (_vm.userinfo.scoremaxtypehei == 1 &&\n        _vm.userinfo.scoredkmaxmoneyhei > 0))\n      ? _vm.t(\"现金券\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyyu > 0 &&\n    (_vm.userinfo.scoremaxtypeyu == 0 ||\n      (_vm.userinfo.scoremaxtypeyu == 1 && _vm.userinfo.scoredkmaxmoneyyu > 0))\n      ? _vm.t(\"余额\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyyu > 0 &&\n    (_vm.userinfo.scoremaxtypeyu == 0 ||\n      (_vm.userinfo.scoremaxtypeyu == 1 && _vm.userinfo.scoredkmaxmoneyyu > 0))\n      ? _vm.t(\"余额\")\n      : null\n  var m18 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyhuang > 0 &&\n    (_vm.userinfo.scoremaxtypehuang == 0 ||\n      (_vm.userinfo.scoremaxtypehuang == 1 &&\n        _vm.userinfo.scoredkmaxmoneyhuang > 0))\n      ? _vm.t(\"红包\")\n      : null\n  var m19 =\n    _vm.isload &&\n    _vm.userinfo.score2moneyhuang > 0 &&\n    (_vm.userinfo.scoremaxtypehuang == 0 ||\n      (_vm.userinfo.scoremaxtypehuang == 1 &&\n        _vm.userinfo.scoredkmaxmoneyhuang > 0))\n      ? _vm.t(\"红包\")\n      : null\n  var m20 = _vm.isload ? _vm.t(\"color1\") : null\n  var m21 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m22 =\n    _vm.isload && _vm.invoiceShow ? _vm.inArray(1, _vm.invoice_type) : null\n  var m23 =\n    _vm.isload && _vm.invoiceShow ? _vm.inArray(2, _vm.invoice_type) : null\n  var m24 = _vm.isload && _vm.invoiceShow ? _vm.t(\"color1\") : null\n  var m25 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var l7 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.allbuydata[_vm.nowbid].freightList[\n            _vm.allbuydata[_vm.nowbid].freightkey\n          ].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m26 =\n              _vm.allbuydata[_vm.nowbid].freight_time == item.value\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m26: m26,\n            }\n          }\n        )\n      : null\n  var m27 =\n    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion && _vm.cxid === 0\n      ? _vm.t(\"color1\")\n      : null\n  var l8 =\n    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion\n      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g5 = _vm.cxids.indexOf(item.id)\n          var m28 = g5 !== -1 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            g5: g5,\n            m28: m28,\n          }\n        })\n      : null\n  var m29 =\n    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion && _vm.cxid == 0\n      ? _vm.t(\"color1\")\n      : null\n  var l9 =\n    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion\n      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m30 = _vm.cxid == item.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m30: m30,\n          }\n        })\n      : null\n  var g6 =\n    _vm.isload && _vm.cuxiaovisible\n      ? _vm.cuxiaoList && _vm.cuxiaoList.info && _vm.cuxiaoList.info.length > 0\n      : null\n  var m31 = _vm.isload && _vm.cuxiaovisible ? _vm.t(\"color1\") : null\n  var l10 =\n    _vm.isload && _vm.type11visible\n      ? _vm.__map(\n          _vm.allbuydata[_vm.bid].freightList[\n            _vm.allbuydata[_vm.bid].freightkey\n          ].type11pricedata,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m32 =\n              _vm.address.id &&\n              _vm.address.province == item.province &&\n              _vm.address.city == item.city &&\n              _vm.address.district == item.area &&\n              _vm.type11key == index\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m32: m32,\n            }\n          }\n        )\n      : null\n  var m33 = _vm.isload && _vm.type11visible ? _vm.t(\"color1\") : null\n  var l11 =\n    _vm.isload && _vm.membervisible\n      ? _vm.__map(_vm.memberList, function (item2, i) {\n          var $orig = _vm.__get_orig(item2)\n          var m34 = _vm.checkMem.id == item2.id ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m34: m34,\n          }\n        })\n      : null\n  var l12 =\n    _vm.isload && _vm.isshowglass\n      ? _vm.__map(_vm.glassrecordlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m35 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m35: m35,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l6: l6,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        l7: l7,\n        m27: m27,\n        l8: l8,\n        m29: m29,\n        l9: l9,\n        g6: g6,\n        m31: m31,\n        l10: l10,\n        m33: m33,\n        l11: l11,\n        l12: l12,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#626262;font-size:28rpx\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\"\r\n\t\t\t\t\t\t\tplaceholder-style=\"color:#626262;font-size:28rpx\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\"\r\n\t\t\t\t\t:data-url=\"'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/address.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.id\">\r\n\t\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\r\n\t\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\r\n\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"f3\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\r\n\t\t\t\t\t<view class=\"btitle\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/ico-shop.png\" />{{buydata.business.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.num > 0\" class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.product.id\">\r\n\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.guige.pic\" :src=\"item.guige.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<image v-else :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"order_change_price\"><input type=\"number\" :value=\"item.guige.sell_price\" :data-price=\"item.guige.sell_price\" :data-index=\"index\" :data-index2=\"index2\" class=\"inputPrice\" @input=\"inputPrice\"></block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-else-if=\"item.guige.is_newcustom == 0\"><text style=\"font-weight:bold;\">￥{{item.guige.yh_price}}</text></block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-else><text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text></block>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{item.num}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.is_yh == 1\" class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.product.id\">\r\n\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.guige.pic\" :src=\"item.guige.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<image v-else :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"order_change_price\"><input type=\"number\" :value=\"item.guige.sell_price\" :data-price=\"item.guige.sell_price\" :data-index=\"index\" :data-index2=\"index2\" class=\"inputPrice\" @input=\"inputPrice\"></block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-else><text style=\"font-weight:bold;\">￥{{item.youhui.product_price}}(优惠价格)</text></block>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{item.youhui.new_num}}(优惠数量)</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"glassinfo\" v-if=\"item.product.glassrecord\" @tap=\"showglass\" :data-index=\"index\" :data-index2=\"index2\" :data-grid=\"item.product.glassrecord.id\" :style=\"'background:rgba('+t('color1rgb')+',0.1)'\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t\t视力档案\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t\t\t<text>{{item.product.glassrecord.type==1?'近视':'远视'}}，右眼{{item.product.glassrecord.degress_right}}度，左眼{{item.product.glassrecord.degress_left}}度</text>\r\n\t\t\t\t\t\t\t\t\t\t<image src=\"../../static/img/arrowright.png\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"freight\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">配送方式</view>\r\n\t\t\t\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, idx2) in buydata.freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"freight-li\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"buydata.freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"changeFreight\" :data-bid=\"buydata.bid\" :data-index=\"idx2\">{{item.name}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\"\r\n\t\t\t\t\t\t\t\tv-if=\"buydata.freightList[buydata.freightkey].minpriceset==1 && buydata.freightList[buydata.freightkey].minprice > 0 && buydata.freightList[buydata.freightkey].minprice*1 > buydata.product_price*1\">\r\n\t\t\t\t\t\t\t\t满{{buydata.freightList[buydata.freightkey].minprice}}元起送，还差{{(buydata.freightList[buydata.freightkey].minprice - buydata.product_price).toFixed(2)}}元\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.freightList[buydata.freightkey].pstimeset==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{buydata.freightList[buydata.freightkey].pstype==1?'取货':'配送'}}时间</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\" :data-bid=\"buydata.bid\">\r\n\t\t\t\t\t\t\t\t{{buydata.pstimetext==''?'请选择时间':buydata.pstimetext}}<text class=\"iconfont iconjiantou\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==1\">\r\n\t\t\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-bid=\"buydata.bid\"\r\n\t\t\t\t\t\t\t\t\t:data-freightkey=\"buydata.freightkey\"\r\n\t\t\t\t\t\t\t\t\t:data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==5\">\r\n\t\t\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">配送门店</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-bid=\"buydata.bid\"\r\n\t\t\t\t\t\t\t\t\t:data-freightkey=\"buydata.freightkey\"\r\n\t\t\t\t\t\t\t\t\t:data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\r\n\t\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.freightList[buydata.freightkey].pstype==11\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">选择物流</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showType11List\" :data-bid=\"buydata.bid\">\r\n\t\t\t\t\t\t\t\t<text>{{buydata.type11key?buydata.freightList[buydata.freightkey].type11pricedata[buydata.type11key-1].name:'请选择'}}</text><text\r\n\t\t\t\t\t\t\t\t\tclass=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">订单金额</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.product_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.leveldk_money>0\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">-¥{{buydata.leveldk_money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.manjian_money>0\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">满减活动</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">-¥{{buydata.manjian_money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{buydata.freightList[buydata.freightkey].freight_price_txt || '运费'}}<text v-if=\"buydata.freightList[buydata.freightkey].pstype!=1 && buydata.freightList[buydata.freightkey].freeset==1\" style=\"color:#aaa;font-size:24rpx;\">（满{{buydata.freightList[buydata.freightkey].free_price}}元包邮）</text></text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">+¥{{buydata.freightList[buydata.freightkey].freight_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"price\"  v-if=\"buydata.diy_amount>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"f1\">框价格</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.diy_amount}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"buydata.couponCount > 0\" class=\"f2\" @tap=\"showCouponList\" :data-bid=\"buydata.bid\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"(buydata.coupons).length>0\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"couponname\" :style=\"{background:t('color1')}\" v-for=\"(item,index) in buydata.coupons\">{{item.couponname}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text class=\"couponname\" :style=\"{background:t('color1')}\">{{buydata.couponCount+'张可用'}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.cuxiaoCount > 0\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">促销活动</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showCuxiaoList\" :data-bid=\"buydata.bid\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"buydata.cuxiaonameArr && buydata.cuxiaonameArr.length > 0\">\r\n\t\t\t\t\t\t\t\t\t<view :style=\"{background:t('color1')}\" class=\"redBg\" v-for=\"(item,index) in buydata.cuxiaonameArr\" :key=\"index\">{{item}}</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<text :style=\"{background:t('color1')}\" class=\"redBg\">{{buydata.cuxiaoname?buydata.cuxiaoname:buydata.cuxiaoCount+'个可用'}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.business.invoice > 0\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">发票</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showInvoice\" :data-url=\"'/shopPackage/shop/invoice?bid=' + buydata.bid + '&prodata=' + opt.prodata\" :data-bid=\"buydata.bid\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\tstyle=\"font-size:24rpx\" v-if=\"buydata.tempInvoice && buydata.tempInvoice.invoice_name\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"buydata.tempInvoice && buydata.tempInvoice.name_type == 1\">个人 - </text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"buydata.tempInvoice && buydata.tempInvoice.name_type == 2\">公司 - </text>\r\n\t\t\t\t\t\t\t\t\t{{buydata.tempInvoice.invoice_name}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.business.invoice > 0 && buydata.business.invoice_rate > 0 && buydata.tempInvoice && buydata.tempInvoice.invoice_name\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">发票费用</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.invoice_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\r\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in buydata.freightList[buydata.freightkey].formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+buydata.bid+'_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form'+buydata.bid+'_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+buydata.bid+'_'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+buydata.bid+'_'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0\"> {{item.val2[buydata.editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+buydata.bid+'_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"buydata.editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"buydata.editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"buydata.editorFormdata[idx]\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-bid=\"buydata.bid\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"buydata.diy_amount>0\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">框价格   x {{buydata.totalnum}}</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.diy_amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"totalFreight\">\r\n\t\t\t\t\t\t  <text class=\"f1\">配送费</text>\r\n\t\t\t\t\t\t  <text class=\"f2\">¥{{totalFreight}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"scoredk\" v-if=\"buy_selectmember\">\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<view class=\"f1\">选择会员</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showMemberList\">\r\n\t\t\t\t\t\t\t<text>{{checkMem.id?checkMem.nickname:'请选择'}}</text><text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"scoredk\"\r\n\t\t\t\t\tv-if=\"userinfo.score2money>0 && (userinfo.scoremaxtype==0 || (userinfo.scoremaxtype==1 && userinfo.scoredkmaxmoney>0))\">\r\n\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\r\n\t\t\t\t\t\t\t\tv-if=\"userinfo.scoremaxtype==0 && userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">\r\n\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtype==1\">\r\n\t\t\t\t\t\t\t\t最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"scoredk\"\r\n\t\t\t\t\tv-if=\"userinfo.score2moneyhei>0 && (userinfo.scoremaxtypehei==0 || (userinfo.scoremaxtypehei==1 && userinfo.scoredkmaxmoneyhei>0))\">\r\n\t\t\t\t\t<checkbox-group @change=\"scoredkhei\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<view>{{userinfo.heiscore*1}} {{t('现金券')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.scorebdkyfhei*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\r\n\t\t\t\t\t\t\t\tv-if=\"userinfo.scoremaxtypehei==0 && userinfo.scoredkmaxpercenthei > 0 && userinfo.scoredkmaxpercenthei<100\">\r\n\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercenthei}}%</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtypehei==1\">\r\n\t\t\t\t\t\t\t\t最多可抵扣{{userinfo.scoredkmaxmoneyhei}}元</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">使用{{t('现金券')}}抵扣\r\n\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"scoredk\"\r\n\t\t\t\t\tv-if=\"userinfo.score2moneyyu>0 && (userinfo.scoremaxtypeyu==0 || (userinfo.scoremaxtypeyu==1 && userinfo.scoredkmaxmoneyyu>0))\">\r\n\t\t\t\t\t<checkbox-group @change=\"scoredkyu\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<view>{{userinfo.yue*1}} {{t('余额')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.scoredkmaxpercentyu}}%</text> </view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\r\n\t\t\t\t\t\t\t\tv-if=\"userinfo.scoremaxtypeyu==0 && userinfo.scoredkmaxpercentyu > 0 && userinfo.scoredkmaxpercentyu<100\">\r\n\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercentyu}}%</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtypeyu==1\">\r\n\t\t\t\t\t\t\t\t最多可抵扣{{userinfo.scoredkmaxmoneyyu}}元</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">使用{{t('余额')}}抵扣\r\n\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"scoredk\"\r\n\t\t\t\t\tv-if=\"userinfo.score2moneyhuang>0 && (userinfo.scoremaxtypehuang==0 || (userinfo.scoremaxtypehuang==1 && userinfo.scoredkmaxmoneyhuang>0))\">\r\n\t\t\t\t\t<checkbox-group @change=\"scoredkhuang\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<view>{{userinfo.scorehuang*1}} {{t('红包')}}可抵扣 <text\r\n\t\t\t\t\t\t\t\t\tstyle=\"color:#e94745\">{{userinfo.scorebdkyfhuang*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\"\r\n\t\t\t\t\t\t\t\tv-if=\"userinfo.scoremaxtypehuang==0 && userinfo.scoredkmaxpercenthuang > 0 && userinfo.scoredkmaxpercenthuang<100\">\r\n\t\t\t\t\t\t\t\t最多可抵扣订单金额的{{userinfo.scoredkmaxpercenthuang}}%</view>\r\n\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtypehuang==1\">\r\n\t\t\t\t\t\t\t\t最多可抵扣{{userinfo.scoredkmaxmoneyhuang}}元</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">使用{{t('红包')}}抵扣\r\n\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view style=\"width: 100%; height:110rpx;\"></view>\r\n\t\t\t\t<view class=\"footer flex notabbarbot\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{alltotalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" :disabled=\"submitDisabled\">\r\n\t\t\t\t\t\t提交订单</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t\t<view v-if=\"invoiceShow\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请填写开票信息</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content invoiceBox\">\r\n\t\t\t\t\t\t<form @submit=\"invoiceFormSubmit\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">发票类型</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeOrderType\" name=\"invoice_type\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(1,invoice_type)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"invoice_type_select == 1 ? true : false\"></radio>普通发票\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"radio\" v-if=\"inArray(2,invoice_type)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"invoice_type_select == 2 ? true : false\"></radio>增值税专用发票\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">抬头类型</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"inputDisabled\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.name_type == 1\">个人</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"invoice && invoice.name_type == 2\">公司</text>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"changeNameType\" name=\"name_type\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio value=\"1\" :checked=\"name_type_select == 1 ? true : false\" :disabled=\"name_type_personal_disabled ? true : false\"></radio>个人\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio value=\"2\" :checked=\"name_type_select == 2 ? true : false\"></radio>公司\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">抬头名称</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"抬头名称\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"invoice_name\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.invoice_name : ''\" ></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"name_type_select == 2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">公司税号</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"公司税号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tax_no\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.tax_no : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">注册地址</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册地址\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"address\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.address : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">注册电话</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"注册电话\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tel\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.tel : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">开户银行</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"开户银行\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_name\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.bank_name : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"invoice_type_select == 2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">银行账号</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"银行账号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"bank_account\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.bank_account : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">手机号</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票手机号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"mobile\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.mobile : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">邮箱</text>\r\n\t\t\t\t\t\t\t\t\t<input class=\"t2\" type=\"text\" placeholder=\"接收电子发票邮箱\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"email\" :disabled=\"inputDisabled\" :value=\"invoice ? invoice.email : ''\"></input>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<button class=\"btn\" form-type=\"submit\" :style=\"{background:t('color1')}\">确定</button>\r\n\t\t\t\t\t\t\t<view style=\"padding-top:30rpx\"></view>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<couponlist :couponlist=\"allbuydata[bid].couponList\" :choosecoupon=\"true\"\r\n\t\t\t\t\t\t\t:selectedrids=\"allbuydata[bid].couponrids\" :bid=\"bid\" @chooseCoupon=\"chooseCoupon\">\r\n\t\t\t\t\t\t</couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tclass=\"popup__title-text\">请选择{{allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送'}}时间</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"hidePstimeDialog\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"pstime-item\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstimeArr\"\r\n\t\t\t\t\t\t\t:key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\"\r\n\t\t\t\t\t\t\t\t:style=\"allbuydata[nowbid].freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"cuxiaovisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"cuxiao-desc\">\r\n\t\t\t\t\t\t\t<block v-if=\"multi_promotion\">\r\n\t\t\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changecxMulti\" data-id=\"0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">不使用促销</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid === 0 ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in allbuydata[bid].cuxiaolist\" :key=\"index\" class=\"cuxiao-item\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"changecxMulti\" :data-id=\"item.id\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#333;padding-left:20rpx\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0\" style=\"margin-top:8rpx;font-size:22rpx;color:#FF6600;padding:8rpx 16rpx;background:#FFF8EE;border-radius:8rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_jifen > 0\">赠送积分:{{item.give_jifen}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_money > 0\">赠送余额:{{item.give_money}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_cash_coupon > 0\">赠送现金券:{{item.give_cash_coupon}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_yellow_points > 0\">赠送红包:{{item.give_yellow_points}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxids.indexOf(item.id) !== -1 ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"cuxiao-item\" @tap=\"changecx\" data-id=\"0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"color:#333\">不使用促销</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid == 0 ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in allbuydata[bid].cuxiaolist\" :key=\"index\" class=\"cuxiao-item\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"changecx\" :data-id=\"item.id\" :data-index=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#333;padding-left:20rpx\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0\" style=\"margin-top:8rpx;font-size:22rpx;color:#FF6600;padding:8rpx 16rpx;background:#FFF8EE;border-radius:8rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_jifen > 0\">赠送积分:{{item.give_jifen}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_money > 0\">赠送余额:{{item.give_money}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_cash_coupon > 0\">赠送现金券:{{item.give_cash_coupon}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_yellow_points > 0\">赠送红包:{{item.give_yellow_points}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"cxid==item.id ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view id=\"cxproinfo\" v-if=\"cuxiaoinfo.product\" style=\"padding:0 40rpx\">\r\n\t\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t\t<view class=\"item flex\" style=\"background:#f5f5f5\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + cuxiaoinfo.product.id\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"cuxiaoinfo.product.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{cuxiaoinfo.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{cuxiaoinfo.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-weight:bold;\">￥{{cuxiaoinfo.guige.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × 1</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"cuxiaoinfo.give_jifen > 0 || cuxiaoinfo.give_money > 0 || cuxiaoinfo.give_cash_coupon > 0 || cuxiaoinfo.give_yellow_points > 0\" style=\"margin-top:16rpx;font-size:24rpx;color:#FF6600;padding:12rpx 20rpx;background:#FFF8EE;border-radius:8rpx;\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"cuxiaoinfo.give_jifen > 0\">赠送积分:{{cuxiaoinfo.give_jifen}} </text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"cuxiaoinfo.give_money > 0\">赠送余额:{{cuxiaoinfo.give_money}} </text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"cuxiaoinfo.give_cash_coupon > 0\">赠送现金券:{{cuxiaoinfo.give_cash_coupon}} </text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"cuxiaoinfo.give_yellow_points > 0\">赠送红包:{{cuxiaoinfo.give_yellow_points}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view v-if=\"cuxiaoList && cuxiaoList.info && cuxiaoList.info.length > 0\" style=\"padding:0 40rpx\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, idx) in cuxiaoList.info\" :key=\"idx\" class=\"cuxiao-item-detail\">\r\n\t\t\t\t\t\t\t\t<view class=\"cuxiao-item-title\" style=\"margin:20rpx 0 10rpx;font-weight:bold;color:#333;\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.product\" class=\"product\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item flex\" style=\"background:#f5f5f5\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'/shopPackage/shop/product?id=' + item.product.id\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"item.guige\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.guige\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × 1</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0\" \r\n\t\t\t\t\t\t\t\t\tstyle=\"margin-top:10rpx;font-size:24rpx;color:#FF6600;padding:12rpx 20rpx;background:#FFF8EE;border-radius:8rpx;\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_jifen > 0\">赠送积分:{{item.give_jifen}} </text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_money > 0\">赠送余额:{{item.give_money}} </text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_cash_coupon > 0\">赠送现金券:{{item.give_cash_coupon}} </text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.give_yellow_points > 0\">赠送红包:{{item.give_yellow_points}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\r\n\t\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tstyle=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\"\r\n\t\t\t\t\t\t\t\t:style=\"{background:t('color1')}\" @tap=\"chooseCuxiao\">确 定</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"type11visible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">选择物流</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"cuxiao-desc\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in allbuydata[bid].freightList[allbuydata[bid].freightkey].type11pricedata\"\r\n\t\t\t\t\t\t\t\t:key=\"index\" @tap=\"changetype11\" :data-index=\"index\" style=\"padding:0 30rpx 20rpx 40rpx\"\r\n\t\t\t\t\t\t\t\tv-if=\"address.id && address.province==item.province && address.city==item.city && address.district==item.area\">\r\n\t\t\t\t\t\t\t\t<view class=\"cuxiao-item\" style=\"padding:0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"color:#333;font-weight:bold;\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"type11key==index ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view style=\"margin-left:20rpx\">发货: {{item.send_address}} - {{item.send_tel}}</view>\r\n\t\t\t\t\t\t\t\t<view style=\"margin-left:20rpx\">收货: {{item.receive_address}} - {{item.receive_tel}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"width:100%; height:120rpx;\"></view>\r\n\t\t\t\t\t\t<view style=\"width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tstyle=\"width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;\"\r\n\t\t\t\t\t\t\t\t:style=\"{background:t('color1')}\" @tap=\"chooseType11\">确 定</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"membervisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\" style=\"height: 1100rpx;\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择指定会员</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx;\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"member_search\">\r\n\t\t\t\t\t\t\t<view style=\"width:130rpx;color:#333;flex-shrink:0\">选择地区</view>\r\n\t\t\t\t\t\t\t<uni-data-picker :localdata=\"items\" :border=\"false\" :placeholder=\"regiondata || '请选择省市区'\" @change=\"regionchange2\" class=\"flex1\" style=\"overflow:hidden\"></uni-data-picker>\r\n\t\t\t\t\t\t\t<view class=\"searchMemberButton\" @click=\"memberSearch\" style=\"flex-shrink:0\">检索用户</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"memberlist\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item2,i) in memberList\" :key=\"i\" class=\"memberitem\" @tap=\"checkMember\" :data-info=\"item2\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item2.headimg\" @tap.stop=\"showmemberinfo\" :data-mid=\"item2.id\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-col\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t1\" @tap.stop=\"showmemberinfo\" :data-mid=\"item2.id\">{{item2.nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t<view>{{item2.province}} {{item2.city}} {{item2.area}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"checkMem.id==item2.id ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" src=\"/static/img/checkd.png\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<view v-if=\"memberinfovisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"memberinfoClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\" style=\"height: 1100rpx;\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">查看资料</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx;\"\r\n\t\t\t\t\t\t\**********=\"memberinfoClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-for=\"item in selectmemberinfo\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-else>{{item[1]}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 眼镜档案 -->\r\n\t\t\t<view v-if=\"isshowglass\" class=\"popup__container glass_popup\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideglass\"></view>\r\n\t\t\t\t<view class=\"popup__modal\" style=\"height: 1100rpx;\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">视力档案</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx;\"\r\n\t\t\t\t\t\t\**********=\"hideglass\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<radio-group @change=\"chooseglass\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in glassrecordlist\" :key=\"index\">\r\n\t\t\t\t\t\t<label>\r\n\t\t\t\t\t\t\t<view class=\"glassitem\" :class=\"grid==item.id?'on':''\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio\"><radio :color=\"t('color1')\" :checked=\"grid==item.id?true:false\" :value=\"''+index\"></radio></view>\r\n\t\t\t\t\t\t\t\t<view class=\"gcontent\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"glassrow\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.type==1?'近视':'远视'}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{item.is_ats==1?'散光':''}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.degress_right}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">右眼</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.degress_left}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">左眼</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.ipd}}mm</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">瞳距</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"item.is_ats==1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"glassrow bt\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.ats_right}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">柱镜右眼</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.ats_left}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">柱镜左眼</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.ats_zright}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">轴位右眼</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"glasscol\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.ats_zleft}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">轴位左眼</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 眼镜档案 -->\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\ttest:'test',\r\n\t\t\t\thavetongcheng: 0,\r\n\t\t\t\taddress: [],\r\n\t\t\t\tmemberList: [],\r\n\t\t\t\tcheckMem:{},\r\n\t\t\t\tusescore: 0,\r\n\t\t\t\tscoredk_money: 0,\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tcuxiaovisible: false,\r\n\t\t\t\tmembervisible: false,\r\n\t\t\t\tmemberinfovisible:false,\r\n\t\t\t\tselectmemberinfo:{},\r\n\t\t\t\tbid: 0,\r\n\t\t\t\tnowbid: 0,\r\n\t\t\t\tneedaddress: 1,\r\n\t\t\t\tlinkman: '',\r\n\t\t\t\tsingleFreight: 0,\r\n\t\t\t\ttotalFreight: 0,\r\n\t\t\t\ttel: '',\r\n\t\t\t\tuserinfo: {},\r\n\t\t\t\tpstimeDialogShow: false,\r\n\t\t\t\tpstimeIndex: -1,\r\n\t\t\t\tmanjian_money: 0,\r\n\t\t\t\tcxid: 0,\r\n\t\t\t\tcxids: [],\r\n\t\t\t\tlatitude: \"\",\r\n\t\t\t\tlongitude: \"\",\r\n\t\t\t\tallbuydata: {},\r\n\t\t\t\tallbuydatawww: {},\r\n\t\t\t\talltotalprice: \"\",\r\n\t\t\t\tcuxiaoinfo: false,\r\n\t\t\t\tcuxiaoList: {},\r\n\t\t\t\ttype11visible: false,\r\n\t\t\t\ttype11key: -1,\r\n\t\t\t\tregiondata: '',\r\n\t\t\t\titems: [],\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\tbuy_selectmember:false,\r\n\t\t\t\tmulti_promotion:0,\r\n\t\t\t\tstoreshowall:false,\r\n\t\t\t\torder_change_price:false,\r\n\t\t\t\tinvoiceShow:false,\r\n\t\t\t\tinvoice:{},\r\n\t\t\t\tinvoice_type:[],\r\n\t\t\t\tinvoice_type_select:1,\r\n\t\t\t\tname_type_select:1,\r\n\t\t\t\tname_type_personal_disabled:false,\r\n\t\t\t\tinputDisabled:false,\r\n\t\t\t\tsubmitDisabled:false,\r\n\t\t\t\tpstype3needAddress:false,\r\n\t\t\t\tisshowglass:false,\r\n\t\t\t\tglassrecordlist:[],\r\n\t\t\t\tgrid:0,\r\n\t\t\t\tcurindex:-1,\r\n\t\t\t\tcurindex2:-1,\r\n\t\t\t\tis_yh:0,//是否存在优惠\r\n\t\t\t\tyh_prices:0,\r\n\t\t\t\tyh_nums:0,\r\n\t\t\t\tnewArr:[],\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\t\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\t\r\n\t\t\tthis.getdata();\r\n\t\t\tthis.newArr = [];\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiShop/buy', {\r\n\t\t\t\t\tprodata: that.opt.prodata\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\t// console.log('返回信息');\r\n\t\t\t\t\t// console.log(res);\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\tapp.alert(res.msg, function() {\r\n\t\t\t\t\t\t\t\tif (res.url) {\r\n\t\t\t\t\t\t\t\t\tapp.goto(res.url);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (res.url) {\r\n\t\t\t\t\t\t\tapp.goto(res.url);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tapp.alert('您没有权限购买该订单');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.havetongcheng = res.havetongcheng;\r\n\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\t\tthat.singleFreight = res.single_freight\r\n\t\t\t\t\tthat.totalFreight = res.total_freight\r\n\t\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\t\tthat.buy_selectmember = res.buy_selectmember;\r\n\t\t\t\t\tthat.order_change_price = res.order_change_price;\r\n\t\t\t\t\tthat.pstype3needAddress = res.pstype3needAddress;\r\n\t\t\t\t\tif(that.buy_selectmember){\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area2.json',\r\n\t\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.allbuydata = res.allbuydata;\r\n\t\t\t\t\tthat.allbuydatawww = JSON.parse(JSON.stringify(res.allbuydata));\r\n\t\t\t\t\t// console.log(that.allbuydata);\r\n\t\t\t\t\t// console.log(res.allbuydata);\r\n\r\n                     console.log( res.allbuydata)\r\n       // 初始化 newArr\r\n                    that.newArr = [];\r\n                    that.is_yh = 0;\r\n\r\n                    // 确保 allbuydata 是数组\r\n                    if (Array.isArray(res.allbuydata)) {\r\n                        res.allbuydata.forEach(function(item, index) {\r\n                            if (item && item.prodata) {\r\n                                let obj = item.prodata;\r\n                                for (let key in obj) {\r\n                                    if (obj[key].yh_danshu == 1 && that.newArr.length == 0) {\r\n                                        console.log(obj[key]);\r\n                                        that.is_yh = 1;\r\n                                        that.newArr.push({\r\n                                            \"product_id\": obj[key].id,\r\n                                            \"is_yh\": obj[key].yh_danshu,\r\n                                            \"yh_num\": obj[key].yh_danshu,\r\n                                            \"yh_price\": obj[key].yh_price\r\n                                        });\r\n                                    }\r\n                                }\r\n                            } else {\r\n                                console.warn('res.allbuydata[' + index + '] 未定义或没有 prodata');\r\n                            }\r\n                        });\r\n                    } else {\r\n                        console.error('res.allbuydata 不是数组或为空');\r\n                    }\r\n\t\t\t\t\t// console.log('222');\r\n\t\t\t\t\tconsole.log(that.newArr);\r\n\t\t\t\t\tconsole.log('结束');\r\n\t\t\t\t\tthat.needLocation = res.needLocation;\r\n\t\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\r\n\t\t\t\t\tthat.scorebdkyfheihei = res.scorebdkyfheihei;\r\n\t\t\t\t\tthat.multi_promotion = res.multi_promotion;\r\n\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// var allbuydata = that.allbuydata;\r\n\t\t\t\t\t// for (var i in allbuydata) {\r\n\t\t\t\t\t// \tallbuydata[i].tempInvoice = uni.getStorageSync('temp_invoice_' + allbuydata[i].bid);\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// that.allbuydata = allbuydata;\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\tif (res.needLocation == 1) {\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\t\t\t\tfor (var i in allbuydata) {\r\n\t\t\t\t\t\t\t\tvar freightList = allbuydata[i].freightList;\r\n\t\t\t\t\t\t\t\tfor (var j in freightList) {\r\n\t\t\t\t\t\t\t\t\tif (freightList[j].pstype == 1 || freightList[j].pstype == 5) {\r\n\t\t\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\r\n\t\t\t\t\t\t\t\t\t\tif (storedata) {\r\n\t\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude,storedata[x].latitude, storedata[x].longitude);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tstoredata.sort(function(a, b) {\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(storedata);\r\n\t\t\t\t\t\t\t\t\t\t\tallbuydata[i].freightList[j].storedata = storedata;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//积分抵扣\r\n\t\t\tscoredk: function(e) {\r\n\t\t\t\tvar usescore = e.detail.value[0];\r\n\t\t\t\tif (!usescore) usescore = 0;\r\n\t\t\t\tthis.usescore = usescore;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//黑积分抵扣\r\n\t\t\tscoredkhei: function(e) {\r\n\t\t\t\tvar usescorehei = e.detail.value[0];\r\n\t\t\t\tif (!usescorehei) usescorehei = 0;\r\n\t\t\t\tthis.usescorehei = usescorehei;\r\n\t\t\t\tconsole.log(this.usescorehei,888)\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//黄积分抵扣\r\n\t\t\tscoredkhuang: function(e) {\r\n\t\t\t\tvar usescorehuang = e.detail.value[0];\r\n\t\t\t\tif (!usescorehuang) usescorehuang = 0;\r\n\t\t\t\tthis.usescorehuang = usescorehuang;\r\n\t\t\t\tconsole.log(this.usescorehuang,999)\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t//余额抵扣\r\n\t\t\tscoredkyu: function(e) {\r\n\t\t\t\tvar usescoreyu = e.detail.value[0];\r\n\t\t\t\tif (!usescoreyu) usescoreyu = 0;\r\n\t\t\t\tthis.usescoreyu = usescoreyu;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tinputLinkman: function(e) {\r\n\t\t\t\tthis.linkman = e.detail.value;\r\n\t\t\t},\r\n\t\t\tinputTel: function(e) {\r\n\t\t\t\tthis.tel = e.detail.value;\r\n\t\t\t},\r\n\t\t\tinputfield: function(e) {\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\t\tallbuydata2[bid][field] = e.detail.value;\r\n\t\t\t\tthis.allbuydata2 = allbuydata2;\r\n\t\t\t},\r\n\t\t\t//选择收货地址\r\n\t\t\tchooseAddress: function() {\r\n\t\t\t\tapp.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\r\n\t\t\t},\r\n\t\t\tinputPrice: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar index2 = e.currentTarget.dataset.index2;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar allbuydatawww = that.allbuydatawww;\r\n\t\t\t\tvar oldprice = allbuydatawww[index]['prodata'][index2].guige.sell_price;\r\n\t\t\t\tif(e.detail.value == '' || parseFloat(e.detail.value) < parseFloat(oldprice)) {\r\n\t\t\t\t\tthat.submitDisabled = true;\r\n\t\t\t\t\tapp.error('不能小于原价:'+oldprice);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.submitDisabled = false;\r\n\t\t\t\tallbuydata[index]['prodata'][index2].guige.sell_price = e.detail.value;\r\n\t\t\t\tallbuydata[index]['product_price'] = (e.detail.value * allbuydata[index]['prodata'][index2].num).toFixed(2);\r\n\t\t\t\t// allbuydata[index].prodatastr = allbuydata[index].prodatastr\r\n\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t\tconsole.log(allbuydata[index]);\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar address = that.address;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar alltotalprice = 0;\r\n\t\t\t\tvar allfreight_price = 0;\r\n\t\t\t\tvar needaddress = 0;\r\n\t\t\t\t// console.log(allbuydata)\r\n\t\t\t\tfor (var k in allbuydata) {\r\n\t\t\t\t\tvar product_price = parseFloat(allbuydata[k].product_price);\r\n\t\t\t\t\tvar diy_amount = parseFloat(allbuydata[k].diy_amount);\r\n\t\t\t\t\tvar leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣\r\n\t\t\t\t\tvar manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动\r\n\t\t\t\t\tvar coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 \r\n\t\t\t\t\tvar cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动 \r\n\t\t\t\t\tvar invoice_money = parseFloat(allbuydata[k].invoice_money); //+发票 \r\n\t\t\t\t\t//var diy_amount = parseFloat(allbuydata[k].diy_amount); //包装价格\r\n\t\t\t\t\t//算运费\r\n\t\t\t\t\tvar freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];\r\n\t\t\t\t\tvar freight_price = freightdata['freight_price'];\r\n\t\t\t\t\tif (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\r\n\t\t\t\t\t\tneedaddress = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.pstype3needAddress && (freightdata.pstype == 3 || freightdata.pstype == 4 || freightdata.pstype == 5)) {\r\n\t\t\t\t\t\tneedaddress = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (allbuydata[k].coupontype == 4) {\r\n\t\t\t\t\t\tfreight_price = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money;\r\n\t\t\t\t\tvar totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money + diy_amount;\r\n\t\t\t\t\tif (totalprice < 0) totalprice = 0; //优惠券不抵扣运费\r\n\r\n\t\t\t\t\ttotalprice = totalprice + freight_price;\r\n\t\t\t\t\tallbuydata[k].freight_price = freight_price.toFixed(2);\r\n\t\t\t\t\tif(allbuydata[k].business.invoice && allbuydata[k].business.invoice_rate > 0 && allbuydata[k].tempInvoice){\r\n\t\t\t\t\t\tvar invoice_money = totalprice * parseFloat(allbuydata[k].business.invoice_rate) / 100;\r\n\t\t\t\t\t\tallbuydata[k].invoice_money = invoice_money.toFixed(2);\r\n\t\t\t\t\t\ttotalprice = totalprice + invoice_money;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log('invoice_money');\r\n\t\t\t\t\tconsole.log(invoice_money);\r\n\t\t\t\t\t\r\n\t\t\t\t\tallbuydata[k].totalprice = totalprice.toFixed(2);\r\n\t\t\t\t\talltotalprice += totalprice;\r\n\t\t\t\t\talltotalprice += that.totalFreight;\r\n\t\t\t\t\tallfreight_price += freight_price;\r\n\t\t\t\t}\r\n\t\t\t\tthat.needaddress = needaddress;\r\n\t\t\t\tif (that.usescore) {\r\n\t\t\t\t\tvar scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scoredk_money = 0;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.usescorehei) {\r\n\t\t\t\t\tvar scorebdkyfhei = parseFloat(that.userinfo.scorebdkyfhei); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scorebdkyfhei = 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (that.usescorehuang) {\r\n\t\t\t\t\tvar scorebdkyfhuang = parseFloat(that.userinfo.scorebdkyfhuang); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scorebdkyfhuang = 0;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.usescoreyu) {\r\n\t\t\t\t\tvar scorebdkyfyu = parseFloat(that.userinfo.scorebdkyfyu); //-余额抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar scorebdkyfyu = 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tvar oldalltotalprice = alltotalprice;\r\n\t\t\t\talltotalprice = alltotalprice - scoredk_money;\r\n\t\t\t\t// alltotalprice = alltotalprice - scorebdkyfhei;\r\n\t\t\t\tif (alltotalprice < 0) alltotalprice = 0;\r\n\r\n\t\t\t\tif (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {\r\n\t\t\t\t\t//积分不抵扣运费\r\n\t\t\t\t\talltotalprice = allfreight_price;\r\n\t\t\t\t\tscoredk_money = oldalltotalprice - allfreight_price;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.scorebdkyfhei == '1' && scorebdkyfhei > 0 && alltotalprice < allfreight_price) {\r\n\t\t\t\t\t//黑积分不抵扣运费\r\n\t\t\t\t\talltotalprice = allfreight_price;\r\n\t\t\t\t\tscorebdkyfhei = oldalltotalprice - allfreight_price;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.scorebdkyfhuang == '1' && scorebdkyfhuang > 0 && alltotalprice < allfreight_price) {\r\n\t\t\t\t\t//黄积分不抵扣运费\r\n\t\t\t\t\talltotalprice = allfreight_price;\r\n\t\t\t\t\tscorebdkyfhuang = oldalltotalprice - allfreight_price;\r\n\t\t\t\t}\r\n\t\t\t\tif (that.scorebdkyfyu == '1' && scorebdkyfyu > 0 && alltotalprice < allfreight_price) {\r\n\t\t\t\t\t//黑积分不抵扣运费\r\n\t\t\t\t\talltotalprice = allfreight_price;\r\n\t\t\t\t\tscorebdkyfyu = oldalltotalprice - allfreight_price;\r\n\t\t\t\t}\r\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\t\t\t\tvar scoremaxtype = parseInt(that.userinfo.scoremaxtype);\r\n\t\t\t\tvar scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);\r\n\t\t\t\t\r\n\t\t\t\tvar scoredkmaxpercenthei = parseFloat(that.userinfo.scoredkmaxpercenthei); //最大抵扣比例\r\n\t\t\t\tvar scoremaxtypehei = parseInt(that.userinfo.scoremaxtypehei);\r\n\t\t\t\tvar scoredkmaxmoneyhei = parseFloat(that.userinfo.scoredkmaxmoneyhei);\r\n\t\t\t\t\r\n\t\t\t\tvar scoredkmaxpercenthuang = parseFloat(that.userinfo.scoredkmaxpercenthuang); //最大抵扣比例\r\n\t\t\t\tvar scoremaxtypehuang = parseInt(that.userinfo.scoremaxtypehuang);\r\n\t\t\t\tvar scoredkmaxmoneyhuang = parseFloat(that.userinfo.scoredkmaxmoneyhuang);\r\n\t\t\t\t\r\n\t\t\t\tvar scoredkmaxpercentyu = parseFloat(that.userinfo.scoredkmaxpercentyu); //最大抵扣比例\r\n\t\t\t\tvar scoremaxtypeyu = parseInt(that.userinfo.scoremaxtypeyu);\r\n\t\t\t\tvar scoredkmaxmoneyyu = parseFloat(that.userinfo.scoredkmaxmoneyyu);\r\n\t\t\t\t\r\n\r\n\t\t\t\tif (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 &&\r\n\t\t\t\t\tscoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t\tscoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;\r\n\t\t\t\t\talltotalprice = oldalltotalprice - scoredk_money;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scoredk_money;\r\n\t\t\t\t\t\r\n\t\t\t\t} else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tscoredk_money = scoredkmaxmoney;\r\n\t\t\t\t\talltotalprice = oldalltotalprice - scoredk_money;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scoredk_money;\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tif (scoremaxtypehei == 0 && scorebdkyfhei > 0 && scoredkmaxpercenthei > 0 && scoredkmaxpercenthei < 100 &&\r\n\t\t\t\t\tscorebdkyfhei > oldalltotalprice * scoredkmaxpercenthei * 0.01) {\r\n\t\t\t\t\tscorebdkyfhei = alltotalprice * scoredkmaxpercenthei * 0.01;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfhei;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scorebdkyfhei;\r\n\t\t\t\t} else if (scoremaxtypehei == 1 && scorebdkyfhei > scoredkmaxmoneyhei) {\r\n\t\t\t\t\tscorebdkyfhei = scoredkmaxmoneyhei;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfhei;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scorebdkyfhei;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t//黄积分\r\n\t\t\t\tif (scoremaxtypehuang == 0 && scorebdkyfhuang > 0 && scoredkmaxpercenthuang > 0 && scoredkmaxpercenthuang < 100 &&\r\n\t\t\t\t\tscorebdkyfhuang > oldalltotalprice * scoredkmaxpercenthuang * 0.01) {\r\n\t\t\t\t\tscorebdkyfhuang = alltotalprice * scoredkmaxpercenthuang * 0.01;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfhuang;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scorebdkyfhuang;\r\n\t\t\t\t} else if (scoremaxtypehuang == 1 && scorebdkyfhuang > scoredkmaxmoneyhuang) {\r\n\t\t\t\t\tscorebdkyfhuang = scoredkmaxmoneyhuang;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfhuang;\r\n\t\t\t\t\t// oldalltotalprice = oldalltotalprice - scorebdkyfhuang;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tif (scoremaxtypeyu == 0 && scorebdkyfyu > 0 && scoredkmaxpercentyu > 0 && scoredkmaxpercentyu < 100 &&\r\n\t\t\t\t\tscorebdkyfyu > oldalltotalprice * scoredkmaxpercentyu * 0.01) {\r\n\t\t\t\t\tscorebdkyfyu = alltotalprice * scoredkmaxpercentyu * 0.01;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfyu;\r\n\t\t\t\t} else if (scoremaxtypeyu == 1 && scorebdkyfyu > scoredkmaxmoneyyu) {\r\n\t\t\t\t\tscorebdkyfyu = scoredkmaxmoneyyu;\r\n\t\t\t\t\talltotalprice = alltotalprice - scorebdkyfyu;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (alltotalprice < 0) alltotalprice = 0;\r\n\t\t\t\talltotalprice = alltotalprice.toFixed(2);\r\n\t\t\t\tthat.alltotalprice = alltotalprice;\r\n\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t},\r\n\t\t\tchangeFreight: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar freightList = allbuydata[bid].freightList;\r\n\t\t\t\tif(freightList[index].pstype==1 && freightList[index].storedata.length < 1) {\r\n\t\t\t\t\tapp.error('无可自提门店');return;\r\n\t\t\t\t}\r\n\t\t\t\tif(freightList[index].pstype==5 && freightList[index].storedata.length < 1) {\r\n\t\t\t\t\tapp.error('无可配送门店');return;\r\n\t\t\t\t}\r\n\t\t\t\tallbuydata[bid].freightkey = index;\r\n\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = [];\r\n\t\t\t},\r\n\t\t\tchooseFreight: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\t// console.log(bid);\r\n\t\t\t\t// console.log(allbuydata);\r\n\t\t\t\tvar freightList = allbuydata[bid].freightList;\r\n\t\t\t\tvar itemlist = [];\r\n\r\n\t\t\t\tfor (var i = 0; i < freightList.length; i++) {\r\n\t\t\t\t\titemlist.push(freightList[i].name);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: itemlist,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\tallbuydata[bid].freightkey = res.tapIndex;\r\n\t\t\t\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchoosePstime: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar freightkey = allbuydata[bid].freightkey;\r\n\t\t\t\tvar freightList = allbuydata[bid].freightList;\r\n\t\t\t\tvar freight = freightList[freightkey];\r\n\t\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\r\n\t\t\t\tvar itemlist = [];\r\n\t\t\t\tfor (var i = 0; i < pstimeArr.length; i++) {\r\n\t\t\t\t\titemlist.push(pstimeArr[i].title);\r\n\t\t\t\t}\r\n\t\t\t\tif (itemlist.length == 0) {\r\n\t\t\t\t\tapp.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.nowbid = bid;\r\n\t\t\t\tthat.pstimeDialogShow = true;\r\n\t\t\t\tthat.pstimeIndex = -1;\r\n\t\t\t},\r\n\t\t\tpstimeRadioChange: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar pstimeIndex = e.currentTarget.dataset.index;\r\n\t\t\t\t// console.log(pstimeIndex)\r\n\t\t\t\tvar nowbid = that.nowbid;\r\n\t\t\t\tvar freightkey = allbuydata[nowbid].freightkey;\r\n\t\t\t\tvar freightList = allbuydata[nowbid].freightList;\r\n\t\t\t\tvar freight = freightList[freightkey];\r\n\t\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\r\n\t\t\t\tvar choosepstime = pstimeArr[pstimeIndex];\r\n\t\t\t\tallbuydata[nowbid].pstimetext = choosepstime.title;\r\n\t\t\t\tallbuydata[nowbid].freight_time = choosepstime.value;\r\n\t\t\t\tthat.allbuydata = allbuydata\r\n\t\t\t\tthat.pstimeDialogShow = false;\r\n\t\t\t},\r\n\t\t\thidePstimeDialog: function() {\r\n\t\t\t\tthis.pstimeDialogShow = false;\r\n\t\t\t},\r\n\t\t\tchooseCoupon: function(e) {\r\n\t\t\t\tvar allbuydata = this.allbuydata;\r\n\t\t\t\tvar bid = e.bid;\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t\tvar couponkey = e.key;\r\n\t\t\t\tvar oldcoupons = allbuydata[bid].coupons;\r\n\t\t\t\tvar oldcouponrids = allbuydata[bid].couponrids;\r\n\t\t\t\tvar couponList = allbuydata[bid].couponList;\r\n\t\t\t\tif (app.inArray(couponrid,oldcouponrids)) {\r\n\t\t\t\t\tvar coupons = [];\r\n\t\t\t\t\tvar couponrids = [];\r\n\t\t\t\t\tfor(var i in oldcoupons){\r\n\t\t\t\t\t\tif(oldcoupons[i].id != couponrid){\r\n\t\t\t\t\t\t\tcoupons.push(oldcoupons[i]);\r\n\t\t\t\t\t\t\tcouponrids.push(oldcoupons[i].id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcoupons = oldcoupons;\r\n\t\t\t\t\tcouponrids = oldcouponrids;\r\n\t\t\t\t\tconsole.log(allbuydata[bid].coupon_peruselimit + '---' + oldcouponrids.length);\r\n\t\t\t\t\tif(allbuydata[bid].coupon_peruselimit > oldcouponrids.length){\r\n\t\t\t\t\t\tconsole.log('xxxx');\r\n\t\t\t\t\t\tcoupons.push(couponList[couponkey]);\r\n\t\t\t\t\t\tcouponrids.push(couponList[couponkey].id);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(allbuydata[bid].coupon_peruselimit > 1){\r\n\t\t\t\t\t\t\tapp.error('最多只能选用'+allbuydata[bid].coupon_peruselimit+'张');\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tcoupons = [couponList[couponkey]];\r\n\t\t\t\t\t\t\tcouponrids = [couponrid];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(coupons);\r\n\t\t\t\tconsole.log(couponrids);\r\n\t\t\t\tallbuydata[bid].coupons = coupons;\r\n\t\t\t\tallbuydata[bid].couponrids = couponrids;\r\n\t\t\t\tvar coupon_money = 0;\r\n\t\t\t\tvar coupontype = 1;\r\n\t\t\t\tfor(var i in coupons){\r\n\t\t\t\t\tif(coupons[i]['type'] == 4){\r\n\t\t\t\t\t\tcoupontype = 4;\r\n\t\t\t\t\t}else if(coupons[i]['type'] == 10){\r\n\t\t\t\t\t\tcoupon_money += coupons[i]['thistotalprice'] * (100-coupons[i]['discount']) * 0.01;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tcoupon_money += coupons[i]['money']\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tallbuydata[bid].coupontype = coupontype;\r\n\t\t\t\tallbuydata[bid].coupon_money = coupon_money;\r\n\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tchoosestore: function(e) {\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.index;\r\n\t\t\t\tvar allbuydata = this.allbuydata;\r\n\t\t\t\tvar buydata = allbuydata[bid];\r\n\t\t\t\tvar freightkey = buydata.freightkey\r\n\t\t\t\tallbuydata[bid].freightList[freightkey].storekey = storekey\r\n\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t},\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar needaddress = that.needaddress;\r\n\t\t\t\tvar addressid = this.address.id;\r\n\t\t\t\tvar checkmemid = this.checkMem.id;\r\n\t\t\t\tvar linkman = this.linkman;\r\n\t\t\t\tvar tel = this.tel;\r\n\t\t\t\tvar usescore = this.usescore;\r\n\t\t\t\tvar usescorehei = this.usescorehei;\r\n\t\t\t\tvar usescoreyu = this.usescoreyu;\r\n\t\t\t\tvar usescorehuang = this.usescorehuang;\r\n\t\t\t\tvar frompage = that.opt.frompage ? that.opt.frompage : '';\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar is_yh = that.is_yh;\r\n\t\t\t\tvar yh_prices = that.yh_prices;\r\n\t\t\t\tvar yh_nums = that.yh_nums;\r\n\t\t\t\tvar newarr = that.newArr;\r\n\t\t\t\t\r\n\t\t\t\tif (needaddress == 0) addressid = 0;\r\n\r\n\t\t\t\tif (needaddress == 1 && addressid == undefined) {\r\n\t\t\t\t\tapp.error('请选择收货地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar buydata = [];\r\n\t\t\t\tfor (var i in allbuydata) {\r\n\t\t\t\t\tvar freightkey = allbuydata[i].freightkey;\r\n\t\t\t\t\tif (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {\r\n\t\t\t\t\t\tapp.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 5) {\r\n\t\t\t\t\t\tvar storekey = allbuydata[i].freightList[freightkey].storekey;\r\n\t\t\t\t\t\tvar storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar storeid = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (allbuydata[i].freightList[freightkey].pstype == 11) {\r\n\t\t\t\t\t\tvar type11key = allbuydata[i].type11key;\r\n\t\t\t\t\t\tif (type11key == 0 || !type11key) {\r\n\t\t\t\t\t\t\tapp.error('请选择物流');\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\ttype11key = type11key - 1;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar type11key = 0\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar formdata_fields = allbuydata[i].freightList[freightkey].formdata;\r\n\t\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\t\tvar newformdata = {};\r\n\t\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\r\n\t\t\t\t\t\tvar thisfield = 'form'+allbuydata[i].bid + '_' + j;\r\n\t\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\r\n\t\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\r\n\t\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(j > 0 && formdata_fields[j].val1 == '确认账号' && formdata_fields[j-1].val1 == '充值账号' && formdata[thisfield] != formdata['form'+allbuydata[i].bid + '_' + (j-1)]){\r\n\t\t\t\t\t\t\tapp.alert('两次输入账号不一致');return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tnewformdata['form'+j] = formdata[thisfield];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar couponrid = (allbuydata[i].couponrids).join(',');\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar buydatatemp = {\r\n\t\t\t\t\t\tbid: allbuydata[i].bid,\r\n\t\t\t\t\t\tprodata: allbuydata[i].prodatastr,\r\n\t\t\t\t\t\tcuxiaoid: allbuydata[i].cuxiaoid,\r\n\t\t\t\t\t\tcouponrid: couponrid,\r\n\t\t\t\t\t\tfreight_id: allbuydata[i].freightList[freightkey].id,\r\n\t\t\t\t\t\tfreight_time: allbuydata[i].freight_time,\r\n\t\t\t\t\t\tstoreid: storeid,\r\n\t\t\t\t\t\tformdata:newformdata,\r\n\t\t\t\t\t\ttype11key: type11key,\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(that.order_change_price) {\r\n\t\t\t\t\t\tbuydatatemp.prodataList = allbuydata[i].prodata;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(allbuydata[i].business.invoice) {\r\n\t\t\t\t\t\tbuydatatemp.invoice = allbuydata[i].tempInvoice;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbuydata.push(buydatatemp);\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(buydata);return;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiShop/createOrder', {\r\n\t\t\t\t\tfrompage: frompage,\r\n\t\t\t\t\tbuydata: buydata,\r\n\t\t\t\t\taddressid: addressid,\r\n\t\t\t\t\tlinkman: linkman,\r\n\t\t\t\t\ttel: tel,\r\n\t\t\t\t\tcheckmemid:checkmemid,\r\n\t\t\t\t\tusescore: usescore,\r\n\t\t\t\t\tusescorehei: usescorehei,\r\n\t\t\t\t\tusescoreyu:usescoreyu,\r\n\t\t\t\t\tusescorehuang:usescorehuang,\r\n\t\t\t\t\tis_yh:is_yh,\r\n\t\t\t\t\tyh_prices:yh_prices,\r\n\t\t\t\t\tyh_nums:yh_nums,\r\n\t\t\t\t\tnewarr:newarr,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\t//that.showsuccess(res.data.msg);\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//app.error('订单编号：' +res.payorderid);\r\n\t\t\t\t\tif(res.payorderid)\r\n\t\t\t\t\tapp.goto('/pages/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowCouponList: function(e) {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t},\r\n\t\t\tshowInvoice: function(e) {\r\n\t\t\t\tthis.invoiceShow = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tlet index = e.currentTarget.dataset.index;\r\n\t\t\t\tthis.invoice_type = this.allbuydata[index].business.invoice_type;\r\n\t\t\t\tthis.invoice = this.allbuydata[index].tempInvoice;\r\n\t\t\t},\r\n\t\t\tchangeOrderType: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tif(value == 2) {\r\n\t\t\t\t\tthat.name_type_select = 2;\r\n\t\t\t\t\tthat.name_type_personal_disabled = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.name_type_personal_disabled = false;\r\n\t\t\t\t}\r\n\t\t\t\tthat.invoice_type_select = value;\r\n\t\t\t},\r\n\t\t\tchangeNameType: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tthat.name_type_select = value;\r\n\t\t\t},\r\n\t\t\tinvoiceFormSubmit: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\tif(formdata.invoice_name == '') {\r\n\t\t\t\t\tapp.error('请填写抬头名称');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {\r\n\t\t\t\t\t///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/\r\n\t\t\t\t\tapp.error('请填写公司税号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.invoice_type == 2) {\r\n\t\t\t\t\tif(formdata.address == '') {\r\n\t\t\t\t\t\tapp.error('请填写注册地址');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(formdata.tel == '') {\r\n\t\t\t\t\t\tapp.error('请填写注册电话');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(formdata.bank_name == '') {\r\n\t\t\t\t\t\tapp.error('请填写开户银行');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(formdata.bank_account == '') {\r\n\t\t\t\t\t\tapp.error('请填写银行账号');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.mobile != '') {\r\n\t\t\t\t\tif(!/^1[3456789]\\d{9}$/.test(formdata.mobile)){\r\n\t\t\t\t\t\tapp.error(\"手机号码有误，请重填\");\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (formdata.email != '') {\r\n\t\t\t\t\tif(!/^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$/.test(formdata.email)){\r\n\t\t\t\t\t\tapp.error(\"邮箱有误，请重填\");\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(formdata.mobile == '' && formdata.email == '') {\r\n\t\t\t\t\tapp.error(\"手机号和邮箱请填写其中一个\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(formdata);\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tfor (var i in allbuydata) {\r\n\t\t\t\t\tif(allbuydata[i].bid == that.bid)\r\n\t\t\t\t\t\tallbuydata[i].tempInvoice = formdata;\r\n\t\t\t\t}\r\n\t\t\t\tthat.allbuydata = allbuydata;\r\n\t\t\t\tthat.invoiceShow = false;\r\n\t\t\t\t\t// that.loading = true;\r\n\t\t\t\t\t// uni.setStorageSync('temp_invoice_' + that.opt.bid, formdata);\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\tthis.cuxiaovisible = false;\r\n\t\t\t\tthis.type11visible = false;\r\n\t\t\t\tthis.membervisible = false;\r\n\t\t\t\tthis.invoiceShow = false;\r\n\t\t\t},\r\n\t\t\tshowCuxiaoList: function(e) {\r\n\t\t\t\tthis.cuxiaovisible = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t},\r\n\t\t\tchangecx: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cxid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar cxindex = e.currentTarget.dataset.index;\r\n\t\t\t\tconsole.log(cxid);\r\n\t\t\t\tthat.cxid = cxid;\r\n\t\t\t\tif (cxid == 0) {\r\n\t\t\t\t\tthat.cuxiaoinfo = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];\r\n\t\t\t\tconsole.log(cuxiaoinfo.cuxiaomoney)\r\n\t\t\t\tapp.post(\"ApiShop/getcuxiaoinfo\", {\r\n\t\t\t\t\tid: cxid\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {\r\n\t\t\t\t\t\tres.cuxiaomoney = cuxiaoinfo.cuxiaomoney\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 添加赠送信息\r\n\t\t\t\t\tif (cuxiaoinfo.give_jifen > 0) {\r\n\t\t\t\t\t\tres.give_jifen = cuxiaoinfo.give_jifen;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (cuxiaoinfo.give_money > 0) {\r\n\t\t\t\t\t\tres.give_money = cuxiaoinfo.give_money;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (cuxiaoinfo.give_cash_coupon > 0) {\r\n\t\t\t\t\t\tres.give_cash_coupon = cuxiaoinfo.give_cash_coupon;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (cuxiaoinfo.give_yellow_points > 0) {\r\n\t\t\t\t\t\tres.give_yellow_points = cuxiaoinfo.give_yellow_points;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.cuxiaoinfo = res;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchangecxMulti: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar cxid = e.currentTarget.dataset.id;\r\n\t\t\t\tvar cxindex = e.currentTarget.dataset.index;\r\n\t\t\t\tthat.cuxiaoList.length = 0;\r\n\t\t\t\tconsole.log('cxid:'+cxid);\r\n\t\t\t\tif (cxid == 0) {\r\n\t\t\t\t\tthat.cuxiaoinfo = false;\r\n\t\t\t\t\tthat.cxids.length = 0;\r\n\t\t\t\t\tthat.cxid = 0;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar index = that.cxids.indexOf(cxid);\r\n\t\t\t\tif(index === -1){\r\n\t\t\t\t\tthat.cxids.push(cxid);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.cxids.splice(index);\r\n\t\t\t\t}\r\n\t\t\t\tif(that.cxids.length == 0) {\r\n\t\t\t\t\tthat.cxid = 0;\r\n\t\t\t\t\tthat.cuxiaoinfo = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.cxid = '';\r\n\t\t\t\tvar cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];\r\n\t\t\t\tconsole.log(cuxiaoinfo.cuxiaomoney)\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.post(\"ApiShop/getcuxiaoinfo\", {\r\n\t\t\t\t\tid: that.cxids\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\t// if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {\r\n\t\t\t\t\t// \tres.cuxiaomoney = cuxiaoinfo.cuxiaomoney\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// 添加赠送信息\r\n\t\t\t\t\tif (res.info && res.info.length > 0) {\r\n\t\t\t\t\t\tfor (var i = 0; i < res.info.length; i++) {\r\n\t\t\t\t\t\t\tvar item = that.allbuydata[that.bid].cuxiaolist.find(c => c.id == res.info[i].id);\r\n\t\t\t\t\t\t\tif (item) {\r\n\t\t\t\t\t\t\t\tif (item.give_jifen > 0) {\r\n\t\t\t\t\t\t\t\t\tres.info[i].give_jifen = item.give_jifen;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (item.give_money > 0) {\r\n\t\t\t\t\t\t\t\t\tres.info[i].give_money = item.give_money;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (item.give_cash_coupon > 0) {\r\n\t\t\t\t\t\t\t\t\tres.info[i].give_cash_coupon = item.give_cash_coupon;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (item.give_yellow_points > 0) {\r\n\t\t\t\t\t\t\t\t\tres.info[i].give_yellow_points = item.give_yellow_points;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tthat.cuxiaoList = res;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseCuxiao: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = that.bid;\r\n\t\t\t\tvar cxid = that.cxid;\r\n\t\t\t\tvar cxids = that.cxids;\r\n\t\t\t\tconsole.log(cxid == 0)\r\n\t\t\t\tif (cxid == 0 || cxid == '') {\r\n\t\t\t\t\tallbuydata[bid].cuxiaoid = '';\r\n\t\t\t\t\tallbuydata[bid].cuxiao_money = 0;\r\n\t\t\t\t\tallbuydata[bid].cuxiaoname = '不使用促销';\r\n\t\t\t\t\tallbuydata[bid].cuxiaonameArr = [];\r\n\t\t\t\t} else {\r\n\t\t\t\t\tallbuydata[bid].cuxiaoid = [];\r\n\t\t\t\t\tallbuydata[bid].cuxiao_money = 0;\r\n\t\t\t\t\tallbuydata[bid].cuxiaotype = [];\r\n\t\t\t\t\tallbuydata[bid].cuxiaonameArr = [];\r\n\t\t\t\t\tconsole.log(that.cuxiaoList.info)\r\n\t\t\t\t\tif(that.cuxiaoList.info && that.cuxiaoList.info.length > 0) {\r\n\t\t\t\t\t\tfor (var i in that.cuxiaoList.info) {\r\n\t\t\t\t\t\t\tvar cxtype = that.cuxiaoList.info[i].type;\r\n\t\t\t\t\t\t\tconsole.log(cxtype);\r\n\t\t\t\t\t\t\tif (cxtype == 1 || cxtype == 6) {\r\n\t\t\t\t\t\t\t\t//满额立减 满件立减\r\n\t\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'] * -1;\r\n\t\t\t\t\t\t\t} else if (cxtype == 2) {\r\n\t\t\t\t\t\t\t\t//满额赠送\r\n\t\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money += 0;\r\n\t\t\t\t\t\t\t} else if (cxtype == 3) {\r\n\t\t\t\t\t\t\t\t//加价换购  27.8+15.964+41.4\r\n\t\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'];\r\n\t\t\t\t\t\t\t} else if (cxtype == 4 || cxtype == 5) {\r\n\t\t\t\t\t\t\t\t//满额打折 满件打折\r\n\t\t\t\t\t\t\t\tvar cuxiaoMoney = 0;\r\n\t\t\t\t\t\t\t\tfor ( var y in that.allbuydata[bid].cuxiaolist) {\r\n\t\t\t\t\t\t\t\t\tif(that.cuxiaoList.info[i].id == that.allbuydata[bid].cuxiaolist[y].id) {\r\n\t\t\t\t\t\t\t\t\t\tcuxiaoMoney = that.allbuydata[bid].cuxiaolist[y].cuxiaomoney;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tconsole.log('cuxiaoMoney');\r\n\t\t\t\t\t\t\t\tconsole.log(cuxiaoMoney);\r\n\t\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money += cuxiaoMoney * -1\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiaoid.push(that.cuxiaoList.info[i].id);\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiaotype.push(cxtype);\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiaonameArr.push(that.cuxiaoList.info[i]['name']);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.log('allbuydata[bid]');\r\n\t\t\t\t\t\tconsole.log(allbuydata[bid]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar cxtype = that.cuxiaoinfo.info.type;\r\n\t\t\t\t\t\tconsole.log(cxtype);\r\n\t\t\t\t\t\tif (cxtype == 1 || cxtype == 6) {\r\n\t\t\t\t\t\t\t//满额立减 满件立减\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;\r\n\t\t\t\t\t\t} else if (cxtype == 2) {\r\n\t\t\t\t\t\t\t//满额赠送\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money = 0;\r\n\t\t\t\t\t\t} else if (cxtype == 3) {\r\n\t\t\t\t\t\t\t//加价换购  27.8+15.964+41.4\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];\r\n\t\t\t\t\t\t} else if (cxtype == 4 || cxtype == 5) {\r\n\t\t\t\t\t\t\t//var product_price = parseFloat(allbuydata[bid].product_price);\r\n\t\t\t\t\t\t\t//var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣\r\n\t\t\t\t\t\t\t//var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动\r\n\t\t\t\t\t\t\t//满额打折 满件打折\r\n\t\t\t\t\t\t\t//allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;\r\n\t\t\t\t\t\t\tallbuydata[bid].cuxiao_money = that.cuxiaoinfo.cuxiaomoney * -1\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tallbuydata[bid].cuxiaoid = cxid;\r\n\t\t\t\t\t\tallbuydata[bid].cuxiaotype = cxtype;\r\n\t\t\t\t\t\tallbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t\tthis.cuxiaovisible = false;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tshowType11List: function(e) {\r\n\t\t\t\tthis.type11visible = true;\r\n\t\t\t\tthis.bid = e.currentTarget.dataset.bid;\r\n\t\t\t},\r\n\t\t\tchangetype11: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = that.bid;\r\n\t\t\t\tthat.type11key = e.currentTarget.dataset.index;\r\n\t\t\t\t// console.log(that.type11key)\r\n\t\t\t},\r\n\t\t\tchooseType11: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar bid = that.bid;\r\n\t\t\t\tvar type11key = that.type11key;\r\n\t\t\t\tif (type11key == -1) {\r\n\t\t\t\t\tapp.error('请选择物流');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tallbuydata[bid].type11key = type11key + 1;\r\n\t\t\t\t// console.log(allbuydata[bid].type11key)\r\n\t\t\t\tvar freightkey = allbuydata[bid].freightkey;\r\n\t\t\t\tvar freightList = allbuydata[bid].freightList;\r\n\t\t\t\tvar freight_price = parseFloat(freightList[freightkey].type11pricedata[type11key].price);\r\n\t\t\t\tvar product_price = parseFloat(allbuydata[bid].product_price);\r\n\t\t\t\t// console.log(freightList[freightkey].freeset);\r\n\t\t\t\t// console.log(parseFloat(freightList[freightkey].free_price));\r\n\t\t\t\t// console.log(product_price);\r\n\t\t\t\tif (freightList[freightkey].freeset == 1 && parseFloat(freightList[freightkey].free_price) <=\r\n\t\t\t\t\tproduct_price) {\r\n\t\t\t\t\tfreight_price = 0;\r\n\t\t\t\t}\r\n\t\t\t\tallbuydata[bid].freightList[freightkey].freight_price = freight_price;\r\n\r\n\t\t\t\tthis.allbuydata = allbuydata;\r\n\t\t\t\tthis.type11visible = false;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\topenMendian: function(e) {\r\n\t\t\t\tvar allbuydata = this.allbuydata\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\t\tvar frightinfo = allbuydata[bid].freightList[freightkey]\r\n\t\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\t\t// console.log(storeinfo)\r\n\t\t\t\tapp.goto('mendian?id=' + storeinfo.id);\r\n\t\t\t},\r\n\t\t\topenLocation: function(e) {\r\n\t\t\t\tvar allbuydata = this.allbuydata\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\r\n\t\t\t\tvar storekey = e.currentTarget.dataset.storekey;\r\n\t\t\t\tvar frightinfo = allbuydata[bid].freightList[freightkey]\r\n\t\t\t\tvar storeinfo = frightinfo.storedata[storekey];\r\n\t\t\t\t// console.log(storeinfo)\r\n\t\t\t\tvar latitude = parseFloat(storeinfo.latitude);\r\n\t\t\t\tvar longitude = parseFloat(storeinfo.longitude);\r\n\t\t\t\tvar address = storeinfo.name;\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tname: address,\r\n\t\t\t\t\tscale: 13\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\teditorBindPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar bid = e.currentTarget.dataset.bid;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\t// console.log(editorFormdata)\r\n\t\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},\r\n\r\n\t\t\tshowMemberList: function(e) {\r\n\t\t\t\tthis.membervisible = true;\r\n\t\t\t},\r\n\t\t\tregionchange2: function(e) {\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\t// console.log(value[0].text + ',' + value[1].text + ',' + value[2].text);\r\n\t\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text\r\n\t\t\t},\r\n\t\t\tmemberSearch: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// console.log(that.regiondata)\r\n\t\t\t\tapp.post('ApiShop/memberSearch', {\r\n\t\t\t\t\tdiqu: that.regiondata\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar data = res.memberList;\r\n\t\t\t\t\tthat.memberList = data;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcheckMember: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.checkMem = e.currentTarget.dataset.info;\r\n\t\t\t\tthis.membervisible = false;\r\n\t\t\t},\r\n\t\t\tshowmemberinfo:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar mid = e.currentTarget.dataset.mid;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiShop/getmemberuplvinfo',{mid:mid}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.selectmemberinfo = res.info;\r\n\t\t\t\t\tthat.memberinfovisible = true;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tmemberinfoClickMask:function(){\r\n\t\t\t\tthis.memberinfovisible = false;\r\n\t\t\t},\r\n\t\t\tdoStoreShowAll:function(){\r\n\t\t\t\tthis.storeshowall = true;\r\n\t\t\t},\r\n\t\t\tshowglass:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tvar grid = e.currentTarget.dataset.grid;\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tvar index2 = e.currentTarget.dataset.index2;\r\n\t\t\t\tconsole.log(grid)\r\n\t\t\t\tconsole.log(index)\r\n\t\t\t\tconsole.log(index2)\r\n\t\t\t\t// console.log(that.glassrecordlist)\r\n\t\t\t\tif(that.glassrecordlist.length<1){\r\n\t\t\t\t\t//没有数据 就重新请求\r\n\t\t\t\t\tthat.loading;\r\n\t\t\t\t\tapp.post('ApiGlass/myrecord', {pagenum:1,listrow:100}, function (res) {\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t  var datalist = res.data;\r\n\t\t\t\t\t\tconsole.log(datalist)\r\n\t\t\t\t\t\tthat.glassrecordlist = datalist;\r\n\t\t\t\t\t\t// console.log(that.glassrecordlist);\r\n\t\t\t\t\t\t// if(datalist.length>0){\r\n\t\t\t\t\t\t// \t// \r\n\t\t\t\t\t\t// \tthat.isshowglass = true\r\n\t\t\t\t\t\t// }else{\r\n\t\t\t\t\t\t// \tapp.error('无可用的视力档案')\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tthat.isshowglass = true\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.isshowglass = true\r\n\t\t\t\t}\r\n\t\t\t\tthat.curindex = index\r\n\t\t\t\tthat.curindex2 = index2\r\n\t\t\t\tthat.grid = grid\r\n\t\t\t},\r\n\t\t\thideglass:function(e){\r\n\t\t\t\tvar that = this\r\n\t\t\t\tthat.isshowglass = false;\r\n\t\t\t},\r\n\t\t\tchooseglass:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar gindex = e.detail.value;\r\n\t\t\t\tvar allbuydata = that.allbuydata;\r\n\t\t\t\tvar grid = that.grid;\r\n\t\t\t\tvar index = that.curindex;\r\n\t\t\t\tvar index2 = that.curindex2;\r\n\t\t\t\tconsole.log(gindex+'-'+that.curindex+'-'+that.curindex2)\r\n\t\t\t\tvar glassrecordlist = that.glassrecordlist;\r\n\t\t\t\tvar product = allbuydata[index]['prodata'][index2].product\r\n\t\t\t\tvar sid = glassrecordlist[gindex].id\r\n\t\t\t\tif(grid==sid){\r\n\t\t\t\t\tproduct.glassrecord = {};\r\n\t\t\t\t\tthat.grid = 0;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tproduct.glassrecord = glassrecordlist[gindex]\r\n\t\t\t\t\tthat.grid = glassrecordlist[gindex].id\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthat.allbuydata[index]['prodata'][index2]['product'] = product;\r\n\t\t\t\tthat.isshowglass = false;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}\r\n.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}\r\n.address-add .f1 {margin-right: 20rpx}\r\n.address-add .f1 .img {width: 66rpx;height: 66rpx;}\r\n.address-add .f2 {color: #666;}\r\n.address-add .f3 {width: 26rpx;height: 26rpx;}\r\n.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;\r\n    text-align: right;}\r\n.linkitem .f1 {width: 160rpx;color: #111111}\r\n.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}\r\n.buydata {width: 94%;margin: 0 3%;background: #fff;margin-bottom: 20rpx;border-radius: 20rpx;}\r\n.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}\r\n.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}\r\n.bcontent {width: 100%;padding: 0 20rpx}\r\n.product {width: 100%;border-bottom: 1px solid #f4f4f4}\r\n.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}\r\n.product .item:last-child {border: none}\r\n.product .info {padding-left: 20rpx;}\r\n.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.product .info .f2 {color: #999999;font-size: 24rpx}\r\n.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n.product image {width: 140rpx;height: 140rpx}\r\n.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}\r\n.freight .f1 {color: #333;margin-bottom: 10rpx}\r\n.freight .f2 {color: #111111;text-align: right;flex: 1}\r\n.freight .f3 {width: 24rpx;height: 28rpx;}\r\n.freighttips {color: red;font-size: 24rpx;}\r\n.freight-ul {width: 100%;display: flex;}\r\n.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}\r\n.inputPrice {border: 1px solid #ddd; width: 200rpx; height: 40rpx; border-radius: 10rpx; padding: 0 4rpx;}\r\n\r\n.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}\r\n.price .f1 {color: #333}\r\n.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.price .f3 {width: 24rpx;height: 24rpx;}\r\n.price .couponname{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;display:inline-block;margin:2rpx 0 2rpx 10rpx}\r\n.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}\r\n.scoredk .f1 {color: #333333}\r\n.scoredk .f2 {color: #999999;text-align: right;flex: 1}\r\n.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}\r\n.remark .f1 {color: #333;width: 200rpx}\r\n.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}\r\n.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\r\n.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n.footer .op[disabled] { background: #aaa !important; color: #666;}\r\n.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}\r\n.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}\r\n.storeitem .panel .f1 {color: #333}\r\n.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}\r\n.storeitem .radio-item:last-child {border: 0}\r\n.storeitem .radio-item .f1 {color: #666;flex: 1}\r\n.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}\r\n.storeitem .radio .radio-img {width: 100%;height: 100%}\r\n.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\r\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\r\n.cuxiao-desc {width: 100%}\r\n.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}\r\n.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.cuxiao-item .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{max-width:100%;}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx}\r\n\r\n.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}\r\n.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}\r\n.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}\r\n.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}\r\n.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}\r\n.memberitem .t1{color:#333;font-weight:bold}\r\n.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.memberitem .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.checkMem{ display: inline-block; }\r\n.checkMem p{ height: 30px; width: 100%; display: inline-block; }\r\n.placeholder{  font-size: 26rpx;line-height: 80rpx;}\r\n.selected-item span{ font-size: 26rpx !important;}\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\r\n\r\n.btn{ height:80rpx;line-height: 80rpx;width:90%;margin:0 auto;border-radius:40rpx;margin-top:40rpx;color: #fff;font-size: 28rpx;font-weight:bold}\r\n.invoiceBox .radio radio{transform: scale(0.8);}\r\n.invoiceBox .radio:nth-child(2) { margin-left: 30rpx;}\r\n.glassinfo{color: #333; padding:10rpx; border-radius: 10rpx;display: flex;justify-content: space-between;align-items: center;background: #f4f4f4;margin-top: 10rpx;}\r\n.glassinfo .f2{display: flex;justify-content: flex-end;}\r\n.glassinfo .f2 image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}\r\n.glassinfo .f1{font-weight: bold;}\r\n\r\n.glass_popup{\r\n\t\r\n}\r\n.glass_popup .popup__title{padding: 30rpx 0 0 0;}\r\n.glassitem{background:#eeeeee;border-radius: 10rpx;width: 94%;margin: 20rpx 3%;display: flex;align-items: center;padding: 20rpx 0;}\r\n.glassitem.on{background: #ffe6c8;}\r\n.glassitem .radio{width: 80rpx;flex-shrink: 0;text-align: center;}\r\n.glassitem .gcontent{flex:1;padding: 0 20rpx;}\r\n.glassrow{display: flex;padding: 10rpx 0;align-items: center;}\r\n.glassrow .glasscol{min-width: 25%;text-align: center;}\r\n.glassitem .bt{border-top:1px solid #e3e3e3}\r\n</style>\r\n", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115007737\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}