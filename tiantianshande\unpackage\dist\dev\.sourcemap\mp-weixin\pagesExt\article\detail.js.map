{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?0656", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?18c2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?6fa5", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?9442", "uni-app:///pagesExt/article/detail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?14cf", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExt/article/detail.vue?1126"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "detail", "datalist", "pagenum", "id", "pagecontent", "title", "sharepic", "nodata", "nomore", "iszan", "plcount", "reward", "reward_data", "openreward", "reward_type", "reward_num", "reward_num_type", "onLoad", "onPullDownRefresh", "onShareAppMessage", "desc", "pic", "callback", "that", "onShareTimeline", "imageUrl", "query", "onReachBottom", "methods", "sharecallback", "app", "getdata", "console", "uni", "<PERSON><PERSON><PERSON><PERSON>", "zan", "pzan", "changeReward", "changeRewardtype", "changeRewardNumType", "inputRewardnum", "num", "setTimeout", "selR<PERSON><PERSON><PERSON>", "postReward"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgJ/wB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MAAAd;MAAAe;MAAAC;MAAAC;QACAC;MACA;IAAA;EACA;EACAC;IACA;IACA;MAAAnB;MAAAe;MAAAC;MACAC;QACAC;MACA;IAAA;IACA;IACA;MACAlB;MACAoB;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC,wDACA;IACA;IACAC;MACA;MACA;MACAR;MACAO;QAAA3B;MAAA;QACA6B;QACAT;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;YACAA;UACA;UACAU;YACA5B;UACA;QACA;UACAyB;QACA;QACAP;QACAA;QACAA;QACAA;UAAAlB;UAAAe;UAAAC;QAAA;MACA;IACA;IACAa;MACA;MACA;MACAX;MACAA;MACAA;MACAO;QAAA5B;QAAAC;MAAA;QACAoB;QACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAY;MACA;MACA;MACAL;QAAA3B;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QACAoB;QACAA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACAN;QAAA3B;MAAA;QACA;UACA;UACA;QACA;UACA;QACA;QAEAF;QACAA;QACAsB;MACA;IACA;IACAc;MACA;MACAd;MACAA;MACAA;MACAA;IACA;IACAe;MACA;MACA;MACAf;MACAA;IACA;IACAgB;MACA;MACA;MACA;QACAhB;MACA;QACAA;MACA;MACAA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;UACAC;QACA;MACA;QACA;UACA;UACA;UACA;UACA;YACAA;UACA;QACA;MACA;MACAC;QACAV;QACAT;MACA;MACA;IACA;;IACAoB;MACA;MACA;MACA;MACApB;MACAA;IACA;IACAqB;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAd;QACA;UACA3B;UACAW;UACAC;QACA;QACAe;UACA;YACA;cACAA;cACAY;gBACAnB;gBACAA;cACA;YACA;cACAO;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChYA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/article/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/article/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=3e42cefe&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/article/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=3e42cefe&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload && _vm.detail.canpl == 1\n      ? _vm.__map(_vm.datalist, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = item.replylist.length\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var m0 =\n    _vm.reward && _vm.openreward && _vm.reward_num_type == 1\n      ? _vm.t(\"积分\")\n      : null\n  var l1 =\n    _vm.reward &&\n    _vm.openreward &&\n    _vm.reward_num_type == 1 &&\n    _vm.reward_data &&\n    _vm.reward_type == 2 &&\n    _vm.reward_data.score_data\n      ? _vm.__map(_vm.reward_data.score_data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.t(\"积分\")\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var m2 =\n    _vm.reward &&\n    _vm.openreward &&\n    _vm.reward_num_type == 1 &&\n    _vm.reward_type == 2\n      ? _vm.t(\"积分\")\n      : null\n  var m3 =\n    _vm.reward &&\n    _vm.openreward &&\n    _vm.reward_num_type == 2 &&\n    _vm.reward_type == 2\n      ? _vm.t(\"积分\")\n      : null\n  var m4 =\n    _vm.reward &&\n    _vm.openreward &&\n    _vm.reward_num_type == 2 &&\n    _vm.reward_type == 2\n      ? _vm.t(\"积分\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        l1: l1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<text class=\"title\" v-if=\"detail.showname==1\">{{detail.name}}</text>\r\n\t\t\t\t<view class=\"artinfo\" v-if=\"detail.showsendtime==1 || detail.showauthor==1 || detail.showreadcount==1\">\r\n\t\t\t\t\t<text class=\"t1\" v-if=\"detail.showsendtime==1\">{{detail.createtime}}</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.showauthor==1\">{{detail.author}}</text>\r\n\t\t\t\t\t<text class=\"t3\" v-if=\"detail.showreadcount==1\">阅读：{{detail.readcount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"padding:8rpx 0\">\r\n\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t</view>\r\n                <block v-if=\"reward\">\r\n                    <view @tap='changeReward' style=\"width: 360rpx;background-color: #FA5151;border-radius: 12rpx;color: #fff;margin: 40rpx auto;line-height: 80rpx;\">\r\n                        <view style=\"display: flex;width: 160rpx;margin: 0 auto;\">\r\n                            <image :src='pre_url+\"/static/img/dianzan.png\"' style='width: 32rpx;height: 32rpx;margin-top: 20rpx;margin-right: 10rpx;'></image>\r\n                            打赏作者\r\n                        </view>\r\n                    </view>\r\n                    <view style=\"width: 360rpx;margin: 0 auto;overflow: hidden;text-align:center;line-height: 80rpx;\">\r\n                        <view style=\"width:80rpx ;float: left;border-top: 2rpx solid #E5E5E5;margin-top: 40rpx;\"></view>\r\n                        <view style=\"width: 200rpx;font-size: 24rpx;float: left;color:#B2B2B2;\">\r\n                            <text  v-if=\"detail.reward_num>1000\" style=\"color: #536084;\">1000+</text>\r\n                            <text v-else style=\"color: #536084;\">{{detail.reward_num}}</text>\r\n                            <text>人打赏</text>\r\n                        </view>\r\n                        <view style=\"width:80rpx ;float: right;border-top: 2rpx solid #E5E5E5;margin-top: 40rpx;\"></view>\r\n                    </view>\r\n                    <view v-if=\"detail.reward_log\" style=\"width: 640rpx;margin:0rpx auto;overflow: hidden;margin-bottom: 40rpx;\">\r\n                        <block v-for=\"(item,index) in detail.reward_log\">\r\n                            <image :src=\"item\" style=\"width: 70rpx;height: 70rpx;margin-top: 20rpx;margin-right: 10rpx;\"></image>\r\n                        </block>\r\n                    </view>\r\n                </block>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<!--评论-->\r\n\t\t\t<block v-if=\"detail.canpl==1\">\r\n\t\t\t<view class=\"plbox\">\r\n\t\t\t\t<view class=\"plbox_title\"><text class=\"t1\">评论</text><text>({{plcount}})</text></view>\r\n\t\t\t\t<view class=\"plbox_content\">\r\n\t\t\t\t\t<block v-for=\"(item, idx) in datalist\" :key=\"item.id\">\r\n\t\t\t\t\t<view class=\"item1 flex\">\r\n\t\t\t\t\t\t<view class=\"f1 flex0\"><image :src=\"item.headimg\"></image></view>\r\n\t\t\t\t\t\t<view class=\"f2 flex-col\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2 plcontent\"><parse :content=\"item.content\" /></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.replylist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"relist\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(hfitem, index) in item.replylist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"item2\">\r\n\t\t\t\t\t\t\t\t\t<view>{{hfitem.nickname}}：</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2 plcontent\"><parse :content=\"hfitem.content\" /></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t </block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class=\"t3 flex\">\r\n\t\t\t\t\t\t\t\t<text>{{item.createtime}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"flex1\"><text v-if=\"detail.canplrp==1\" class=\"phuifu\" style=\"cursor:pointer\" @tap=\"goto\" :data-url=\"'pinglun?type=1&id=' + detail.id + '&hfid=' + item.id\">回复</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-y-center pzan\" @tap=\"pzan\" :data-id=\"item.id\" :data-index=\"idx\"><image :src=\"'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'\"></image>{{item.zan}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\n\t\t\t\t<!-- <nodata v-if=\"nodata\"></nodata> -->\n\t\t\t\t<!-- <nomore v-if=\"nomore\"></nomore> -->\n\t\t\t\t<loading v-if=\"loading\"></loading>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height:160rpx\"></view>\r\n\t\t\t<view class=\"pinglun notabbarbot\">\r\n\t\t\t\t<view class=\"pinput\" @tap=\"goto\" :data-url=\"'pinglun?type=0&id=' + detail.id\">发表评论</view>\r\n\t\t\t\t<view class=\"zan flex-y-center\" @tap=\"zan\" :data-id=\"detail.id\">\r\n\t\t\t\t\t<image :src=\"'/static/img/zan-' + (iszan?'2':'1') + '.png'\"/><text style=\"padding-left:2px\">{{detail.zan}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"detail.btntxt && detail.btnurl\">\r\n\t\t\t\t\t<view class=\"buybtn\" style=\"cursor:pointer\" :onclick=\"'location.href=' + detail.btnurl\">{{detail.btntxt}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\n\t</block>\r\n    <block v-if=\"reward && openreward\">\r\n        <view @tap=\"changeReward\" style=\"background-color: #000;opacity: 0.4;width: 100%;height: 100%;position: fixed;top: 0;z-index: 996;\"></view>\r\n        <view style=\"position: fixed;width: 690rpx;left: 30rpx;top:30%;background-color: #fff;z-index: 997;border-radius: 8rpx;\">\r\n            <view style=\"overflow: hidden;padding: 30rpx 30rpx 0;\">\r\n                <text>打赏作者</text>\r\n                <image @tap=\"changeReward\" src=\"/static/img/close.png\" style=\"float: right;width: 30rpx;height: 30rpx;\"></image>\r\n            </view>\r\n            <view v-if=\"reward_num_type == 1\" style=\"padding: 0 30rpx 30rpx;\">\r\n                <view style=\"width: 300rpx;text-align: center;margin: 0 auto;overflow: hidden;line-height: 70rpx;margin-top: 20rpx;\">\r\n                    <view @tap=\"changeRewardtype\" data-type=\"1\" class=\"reward_typel\" :style=\"reward_type == 1?'background-color: #FA5151;color: #fff;':'background-color: #f3f3f3;color: #000;'\">\r\n                        金额\r\n                    </view>\r\n                    <view @tap=\"changeRewardtype\" data-type=\"2\" class=\"reward_typer\" :style=\"reward_type == 2?'background-color: #FA5151;color: #fff;':'background-color: #f3f3f3;color: #000;'\">\r\n                        {{t('积分')}}\r\n                    </view>\r\n                </view>\r\n                <view v-if=\"reward_data\" style=\"width: 580rpx;overflow: hidden;text-align: center;margin: 0 auto;line-height: 80rpx;color: #FA5151;font-size: 30rpx;\">\r\n                    <block v-if=\"reward_type == 1 && reward_data.money_data\">\r\n                        <view  v-for=\"(item,index) in reward_data.money_data\" :class=\"reward_num == item?'reward_content reward_num':'reward_content'\" :style=\"(index+1)%3 ==0?'':'margin-right: 20rpx;'\" >\r\n                            <view @tap=\"selRewardnum\" :data-num=\"item\">\r\n                                ￥{{item}}\r\n                            </view>\r\n                        </view>\r\n                    </block>\r\n                    <block v-if=\"reward_type == 2 && reward_data.score_data\">\r\n                        <view  v-for=\"(item,index) in reward_data.score_data\" :class=\"reward_num == item?'reward_content reward_num':'reward_content'\" :style=\"(index+1)%3 ==0?'':'margin-right: 20rpx;'\">\r\n                            <view  @tap=\"selRewardnum\" :data-num=\"item\">\r\n                                {{item}}{{t('积分')}}\r\n                            </view>\r\n                        </view>\r\n                    </block>\r\n                </view>\r\n                <view @tap=\"changeRewardNumType\" data-type=\"1\" v-if=\"reward_type == 1\" class=\"reward_num_type\">其他金额</view>\r\n                <view @tap=\"changeRewardNumType\" data-type=\"1\" v-if=\"reward_type == 2\" class=\"reward_num_type\">其他{{t('积分')}}</view>\r\n            </view>\r\n            <view v-if=\"reward_num_type == 2\">\r\n                <view>\r\n                    <view style=\"width: 250rpx;margin: 0 auto;font-size: 40rpx;font-weight: bold;line-height: 80rpx;display: flex;text-align: center;margin: 40rpx auto;\">\r\n                        <text v-if=\"reward_type == 1\">￥</text>\r\n                        <input v-model=\"reward_num\" @input=\"inputRewardnum\"  placeholder=\"0(整数)\" placeholder-style=\"line-height: 80rpx;\" style=\"height: 80rpx;line-height:80rpx;display: inline-block;width: 170rpx;border-bottom: 2rpx solid #eee;\"/>\r\n                        <text v-if=\"reward_type == 2\" style=\"font-size: 36rpx;\">{{t('积分')}}</text>\r\n                    </view>\r\n                    <view @tap=\"changeRewardNumType\" data-type=\"2\" v-if=\"reward_type == 1\" class=\"reward_num_type\">固定金额</view>\r\n                    <view @tap=\"changeRewardNumType\" data-type=\"2\" v-if=\"reward_type == 2\" class=\"reward_num_type\">固定{{t('积分')}}</view>\r\n                </view>\r\n                \r\n                <view @tap=\"postReward\" style=\"width: 100%;line-height: 80rpx;text-align: center;border-top: 2rpx solid #eee;font-size: 30rpx;color: #FA5151;\">\r\n                    确定\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\n  data() {\n    return {\n            opt:{},\n            loading:false,\n            isload: false,\n            menuindex:-1,\r\n            pre_url:app.globalData.pre_url,\n\n            detail:[],\n            datalist: [],\n            pagenum: 1,\n            id: 0,\n            pagecontent: \"\",\n            title: \"\",\n            sharepic: \"\",\n            nodata:false,\n            nomore:false,\n            iszan: \"\",\n\t\t\tplcount:0,\r\n            \r\n            reward:false,\r\n            reward_data:'',\r\n            openreward:false,\r\n            reward_type:1,\r\n            reward_num:0,\r\n            reward_num_type:1,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    this.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonShareAppMessage:function(){\r\n\t\tvar that = this;\n\t\treturn this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,callback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\n\t},\n\tonShareTimeline:function(){\r\n\t\tvar that = this;\n\t\tvar sharewxdata = this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,\r\n\t\t\tcallback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}});\n\t\tvar query = (sharewxdata.path).split('?')[1];\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore && this.detail.canpl==1) {\n      this.pagenum = this.pagenum + 1\n      this.getpllist();\n    }\n  },\n  methods: {\r\n\t  sharecallback: function() {\r\n\t  \tapp.post(\"ApiArticle/giveScorenum\",{}, function(res) {\r\n\t  \t});\t\r\n\t  },\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar id = that.opt.id;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiArticle/detail', {id: id}, function (res) {\r\n\t\t\t\tconsole.log(res)\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1){\n\t\t\t\t\tthat.detail = res.detail;\n\t\t\t\t\tthat.pagecontent = res.pagecontent;\n\t\t\t\t\tthat.plcount = res.plcount;\n\t\t\t\t\tthat.iszan = res.iszan;\n\t\t\t\t\tthat.title = res.detail.name;\n\t\t\t\t\tthat.sharepic = res.detail.pic;\r\n                    if(res.reward){\r\n                        that.reward      = res.reward;\r\n                        that.reward_data = res.reward_data;\r\n                    }\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: res.detail.name\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t}\n\t\t\t\tthat.pagenum = 1;\n\t\t\t\tthat.datalist = [];\n\t\t\t\tthat.getpllist();\n\t\t\t\tthat.loaded({title:res.detail.name,desc:res.detail.subname,pic:res.detail.pic});\n\t\t\t});\n\t\t},\n    getpllist: function () {\n        var that = this;\n        var pagenum = that.pagenum;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n        app.post('ApiArticle/getpllist', {pagenum: pagenum,id: that.detail.id}, function (res) {\n\t\t\t\tthat.loading = false;\n            var data = res.data;\n            if (data.length == 0) {\n                if(pagenum == 1){\n                    that.nodata = true;\n                }else{\n                    that.nomore = true;\n                }\n            }\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n        });\n    },\n    zan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.post(\"ApiArticle/zan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          var iszan = 0;\n        } else {\n          var iszan = 1;\n        }\n        that.iszan = iszan;\n        that.detail.zan = res.zancount;\n      });\n    },\n    pzan: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var index = e.currentTarget.dataset.index;\n      var datalist = that.datalist;\n      app.post(\"ApiArticle/pzan\", {id: id}, function (res) {\n        if (res.type == 0) {\n          //取消点赞\n          var iszan = 0;\n        } else {\n          var iszan = 1;\n        }\n\n        datalist[index].iszan = iszan;\n        datalist[index].zan = res.zancount;\n        that.datalist = datalist;\n      });\n    },\r\n    changeReward:function(){\r\n        var that = this;\r\n        that.openreward      = !that.openreward;\r\n        that.reward_type     = 1;\r\n        that.reward_num_type = 1;\r\n        that.reward_num      = '';\r\n    },\r\n    changeRewardtype:function(e){\r\n        var that = this;\r\n        var type         = e.currentTarget.dataset.type;\r\n        that.reward_type = type;\r\n        that.reward_num  = '';\r\n    },\r\n    changeRewardNumType:function(e){\r\n        var that = this;\r\n        var reward_num_type = that.reward_num_type;\r\n        if(reward_num_type == 1){\r\n            that.reward_num_type = 2;\r\n        }else{\r\n            that.reward_num_type = 1;\r\n        }\r\n        that.reward_num  = '';\r\n    },\r\n    inputRewardnum:function(e){\r\n        var that = this;\r\n        var reward_type = that.reward_type;\r\n        var num  = e.detail.value;\r\n        var index = num.indexOf('.');\r\n        if(reward_type == 2){\r\n            if(index>=0){\r\n                // app.alert(that.t('积分')+'必须为整数');\r\n                // return;\r\n                num = parseInt(num);\r\n            }\r\n        }else{\r\n            if(index>=0){\r\n                var slice_index = index+1;\r\n                var afternum = num.slice(slice_index);\r\n                var len = afternum.length;\r\n                if(len>2){\r\n                    num = parseInt(num*100)/100;\r\n                }\r\n            }\r\n        }\r\n        setTimeout(function(){\r\n            console.log(num);\r\n            that.reward_num = num;\r\n        },0)\r\n        //that.$set(that, 'reward_num', num);\r\n    },\r\n    selRewardnum:function(e){\r\n        var that = this;\r\n        var reward_type = that.reward_type;\r\n        var num  = e.currentTarget.dataset.num;\r\n        that.reward_num = num;\r\n        that.postReward();\r\n    },\r\n    postReward:function(){\r\n        var that = this;\r\n        var reward_type = that.reward_type;\r\n        var reward_num  = that.reward_num;\r\n        if(reward_type == 1){\r\n            var msg = '确定打赏'+reward_num+'元吗？';\r\n        }else{\r\n            var msg = '确定打赏'+reward_num+that.t('积分')+'吗？';\r\n        }\r\n        app.confirm(msg,function(){\r\n            var data = {\r\n                id          : that.opt.id,\r\n                reward_type : reward_type,\r\n                reward_num  : reward_num\r\n            }\r\n            app.post('ApiArticle/reward',data,function(res){\r\n                if(res.status == 1){\r\n                    if(reward_type == 2){\r\n                        app.alert(res.msg);\r\n                        setTimeout(function(){\r\n                            that.getdata();\r\n                            that.changeReward();\r\n                        },800)\r\n                    }else{\r\n                        app.goto('/pages/pay/pay?id=' + res.payorderid);\r\n                    }\r\n                }else{\r\n                    app.alert(res.msg);\r\n                }\r\n            })\r\n        })\r\n    }\n  }\n};\r\n</script>\r\n<style>\r\n.header{ background-color: #fff;padding: 10rpx 20rpx 0 20rpx;position: relative;display:flex;flex-direction:column;}\r\n.header .title{width:100%;font-size: 36rpx;color:#333;line-height: 1.4;margin:10rpx 0;margin-top:20rpx;font-weight:bold}\r\n.header .artinfo{width:100%;font-size:28rpx;color: #8c8c8c;font-style: normal;overflow: hidden;display:flex;margin:10rpx 0;}\r\n.header .artinfo .t1{padding-right:8rpx}\r\n.header .artinfo .t2{color:#777;padding-right:8rpx}\r\n.header .artinfo .t3{text-align:right;flex:1;}\r\n.header .subname{width:100%;font-size:28rpx;color: #888;border:1px dotted #ddd;border-radius:10rpx;margin:10rpx 0;padding:10rpx}\r\n\r\n\r\n.pinglun{ width:96%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:10;border-top:1px solid #f7f7f7;padding:0 2%;box-sizing:content-box}\r\n.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}\r\n.pinglun .zan{padding:0 12rpx;line-height:100rpx}\r\n.pinglun .zan image{width:48rpx;height:48rpx}\r\n.pinglun .zan span{height:40rpx;line-height:50rpx;font-size:32rpx}\r\n.pinglun .buybtn{margin-left:0.08rpx;background:#31C88E;height:72rpx;line-height:72rpx;padding:0 20rpx;color:#fff;border-radius:6rpx}\r\n\r\n.plbox{width:100%;padding:40rpx 20rpx;background:#fff;margin-top:10px}\r\n.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx}\r\n.plbox_title .t1{color:#000;font-weight:bold}\r\n.plbox_content .plcontent{vertical-align: middle;color:#111}\r\n.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}\r\n.plbox_content .item1{width:100%;margin-bottom:20rpx}\r\n.plbox_content .item1 .f1{width:80rpx;}\r\n.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}\r\n.plbox_content .item1 .f2{flex:1}\r\n.plbox_content .item1 .f2 .t1{}\r\n.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}\r\n.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}\r\n.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:2px}\r\n.plbox_content .item1 .f2 .phuifu{margin-left:6px;color:#507DAF}\r\n.plbox_content .relist{width:100%;background:#f5f5f5;padding:4rpx 20rpx;margin-bottom:20rpx}\r\n.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}\r\n\r\n.copyright{display:none}\r\n\r\n.reward_typel{width: 150rpx;display: inline-block;border-radius: 8rpx 0 0 8rpx;}\r\n.reward_typer{width: 150rpx;display: inline-block;border-radius: 0rpx 8rpx 8rpx 0rpx;}\r\n.reward_content{width: 180rpx;border-radius: 8rpx;border: 2rpx solid #FA5151;float: left;margin-top: 20rpx;overflow: hidden;white-space: nowrap;}\r\n.reward_num{background-color:#FA5151;color:#fff}\r\n.reward_num_type{width:200rpx;margin:20rpx auto;height:60rpx;line-height: 60rpx;text-align: center;color: #536084;overflow: hidden;}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115102736\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}