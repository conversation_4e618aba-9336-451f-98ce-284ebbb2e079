{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?7102", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?959c", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?4ad4", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?7480", "uni-app:///zhaopin/jobMatch.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?fa20", "webpack:///D:/qianhouduankaifabao/tiantianshande/zhaopin/jobMatch.vue?5dbf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "matchStats", "totalJobs", "highMatch", "salary", "matchDistribution", "type", "label", "percentage", "filterTags", "name", "active", "field", "order", "matchedJobs", "loading", "currentPage", "pageSize", "filterData", "matchResult", "longitude", "latitude", "onLoad", "console", "uni", "methods", "getUserLocation", "app", "that", "title", "icon", "loadUserProfile", "userProfile", "fetchMatchedJobs", "activeTag", "params", "page", "page_size", "sort_field", "sort_order", "getActiveSortType", "toggleFilter", "tag", "viewJobDetail", "url", "stripHtml", "processMatchResult", "distribution", "count", "id", "company", "tags", "location", "workTime", "job", "match_degree", "distance"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8GjxB;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,oBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC,aACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;;IAEA;IACA;IAEA;MACA;MACA;MACA;MAEAA;MACAA;MAEA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;;MAEA;MACAC;MACAA;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACAJ;QACAK;QACAA;QACA;QACAA;MACA;QACAL;QACAC;UACAK;UACAC;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAR;kBACAK;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;;kBAEA;kBACAC;oBAAA;kBAAA;kBACAtB;kBACAC;kBAEAsB;oBACAC;oBACAC;oBACAC;oBACAC;oBACAnB;oBACAC;kBAAA,GACA;kBAGAE;kBAEAI;oBACAJ;oBACA;sBACA;oBACA;sBACAC;wBACAK;wBACAC;sBACA;oBACA;oBACA;kBACA;gBACA;kBACAP;kBACAC;oBACAK;oBACAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAU;MACA;QAAA;MAAA;MACA;MACA;IACA;IAEAC;MACA;QACAC;MACA;MACA;MACA;IACA;IAEAC;MACAnB;QACAoB;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACAtB;QACA;MACA;IACA;IAEA;IACAuB;MACA;MAEA;QACAvB;QACAC;UACAK;UACAC;QACA;QACA;MACA;MAEA;QACA;QACA;UACA;YACA5B;YACAC;YACAC;UACA;;UAEA;UACA;UACA,2CACA2C,+BACAA,+BACAA;;UAEA;UACA;;UAEA;UACA,cACA;YACAzC;YACAC;YACAyC;YACAxC;UACA,GACA;YACAF;YACAC;YACAyC;YACAxC;UACA,GACA;YACAF;YACAC;YACAyC;YACAxC;UACA,GACA;YACAF;YACAC;YACAyC;YACAxC;UACA,EACA;UAEA;QACA;;QAEA;QACA;UACA;YAAA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YAEA;cACAyC;cACApB;cACAqB;cACAC;cACA/C;cACAgD;cACAC,+DACAC;cACAC;cACAC;cAAA;cACAnC;cACAD;YACA;UACA;YACAG;YACA;YACA;cACA0B;cACApB;cACAqB;cACAC;cACA/C;cACAgD;cACAC;cACAE;cACAC;YACA;UACA;QACA;QAEAjC;MACA;QACAA;QACAC;UACAK;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpZA;AAAA;AAAA;AAAA;AAA47C,CAAgB,u4CAAG,EAAC,C;;;;;;;;;;;ACAh9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "zhaopin/jobMatch.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './zhaopin/jobMatch.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jobMatch.vue?vue&type=template&id=3b2ec9ac&scoped=true&\"\nvar renderjs\nimport script from \"./jobMatch.vue?vue&type=script&lang=js&\"\nexport * from \"./jobMatch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jobMatch.vue?vue&type=style&index=0&id=3b2ec9ac&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3b2ec9ac\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"zhaopin/jobMatch.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobMatch.vue?vue&type=template&id=3b2ec9ac&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filterTags, function (tag, index) {\n    var $orig = _vm.__get_orig(tag)\n    var m0 = tag.active ? _vm.t(\"color1\") : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var m1 = _vm.t(\"color1rgb\")\n  var m2 = _vm.t(\"color1\")\n  var m4 = _vm.t(\"color1\")\n  var l1 = _vm.__map(_vm.matchedJobs, function (job, index) {\n    var $orig = _vm.__get_orig(job)\n    var m3 = _vm.t(\"color1rgb\")\n    return {\n      $orig: $orig,\n      m3: m3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        m4: m4,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobMatch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobMatch.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"job-match-page\">\r\n    <!-- 顶部匹配分析 -->\r\n    <!-- <view class=\"match-analysis\" :style=\"{ background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')})` }\">\r\n      <view class=\"analysis-header\">\r\n        <text class=\"title\">智能匹配分析</text>\r\n        <text class=\"subtitle\">基于您的简历信息，为您推荐最适合的工作</text>\r\n      </view>\r\n      \r\n      <view class=\"match-stats\">\r\n        <view class=\"stat-item\">\r\n          <text class=\"number\">{{ matchStats.totalJobs }}</text>\r\n          <text class=\"label\">匹配职位</text>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <text class=\"number\">{{ matchStats.highMatch }}%</text>\r\n          <text class=\"label\">较高匹配度</text>\r\n        </view>\r\n        <view class=\"stat-item\">\r\n          <text class=\"number\">{{ matchStats.salary }}</text>\r\n          <text class=\"label\">平均薪资</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"match-chart\" :style=\"{ background: `rgba(${t('color1rgb')}, 0.1)` }\">\r\n        <view class=\"chart-title\">匹配度分布</view>\r\n        <view class=\"chart-bars\">\r\n          <view \r\n            v-for=\"(item, index) in matchDistribution\" \r\n            :key=\"index\"\r\n            class=\"bar-item\"\r\n          >\r\n            <view \r\n              class=\"bar\" \r\n              :style=\"{\r\n                height: item.percentage + '%',\r\n                background: t('color1'),\r\n                opacity: item.type === 'very-high' ? 1 :\r\n                         item.type === 'high' ? 0.8 :\r\n                         item.type === 'medium' ? 0.6 : 0.4\r\n              }\"\r\n            ></view>\r\n            <view class=\"bar-info\">\r\n              <text class=\"bar-count\">{{ item.count }}个</text>\r\n              <text class=\"bar-label\">{{ item.label }}</text>\r\n              <text class=\"bar-percent\">{{ item.percentage }}%</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view> -->\r\n    \r\n    <!-- 标签筛选 -->\r\n    <view class=\"filter-section\">\r\n      <scroll-view scroll-x class=\"filter-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"filter-tags\">\r\n          <view \r\n            v-for=\"(tag, index) in filterTags\" \r\n            :key=\"index\"\r\n            class=\"tag\"\r\n            :style=\"{\r\n              background: tag.active ? t('color1') : '#f5f5f5',\r\n              color: tag.active ? '#ffffff' : '#666'\r\n            }\"\r\n            @tap=\"toggleFilter(index)\"\r\n          >\r\n            {{ tag.name }}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 推荐职位列表 -->\r\n    <view class=\"job-list\">\r\n      <view \r\n        v-for=\"(job, index) in matchedJobs\" \r\n        :key=\"index\"\r\n        class=\"job-card\"\r\n        :style=\"{ boxShadow: `0 2rpx 12rpx rgba(${t('color1rgb')}, 0.05)` }\"\r\n        @tap=\"viewJobDetail(job.id)\"\r\n      >\r\n        <view class=\"job-header\">\r\n          <view class=\"job-info\">\r\n            <text class=\"job-title\" :style=\"{ color: t('color1') }\">{{ job.title }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"job-tags\">\r\n          <text \r\n            class=\"tag\" \r\n            v-for=\"(tag, tagIndex) in job.tags\" \r\n            :key=\"tagIndex\"\r\n            :style=\"{ background: `rgba(${t('color1rgb')}, 0.1)` }\"\r\n          >\r\n            {{ tag }}\r\n          </text>\r\n        </view>\r\n        \r\n        <view class=\"job-footer\">\r\n          <view class=\"salary\" :style=\"{ color: t('color1') }\">{{ job.salary }}</view>\r\n          <view class=\"location\">{{ job.location }}\r\n            <text class=\"distance\" v-if=\"job.distance\">· {{job.distance}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n      matchStats: {\r\n        totalJobs: 0,\r\n        highMatch: 0,\r\n        salary: '0'\r\n      },\r\n      matchDistribution: [\r\n        { type: 'very-high', label: '90%+', percentage: 0 },\r\n        { type: 'high', label: '80-89%', percentage: 0 },\r\n        { type: 'medium', label: '60-79%', percentage: 0 },\r\n        { type: 'low', label: '60%以下', percentage: 0 }\r\n      ],\r\n      filterTags: [\r\n        { name: '全部', active: true, field: '', order: '' },\r\n        { name: '薪资优先', active: false, field: 'salary', order: 'desc' },\r\n        { name: '距离优先', active: false, field: 'distance', order: 'asc' },\r\n        { name: '最新发布', active: false, field: 'time', order: 'desc' }\r\n      ],\r\n      matchedJobs: [],\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      filterData: null,\r\n      matchResult: null,\r\n      longitude: '',\r\n      latitude: ''\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    console.log('页面加载参数:', options)\r\n    \r\n    // 获取用户位置信息\r\n    this.getUserLocation()\r\n    \r\n    if (options.from === 'filter') {\r\n      // 从筛选页面来，获取缓存的数据\r\n      const filterData = uni.getStorageSync('jobMatchFilterData')\r\n      const matchResult = uni.getStorageSync('jobMatchResult')\r\n      \r\n      console.log('从缓存获取的筛选数据:', filterData)\r\n      console.log('从缓存获取的匹配结果:', matchResult)\r\n      \r\n      if (filterData) {\r\n        this.filterData = filterData\r\n      }\r\n      \r\n      if (matchResult && matchResult.status === 1) {\r\n        this.processMatchResult(matchResult)\r\n      } else {\r\n        this.fetchMatchedJobs()\r\n      }\r\n      \r\n      // 使用完后清除缓存\r\n      uni.removeStorageSync('jobMatchFilterData')\r\n      uni.removeStorageSync('jobMatchResult')\r\n    } else {\r\n      // 直接进入页面，获取默认数据\r\n      this.fetchMatchedJobs()\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 获取用户位置\r\n    getUserLocation() {\r\n      const that = this\r\n      app.getLocation(function(res) {\r\n        console.log('获取位置成功:', res)\r\n        that.latitude = res.latitude\r\n        that.longitude = res.longitude\r\n        // 重新获取职位列表\r\n        that.fetchMatchedJobs()\r\n      }, function(err) {\r\n        console.error('获取位置失败:', err)\r\n        uni.showToast({\r\n          title: '获取位置信息失败，距离排序可能不准确',\r\n          icon: 'none'\r\n        })\r\n      })\r\n    },\r\n    \r\n    async loadUserProfile() {\r\n      try {\r\n        const userProfile = await this.$api.getUserProfile()\r\n        // 处理用户简历数据\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '获取简历信息失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    \r\n    async fetchMatchedJobs() {\r\n      try {\r\n        this.loading = true\r\n        \r\n        // 获取当前选中的排序类型\r\n        const activeTag = this.filterTags.find(tag => tag.active) || this.filterTags[0]\r\n        const field = activeTag.field\r\n        const order = activeTag.order\r\n        \r\n        const params = {\r\n          page: this.currentPage,\r\n          page_size: this.pageSize,\r\n          sort_field: field,\r\n          sort_order: order,\r\n          longitude: this.longitude,\r\n          latitude: this.latitude,\r\n          ...(this.filterData || {}) // 确保filterData为空时不会报错\r\n        }\r\n        \r\n        console.log('请求参数:', params)\r\n        \r\n        app.get('apiZhaopin/jobMatch', params, (result) => {\r\n          console.log('匹配结果:', result)\r\n          if (result.status === 1) {\r\n            this.processMatchResult(result)\r\n          } else {\r\n            uni.showToast({\r\n              title: result.msg || '获取数据失败',\r\n              icon: 'none'\r\n            })\r\n          }\r\n          this.loading = false\r\n        })\r\n      } catch (error) {\r\n        console.error('获取匹配职位失败:', error)\r\n        uni.showToast({\r\n          title: '获取匹配职位失败',\r\n          icon: 'none'\r\n        })\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    getActiveSortType() {\r\n      const activeTag = this.filterTags.find(tag => tag.active)\r\n      if (!activeTag) return ''\r\n      return activeTag.field\r\n    },\r\n    \r\n    toggleFilter(index) {\r\n      this.filterTags.forEach((tag, i) => {\r\n        tag.active = i === index\r\n      })\r\n      this.currentPage = 1\r\n      this.fetchMatchedJobs()\r\n    },\r\n    \r\n    viewJobDetail(jobId) {\r\n      uni.navigateTo({\r\n        url: `/zhaopin/partdetails?id=${jobId}`\r\n      })\r\n    },\r\n    \r\n    // 添加HTML标签过滤函数\r\n    stripHtml(html) {\r\n      if (!html) return ''\r\n      try {\r\n        return html.replace(/<[^>]+>/g, '').replace(/&[^;]+;/g, '').trim()\r\n      } catch (e) {\r\n        console.warn('处理HTML标签失败:', e)\r\n        return String(html)\r\n      }\r\n    },\r\n    \r\n    // 在processMatchResult方法中修改职位数据处理部分，适应新的接口格式\r\n    processMatchResult(result) {\r\n      const { data } = result\r\n      \r\n      if (!data || !data.list) {\r\n        console.error('匹配结果数据格式错误:', result)\r\n        uni.showToast({\r\n          title: '数据格式错误',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 更新统计数据(如果有的话)\r\n        if (data.statistics) {\r\n          this.matchStats = {\r\n            totalJobs: data.statistics.match_count || 0,\r\n            highMatch: (data.statistics.match_degree || 0).toFixed(1),\r\n            salary: data.statistics.average_salary || '0'\r\n          }\r\n          \r\n          // 更新分布数据\r\n          const distribution = data.statistics.distribution || {}\r\n          const total = (distribution['90up'] || 0) + \r\n                       (distribution['80_89'] || 0) + \r\n                       (distribution['60_79'] || 0) + \r\n                       (distribution['below60'] || 0)\r\n                       \r\n          // 如果总数为0，设置一个默认值避免除以0\r\n          const safeTotal = total || 1\r\n          \r\n          // 计算每个区间的实际数量和百分比\r\n          const ranges = [\r\n            { \r\n              type: 'very-high', \r\n              label: '90%+', \r\n              count: distribution['90up'] || 0,\r\n              percentage: Math.round((distribution['90up'] || 0) / safeTotal * 100)\r\n            },\r\n            { \r\n              type: 'high', \r\n              label: '80-89%', \r\n              count: distribution['80_89'] || 0,\r\n              percentage: Math.round((distribution['80_89'] || 0) / safeTotal * 100)\r\n            },\r\n            { \r\n              type: 'medium', \r\n              label: '60-79%', \r\n              count: distribution['60_79'] || 0,\r\n              percentage: Math.round((distribution['60_79'] || 0) / safeTotal * 100)\r\n            },\r\n            { \r\n              type: 'low', \r\n              label: '60%以下', \r\n              count: distribution['below60'] || 0,\r\n              percentage: Math.round((distribution['below60'] || 0) / safeTotal * 100)\r\n            }\r\n          ]\r\n          \r\n          this.matchDistribution = ranges\r\n        }\r\n        \r\n        // 转换职位数据，适配新的接口数据格式\r\n        this.matchedJobs = (data.list || []).map(job => {\r\n          try {\r\n            // 处理工作时间\r\n            const workTime = job.work_time_type || ''\r\n            const workMode = job.work_mode || ''\r\n            const workIntensity = job.work_intensity || ''\r\n            \r\n            // 构建标签数组\r\n            const tags = []\r\n            if (workTime) tags.push(workTime)\r\n            if (workMode) tags.push(workMode)\r\n            if (workIntensity) tags.push(workIntensity)\r\n            if (job.rest_time) tags.push(job.rest_time)\r\n            if (job.payment) tags.push(job.payment) // 添加结算方式\r\n            \r\n            // 获取教育和经验要求作为标签\r\n            if (job.education) tags.push(job.education)\r\n            if (job.experience) tags.push(job.experience)\r\n            \r\n            return {\r\n              id: job.id || '',\r\n              title: job.title || '职位名称未知',\r\n              company: job.company?.name || '未知公司',\r\n              tags: tags,\r\n              salary: job.salary || '薪资面议',\r\n              location: job.work_address || job.district || job.city || '地点未知',\r\n              workTime: job.work_time_start && job.work_time_end ? \r\n                       `${job.work_time_start.substring(0, 5)}-${job.work_time_end.substring(0, 5)}` : '',\r\n              match_degree: job.match_degree || 0,\r\n              distance: job.distance_text || null, // 使用服务端返回的格式化距离\r\n              latitude: job.latitude || null,\r\n              longitude: job.longitude || null\r\n            }\r\n          } catch (error) {\r\n            console.error('处理职位数据时出错:', error, job)\r\n            // 返回一个默认的职位对象\r\n            return {\r\n              id: job.id || '',\r\n              title: '数据解析错误',\r\n              company: '未知公司',\r\n              tags: [],\r\n              salary: '薪资面议',\r\n              location: '地点未知',\r\n              workTime: '',\r\n              match_degree: 0,\r\n              distance: null\r\n            }\r\n          }\r\n        })\r\n        \r\n        console.log('处理后的职位数据:', this.matchedJobs)\r\n      } catch (error) {\r\n        console.error('处理匹配结果时出错:', error)\r\n        uni.showToast({\r\n          title: '数据处理失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.job-match-page {\r\n  min-height: 100vh;\r\n  background-color: #f8f9fa;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  \r\n  .match-analysis {\r\n    padding: 40rpx 30rpx 60rpx;\r\n    color: #ffffff;\r\n    border-radius: 0 0 30rpx 30rpx;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n    position: relative;\r\n    overflow: hidden;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 120rpx;\r\n      background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));\r\n      pointer-events: none;\r\n    }\r\n    \r\n    .analysis-header {\r\n      margin-bottom: 40rpx;\r\n      \r\n      .title {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        letter-spacing: 2rpx;\r\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n      }\r\n      \r\n      .subtitle {\r\n        font-size: 28rpx;\r\n        opacity: 0.9;\r\n        margin-top: 12rpx;\r\n        letter-spacing: 1rpx;\r\n      }\r\n    }\r\n    \r\n    .match-stats {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-bottom: 50rpx;\r\n      \r\n      .stat-item {\r\n        text-align: center;\r\n        background: rgba(255, 255, 255, 0.1);\r\n        padding: 20rpx 30rpx;\r\n        border-radius: 16rpx;\r\n        backdrop-filter: blur(10px);\r\n        transition: transform 0.3s ease;\r\n        \r\n        &:active {\r\n          transform: scale(0.95);\r\n        }\r\n        \r\n        .number {\r\n          font-size: 44rpx;\r\n          font-weight: bold;\r\n          margin-bottom: 8rpx;\r\n          display: block;\r\n          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n        }\r\n        \r\n        .label {\r\n          font-size: 24rpx;\r\n          opacity: 0.9;\r\n          letter-spacing: 2rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .match-chart {\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      backdrop-filter: blur(10px);\r\n      \r\n      .chart-title {\r\n        font-size: 30rpx;\r\n        margin-bottom: 30rpx;\r\n        font-weight: 500;\r\n        letter-spacing: 1rpx;\r\n      }\r\n      \r\n      .chart-bars {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        height: 240rpx;\r\n        align-items: flex-end;\r\n        padding: 0 20rpx;\r\n        \r\n        .bar-item {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          padding: 0 10rpx;\r\n          \r\n          .bar {\r\n            width: 40rpx;\r\n            border-radius: 20rpx 20rpx 6rpx 6rpx;\r\n            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n            position: relative;\r\n            overflow: hidden;\r\n            min-height: 4rpx;\r\n            \r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              top: 0;\r\n              left: 0;\r\n              right: 0;\r\n              height: 30%;\r\n              background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);\r\n            }\r\n          }\r\n          \r\n          .bar-info {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            margin-top: 16rpx;\r\n            opacity: 0.9;\r\n            \r\n            .bar-count {\r\n              font-size: 24rpx;\r\n              font-weight: bold;\r\n              margin-bottom: 4rpx;\r\n              color: #ffffff;\r\n              text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\r\n            }\r\n            \r\n            .bar-label {\r\n              font-size: 22rpx;\r\n              margin-bottom: 4rpx;\r\n              color: rgba(255, 255, 255, 0.9);\r\n            }\r\n            \r\n            .bar-percent {\r\n              font-size: 20rpx;\r\n              color: rgba(255, 255, 255, 0.8);\r\n              background: rgba(255, 255, 255, 0.1);\r\n              padding: 2rpx 8rpx;\r\n              border-radius: 10rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .filter-section {\r\n    background-color: #ffffff;\r\n    padding: 20rpx 0;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 10;\r\n    \r\n    .filter-scroll {\r\n      white-space: nowrap;\r\n      \r\n      .filter-tags {\r\n        padding: 0 20rpx;\r\n        display: inline-flex;\r\n        \r\n        .tag {\r\n          display: inline-block;\r\n          padding: 12rpx 30rpx;\r\n          font-size: 28rpx;\r\n          border-radius: 32rpx;\r\n          margin-right: 20rpx;\r\n          transition: all 0.3s ease;\r\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n          \r\n          &:active {\r\n            transform: scale(0.95);\r\n          }\r\n          \r\n          &:last-child {\r\n            margin-right: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .job-list {\r\n    padding: 20rpx;\r\n    \r\n    .job-card {\r\n      background-color: #ffffff;\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 20rpx;\r\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n      transition: all 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.98);\r\n        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n      }\r\n      \r\n      .job-header {\r\n        margin-bottom: 20rpx;\r\n        \r\n        .job-info {\r\n          .job-title {\r\n            font-size: 36rpx;\r\n            font-weight: bold;\r\n            display: block;\r\n            line-height: 1.4;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .job-tags {\r\n        margin-bottom: 24rpx;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 12rpx;\r\n        \r\n        .tag {\r\n          padding: 6rpx 16rpx;\r\n          font-size: 24rpx;\r\n          color: #666;\r\n          border-radius: 6rpx;\r\n          background: rgba(0, 0, 0, 0.03);\r\n        }\r\n      }\r\n      \r\n      .job-footer {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        \r\n        .salary {\r\n          font-size: 36rpx;\r\n          font-weight: bold;\r\n          margin-right: 20rpx;\r\n        }\r\n        \r\n        .location {\r\n          font-size: 26rpx;\r\n          color: #666;\r\n          flex: 1;\r\n          text-align: right;\r\n          \r\n          .distance {\r\n            color: #999;\r\n            margin-left: 6rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 添加骨架屏动画\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n.loading {\r\n  .job-card {\r\n    background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);\r\n    background-size: 200% 100%;\r\n    animation: shimmer 1.5s infinite;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobMatch.vue?vue&type=style&index=0&id=3b2ec9ac&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./jobMatch.vue?vue&type=style&index=0&id=3b2ec9ac&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115048820\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}