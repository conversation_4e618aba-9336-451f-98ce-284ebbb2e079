<?php /*a:3:{s:76:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\workflow.html";i:1754104431;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>工作流管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> 工作流管理</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('<?php echo url('edit'); ?>/type/workflow')">添加工作流</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">启用</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">禁用</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:80px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">启用</option>
										<option value="0">禁用</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-content-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script type="text/html" id="operationTpl">
		{{# if(d.status == 1){ }}
		<button class="table-btn" onclick="testWorkflow('{{d.id}}', 'sync')">同步测试</button>
		<button class="table-btn" onclick="testWorkflow('{{d.id}}', 'async')">异步测试</button>
		{{# } }}
		<button class="table-btn" onclick="configParams('{{d.id}}')">参数配置</button>
		<button class="table-btn" onclick="openmax('<?php echo url('edit'); ?>/id/{{d.id}}/type/workflow')">编辑</button>
		<button class="table-btn" onclick="datadel('{{d.id}}')">删除</button>
	</script>
          </div>
        </div>
    </div>
  </div>

	<script>
  var table = layui.table;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,id: 'tabledata' // 添加表格ID，用于获取数据
    ,url: '<?php echo url("workflow"); ?>' //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '工作流名称',width:200},
      {field: 'workflow_id', title: '工作流ID',width:200},
      {field: 'description', title: '描述',width:200},
      {field: 'status_text', title: '状态',width:80},
      {field: 'create_time_text', title: '创建时间',width:160},
      {field: 'operation', title: '操作',templet: '#operationTpl',width:300}
    ]]
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-content-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('del'); ?>",{ids:ids,type:'workflow'},function(data){
				layer.close(index);
				dialog(data.msg,data.code);
				tableIns.reload()
			})
		});
	}
	//设置状态
	function setst(id,status){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		var index = layer.load();
		$.post("<?php echo url('setst'); ?>",{ids:ids,status:status,type:'workflow'},function(data){
			layer.close(index);
			dialog(data.msg,data.code);
			tableIns.reload()
		})
	}

	// 参数配置
	function configParams(id) {
		console.log('配置参数，工作流ID:', id);
		var data = getRowDataById(id);
		if(!data){
			layer.msg('未找到工作流信息，请刷新页面后重试', {icon: 2});
			console.error('未找到工作流数据，ID:', id);
			return;
		}

		if(!data.workflow_id){
			layer.msg('工作流ID为空，无法配置参数', {icon: 2});
			console.error('工作流ID为空，数据:', data);
			return;
		}

		console.log('打开参数配置页面，工作流ID:', data.workflow_id);
		openmax('<?php echo url("workflowParams"); ?>/workflow_id/' + data.workflow_id, '参数配置 - ' + data.name);
	}

	// 根据ID获取行数据
	function getRowDataById(id) {
		console.log('=== 开始获取行数据 ===');
		console.log('查找ID:', id, '类型:', typeof id);

		// 方法1: 使用layui table.cache获取数据（正确的方法）
		try {
			var cacheData = table.cache.tabledata;
			console.log('方法1 - table.cache.tabledata:', cacheData);
			if(cacheData && cacheData.length > 0){
				for(var i = 0; i < cacheData.length; i++){
					console.log('检查缓存数据[' + i + ']:', cacheData[i]);
					// 确保ID类型匹配
					if(cacheData[i].id == id || cacheData[i].id === String(id) || cacheData[i].id === Number(id)){
						console.log('从cache找到数据:', cacheData[i]);
						return cacheData[i];
					}
				}
			}
		} catch(e) {
			console.log('方法1失败:', e);
		}

		// 方法2: 使用table.getData获取数据
		try {
			var tableData = table.getData('tabledata');
			console.log('方法2 - table.getData:', tableData);
			if(tableData && tableData.length > 0){
				for(var i = 0; i < tableData.length; i++){
					console.log('检查getData数据[' + i + ']:', tableData[i]);
					if(tableData[i].id == id || tableData[i].id === String(id) || tableData[i].id === Number(id)){
						console.log('从getData找到数据:', tableData[i]);
						return tableData[i];
					}
				}
			}
		} catch(e) {
			console.log('方法2失败:', e);
		}

		// 方法3: 直接从DOM中获取数据
		try {
			var $table = $('#tabledata').next('.layui-table-view');
			console.log('方法3 - DOM表格:', $table);
			if($table.length > 0){
				var rows = $table.find('.layui-table-body tbody tr');
				console.log('方法3 - 找到行数:', rows.length);
				rows.each(function(index, row){
					var $row = $(row);
					var rowData = $row.data('index');
					console.log('方法3 - 行数据:', rowData);
				});
			}
		} catch(e) {
			console.log('方法3失败:', e);
		}

		console.log('=== 所有方法都未找到数据，ID:', id, ' ===');
		return null;
	}
	// 测试工作流
	function testWorkflow(id, type) {
		console.log('测试工作流，ID:', id, '类型:', type);
		var data = getRowDataById(id);
		if(!data){
			layer.msg('未找到工作流信息，请刷新页面后重试', {icon: 2});
			console.error('未找到工作流数据，ID:', id);
			return;
		}

		if(!data.workflow_id){
			layer.msg('工作流ID为空，无法测试', {icon: 2});
			console.error('工作流ID为空，数据:', data);
			return;
		}

		var workflowId = data.workflow_id;
		var workflowName = data.name;
		var isAsync = (type === 'async');

		// 先获取工作流参数配置，根据配置显示不同界面
		$.get('<?php echo url("getWorkflowParams"); ?>/workflow_id/' + workflowId, function(res) {
			if (res.code === 1) {
				var workflow = res.data.workflow;
				var params = res.data.params;
				var mode = res.data.mode;

				if (mode === 'custom' && params && params.length > 0) {
					// 自定义字段模式：显示动态表单
					showDynamicTestForm(workflowId, workflowName, params, isAsync);
				} else {
					// JSON模式：显示传统JSON输入框
					showJsonTestForm(workflowId, workflowName, workflow, isAsync);
				}
			} else {
				// 获取参数配置失败，使用默认JSON模式
				showJsonTestForm(workflowId, workflowName, data, isAsync);
			}
		}).fail(function() {
			// 请求失败，使用默认JSON模式
			showJsonTestForm(workflowId, workflowName, data, isAsync);
		});
	}

	// 显示JSON参数测试表单
	function showJsonTestForm(workflowId, workflowName, workflow, isAsync) {
		// 获取工作流的默认参数
		var defaultParams = {};

		// 处理不同的数据结构
		var defaultParamsStr = '';
		if (workflow && workflow.default_params) {
			defaultParamsStr = workflow.default_params;
		} else if (workflow && workflow.default_parameters) {
			defaultParamsStr = workflow.default_parameters;
		}

		if (defaultParamsStr) {
			try {
				defaultParams = JSON.parse(defaultParamsStr);
			} catch (e) {
				console.log('解析默认参数失败:', e);
				defaultParams = {};
			}
		}

		// 弹出参数输入框
		layer.open({
			type: 1,
			title: '测试工作流: ' + workflowName + ' (' + (isAsync ? '异步' : '同步') + ') - JSON模式',
			content: '<div style="padding: 20px;">' +
					 '<div class="layui-form-item">' +
					 '<label class="layui-form-label">工作流ID:</label>' +
					 '<div class="layui-input-block">' + workflowId + '</div>' +
					 '</div>' +
					 '<div class="layui-form-item">' +
					 '<label class="layui-form-label">执行参数:</label>' +
					 '<div class="layui-input-block">' +
					 '<textarea id="testParams" placeholder="请输入JSON格式的参数" class="layui-textarea" style="height: 200px;">' +
					 JSON.stringify(defaultParams, null, 2) + '</textarea>' +
					 '</div>' +
					 '</div>' +
					 '<div class="layui-form-item">' +
					 '<div class="layui-input-block">' +
					 '<button type="button" class="layui-btn" onclick="executeWorkflowRequest(\'' + workflowId + '\', \'' + workflowName + '\', null, ' + isAsync + ')">执行工作流</button>' +
					 '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>' +
					 '</div>' +
					 '</div>' +
					 '</div>',
			area: ['600px', '450px']
		});
	}

	// 显示动态参数测试表单
	function showDynamicTestForm(workflowId, workflowName, params, isAsync) {
		// 调试信息
		console.log('显示动态表单，参数:', params);

		if (!params || params.length === 0) {
			layer.msg('没有找到参数配置，使用JSON模式');
			showJsonTestForm(workflowId, workflowName, {}, isAsync);
			return;
		}

		var formHtml = '<div style="padding: 20px;">';
		formHtml += '<form class="layui-form" id="dynamicTestForm" lay-filter="dynamicTestForm">';

		// 工作流信息
		formHtml += '<div class="layui-form-item">';
		formHtml += '<label class="layui-form-label" style="width: 100px;">工作流ID:</label>';
		formHtml += '<div class="layui-input-block" style="margin-left: 130px;">';
		formHtml += '<div class="layui-form-mid layui-word-aux">' + workflowId + '</div>';
		formHtml += '</div>';
		formHtml += '</div>';

		formHtml += '<div class="layui-form-item">';
		formHtml += '<label class="layui-form-label" style="width: 100px;">工作流名称:</label>';
		formHtml += '<div class="layui-input-block" style="margin-left: 130px;">';
		formHtml += '<div class="layui-form-mid layui-word-aux">' + workflowName + '</div>';
		formHtml += '</div>';
		formHtml += '</div>';

		// 根据参数配置生成表单字段
		for (var i = 0; i < params.length; i++) {
			var param = params[i];

			// 详细调试信息 - 输出完整的参数对象结构
			console.log('=== 参数 ' + i + ' 详细信息 ===');
			console.log('完整参数对象:', JSON.stringify(param, null, 2));
			console.log('对象属性列表:', Object.keys(param));
			console.log('对象类型:', typeof param);

			// 获取参数名称，支持多种字段名
			var paramName = param.name || param.field || param.key || param.param_name || param.field_name || ('param_' + i);
			var paramLabel = param.label || param.title || param.display_name || param.param_label || param.field_label || param.name || param.field || param.key || ('参数' + (i + 1));
			var paramType = param.type || param.field_type || param.input_type || 'text';

			// 输出解析结果
			console.log('解析结果:');
			console.log('- paramName:', paramName);
			console.log('- paramLabel:', paramLabel);
			console.log('- paramType:', paramType);

			formHtml += '<div class="layui-form-item">';
			formHtml += '<label class="layui-form-label" style="width: 100px;">' + paramLabel + ':</label>';
			formHtml += '<div class="layui-input-block" style="margin-left: 130px;">';

			var defaultValue = param.default_value || param.defaultValue || param.value || param.default || '';
			var placeholder = param.placeholder || param.hint || param.tips || '';
			var required = param.required ? 'lay-verify="required"' : '';

			console.log('其他属性 - 默认值:', defaultValue, '占位符:', placeholder, '必填:', param.required);
			console.log('========================');

			if (paramType === 'text' || !paramType) {
				formHtml += '<input type="text" name="' + paramName + '" placeholder="' + placeholder + '" value="' + defaultValue + '" class="layui-input" ' + required + '>';
			} else if (paramType === 'textarea') {
				formHtml += '<textarea name="' + paramName + '" placeholder="' + placeholder + '" class="layui-textarea" ' + required + '>' + defaultValue + '</textarea>';
			} else if (paramType === 'number') {
				formHtml += '<input type="number" name="' + paramName + '" placeholder="' + placeholder + '" value="' + defaultValue + '" class="layui-input" ' + required + '>';
			} else if (paramType === 'select') {
				formHtml += '<select name="' + paramName + '" lay-filter="' + paramName + '" ' + required + '>';
				formHtml += '<option value="">请选择</option>';
				if (param.options && param.options.length > 0) {
					for (var j = 0; j < param.options.length; j++) {
						var option = param.options[j];
						var selected = (option.value == defaultValue) ? 'selected' : '';
						var optionLabel = option.label || option.text || option.name || option.value;
						formHtml += '<option value="' + option.value + '" ' + selected + '>' + optionLabel + '</option>';
					}
				}
				formHtml += '</select>';
			} else if (paramType === 'radio') {
				if (param.options && param.options.length > 0) {
					for (var k = 0; k < param.options.length; k++) {
						var radioOption = param.options[k];
						var checked = (radioOption.value == defaultValue) ? 'checked' : '';
						var radioLabel = radioOption.label || radioOption.text || radioOption.name || radioOption.value;
						formHtml += '<input type="radio" name="' + paramName + '" value="' + radioOption.value + '" title="' + radioLabel + '" ' + checked + '>';
					}
				}
			} else if (paramType === 'checkbox') {
				var checked = defaultValue ? 'checked' : '';
				formHtml += '<input type="checkbox" name="' + paramName + '" lay-skin="switch" lay-text="是|否" ' + checked + '>';
			}

			// 添加参数说明
			if (param.description) {
				formHtml += '<div class="layui-form-mid layui-word-aux">' + param.description + '</div>';
			}

			formHtml += '</div>';
			formHtml += '</div>';
		}

		// 按钮区域
		formHtml += '<div class="layui-form-item" style="margin-top: 30px;">';
		formHtml += '<div class="layui-input-block" style="margin-left: 0; text-align: center;">';
		formHtml += '<button type="button" class="layui-btn layui-btn-normal" onclick="executeDynamicTest(\'' + workflowId + '\', \'' + workflowName + '\', ' + isAsync + ')">执行工作流</button>';
		formHtml += '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()" style="margin-left: 10px;">取消</button>';
		formHtml += '</div>';
		formHtml += '</div>';

		formHtml += '</form>';
		formHtml += '</div>';

		layer.open({
			type: 1,
			title: '测试工作流: ' + workflowName + ' (' + (isAsync ? '异步' : '同步') + ') - 自定义参数',
			content: formHtml,
			area: ['650px', '600px'],
			success: function(layero, index) {
				// 重新渲染layui表单
				setTimeout(function() {
					layui.form.render();
					console.log('layui表单渲染完成');
				}, 100);
			}
		});
	}

	// 执行动态表单测试
	function executeDynamicTest(workflowId, workflowName, isAsync) {
		var formData = {};
		$('#dynamicTestForm input, #dynamicTestForm textarea, #dynamicTestForm select').each(function() {
			var name = $(this).attr('name');
			var value = $(this).val();
			if (name) {
				formData[name] = value;
			}
		});

		executeWorkflowRequest(workflowId, workflowName, formData, isAsync);
	}

	// 执行工作流请求
	function executeWorkflowRequest(workflowId, workflowName, formData, isAsync) {
		var params;
		if (formData) {
			// 动态表单数据
			params = JSON.stringify(formData);
		} else {
			// JSON模式数据
			params = $('#testParams').val();
		}

		var index = layer.load();

		$.post('<?php echo url("runWorkflowDemo"); ?>', {
			workflow_id: workflowId,
			parameters: params,
			is_async: isAsync
		}, function(res) {
			layer.close(index);
			if(res.code == 1) {
				layer.msg('测试执行成功', {icon: 1});
				layer.closeAll();
				// 刷新执行日志
				if (typeof tableIns !== 'undefined') {
					setTimeout(function() {
						tableIns.reload();
					}, 1000);
				}
			} else {
				layer.msg('测试执行失败: ' + res.msg, {icon: 2});
			}
		}).fail(function() {
			layer.close(index);
			layer.msg('请求失败，请检查网络连接', {icon: 2});
		});
	}
  </script>
</body>
</html>
