{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?cdac", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?6ae0", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?cb46", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?4ae4", "uni-app:///yuyue/peodetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?fb28", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/peodetail.vue?a81b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "type", "keyword", "nodata", "curTopIndex", "video_status", "video_title", "video_tag", "onLoad", "onShareAppMessage", "title", "pic", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "switchTopTab", "getdatalist", "pagenum", "field", "order", "uni", "console", "scrolltolower"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+GlxB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAL;MACAM;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAmBA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACAC;QAAAC;MAAA;QACAF;QACA;QACAA;QACA;UACAA;UACAA;UACAA;QACA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA;QACA;MACA;MACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;MACAA;MACAA;MACAC;QAAAZ;QAAAgB;QAAAC;QAAAC;QAAAL;MAAA;QACAF;QACAQ;QACA;QACAC;QACA;UACAA;UACA;YACAT;YACAS;UACA;YACAT;UACA;QACA;QACA;QACA;QACAA;MACA;IAEA;IACAU;MAEA;QAEA;QACA;MAEA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5OA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/peodetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/peodetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peodetail.vue?vue&type=template&id=829b8e2a&\"\nvar renderjs\nimport script from \"./peodetail.vue?vue&type=script&lang=js&\"\nexport * from \"./peodetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peodetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/peodetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail.vue?vue&type=template&id=829b8e2a&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.data.showdesc ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"top flex\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"data.headimg\"> </view>\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"t1\">{{data.realname}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{data.jineng}}</view>\r\n\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t<text class=\"t11\"><text class=\"bold\">{{data.totalnum}}</text> 次服务</text>\r\n\t\t\t\t\t\t<text class=\"t11\"><text class=\"bold\">{{data.comment_score}}</text> 评价</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"data.desc\" class=\"desc\" >\r\n\t\t\t\t{{data.desc}}\r\n\t\t\t</view>\r\n\t\t\t<!-- #ifdef MP-TOUTIAO -->\r\n\t\t\t<view class=\"dp-cover\" v-if=\"video_status\">\r\n\t\t\t\t<button open-type=\"share\" data-channel=\"video\" class=\"dp-cover-cover\" :style=\"{\r\n\t\t\t\t\tzIndex:10,\r\n\t\t\t\t\ttop:'20vh',\r\n\t\t\t\t\tleft:'80vw',\r\n\t\t\t\t\twidth:'110rpx',\r\n\t\t\t\t\theight:'110rpx'\r\n\t\t\t\t}\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/uploadvideo2.png'\" :style=\"{width:'110rpx',height:'110rpx'}\"/>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"tab\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == 0 ? 'on' : '') \" @tap=\"switchTopTab\" :data-index=\"0\">服务 {{data.count}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == 1 ? 'on' : '') \" @tap=\"switchTopTab\" :data-index=\"1\">评价 {{data.comment_score}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\t<view \tv-if=\"data.showdesc\" :class=\"'item ' + (curTopIndex == 2 ? 'on' : '') \" @tap=\"switchTopTab\" :data-index=\"2\">个人资料 <view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"curTopIndex==0\" v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content2 flex\" :data-id=\"item.id\">\r\n\t\t\t\t\t<view class=\"f1\" @click=\"goto\" :data-url=\"'product?id='+item.id\" >\r\n\t\t\t\t\t\t<view class=\"headimg\"><image :src=\"item.pic\" /></view>\r\n\t\t\t\t\t\t<view class=\"text1\" style=\"margin-bottom:16rpx\">\t\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}} </text>\r\n\t\t\t\t\t\t\t<!-- <view class=\"text2\">服务时长：{{item.fwlong}}分钟</view> -->\r\n\t\t\t\t\t\t\t<view class=\"text2\">已售 {{item.sales}}</view>\r\n\t\t\t\t\t\t\t<view class=\"text3\">\r\n\t\t\t\t\t\t\t\t<text class=\"t4\">￥<text class=\"price\"> {{item.sell_price}}</text> </text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text4\" v-if=\"item.order_flow_mode === 1\">\r\n\t\t\t\t\t\t\t\t<text class=\"flow-mode\">先支付后预约</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"yuyue\"  @click=\"goto\" :data-url=\"'product?id='+item.id\">预约</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"comment\" v-if=\"curTopIndex==1\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image class=\"t1\" :src=\"item.headimg\"/>\r\n\t\t\t\t\t\t\t<view class=\"t2\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (item.score>item2?'2':'') + '.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"color:#777;font-size:22rpx;\">{{item.createtime}}</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.content}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"item.content_pic!=''\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in item.content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f3\" v-if=\"item.reply_content\">\r\n\t\t\t\t\t\t\t<view class=\"arrow\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t1\">商家回复：{{item.reply_content}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view  v-if=\"curTopIndex==2\">\r\n\t\t\t\t\t<view  class=\"plugdesc\">\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">服务类目：</label>{{data.typename}}</view>\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">服务城市：</label>{{data.citys}}</view>\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">服务公里数：</label>{{data.fuwu_juli}}km</view>\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">性别：</label>{{data.sex}}</view>\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">年龄：</label>{{data.age}}</view>\r\n\t\t\t\t\t\t<view class=\"item\"><label class=\"t1\">联系信息：</label>{{data.tel}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<view style=\"height:140rpx\"></view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tdata:[],\r\n      datalist: [],\r\n      type: \"\",\r\n\t\t\tkeyword:'',\r\n\t\t\tnodata:false,\r\n\t\t\tcurTopIndex:0,\r\n\t\t\t\r\n\t\t\tvideo_status:0,\r\n\t\t\tvideo_title:'',\r\n\t\t\tvideo_tag:[]\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.type = this.opt.type || '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonShareAppMessage:function(shareOption){\r\n\t\t//#ifdef MP-TOUTIAO\r\n\t\tconsole.log(shareOption);\r\n\t\t\treturn {\r\n\t\t\t\t\r\n\t\t\t\ttitle: this.video_title,\r\n\t\t\t\tchannel: \"video\",\r\n\t\t\t\textra: {\r\n\t\t\t\t        hashtag_list: this.video_tag,\r\n\t\t\t\t      },\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log(\"分享成功\");\r\n\t\t\t\t},\r\n\t\t\t\t fail: (res) => {\r\n\t\t\t\t    console.log(res);\r\n\t\t\t\t    // 可根据 res.errCode 处理失败case\r\n\t\t\t\t  },\r\n\t\t\t};\r\n\t\t//#endif\r\n\t\treturn this._sharewx({title:this.realname,pic:this.headimg});\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tapp.get('ApiYuyue/peodetail', {id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.data = data;\r\n\t\t\t\tif(res.set){\r\n\t\t\t\t\tthat.video_status = res.set.video_status;\r\n\t\t\t\t\tthat.video_title = res.set.video_title;\r\n\t\t\t\t\tthat.video_tag = res.set.video_tag;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tthat.getdatalist();\r\n\t\t\t});\r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  this.curTopIndex = index;\r\n\t\t\tif(index<2){\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t\t}\r\n\t\t\tif(index==2) that.nodata = false;\r\n\t\t\t\r\n\t\t},   \r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tvar order = that.order;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.post('ApiYuyue/getdlist', {curTopIndex:that.curTopIndex,pagenum: pagenum,field: field,order: order,id:id}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tconsole.log(data.length);\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tconsole.log(pagenum);\r\n\t\t\t\t\tif(pagenum == 1){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t\tconsole.log(222);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.nomore = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar datalist = that.datalist;\r\n\t\t\t\tvar newdata = datalist.concat(data);\r\n\t\t\t\tthat.datalist = newdata;\r\n\t\t\t});\r\n\t\t \r\n\t\t},\r\n\t\tscrolltolower: function () {\r\n\t\t     \r\n\t\t\tif (!this.nomore) {\r\n\t\t   \r\n\t\t\t\tthis.pagenum = this.pagenum + 1;    \r\n\t\t\t\tthis.getdatalist(true);\r\n\t\t \r\n\t\t\t}\r\n\t\t \r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.container{ background: #fff; }\r\n.headimg{ width: 160rpx; height: 160rpx;margin-right: 20rpx;}\r\n.headimg image{ width: 160rpx; height: 160rpx; border-radius: 50%; }\r\n.top .f1{ margin-left: 10rpx;}\r\n.f1 .t1{ color:#323232;  font-size: 32rpx; font-weight: bold; margin-top: 20rpx;}\r\n.f1 .t2{ color:#999;  font-size: 24rpx; }\r\n.f1 .t3{ margin-top: 30rpx;color:#999;font-size: 0.24rpx;}\r\n.f1 .t3 .bold{ font-size: 36rpx; color: #323232; font-weight: bold;margin:0 8rpx;}\r\n.f1 .t11{ margin-right: 30rpx; font-size: 24rpx;}\r\n.desc{ color: #6d6e74; font-size: 26rpx; margin-top: 30rpx; padding:0 30rpx;}\r\n.tab{ margin-top: 20rpx; display: flex; }\r\n.tab .item{ padding-right:20rpx; color: #323232;font-size: 28rpx;  margin-right: 40rpx; line-height: 60rpx; overflow: hidden;position:relative; }\r\n.tab .after{display:none;position:absolute;left:45%;margin-left:-20rpx;bottom:0rpx;height:6rpx;border-radius:1.5px;width:40rpx}\r\n.tab .on .after{display:block}\r\n.top{ padding: 30rpx;}\r\n\r\n.plugdesc { padding: 30rpx 0;}\r\n.plugdesc .item{ display: flex; padding: 10rpx 0;  color: #778899; }\r\n.plugdesc .item .t1{ text-align: right; width: 180rpx; margin-right: 20rpx; color: #333;}\r\n\r\n.list{ padding: 0 40rpx;}\r\n.content2{width:100%;background:#fff;border-radius:5px; justify-content: space-between; margin-top: 40rpx; border-bottom: 1px solid #EEEEEE;}\r\n.content2 .f1{display:flex;align-items:center}\r\n.content2 .f1 image{ width: 140rpx; height: 140rpx; border-radius: 10rpx;}\r\n.content2 .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:10rpx;}\r\n.content2 .f1 .t2{color:#999999;font-size:28rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content2 .f1 .t3{ margin-left:10rpx;display: block; height: 40rpx;line-height: 40rpx;}\r\n.content2 .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}\r\n.content2 .f3{height:96rpx;display:flex;align-items:center}\r\n.content2 .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content2 .radio .radio-img{width:100%;height:100%}\r\n.content2 .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\r\n.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3 .t5{ margin-left: 20rpx;}\r\n.text3 .t5 text{ color:#7A83EC}\r\n.text3 .t4 text{ color:#FF5347}\r\n.text3 .t4 { color:#FF5347}\r\n.text3 .t4 .price{ font-weight: bold;font-size: 30rpx;}\r\n.yuyue{ background: #7A83EC; width:136rpx;height: 60rpx; line-height: 60rpx; padding: 0 10rpx; color:#fff; border-radius:28rpx; ; font-size: 20rpx; text-align: center; margin-top: 20rpx;}\r\n\r\n.comment{display:flex;flex-direction:column;padding:10rpx 0;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{width:100%;padding:10rpx 0;position:relative}\r\n.comment .item .f3 .arrow{width: 16rpx;height: 16rpx;background:#eee;transform: rotate(45deg);position:absolute;top:0rpx;left:36rpx}\r\n.comment .item .f3 .t1{width:100%;border-radius:10rpx;padding:10rpx;font-size:22rpx;color:#888;background:#eee}\r\n.dp-cover{height: auto; position: relative;}\r\n.dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}\r\n.text4 {\r\n  margin-left: 10rpx;\r\n  margin-top: 10rpx;\r\n}\r\n.flow-mode {\r\n  color: #7A83EC;\r\n  font-size: 20rpx;\r\n  background: #E8E8F7;\r\n  padding: 3rpx 20rpx;\r\n  border-radius: 18rpx;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./peodetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115043745\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}