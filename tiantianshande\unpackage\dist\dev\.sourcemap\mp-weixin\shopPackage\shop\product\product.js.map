{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?22ce", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?438d", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?3911", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?1013", "uni-app:///shopPackage/shop/product/product.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?8ce0", "webpack:///D:/qianhouduankaifabao/tiantianshande/shopPackage/shop/product/product.vue?0b71"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "textset", "onLoad", "onShow", "uni", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "console", "imageUrl", "query", "onUnload", "clearInterval", "methods", "showLinkChange", "getdata", "that", "app", "id", "desc", "setTimeout", "view0", "size", "rect", "scrollOffset", "view1", "view2", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "shareScheme", "schemeDialogClose", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "changetoptab", "scroll", "sharemp", "shareapp", "itemList", "success", "scene", "sharedata", "sharelink", "showsubqrcode", "closesubqrcode", "addcart", "showgg1Dialog", "closegg1Dialog", "showgg2Dialog", "closegg2Dialog"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0chxB;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,wDACA,oDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,2DACA,4DACA,sDACA,0DACA,0DACA,2DACA,0DACA,sDACA,4DACA,4DACA,4DACA,4DACA,sDACA,0DACA,yDACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACAE;IACAA;IACA;MACAH;MACAI;MACAC;IACA;EACA;EACAC;IACAC;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACAD;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAd;UACAG;QACA;QAEAW;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UAAAX;UAAAC;UAAAa;QAAA;QAEAC;UACA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;YACAhB;YACAQ;UACA;UACA;UACAS;YACAH;YAAA;YACAC;YAAA;YACAC;UACA;YACAhB;YACAQ;UACA;UACA;UACAU;YACAJ;YAAA;YACAC;YAAA;YACAC;UACA;YACAhB;YACAQ;UACA;QACA;MAEA;IACA;IACAW;MACA;MACAX;IACA;IACAY;MACA;MACA1B;IACA;IACA2B;MACA;MACA3B;IACA;IACA4B;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAd;QAAAe;QAAAC;MAAA;QACA;UACAjB;QACA;QACAC;MACA;IACA;IACAiB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACApB;MACAA;MACAC;MACAA;QAAAe;MAAA;QACAf;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IAEAqB;MACA;MACApB;MACAA;QAAAe;MAAA;QACAf;QACA;UACAA;QACA;UACAD;UACAA;QACA;MACA;IACA;IAEAsB;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAC;MACA;MACA;MACA;MACA;MACAtC;IACA;IACAuC;MACA;MACA;MACA;MACA;QACA/B;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACAR;MACAA;MACAA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAwC;MACA/B;MACA;IACA;IACAgC;MACA;MACAjC;MACAd;QACAgD;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAC;oBACA;oBACA;sBACAA;oBACA;oBACAD;kBACA;gBACA;cACA;YACA;YACAnD;UACA;QACA;MACA;IACA;IACAqD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAjD;MACA;IACA;IACAkD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACz0BA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shopPackage/shop/product/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './shopPackage/shop/product/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=22e8c403&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shopPackage/shop/product/product.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=template&id=22e8c403&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var m2 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.tjdatalist.length\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    g1 > 0 &&\n    _vm.toptabbar_index == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g2 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g3 = _vm.isload\n    ? _vm.showtoptabbar == 1 && _vm.couponlist.length > 0\n    : null\n  var l0 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m10 = _vm.t(\"color1rgb\")\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 =\n    _vm.isload && (_vm.product.price_type != 1 || _vm.product.min_price > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !(_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.xunjia_text\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m15 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m17 =\n    _vm.isload && _vm.product.buyselect_commission > 0 ? _vm.t(\"佣金\") : null\n  var m18 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m19 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m20 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m21 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m22 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m23 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var g4 = _vm.isload\n    ? _vm.cuxiaolist.length > 0 ||\n      _vm.couponlist.length > 0 ||\n      _vm.fuwulist.length > 0 ||\n      _vm.product.discount_tips != \"\"\n    : null\n  var g5 = _vm.isload && g4 ? _vm.fuwulist.length : null\n  var g6 = _vm.isload && g4 ? _vm.cuxiaolist.length : null\n  var l1 =\n    _vm.isload && g4 && g6 > 0\n      ? _vm.__map(_vm.cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m24 = _vm.t(\"color1rgb\")\n          var m25 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m24: m24,\n            m25: m25,\n          }\n        })\n      : null\n  var g7 =\n    _vm.isload && g4\n      ? _vm.couponlist.length > 0 && _vm.showtoptabbar == 0\n      : null\n  var l2 =\n    _vm.isload && g4 && g7\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m26 = _vm.t(\"color1rgb\")\n          var m27 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m26: m26,\n            m27: m27,\n          }\n        })\n      : null\n  var m28 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g8 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m29 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m30 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m31 = _vm.isload ? _vm.isEmpty(_vm.product.paramdata) : null\n  var g9 = _vm.isload ? _vm.tjdatalist.length : null\n  var m32 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.cartnum > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m33 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m34 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m35 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    _vm.product.price_type == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.price_type == 1) &&\n    _vm.product.freighttype != 3 &&\n    _vm.product.freighttype != 4\n      ? _vm.t(\"color2\")\n      : null\n  var m37 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.price_type == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m38 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m39 =\n    _vm.isload && _vm.sharetypevisible && !(m38 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m40 =\n    _vm.isload && _vm.sharetypevisible && !(m38 == \"app\") && !(m39 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m41 =\n    _vm.isload && _vm.sharetypevisible\n      ? _vm.getplatform() == \"wx\" && _vm.xcx_scheme\n      : null\n  var m42 = _vm.isload && _vm.showScheme ? _vm.t(\"color1\") : null\n  var m43 = _vm.isload && _vm.showScheme ? _vm.t(\"color1rgb\") : null\n  var m44 =\n    _vm.isload && _vm.showLinkStatus && _vm.business.tel\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g1: g1,\n        m8: m8,\n        m9: m9,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        l1: l1,\n        g7: g7,\n        l2: l2,\n        m28: m28,\n        g8: g8,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        g9: g9,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\r\n\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"sysset.logo\"/></view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\r\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t</block>\r\n\r\n\t\t<view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\r\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\" class=\"flex-y-center\">\r\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\r\n\t\t\t\t\t<view style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"toptabbar_tab\" v-if=\"showtoptabbar==1 && toptabbar_show==1\">\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==0?'on':''\" :style=\"{color:toptabbar_index==0?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"0\">商品<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==1?'on':''\" :style=\"{color:toptabbar_index==1?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"1\">评价<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" :class=\"toptabbar_index==2?'on':''\" :style=\"{color:toptabbar_index==2?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"2\">详情<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t<view class=\"item\" v-if=\"tjdatalist.length > 0\" :class=\"toptabbar_index==3?'on':''\" :style=\"{color:toptabbar_index==3?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"3\">推荐<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t</view>\r\n\r\n\t\t<scroll-view @scroll=\"scroll\" :scrollIntoView=\"scrollToViewId\" :scrollTop=\"scrollTop\" :scroll-y=\"true\" style=\"height:100%;overflow:scroll\">\r\n\t\t\r\n\t\t<view id=\"scroll_view_tab0\">\r\n\t\t\t<view class=\"swiper-container\" v-if=\"isplay==0\">\r\n\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item\"/></view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"imageCount\" v-if=\"product.diypics\" @tap=\"goto\" :data-url=\"'/pagesExt/shop/diylight?id='+product.id\" style=\"bottom: 92rpx; width: 140rpx;\">自助试灯</view>\r\n\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\"><image src=\"/static/img/video.png\"/><view class=\"txt\">{{product.video_duration}}</view></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"videobox\" v-if=\"isplay==1\">\r\n\t\t\t\t<video autoplay=\"true\" class=\"video\" id=\"video\" :src=\"product.video\"></video>\r\n\t\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">退出播放</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"showtoptabbar==1 && couponlist.length>0\" style=\"background:#fff;padding:0 16rpx\">\r\n\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view style=\"background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0\" v-if=\"shopset.detail_guangao1\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao1\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao1\" @tap=\"showgg1Dialog\"/>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"gg1Dialog\" ref=\"gg1Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao1 && shopset.detail_guangao1_t\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao1_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao1_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\r\n\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg1Dialog\">\r\n\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t\t\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<block v-if=\"product.price_type != 1 || product.min_price > 0\">\r\n\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\r\n\t\t\t\t\t\t\t<view v-if=\"product.huang_dx.types\" class=\"huang_bz\">\r\n\t\t\t\t\t\t\t\t<view class=\"huang_i\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"huang_nums\">可用红包：{{product.huang_dx.nums}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" src=\"/static/img/share.png\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"product.yuanbao\" style=\"margin: 0;font-size: 26rpx;margin-bottom: 10rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">元宝价：{{product.yuanbao}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"product.xunjia_text\" class=\"price_share\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">询价</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" src=\"/static/img/share.png\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\r\n\t\t\t\t<view class=\"sales_stock\" v-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">\r\n\t\t\t\t\t<!-- <view class=\"f1\">奖励消费值：{{product.xiaofeizhi2}} </view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"f1\">奖励创业值：{{product.chuangyezhi}} </view> -->\r\n\t\t\t\t\t<view class=\"f2\">推广佣金：{{product.commission}}元</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"sales_stock\" v-if=\"shopset.hide_sales != 1 || shopset.hide_stock != 1\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"shopset.hide_sales != 1\">销量：{{product.sales}} </view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"shopset.hide_stock != 1\">库存：{{product.stock}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">分享好友购买预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}</view>\r\n\t\t\t\t<view style=\"margin:20rpx 0;color:#333;font-size:22rpx\" v-if=\"product.balance_price > 0\">首付款金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\r\n\t\t\t\t<view style=\"margin:20rpx 0;color:#666;font-size:22rpx\" v-if=\"product.buyselect_commission > 0\">下单被选奖励预计可得{{t('佣金')}}：<text style=\"font-weight:bold;padding:0 2px\">{{product.buyselect_commission}}</text>元</view>\r\n\r\n\t\t\t\t<view class=\"upsavemoney\" :style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\" v-if=\"product.upsavemoney > 0\">\r\n\t\t\t\t\t<view class=\"flex1\">升级到 {{product.nextlevelname}} 预计可节省<text style=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney}}</text>元</view>\r\n\t\t\t\t\t<view style=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\" @tap=\"goto\" data-url=\"/pagesExa/my/levelup\">立即升级<image src=\"/static/img/arrowright2.png\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t</view> \r\n\t\t\t</view>\r\n\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\r\n\t\t\t\t<view class=\"f0\">规格</view>\r\n\t\t\t\t<view class=\"f1 flex1\">\r\n\t\t\t\t\t<block v-if=\"product.price_type == 1\">查看规格</block>\r\n\t\t\t\t\t<block v-else>请选择商品规格及数量</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image class=\"f2\" src=\"/static/img/arrowright.png\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.xiaofeizhi2 > 0\">\r\n\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t<view class=\"f0\">送{{t('消费值')}}</view>\r\n\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('消费值')}}{{product.xiaofeizhi2}} 个</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.chuangyezhi > 0\">\r\n\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t<view class=\"f0\">送{{t('创业值')}}</view>\r\n\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx;\">购买可得{{t('创业值')}}{{product.chuangyezhi}} 个</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.givescore > 0\">\r\n\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t<view class=\"f0\">送{{t('积分')}}</view>\r\n\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('积分')}}{{product.givescore}}个</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"cuxiaolist.length>0 || couponlist.length>0 || fuwulist.length>0 || product.discount_tips!=''\">\r\n\t\t\t\t<view class=\"fuwupoint cuxiaoitem\" v-if=\"fuwulist.length>0\">\r\n\t\t\t\t\t<view class=\"f0\">服务</view>\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"cuxiaolist.length>0\">\r\n\t\t\t\t\t<view class=\"f0\">促销</view>\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\">{{item.tip}}</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"product.discount_tips!=''\">\r\n\t\t\t\t\t<view class=\"f0\">折扣</view>\r\n\t\t\t\t\t<view class=\"f1\" style=\"padding-left:10rpx\">{{product.discount_tips}}</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExa/my/levelinfo\">\r\n\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"couponlist.length>0 && showtoptabbar==0\">\r\n\t\t\t\t\t<view class=\"f0\">优惠</view>\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\r\n\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidefuwudetail\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"showcuxiaodialog\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidecuxiaodetail\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\r\n\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidecuxiaodetail\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"suffix\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text style=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text> <text style=\"color:#333;margin-left:20rpx\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<couponlist :couponlist=\"couponlist\" @getcoupon=\"getcoupon\"></couponlist>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view style=\"width:100%;height:auto;padding:20rpx 0 0\" v-if=\"shopset.detail_guangao2\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao2\" style=\"width:100%;height:auto\" mode=\"widthFix\" v-if=\"shopset.detail_guangao2\" @tap=\"showgg2Dialog\"/>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"gg2Dialog\" ref=\"gg2Dialog\" type=\"dialog\" v-if=\"shopset.detail_guangao2 && shopset.detail_guangao2_t\">\r\n\t\t\t\t<image :src=\"shopset.detail_guangao2_t\" @tap=\"previewImage\" :data-url=\"shopset.detail_guangao2_t\" class=\"img\" mode=\"widthFix\" style=\"width:600rpx;height:auto;border-radius:10rpx;\"/>\r\n\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg2Dialog\">\r\n\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t\r\n\t\t</view>\r\n\r\n\t\t<view id=\"scroll_view_tab1\">\r\n\r\n\t\t\t<view class=\"commentbox\" v-if=\"shopset.comment==1 && commentcount > 0\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评率 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" src=\"/static/img/arrowright.png\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"comment\">\r\n\t\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\r\n\t\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"'/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<view id=\"scroll_view_tab2\">\r\n\t\t\t\r\n\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入首页</button>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"!isEmpty(product.paramdata)\">\r\n\t\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品参数</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t\t<view style=\"background:#fff;padding:20rpx 40rpx;\" class=\"paraminfo\">\r\n\t\t\t\t<view v-for=\"(item, index) in product.paramdata\" class=\"paramitem\">\r\n\t\t\t\t\t<view class=\"f1\">{{index}}</view>\r\n\t\t\t\t\t<view class=\"f2\">{{item}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\r\n\t\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t\t<view class=\"detail\">\r\n\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t\r\n\t\t<view id=\"scroll_view_tab3\">\r\n\r\n\t\t\t<view v-if=\"tjdatalist.length > 0\">\r\n\t\t\t\t<view class=\"xihuan\">\r\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t\t<view class=\"xihuan-text\">\r\n\t\t\t\t\t\t<image src=\"/static/img/xihuan.png\" class=\"img\"/>\r\n\t\t\t\t\t\t<text class=\"txt\">为您推荐</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prolist\">\r\n\t\t\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\" :menuindex=\"menuindex\"></dp-product-item>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\r\n\t\t</scroll-view>\r\n\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1&&!showcuxiaodialog\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"item flex1\" @tap=\"goto\" data-url=\"/pages/shop/cart\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/gwc.png\"/>\r\n\t\t\t\t\t<view class=\"t1\">购物车</view>\r\n\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">{{cartnum}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" src=\"/static/img/shoucang.png\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op2\" v-if=\"showjiesheng==1\">\r\n\t\t\t\t<view class=\"tocart2\" :style=\"{background:t('color2')}\" @tap=\"shareClick\"><text>分享赚钱</text><text style=\"font-size:24rpx\">赚￥{{product.commission}}</text></view>\r\n\t\t\t\t<view class=\"tobuy2\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\"><text>立即购买</text><text style=\"font-size:24rpx\" v-if=\"product.jiesheng_money > 0\">省￥{{product.jiesheng_money}}</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\" v-else>\r\n\t\t\t\t<block v-if=\"product.price_type == 1\">\r\n\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"showLinkChange\" data-btntype=\"2\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\" @tap=\"buydialogChange\" data-btntype=\"1\" v-if=\"product.freighttype!=3 && product.freighttype!=4\">加入购物车</view>\r\n\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\">立即购买</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<buydialog v-if=\"buydialogShow\" \r\n\t\t\t:proid=\"product.id\" \r\n\t\t\t:btntype=\"btntype\" \r\n\t\t\t@buydialogChange=\"buydialogChange\" \r\n\t\t\t@showLinkChange=\"showLinkChange\" \r\n\t\t\t:menuindex=\"menuindex\" \r\n\t\t\t@addcart=\"addcart\">\r\n\t\t</buydialog>\r\n\r\n\t\t<view class=\"scrolltop\" v-show=\"scrolltopshow\" @tap=\"changetoptab\" data-index=\"0\"><image class=\"image\" src=\"/static/img/gotop.png\"/></view>\r\n\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/sharepic.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareScheme\" v-if=\"getplatform() == 'wx' && xcx_scheme\">\r\n\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">小程序链接</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" src=\"/static/img/close.png\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"posterDialog schemeDialog\" v-if=\"showScheme\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"schemecon\">\r\n\t\t\t\t\t<view style=\"line-height: 60rpx;\">{{product.name}} </view>\r\n\t\t\t\t\t<view >购买链接：<text style=\"color: #00A0E9;\">{{schemeurl}}</text></view>\r\n\t\t\t\t\t<view class=\"copybtn\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap.stop=\"copy\" :data-text=\"product.name+'购买链接：'+schemeurl\"> 一键复制 </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" src=\"/static/img/close.png\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"title\">{{sysset.name}}</view>\r\n\t\t\t\t\t<view class=\"row\" v-if=\"product.bid > 0\">\r\n\t\t\t\t\t\t<view class=\"f1\">店铺名称</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+product.bid\">{{business.name}}<image src=\"/static/img/arrowright.png\" class=\"image\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"row\" v-if=\"business.tel\">\r\n\t\t\t\t\t\t<view class=\"f1\">联系电话</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'tel::'+business.tel\" :style=\"{color:t('color1')}\">{{business.tel}}<image src=\"/static/img/copy.png\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"business.tel\"></image></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\ttextset:{},\r\n\t\t\tisload:false,\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tbtntype:1,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tisplay: 0,\r\n\t\t\tshowcuxiaodialog: false,\r\n\t\t\tshowfuwudialog:false,\r\n\t\t\tbusiness: \"\",\r\n\t\t\tproduct: [],\r\n\t\t\tcartnum: \"\",\r\n\t\t\tcommentlist: \"\",\r\n\t\t\tcommentcount: \"\",\r\n\t\t\tcuxiaolist: \"\",\r\n\t\t\tcouponlist: \"\",\r\n\t\t\tfuwulist: [],\r\n\t\t\tpagecontent: \"\",\r\n\t\t\tshopset: {},\r\n\t\t\tsysset:{},\r\n\t\t\ttitle: \"\",\r\n\t\t\tbboglist: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\tshowLinkStatus:false,\r\n\t\t\tshowjiesheng:0,\r\n\t\t\ttjdatalist:[],\r\n\t\t\tshowtoptabbar:0,\r\n\t\t\ttoptabbar_show:0,\r\n\t\t\ttoptabbar_index:0,\r\n      scrollToViewId: \"\",\r\n\t\t\tscrollTop:0,\r\n\t\t\tscrolltab0Height:0,\r\n\t\t\tscrolltab1Height:0,\r\n\t\t\tscrolltab2Height:0,\r\n\t\t\tscrolltab3Height:0,\r\n\t\t\txcx_scheme:false,\r\n\t\t\tshowScheme:false,\r\n\t\t\tschemeurl:''\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonShow:function(e){\r\n\t\tuni.$emit('getglassrecord');\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\r\n\tmethods: {\r\n\t\tshowLinkChange: function () {\r\n\t\t\tthis.showLinkStatus = !this.showLinkStatus;\r\n\t\t},\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiShop/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.cartnum = res.cartnum;\r\n\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\tthat.cuxiaolist = res.cuxiaolist;\r\n\t\t\t\tthat.couponlist = res.couponlist;\r\n\t\t\t\tthat.fuwulist = res.fuwulist;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.showjiesheng = res.showjiesheng || 0;\r\n\t\t\t\tthat.tjdatalist = res.tjdatalist || [];\r\n\t\t\t\tthat.showtoptabbar = res.showtoptabbar || 0;\r\n\t\t\t\tthat.bboglist = res.bboglist;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tthat.xcx_scheme = res.xcx_scheme\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid='+product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});\r\n\t\t\t\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')\r\n\t\t\t\t\tview0.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab0Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t\tlet view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')\r\n\t\t\t\t\tview1.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab1Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t\tlet view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')\r\n\t\t\t\t\tview2.fields({\r\n\t\t\t\t\t\tsize: true,//是否返回节点尺寸（width height）\r\n\t\t\t\t\t\trect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\tscrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthat.scrolltab2Height = res.height\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t},500)\r\n\r\n\t\t\t});\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\t\tpayvideo: function () {\r\n\t\t\tthis.isplay = 1;\r\n\t\t\tuni.createVideoContext('video').play();\r\n\t\t},\r\n\t\tparsevideo: function () {\r\n\t\t\tthis.isplay = 0;\r\n\t\t\tuni.createVideoContext('video').stop();\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\t//收藏操作\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiShop/addfavorite', {proid: proid,type: 'shop'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\t\t\tapp.post('ApiShop/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tshareScheme: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tapp.showLoading();\r\n\t\t\tapp.post('ApiShop/getwxScheme', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.showScheme = true;\r\n\t\t\t\t\t\tthat.schemeurl=data.openlink\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tschemeDialogClose: function () {\r\n\t\t\tthis.showScheme = false;\r\n\t\t},\r\n\t\t\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\t\tshowfuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = true;\r\n\t\t},\r\n\t\thidefuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = false\r\n\t\t},\r\n\t\tshowcuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = true;\r\n\t\t},\r\n\t\thidecuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = false\r\n\t\t},\r\n\t\tgetcoupon:function(){\r\n\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\t//var that = this;\r\n\t\t\t//var scrollY = e.scrollTop;     \r\n\t\t\t//if (scrollY > 200) {\r\n\t\t\t//\tthat.scrolltopshow = true;\r\n\t\t\t//}\r\n\t\t\t//if(scrollY < 150) {\r\n\t\t\t//\tthat.scrolltopshow = false\r\n\t\t\t//}\r\n\t\t\t//if (scrollY > 100) {\r\n\t\t\t//\tthat.toptabbar_show = true;\r\n\t\t\t//}\r\n\t\t\t//if(scrollY < 50) {\r\n\t\t\t//\tthat.toptabbar_show = false\r\n\t\t\t//}\r\n\t\t},\r\n\t\tchangetoptab:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tthis.scrollToViewId = 'scroll_view_tab'+index;\r\n\t\t\tthis.toptabbar_index = index;\r\n\t\t\tif(index == 0) this.scrollTop = 0;\r\n\t\t\tconsole.log(index);\r\n\t\t},\r\n\t\tscroll:function(e){\r\n\t\t\tvar scrollTop = e.detail.scrollTop;\r\n\t\t\t//console.log(e)\r\n\t\t\tvar that = this;\r\n\t\t\tif (scrollTop > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollTop < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t\tif (scrollTop > 100) {\r\n\t\t\t\tthat.toptabbar_show = true;\r\n\t\t\t}\r\n\t\t\tif(scrollTop < 50) {\r\n\t\t\t\tthat.toptabbar_show = false\r\n\t\t\t}\r\n\t\t\tvar height0 = that.scrolltab0Height;\r\n\t\t\tvar height1 = that.scrolltab0Height + that.scrolltab1Height;\r\n\t\t\tvar height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;\r\n\t\t\t//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;\r\n\t\t\tconsole.log('-----------------------');\r\n\t\t\tconsole.log(scrollTop);\r\n\t\t\tconsole.log(height2);\r\n\t\t\tif(scrollTop >=0 && scrollTop < height0){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab0';\r\n\t\t\t\tthis.toptabbar_index = 0;\r\n\t\t\t}else if(scrollTop >= height0 && scrollTop < height1){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab1';\r\n\t\t\t\tthis.toptabbar_index = 1;\r\n\t\t\t}else if(scrollTop >= height1 && scrollTop < height2){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab2';\r\n\t\t\t\tthis.toptabbar_index = 2;\r\n\t\t\t}else if(scrollTop >= height2){\r\n\t\t\t\t//this.scrollToViewId = 'scroll_view_tab3';\r\n\t\t\t\tthis.toptabbar_index = 3;\r\n\t\t\t}\r\n\t\t},\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.sharetitle || that.product.name;\r\n\t\t\t\t\t\tsharedata.summary = that.product.sharedesc || that.product.sellpoint;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/shop/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/pages/shop/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t},\r\n\t\tshowsubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\t\taddcart:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.cartnum = this.cartnum + e.num;\r\n\t\t},\r\n\t\tshowgg1Dialog:function(){\r\n\t\t\tthis.$refs.gg1Dialog.open();\r\n\t\t},\r\n\t\tclosegg1Dialog:function(){\r\n\t\t\tthis.$refs.gg1Dialog.close();\r\n\t\t},\r\n\t\tshowgg2Dialog:function(){\r\n\t\t\tthis.$refs.gg2Dialog.open();\r\n\t\t},\r\n\t\tclosegg2Dialog:function(){\r\n\t\t\tthis.$refs.gg2Dialog.close();\r\n\t\t},\r\n\t}\r\n\r\n};\r\n</script>\r\n<style>\r\npage {position: relative;width: 100%;height: 100%;}\r\n.container{height:100%}\r\n\r\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\r\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\r\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\r\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\r\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;} \r\n.follow_topbar .info {height:80rpx; float:left;}\r\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\r\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.qrcodebox .img{width:400rpx;height:400rpx}\r\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.swiper-container{position:relative;height: 750rpx;overflow: hidden;}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}\r\n\r\n.header {width: 100%;padding: 20rpx 3%;background: #fff;}\r\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .price{display:flex;align-items:flex-end}\r\n.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}\r\n.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center;min-width: 60rpx;}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .price_share .title { display:flex;align-items:flex-end;}\r\n.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}\r\n.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n.header .upsavemoney{display:flex;align-items:center;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:70rpx;padding:0 20rpx}\r\n\r\n.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }\r\n.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.choose .f2{ width: 32rpx; height: 32rpx;}\r\n\r\n.cuxiaodiv{background:#fff;margin-top:20rpx;padding:0 3%;}\r\n\r\n.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}\r\n.fuwupoint .f1 .t:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.fuwupoint .f2 .img{width:32rpx;height:32rpx;}\r\n.fuwudialog-content{font-size:24rpx}\r\n.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.fuwudialog-content .f1:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwudialog-content .f2{color:#777}\r\n\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaoitem{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaoitem:last-child{border-bottom:0}\r\n\r\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\r\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\r\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}\r\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\r\n.popup__title-text{font-size:32rpx}\r\n.popup__close{position:absolute;top:34rpx;right:34rpx}\r\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\r\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.service-item .prefix{padding-top: 2px;}\r\n.service-item .suffix{padding-left: 10rpx;}\r\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\r\n\r\n.bottombar .op2{width:60%;overflow:hidden;display:flex;}\r\n.bottombar .tocart2{ flex:1;height: 80rpx;border-radius:10rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin-right:10rpx;}\r\n.bottombar .tobuy2{ flex:1; height: 80rpx;border-radius:10rpx;color: #fff; background: #df2e24; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n\r\n\r\n.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}\r\n.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}\r\n.sharetypecontent button::after{border:0}\r\n.sharetypecontent .f1 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}\r\n.sharetypecontent .f2 .img{width:90rpx;height:90rpx}\r\n.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}\r\n\r\n.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}\r\n.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}\r\n.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}\r\n.posterDialog .close .img{ width:32rpx;height:32rpx;}\r\n.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\r\n.posterDialog .content .img{width:540rpx;height:960rpx}\r\n.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}\r\n.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;\r\n\t\t-webkit-transform: translate(-50%,-50%);\r\n    transform: translate(-50%,-50%);}\r\n.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}\r\n.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}\r\n.linkDialog .row .f1 {width: 40%; text-align: left;}\r\n.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}\r\n.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}\r\n.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}\r\n\r\n.paramitem{display:flex;border-bottom:1px solid #f5f5f5;padding:20rpx}\r\n.paramitem .f1{width:30%;color:#666}\r\n.paramitem .f2{color:#333}\r\n.paramitem:last-child{border-bottom:0}\r\n\r\n.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:20rpx 160rpx;margin-top:20rpx}\r\n.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}\r\n.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}\r\n.xihuan-text .txt{color:#111;font-size:30rpx}\r\n.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}\r\n.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}\r\n\r\n.toptabbar_tab{display:flex;width:100%;height:90rpx;background: #fff;top:var(--window-top);z-index:11;position:fixed;border-bottom:1px solid #f3f3f3}\r\n.toptabbar_tab .item{flex:1;font-size:28rpx; text-align:center; color:#666; height: 90rpx; line-height: 90rpx;overflow: hidden;position:relative}\r\n.toptabbar_tab .item .after{display:none;position:absolute;left:50%;margin-left:-16rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:32rpx}\r\n.toptabbar_tab .on{color: #323233;}\r\n.toptabbar_tab .on .after{display:block}\r\n\r\n.scrolltop{position:fixed;bottom:160rpx;right:20rpx;width:60rpx;height:60rpx;background:rgba(0,0,0,0.4);color:#fff;border-radius:50%;padding:12rpx 10rpx 8rpx 10rpx;z-index:9;}\r\n.scrolltop .image{width:100%;height:100%;}\r\n\r\n.ggdiaplog_close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.schemeDialog {background:rgba(0,0,0,0.4);z-index:12;}\r\n.schemeDialog .main{ position: absolute;top:30%}\r\n.schemecon{padding: 40rpx 30rpx; }\r\n.copybtn{ text-align: center; margin-top: 30rpx; padding:15rpx 20rpx; border-radius: 50rpx; color:#fff}\r\n\r\n.huang_bz{margin: auto;padding-left: 6px;}\r\n.huang_nums{padding: 2px 5px; background: #97e29d;border-radius: 10px;color: #fff;font-size: 10px;}\r\n\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115005242\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}