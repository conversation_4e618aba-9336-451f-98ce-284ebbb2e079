{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?d061", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?26f5", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?3fe4", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?4e1e", "uni-app:///yuyue/logistics.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?2254", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/logistics.vue?d60b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nodata", "express_com", "express_no", "datalist", "orderinfo", "prolist", "binfo", "worker", "worker_order", "mid", "service_progress", "arrival_info", "completion_info", "need_location", "need_arrival_photo", "need_complete_photo", "yuyue_sign", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "console", "call", "uni", "phoneNumber", "loaded", "da<PERSON><PERSON>", "title", "icon", "latitude", "longitude", "name", "address", "previewImage", "imgList", "current", "urls", "dateFormat", "month", "day", "hour", "minute", "goto", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0MlxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EAEAC;IACAC;MACA;MACAC;MACAA;MACAC;QAAApB;MAAA;QACAmB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QACAE;MACA;IACA;IAEAC;MACA;MACAC;QACAC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAH;UACAI;UACAC;QACA;QACA;MACA;MAEAL;QACAM;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;;MAEA;MACA;QACAC;MACA;MAEA;QACAA;MACA;;MAEA;MACA;QACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;MACA;MAEAX;QACAY;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEAC;MACAC;MACAC;MACAC;MAEA;IACA;IAEA;IACAC;MACA;MACAnB;QACAoB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChXA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/logistics.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/logistics.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./logistics.vue?vue&type=template&id=28f5b113&\"\nvar renderjs\nimport script from \"./logistics.vue?vue&type=script&lang=js&\"\nexport * from \"./logistics.vue?vue&type=script&lang=js&\"\nimport style0 from \"./logistics.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/logistics.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=template&id=28f5b113&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload\n    ? _vm.worker_order.status != 0 &&\n      _vm.service_progress &&\n      _vm.service_progress.length > 0\n    : null\n  var l0 =\n    _vm.isload && g0\n      ? _vm.__map(_vm.service_progress, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = _vm.service_progress.length\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var m0 =\n    _vm.isload && _vm.worker_order.status != 0\n      ? _vm.dateFormat(_vm.worker_order.starttime)\n      : null\n  var m1 =\n    _vm.isload && _vm.worker_order.status != 0 && _vm.worker_order.daodiantime\n      ? _vm.dateFormat(_vm.worker_order.daodiantime)\n      : null\n  var m2 =\n    _vm.isload && _vm.worker_order.status != 0 && _vm.worker_order.sign_time\n      ? _vm.dateFormat(_vm.worker_order.sign_time)\n      : null\n  var m3 =\n    _vm.isload && _vm.worker_order.status != 0 && _vm.worker_order.endtime\n      ? _vm.dateFormat(_vm.worker_order.endtime)\n      : null\n  var m4 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.createtime) : null\n  var m5 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.paytime) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<map v-if=\"worker_order.status!=0 && worker_order.status!=4\" class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_business.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:worker.latitude,\r\n\t\t\tlongitude:worker.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_qishou.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\t\t<map v-else class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_business.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: '/static/peisong/marker_kehu.png',\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\r\n\t\t<view class=\"order-box\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"worker_order.status==3\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已完成</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"worker_order.status==1\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>已接单</view>\r\n\t\t\t\t<view class=\"f1\" v-if=\"worker_order.status==2\"><image src=\"/static/peisong/ps_time.png\" class=\"img\"/>服务中</view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" style=\"border-bottom:0\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"t1\"><text class=\"x1\">{{worker_order.juli}}</text><text class=\"x2\">{{worker_order.juli_unit}}</text></view>\r\n\t\t\t\t\t<view class=\"t2\"><image src=\"/static/peisong/ps_juli.png\" class=\"img\"/></view>\r\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">{{worker_order.juli2}}</text><text class=\"x2\">{{worker_order.juli2_unit}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">{{binfo.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{binfo.address}}</view>\r\n\t\t\t\t\t<view class=\"t3\">{{orderinfo.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 服务进度时间轴 -->\r\n\t\t<view class=\"service-timeline\" v-if=\"worker_order.status!=0 && service_progress && service_progress.length>0\">\r\n\t\t\t<view class=\"timeline-title\">服务进度</view>\r\n\t\t\t<view class=\"timeline-container\">\r\n\t\t\t\t<view v-for=\"(item, index) in service_progress\" :key=\"index\" class=\"timeline-item\" :class=\"item.status === 'completed' ? 'completed' : 'waiting'\">\r\n\t\t\t\t\t<view class=\"timeline-icon\">\r\n\t\t\t\t\t\t<image :src=\"'/static/peisong/' + item.icon + '.png'\" class=\"icon-img\"/>\r\n\t\t\t\t\t\t<view class=\"timeline-line\" v-if=\"index < service_progress.length - 1\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t<view class=\"timeline-header\">\r\n\t\t\t\t\t\t\t<text class=\"timeline-title-text\">{{item.title}}</text>\r\n\t\t\t\t\t\t\t<text class=\"timeline-time\" v-if=\"item.time\">{{item.time}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"timeline-desc\">{{item.desc}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 到达照片 -->\r\n\t\t\t\t\t\t<view class=\"timeline-photo\" v-if=\"item.arrival_photo && need_arrival_photo == 1\">\r\n\t\t\t\t\t\t\t<image :src=\"item.arrival_photo\" mode=\"aspectFill\" @tap=\"previewImage\" :data-src=\"item.arrival_photo\" class=\"photo-img\"/>\r\n\t\t\t\t\t\t\t<text class=\"photo-desc\">到达现场照片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 完成照片 -->\r\n\t\t\t\t\t\t<view class=\"timeline-photo\" v-if=\"item.complete_photo && need_complete_photo == 1\">\r\n\t\t\t\t\t\t\t<image :src=\"item.complete_photo\" mode=\"aspectFill\" @tap=\"previewImage\" :data-src=\"item.complete_photo\" class=\"photo-img\"/>\r\n\t\t\t\t\t\t\t<text class=\"photo-desc\">服务完成照片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 技师位置信息 -->\r\n\t\t<view class=\"technician-location\" v-if=\"need_location == 1 && arrival_info && arrival_info.arrival_distance\">\r\n\t\t\t<view class=\"location-title\">\r\n\t\t\t\t<text class=\"icon-location\">📍</text>\r\n\t\t\t\t<text>技师到达位置</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"location-content\">\r\n\t\t\t\t<view class=\"distance-info\">\r\n\t\t\t\t\t<text class=\"label\">距离您:</text>\r\n\t\t\t\t\t<text class=\"value\">{{arrival_info.arrival_distance_text}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"arrival-time\">\r\n\t\t\t\t\t<text class=\"label\">到达时间:</text>\r\n\t\t\t\t\t<text class=\"value\">{{arrival_info.time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"location-photo\" v-if=\"arrival_info.arrival_photo && need_arrival_photo == 1\">\r\n\t\t\t\t<image :src=\"arrival_info.arrival_photo\" mode=\"aspectFill\" @tap=\"previewImage\" :data-src=\"arrival_info.arrival_photo\" class=\"photo-img\"/>\r\n\t\t\t\t<text class=\"photo-desc\">到达现场照片</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">购买清单({{orderinfo.procount}})</view>\r\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"item\">\r\n\t\t\t\t<text class=\"t1 flex1\">{{item.name}} {{item.ggname}}</text>\r\n\t\t\t\t<text class=\"t2 flex0\">￥{{item.sell_price}} ×{{item.num}} </text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"worker_order.status!=0\">\r\n\t\t\t<view class=\"box-title\">服务信息</view>\r\n\t\t\t<view class=\"item\" v-if=\"worker.realname\">\r\n\t\t\t\t<text class=\"t1\">服务人员</text>\r\n\t\t\t\t<text class=\"t2\"><text style=\"font-weight:bold\">{{worker.realname}}</text>({{worker.tel}})</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">接单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(worker_order.starttime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"worker_order.daodiantime\">\r\n\t\t\t\t<text class=\"t1\">{{yuyue_sign?'出发时间':'到店时间'}}</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(worker_order.daodiantime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"worker_order.sign_time\">\r\n\t\t\t\t<text class=\"t1\">开始时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(worker_order.sign_time)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"worker_order.endtime\">\r\n\t\t\t\t<text class=\"t1\">完成时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(worker_order.endtime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"worker_order.arrival_distance && need_location == 1\">\r\n\t\t\t\t<text class=\"t1\">到达距离</text>\r\n\t\t\t\t<text class=\"t2\">{{worker_order.arrival_distance}} 米</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">订单信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{orderinfo.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.createtime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.paytime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t<text class=\"t2\">{{orderinfo.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">商品金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">备注</text>\r\n\t\t\t\t<text class=\"t2 red\">{{orderinfo.message ? orderinfo.message : '无'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:120rpx\"></view>\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<view class=\"f1\" v-if=\"worker_order.status!=0 && worker.tel\" @tap=\"call\" :data-tel=\"worker.tel\"><image src=\"/static/peisong/tel1.png\" class=\"img\"/>联系服务人员</view>\r\n\t\t\t<view class=\"f2\" v-if=\"worker_order.status!=0\" @tap=\"call\" :data-tel=\"binfo.tel\"><image src=\"/static/peisong/tel2.png\" class=\"img\"/>联系商家</view>\r\n\t\t\t<view class=\"btn1\" @tap=\"goto\" :data-url=\"'commentps?id='+worker_order.id\" v-if=\"mid==orderinfo.mid && worker_order.worker_id>0 && worker_order.status==3\">评价服务人员</view>\r\n\t\t</view>\r\n\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tnodata:false,\r\n      express_com: '',\r\n      express_no: '',\r\n      datalist: [],\r\n\r\n      orderinfo: {},\r\n      prolist: [],\r\n      binfo: {},\r\n      worker: {},\r\n      worker_order: {},\r\n\t\t\tmid:'',\r\n\t\t\t\r\n\t\t\t// 新增字段\r\n\t\t\tservice_progress: [],\r\n\t\t\tarrival_info: {},\r\n\t\t\tcompletion_info: {},\r\n\t\t\tneed_location: 1,\r\n\t\t\tneed_arrival_photo: 1,\r\n\t\t\tneed_complete_photo: 1,\r\n\t\t\tyuyue_sign: false\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.express_no = that.opt.express_no;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYuyue/logistics', { express_no: that.express_no}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.orderinfo = res.orderinfo;\r\n\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\tthat.binfo = res.binfo;\r\n\t\t\t\tthat.worker = res.worker;\r\n\t\t\t\tthat.worker_order = res.worker_order;\r\n\t\t\t\tthat.set = res.set;\r\n\t\t\t\tthat.mid = res.mid;\r\n\t\t\t\t\r\n\t\t\t\t// 新增服务进度数据处理\r\n\t\t\t\tthat.service_progress = res.service_progress || [];\r\n\t\t\t\tthat.arrival_info = res.arrival_info || {};\r\n\t\t\t\tthat.completion_info = res.completion_info || {};\r\n\t\t\t\tthat.need_location = res.need_location || 0;\r\n\t\t\t\tthat.need_arrival_photo = res.need_arrival_photo || 0;\r\n\t\t\t\tthat.need_complete_photo = res.need_complete_photo || 0;\r\n\t\t\t\tthat.yuyue_sign = res.yuyue_sign || false;\r\n\t\t\t\t\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tconsole.log('物流数据加载完成', res);\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tcall: function(e) {\r\n\t\t\tvar tel = e.currentTarget.dataset.tel;\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: tel\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tloaded: function() {\r\n\t\t\tthis.isload = true;\r\n\t\t},\r\n\t\t\r\n\t\t// 地图导航功能\r\n\t\tdaohang: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tif (!that.binfo.latitude || !that.binfo.longitude) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '暂无位置信息',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tlatitude: parseFloat(that.binfo.latitude),\r\n\t\t\t\tlongitude: parseFloat(that.binfo.longitude),\r\n\t\t\t\tname: that.binfo.name,\r\n\t\t\t\taddress: that.binfo.address\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 图片预览功能\r\n\t\tpreviewImage: function(e) {\r\n\t\t\tvar src = e.currentTarget.dataset.src;\r\n\t\t\tif (!src) return;\r\n\t\t\t\r\n\t\t\tvar imgList = [];\r\n\t\t\t\r\n\t\t\t// 收集所有可预览的照片\r\n\t\t\tif (this.arrival_info && this.arrival_info.arrival_photo) {\r\n\t\t\t\timgList.push(this.arrival_info.arrival_photo);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.completion_info && this.completion_info.complete_photo) {\r\n\t\t\t\timgList.push(this.completion_info.complete_photo);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 从服务进度中收集照片\r\n\t\t\tif (this.service_progress && this.service_progress.length > 0) {\r\n\t\t\t\tthis.service_progress.forEach(function(item) {\r\n\t\t\t\t\tif (item.arrival_photo) {\r\n\t\t\t\t\t\timgList.push(item.arrival_photo);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (item.complete_photo) {\r\n\t\t\t\t\t\timgList.push(item.complete_photo);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果没有照片，使用当前照片\r\n\t\t\tif (imgList.length === 0) {\r\n\t\t\t\timgList.push(src);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.previewImage({\r\n\t\t\t\tcurrent: src,\r\n\t\t\t\turls: imgList\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 日期格式化\r\n\t\tdateFormat: function(timestamp) {\r\n\t\t\tif (!timestamp) return '';\r\n\t\t\tvar date = new Date(timestamp * 1000);\r\n\t\t\tvar year = date.getFullYear();\r\n\t\t\tvar month = date.getMonth() + 1;\r\n\t\t\tvar day = date.getDate();\r\n\t\t\tvar hour = date.getHours();\r\n\t\t\tvar minute = date.getMinutes();\r\n\t\t\t\r\n\t\t\tmonth = month < 10 ? '0' + month : month;\r\n\t\t\tday = day < 10 ? '0' + day : day;\r\n\t\t\thour = hour < 10 ? '0' + hour : hour;\r\n\t\t\tminute = minute < 10 ? '0' + minute : minute;\r\n\t\t\t\r\n\t\t\treturn year + '-' + month + '-' + day + ' ' + hour + ':' + minute;\r\n\t\t},\r\n\t\t\r\n\t\t// 页面跳转\r\n\t\tgoto: function(e) {\r\n\t\t\tvar url = e.currentTarget.dataset.url;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.expressinfo .head { width:100%;background: #fff; margin:20rpx 0;padding: 20rpx 20rpx;display:flex;align-items:center}\r\n.expressinfo .head .f1{ width:120rpx;height:120rpx;margin-right:20rpx}\r\n.expressinfo .head .f1 image{width:100%;height:100%}\r\n.expressinfo .head .f2{display:flex;flex-direction:column;flex:auto;font-size:30rpx;color:#999999}\r\n.expressinfo .head .f2 .t1{margin-bottom:8rpx}\r\n.expressinfo .content{ width: 100%;  background: #fff;display:flex;flex-direction:column;color: #979797;padding:20rpx 40rpx}\r\n.expressinfo .content .on{color: #23aa5e;}\r\n.expressinfo .content .item{display:flex;width: 96%;  margin: 0 2%;border-left: 1px #dadada solid;padding:10rpx 0}\r\n.expressinfo .content .item .f1{ width:40rpx;flex-shrink:0;position:relative}\r\n.expressinfo .content image{width: 30rpx; height: 30rpx; position: absolute; left: -16rpx; top: 22rpx;}\r\n/*.content .on image{ top:-1rpx}*/\r\n.expressinfo .content .item .f1 image{ width: 30rpx; height: 30rpx;}\r\n\r\n.expressinfo .content .item .f2{display:flex;flex-direction:column;flex:auto;}\r\n.expressinfo .content .item .f2 .t1{font-size: 30rpx;}\r\n.expressinfo .content .item .f2 .t1{font-size: 26rpx;}\r\n\r\n\r\n.map{width:100%;height:500rpx;overflow:hidden}\r\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n.ordertop .f1 .t2{font-size:24rpx}\r\n\r\n.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#222222}\r\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}\r\n.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}\r\n.order-box .head .f2{color:#FF6F30}\r\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\r\n\r\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}\r\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\r\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\r\n\r\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\r\n.order-box .content .f2{}\r\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\r\n\r\n.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}\r\n.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}\r\n.orderinfo .item .t1{width:200rpx;color:#161616}\r\n.orderinfo .item .t2{flex:1;text-align:right;color:#222222}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;align-items:center;height:100rpx;}\r\n.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}\r\n.bottom .f1 .img{width:44rpx;height:44rpx}\r\n.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}\r\n.bottom .f2 .img{width:44rpx;height:44rpx}\r\n.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}\r\n\r\n/* 服务进度时间轴样式 */\r\n.service-timeline {\r\n  margin: 20rpx 3%;\r\n  width: 94%;\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.timeline-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 30rpx;\r\n  color: #333;\r\n}\r\n\r\n.timeline-container {\r\n  position: relative;\r\n}\r\n\r\n.timeline-item {\r\n  display: flex;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n}\r\n\r\n.timeline-icon {\r\n  width: 80rpx;\r\n  position: relative;\r\n}\r\n\r\n.timeline-icon .icon-img {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n  z-index: 2;\r\n  position: relative;\r\n}\r\n\r\n.timeline-line {\r\n  position: absolute;\r\n  top: 40rpx;\r\n  left: 20rpx;\r\n  width: 2rpx;\r\n  height: calc(100% + 40rpx);\r\n  background: #e0e0e0;\r\n  z-index: 1;\r\n}\r\n\r\n.timeline-content {\r\n  flex: 1;\r\n  padding-left: 20rpx;\r\n}\r\n\r\n.timeline-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.timeline-title-text {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.timeline-desc {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.timeline-photo {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.timeline-photo .photo-img {\r\n  width: 100%;\r\n  height: 360rpx;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.photo-desc {\r\n  display: block;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.completed .timeline-icon .icon-img {\r\n  border: 2rpx solid #07c160;\r\n}\r\n\r\n.waiting .timeline-icon .icon-img {\r\n  border: 2rpx solid #ccc;\r\n}\r\n\r\n/* 技师位置信息样式 */\r\n.technician-location {\r\n  margin: 20rpx 3%;\r\n  width: 94%;\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.location-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n  color: #333;\r\n}\r\n\r\n.icon-location {\r\n  margin-right: 10rpx;\r\n  color: #07c160;\r\n}\r\n\r\n.location-content {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.distance-info, .arrival-time {\r\n  display: flex;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.label {\r\n  width: 160rpx;\r\n  color: #666;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.value {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.location-photo {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.location-photo .photo-img {\r\n  width: 100%;\r\n  height: 360rpx;\r\n  border-radius: 8rpx;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logistics.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115044709\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}