{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?70fd", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?23cf", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?1726", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?524b", "uni-app:///yuyue/cycle/orderList.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?5cad", "webpack:///D:/qianhouduankaifabao/tiantianshande/yuyue/cycle/orderList.vue?eca5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "nodata", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "uni", "scrollTop", "duration", "getdata", "that", "app", "toggle<PERSON><PERSON>e", "id", "setTimeout", "cancelOrder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,ywBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+ClxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MAEAC;QAAAhB;QAAAE;MAAA;QACAa;QACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA;QAAA;MAAA;MACA;MACAD;QACAA;QACAA;UAAAE;QAAA;UACAF;UACA;YACAA;YACAG;cACAJ;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAJ;QACAA;QACAA;UAAAE;QAAA;UACAF;UACA;YACAA;YACAG;cACAJ;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjKA;AAAA;AAAA;AAAA;AAA8kC,CAAgB,0jCAAG,EAAC,C;;;;;;;;;;;ACAlmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "yuyue/cycle/orderList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './yuyue/cycle/orderList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderList.vue?vue&type=template&id=7a490c23&\"\nvar renderjs\nimport script from \"./orderList.vue?vue&type=script&lang=js&\"\nexport * from \"./orderList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"yuyue/cycle/orderList.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=template&id=7a490c23&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['全部','进行中','已暂停','已完成','已取消']\" :itemst=\"['all','1','2','3','4']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t\t<view style=\"width:100%;height:100rpx\"></view>\n\t\t\n\t\t<view class=\"order-content\">\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'/yuyue/cycle/orderDetail?id=' + item.id\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<text class=\"flex1\">{{item.product_name}}</text>\n\t\t\t\t\t<text :class=\"'status-tag st' + item.status\">{{item.status_text}}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"content\">\n\t\t\t\t\t<view class=\"img-wrapper\" @tap.stop=\"goto\" :data-url=\"'/yuyue/cycle/productDetail?id=' + item.product_id\">\n\t\t\t\t\t\t<image :src=\"item.product_pic\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t<view class=\"f1\">总期数：{{item.total_period}}期</view>\n\t\t\t\t\t\t<view class=\"f2\">已完成：{{item.served_period}}期</view>\n\t\t\t\t\t\t<view class=\"f3\">开始于：{{item.start_date}}</view>\n\t\t\t\t\t\t<view class=\"f3\">下次服务：{{item.next_service_date || '暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"op\">\n\t\t\t\t\t<view class=\"btn\" @tap.stop=\"goto\" :data-url=\"'/yuyue/cycle/orderDetail?id=' + item.id\">查看详情</view>\n\t\t\t\t\t<!-- 根据状态显示操作按钮 -->\n\t\t\t\t\t<block v-if=\"item.status == 1 || item.status == 2\"> <!-- 进行中或已暂停 -->\n\t\t\t\t\t\t<view class=\"btn\" :class=\"item.status == 1 ? 'btn-pause' : 'btn-resume'\" @tap.stop=\"togglePause\" :data-id=\"item.id\">{{item.status == 1 ? '暂停服务' : '恢复服务'}}</view>\n\t\t\t\t\t\t<view class=\"btn btn-cancel\" @tap.stop=\"cancelOrder\" :data-id=\"item.id\">取消服务</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t<nodata v-if=\"nodata\"></nodata>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t\topt:{},\n\t\t\t\tloading:false,\n\t\t\t\tisload: false,\n\t\t\t\tmenuindex:-1,\n\t\t\t\tst: 'all', // 默认显示全部状态 all, 1(进行中), 2(暂停), 3(完成), 4(取消)\n\t\t\t\tdatalist: [],\n\t\t\t\tpagenum: 1,\n\t\t\t\tnomore: false,\n\t\t\t\tnodata: false,\n\t\t};\n\t},\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || 'all';\n\t\tthis.getdata();\n\t},\n\tonShow: function() {\n\t\t// 每次进入页面都刷新数据\n\t\tthis.getdata();\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\tonReachBottom: function () {\n\t\tif (!this.nodata && !this.nomore) {\n\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\tthis.getdata(true);\n\t\t}\n\t},\n\tmethods: {\n\t\tchangetab: function (st) {\n\t\t\tthis.st = st;\n\t\t\tuni.pageScrollTo({\n\t\t\t\tscrollTop: 0,\n\t\t\t\tduration: 0\n\t\t\t});\n\t\t\tthis.getdata();\n\t\t},\n\t\tgetdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n\t\t\tvar that = this;\n\t\t\tvar pagenum = that.pagenum;\n\t\t\tvar st = that.st;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\tapp.post('ApiPeriodicService/orderList', {st: st, pagenum: pagenum}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.isload = true;\n\t\t\t\tvar data = res.data;\n\t\t\t\tif (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (data.length == 0) {\n\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar datalist = that.datalist;\n\t\t\t\t\t\tvar newdata = datalist.concat(data);\n\t\t\t\t\t\tthat.datalist = newdata;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\ttogglePause: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id;\n\t\t\tvar order = that.datalist.find(item => item.id == orderid);\n\t\t\tvar actionText = order.status == 1 ? '暂停' : '恢复';\n\t\t\tapp.confirm('确定要'+actionText+'该服务吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiPeriodicService/togglePauseOrder', {id: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status == 1){\n\t\t\t\t\t\tapp.success(actionText+'成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tcancelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要取消该周期服务吗? 未完成的服务将一并取消。', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiPeriodicService/cancelOrder', {id: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status == 1){\n\t\t\t\t\t\tapp.success('取消成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n<style>\n.container{ width:100%; background-color: #f8f8f8; min-height: 100vh; }\n\n.order-content{ display: flex; flex-direction: column; padding-bottom: 30rpx; }\n.order-box{ \n\twidth: 94%; \n\tmargin: 20rpx 3%; \n\tpadding: 0; \n\tbackground: #fff; \n\tborder-radius: 16rpx; \n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n\toverflow: hidden;\n}\n\n.order-box .head{ \n\tdisplay: flex; \n\twidth: 100%; \n\tpadding: 24rpx 30rpx;\n\tborder-bottom: 1px #f4f4f4 solid; \n\theight: 80rpx; \n\tline-height: 80rpx; \n\toverflow: hidden; \n\talign-items: center;\n}\n\n.order-box .head .flex1 { \n\tcolor: #333; \n\tfont-weight: bold; \n\tfont-size: 32rpx;\n\toverflow: hidden; \n\twhite-space: nowrap; \n\ttext-overflow: ellipsis; \n\tflex: 1;\n}\n\n/* 状态标签样式 */\n.status-tag {\n\tpadding: 6rpx 20rpx;\n\tborder-radius: 30rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n\ttext-align: center;\n}\n.st0 { background-color: #e6f7ff; color: #1890ff; } /* 待激活 */\n.st1 { background-color: #fff7e6; color: #FFB600; } /* 进行中 */\n.st2 { background-color: #fff2e8; color: #FF8D00; } /* 已暂停 */\n.st3 { background-color: #e6fffb; color: #06A051; } /* 已完成 */\n.st4 { background-color: #f5f5f5; color: #999999; } /* 已取消 */\n\n.order-box .content{\n\tdisplay: flex;\n\twidth: 100%; \n\tpadding: 24rpx 30rpx;\n\tposition: relative;\n}\n\n.order-box .content .img-wrapper {\n\twidth: 140rpx;\n\theight: 140rpx;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbackground-color: #f7f7f7;\n}\n\n.order-box .content image{ \n\twidth: 140rpx; \n\theight: 140rpx; \n}\n\n.order-box .content .detail{\n\tdisplay: flex;\n\tflex-direction: column;\n\tmargin-left: 24rpx;\n\tflex: 1; \n\tfont-size: 28rpx; \n\tcolor: #666; \n\tline-height: 50rpx;\n}\n\n.order-box .content .detail .f1{\n\tcolor: #333; \n\tfont-weight: bold;\n}\n\n.order-box .content .detail .f3{\n\tfont-size: 26rpx; \n\tcolor: #999;\n}\n\n.order-box .op{ \n\tdisplay: flex;\n\tjustify-content: flex-end;\n\talign-items: center;\n\twidth: 100%; \n\tpadding: 20rpx 30rpx; \n\tborder-top: 1px #f4f4f4 solid; \n}\n\n.btn {\n\tmargin-left: 20rpx;\n\theight: 64rpx;\n\tline-height: 64rpx;\n\tpadding: 0 30rpx;\n\tcolor: #333;\n\tbackground: #fff;\n\tborder: 1px solid #e6e6e6;\n\tborder-radius: 32rpx;\n\ttext-align: center; \n\tfont-size: 28rpx;\n}\n\n.btn-pause {\n\tcolor: #ff9800;\n\tborder-color: #ff9800;\n}\n\n.btn-resume {\n\tcolor: #2196f3;\n\tborder-color: #2196f3;\n}\n\n.btn-cancel {\n\tcolor: #f44336;\n\tborder-color: #f44336;\n}\n</style> ", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115041160\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}