{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?2ae3", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?6642", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?896a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?2a5f", "uni-app:///pagesExb/form/formdetail.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?7db8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExb/form/formdetail.vue?47b5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "detail", "formcontent", "againname", "form", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "op", "todel", "setTimeout", "download", "uni", "url", "success", "filePath", "showMenu", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwFnxB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;;MAGAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAI;MACA;MACAH;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAI;YACAJ;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;MAMAC;QACAC;QACAC;UACA;UACA;YACAF;cACAG;cACAC;cACAF;gBACAG;cACA;YACA;UACA;QACA;MACA;IAEA;EACA;;;;;;;;;;;;;;;AClKA;AAAA;AAAA;AAAA;AAA+kC,CAAgB,2jCAAG,EAAC,C;;;;;;;;;;;ACAnmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExb/form/formdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExb/form/formdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./formdetail.vue?vue&type=template&id=9fa56d2c&\"\nvar renderjs\nimport script from \"./formdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./formdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./formdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExb/form/formdetail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./formdetail.vue?vue&type=template&id=9fa56d2c&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload &&\n    _vm.opt.op != \"view\" &&\n    _vm.detail.payorderid &&\n    _vm.detail.paystatus == 0\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./formdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./formdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" :style=\"form.form_query==1 && form.form_query_bgcolor ? 'min-height:100vh;background-color:'+form.form_query_bgcolor : ''\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"orderinfo\" :style=\"form.form_query==1 && form.form_query_bgcolor ? 'background-color:'+form.form_query_bgcolor+';color:'+form.form_query_txtcolor+'!important' : ''\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">标题</text>\n\t\t\t\t<text class=\"t2\">{{detail.title}}</text>\n\t\t\t</view>\n\t\t\t<view v-for=\"(item, index) in formcontent\" :key=\"index\" class=\"item\">\n\t\t\t\t<text class=\"t1\" :class=\"item.key=='separate'?'title':''\">{{item.val1}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"item.key!='upload' && item.key!='upload_file'\">{{detail['form'+index]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload'\"><image :src=\"detail['form'+index]\" style=\"width:50px\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"detail['form'+index]\"></image></view>\r\n                <!-- #ifdef !H5 && !MP-WEIXIN -->\r\n                <view class=\"t2\" v-if=\"item.key=='upload_file'\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n                    {{detail['form'+index]}}\r\n                </view>\r\n                <!-- #endif -->\r\n                <!-- #ifdef H5 || MP-WEIXIN -->\r\n                <view class=\"t2\" v-if=\"item.key=='upload_file'\"  @tap=\"download\" :data-file=\"detail['form'+index]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n                    {{detail['form'+index]}}\r\n                </view>\r\n                <!-- #endif -->\n\t\t\t</view>\r\n\t\t\t<view class=\"feebox\" v-if=\"detail.is_other_fee==1\">\r\n\t\t\t\t<text class=\"title\">费用明细</text>\r\n\t\t\t\t<view class=\"feelist\">\r\n\t\t\t\t\t<view class=\"feeitem\" v-for=\"(item,index) in detail.fee_items\" :key=\"index\">\r\n\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"price\">￥{{item.money}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">提交时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">审核状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && (!detail.payorderid||detail.paystatus==1)\" style=\"color:#88e\">待确认</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && detail.payorderid && detail.paystatus==0\" style=\"color:red\">待支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\" style=\"color:green\">已确认</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\" style=\"color:red\">已驳回</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==2\">\n\t\t\t\t<text class=\"t1\">驳回原因</text>\n\t\t\t\t<text class=\"t2\" style=\"color:red\">{{detail.reason}}</text>\n\t\t\t</view>\n\t\t\t<block v-if=\"detail.payorderid\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款金额</text>\n\t\t\t\t<text class=\"t2\" style=\"font-size:32rpx;color:#e94745\">￥{{detail.money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype || ''}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">付款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==0\" style=\"color:green\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==1\" style=\"color:red\">已退款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==0\" style=\"color:red\">未付款</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paystatus>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">付款时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\n\t\t\n\t\t<view style=\"width:100%;height:160rpx\"></view>\n\n\t\t<view class=\"bottom notabbarbot\" v-if=\"opt.op != 'view'\">\n\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除</view>\r\n            <view class=\"btn2\" @tap=\"goto\" :data-url=\"detail.fromurl+'&fromrecord='+detail.id+'&type=edit'\" v-if=\"detail.fromurl && detail.edit_status\">{{detail.edit_name?detail.edit_name:'编辑'}}</view>\n\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"detail.fromurl+'&fromrecord='+detail.id\" v-if=\"detail.fromurl\">{{againname}}</view>\n\t\t\t<block v-if=\"detail.payorderid && detail.paystatus==0\">\n\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pages/pay/pay?id=' + detail.payorderid\">去付款</view>\n\t\t\t</block>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tdetail:{},\n\t\t\tformcontent:[],\n\t\t\tagainname:'',\n\t\t\tform:{},\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiMy/formdetail', {id: that.opt.id,op:that.opt.op}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.form = res.form;\n\t\t\t\tthat.formcontent = res.formcontent;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.againname = res.againname;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\ttodel:function(e){\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.confirm('确定要删除吗?',function(){\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiMy/formdelete', {id: id}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n\t\t\t})\n\t\t},\r\n        download:function(e){\r\n            var that = this;\r\n            var file = e.currentTarget.dataset.file;\r\n            // #ifdef H5\r\n                window.location.href= file;\r\n            // #endif\r\n            \r\n            // #ifdef MP-WEIXIN\r\n            uni.downloadFile({\r\n            \turl: file, \r\n            \tsuccess: (res) => {\r\n                    var filePath = res.tempFilePath;\r\n            \t\tif (res.statusCode === 200) {\r\n            \t\t\tuni.openDocument({\r\n                          filePath: filePath,\r\n                          showMenu: true,\r\n                          success: function (res) {\r\n                            console.log('打开文档成功');\r\n                          }\r\n                        });\r\n            \t\t}\r\n            \t}\r\n            });\r\n            // #endif\r\n        },\n  }\n};\r\n</script>\r\n<style>\r\n.container{padding-top:10rpx}\r\n.orderinfo{ width:100%;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t1.title{font-size: 36rpx;font-weight: 600;line-height: 80rpx;}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;min-width:160rpx;padding: 0 20rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\r\n.feebox{border-bottom:1px dashed #ededed;padding: 20rpx 0;}\r\n.feelist{line-height: 50rpx;font-size: 24rpx;}\r\n.feeitem{display: flex;align-items: center;justify-content: space-between;}\r\n.feeitem .price{color: #e9393b;}\r\n.feebox .title{ \r\n\tfont-size: 28rpx;\r\n\tcolor: #9c9c9c;\r\n\tborder-bottom: 2rpx solid #d2d1d1;\r\n\tpadding-bottom: 4rpx;\r\n\tdisplay: inline-block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115024385\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}