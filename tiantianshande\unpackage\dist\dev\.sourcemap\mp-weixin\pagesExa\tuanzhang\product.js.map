{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?5d7f", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?1453", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?cfc9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?3056", "uni-app:///pagesExa/tuanzhang/product.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?6e45", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesExa/tuanzhang/product.vue?8ca4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "BuyDialog", "data", "priceIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opt", "loading", "isload", "menuindex", "textset", "onLoad", "console", "onShow", "uni", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "getSweepstakesStatus", "that", "pay_type", "app", "getSweepstakesList", "res", "length", "goEdit", "url", "savePhoto", "success", "filePath", "fail", "icon", "showLinkChange", "getdata", "id", "bid", "desc", "setTimeout", "view0", "size", "rect", "scrollOffset", "view1", "view2", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "addfavorite", "proid", "type", "shareClick", "handleClickMask", "showPoster", "shareScheme", "schemeDialogClose", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "changetoptab", "scroll", "sharemp", "shareapp", "itemList", "scene", "sharedata", "globalData", "sharelink", "showsubqrcode", "closesubqrcode", "addcart", "showgg1Dialog", "closegg1Dialog", "showgg2Dialog", "closegg2Dialog", "onClickGame", "keyName", "item", "path", "inputLuckyPrice", "value", "e", "getProbability", "onCloseLucky", "pickerChangePrice", "onLuckyNow", "hidePriceLink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtcA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,uwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2qBhxB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eACA;EAEAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,wDACA,oDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,2DACA,4DACA,sDACA,0DACA,0DACA,2DACA,0DACA,sDACA,4DACA,4DACA,4DACA,4DACA,sDACA,0DACA,yDACA,uDACA,wDACA,uDA+BA,8DACA;EAEA;EACAC;IACA;IACA;IACA;IACA;MACA;MACAC;IACA;EACA;;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAF;MACAC;IACA;IACA;IACAN;IACAA;IACA;MACAK;MACAG;MACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;MACAC;MACA;QACAC;MACA;;MACAC;QACA;UACAA;UACA;QACA;QACAF;QACAA;MACA;IACA;IACA;AACA;AACA;IACAG;MACA;MACAH;MACAE;QAAA;UAAA;YAAA;cAAA;gBAAA;kBACAF;kBAAA,MACAI;oBAAA;oBAAA;kBAAA;kBACAF;kBAAA;gBAAA;kBAGAF;kBACA;oBACAA;sBAAA;oBAAA;oBACAA;oBACAA;oBACAA;sBACAK;oBACA;sBAAA;oBAAA;oBACAL;kBACA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CACA;QAAA;UAAA;QAAA;MAAA;IACA;IAEAM;MAEAlB;QACAmB;MACA;IACA;IAEAC;MACA;MACA;MACApB;QACAmB;QACAE;UACA;YACA;YACArB;cACAsB;cACAD;gBACArB;kBACAG;gBACA;cACA;cACAoB;gBACAvB;kBACAG;kBACAqB;gBACA;cACA;YACA;UACA;YACAxB;cACAG;cACAqB;YACA;UACA;QACA;QACAD;UACAvB;YACAG;YACAqB;UACA;QACA;MACA;IAEA;IAEAC;MACA;IACA;IACAC;MAEA;MACA;MACA;MACAd;MACAE;QACAa;QACAC;MACA;QAEA9B;QAEAc;QACA;UACAE;UACA;QACA;QACAF;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAZ;UACAG;QACA;QAEAS;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;UACAT;UACAC;UACAyB;QACA;QAEAC;UACA;UACAC;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;YACApC;YACAc;UACA;UACA;UACAuB;YACAH;YAAA;YACAC;YAAA;YACAC;UACA;YACApC;YACAc;UACA;UACA;UACAwB;YACAJ;YAAA;YACAC;YAAA;YACAC;UACA;YACApC;YACAc;UACA;QACA;MAEA;IACA;IACAyB;MACA;MACAzB;IACA;IACA0B;MACA;MACAtC;IACA;IACAuC;MACA;MACAvC;IACA;IACAwC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA3B;QACA4B;QACAC;MACA;QACA;UACA/B;QACA;QACAE;MACA;IACA;IACA8B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAlC;MACAA;MACAE;MACAA;QACA4B;MACA;QACA5B;QACA;UACAA;QACA;UACAF;QACA;MACA;IACA;IAEAmC;MACA;MACAjC;MACAA;QACA4B;MACA;QACA5B;QACA;UACAA;QACA;UACAF;UACAA;QACA;MACA;IACA;IAEAoC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAC;MACA;MACA;MACA;MACA;MACA1D;IACA;IACA2D;MACA;MACA;MACA;MACA;QACA7C;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACAd;MACAA;MACAA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA4D;MACA5C;MACA;IACA;IACA6C;MACA;MACA/C;MACAZ;QACA4D;QACAvC;UACA;YACA;YACA;cACAwC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACAA;YACAA,wEACA,8EACAC;YACAD;YACA;YACA;cACA;gBACA;kBACAA;kBACAA;kBACAA;kBACA;oBACA;oBACA;sBACAE,kDACAD;oBACA;oBACA;sBACAC,2DACA;oBACA;oBACAF;kBACA;gBACA;cACA;YACA;YACA9D;UACA;QACA;MACA;IACA;IACAiE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACArE;MACA;IACA;IACAsE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA,IACAC,UAEAC,KAFAD;QACAE,OACAD,KADAC;MAEA;MACA;QACA;UACA;UACA;UACA;QACA;UACA7D;UACA;MAAA;IAEA;IACA;AACA;AACA;IACA8D;MACA,IACAC,QACAC,SADAD;MAEA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACApE,kCACA,6FACA;IAEA;IACAqE;MACArE;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACzwCA;AAAA;AAAA;AAAA;AAA4kC,CAAgB,wjCAAG,EAAC,C;;;;;;;;;;;ACAhmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExa/tuanzhang/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExa/tuanzhang/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=5293f2f2&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExa/tuanzhang/product.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=template&id=5293f2f2&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    dpProductItem: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product-item/dp-product-item\" */ \"@/components/dp-product-item/dp-product-item.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var m2 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    _vm.toptabbar_index == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.t(\"color1\")\n      : null\n  var g1 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1\n      ? _vm.tjdatalist.length\n      : null\n  var m8 =\n    _vm.isload &&\n    _vm.showtoptabbar == 1 &&\n    _vm.toptabbar_show == 1 &&\n    g1 > 0 &&\n    _vm.toptabbar_index == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m9 =\n    _vm.isload && _vm.showtoptabbar == 1 && _vm.toptabbar_show == 1 && g1 > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g2 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g3 = _vm.isload\n    ? _vm.showtoptabbar == 1 && _vm.couponlist.length > 0\n    : null\n  var l0 =\n    _vm.isload && g3\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m10 = _vm.t(\"color1rgb\")\n          var m11 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 = _vm.isload && _vm.product.hide_price == 1 ? _vm.t(\"color1\") : null\n  var m13 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.is_newcustom == 1 &&\n    _vm.product.is_member_yh == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    !(_vm.product.is_newcustom == 1 && _vm.product.is_member_yh == 0)\n      ? _vm.t(\"color1\")\n      : null\n  var g4 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    !(_vm.product.is_newcustom == 1 && _vm.product.is_member_yh == 0)\n      ? (_vm.product.guigelist &&\n          Object.values(_vm.product.guigelist)[0] &&\n          Object.values(_vm.product.guigelist)[0].sell_price) ||\n        _vm.product.min_price\n      : null\n  var g5 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    (_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.market_price * 1 > _vm.product.sell_price * 1\n      ? (_vm.product.guigelist &&\n          Object.values(_vm.product.guigelist)[0] &&\n          Object.values(_vm.product.guigelist)[0].market_price) ||\n        _vm.product.market_price\n      : null\n  var m15 =\n    _vm.isload &&\n    !(_vm.product.hide_price == 1) &&\n    !(_vm.product.price_type != 1 || _vm.product.min_price > 0) &&\n    _vm.product.xunjia_text\n      ? _vm.t(\"color1\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m18 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    _vm.product.commission > 0 &&\n    _vm.showjiesheng == 0\n      ? _vm.t(\"佣金\")\n      : null\n  var m19 =\n    _vm.isload && _vm.product.buyselect_commission > 0 ? _vm.t(\"佣金\") : null\n  var m20 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m21 = _vm.isload && _vm.product.xiaofeizhi2 > 0 ? _vm.t(\"消费值\") : null\n  var m22 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m23 = _vm.isload && _vm.product.chuangyezhi > 0 ? _vm.t(\"创业值\") : null\n  var m24 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m25 = _vm.isload && _vm.product.givescore > 0 ? _vm.t(\"积分\") : null\n  var m26 =\n    _vm.isload && _vm.product.score_deduction_message ? _vm.t(\"积分\") : null\n  var g6 = _vm.isload\n    ? _vm.cuxiaolist.length > 0 ||\n      _vm.couponlist.length > 0 ||\n      _vm.fuwulist.length > 0 ||\n      _vm.product.discount_tips != \"\"\n    : null\n  var g7 = _vm.isload && g6 ? _vm.fuwulist.length : null\n  var g8 = _vm.isload && g6 ? _vm.cuxiaolist.length : null\n  var l1 =\n    _vm.isload && g6 && g8 > 0\n      ? _vm.__map(_vm.cuxiaolist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m27 = _vm.t(\"color1rgb\")\n          var m28 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m27: m27,\n            m28: m28,\n          }\n        })\n      : null\n  var g9 =\n    _vm.isload && g6\n      ? _vm.couponlist.length > 0 && _vm.showtoptabbar == 0\n      : null\n  var l2 =\n    _vm.isload && g6 && g9\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m29 = _vm.t(\"color1rgb\")\n          var m30 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m29: m29,\n            m30: m30,\n          }\n        })\n      : null\n  var m31 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var g10 =\n    _vm.isload && _vm.shopset.comment == 1 && _vm.commentcount > 0\n      ? _vm.commentlist.length\n      : null\n  var m32 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m33 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m34 = _vm.isload ? _vm.isEmpty(_vm.product.paramdata) : null\n  var g11 = _vm.isload ? _vm.tjdatalist.length : null\n  var m35 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.cartnum > 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m36 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m37 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    _vm.showjiesheng == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m38 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    _vm.product.hide_price == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m39 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.hide_price == 1) &&\n    _vm.product.price_type == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m40 =\n    _vm.isload &&\n    _vm.product.status == 1 &&\n    !_vm.showcuxiaodialog &&\n    !(_vm.showjiesheng == 1) &&\n    !(_vm.product.hide_price == 1) &&\n    !(_vm.product.price_type == 1)\n      ? _vm.t(\"color1\")\n      : null\n  var m41 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m42 =\n    _vm.isload && _vm.sharetypevisible && !(m41 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m43 =\n    _vm.isload && _vm.sharetypevisible && !(m41 == \"app\") && !(m42 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m44 =\n    _vm.isload && _vm.sharetypevisible\n      ? _vm.getplatform() == \"wx\" && _vm.xcx_scheme\n      : null\n  var m45 = _vm.isload && _vm.showposter ? _vm.t(\"color1\") : null\n  var m46 = _vm.isload && _vm.showposter ? _vm.t(\"color1rgb\") : null\n  var m47 = _vm.isload && _vm.showposter ? _vm.getplatform() : null\n  var m48 =\n    _vm.isload && _vm.showposter && m47 == \"app\" ? _vm.t(\"color1\") : null\n  var m49 =\n    _vm.isload && _vm.showposter && m47 == \"app\" ? _vm.t(\"color1rgb\") : null\n  var m50 =\n    _vm.isload && _vm.showposter && !(m47 == \"app\") ? _vm.getplatform() : null\n  var m51 =\n    _vm.isload && _vm.showposter && !(m47 == \"app\") && m50 == \"mp\"\n      ? _vm.t(\"color1\")\n      : null\n  var m52 =\n    _vm.isload && _vm.showposter && !(m47 == \"app\") && m50 == \"mp\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m53 =\n    _vm.isload && _vm.showposter && !(m47 == \"app\") && !(m50 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m54 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m47 == \"app\") &&\n    !(m50 == \"mp\") &&\n    m53 == \"h5\"\n      ? _vm.t(\"color1\")\n      : null\n  var m55 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m47 == \"app\") &&\n    !(m50 == \"mp\") &&\n    m53 == \"h5\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m56 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m47 == \"app\") &&\n    !(m50 == \"mp\") &&\n    !(m53 == \"h5\")\n      ? _vm.t(\"color1\")\n      : null\n  var m57 =\n    _vm.isload &&\n    _vm.showposter &&\n    !(m47 == \"app\") &&\n    !(m50 == \"mp\") &&\n    !(m53 == \"h5\")\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m58 = _vm.isload && _vm.showScheme ? _vm.t(\"color1\") : null\n  var m59 = _vm.isload && _vm.showScheme ? _vm.t(\"color1rgb\") : null\n  var m60 =\n    _vm.isload && _vm.showLinkStatus && _vm.business.tel\n      ? _vm.t(\"color1\")\n      : null\n  var m61 = _vm.getProbability()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        g1: g1,\n        m8: m8,\n        m9: m9,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        g4: g4,\n        g5: g5,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        l1: l1,\n        g9: g9,\n        l2: l2,\n        m31: m31,\n        g10: g10,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        g11: g11,\n        m35: m35,\n        m36: m36,\n        m37: m37,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n        m44: m44,\n        m45: m45,\n        m46: m46,\n        m47: m47,\n        m48: m48,\n        m49: m49,\n        m50: m50,\n        m51: m51,\n        m52: m52,\n        m53: m53,\n        m54: m54,\n        m55: m55,\n        m56: m56,\n        m57: m57,\n        m58: m58,\n        m59: m59,\n        m60: m60,\n        m61: m61,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\r\n\t\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t\t<view class=\"headimg\">\r\n\t\t\t\t\t\t<image :src=\"sysset.logo\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\r\n\t\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\" />\r\n\t\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-popup>\r\n\t\t\t</block>\r\n\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\"\r\n\t\t\t\tv-if=\"bboglist.length>0\">\r\n\t\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\"\r\n\t\t\t\t\t:vertical=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\"\r\n\t\t\t\t\t\t:data-url=\"'/shopPackage/shop/product?id=' + item.proid\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t<image :src=\"item.headimg\"\r\n\t\t\t\t\t\t\tstyle=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\" />\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tstyle=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">\r\n\t\t\t\t\t\t\t{{item.nickname}} {{item.showtime}}购买了该商品\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"toptabbar_tab\" v-if=\"showtoptabbar==1 && toptabbar_show==1\">\r\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==0?'on':''\"\r\n\t\t\t\t\t:style=\"{color:toptabbar_index==0?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"0\">商品<view\r\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==1?'on':''\"\r\n\t\t\t\t\t:style=\"{color:toptabbar_index==1?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"1\">评价<view\r\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" :class=\"toptabbar_index==2?'on':''\"\r\n\t\t\t\t\t:style=\"{color:toptabbar_index==2?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"2\">详情<view\r\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" v-if=\"tjdatalist.length > 0\" :class=\"toptabbar_index==3?'on':''\"\r\n\t\t\t\t\t:style=\"{color:toptabbar_index==3?t('color1'):'#333'}\" @tap=\"changetoptab\" data-index=\"3\">推荐<view\r\n\t\t\t\t\t\tclass=\"after\" :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<scroll-view @scroll=\"scroll\" :scrollIntoView=\"scrollToViewId\" :scrollTop=\"scrollTop\" :scroll-y=\"true\"\r\n\t\t\t\tstyle=\"height:100%;overflow:scroll\">\r\n\t\t\t\t<view id=\"scroll_view_tab0\">\r\n\t\t\t\t\t<view class=\"game\" v-if=\"isGameOpen\">\r\n\t\t\t\t\t\t<view class=\"game-grid\" v-for=\"item in gameList\" :key=\"item.keyName\" @click=\"onClickGame(item)\">\r\n\t\t\t\t\t\t\t<image class=\"game-img\" :src=\"item.imgUrl\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"swiper-container\" v-if=\"isplay==0\">\r\n\r\n\t\t\t\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\"\r\n\t\t\t\t\t\t\t@change=\"swiperChange\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"swiper-item-view\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"item\" mode=\"widthFix\" @tap=\"previewImage\"\r\n\t\t\t\t\t\t\t\t\t\t\t:data-url=\"item\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t<view class=\"imageCount\" v-if=\"product.diypics\" @tap=\"goto\"\r\n\t\t\t\t\t\t\t:data-url=\"'/pagesExt/shop/diylight?id='+product.id\" style=\"bottom: 92rpx; width: 140rpx;\">\r\n\t\t\t\t\t\t\t自助试灯</view>\r\n\t\t\t\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t\t\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/video.png\" />\r\n\t\t\t\t\t\t\t<view class=\"txt\">{{product.video_duration}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"videobox\" v-if=\"isplay==1\">\r\n\t\t\t\t\t\t<video autoplay=\"true\" class=\"video\" id=\"video\" :src=\"product.video\"></video>\r\n\t\t\t\t\t\t<view class=\"parsevideo\" @tap=\"parsevideo\">退出播放</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"showtoptabbar==1 && couponlist.length>0\"\r\n\t\t\t\t\t\tstyle=\"background:#fff;padding:0 16rpx\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\"\r\n\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0\"\r\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao1\">\r\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao1\" style=\"width:100%;height:auto\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tv-if=\"shopset.detail_guangao1\" @tap=\"showgg1Dialog\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<uni-popup id=\"gg1Dialog\" ref=\"gg1Dialog\" type=\"dialog\"\r\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao1 && shopset.detail_guangao1_t\">\r\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao1_t\" @tap=\"previewImage\"\r\n\t\t\t\t\t\t\t:data-url=\"shopset.detail_guangao1_t\" class=\"img\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width:600rpx;height:auto;border-radius:10rpx;\" />\r\n\t\t\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg1Dialog\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-popup>\r\n\r\n\t\t\t\t\t<view class=\"header\">\r\n\t\t\t\t\t\t<block v-if=\"product.hide_price == 1\">\r\n\t\t\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">{{product.hide_price_detail_text}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else-if=\"product.price_type != 1 || product.min_price > 0\">\r\n\t\t\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"product.is_newcustom == 1 && product.is_member_yh == 0\" class=\"f1\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"product.is_newcustom == 1\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"font-size:36rpx\">￥</text>{{product.yh_price}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">￥</text>{{(product.guigelist && Object.values(product.guigelist)[0] && Object.values(product.guigelist)[0].sell_price) || product.min_price}}<text\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">\r\n\t\t\t\t\t\t\t\t\t\t￥{{(product.guigelist && Object.values(product.guigelist)[0] && Object.values(product.guigelist)[0].market_price) || product.market_price}}<text\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"product.max_price!=product.min_price\">起</text></view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"product.huang_dx.types\" class=\"huang_bz\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"huang_i\"></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"huang_nums\">可用红包：{{product.huang_dx.nums}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"product.yuanbao\"\r\n\t\t\t\t\t\t\t\tstyle=\"margin: 0;font-size: 26rpx;margin-bottom: 10rpx;\">\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">元宝价：{{product.yuanbao}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view v-if=\"product.xunjia_text\" class=\"price_share\">\r\n\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:36rpx\">询价</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"price_share\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"share\" @tap=\"shareClick\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/share.png\" /><text class=\"txt\">分享</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\r\n\t\t\t\t\t\t<view class=\"sales_stock\"\r\n\t\t\t\t\t\t\tv-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">\r\n\t\t\t\t\t\t\t<!-- <view class=\"f1\">奖励消费值：{{product.xiaofeizhi2}} </view> -->\r\n\t\t\t\t\t\t\t<view class=\"f1\">奖励创业值：{{product.chuangyezhi}} </view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">推广佣金：{{product.commission}}元</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"sales_stock\" v-if=\"shopset.hide_sales != 1 || shopset.hide_stock != 1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"shopset.hide_sales != 1\">销量：{{product.sales}} </view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"shopset.hide_stock != 1\">库存：{{product.stock}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"\r\n\t\t\t\t\t\t\tv-if=\"shopset.showcommission==1 && product.commission > 0 && showjiesheng==0\">\r\n\t\t\t\t\t\t\t分享好友购买预计可得{{t('佣金')}}：<text\r\n\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"margin:20rpx 0;color:#333;font-size:22rpx\" v-if=\"product.balance_price > 0\">\r\n\t\t\t\t\t\t\t首付款金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\r\n\t\t\t\t\t\t<view style=\"margin:20rpx 0;color:#666;font-size:22rpx\" v-if=\"product.buyselect_commission > 0\">\r\n\t\t\t\t\t\t\t下单被选奖励预计可得{{t('佣金')}}：<text\r\n\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px\">{{product.buyselect_commission}}</text>元</view>\r\n\r\n\t\t\t\t\t\t<view class=\"upsavemoney\"\r\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}\"\r\n\t\t\t\t\t\t\tv-if=\"product.upsavemoney > 0\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">升级到 {{product.nextlevelname}} 预计可节省<text\r\n\t\t\t\t\t\t\t\t\tstyle=\"font-weight:bold;padding:0 2px;color:#ca4312\">{{product.upsavemoney}}</text>元\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tstyle=\"margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312\"\r\n\t\t\t\t\t\t\t\t@tap=\"goto\" data-url=\"/pagesExa/my/levelup\">立即升级\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrowright2.png\" style=\"width:30rpx;height:30rpx\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\r\n\t\t\t\t\t\t<view class=\"f0\">规格</view>\r\n\t\t\t\t\t\t<view class=\"f1 flex1\">\r\n\t\t\t\t\t\t\t<block v-if=\"product.price_type == 1\">查看规格</block>\r\n\t\t\t\t\t\t\t<block v-else>请选择商品规格及数量</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image class=\"f2\" src=\"/static/img/arrowright.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.xiaofeizhi2 > 0\">\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('消费值')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('消费值')}}{{product.xiaofeizhi2}} 个</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.chuangyezhi > 0\">\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('创业值')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx;\">购买可得{{t('创业值')}}{{product.chuangyezhi}} 个</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.givescore > 0\">\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">送{{t('积分')}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">购买可得{{t('积分')}}{{product.givescore}}个</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cuxiaodiv\" v-if=\"product.score_deduction_message\">\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">抵扣{{ t('积分') }}</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"font-size:26rpx\">{{ product.score_deduction_message }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t<view class=\"cuxiaodiv\"\r\n\t\t\t\t\t\tv-if=\"cuxiaolist.length>0 || couponlist.length>0 || fuwulist.length>0 || product.discount_tips!=''\">\r\n\t\t\t\t\t\t<view class=\"fuwupoint cuxiaoitem\" v-if=\"fuwulist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">服务</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"cuxiaolist.length>0\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">促销</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"t\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"t0\">{{item.tip}}</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"product.discount_tips!=''\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">折扣</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"padding-left:10rpx\">{{product.discount_tips}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" data-url=\"/pagesExa/my/levelinfo\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cuxiaopoint cuxiaoitem\" v-if=\"couponlist.length>0 && showtoptabbar==0\">\r\n\t\t\t\t\t\t\t<view class=\"f0\">优惠</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text\r\n\t\t\t\t\t\t\t\t\t\tclass=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrow-point.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\r\n\t\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\r\n\t\t\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\t\t\**********=\"hidefuwudetail\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"showcuxiaodialog\" class=\"popup__container\">\r\n\t\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidecuxiaodetail\"></view>\r\n\t\t\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t\t\t<text class=\"popup__title-text\">优惠促销</text>\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\t\t\**********=\"hidecuxiaodetail\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in cuxiaolist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"suffix\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"type-name\"><text\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px\">{{item.tip}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"color:#333;margin-left:20rpx\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<couponlist :couponlist=\"couponlist\" @getcoupon=\"getcoupon\"></couponlist>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"width:100%;height:auto;padding:20rpx 0 0\" v-if=\"shopset.detail_guangao2\">\r\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao2\" style=\"width:100%;height:auto\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tv-if=\"shopset.detail_guangao2\" @tap=\"showgg2Dialog\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<uni-popup id=\"gg2Dialog\" ref=\"gg2Dialog\" type=\"dialog\"\r\n\t\t\t\t\t\tv-if=\"shopset.detail_guangao2 && shopset.detail_guangao2_t\">\r\n\t\t\t\t\t\t<image :src=\"shopset.detail_guangao2_t\" @tap=\"previewImage\"\r\n\t\t\t\t\t\t\t:data-url=\"shopset.detail_guangao2_t\" class=\"img\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width:600rpx;height:auto;border-radius:10rpx;\" />\r\n\t\t\t\t\t\t<view class=\"ggdiaplog_close\" @tap=\"closegg2Dialog\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/close2.png\" style=\"width:100%;height:100%\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-popup>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view id=\"scroll_view_tab1\">\r\n\r\n\t\t\t\t\t<view class=\"commentbox\" v-if=\"shopset.comment==1 && commentcount > 0\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评率 <text\r\n\t\t\t\t\t\t\t\t\t:style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text>\r\n\t\t\t\t\t\t\t\t<image style=\"width:32rpx;height:32rpx;\" src=\"/static/img/arrowright.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"comment\">\r\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\" />\r\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"\r\n\t\t\t\t\t\t\t\t\t\t\t:src=\"'/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t:data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view id=\"scroll_view_tab2\">\r\n\r\n\t\t\t\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t\t\t\t<image :src=\"business.logo\" class=\"p1\" />\r\n\t\t\t\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"p4\"\r\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\r\n\t\t\t\t\t\t\t@tap=\"goto\"\r\n\t\t\t\t\t\t\t:data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\"\r\n\t\t\t\t\t\t\tdata-opentype=\"reLaunch\">进入首页</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"!isEmpty(product.paramdata)\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t<view class=\"t1\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t0\">商品参数</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t1\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"background:#fff;padding:20rpx 40rpx;\" class=\"paraminfo\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in product.paramdata\" class=\"paramitem\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{index}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">{{item}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t<view class=\"t1\"></view>\r\n\t\t\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t\t\t<view class=\"t0\">商品描述</view>\r\n\t\t\t\t\t\t<view class=\"t2\"></view>\r\n\t\t\t\t\t\t<view class=\"t1\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view id=\"scroll_view_tab3\">\r\n\r\n\t\t\t\t\t<view v-if=\"tjdatalist.length > 0\">\r\n\t\t\t\t\t\t<view class=\"xihuan\">\r\n\t\t\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t\t\t\t<view class=\"xihuan-text\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/xihuan.png\" class=\"img\" />\r\n\t\t\t\t\t\t\t\t<text class=\"txt\">为您推荐</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"xihuan-line\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"prolist\">\r\n\t\t\t\t\t\t\t<dp-product-item :data=\"tjdatalist\" @addcart=\"addcart\"\r\n\t\t\t\t\t\t\t\t:menuindex=\"menuindex\"></dp-product-item>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\r\n\t\t\t</scroll-view>\r\n\r\n\t\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\"\r\n\t\t\t\tv-if=\"product.status==1&&!showcuxiaodialog\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\" />\r\n\t\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"item\" v-else open-type=\"contact\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/kefu.png\" />\r\n\t\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"item flex1\" @tap=\"goto\" data-url=\"/shopPackage/shop/cart\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/gwc.png\" />\r\n\t\t\t\t\t\t<view class=\"t1\">购物车</view>\r\n\t\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\" :style=\"{background:'rgba('+t('color1rgb')+',0.8)'}\">\r\n\t\t\t\t\t\t\t{{cartnum}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/shoucang.png\" />\r\n\t\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op2\" v-if=\"showjiesheng==1\">\r\n\t\t\t\t\t<view class=\"tocart2\" :style=\"{background:t('color2')}\" @tap=\"shareClick\"><text>分享赚钱</text><text\r\n\t\t\t\t\t\t\tstyle=\"font-size:24rpx\">赚￥{{product.commission}}</text></view>\r\n\t\t\t\t\t<view class=\"tobuy2\" :style=\"{background:t('color1')}\" @tap=\"buydialogChange\" data-btntype=\"2\">\r\n\t\t\t\t\t\t<text>立即购买</text><text style=\"font-size:24rpx\"\r\n\t\t\t\t\t\t\tv-if=\"product.jiesheng_money > 0\">省￥{{product.jiesheng_money}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\" v-else>\r\n\t\t\t\t\t<block v-if=\"product.hide_price == 1\">\r\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\r\n\t\t\t\t\t\t\t@tap=\"hidePriceLink\" data-btntype=\"2\">\r\n\t\t\t\t\t\t\t{{product.hide_price_detail_text ? product.hide_price_detail_text : '咨询'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else-if=\"product.price_type == 1\">\r\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\r\n\t\t\t\t\t\t\t@tap=\"showLinkChange\" data-btntype=\"2\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t<!-- \t<view class=\"tocart flex-x-center flex-y-center\" :style=\"{background:t('color2')}\"\r\n\t\t\t\t\t\t\t@tap=\"buydialogChange\" data-btntype=\"1\"\r\n\t\t\t\t\t\t\tv-if=\"product.freighttype!=3 && product.freighttype!=4\">加入购物车</view> -->\r\n\t\t\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\"\r\n\t\t\t\t\t\t\t@tap=\"buydialogChange\" data-btntype=\"2\">立即购买</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<BuyDialog \r\n\t\t\t\tv-if=\"buydialogShow\" \r\n\t\t\t\t:proid=\"product.id\"\r\n\t\t\t\t:bid=\"opt.bid\"  \r\n\t\t\t\t:btntype=\"btntype\"\r\n\t\t\t\t@buydialogChange=\"buydialogChange\"\r\n\t\t\t\t@showLinkChange=\"showLinkChange\"\r\n\t\t\t\t:menuindex=\"menuindex\"\r\n\t\t\t\t@addcart=\"addcart\"></BuyDialog>\r\n\r\n\t\t\t<view class=\"scrolltop\" v-show=\"scrolltopshow\" @tap=\"changetoptab\" data-index=\"0\">\r\n\t\t\t\t<image class=\"image\" src=\"/static/img/gotop.png\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t\t<image src=\"/static/img/close.png\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else>\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/sharepic.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareScheme\" v-if=\"getplatform() == 'wx' && xcx_scheme\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/weixin.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">小程序链接</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goEdit\">\r\n\t\t\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/edit2.png\" />\r\n\t\t\t\t\t\t\t\t<text class=\"t1\">发布笔记</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t\t<view class=\"main\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/close.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 0 10px 10px 10px;\">\r\n\r\n\r\n\t\t\t\t\t\t<button class=\"pp4\" @tap=\"savePhoto\"\r\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">保存</button>\r\n\t\t\t \r\n\t\t\t\t\t\t <button class=\"pp4\" v-if=\"getplatform() == 'app'\" @tap=\"shareapp\"\r\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t <button class=\"pp4\" v-else-if=\"getplatform() == 'mp'\"  @tap=\"sharemp\"\r\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t <button class=\"pp4\" v-else-if=\"getplatform() == 'h5'\"  @tap=\"sharemp\"\r\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t <button class=\"pp4\" open-type=\"share\" v-else\r\n\t\t\t\t\t\t \t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">转发</button>\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<view class=\"posterDialog schemeDialog\" v-if=\"showScheme\">\r\n\t\t\t\t<view class=\"main\">\r\n\t\t\t\t\t<view class=\"schemecon\">\r\n\t\t\t\t\t\t<view style=\"line-height: 60rpx;\">{{product.name}} </view>\r\n\t\t\t\t\t\t<view>购买链接：<text style=\"color: #00A0E9;\">{{schemeurl}}</text></view>\r\n\t\t\t\t\t\t<view class=\"copybtn\"\r\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\r\n\t\t\t\t\t\t\**********=\"copy\" :data-text=\"product.name+'购买链接：'+schemeurl\"> 一键复制 </view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\r\n\t\t\t\t<view class=\"main\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"showLinkChange\">\r\n\t\t\t\t\t\t<image class=\"img\" src=\"/static/img/close.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t<view class=\"title\">{{sysset.name}}</view>\r\n\t\t\t\t\t\t<view class=\"row\" v-if=\"product.bid > 0\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">店铺名称</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+product.bid\">\r\n\t\t\t\t\t\t\t\t{{business.name}}\r\n\t\t\t\t\t\t\t\t<image src=\"/static/img/arrowright.png\" class=\"image\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\" v-if=\"business.tel\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">联系电话</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'tel::'+business.tel\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t\t{{business.tel}}\r\n\t\t\t\t\t\t\t\t<image src=\"../../static/img/copy.png\" class=\"copyicon\" @tap.stop=\"copy\"\r\n\t\t\t\t\t\t\t\t\t:data-text=\"business.tel\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t<!-- 幸运夺宝 -->\r\n\t\t<uni-popup ref=\"popupLucky\">\r\n\t\t\t<view class=\"model show\">\r\n\t\t\t\t<view class=\"luckBao-header\">\r\n\t\t\t\t\t<image @tap=\"onCloseLucky\" class=\"luckBao-img-close\" src=\"@/game/static/game/lucky/icon_close.png\"\r\n\t\t\t\t\t\tmode=\"widthFix\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"luckBao\">\r\n\t\t\t\t\t<view class=\"luckBao-text\">中奖概率：{{ getProbability() }}%</view>\r\n\t\t\t\t\t<view class=\"luckBao-input-wrap\">\r\n\t\t\t\t\t\t<text>幸运价</text>\r\n\t\t\t\t\t\t<input class=\"luckBao-input-class\" :value=\"luckyPrice\" @input=\"inputLuckyPrice\"></input>\r\n\t\t\t\t\t\t<picker :value=\"priceIndex\" :range=\"luckyPriceArray\" @change=\"pickerChangePrice\">\r\n\t\t\t\t\t\t\t<image class=\"img-cursor-2\" src=\"@/game/static/game/lucky/icon_cursor.png\"\r\n\t\t\t\t\t\t\t\tmode=\"widthFix\" />\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"luckBao-tips-wrap1\">点击输入框手动填写价格</view>\r\n\t\t\t\t\t<view class=\"luckBao-tips-wrap2\">\r\n\t\t\t\t\t\t<text>或者点击</text>\r\n\t\t\t\t\t\t<image class=\"img-cursor-1\" src=\"@/game/static/game/lucky/icon_cursor.png\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t<text>选择价格</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"lucky-btn-now\" @tap=\"onLuckyNow\">立即夺宝</view>\r\n\t\t\t\t<view class=\"lucky-btn-origin-price\" @tap=\"buydialogChange\">原价购买</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\tvar interval = null;\r\n\timport BuyDialog from '@/pagesExa/tuanzhang/buydialog/buydialog.vue'; // Adjust the path as necessary\r\n\texport default {\r\n\t\t\r\n\t\tcomponents: {\r\n\t\t    BuyDialog,\r\n\t\t  },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpriceIndex: 0,\r\n\t\t\t\tluckyPriceArray: [],\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\ttextset: {},\r\n\t\t\t\tisload: false,\r\n\t\t\t\tbuydialogShow: false,\r\n\t\t\t\tbtntype: 1,\r\n\t\t\t\tisfavorite: false,\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tisplay: 0,\r\n\t\t\t\tshowcuxiaodialog: false,\r\n\t\t\t\tshowfuwudialog: false,\r\n\t\t\t\tbusiness: \"\",\r\n\t\t\t\tproduct: [],\r\n\t\t\t\tcartnum: \"\",\r\n\t\t\t\tcommentlist: \"\",\r\n\t\t\t\tcommentcount: \"\",\r\n\t\t\t\tcuxiaolist: \"\",\r\n\t\t\t\tcouponlist: \"\",\r\n\t\t\t\tfuwulist: [],\r\n\t\t\t\tpagecontent: \"\",\r\n\t\t\t\tshopset: {},\r\n\t\t\t\tsysset: {},\r\n\t\t\t\ttitle: \"\",\r\n\t\t\t\tbboglist: \"\",\r\n\t\t\t\tsharepic: \"\",\r\n\t\t\t\tsharetypevisible: false,\r\n\t\t\t\tshowposter: false,\r\n\t\t\t\tposterpic: \"\",\r\n\t\t\t\tscrolltopshow: false,\r\n\t\t\t\tkfurl: '',\r\n\t\t\t\tshowLinkStatus: false,\r\n\t\t\t\tshowjiesheng: 0,\r\n\t\t\t\ttjdatalist: [],\r\n\t\t\t\tshowtoptabbar: 0,\r\n\t\t\t\ttoptabbar_show: 0,\r\n\t\t\t\ttoptabbar_index: 0,\r\n\t\t\t\tscrollToViewId: \"\",\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\tscrolltab0Height: 0,\r\n\t\t\t\tscrolltab1Height: 0,\r\n\t\t\t\tscrolltab2Height: 0,\r\n\t\t\t\tscrolltab3Height: 0,\r\n\t\t\t\txcx_scheme: false,\r\n\t\t\t\tshowScheme: false,\r\n\t\t\t\tschemeurl: '',\r\n\t\t\t\tisGameOpen: false, // 游戏开启状态\r\n\t\t\t\tgameList: [],\r\n\t\t\t\t// gameList: [{\r\n\t\t\t\t// \t\tid: 2,\r\n\t\t\t\t// \t\tkey: 'happy',\r\n\t\t\t\t// \t\timg: '/static/game/happy.png',\r\n\t\t\t\t// \t\tname: '幸运大转盘',\r\n\t\t\t\t// \t\tamount: 0.98\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 1,\r\n\t\t\t\t// \t\tkey: 'lucky',\r\n\t\t\t\t// \t\timg: '/static/game/lucky.png',\r\n\t\t\t\t// \t\tname: '幸运夺宝',\r\n\t\t\t\t// \t\tamount: 0.88\r\n\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 3,\r\n\t\t\t\t// \t\tkey: 'free',\r\n\t\t\t\t// \t\timg: '/static/game/free.png',\r\n\t\t\t\t// \t\tname: '免单',\r\n\t\t\t\t// \t\tamount: 0.88\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 4,\r\n\t\t\t\t// \t\tkey: 'cat',\r\n\t\t\t\t// \t\timg: '/static/game/cat.png',\r\n\t\t\t\t// \t\tname: '猫',\r\n\t\t\t\t// \t\tamount: 0.88\r\n\t\t\t\t// \t}\r\n\t\t\t\t// ],\r\n\t\t\t\tluckyPrice: 0.00,\r\n\t\t\t\tcurrentGameInfo: {}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t\tthis.getSweepstakesStatus()\r\n\t\t\t if(options.bid) {\r\n\t\t\t            this.bid = options.bid;\r\n\t\t\t            console.log('获取到bid:', this.bid); // 调试日志\r\n\t\t\t        }\r\n\t\t},\r\n\t\tonShow: function(e) {\r\n\t\t\tuni.$emit('getglassrecord');\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn this._sharewx({\r\n\t\t\t\ttitle: this.product.sharetitle || this.product.name,\r\n\t\t\t\tpic: this.product.sharepic || this.product.pic\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShareTimeline: function() {\r\n\t\t\tvar sharewxdata = this._sharewx({\r\n\t\t\t\ttitle: this.product.sharetitle || this.product.name,\r\n\t\t\t\tpic: this.product.sharepic || this.product.pic\r\n\t\t\t});\r\n\t\t\tvar query = (sharewxdata.path).split('?')[1];\r\n\t\t\tconsole.log(sharewxdata)\r\n\t\t\tconsole.log(query)\r\n\t\t\treturn {\r\n\t\t\t\ttitle: sharewxdata.title,\r\n\t\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\t\tquery: query\r\n\t\t\t}\r\n\t\t},\r\n\t\tonUnload: function() {\r\n\t\t\tclearInterval(interval);\r\n\t\t\tthis.$refs.popupLucky && this.$refs.popupLucky.close();\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 获取抽奖总状态\r\n\t\t\t */\r\n\t\t\tgetSweepstakesStatus() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tpay_type: 2 //支付方式，1->线上支付，2->积分\r\n\t\t\t\t}\r\n\t\t\t\tapp.get('ApiSweepstakes/getSet', {}, function(res) {\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.isGameOpen = !!res.is_open\r\n\t\t\t\t\tthat.getSweepstakesList()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 抽奖列表\r\n\t\t\t */\r\n\t\t\tgetSweepstakesList() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiSweepstakes/getList', {}, async function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.gameList = res.list\r\n\t\t\t\t\tif (that.isGameOpen && that.gameList && that.gameList.length > 0) {\r\n\t\t\t\t\t\tthat.currentGameInfo = that.gameList.find(item => item.keyName === 'lucky')\r\n\t\t\t\t\t\tthat.luckyPrice = that.currentGameInfo.amount\r\n\t\t\t\t\t\tthat.luckyPriceArray = []\r\n\t\t\t\t\t\tthat.luckyPriceArray = Array.from({\r\n\t\t\t\t\t\t\tlength: 33\r\n\t\t\t\t\t\t}, (_, i) => (i + 8) / 10);\r\n\t\t\t\t\t\tthat.$refs.popupLucky.open()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\tgoEdit() {\r\n\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/daihuobiji/detail/fatie?goodid=' + this.opt.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tsavePhoto(){\r\n\t\t\t\tlet that = this\r\n\t\t\t\t// 首先下载图片\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t    url: that.posterpic,\r\n\t\t\t\t    success: downloadResult => {\r\n\t\t\t\t        if (downloadResult.statusCode === 200) {\r\n\t\t\t\t            // 下载成功，保存图片到系统相册\r\n\t\t\t\t            uni.saveImageToPhotosAlbum({\r\n\t\t\t\t                filePath: downloadResult.tempFilePath,\r\n\t\t\t\t                success: () => {\r\n\t\t\t\t                    uni.showToast({\r\n\t\t\t\t                        title: '图片保存成功'\r\n\t\t\t\t                    });\r\n\t\t\t\t                },\r\n\t\t\t\t                fail: () => {\r\n\t\t\t\t                    uni.showToast({\r\n\t\t\t\t                        title: '图片保存失败',\r\n\t\t\t\t                        icon: 'none'\r\n\t\t\t\t                    });\r\n\t\t\t\t                }\r\n\t\t\t\t            });\r\n\t\t\t\t        } else {\r\n\t\t\t\t            uni.showToast({\r\n\t\t\t\t                title: '图片下载失败',\r\n\t\t\t\t                icon: 'none'\r\n\t\t\t\t            });\r\n\t\t\t\t        }\r\n\t\t\t\t    },\r\n\t\t\t\t    fail: () => {\r\n\t\t\t\t        uni.showToast({\r\n\t\t\t\t            title: '图片下载失败',\r\n\t\t\t\t            icon: 'none'\r\n\t\t\t\t        });\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t},\r\n\r\n\t\t\tshowLinkChange: function() {\r\n\t\t\t\tthis.showLinkStatus = !this.showLinkStatus;\r\n\t\t\t},\r\n\t\t\tgetdata: function() {\r\n\t\t\t\t\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = this.opt.id || 0;\r\n\t\t\t\tvar bid = this.opt.bid || 0;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiTuanzhang/product', {\r\n\t\t\t\t\tid: id,\r\n\t\t\t\t\tbid: bid\r\n\t\t\t\t}, function(res) {\r\n\r\n\t\t\t\t\tconsole.log(res);\r\n\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\t\tvar product = res.product;\r\n\t\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\t\tthat.business = res.business;\r\n\t\t\t\t\tthat.product = product;\r\n\t\t\t\t\tthat.cartnum = res.cartnum;\r\n\t\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\t\tthat.cuxiaolist = res.cuxiaolist;\r\n\t\t\t\t\tthat.couponlist = res.couponlist;\r\n\t\t\t\t\tthat.fuwulist = res.fuwulist;\r\n\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\t\tthat.title = product.name;\r\n\t\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\t\tthat.showjiesheng = res.showjiesheng || 0;\r\n\t\t\t\t\tthat.tjdatalist = res.tjdatalist || [];\r\n\t\t\t\t\tthat.showtoptabbar = res.showtoptabbar || 0;\r\n\t\t\t\t\tthat.bboglist = res.bboglist;\r\n\t\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\t\tthat.xcx_scheme = res.xcx_scheme\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: product.name\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tthat.kfurl = '/pagesExt/kefu/index?bid=' + product.bid;\r\n\t\t\t\t\tif (app.globalData.initdata.kfurl != '') {\r\n\t\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.business && that.business.kfurl) {\r\n\t\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded({\r\n\t\t\t\t\t\ttitle: product.sharetitle || product.name,\r\n\t\t\t\t\t\tpic: product.sharepic || product.pic,\r\n\t\t\t\t\t\tdesc: product.sharedesc || product.sellpoint\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tlet view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')\r\n\t\t\t\t\t\tview0.fields({\r\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\r\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tthat.scrolltab0Height = res.height\r\n\t\t\t\t\t\t}).exec();\r\n\t\t\t\t\t\tlet view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')\r\n\t\t\t\t\t\tview1.fields({\r\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\r\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tthat.scrolltab1Height = res.height\r\n\t\t\t\t\t\t}).exec();\r\n\t\t\t\t\t\tlet view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')\r\n\t\t\t\t\t\tview2.fields({\r\n\t\t\t\t\t\t\tsize: true, //是否返回节点尺寸（width height）\r\n\t\t\t\t\t\t\trect: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t\tscrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport\r\n\t\t\t\t\t\t}, (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tthat.scrolltab2Height = res.height\r\n\t\t\t\t\t\t}).exec();\r\n\t\t\t\t\t}, 500)\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tswiperChange: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.current = e.detail.current\r\n\t\t\t},\r\n\t\t\tpayvideo: function() {\r\n\t\t\t\tthis.isplay = 1;\r\n\t\t\t\tuni.createVideoContext('video').play();\r\n\t\t\t},\r\n\t\t\tparsevideo: function() {\r\n\t\t\t\tthis.isplay = 0;\r\n\t\t\t\tuni.createVideoContext('video').stop();\r\n\t\t\t},\r\n\t\t\tbuydialogChange: function(e) {\r\n\t\t\t\tif (!this.buydialogShow) {\r\n\t\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype\r\n\t\t\t\t}\r\n\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t},\r\n\t\t\t//收藏操作\r\n\t\t\taddfavorite: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tapp.post('ApiShop/addfavorite', {\r\n\t\t\t\t\tproid: proid,\r\n\t\t\t\t\ttype: 'shop'\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareClick: function() {\r\n\t\t\t\tthis.sharetypevisible = true;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.sharetypevisible = false\r\n\t\t\t},\r\n\t\t\tshowPoster: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.showposter = true;\r\n\t\t\t\tthat.sharetypevisible = false;\r\n\t\t\t\tapp.showLoading('生成海报中');\r\n\t\t\t\tapp.post('ApiShop/getposter', {\r\n\t\t\t\t\tproid: that.product.id\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tshareScheme: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.post('ApiShop/getwxScheme', {\r\n\t\t\t\t\tproid: that.product.id\r\n\t\t\t\t}, function(data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.showScheme = true;\r\n\t\t\t\t\t\tthat.schemeurl = data.openlink\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tschemeDialogClose: function() {\r\n\t\t\t\tthis.showScheme = false;\r\n\t\t\t},\r\n\r\n\t\t\tposterDialogClose: function() {\r\n\t\t\t\tthis.showposter = false;\r\n\t\t\t},\r\n\t\t\tshowfuwudetail: function() {\r\n\t\t\t\tthis.showfuwudialog = true;\r\n\t\t\t},\r\n\t\t\thidefuwudetail: function() {\r\n\t\t\t\tthis.showfuwudialog = false\r\n\t\t\t},\r\n\t\t\tshowcuxiaodetail: function() {\r\n\t\t\t\tthis.showcuxiaodialog = true;\r\n\t\t\t},\r\n\t\t\thidecuxiaodetail: function() {\r\n\t\t\t\tthis.showcuxiaodialog = false\r\n\t\t\t},\r\n\t\t\tgetcoupon: function() {\r\n\t\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\t\tthis.getdata();\r\n\t\t\t},\r\n\t\t\tonPageScroll: function(e) {\r\n\t\t\t\t//var that = this;\r\n\t\t\t\t//var scrollY = e.scrollTop;     \r\n\t\t\t\t//if (scrollY > 200) {\r\n\t\t\t\t//\tthat.scrolltopshow = true;\r\n\t\t\t\t//}\r\n\t\t\t\t//if(scrollY < 150) {\r\n\t\t\t\t//\tthat.scrolltopshow = false\r\n\t\t\t\t//}\r\n\t\t\t\t//if (scrollY > 100) {\r\n\t\t\t\t//\tthat.toptabbar_show = true;\r\n\t\t\t\t//}\r\n\t\t\t\t//if(scrollY < 50) {\r\n\t\t\t\t//\tthat.toptabbar_show = false\r\n\t\t\t\t//}\r\n\t\t\t},\r\n\t\t\tchangetoptab: function(e) {\r\n\t\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\t\tthis.scrollToViewId = 'scroll_view_tab' + index;\r\n\t\t\t\tthis.toptabbar_index = index;\r\n\t\t\t\tif (index == 0) this.scrollTop = 0;\r\n\t\t\t\tconsole.log(index);\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tvar scrollTop = e.detail.scrollTop;\r\n\t\t\t\t//console.log(e)\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (scrollTop > 200) {\r\n\t\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t\t}\r\n\t\t\t\tif (scrollTop < 150) {\r\n\t\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t\t}\r\n\t\t\t\tif (scrollTop > 100) {\r\n\t\t\t\t\tthat.toptabbar_show = true;\r\n\t\t\t\t}\r\n\t\t\t\tif (scrollTop < 50) {\r\n\t\t\t\t\tthat.toptabbar_show = false\r\n\t\t\t\t}\r\n\t\t\t\tvar height0 = that.scrolltab0Height;\r\n\t\t\t\tvar height1 = that.scrolltab0Height + that.scrolltab1Height;\r\n\t\t\t\tvar height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;\r\n\t\t\t\t//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;\r\n\t\t\t\tconsole.log('-----------------------');\r\n\t\t\t\tconsole.log(scrollTop);\r\n\t\t\t\tconsole.log(height2);\r\n\t\t\t\tif (scrollTop >= 0 && scrollTop < height0) {\r\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab0';\r\n\t\t\t\t\tthis.toptabbar_index = 0;\r\n\t\t\t\t} else if (scrollTop >= height0 && scrollTop < height1) {\r\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab1';\r\n\t\t\t\t\tthis.toptabbar_index = 1;\r\n\t\t\t\t} else if (scrollTop >= height1 && scrollTop < height2) {\r\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab2';\r\n\t\t\t\t\tthis.toptabbar_index = 2;\r\n\t\t\t\t} else if (scrollTop >= height2) {\r\n\t\t\t\t\t//this.scrollToViewId = 'scroll_view_tab3';\r\n\t\t\t\t\tthis.toptabbar_index = 3;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsharemp: function() {\r\n\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\t\tthis.sharetypevisible = false\r\n\t\t\t},\r\n\t\t\tshareapp: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.sharetypevisible = false;\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.tapIndex >= 0) {\r\n\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\t\tsharedata.title = that.product.sharetitle || that.product.name;\r\n\t\t\t\t\t\t\tsharedata.summary = that.product.sharedesc || that.product.sellpoint;\r\n\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +\r\n\t\t\t\t\t\t\t\t'.html#/shopPackage/shop/product?scene=id_' + that.product.id + '-pid_' + app\r\n\t\t\t\t\t\t\t\t.globalData.mid;\r\n\t\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\t\tif (sharelist) {\r\n\t\t\t\t\t\t\t\tfor (var i = 0; i < sharelist.length; i++) {\r\n\t\t\t\t\t\t\t\t\tif (sharelist[i]['indexurl'] == '/shopPackage/shop/product') {\r\n\t\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\t\tif (sharelist[i].url) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\t\tif (sharelink.indexOf('/') === 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url + '/h5/' + app\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.globalData.aid + '.html#' + sharelink;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (app.globalData.mid > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tsharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'pid=' + app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowsubqrcode: function() {\r\n\t\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t\t},\r\n\t\t\tclosesubqrcode: function() {\r\n\t\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t\t},\r\n\t\t\taddcart: function(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tthis.cartnum = this.cartnum + e.num;\r\n\t\t\t},\r\n\t\t\tshowgg1Dialog: function() {\r\n\t\t\t\tthis.$refs.gg1Dialog.open();\r\n\t\t\t},\r\n\t\t\tclosegg1Dialog: function() {\r\n\t\t\t\tthis.$refs.gg1Dialog.close();\r\n\t\t\t},\r\n\t\t\tshowgg2Dialog: function() {\r\n\t\t\t\tthis.$refs.gg2Dialog.open();\r\n\t\t\t},\r\n\t\t\tclosegg2Dialog: function() {\r\n\t\t\t\tthis.$refs.gg2Dialog.close();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 点击游戏\r\n\t\t\t * @param {Object} item\r\n\t\t\t */\r\n\t\t\tonClickGame(item) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tkeyName,\r\n\t\t\t\t\tpath\r\n\t\t\t\t} = item\r\n\t\t\t\tthis.currentGameInfo = Object.assign({}, item)\r\n\t\t\t\tswitch (keyName) {\r\n\t\t\t\t\tcase 'lucky':\r\n\t\t\t\t\t\tthis.luckyPrice = item.amount\r\n\t\t\t\t\t\tthis.$refs.popupLucky.open('top');\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tapp.goto(`/game/${keyName}?id=${this.opt.id}&gameId=${item.id}&amount=${item.amount}`)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 输入幸运价\r\n\t\t\t */\r\n\t\t\tinputLuckyPrice(e) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tvalue\r\n\t\t\t\t} = e.detail;\r\n\t\t\t\tthis.luckyPrice = value\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 中奖概率\r\n\t\t\t */\r\n\t\t\tgetProbability() {\r\n\t\t\t\tlet num = this.luckyPrice / this.product.min_price;\r\n\t\t\t\treturn ((num && num * 100) || 0).toFixed(2);\r\n\t\t\t},\r\n\t\t\tonCloseLucky() {\r\n\t\t\t\tthis.currentGameInfo = {}\r\n\t\t\t\tthis.$refs.popupLucky.close();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 幸运夺宝-修改价格\r\n\t\t\t */\r\n\t\t\tpickerChangePrice(e) {\r\n\t\t\t\tthis.luckyPrice = this.luckyPriceArray[e.detail.value]\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 立即夺宝\r\n\t\t\t */\r\n\t\t\tonLuckyNow() {\r\n\t\t\t\tthis.$refs.popupLucky.close();\r\n\t\t\t\tapp.goto(\r\n\t\t\t\t\t`/game/lucky?id=${this.opt.id}&gameId=${this.currentGameInfo.id}&amount=${this.luckyPrice}`\r\n\t\t\t\t)\r\n\r\n\t\t\t},\r\n\t\t\thidePriceLink() {\r\n\t\t\t\tapp.goto(this.product.hide_price_link)\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t};\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.container {\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.follow_topbar {\r\n\t\theight: 88rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 640px;\r\n\t\tbackground: rgba(0, 0, 0, 0.8);\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tz-index: 13;\r\n\t}\r\n\r\n\t.follow_topbar .headimg {\r\n\t\theight: 64rpx;\r\n\t\twidth: 64rpx;\r\n\t\tmargin: 6px;\r\n\t\tfloat: left;\r\n\t}\r\n\r\n\t.follow_topbar .headimg image {\r\n\t\theight: 64rpx;\r\n\t\twidth: 64rpx;\r\n\t}\r\n\r\n\t.follow_topbar .info {\r\n\t\theight: 56rpx;\r\n\t\tpadding: 16rpx 0;\r\n\t}\r\n\r\n\t.follow_topbar .info .i {\r\n\t\theight: 28rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tcolor: #ccc;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.follow_topbar .info {\r\n\t\theight: 80rpx;\r\n\t\tfloat: left;\r\n\t}\r\n\r\n\t.follow_topbar .sub {\r\n\t\theight: 48rpx;\r\n\t\twidth: auto;\r\n\t\tbackground: #FC4343;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin: 20rpx 16rpx 20rpx 0;\r\n\t\tfloat: right;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\tline-height: 52rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.qrcodebox {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 50rpx;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx\r\n\t}\r\n\r\n\t.qrcodebox .img {\r\n\t\twidth: 400rpx;\r\n\t\theight: 400rpx\r\n\t}\r\n\r\n\t.qrcodebox .txt {\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.qrcodebox .close {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: -100rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -25rpx;\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.5);\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.swiper-container {\r\n\t\tposition: relative;\r\n\t\theight: 750rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.swiper-item-view {\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t}\r\n\r\n\t.swiper .img {\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.imageCount {\r\n\t\twidth: 100rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t\tborder-radius: 40rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 13px;\r\n\t\tbottom: 20rpx;\r\n\t}\r\n\r\n\t.provideo {\r\n\t\tbackground: rgba(255, 255, 255, 0.7);\r\n\t\twidth: 160rpx;\r\n\t\theight: 54rpx;\r\n\t\tpadding: 0 20rpx 0 4rpx;\r\n\t\tborder-radius: 27rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 30rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.provideo image {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t}\r\n\r\n\t.provideo .txt {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tpadding-left: 10rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.videobox {\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground: #000\r\n\t}\r\n\r\n\t.videobox .video {\r\n\t\twidth: 100%;\r\n\t\theight: 650rpx;\r\n\t}\r\n\r\n\t.videobox .parsevideo {\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 20rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground: #ccc;\r\n\t\twidth: 140rpx;\r\n\t\tborder-radius: 25rpx;\r\n\t\tfont-size: 24rpx\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 3%;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.header .price_share {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.header .price_share .price {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end\r\n\t}\r\n\r\n\t.header .price_share .price .f1 {\r\n\t\tfont-size: 50rpx;\r\n\t\tcolor: #51B539;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.header .price_share .price .f2 {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #C2C2C2;\r\n\t\ttext-decoration: line-through;\r\n\t\tmargin-left: 30rpx;\r\n\t\tpadding-bottom: 5px\r\n\t}\r\n\r\n\t.header .price_share .share {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmin-width: 60rpx;\r\n\t}\r\n\r\n\t.header .price_share .share .img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tmargin-bottom: 2px\r\n\t}\r\n\r\n\t.header .price_share .share .txt {\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 20rpx\r\n\t}\r\n\r\n\t.header .title {\r\n\t\tcolor: #000000;\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 42rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.header .price_share .title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end;\r\n\t}\r\n\r\n\t.header .sellpoint {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tpadding-top: 20rpx;\r\n\t}\r\n\r\n\t.header .sales_stock {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #777777\r\n\t}\r\n\r\n\t.header .commission {\r\n\t\tdisplay: inline-block;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\theight: 44rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tpadding: 0 20rpx\r\n\t}\r\n\r\n\t.header .upsavemoney {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\theight: 70rpx;\r\n\t\tpadding: 0 20rpx\r\n\t}\r\n\r\n\t.choose {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 20rpx;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tpadding: 0 3%;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.choose .f0 {\r\n\t\tcolor: #555;\r\n\t\tfont-weight: bold;\r\n\t\theight: 32rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.choose .f2 {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.cuxiaodiv {\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 0 3%;\r\n\t}\r\n\r\n\t.fuwupoint {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tpadding: 12rpx 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.fuwupoint .f0 {\r\n\t\tcolor: #555;\r\n\t\tfont-weight: bold;\r\n\t\theight: 32rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.fuwupoint .f1 {\r\n\t\tmargin-right: 20rpx;\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.fuwupoint .f1 .t {\r\n\t\tpadding: 4rpx 20rpx 4rpx 0;\r\n\t\tcolor: #777;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.fuwupoint .f1 .t:before {\r\n\t\tcontent: \"\";\r\n\t\tdisplay: inline-block;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-top: -4rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\r\n\t\tbackground-size: 24rpx auto;\r\n\t}\r\n\r\n\t.fuwupoint .f2 {\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.fuwupoint .f2 .img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.fuwudialog-content {\r\n\t\tfont-size: 24rpx\r\n\t}\r\n\r\n\t.fuwudialog-content .f1 {\r\n\t\tcolor: #333;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.fuwudialog-content .f1:before {\r\n\t\tcontent: \"\";\r\n\t\tdisplay: inline-block;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-top: -4rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;\r\n\t\tbackground-size: 24rpx auto;\r\n\t}\r\n\r\n\t.fuwudialog-content .f2 {\r\n\t\tcolor: #777\r\n\t}\r\n\r\n\t.cuxiaopoint {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tpadding: 12rpx 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.cuxiaopoint .f0 {\r\n\t\tcolor: #555;\r\n\t\tfont-weight: bold;\r\n\t\theight: 32rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding-right: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.cuxiaopoint .f1 {\r\n\t\tmargin-right: 20rpx;\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.cuxiaopoint .f1 .t {\r\n\t\tmargin-left: 10rpx;\r\n\t\tborder-radius: 3px;\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tpadding-right: 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t\toverflow: hidden\r\n\t}\r\n\r\n\t.cuxiaopoint .f1 .t0 {\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 0 5px;\r\n\t}\r\n\r\n\t.cuxiaopoint .f1 .t1 {\r\n\t\tpadding: 0 4px\r\n\t}\r\n\r\n\t.cuxiaopoint .f2 {\r\n\t\tflex-shrink: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.cuxiaopoint .f2 .img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.cuxiaodiv .cuxiaoitem {\r\n\t\tborder-bottom: 1px solid #E6E6E6;\r\n\t}\r\n\r\n\t.cuxiaodiv .cuxiaoitem:last-child {\r\n\t\tborder-bottom: 0\r\n\t}\r\n\r\n\t.popup__container {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tz-index: 10;\r\n\t\tbackground: #fff\r\n\t}\r\n\r\n\t.popup__overlay {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 11;\r\n\t\topacity: 0.3;\r\n\t\tbackground: #000\r\n\t}\r\n\r\n\t.popup__modal {\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tcolor: #3d4145;\r\n\t\toverflow-x: hidden;\r\n\t\toverflow-y: hidden;\r\n\t\topacity: 1;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tz-index: 12;\r\n\t\tmin-height: 600rpx;\r\n\t\tmax-height: 1000rpx;\r\n\t}\r\n\r\n\t.popup__title {\r\n\t\ttext-align: center;\r\n\t\tpadding: 30rpx;\r\n\t\tposition: relative;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.popup__title-text {\r\n\t\tfont-size: 32rpx\r\n\t}\r\n\r\n\t.popup__close {\r\n\t\tposition: absolute;\r\n\t\ttop: 34rpx;\r\n\t\tright: 34rpx\r\n\t}\r\n\r\n\t.popup__content {\r\n\t\twidth: 100%;\r\n\t\tmax-height: 880rpx;\r\n\t\toverflow-y: scroll;\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.service-item {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 40rpx 20rpx 40rpx;\r\n\t}\r\n\r\n\t.service-item .prefix {\r\n\t\tpadding-top: 2px;\r\n\t}\r\n\r\n\t.service-item .suffix {\r\n\t\tpadding-left: 10rpx;\r\n\t}\r\n\r\n\t.service-item .suffix .type-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #49aa34;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\r\n\t.shop {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 20rpx 3%;\r\n\t\tposition: relative;\r\n\t\tmin-height: 100rpx;\r\n\t}\r\n\r\n\t.shop .p1 {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\tflex-shrink: 0\r\n\t}\r\n\r\n\t.shop .p2 {\r\n\t\tpadding-left: 10rpx\r\n\t}\r\n\r\n\t.shop .p2 .t1 {\r\n\t\twidth: 100%;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #111;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.shop .p2 .t2 {\r\n\t\twidth: 100%;\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-top: 8rpx\r\n\t}\r\n\r\n\t.shop .p4 {\r\n\t\theight: 64rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tborder-radius: 32rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tflex-shrink: 0;\r\n\t\tpadding: 0 30rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.detail {\r\n\t\tmin-height: 200rpx;\r\n\t}\r\n\r\n\t.detail_title {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 60rpx;\r\n\t\tmargin-bottom: 30rpx\r\n\t}\r\n\r\n\t.detail_title .t0 {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #222222;\r\n\t\tmargin: 0 20rpx\r\n\t}\r\n\r\n\t.detail_title .t1 {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tbackground: rgba(253, 74, 70, 0.2);\r\n\t\ttransform: rotate(45deg);\r\n\t\tmargin: 0 4rpx;\r\n\t\tmargin-top: 6rpx\r\n\t}\r\n\r\n\t.detail_title .t2 {\r\n\t\twidth: 18rpx;\r\n\t\theight: 18rpx;\r\n\t\tbackground: rgba(253, 74, 70, 0.4);\r\n\t\ttransform: rotate(45deg);\r\n\t\tmargin: 0 4rpx\r\n\t}\r\n\r\n\t.commentbox {\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 0 3%;\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\t.commentbox .title {\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tborder-bottom: 1px solid #DDDDDD;\r\n\t\tdisplay: flex\r\n\t}\r\n\r\n\t.commentbox .title .f1 {\r\n\t\tflex: 1;\r\n\t\tcolor: #111111;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx\r\n\t}\r\n\r\n\t.commentbox .title .f2 {\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.commentbox .nocomment {\r\n\t\theight: 100rpx;\r\n\t\tline-height: 100rpx\r\n\t}\r\n\r\n\t.comment {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: 200rpx;\r\n\t}\r\n\r\n\t.comment .item {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.comment .item .f1 {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.comment .item .f1 .t1 {\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.comment .item .f1 .t2 {\r\n\t\tpadding-left: 10rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.comment .item .f1 .t3 {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.comment .item .f1 .t3 .img {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 10rpx\r\n\t}\r\n\r\n\t.comment .item .score {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #f99716;\r\n\t}\r\n\r\n\t.comment .item .score image {\r\n\t\twidth: 140rpx;\r\n\t\theight: 50rpx;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-bottom: 6rpx;\r\n\t\tmargin-right: 6rpx;\r\n\t}\r\n\r\n\t.comment .item .f2 {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.comment .item .f2 .t1 {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.comment .item .f2 .t2 {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%\r\n\t}\r\n\r\n\t.comment .item .f2 .t2 image {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tmargin: 10rpx;\r\n\t}\r\n\r\n\t.comment .item .f2 .t3 {\r\n\t\tcolor: #aaa;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.comment .item .f3 {\r\n\t\tmargin: 20rpx auto;\r\n\t\tpadding: 0 30rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tborder: 1px solid #E6E6E6;\r\n\t\tborder-radius: 30rpx;\r\n\t\tcolor: #111111;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 26rpx\r\n\t}\r\n\r\n\t.bottombar {\r\n\t\twidth: 94%;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0px;\r\n\t\tleft: 0px;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\theight: 100rpx;\r\n\t\tpadding: 0 4% 0 2%;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: content-box\r\n\t}\r\n\r\n\t.bottombar .f1 {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-right: 30rpx\r\n\t}\r\n\r\n\t.bottombar .f1 .item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\twidth: 80rpx;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.bottombar .f1 .item .img {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx\r\n\t}\r\n\r\n\t.bottombar .f1 .item .t1 {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #222222;\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tmargin-top: 6rpx\r\n\t}\r\n\r\n\t.bottombar .op {\r\n\t\twidth: 60%;\r\n\t\tborder-radius: 36rpx;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.bottombar .tocart {\r\n\t\tflex: 1;\r\n\t\theight: 72rpx;\r\n\t\tline-height: 72rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 0px;\r\n\t\tborder: none;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.bottombar .tobuy {\r\n\t\tflex: 1;\r\n\t\theight: 72rpx;\r\n\t\tline-height: 72rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 0px;\r\n\t\tborder: none;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold\r\n\t}\r\n\r\n\t.bottombar .cartnum {\r\n\t\tposition: absolute;\r\n\t\tright: 4rpx;\r\n\t\ttop: -4rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50%;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tline-height: 32rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 22rpx;\r\n\t}\r\n\r\n\t.bottombar .op2 {\r\n\t\twidth: 60%;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.bottombar .tocart2 {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #fa938a;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.bottombar .tobuy2 {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #df2e24;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\r\n\t.sharetypecontent {\r\n\t\theight: 250rpx;\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 50rpx;\r\n\t\talign-items: flex-end\r\n\t}\r\n\r\n\t.sharetypecontent .f1 {\r\n\t\tcolor: #51c332;\r\n\t\twidth: 50%;\r\n\t\theight: 150rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.sharetypecontent button::after {\r\n\t\tborder: 0\r\n\t}\r\n\r\n\t.sharetypecontent .f1 .img {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx\r\n\t}\r\n\r\n\t.sharetypecontent .f2 {\r\n\t\tcolor: #51c332;\r\n\t\twidth: 50%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center\r\n\t}\r\n\r\n\t.sharetypecontent .f2 .img {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx\r\n\t}\r\n\r\n\t.sharetypecontent .t1 {\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #666\r\n\t}\r\n\r\n\t.posterDialog {\r\n\t\tposition: fixed;\r\n\t\tz-index: 9;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0.8);\r\n\t\ttop: var(--window-top);\r\n\t\tleft: 0\r\n\t}\r\n\r\n\t.posterDialog .main {\r\n\t\twidth: 80%;\r\n\t\tmargin: 60rpx 10% 30rpx 10%;\r\n\t\tbackground: #fff;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx\r\n\t}\r\n\r\n\t.posterDialog .close {\r\n\t\tposition: absolute;\r\n\t\tpadding: 20rpx;\r\n\t\ttop: 0;\r\n\t\tright: 0\r\n\t}\r\n\r\n\t.posterDialog .close .img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.posterDialog .content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 70rpx 20rpx 30rpx 20rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 30rpx;\r\n\t\ttext-align: center\r\n\t}\r\n\r\n\t.posterDialog .content .img {\r\n\t\twidth: 540rpx;\r\n\t\theight: 960rpx\r\n\t}\r\n\r\n\t.linkDialog {\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\t.linkDialog .main {\r\n\t\twidth: 90%;\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\tmargin: 0;\r\n\t\t-webkit-transform: translate(-50%, -50%);\r\n\t\ttransform: translate(-50%, -50%);\r\n\t}\r\n\r\n\t.linkDialog .title {\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.linkDialog .row {\r\n\t\tdisplay: flex;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tpadding: 0 16rpx;\r\n\t}\r\n\r\n\t.linkDialog .row .f1 {\r\n\t\twidth: 40%;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.linkDialog .row .f2 {\r\n\t\twidth: 60%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: right;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.linkDialog .image {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tmargin-left: 8rpx;\r\n\t\tmargin-top: 2rpx;\r\n\t}\r\n\r\n\t.linkDialog .copyicon {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tmargin-left: 8rpx;\r\n\t\tposition: relative;\r\n\t\ttop: 4rpx;\r\n\t}\r\n\r\n\t.paramitem {\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\tpadding: 20rpx\r\n\t}\r\n\r\n\t.paramitem .f1 {\r\n\t\twidth: 30%;\r\n\t\tcolor: #666\r\n\t}\r\n\r\n\t.paramitem .f2 {\r\n\t\tcolor: #333\r\n\t}\r\n\r\n\t.paramitem:last-child {\r\n\t\tborder-bottom: 0\r\n\t}\r\n\r\n\t.xihuan {\r\n\t\theight: auto;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 160rpx;\r\n\t\tmargin-top: 20rpx\r\n\t}\r\n\r\n\t.xihuan-line {\r\n\t\theight: auto;\r\n\t\tpadding: 0;\r\n\t\toverflow: hidden;\r\n\t\tflex: 1;\r\n\t\theight: 0;\r\n\t\tborder-top: 1px solid #eee\r\n\t}\r\n\r\n\t.xihuan-text {\r\n\t\tpadding: 0 32rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\t.xihuan-text .txt {\r\n\t\tcolor: #111;\r\n\t\tfont-size: 30rpx\r\n\t}\r\n\r\n\t.xihuan-text .img {\r\n\t\ttext-align: center;\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tmargin-right: 12rpx\r\n\t}\r\n\r\n\t.prolist {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tpadding: 8rpx 20rpx;\r\n\t}\r\n\r\n\t.toptabbar_tab {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tbackground: #fff;\r\n\t\ttop: var(--window-top);\r\n\t\tz-index: 11;\r\n\t\tposition: fixed;\r\n\t\tborder-bottom: 1px solid #f3f3f3\r\n\t}\r\n\r\n\t.toptabbar_tab .item {\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #666;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative\r\n\t}\r\n\r\n\t.toptabbar_tab .item .after {\r\n\t\tdisplay: none;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -16rpx;\r\n\t\tbottom: 10rpx;\r\n\t\theight: 3px;\r\n\t\tborder-radius: 1.5px;\r\n\t\twidth: 32rpx\r\n\t}\r\n\r\n\t.toptabbar_tab .on {\r\n\t\tcolor: #323233;\r\n\t}\r\n\r\n\t.toptabbar_tab .on .after {\r\n\t\tdisplay: block\r\n\t}\r\n\r\n\t.scrolltop {\r\n\t\tposition: fixed;\r\n\t\tbottom: 160rpx;\r\n\t\tright: 20rpx;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 12rpx 10rpx 8rpx 10rpx;\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.scrolltop .image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.ggdiaplog_close {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: -100rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -25rpx;\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.5);\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 8rpx\r\n\t}\r\n\r\n\t.schemeDialog {\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tz-index: 12;\r\n\t}\r\n\r\n\t.schemeDialog .main {\r\n\t\tposition: absolute;\r\n\t\ttop: 30%\r\n\t}\r\n\r\n\t.schemecon {\r\n\t\tpadding: 40rpx 30rpx;\r\n\t}\r\n\r\n\t.copybtn {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tcolor: #fff\r\n\t}\r\n\r\n\t.huang_bz {\r\n\t\tmargin: auto;\r\n\t\tpadding-left: 6px;\r\n\t}\r\n\r\n\t.huang_nums {\r\n\t\tpadding: 2px 5px;\r\n\t\tbackground: #97e29d;\r\n\t\tborder-radius: 10px;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 10px;\r\n\t}\r\n\r\n\t.game {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 60%;\r\n\t\tz-index: 10;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding-top: 50px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.game .game-grid {\r\n\t\twidth: 35%;\r\n\t\theight: 70px;\r\n\t\tbox-sizing: border-box;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.game .game-img {\r\n\t\tdisplay: block;\r\n\t\twidth: 90px;\r\n\t\theight: 70px;\r\n\t}\r\n\r\n\t.model {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\ttext-align: center;\r\n\t\ttransform-origin: right top;\r\n\t\ttransition: all 1s;\r\n\t}\r\n\r\n\t/* .luckBao {\r\n\t\twidth: 305px;\r\n\t\theight: 305px;\r\n\t\tcolor: #fff;\r\n\t\tpadding-top: 99px;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: url('../../game/static/game/lucky/lucky_pre.png') no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t} */\r\n\r\n\t.luckBao-text {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.luckBao-input-wrap {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\twidth: 60%;\r\n\t\tmargin: 20px auto 0;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.luckBao-input-class {\r\n\t\toutline: none;\r\n\t\tborder: 1px solid #ed8b56;\r\n\t\twidth: 66px;\r\n\t\theight: 35px;\r\n\t\tborder-radius: 35px;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #F04F30;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.luckBao-tips-wrap1 {\r\n\t\tmargin-top: 20px;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.luckBao-tips-wrap2 {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.img-cursor-1 {\r\n\t\tmargin: 0 5px;\r\n\t\twidth: 15px;\r\n\t\theight: 15px;\r\n\t}\r\n\r\n\t.img-cursor-2 {\r\n\t\twidth: 36px;\r\n\t\theight: 36px;\r\n\t}\r\n\r\n\t.lucky-btn-now {\r\n\t\tbackground: linear-gradient(90deg, #934FFE, #C936DF);\r\n\t\tborder-radius: 39px;\r\n\t\twidth: 150px;\r\n\t\theight: 40px;\r\n\t\tline-height: 40px;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 18px;\r\n\t\tmargin: 20px auto 10px;\r\n\t}\r\n\r\n\t.luckBao-header {\r\n\t\tmargin-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\r\n\t.luckBao-img-close {\r\n\t\twidth: 25px;\r\n\t\theight: 25px;\r\n\t}\r\n\r\n\t.lucky-btn-origin-price {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #FFB320;\r\n\t\tletter-spacing: 3px;\r\n\t}\r\n\t\r\n\t.pp4 {\r\n\t    height: 32px;\r\n\t    line-height: 32px;\r\n\t    color: #FFFFFF;\r\n\t    border-radius: 16px;\r\n\t    margin-left: 10px;\r\n\t    flex-shrink: 0;\r\n\t    padding: 0 15px;\r\n\t    font-size: 12px;\r\n\t    font-weight: bold;\r\n\t\twidth: 45%;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115056290\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}